const path = require('path');
const os = require('os');
const webpack = require('webpack');
const ExtractTextPlugin = require('extract-text-webpack-plugin');
const HappyPack = require('happypack');
var happyThreadPool = HappyPack.ThreadPool({ size: os.cpus().length });
function resolve (dir) {
    return path.join(__dirname, dir);
}
let baseURl = '';
switch (process.env.NODE_ENV) {
    case 'dev':
        baseURl = '/yop-portal';
        break;
    case 'qa':
        baseURl = '/yop-portal';
        break;
    default:
        baseURl = '/yop-portal';
}
module.exports = {
    entry: {
        main: '~/main',
        'vender-base': '~/vendors/vendors.base.js',
        'vender-exten': '~/vendors/vendors.exten.js',
    },
    output: {
        path: path.resolve(__dirname, '../dist/dist')
    },
    devtool: "cheap-source-map",
    module: {
        rules: [
            {
                test: /\.vue$/,
                loader: 'vue-loader',
                options: {
                    loaders: {
                        css: 'vue-style-loader!css-loader',
                        less: 'vue-style-loader!css-loader!less-loader'
                    },
                    postLoaders: {
                        html: 'babel-loader'
                    }
                }
            },
            {
                test: /iview\/.*?js$/,
                loader: 'happypack/loader?id=happybabel',
                exclude: /node_modules/
            },
            // {
            //     test: /\.js$/,
            //     loader: 'babel-loader',
            //     options: {
            //         presets: ['stage-3'],
            //     },
            //     include: [
            //         // path.resolve('src'), path.resolve('test'), 
            //         path.resolve(__dirname, '../node_modules/@yeepay/antd-materials'), 
            //         path.resolve(__dirname, '../node_modules/@yeepay/lowcode-renderer')
            //     ]
            // },
            {
                test: /\.js$/,
                loader: 'happypack/loader?id=happybabel',
                exclude: /node_modules/
            },
            {
                test: /\.js[x]?$/,
                include: [resolve('src'),  path.resolve(__dirname,'../node_modules/_nanoid@4.0.0@nanoid')],
                // exclude: /node_modules/,
                loader: 'happypack/loader?id=happybabel'
            },
            {
                test: /\.css$/,
                use: ExtractTextPlugin.extract({
                    use: ['css-loader?minimize', 'autoprefixer-loader'],
                    fallback: 'style-loader'
                })
            },
            {
                test: /\.less$/,
                use: ExtractTextPlugin.extract({
                    use: ['css-loader?minimize','autoprefixer-loader', 'less-loader'],
                    fallback: 'style-loader'
                }),
            },
            {
                test: /\.(woff|svg|eot|ttf)\??.*$/,
                // loader: 'url-loader?limit=1024'
                loader: 'file-loader?publicPath=./'
            },
            {
                test: /\.(gif|jpg|png)\??.*$/,
                loader: 'file-loader?publicPath=./dist/'

            },
            {
                test: /\.(html|tpl)$/,
                loader: 'html-loader'
            }
        ]
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env': {
                baseURl: JSON.stringify(baseURl)
            }
        }),
        new HappyPack({
            id: 'happybabel',
            loaders: ['babel-loader'],
            threadPool: happyThreadPool,
            verbose: true
        })
    ],
    devServer: {
        proxy: {
            '/yop-portal/*': {
                target: 'https://qayop.yeepay.com',
                changeOrigin: true,
                ws: false
            },
        }
    },
    resolve: {
        extensions: ['.js', '.vue'],
        alias: {
            'vue': 'vue/dist/vue.esm.js',
            '~': resolve('../src'),
            // '@': resolve('../')
        }
    },
    externals:{
        // "vue":"vue",
        // "iview":"iView",
        // "axios":"axios"
    }
};
