Arguments: 
  /Users/<USER>/.nvm/versions/node/v12.18.3/bin/node /Users/<USER>/.nvm/versions/node/v12.18.3/bin/yarn add vue-eslint-parser --dev

PATH: 
  /Users/<USER>/.nvm/versions/node/v12.18.3/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/Applications/VMware Fusion.app/Contents/Public:/Users/<USER>/.nvm/versions/node/v12.18.3/bin

Yarn version: 
  1.22.10

Node version: 
  12.18.3

Platform: 
  darwin x64

Trace: 
  Error: read ECONNRESET
      at TLSWrap.onStreamRead (internal/stream_base_commons.js:205:27)

npm manifest: 
  {
    "name": "yop-portal",
    "version": "v100.0.0",
    "description": "",
    "main": "index.js",
    "scripts": {
      "dev": "NODE_ENV=dev webpack-dev-server --content-base ./ --open --inline --hot --compress --progress --profile --config build/webpack.dev.config.js",
      "qa": "NODE_ENV=qa webpack-dev-server --content-base ./ --open --inline --hot --compress --progress --profile --config build/webpack.dev.config.js",
      "build": "webpack --progress --hide-modules --config build/webpack.prod.config.js",
      "lint": "eslint --fix --ext .js,.vue src",
      "test": "npm run lint"
    },
    "license": "MIT",
    "dependencies": {
      "area-data": "^1.0.0",
      "axios": "^0.17.1",
      "clipboard": "^1.7.1",
      "countup": "^1.8.2",
      "cropperjs": "^1.2.2",
      "draggable": "^4.2.0",
      "echarts": "^4.3.0",
      "element-ui": "^2.11.1",
      "fundebug-javascript": "^2.0.0",
      "fundebug-revideo": "^0.6.0",
      "fundebug-vue": "0.0.1",
      "html2canvas": "^0.5.0-beta4",
      "iview": "^2.14.3",
      "iview-area": "^1.5.16",
      "js-cookie": "^2.2.0",
      "mavon-editor": "^2.7.7",
      "qs": "^6.5.1",
      "rasterizehtml": "^1.2.4",
      "simplemde": "^1.11.2",
      "sortablejs": "^1.7.0",
      "tinymce": "^4.7.4",
      "vditor": "3.3.9",
      "vue": "^2.5.13",
      "vue-clipboard2": "^0.2.1",
      "vue-code-diff": "0.0.4",
      "vue-codemirror": "^4.0.6",
      "vue-draggable": "^2.0.2",
      "vue-json-viewer": "^2.2.11",
      "vue-router": "^3.0.1",
      "vue-socket.io": "^2.1.1-b",
      "vue-virtual-scroller": "^0.10.6",
      "vuedraggable": "^2.23.0",
      "vuex": "^3.0.1",
      "xlsx": "^0.11.17"
    },
    "devDependencies": {
      "autoprefixer-loader": "^3.2.0",
      "babel": "^6.23.0",
      "babel-core": "^6.23.1",
      "babel-eslint": "^8.2.1",
      "babel-loader": "^7.1.2",
      "babel-plugin-dynamic-import-node": "^2.3.0",
      "babel-plugin-dynamic-import-webpack": "^1.1.0",
      "babel-plugin-syntax-dynamic-import": "^6.18.0",
      "babel-plugin-transform-runtime": "^6.12.0",
      "babel-preset-env": "^1.6.1",
      "babel-preset-es2015": "^6.9.0",
      "babel-preset-stage-3": "^6.24.1",
      "babel-runtime": "^6.11.6",
      "clean-webpack-plugin": "^0.1.17",
      "copy-webpack-plugin": "^4.3.1",
      "css-hot-loader": "^1.3.5",
      "css-loader": "^0.28.8",
      "drag-tree-table": "^1.2.5",
      "ejs-loader": "^0.3.0",
      "eslint": "^4.15.0",
      "eslint-config-google": "^0.9.1",
      "eslint-config-standard": "^10.2.1",
      "eslint-plugin-html": "^4.0.1",
      "eslint-plugin-import": "^2.8.0",
      "eslint-plugin-node": "^5.2.1",
      "eslint-plugin-promise": "^3.6.0",
      "eslint-plugin-standard": "^3.0.1",
      "extract-text-webpack-plugin": "^3.0.2",
      "file-loader": "^1.1.6",
      "generate-asset-webpack-plugin": "^0.3.0",
      "happypack": "^4.0.0",
      "html-loader": "^0.5.4",
      "html-webpack-plugin": "^2.28.0",
      "less": "^2.7.3",
      "less-loader": "^4.0.5",
      "semver": "^5.4.1",
      "style-loader": "^0.19.1",
      "unsupported": "^1.1.0",
      "url-loader": "^0.6.2",
      "vditor": "^3.2.10",
      "vue-codemirror": "^4.0.6",
      "vue-hot-reload-api": "^2.2.4",
      "vue-html-loader": "^1.2.3",
      "vue-i18n": "^5.0.3",
      "vue-loader": "^13.7.0",
      "vue-style-loader": "^3.0.3",
      "vue-template-compiler": "^2.5.13",
      "webpack": "^3.10.0",
      "webpack-dev-server": "^2.10.1",
      "webpack-merge": "^4.1.1",
      "webpack-uglify-parallel": "^0.1.4"
    }
  }

yarn manifest: 
  No manifest

Lockfile: 
  No lockfile
