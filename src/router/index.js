import Vue from 'vue';
import VueRouter from 'vue-router';
import store from '../store';
import util from '../libs/util';

Vue.use(VueRouter);

// 解决报错
const originalPush = VueRouter.prototype.push;
const originalReplace = VueRouter.prototype.replace;
// push
VueRouter.prototype.push = function push (location, onResolve, onReject) {
  if (onResolve || onReject) { return originalPush.call(this, location, onResolve, onReject); }
  // return originalPush.call(this, location).catch(err => err);
  return originalPush.call(this, location);
};
// replace
VueRouter.prototype.replace = function push (location, onResolve, onReject) {
  if (onResolve || onReject) { return originalReplace.call(this, location, onResolve, onReject); }
  return originalReplace.call(this, location);
};
// 路由配置
const RouterConfig = {
  // mode: 'history',
  routes: [] // todo
  // routes: []//todo
};

export const router = new VueRouter(RouterConfig);
router.beforeEach((to, from, next) => {
  /* 路由发生变化修改页面title */
  document.title =
      to.meta.title == undefined
        ? '易宝运营后台'
        : to.meta.title + ' - 易宝运营后台';
  let name = to.name;
  if (to.meta.parentName) {
    name = to.meta.parentName;
  }
  store.dispatch('getButtons', name).then(res => {
    next();
  });
});
router.afterEach((to) => {
  util.webfunnyUpEvent({
    xiangMuLeiXing: 'BOSS', // 项目类型
    xiangMuBiaoShi: 'yop-portal', // 项目标识
    yeMianLuYou: to.path || '', // 页面路由
    userName:  window.localStorage.getItem('userName') || '', // 用户名
    delay: 1000 // 延迟执行 ms
  })
})
