const initRouter = [
  {
    path: "home",
    title: { i18n: "home" },
    name: "home_index",
    component: () => import("~/views/invoke-logging/list.vue")
  },
  {
    path: "diff",
    title: { i18n: "diff" },
    name: "diff_index",
    component: () => import("~/views/diff/index.vue")
  },
  {
    path: "api_basics",
    title: "API新增",
    name: "api_basics",
    icon: "social-buffer",
    meta: {
      parentName: "/api/manage/list"
    },
    component: () => import("~/views/api/manage/api_basics.vue")
  },
  {
    path: "api/manage/publish_record",
    title: "API发布记录",
    name: "api/manage/publish_record",
    meta: {
      parentName: "/api/manage/list"
    },
    icon: "social-buffer",
    component: () => import("~/views/api/manage/api_publish_record.vue")
  },
  {
    path: "api_basics_edit",
    title: "API修改",
    name: "api_basics_edit",
    meta: {
      parentName: "/api/manage/list"
    },
    icon: "social-buffer",
    component: () => import("~/views/api/manage/api_basics.vue")
  },
  {
    path: "api_basics_desc",
    title: "API详情",
    name: "api_basics_desc",
    meta: {
      parentName: "/api/manage/list"
    },
    icon: "social-buffer",
    component: () => import("~/views/api/manage/api_basics.vue")
  },
  {
    path: "api_route_manage",
    title: "API路由",
    name: "api_route_manage",
    meta: {
      parentName: "/api/manage/list"
    },
    icon: "social-buffer",
    component: () => import("~/views/api/manage/api_route_manage.vue")
  },
  {
    path: "spi_basics",
    title: "新增结果通知",
    name: "spi_basics",
    meta: {
      parentName: "/spi/list"
    },
    icon: "social-buffer",
    component: () => import("~/views/spi/spi_basics.vue")
  },
  {
    path: "spi_basics_edit",
    title: "修改结果通知",
    name: "spi_basics_edit",
    meta: {
      parentName: "/spi/list"
    },
    icon: "social-buffer",
    component: () => import("~/views/spi/spi_basics.vue")
  },
  {
    path: "spi_basics_desc",
    title: "结果通知详情",
    name: "spi_basics_desc",
    meta: {
      parentName: "/spi/list"
    },
    icon: "social-buffer",
    component: () => import("~/views/spi/spi_basics.vue")
  },
  {
    path: "isv/add_merchant",
    title: "新增商户",
    name: "isv/add_merchant",
    meta: {
      parentName: "/isv/list"
    },
    icon: "social-buffer",
    component: () => import("~/views/isv/add_merchant.vue")
  },
  {
    path: "doc/edit_product",
    title: "编辑文档",
    name: "doc/edit_product",
    meta: {
      parentName: "/doc/list"
    },
    icon: "social-buffer",
    component: () => import("~/views/doc/edit/edit-product.vue")
  },
  {
    path: "product/authz/batch-auth",
    title: "新增产品授权",
    name: "product/authz/batch-auth",
    meta: {
      parentName: "/product/authz/list"
    },
    icon: "social-buffer",
    component: () => import("~/views/product/authz/batch-auth.vue")
  },
  {
      path: 'faq/created',
      title: '创建常见问题',
      name: '创建常见问题',
      meta: {
          parentName: '/faq/list'
      },
      icon: 'social-buffer',
      component: () => import('~/views/faq/created.vue')
  },
    {
        path: 'faq/editor',
        title: '编辑常见问题',
        name: '编辑常见问题',
        meta: {
            parentName: '/faq/list'
        },
        icon: 'social-buffer',
        component: () => import('~/views/faq/created.vue')
    },
    {
        path: 'faq/detail',
        title: '查看见问题',
        name: '查看见问题',
        meta: {
            parentName: '/faq/list'
        },
        icon: 'social-buffer',
        component: () => import('~/views/faq/created.vue')
    }
    // { path: 'acl/log/list',title: '操作日志',name: 'acl/log/list',icon: 'social-buffer', component: () => import('~/views/acl/log/list.vue')},
];

export default initRouter;
