import Vue from 'vue';
import iView from 'iview';
import { router } from './router/index';
import store from './store';
import App from './app.vue';
import '~/locale';
import 'iview/dist/styles/iview.css';
import VueI18n from 'vue-i18n';
import {
  Tree,
  Select,
  Option,
  Collapse,
  CollapseItem,
  Button,
  Table,
  TableColumn,
  Divider,
  Input,
  Popover,
  Tooltip,
  Cascader,
  Drawer,
  Loading
} from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import util from './libs/util';
import './libs/btnPermission';
import VueClipboard from 'vue-clipboard2';
import ypmsg from './libs/messge_modal';
// 阿里字体图标
import './styles/iconfonts/iconfont.css';
import './styles/vditorNew.css';

// Markdown 编辑器
import mavonEditor from 'mavon-editor';

// material.install(AntdMaterials) // antd 物料集
// Vue.use(Antd, { materials }) // antd 物料集vueuse
// Vue.use(Renderer)
Vue.use(mavonEditor);
// Vue.use(Renderer)
// injectRequest(axiosInstance)

VueClipboard.config.autoSetContainer = true;
Vue.use(VueClipboard);

Vue.use(VueI18n);
Vue.use(iView);
Vue.use(Tree);
Vue.use(Select);
Vue.use(Option);

Vue.prototype.$ypMsg = ypmsg;

Vue.use(Collapse);
Vue.use(CollapseItem);
Vue.use(Button);
Vue.use(Table);
Vue.use(Input);
Vue.use(TableColumn);
Vue.use(Divider);
Vue.use(Popover);
Vue.use(Tooltip);
Vue.use(Cascader);
Vue.use(Drawer);
Vue.use(Loading);

new Vue({
  el: '#app',
  router: router,
  store: store,
  render: h => h(App),
  data: {
    currentPageName: ''
  },
  mounted () {
    this.currentPageName = this.$route.name;
    // 显示打开的页面的列表
    this.$store.commit('setOpenedList');
    this.$store.commit('initCachepage');
    // iview-admin检查更新
    // util.checkUpdate(this);
    if (location.href.indexOf('yuiassotoken=') != -1) {
      var yuiassotoken = util.getQueryVariable('yuiassotoken');
      localStorage.setItem('yuiassotoken', yuiassotoken)
    } else {
      localStorage.setItem('yuiassotoken', '')
    }
  },
  created () {
    let tagsList = [];
    this.$store.commit('setTagsList', tagsList);
  }
});
