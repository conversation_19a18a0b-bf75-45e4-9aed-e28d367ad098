import Vue from 'vue'
/**权限指令**/
const url = Vue.directive('url', {
    inserted: function (el, binding, vnode) {
        // 获取页面按钮权限
        let btnPermissionsArr = vnode.context.$store.state.app.buttons;
        let result = Vue.prototype.$_has(btnPermissionsArr,binding.value.url);
        if(result === 'enable'){

        }else if(result === 'disable'){
            el.disabled = true;
        }else{
            if(el.parentNode){
                el.parentNode.removeChild(el);
            }
        }
    },
});
// 权限检查方法
Vue.prototype.$_has = function (arr,value) {
    let isExist = 'delete';
    if(arr){
        for(var i in  arr){
            if(value === arr[i].url){
                if(arr[i].status === 'ENABLE'){
                    isExist ='enable'
                }else{
                    isExist ='disable'
                }
            }
        }
    }
    return isExist;
};

export {url}
