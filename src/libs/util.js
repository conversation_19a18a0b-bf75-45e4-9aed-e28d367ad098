import axios from 'axios';
import semver from 'semver';
import packjson from '../../package.json';
import api from '../api/api.js'
import store from '../store'
/**
 * iviewAdmin 自带工具类
 */
let util = {

};
util.title = function (title) {
    title = title || '易宝运营后台';
    window.document.title = title;
};
util.getIdByUrl = function (name, arr) {
    let id = '';
    function handleGetIdByUrl (name, arr) {
        for (let i = 0; i < arr.length; i++) {
            if (id) {
                break;
            }
            if (arr[i].name === name) {
                id = arr[i].id;
                break;
            }
            if (arr[i].children && arr[i].children.length > 0) {
                handleGetIdByUrl(name, arr[i].children);
            }
        }
    }
    handleGetIdByUrl(name, arr);
    return id;
};

util.checkAudit = function(url){
    let perrmissionButton = store.state.app.buttons,
        ifNeedAudit = false;
    perrmissionButton.forEach(item => {
        if(item.url == url){
            if(item.needAudit){
              ifNeedAudit = true
            }
          }
      });
      return ifNeedAudit
};
util.inOf = function (arr, targetArr) {
    let res = true;
    arr.forEach(item => {
        if (targetArr.indexOf(item) < 0) {
            res = false;
        }
    });
    return res;
};

util.oneOf = function (ele, targetArr) {
    if (targetArr.indexOf(ele) >= 0) {
        return true;
    } else {
        return false;
    }
};

util.showThisRoute = function (itAccess, currentAccess) {
    if (typeof itAccess === 'object' && Array.isArray(itAccess)) {
        return util.oneOf(currentAccess, itAccess);
    } else {
        return itAccess === currentAccess;
    }
};

util.getRouterObjByName = function (routers, name) {
    if (!name || !routers || !routers.length) {
        return null;
    }
    // debugger;
    let routerObj = null;
    for (let item of routers) {
        if (item.name === name) {
            return item;
        }
        routerObj = util.getRouterObjByName(item.children, name);
        if (routerObj) {
            return routerObj;
        }
    }
    return null;
};

util.handleTitle = function (vm, item) {
    if (typeof item.title === 'object') {
        return vm.$t(item.title.i18n);
    } else {
        return item.title;
    }
};

util.setCurrentPath = function (vm, name) {
    let title = '';
    let isOtherRouter = false;
    vm.$store.state.app.routers.forEach(item => {
        if (item.children.length === 1) {
            if (item.children[0].name === name) {
                title = util.handleTitle(vm, item);
                if (item.name === 'otherRouter') {
                    isOtherRouter = true;
                }
            }
        } else {
            item.children.forEach(child => {
                if (child.name === name) {
                    title = util.handleTitle(vm, child);
                    if (item.name === 'otherRouter') {
                        isOtherRouter = true;
                    }
                }
            });
        }
    });
    let currentPathArr = [];
    if (name === 'home_index') {
        currentPathArr = [
            {
                title: util.handleTitle(vm, util.getRouterObjByName(vm.$store.state.app.routers, 'home_index')),
                path: '',
                name: 'home_index'
            }
        ];
    } else if ((name.indexOf('_index') >= 0 || isOtherRouter) && name !== 'home_index') {
        currentPathArr = [
            {
                title: util.handleTitle(vm, util.getRouterObjByName(vm.$store.state.app.routers, 'home_index')),
                path: '/home',
                name: 'home_index'
            },
            {
                title: title,
                path: '',
                name: name
            }
        ];
    } else {
        let currentPathObj = vm.$store.state.app.routers.filter(item => {
            if (item.children[0] && item.children.length <= 1) {
                return item.children[0].name === name;
                if(!item.children[0])
                return false
            } else {
                let i = 0;
                let childArr = item.children;
                let len = childArr.length;
                while (i < len) {
                    if (childArr[i].name === name) {
                        return true;
                    }
                    i++;
                }
                return false;
            }
        })[0];
        if (currentPathObj.children.length <= 1 && currentPathObj.name === 'home') {
            currentPathArr = [
                {
                    title: '首页',
                    path: '',
                    name: 'home_index'
                }
            ];
        } else if ( currentPathObj.children.length <= 1 && currentPathObj.name !== 'home') {
            currentPathArr = [
                {
                    title: '首页',
                    path: '/home',
                    name: 'home_index'
                },
                {
                    title: currentPathObj.title,
                    path: '',
                    name: name
                }
            ];
        } else {
            let childObj = currentPathObj.children.filter((child) => {
                return child.name === name;
            })[0];
            currentPathArr = [
                {
                    title: '首页',
                    path: '/home',
                    name: 'home_index'
                },
                {
                    title: currentPathObj.title,
                    path: '',
                    name: currentPathObj.name
                },
                {
                    title: childObj.title,
                    path:  childObj.path,
                    name: name
                }
            ];
        }
    }
    vm.$store.commit('setCurrentPath', currentPathArr);

    return currentPathArr;
};

util.openNewPage = function (vm, name, argu, query) {
    let pageOpenedList = vm.$store.state.app.pageOpenedList;
    let openedPageLen = pageOpenedList.length;
    let i = 0;
    let tagHasOpened = false;
    while (i < openedPageLen) {
        if (name === pageOpenedList[i].name) { // 页面已经打开
            vm.$store.commit('pageOpenedList', {
                index: i,
                argu: argu,
                query: query
            });
            tagHasOpened = true;
            break;
        }
        i++;
    }
    if (!tagHasOpened) {
        let tag = vm.$store.state.app.tagsList.filter((item) => {
            if (item.children) {
                return name === item.children[0].name;
            } else {
                return name === item.name;
            }
        });
        tag = tag[0];
        if (tag) {
            tag = tag.children ? tag.children[0] : tag;
            if (argu) {
                tag.argu = argu;
            }
            if (query) {
                tag.query = query;
            }
            vm.$store.commit('increateTag', tag);
        }
    }
    vm.$store.commit('setCurrentPageName', name);
};

util.toDefaultPage = function (routers, name, route, next) {
    let len = routers.length;
    let i = 0;
    let notHandle = true;
    while (i < len) {
        if (routers[i].name === name && routers[i].children && routers[i].redirect === undefined) {
            route.replace({
                name: routers[i].children[0].name
            });
            notHandle = false;
            next();
            break;
        }
        i++;
    }
    if (notHandle) {
        next();
    }
};
// 获取地址栏参数
util.getQueryVariable = (name, url) => {
  let u = window.location.href;
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  let r = u.substr(u.indexOf("?") + 1).match(reg);
  return r != null ? decodeURI(r[2]) : "";
}
util.fullscreenEvent = function (vm) {
    vm.$store.commit('initCachepage');
    // 权限菜单过滤相关
    vm.$store.commit('updateMenulist');
    // 全屏相关
};

// util.checkUpdate = function (vm) {
//     axios.get('https://api.github.com/repos/iview/iview-admin/releases/latest').then(res => {
//         let version = res.data.tag_name;
//         vm.$Notice.config({
//             duration: 0
//         });
//         if (semver.lt(packjson.version, version)) {
//             vm.$Notice.info({
//                 title: 'iview-admin更新啦',
//                 desc: '<p>iView-admin更新到了' + version + '了，去看看有哪些变化吧</p><a style="font-size:13px;" href="https://github.com/iview/iview-admin/releases" target="_blank">前往github查看</a>'
//             });
//         }
//     });
// };
/**
 * yop-dashboard 自定义工具类
 */

// 获取字符串长度
util.getLength = function(str) {
    if(str){
        let strTmp = this.ClearBr(str);
        return strTmp.replace(/[\u0391-\uFFE5]/gm,"a").length;
    }else{
        return 0
    }

};
//去除换行
util.ClearBr = function(key) {
    key = key.replace(/<\/?.+?>/g,"");
    key = key.replace(/[\r\n]/g, "");
    return key;
};

// 访问第三方登录域名验证
util.loginUriGet = function () {

};
// 判断是否为空
util.isNull = function( str ){
    if ( str === "" || str === null ||str === undefined) return true;
    var regu = "^[ ]+$";
    var re = new RegExp(regu);
    return re.test(str);
}
// 参数为空剔除字段
util.paramFormat = function (params) {
    Object.keys(params).forEach(function(key){
        if(params[key] === ''){
            delete params[key];
        }
    });
}
// 日期格式化(全部)
util.dateFormat = function (data_date,type) {
    if(data_date !== ''){
        let year = data_date.getFullYear();
        let month = data_date.getMonth() + 1;
        if(month<10) month='0'+month
        let day = data_date.getDate();
        if(day<10) day='0'+day
        let hour = data_date.getHours();
        if(hour<10) hour='0'+hour
        let minute = data_date.getMinutes();
        if(minute<10) minute='0'+minute
        let second = data_date.getSeconds();
        if(second<10) second='0'+second
        if(type === 'DAILY'){
            return year + '-' + month + '-' + day;
        }else{
            return year + '-' + month
        }
    }else{
        return '';
    }
}
// 日期格式化（年月日和时分秒分开）
util.dateFormat_sep = function (data_date,date_time) {
    if(data_date !== ''){
            let year = data_date.getFullYear();
            let month = data_date.getMonth() + 1;
            if(month<10) month='0'+month
            let day = data_date.getDate();
            if(day<10) day='0'+day
        if(date_time !== ''){
            // let hour = date_time.getHours();
            // if(hour<10) hour='0'+hour
            // let minute = date_time.getMinutes();
            // if(minute<10) minute='0'+minute
            // let second = date_time.getSeconds();
            // if(second<10) second='0'+second
            // return year + '-' + month + '-' + day  + ' ' + hour + ':' + minute + ':' + second;
            return year + '-' + month + '-' + day  + ' '+date_time
        }else{
            return year + '-' + month + '-' + day  + ' 00:00:00';
        }

    }else{
        return '';
    }
}

util.dateFormat_component = function (data_date) {
    if(data_date !== ''){
         data_date = data_date.replace(new RegExp('/','g'),'-')
        return data_date  + ' 00:00:00';
    }else{
        return '';
    }
}
util.dateFormat_component_end = function (data_date) {
    if(data_date !== ''){
        data_date = data_date.replace(new RegExp('/','g'),'-')
        return data_date  + ' 23:59:59';
    }else{
        return '';
    }
}

util.dateFormat_component_normal = function (data_date) {
    if(data_date !== ''){
         data_date = data_date.replace(new RegExp('/','g'),'-')
        return data_date;
    }else{
        return '';
    }
}
util.dateFormat_component_end_normal = function (data_date) {
    if(data_date !== ''){
        data_date = data_date.replace(new RegExp('/','g'),'-')
        return data_date;
    }else{
        return '';
    }
}
// 返回当前日期前一天
util.date_oneDay = function () {
    let now = new Date();
    let date = new Date(now.getTime() - 24 * 3600 * 1000);
    let year_before = date.getFullYear();
    let month_before = date.getMonth() + 1;
    if(month_before<10) month_before='0'+month_before
    let day_before = date.getDate();
    if(day_before<10) day_before='0'+day_before
    let year_after = now.getFullYear();
    let month_after = now.getMonth() + 1;
    if(month_after<10) month_after='0'+month_after
    let day_after = now.getDate();
    if(day_after<10) day_after='0'+day_after
    // let returnDate = {
    //     before : year_before + '-' + month_before + '-' + day_before,
    //     after : year_after+ '-' + month_after + '-' + day_after
    // }
    let returnDate = {
        before : date,
        after : now
    }
    return returnDate;
}
// 返回当前日期前一天
util.date_oneDay_string = function () {
    let now = new Date();
    let date = new Date(now.getTime() - 24 * 3600 * 1000);
    let year_before = date.getFullYear();
    let month_before = date.getMonth() + 1;
    if(month_before<10) month_before='0'+month_before
    let day_before = date.getDate();
    if(day_before<10) day_before='0'+day_before
    let year_after = now.getFullYear();
    let month_after = now.getMonth() + 1;
    if(month_after<10) month_after='0'+month_after
    let day_after = now.getDate();
    if(day_after<10) day_after='0'+day_after
    let returnDate = {
        before : year_before + '-' + month_before + '-' + day_before,
        after : year_after+ '-' + month_after + '-' + day_after
    }
    return returnDate;
}
// 返回当前日期前一个月　
util.date_oneMonth = function () {
    let now = new Date();
    let date = new Date(now.getTime() - 24 * 3600 * 1000);
    let year_before = date.getFullYear();
    let month_before = date.getMonth() + 1;
    if(month_before<10) month_before='0'+month_before
    let day_before = date.getDate();
    if(day_before<10) day_before='0'+day_before
    // let returnDate = {
    //     before : year_before + '-' + month_before + '-01',
    //     after :  year_before + '-' + month_before + '-' + day_before
    // }
    let str = (year_before + '-' + month_before + '-01').toString();
    str = str.replace("/-/g", "/");
    let odateBefore = new Date(str);
    let returnDate = {
        before : odateBefore,
        after : date
    }
    return returnDate;
}

// 返回当前日期前一个月　
util.date_lastMonth = function () {
    let now = new Date();
    let year_before = now.getFullYear();
    let month_before = now.getMonth();
    if(month_before<10) month_before='0'+month_before
    let day_before = now.getDate();
    if(day_before<10) day_before='0'+day_before;
    let returnDate = {
        dateString : year_before+'-'+month_before,
        dateDate : new Date(year_before+'-'+month_before+'-'+day_before)
    }
    return returnDate;
}
// 判断是否只有数字字母和中划线 第一位为字母开头
util.formatCheck = function (val) {
    let reg = /^[a-z]{1}([0-9a-z-]+?)$/
    return !reg.test(val)
}
// 判断是否只有数字字母和中划线.
util.formatCheck2 = function (val) {
    let reg = /^[0-9a-z-.]+?$/
    return !reg.test(val)
}
// 判断数字大小写字母和汉字
util.formatCheck3 = function (val){
    let reg= /^[0-9a-zA-Z\u4e00-\u9fa5]+?$/
    return !reg.test(val)
}
// 判断url 以/开头 支持小写字母、数字、/.-
util.formatCheck4 = function (val){
    let reg= reg = /^[/]{1}([0-9a-z-./]+?)$/
    return !reg.test(val)
}
// 判断数字大小写字母和汉字
util.formatCheck5 = function (val){
    let reg= /^[a-zA-Z\u4e00-\u9fa5]+?$/
    return !reg.test(val)
}
// 判断数字小写字母和下划线
util.formatCheck6 = function (val){
    let reg= /^[a-z_]+?$/
    return !reg.test(val)
}
// 判断是否只有数字字母和中划线.
util.formatCheck7 = function (val) {
    let reg = /^[0-9A-Za-z-.:]+?$/
    return !reg.test(val)
}
// 自定义规则判断
util.format_check_common = function (val,rule){
    let reg =rule;
    return !reg.test(val)
}
// jsonparse格式判断
util.jsonParseCheck = function (str) {
    if (typeof str == 'string') {
        try {
            var obj=JSON.parse(str);
            if(typeof obj == 'object' && obj ){
                return true;
            }else{
                return false;
            }
        } catch(e) {
            return false;
        }
    }
}
// 空字符串处理
util.empty_handler = function (val) {
    if(val){
        return val;
    }else{
        return '--';
    }
}
// 空字符串处理
util.empty_handler2 = function (val) {
    if(val){
        return val;
    }else{
        return '';
    }
}
// json格式化函数
util.formatJson = function(msg) {
    let returnData = JSON.stringify(msg, null,2);
    returnData = returnData.replace(/\[\n    /g,"[");
    returnData = returnData.replace(/\n  \]/g,"]");
    return returnData
}
// 判断是否为空
util.emptyCheck = function(data) {
    if(!!data){
        return true;
    }else{
        return false;
    }
}
util.IsInArray = function(arr,val) {
    let testStr=','+arr.join(',')+',';
    return testStr.indexOf(','+val+',')!== -1;
}
// 校验ip（可含有网段）
util.checkIP = function(ip) {
    var onlyIp = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    var ipAndNet = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\/(([1-9])|([1-2][0-9])|(3[0-2]))$/;
    var ipv6 = /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*/;

    return onlyIp.test(ip) || ipv6.test(ip) || ipAndNet.test(ip);
}

// api swagger 属性字段对照
util.swagger_properties = {
    type : 'type',
    title: 'name',
    format: 'format',
    required : 'required',
    'x-yop-example' :'sample',
    description : 'description',
    'x-yop-internal' : 'internal',
    'x-yop-default' : 'default',
    'x-yop-sensitive' : 'sensitive',
    'x-yop-end-param-index' : 'param_index',
    'x-yop-end-param-name' : 'param_name',
    'maximum' : 'maximum',
    exclusiveMaximum : 'exclusiveMaximum',
    minimum : 'minimum',
    exclusiveMinimum : 'exclusiveMinimum',
    pattern : 'pattern',
    maxLength : 'maxLength',
    minLength : 'minLength',
    maxItems : 'maxItems',
    minItems : 'minItems',
    $ref : 'model',
    'x-yop-biz-order-no' :'orderNo'
}

util.webfunnyUpEvent = (data) => {
  setTimeout(() => {
    const userName = data.userName || window.localStorage.getItem('username') || ''
    const isLowCode = document.querySelector('.low-code-renderer')
    const projectType = data.xiangMuLeiXing || 'BOSS'

    const params = [{
      ...data,
      projectId: 'event1051',
      pointId: '77',
      weUserId: userName, // 用户标识
      xiangMuLeiXing: projectType, // 项目类型
      diDaiMaYeMian: isLowCode ? '是' : '否', // 是否为低代码页面
      wePath: encodeURIComponent(window.location.href), // 页面地址
      yeMianWeiYiBiaoShi: `${projectType}_${data.xiangMuBiaoShi}_${data.yeMianLuYou}` // 页面唯一标识
    }]

    const xhr = window.XMLHttpRequest ? new XMLHttpRequest() : new window.ActiveXObject('Microsoft.XMLHTTP')
    xhr.open('POST', 'https://probe.yeepay.com/tracker/upMyEvents', true)
    xhr.setRequestHeader(
      'Content-Type',
      'application/x-www-form-urlencoded; charset=UTF-8'
    )
    xhr.send('data=' + JSON.stringify(params))
  }, data.delay || 0)
}
util.handlerSchema = (schema, isSubmit = true) => {
  if (isSubmit) {
    let extensions = {
      'x-yop-api-param-conditions': {}
    }
    Object.keys(schema.properties).forEach(key => {
      const val = schema.properties[key].xYopApiParamConditions
      if (val && val.conditionRequired) {
        extensions['x-yop-api-param-conditions'][key] = val
        delete schema.properties[key].xYopApiParamConditions
      }
    })
    schema.extensions = extensions
  } else {
    schema.properties.forEach(item => {
      const key = item.name
      if (!schema.extensions || !schema.extensions['x-yop-api-param-conditions'][key]) {
        item.xYopApiParamConditions = {
          conditionRequired: false,
          type: 'PLAIN',
          value: ''
        }
        return
      }
      const val = schema.extensions['x-yop-api-param-conditions'][key]
      if (val) {
        item.xYopApiParamConditions = val
      }
    })
  }
  return schema
}
export default util;
