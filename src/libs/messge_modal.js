import util from './util'
import Clipboard from 'clipboard'
let message = {};

message.notice_error = function (_this,title,content,solution,time){
    _this.$Notice.error({
        title : title?title:'错误',
        desc : solution ? '<span style="font-weight: bold">原因：</span><br/><span style="width:252px;word-wrap: break-word">'+content+'</span><br/><span style="font-weight: bold">解决方案：</span><br/><span style="width:252px;word-wrap: break-word">'+solution+'</span><br/>(GUID会在点击右上角关闭后自动复制)<br/>'
                            :'<span style="font-weight: bold">原因：</span><br/><span style="width:252px;word-wrap: break-word">'+content+'</span>',
        duration : time||time===0?time:10,
        onClose : function (){
            _this.$copyText(solution)
        }
    });
}
message.notice_error_nocopy = function (_this,title,content,solution,time){
    _this.$Notice.error({
        title : title?title:'错误',
        desc : solution ? '<span style="font-weight: bold">原因：</span><br/><span style="width:252px;word-wrap: break-word">'+content+'</span><br/><span style="font-weight: bold">解决方案：</span><br/><span style="width:252px;word-wrap: break-word">'+solution+'</span><br/>(GUID会在点击右上角关闭后自动复制)<br/>'
                            :'<span style="font-weight: bold">原因：</span><br/><span style="width:252px;word-wrap: break-word">'+content+'</span>',
        duration : time||time===0?time:10
    });
}
message.notice_scroll = function (_this,type,title,content,time){
    _this.$Notice[type]({
        title : title?title:'提示',
        duration : time||time===0?time:10,
        render: h => {
            return h('div',{
                style:{
                    overflow: 'scroll',
                    'max-height' : '200px',
                    'line-height' : '20px'
                },
                domProps:{
                    innerHTML : content
                }
            })
        }
    });
}
message.notice_error_simple = function (_this,title,content,time){
    _this.$Notice.error({
        title : title?title:'错误',
        desc : content?content:'',
        duration : time||time===0?time:10
    });
}
message.notice_success = function (_this,content,title,time){
    _this.$Notice.success({
        title : title?title:'成功',
        desc : content,
        duration : time||time===0?time:5
    });
}

message.notice_warning = function (_this,content,title,time){
    _this.$Notice.warning({
        title : title?title:'警告',
        desc : content,
        duration : time||time===0?time:10
    });
}

message.notice_info = function (_this,content,title,time){
    _this.$Notice.info({
        title : title?title:'提示',
        desc : content,
        duration : time||time===0?time:10
    });
}

export default message;
