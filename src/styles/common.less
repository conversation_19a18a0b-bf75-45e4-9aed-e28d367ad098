.margin-top-8{
    margin-top: 8px;
}
.margin-top-10{
    margin-top: 10px;
}
.margin-top-20{
    margin-top: 20px;
}
.margin-left-10{
    margin-left: 10px;
}
.margin-left-16{
    margin-left: 16px;
}
.margin-bottom-10{
    margin-bottom: 10px;
}
.margin-bottom-30{
    margin-bottom: 30px;
}
.margin-bottom-20{
    margin-bottom: 20px;
}
.text-align-start{
    text-align: start;
}
.margin-bottom-100{
    margin-bottom: 100px;
}
.margin-right-10{
    margin-right: 10px;
}
.padding-left-6{
    padding-left: 6px;
}
.padding-left-8{
    padding-left: 5px;
}
.padding-left-10{
    padding-left: 10px;
}
.padding-left-20{
    padding-left: 20px;
}
.padding-top-5{
    padding-top:5px;
}
.padding-bottom-10{
    padding-bottom: 10px;
}
.height-100{
    height: 100%;
}
.height-120px{
    height: 100px;
}
.height-200px{
    height: 200px;
}
.height-492px{
    height: 492px;
}
.height-460px{
    height: 460px;
}
.margin-top-5{
    margin-top: 5px;
}
.margin-right-20{
     margin-right: 20px;
 }
.margin-left-5{
    margin-left: 5px;
}
.margin-left-20{
    margin-left: 20px;
}
.padding-top-10{
    padding-top: 10px;
}
.line-gray{
    height: 0;
    border-bottom: 2px solid #dcdcdc;
}
.notwrap{
    word-break:keep-all;
    white-space:nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.padding-left-5{
    padding-left: 10px;
}
[v-cloak]{
    display: none;
}
.yop-spin-icon-load{
    animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
    from { transform: rotate(0deg);}
    50%  { transform: rotate(180deg);}
    to   { transform: rotate(360deg);}
}
.yop-spin-col{
    height: 100px;
    position: relative;
    border: 1px solid #eee;
}
.yop-info{
    padding-top: 5px;
    padding-left: 5px;
    color: #f90;
    font-size: 15px;
}
.yop-transition-height-06 {
    transition: height 0.6s;
}
.yop-table-connect {
    margin-top: -1px;
}
.el-tree__drop-indicator {
    background-color: #2d8cf0;
    left: -10px !important;
    right: -10px !important;
    height: 3px !important;
}

.yop-page-content {
  padding:8px;
  background: #eee;
}
