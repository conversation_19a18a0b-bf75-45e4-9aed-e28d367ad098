import Vue from 'vue';
import Vuex from 'vuex';

import app from './modules/app';
import user from './modules/user';
import select from './modules/select'
import apiRoute from './modules/apiRoute'
Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    // 端点url数据绑定
    endpoint_Url: '',
    // 显示端点url部分
    show_pointUrl: false,
    // 端点类名数据绑定
    endpoint_Name: '',
    // 是否幂等数据绑定
    idempotent: '否',
    // 端点url自定义
    pointUrl_user_defined: false,
    // 适配类型下拉框数据绑定
    data_select_type: 'TRANSFORM',
    // 适配类型下拉框数据
    data_type_List: [],
    // 端点协议下拉框数据绑定
    data_select_EndpointProtocol: 'HESSIAN',
    // 端点协议下拉框数据
    data_EndpointProtocol_List: [{
      value: 'HESSIAN',
      label: 'hessian'
    }],
    // 端点方法下拉框数据绑定
    data_select_EndpointMethod: '',
    // 端点方法下拉框数据
    data_EndpointMethod_List: [],
    // 安全需求数据缓存
    security_cache: [],
    // api uri
    api_uri: '',
    // 当前api分组缓存
    current_apiGroup: '',
    // api 分组数据绑定
    api_group: '',
    // api 类型数据绑定
    api_type: '',
    // 当前应用标识
    current_appId: '',
    // 当前是否为测试用例
    test_template_label: false,
    // 测试用例创建提示文案
    test_template_tips: '注意：一个测试商编只能创建一个回归测试应用，回归测试应用统一由YOP运营人员创建，请将测试商编、回归测试应用名称发邮件给舒伟',
    // 运营邮箱
    mail_operation: '<EMAIL>',
    // 是否服务提供方跳转
    create_service_supplier_label: false,
    sys_params_list: [],
    // 控制是否需要审核权限
    ifShowCheckFlag: false,
    // 审核公共参数
    commonAuditParam: {},
    api_status_List: [
      {
        value: 'UNPUBLISHED',
        color: 'yellow',
        label: '更新待发布'
      },
      {
        value: 'PUBLISHED',
        color: 'green',
        label: '已发布'
      },
      {
        value: 'DISABLED',
        color: 'red',
        label: '已禁用'
      },
      {
        value: 'ENABLED',
        color: 'grey',
        label: '未发布'
      }
    ],
    v1ApiStausList: [
      {
        value: 'ACTIVE',
        color: 'green',
        label: '活动中'
      },
      {
        value: 'OFFLINE',
        color: 'grey',
        label: '已下线'
      },
      {
        value: 'FORBID',
        color: 'grey',
        label: '已禁用'
      }
    ],
    v1v2ApiStausList: [
      {
        value: 'ACTIVE',
        color: 'green',
        label: '活动中'
      },
      {
        value: 'OFFLINE',
        color: 'grey',
        label: '已下线'
      },
      {
        value: 'FORBID',
        color: 'grey',
        label: '已禁用'
      },
      {
        value: 'UNPUBLISHED',
        color: 'yellow',
        label: '更新待发布'
      },
      {
        value: 'DOC_UNPUBLISHED',
        color: 'yellow',
        label: '更新待发布'
      },
      {
        value: 'PUBLISHED',
        color: 'green',
        label: '已发布'
      },
      {
        value: 'DISABLED',
        color: 'red',
        label: '已禁用'
      },
      {
        value: 'ENABLED',
        color: 'grey',
        label: '未发布'
      }
    ],
    loadMenu: true
  },
  mutations: {
    setLoadMenu (state, payload) {
      state.loadMenu = payload
    }
  },
  actions: {

  },
  modules: {
    app,
    user,
    apiRoute,
    select
  }
});

export default store;
