
const select = {
    state: {
        apiGroup: {
            // uri: '/rest/api-group/list-with-api',
            uri: '/rest/api-group/commons/api-group-codes',
            data: []
        },
        serviceSupplier:{
            uri:'/rest/isp/commons/sp-codes',
            data:[]
        },
        customSolutionListForDoc:{
            uri:'/rest/custom-solution/list-for-doc',
            data:[]
        },
        errorCodeType:{
            uri:'/rest/api-group/error-code/commons/error-code-type/list',
            data:[]
        },
        errorCode:{
            uri:'/rest/error-code/commons/error-codes',
            data:[]
        },
        apiUri:{
            uri:'/rest/api-group/error-code/api-specific/list',
            data:[]
        },
        ceph:{
            uri:'/rest/filestore/commons/store-types',
            data:[]
        },
        ba_env : {
            uri:'/rest/backend-app/commons/deploy-mode/list',
            data:[]
        },
        ba_classLoader : {
            uri:'/rest/backend-app/commons/class-load-mode/list',
            data:[]
        },
        ba_service : {
            uri:'/rest/backend-app/commons/rpc-mode/list',
            data:[]
        },
        role_type : {
            uri: '/rest/acl/role/type',
            data : []
        },
        // 岗位
        position : {
            uri: '/rest/acl/user/position/list',
            data : []
        },
        // 用户状态
        user_status : {
            uri : '/rest/isp/status-enum',
            data : []
            // data : [
            //     {
            //         value : 'NORMAL',
            //         label : '正常',
            //         name : '正常'
            //     },
            //     {
            //         value : 'FROZEN',
            //         label : '冻结',
            //         name : '冻结'
            //     }
            // ]
        },
        // 用户类型
        user_type : {
            data : [
                {
                    value : 'SP_BASED',
                    label : 'SP用户',
                    name : 'SP用户'
                },
                {
                    value : 'PLATFORM',
                    label : '平台用户',
                    name : '平台用户'
                }
            ]
        },
        // 查询没有分配角色给用户的且用户所属的服务提供方列表
        sp_unassinged : {
            uri : '/rest/acl/isp/has-right-but-unassigned-to-user',
            data : []
        },
        sp_has_right : {
          uri : '/rest/acl/isp/has-right',
          data : []
        },
        sp_status : {
            data : [
                {
                    value : 'NORMAL',
                    label : '活动',
                    name : '活动'
                },
                {
                    value : 'FROZEN',
                    label : '冻结',
                    name : '冻结'
                }
                ,
                {
                    value : 'DELETE',
                    label : '已删除',
                    name : '已删除'
                }
            ]
        },
        opt_type:{
            uri : '/rest/backend-app/commons/rebuild-type/list',
            data : []
        },
        // 附件上传文件类型下拉框
        attachment_fileType:{
            uri: '/rest/attachments/commons/file-type',
            data: []
        },
// 产品

        // 产品类型
        productType : {
            uri: '/rest/product/commons/type',
            data: []
        },
        // 产品状态下拉框
        product_status : {
            uri: '/rest/product/commons/status',
            data: []
        },
        // 产品变更记录操作类型下拉框

        product_change_status : {
            uri: '/rest/product/commons/oper-type',
            data: []
        },
        product_authz_change_status : {
            uri: '/rest/product/authz/commons/oper-type',
            data: []
        },

        // 应用标识列表
        app_authz : {
            uri: '/rest/app/list/for-authz',
            data: []
        },
        // 服务分组授权状态
        service_authz_status : {
            uri: '/rest/product/authz/commons/status',
            data: []
        },
        // 服务分组授权有效期
        service_authz_expiry : {
            uri: '/rest/service-authz/commons/validity-term',
            data: []
        },
        // 服务分组授权应用标识
        service_authz_appKey :{
            uri : '/rest/app/commons/list',
            data : []
        },
        // api 常用示例值
        api_commmon_sample : {
            uri : '/rest/loader/internal-variable/expressions',
            data : []
        },
        // api 常用正则表达式
        api_common_pattern : {
            data : [
                {
                    label: '内置邮箱',
                    name: '内置邮箱',
                    value: `^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$`
                },{
                    label: '内置11位手机号',
                    name: '内置11位手机号',
                    value: `^[1-9]\\d{11}$`
                },{
                    label: '内置15/18位身份证',
                    name: '内置15/18位身份证',
                    value: `^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}$`
                },{
                    label: '内置15-21位银行卡',
                    name: '内置15-21位银行卡',
                    value: `^[0-9]\\d{15,21}$`
                },{
                    label: '内置3/4位CVV',
                    name: '内置3/4位CVV',
                    value: `^[0-9]\\d{3,4}$`
                }
            ]
        },
        // api model
        api_model :{
            uri:'/rest/api/commons/type/list',
            data:[]
        },
        /**
         * api 相关下拉框
         */
        // api 类型
        api_type : {
            uri:'/rest/api/commons/type/list',
            data: []
        },
        // api 请求类型
        api_method : {
            data : []
        },
        // api contentType
        api_content_type : {
            data: []
        },
        /**
         * 应用相关下拉框
         */
        // 应用类型
        app_type : {
            uri : '/rest/app/commons/type',
            data : []
        },
        // 协议类型
        protocol_type : {
            uri : '/rest/notify-config/commons/protocol',
            data : []
        },
        // 应用状态
        app_status : {
            uri : '/rest/app/commons/status',
            data : []
        },
        /**
         * 商户管理下拉框
         */
        provider_code : {
            uri : '/rest/isv/commons/provider-code',
            data : []
        },
        provider_status : {
            uri : '/rest/isv/commons/status',
            data : []
        },
        provider_status_oper : {
            uri : '/rest/isv/oper/commons/status',
            data : []
        },
        provider_type : {
            uri : '/rest/isv/commons/type',
            data : []
        },
        provider_change_type : {
            uri : '/rest/isv/commons/change-type',
            data : []
        },

        provider_operate_type : {
            uri : '/rest/isv/commons/operate-type',
            data : []
        },




        /**
         * 商户密钥管理下拉框
         */
        // 密钥类型
        cert_type : {
            uri : '/rest/isv/cert/commons/type',
            data : []
        },
        // 密钥类型
        cert_type_common : {
            uri : '/rest/isv/cert/commons/type',
            data : []
        },
        // 密钥状态
        cert_status : {
            uri : '/rest/isv/cert/commons/status',
            data : []
        },
        // 操作类型
        cert_operate_type : {
            uri : '/rest/isv/cert/commons/operate-type',
            data : []
        },
        // spi操作类型
        spi_opType : {
            uri : '/rest/spi/commons/op-type',
            data : []
        },
        // spi操作类型
        api_opType : {
            uri : '/rest/api/manage/commons/op-type',
            data : []
        },
        // model操作类型
        model_opType : {
            uri : '/rest/model/commons/op-type',
            data : []
        },
        // 密钥用途
        cert_usage : {
            uri : '/rest/isv/cert/commons/usage',
            data : []
        }
        // ,
        // acl_log_top_resource:{
        //     uri : '/rest/acl/log/commons/top-resource',
        //     data : []
        // }
    },
    mutations: {
    }
};

export default select;
