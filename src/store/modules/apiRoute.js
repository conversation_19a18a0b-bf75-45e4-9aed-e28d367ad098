import api from '~/api/newApiManage/apiManage';
import Api from '~/api/newApiManage/route';
export default {
  namespaced: true,
  state: {
    formType: {},
    apiMsg: {},
    valueListMap: {
      DUBBO: ['PARAM', 'RPC_CONTEXT'],
      HTTP: ['QUERY', 'BODY', 'HEADER'],
      resSelect:['BODY']
    },
    parametersList: [],
    parametersListSpecial: [],
    sysParamsList: [],
    serviceNameList: [],
    apiId: null
  },
  mutations: {
    setApiMsg (state, payload) {
      state.apiMsg = payload;
    },
    changeFormType (state, payload) {
      state.formType = Object.assign({}, state.formType, payload);
    },
    setApiId (state, payload) {
      state.apiId = payload;
    },
    handleParmeters (state, { key, type, index, item }) {
      if (type === 'delete') {
        state.routeForm[key].splice(index, 1);
      } else {
        state.routeForm[key].push(item);
      }
    }
  },
  actions: {
    getParametersList ({ state }) {
      api.getParametersList({
        apiId: state.apiId
      }).then(res => {
        const { status, data } = res.data;
        if (status === 'success') {
          state.parametersList = data.result;
          state.parametersListSpecial = data.result.filter(item => item.array);
        }
      });
    },
    getResponseList ({ state }) {
      api.getResponseList({
        apiId: state.apiId
      }).then(res => {
        const { status, data } = res.data;
        if (status === 'success') {
          state.responseList = data.result;
          // state.responseListSpecial = data.result.filter(item => item.array);
        }
      });
    },
    getSysParams ({ state }) {
      Api.sysParams({
        apiId: state.apiId
      }).then(res => {
        const { status, data } = res.data;
        if (status === 'success') {
          state.sysParamsList = data.result;
        }
      });
    },
    getServiceNameList ({ state }) {
      Api.getServiceNameList().then(res => {
        const { status, data } = res.data;
        if (status === 'success') {
          state.serviceNameList = data.result;
        }
      });
    }
  }
};
