<template>
  <div>
    <Modal v-model="hasAPiShow" width="50%">
      <Card dis-hover :bordered="false">
        <Row class="margin-top-20" type="flex" justify="space-between">
          <Col>
            <Button type="ghost" @click="deleteApi" id="btnDeleteApi"
              >批量删除</Button
            >
          </Col>
          <Col>
            <Button type="ghost" @click="showAddApiModal" id="btnAddApi"
              >添加API</Button
            >
          </Col>
        </Row>
        <Row class="margin-top-20">
          <Table
            id="table1"
            border
            :columns="hasApiColumns"
            :data="hasApiData"
            @on-selection-change="handleselection"
          ></Table>
          <loading :show="showLoadingHasApi"></loading>
        </Row>
        <Row class="margin-top-20" type="flex" justify="end">
          <Tooltip
            content="输入页码后点击回车键跳转页数"
            style="float: right"
            placement="bottom-end"
          >
            <Page
              :total="hasApiParams.total"
              :current="hasApiParams.pageNo"
              show-elevator
              @on-change="onHasApiPageChange"
            />
          </Tooltip>
        </Row>
      </Card>
      <div slot="footer">
        <Button type="ghost" id="popupClose" @click="hasAPiShow = false"
          >关闭</Button
        >
      </div>
    </Modal>
    <Modal
      id="modal_request_4"
      v-model="model_show"
      :closable="false"
      width="75%"
    >
      <p slot="header" style="color: #2d8cf0">
        <span style="color: black">添加API</span>
      </p>
      <Card dis-hover :bordered="false">
        <Row type="flex" align="middle">
          <Col span="20">
            <Col span="7">
              <Col span="6" class="margin-top-5">
                <span>API名称:</span>
              </Col>
              <Col span="18">
                <Input
                  clearable
                  v-model="getApiParams.apiTitle"
                  id="inputApiName"
                  placeholder="API名称"
                ></Input>
              </Col>
            </Col>
            <Col offset="1" span="7">
              <Col span="6" class="margin-top-5">
                <span>API URL:</span>
              </Col>
              <Col span="18">
                <Input
                  clearable
                  v-model="getApiParams.apiUri"
                  id="inputApiUrl"
                  placeholder="API URL"
                ></Input>
              </Col>
            </Col>
            <Col span="7" offset="1">
              <Col span="6" class="margin-top-5">
                <span>API类型:</span>
              </Col>
              <Col span="18">
                <Select
                  id="selectApiType"
                  ref="doc_status"
                  v-model="getApiParams.apiType"
                  placeholder="请选择（默认全部）"
                  clearable
                >
                  <Option
                    v-for="item in apiTypeOptions"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.desc }}</Option
                  >
                </Select>
              </Col>
            </Col>
          </Col>
          <Col span="4">
            <Col span="11" style="text-align: center">
              <Button id="btnSearch" type="primary" @click="getAllApi"
                >查询</Button
              >
            </Col>
            <Col span="11" style="text-align: center">
              <Button id="btnReset" type="ghost" @click="reset_Interface"
                >重置</Button
              >
            </Col>
          </Col>
        </Row>
        <Row type="flex" justify="start" class="margin-top-20">
          <Col span="20">
          <Col span="7">
            <Col span="6" class="margin-top-5">
              <span>API版本:</span>
            </Col>
            <Col span="18">
              <Select
                id="select_apiM_version"
                v-model="getApiParams.apiVersion"
                placeholder="请选择"
                @on-change="toggleApiVersion"
              >
                <Option
                  value="V1"
                  key="V1"
                >旧版API</Option>
                <Option
                  value="V2"
                  key="V2"
                >新版API</Option>
              </Select>
            </Col>
          </Col>
          </Col>
        </Row>
        <Row class="margin-top-20">
          <Table
            id="table2"
            border
            ref="singleTable"
            :columns="columns_ApiGroupList"
            :data="allApiData"
            @on-selection-change="handleAddApiselection"
            row-key="id"
          ></Table>
          <loading :show="showLoadingAllApi"></loading>
        </Row>
        <Row class="margin-top-20" type="flex" justify="end">
          <Tooltip
            content="输入页码后点击回车键跳转页数"
            style="float: right"
            placement="bottom-end"
          >
            <Page
              :total="getApiParams.total"
              :current="getApiParams.pageNo"
              show-elevator
              @on-change="onAllApiPageChange"
            />
          </Tooltip>
        </Row>
      </Card>
      <div slot="footer">
        <Button type="primary" id="popupConfirm" @click="confirmAddApi"
          >确定</Button
        >
        <Button type="ghost" id="popupCancel" @click="close_modal">关闭</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import Api from "~/api/api";
import loading from "~/views/my-components/loading/loading";
export default {
  name: "modal-add-api-dialog",
  components: {
    loading,
  },
  data() {
    return {
      hasAPiShow: false, // 已有Apimodal
      model_show: false,
      showLoadingAllApi: false,
      showLoadingHasApi: false,
      hasApiColumns: [
        {
          type: "selection",
          width: 60,
          align: "center",
        },
        {
          title: "API接口",
          render: (h, params) => {
            const { httpMethod, oldApi, apiUri } = params.row;
            const eleList = [
              h("tag", { props: { color: "blue" } }, oldApi ? "旧版 " : "新版 "),
              h("span", apiUri),
            ];
            if (httpMethod) {
              eleList.unshift(
                h("tag", { props: { color: "blue" } }, httpMethod)
              );
            }
            return h("div", eleList);
          },
          align: "center",
        },
        {
          title: "操作",
          width: 100,
          render: (h, params) => {
            return h(
              "Button",
              {
                props: {
                  type: "text",
                  size: "small",
                },
                on: {
                  click: () => {
                    this.deleteApiList = [params.row];
                    this.deleteApi();
                  },
                },
              },
              "移除"
            );
          },
          align: "center",
        },
      ],
      hasApiParams: {
        pageNo: 1,
        total: 10,
        errcodeId: "",
      },
      getApiParams: {
        apiVersion: "V2",
        apiGroupCode: null,
        apiTitle: null,
        apiUri: null,
        apiStatus: "ACTIVE",
        joinCode: "ERRCODE",
        joinValue: "",
        apiType: null,
        pageNo: 1,
        total: 10,
      },
      addApiPageParams: {},
      hasApiData: [],
      allApiData: [],
      deleteApiList: [],
      addApiList: [],
      apiTypeOptions: [],
      apiStatusOptions: [],
      // 表格头数据绑定
      columns_ApiGroupList: [
        {
          type: "selection",
          width: 60,
          align: "center",
        },
        {
          title: "API分组",
          key: "apiGroupCode",
          align: "center",
        },
        {
          title: "API名称",
          key: "apiTitle",
          align: "center",
        },
        {
          title: "API请求路径",
          render: (h, params) => {
            return h("div", [
              h("p", params.row.apiName),
              h("p", params.row.apiUri),
            ]);
          },
          align: "center",
        },
        {
          title: "API类型",
          width: 100,
          render: (h, params) => {
            const { desc } = this.apiTypeOptions.find(
              (item) => item.value === params.row.apiType
            );
            return h("div", [h("p", desc)]);
          },
          align: "center",
        },
        {
          title: "状态",
          render: (h, params) => {
            let color = 'green';
            let statusDesc = '';
            const { apiStatus } = params.row;
            const apiStatusItem = this.v1v2ApiStausList.find(item => item.value === apiStatus);
            if (apiStatusItem) {
              statusDesc = apiStatusItem.label;
              color = apiStatusItem.color;
            }
            return h("div", [
              h(
                "Tag",
                {
                  style: {
                    align: "center",
                  },
                  props: {
                    color: color,
                  },
                },
                statusDesc
              ),
            ]);
          },
          width: 120,
          align: "center",
        },
        {
          title: "更新日期/创建日期",
          width: 200,
          align: "center",
          render: (h, params) => {
            return h("div", [
              h("p", params.row.lastModifiedDate),
              h("p", params.row.createdDate),
            ]);
          },
        },
      ],
    };
  },
  computed: {
    v1v2ApiStausList () {
      return this.$store.state.v1v2ApiStausList
    }
  },
  methods: {
    async toggleApiVersion(version) {
      this.getApiParams.apiVersion = version;
      this.getApiParams.apiStatus = "ACTIVE";
      this.getApiParams.apiType = null;
      this.getApiParams.apiTitle = null;
      this.getApiParams.apiUri = null;
      this.getApiParams.pageNo = 1;
      this.getApiParams.total = 10;
      this.apiTypeOptions = [];
      this.allApiData = [];
      try {
        await this.getApiType();
        this.getAllApi();
      } catch (error) {}
    },
    reset_Interface() {
      this.toggleApiVersion("V2");
    },
    getHasApiListByCode() {
      this.showLoadingHasApi = true;
      Api.yop_errorCode_relevance_api(this.hasApiParams)
        .then((res) => {
          const { status, message, solutions, data } = res.data;
          if (status === "success") {
            const { items, pageNo, totalPageNum } = data.page;
            this.hasApiParams.pageNo = pageNo;
            this.hasApiData = items;
            this.hasApiParams.total = this.computPageTotal(
              pageNo,
              10,
              totalPageNum,
              items,
              () => {
                this.getHasApiListByCode();
              }
            );
          } else {
            this.$ypMsg.notice_error(this, "列表获取错误", message, solutions);
          }
        })
        .finally(() => {
          this.showLoadingHasApi = false;
        });
    },
    computPageTotal(pageNo, pageSize, totalPageNum, arr, cb) {
      if (!arr) return 0;
      if (!totalPageNum) return 0;
      if (pageNo > totalPageNum && totalPageNum > 0) {
        this.getApiParams.pageNo = totalPageNum;
        cb();
        return;
      }
      if (pageNo <= totalPageNum - 1) {
        return NaN;
      }
      return (totalPageNum - 1) * pageSize + arr.length;
    },
    getAllApi() {
      this.showLoadingAllApi = true;
      Api.yop_errorCode_join_api(this.getApiParams)
        .then((res) => {
          const { status, message, solutions, data } = res.data;
          if (status === "success") {
            const { items, pageNo, totalPageNum } = data.page;
            this.getApiParams.pageNo = pageNo;
            this.allApiData = items;
            this.getApiParams.total = this.computPageTotal(
              pageNo,
              10,
              totalPageNum,
              items,
              () => {
                this.getAllApi();
              }
            );
          } else {
            this.$ypMsg.notice_error(this, "列表获取错误", message, solutions);
          }
        })
        .finally(() => {
          this.showLoadingAllApi = false;
        });
    },
    getApiType() {
      return Api.yop_errorCode_get_api_type({
        apiVersion: this.getApiParams.apiVersion,
      }).then((res) => {
        const { status, message, solutions, data } = res.data;
        if (status === "success") {
          this.apiTypeOptions = data.result;
        } else {
          this.$ypMsg.notice_error(this, "列表获取错误", message, solutions);
        }
      });
    },
    getApiStatus() {
      Api.yop_errorCode_get_api_status({
        apiVersion: this.getApiParams.apiVersion,
      }).then((res) => {
        const { status, message, solutions, data } = res.data;
        if (status === "success") {
          this.apiStatusOptions = data.statusList;
        } else {
          this.$ypMsg.notice_error(this, "列表获取错误", message, solutions);
        }
      });
    },
    show_model({ id, apiGroupCode }) {
      this.hasApiParams.errcodeId = id;
      this.getApiParams.joinValue = id;
      this.getApiParams.apiGroupCode = apiGroupCode;
      this.hasAPiShow = true;
      this.deleteApiList = [];
      this.getHasApiListByCode();
    },
    close_modal() {
      this.model_show = false;
    },
    deleteApi() {
      if (this.deleteApiList.length === 0) {
        this.$ypMsg.notice_warning(this, "请选择需要删除API后再点击");
        return;
      }
      this.$Modal.confirm({
        title: "删除API",
        content: "确定删除该API吗？",
        "ok-text": "删除",
        onOk: () => {
          Api.yop_errorCode_delete_api({
            ids: this.deleteApiList.map((item) => item.id),
          }).then((res) => {
            const { status, message } = res;
            if (status === "success") {
              this.$ypMsg.notice_success(this, "移除成功");
              this.deleteApiList = [];
              this.getHasApiListByCode();
            } else {
              this.$ypMsg.notice_error(this,'错误', message);
            }
          });
        },
      });
    },
    showAddApiModal() {
      this.model_show = true;
      this.addApiList = [];
      this.allApiData = [];
      this.toggleApiVersion("V2");
    },
    confirmAddApi() {
      if (this.addApiList.length === 0) {
        this.$ypMsg.notice_warning(this, "请选择需要添加API后再点击");
        return;
      }
      Api.yop_errorCode_add_api({
        errcodeId: this.hasApiParams.errcodeId,
        apiIds: this.addApiList.map((item) => item.apiId),
      }).then((res) => {
        const { status, message } = res;
        if (status === "success") {
          this.$ypMsg.notice_success(this, "添加成功");
          this.model_show = false;
          this.getHasApiListByCode();
        } else {
          this.$ypMsg.notice_error(this, message);
        }
      });
    },
    handleselection(val) {
      this.deleteApiList = val;
    },
    handleAddApiselection(val) {
      this.addApiList = val;
    },
    onHasApiPageChange(val) {
      this.hasApiParams.pageNo = val;
      this.getHasApiListByCode();
    },
    onAllApiPageChange(val) {
      this.getApiParams.pageNo = val;
      this.getAllApi();
    },
  },
};
</script>

<style scoped>
</style>

