<style lang="less">
    @import '../../../styles/common.less';
    @import '../../API_Management_Open/api_Mangement_Open.less';

    .demo-badge-alone {
        background: #5cb85c !important;
    }

    .round {
        width: 16px;
        height: 16px;
        display: inline-block;
        font-size: 20px;
        line-height: 16px;
        text-align: center;
        color: #f00;
        text-decoration: none
    }
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">
                <Col span="7">
                <Col span="7" class="margin-top-10">
                <span>错误码类型:</span>
                </Col>
                <Col span="17">
                <common-select id="select_ec_1" ref="select_ect" @on-update="updateSelect_errorCodeType"
                               type="normal"
                               holder="请选择（默认全部）"
                               keyWord="result"
                               code="value"
                               title="displayName"
                               group="errorCodeType"
                               @on-loaded="select_callBack"
                               :default="this.data_select_errorCodeType"
                               :uri="this.$store.state.select.errorCodeType.uri"></common-select>
                </Col>
                </Col>
                <Col offset="1" span="7">
                <Col span="7" class="margin-top-10">
                <span>API分组:</span>
                </Col>
                <Col span="17">
                <common-select id="select_ec_2" ref="select_apiGroup"
                                @on-update="updateSelect_apiGroup"
                               type="combo"
                               keyWord="result"
                               holder="请选择（默认全部）"
                               code="apiGroupCode"
                               title="apiGroupName"
                               group="apiGroup"
                               @on-loaded="select_callBack"
                               :default="this.data_select_apiGroup"
                               :uri="this.$store.state.select.apiGroup.uri"></common-select>
                </Col>
                </Col>
                <Col offset="1" span="7">
                <Col span="7" class="margin-top-10">
                <span>API URI:</span>
                </Col>
                <Col span="17">
                <Input id='input_ec_1' class="margin-top-5" v-model="data_api_uri" placeholder="API URI"
                       @on-enter="search_interface"></Input>
                </Col>
                </Col>
                <Col  span="7">
                <Col span="7" class="margin-top-10">
                <span>错误码:</span>
                </Col>
                <Col span="17">
                <common-select id="select_ec_3" ref="select_ec" @on-update="updateSelect_errorCode"
                               type="combo"
                               holder="请选择（默认全部）"
                               keyWord="result"
                               code="errorCode"
                               title="errorMsg"
                               group="errorCode"
                               @on-loaded="select_callBack"
                               :default="this.data_select_errorCode"
                               :uri="this.$store.state.select.errorCode.uri"></common-select>
                </Col>
                </Col>
                <Col offset="1" class="margin-top-5" span="7">
                <Col span="7" class="margin-top-10">
                <span>子错误码:</span>
                </Col>
                <Col span="17">
                <Input id='input_ec_2' class="margin-top-5" v-model="data_subErrorCode" placeholder="子错误码"
                       @on-enter="search_interface"></Input>
                </Col>
                </Col>
                <Col class="margin-top-5" offset="1" span="7">
                <Col span="7" class="margin-top-10">
                </Col>
                <Col span="17">
                </Col>
                </Col>
                </Col>
                <Col span="4">
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_ec_1' type="primary" v-url="{url:'/rest/api-group/error-code/list'}" @click="search_interface">查询</Button>
                </Col>
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_ec_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Button id='btn_ec_3' class="margin-right-10" v-url="{url:'/rest/api-group/error-code/create'}" type="primary" @click="errorCode_add">新增错误码</Button>
                <Button id='btn_ec_10' class="margin-right-10" type="primary" v-url="{url:'/rest/api-group/error-code/batch-import'}"  @click="errorCode_add_batch">批量导入错误码</Button>
                </Col>
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_ec_1' border ref="selection" :columns="columns_errorCodeList" :data="data_errorCodeList"
                       @on-selection-change="handleselection"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10"
                          :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <!--<Spin fix v-show="show_loading_apiList">-->
                <!--<Icon type="load-c" size=18 class="yop-spin-icon-load"></Icon>-->
                <!--<div>数据加载中...</div>-->
                <!--</Spin>-->
                <loading :show="show_loading_errorCodeList"></loading>
            </Row>
            <Modal id="modal_ec_1" v-model="modal_create_errorCode" width="600" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" v-show="create_orEdit">新增错误码</span>
                    <span style="color:black" v-show="!create_orEdit">编辑错误码</span>
                </p>
                <div>
                    <Form ref="formCustom" :model="formCustom" :rules="ruleCustom" :label-width="120">
                        <FormItem label="错误码类型：" prop="errorCodeType" v-show="create_orEdit">
                            <!--<RadioGroup v-model="formCustom.errorCodeType" type="button" size="small" @on-change="typeChange">-->
                            <!--<Radio label="API分组公共"></Radio>-->
                            <!--<Radio label="API特有"></Radio>-->
                            <!--<Radio label="平台公共"></Radio>-->
                            <!--</RadioGroup>-->
                            <Button id="modal_ec_btn_1" size="small" style="width:82px;margin-right: 20px;" @click="typeChange('API分组公共')" :type="(!plateform_public && !api_specific)?'primary':'ghost'">API分组公共</Button>
                            <!-- <Button id="modal_ec_btn_2" size="small" style="width:82px;margin-right: 20px;" @click="typeChange('API特有')" :type="api_specific?'primary':'ghost'">API特有</Button> -->
                            <Button id="modal_ec_btn_3" size="small" style="width:82px;"@click="typeChange('平台公共')"  :disabled="platform_button" :type="plateform_public?'primary':'ghost'">平台公共</Button>
                        </FormItem>
                        <FormItem label="错误码类型：" v-show="!create_orEdit">
                            {{formCustom.errorCodeType}}
                        </FormItem>
                        <FormItem label="API分组：" prop="apiGroup" v-show="create_orEdit && !plateform_public">
                            <common-select id="modal_ec_select_1" ref="select_ag_m" @on-update="updateSelect_apiGroup_modal"
                                           type="combo"
                                           holder="请选择"
                                           keyWord="result"
                                           code="apiGroupCode"
                                           title="apiGroupName"
                                           group="apiGroup"
                                           @on-loaded="select_callBack"
                                           :default="this.formCustom.apiGroup"
                                           size="small"
                                           :uri="this.$store.state.select.apiGroup.uri"
                                           style="width:80%"></common-select>
                        </FormItem>
                        <FormItem label="API分组：" v-show="!create_orEdit && !plateform_public">
                            {{formCustom.apiGroupName}}({{formCustom.apiGroup}})
                        </FormItem>
                        <FormItem label="指定API URI：" prop="apiUri" v-show="create_orEdit && api_specific">
                            <!--<common-select @on-update="updateSelect_apiUri"-->
                            <!--type="combo"-->
                            <!--holder="请选择（默认全部）"-->
                            <!--keyWord="api-group-query-list"-->
                            <!--code="groupCode"-->
                            <!--title="groupTitle"-->
                            <!--group="apiGroup"-->
                            <!--@on-loaded="select_callBack"-->
                            <!--:default="this.formCustom.apiUri"-->
                            <!--size="small"-->
                            <!--:uri="this.$store.state.select.apiGroup.uri"-->
                            <!--style="width:80%"></common-select>-->
                            <Select id='modal_ec_select_2' size="small" v-model="formCustom.apiUri"  style="width:80%" placeholder="请选择">
                                <Option v-for="item in data_apiUri_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="指定API URI：" v-show="!create_orEdit && api_specific">
                            {{formCustom.apiUri}}
                        </FormItem>
                        <FormItem label="错误码：" prop="errorCode" v-show="create_orEdit && plateform_public ">
                            <!--<Input type="text" size="small" v-model="formCustom.errorCode" style="width:80%"></Input>-->
                            <common-select id="modal_ec_select_3" ref="select_ec_m" @on-update="updateSelect_errorCode_modal"
                                           type="combo"
                                           holder="请选择"
                                           keyWord="result"
                                           code="errorCode"
                                           title="errorMsg"
                                           group="errorCode"
                                           size="small"
                                           @on-loaded="select_callBack"
                                           :default="this.data_select_errorCode"
                                           style="width:80%"
                                           :uri="this.$store.state.select.errorCode.uri"></common-select>
                            </Col>
                        </FormItem>
                        <FormItem label="错误码：" v-show="!create_orEdit">
                            {{formCustom.errorCode}}
                        </FormItem>
                        <FormItem label="错误码：" prop="errorCode_public" v-show="create_orEdit && !plateform_public">
                            <Col>
                            {{formCustom.errorCode_public}}</Col>
                        </FormItem>
                        <FormItem label="子错误码：" prop="subErrorCode">
                            <Input id="modal_ec_input_1" :disabled="!create_orEdit" type="text" size="small" v-model="formCustom.subErrorCode" style="width:80%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">支持：字母、数字、中划线、冒号；全部大写、小写,最大支持64个字符</p>
                        <FormItem label="子错误码描述：" prop="subErrorCodeDes">
                            <Input id="modal_ec_input_2" type="textarea" size="small" v-model="formCustom.subErrorCodeDes"
                                   style="width:80%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">最大支持50字</p>
                        <FormItem label="解决方案：" prop="solution">
                            <Tabs id="modal_ec_tab_1" size="small" v-model="tabNow" style="width:80%">
                                <TabPane label="解决方案(对内)" name="solution_in">
                                    <text-editor ref="solution_in" size="350px" :id="'solution_in'"></text-editor>
                                </TabPane>
                                <TabPane label="解决方案(对外)" name="solution_out">
                                    <text-editor style="margin-left:2px;" ref="solution_out" size="350px"
                                                 :id="'solution_out'"></text-editor>
                                </TabPane>
                            </Tabs>
                        </FormItem>
                        <p class="yop-explain-120">最大支持1000字</p>
                    </Form>
                </div>
                <div slot="footer">
                    <Button id="modal_ec_btn_4" type="primary" @click="ok_create_apiGroup('formCustom')">确定
                    </Button>
                    <Button id="modal_ec_btn_5" type="ghost" @click="cancel_create_apiGroup">取消</Button>
                </div>
            </Modal>
            <Modal id="modal_request_2" v-model="modal_solution" width="90%" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black">解决方案</span>
                </p>
                <div>
                    <Table id='table_ec_2' border ref="selection" :columns="columns_solutionList"
                           :data="data_solutionList"></Table>
                    <Tabs id="modal_ec_tab_2" v-model="tabNow_model">
                        <TabPane label="解决方案(对内)" name="solution_in_modal">
                            <div v-html="innerSolution"></div>
                        </TabPane>
                        <TabPane label="解决方案(对外)" name="solution_out_modal">
                            <div v-html="outerSolution"></div>
                            <!--{{outerSolution}}-->
                        </TabPane>
                    </Tabs>
                </div>
                <div slot="footer">
                    <Button id="modal_ec_btn_6" type="primary" @click="errorCode_batch_close">关闭</Button>
                </div>
                <loading :show="show_loading_solution"></loading>
            </Modal>
            <Modal id="modal_request_3" v-model="modal_import_batch" width="60%" :closable="false" :mask-closable="false">
                    <p slot="header" style="color:#2d8cf0;">
                        <span style="color:black">错误码批量导入</span>
                    </p>
                    <div>
                        <Input id="modal_ec_input_10" type="textarea" size="small" v-model="errorCode_batch_content" placeholder="批量错误码需符合csv文件规范，有标题行（详情见文档链接）" :rows="10"></Input>
                        <Alert style="margin-top:10px;" ><a href="https://yeepay.feishu.cn/wiki/wikcnbGKpDxRuvwh8FRQZ9W8d6c" target="blank">错误码批量导入格式说明>></a></Alert>
                    </div>
                    <div slot="footer">
                        <Button id="modal_ec_btn_8" type="primary" @click="errorCode_batch_submit">确定</Button>
                        <Button id="modal_ec_btn_7" type="primary" @click="errorCode_batch_close">关闭</Button>
                    </div>
                    <loading-final :show="show_loading_errorCode_batch" content="正在导入错误码，请稍后..."></loading-final>
                </Modal>
                <AddApiDia ref="addApiDia" />
        </Card>
    </div>
</template>

<script>
    import api from '../../../api/api';
    import commonSelect from '../../common-components/select-components/selectCommon';
    import loading from '../../my-components/loading/loading';
    import loadingFinal from '../../my-components/loading/loading';
    import textEditor from '../../my-components/text-editor/text-editor-size';
    import util from '../../../libs/util';
    import AddApiDia from './modals/addApiDia.vue';

    export default {
        name: 'error-code-manage',
        components: {
            loading,
            commonSelect,
            textEditor,
            loadingFinal,
            AddApiDia
        },
        created () {
            if(localStorage.userType === 'PLATFORM'){
                this.platform_button = false;
            }else{
                this.platform_button = true;
            }
        },
        data () {
            // 子错误码验证
            const validate_subErrorCodeDes = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('子错误码描述不能为空'));
                }else if(util.getLength(value) > 50){
                    callback(new Error('长度不能大于50'));
                }else{
                    callback();
                }
            };
            // 子错误码描述验证
            const validate_subErrorCode = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('子错误码不能为空'));
                }else if(util.formatCheck7(value)){
                    callback(new Error('格式不正确'));
                }else if(util.getLength(value) > 64){
                    callback(new Error('长度不能大于64个字符'));
                }else{
                    callback();
                }
            };
            // 解决方案验证
            const validate_solution = (rule, value, callback) => {
                if(!this.$refs.solution_in.getContent_text()){
                    callback(new Error('解决方案(对内)不能为空'));
                }
                if(!this.$refs.solution_out.getContent_text()){
                    callback(new Error('解决方案(对外)不能为空'));
                }

                if(util.getLength(this.$refs.solution_in.getContent_text()) > 1000){
                    callback(new Error('解决方案(对内)不能超过1000字'));
                }else if(util.getLength(this.$refs.solution_out.getContent_text()) > 1000){
                    callback(new Error('解决方案(对外)不能超过1000字'));
                }else{
                    callback();
                }
            };
            return {
                // 批量导入窗口
                modal_import_batch : false,
                // 批量导入文本框内容
                errorCode_batch_content : "",
                // 批量导入loading
                show_loading_errorCode_batch : false,
                // 平台按钮是否可点击
                platform_button : false,
                // 指定api下拉框绑定数据
                data_apiUri_list : [],
                // 新增和编辑label  true:新增/false: 编辑　
                create_orEdit: true,
                // 加载绑定
                show_loading_errorCodeList: false,
                // 解决方案加载绑定
                show_loading_solution: false,
                // 相关下拉框count
                count_select_related: 3,
                // 错误码类型数据绑定
                data_select_errorCodeType: '',
                // 错误码数据绑定
                data_select_errorCode: '',
                // api分组数据绑定
                data_select_apiGroup: '',
                // api分组
                // api uri 数据绑定
                data_api_uri: '',
                // 错误码数据绑定
                data_errorCode: '',
                // 子错误码数据绑定
                data_subErrorCode: '',
                // 错误码管理列表总页数
                pageTotal: 10,
                // 错误码管理当前页
                pageNo: 1,
                // 错误码管理列表表头
                columns_errorCodeList: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    {
                        title: '错误码类型',
                        key: 'errorCodeType',
                        width: 110,
                        align: 'center'
                    },
                    {
                        title: 'API分组',
                        width: 140,
                        render: (h, params) => {
                            if(params.row.errorCodeTypeCode === 'PLATFORM_COMMON'){
                                return h('div','全部');
                            }else{
                                return h('div', [
                                    h('p', params.row.apiGroupName),
                                    h('p', '(' + params.row.apiGroupCode + ')')
                                ]);
                            }
                        },
                        align: 'center'
                    },
                    {
                        title: 'API URI',
                        'min-width': 150,
                        align: 'center',
                        render: (h, params) => {
                            if(params.row.errorCodeTypeCode !== 'API_SPECIFIC'){
                                return h('div','全部');
                            }else{
                                return h('div', [
                                    h('p', params.row.APIURI)
                                ]);
                            }
                        },

                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '错误码'),
                                h('p', '错误码描述')
                            ]);
                        },
                        width: 130,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.errorCode),
                                h('p', '(' + params.row.errorCodeDes + ')')
                            ]);
                        },
                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '子错误码'),
                                h('p', '子错误码描述')
                            ]);
                        },
                        // width: 130,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.subErrorCode),
                                h('p', '(' + params.row.subErrorCodeDes + ')')
                            ]);
                        },

                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ]);
                        },
                        width: 160,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [h('p', params.row.createTime),
                                h('p', params.row.lastModifyTime)]);
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'operations',
                        // width: 200,
                        render: (h, params) => {
                            const list = [
                                h('Button', {
                                    domProps:{
                                        id: "api_error_code_edit_btn1"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api-group/error-code/update'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.errorCode_modify(params.row.id);
                                        }
                                    }
                                }, '编辑'),
                                h('Button', {
                                    domProps:{
                                        id: "api_error_code_edit_btn2"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api-group/error-code/delete'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.errorCode_delete(params.row.id);
                                        }
                                    }
                                }, '删除'),
                                h('Button', {
                                    domProps:{
                                        id: "api_error_code_edit_btn3"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api-group/error-code/detail'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.errorCode_solution(params.row.id);
                                        }
                                    }
                                }, '解决方案'),
                            ]
                            if (params.row.errorCodeTypeCode === 'GROUP_COMMON') {
                                list.push(h('Button', {
                                    domProps:{
                                        id: "api_error_code_edit_btn4"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.relevance_api(params.row);
                                        }
                                    }
                                }, '关联API'));
                            }
                            return h('div', list);
                        }

                    }
                ],
                // 错误码管理列表数据
                data_errorCodeList: [
                    // {
                    //     id: '1L',
                    //     apiGroupName: '短信用途',
                    //     apiGroupCode: 'message',
                    //     errorCodeType: 'API特有',
                    //     APIName: '修改结算卡信息',
                    //     APIURI: '/rest/v1.0/bc-app/netin/modifySettleCardInfo',
                    //     errorCode: '40044',
                    //     errorCodeDes: '业务处理失败',
                    //     subErrorCode: '40011111',
                    //     subErrorCodeDes: '查询错误',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-03 18:40:09'
                    // }
                ],
                // 错误码管理列表表头
                columns_solutionList: [
                    {
                        title: '错误码类型',
                        key: 'errorCodeType',
                        'min-width': 110,
                        align: 'center'
                    },
                    {
                        title: 'API分组',
                        'min-width': 140,
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.apiGroupName),
                                h('p', '(' + params.row.apiGroupCode + ')')
                            ]);
                        },
                        align: 'center'
                    },
                    // {
                    //     title: 'API URI',
                    //     'min-width': 150,
                    //     align: 'center',
                    //     render: (h, params) => {
                    //         return h('div', [
                    //             h('p', params.row.APIURI)
                    //         ]);
                    //     },

                    // },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '错误码'),
                                h('p', '错误码描述')
                            ]);
                        },
                        'min-width': 130,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.errorCode),
                                h('p', '(' + params.row.errorCodeDes + ')')
                            ]);
                        },

                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '子错误码'),
                                h('p', '错误码描述')
                            ]);
                        },
                        'min-width': 130,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.subErrorCode),
                                h('p', '(' + params.row.subErrorCodeDes + ')')
                            ]);
                        },

                    }
                ],
                // 错误码管理列表数据
                data_solutionList: [
                    // {
                    //     id: '1L',
                    //     apiGroupName: '短信用途',
                    //     apiGroupCode: 'message',
                    //     errorCodeType: 'API特有',
                    //     APIName: '修改结算卡信息',
                    //     APIURI: '/rest/v1.0/bc-app/netin/modifySettleCardInfo',
                    //     errorCode: '40044',
                    //     errorCodeDes: '业务处理失败',
                    //     subErrorCode: '40011111',
                    //     subErrorCodeDes: '查询错误',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-03 18:40:09'
                    // }
                ],
                // 表单数据绑定
                formCustom: {
                    id : '',
                    errorCodeType: 'API分组公共',
                    apiUri: '',
                    apiGroup: '',
                    apiGroupName: '',
                    errorCode: '',
                    errorCode_public: '40044 （业务处理失败）',
                    subErrorCode: '',
                    subErrorCodeDes: '',
                    version :'',
                    solution: '123'
                },
                // 表单验证规则数据绑定
                ruleCustom: {
                    apiGroup: [
                        {required: true, message: 'api分组名称不能为空', trigger: 'change'},
                    ],
                    apiUri: [
                        {required: true, message: '指定api不能为空', trigger: 'change'},
                    ],
                    subErrorCode: [
                        {required: true, message: '子错误码不能为空', trigger: 'blur'},
                        { validator: validate_subErrorCode, trigger: 'blur' }
                    ],
                    subErrorCodeDes: [
                        {required: true, message: '子错误码描述不能为空', trigger: 'blur'},
                        { validator: validate_subErrorCodeDes, trigger: 'blur' }
                    ],
                    errorCodeType: [
                        {required: true},
                    ],
                    errorCode_public: [
                        {required: true},
                    ],
                    solution: [
                        {required: true},
                        { validator: validate_solution, trigger: 'blur' }
                    ],
                    errorCode: [
                        {required: true, message: '错误码不能为空', trigger: 'blur'},
                    ],
                },
                // 弹窗label
                modal_create_errorCode: false,
                // 解决方案当前tab
                tabNow: 'solution_in',
                // 解决方案详情tab
                tabNow_model : 'solution_in_modal',
                // 平台公共
                plateform_public: false,
                // api特有
                api_specific: false,
                // api分组公共
                api_common: true,
                // 解决方案弹窗label
                modal_solution: false,
                // 对内解决方案数据绑定
                innerSolution : '',
                // 对外解决方案数据绑定
                outerSolution : '',
                // 错误码类型对照对象
                errorCodeTypeMap :{
                    'API分组公共' :'公共错误码',
                    'API特有' : '特定错误码',
                    '平台公共' : '平台错误码'
                }

            };
        },
        mounted () {
            this.init();
        },
        beforeRouteLeave (to, from, next) {
            this.$store.state.current_apiGroup ='';
            this.$destroy();
            next();
        },
        methods: {
            // 初始化页面
            init() {
                if(this.$store.state.select.apiGroup.data.length > 0){
                    this.data_select_apiGroup = this.$store.state.current_apiGroup;
                    this.search_interface();
                }
            },
            // 查询接口
            search_interface () {
                this.show_loading_errorCodeList = true;
                let params = {
                    type: this.data_select_errorCodeType,
                    apiGroupCode: this.data_select_apiGroup,
                    apiUri: this.data_api_uri.trim(),
                    errorCode: this.data_select_errorCode,
                    subErrorCode: this.data_subErrorCode.trim(),
                    _pageNo: 1,
                    _pageSize: 10
                };
                util.paramFormat(params);
                api.yop_errorCode_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            this.pageTotal = response.data.data.result.totalPageNum * 10;
                            this.show_loading_errorCodeList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_errorCodeList = false;
                        }
                    }
                );
            },
            // 表格数据处理函数
            tableDataFormat (items) {
                this.data_errorCodeList = [];
                for (var i in items) {
                    this.data_errorCodeList.push({
                        id: items[i].id,
                        apiGroupName: util.empty_handler(this.data_name_handler(items[i].apiGroupCode, 'apiGroup')),
                        apiGroupCode: util.empty_handler(items[i].apiGroupCode),
                        errorCodeType: util.empty_handler(this.data_name_handler(items[i].type, 'errorCodeType')),
                        errorCodeTypeCode: util.empty_handler(items[i].type),
                        // APIName : '修改结算卡信息',
                        APIURI: util.empty_handler(items[i].apiUri),
                        errorCode: util.empty_handler(items[i].errorCode),
                        errorCodeDes: util.empty_handler(this.data_name_handler(items[i].errorCode, 'errorCode')),
                        subErrorCode: util.empty_handler(items[i].subErrorCode),
                        subErrorCodeDes: util.empty_handler(items[i].subErrorMsg),
                        createTime: util.empty_handler(items[i].createdDateTime),
                        lastModifyTime: util.empty_handler(items[i].lastModifiedDateTime)
                    });
                }
            },
            // 返回相应编码的名字
            data_name_handler (code, name) {
                if (code) {
                    let group = this.$store.state.select[name].data;
                    for (var i in group) {
                        if (group[i].value === code) {
                            return group[i].name;
                        }
                    }
                } else {
                    return '';
                }
            },
            // 返回相应名字的编码
            data_code_handler (title, name) {
                if (title) {
                    let group = this.$store.state.select[name].data;
                    for (var i in group) {
                        if (group[i].name === title) {
                            return group[i].value;
                        }
                    }
                } else {
                    return '';
                }
            },
            // 重置接口函数
            reset_Interface () {
                this.$refs.select_ect.resetSelected();
                // this.data_select_errorCodeType = '';
                // this.data_select_apiGroup = '' ;
                this.$refs.select_ec.resetSelected();
                this.$refs.select_apiGroup.resetSelected();
                this.data_api_uri = '';
                this.data_select_errorCode = '';
                this.data_subErrorCode = '';
            },
            // api分组下拉框更新
            updateSelect_apiGroup (val) {
                this.data_select_apiGroup = val;
            },
            // 弹窗内api分组下拉框更新
            updateSelect_apiGroup_modal (val) {
                this.formCustom.apiGroup = val;
                if(this.formCustom.errorCodeType === 'API特有'){
                    this.data_apiUri_list = [];
                    this.formCustom.apiUri = '';
                }
                if(!!val){
                    api.yop_errorCode_apiSpecific({apiGroupCode : val}).then(
                        (response) => {
                            let result = response.data.data.result;
                            this.data_apiUri_list = []
                            result.forEach(
                                item =>{
                                    this.data_apiUri_list.push({
                                        label : item.apiUri+'('+util.empty_handler(item.description)+')' ,
                                        value : item.apiUri
                                    })
                                }
                            )
                        }
                    )
                }
            },
            // 错误码类型数据更新
            updateSelect_errorCodeType (val) {
                this.data_select_errorCodeType = val;
            },
            // 错误码数据更新
            updateSelect_errorCode (val) {
                this.data_select_errorCode = val;
            },
            // 错误码数据更新
            updateSelect_errorCode_modal (val) {
                this.formCustom.errorCode = val;
            },
            // apiUri数据更新
            updateSelect_apiUri (val) {
                this.formCustom.apiUri = val;
            },
            // 下拉框加载完处理函数
            select_callBack () {
                this.count_select_related--;
                if (this.count_select_related === 0) {
                    this.search_interface();
                }
            },
            // 列表查询函数
            pageRefresh (val) {
                this.show_loading_errorCodeList = true;
                let params = {
                    type: this.data_select_errorCodeType,
                    apiGroupCode: this.data_select_apiGroup,
                    apiUri: this.data_api_uri,
                    errorCode: this.data_select_errorCode,
                    subErrorCode: this.data_subErrorCode,
                    _pageNo: 1,
                    _pageSize: 10
                };
                if (val) {
                    params._pageNo = val;
                }
                util.paramFormat(params);
                api.yop_errorCode_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            this.pageTotal = response.data.data.result.totalPageNum * 10;
                            this.show_loading_errorCodeList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_errorCodeList = false;
                        }
                    }
                );
            },
            // 选择处理函数
            handleselection () {

            },
            // 错误码修改函数
            errorCode_modify (id) {
                this.create_orEdit = false;
                this.formData_reset();
                this.modal_create_errorCode = true;
                api.yop_errorCode_detail({id:id}).then(
                    (response)=>{
                        let errorCodeType = response.data.data.result.type;
                        this.typeChange(this.data_name_handler(response.data.data.result.type, 'errorCodeType'));
                        this.detail_format(response.data.data.result)
                    }
                )
            },
            // 详细信息处理函数
            detail_format (result) {
                this.formCustom.apiGroup = util.empty_handler(result.apiGroupCode);
                this.formCustom.apiGroupName = util.empty_handler(this.data_name_handler(result.apiGroupCode, 'apiGroup'));
                this.formCustom.errorCodeType = util.empty_handler(this.data_name_handler(result.type, 'errorCodeType'));
                this.formCustom.apiUri = util.empty_handler(result.apiUri);
                this.formCustom.errorCode = util.empty_handler(result.errorCode)+'('+util.empty_handler(this.data_name_handler(result.errorCode, 'errorCode'))+')';
                this.formCustom.subErrorCode = util.empty_handler(result.subErrorCode);
                this.formCustom.subErrorCodeDes = util.empty_handler(result.subErrorMsg);
                this.formCustom.id = result.id;
                this.formCustom.version = result.version;
                this.$refs.solution_in.setContent(result.innerSolution);
                this.$refs.solution_out.setContent(result.outerSolution);
            },
            // 错误码删除函数
            errorCode_delete (id) {
                this.$Modal.confirm({
                    title: '删除错误码',
                    content: '确定删除该错误码？',
                    'ok-text': '删除',
                    onOk: () => {
                        api.yop_errorCode_delete({id: id}).then(
                            (response) => {
                                if (response.status === 'success') {
                                    this.$ypMsg.notice_success(this,'错误码删除成功');
                                    this.search_interface();
                                } else {
                                    this.$ypMsg.notice_error(this,'错误码删除错误',response.message,response.solution);
                                }
                            }
                        );
                    }
                });
            },
            // 解决方案函数
            errorCode_solution (id) {
                this.show_loading_solution = true;
                this.modal_solution = true;
                this.tabNow_model = 'solution_in_modal';
                api.yop_errorCode_detail({id: id}).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat_solution(response.data.data.result);
                            this.innerSolution = response.data.data.result.innerSolution;
                            this.outerSolution = response.data.data.result.outerSolution;
                            this.show_loading_solution = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_solution = false;
                        }
                    }
                );

            },
            // 解决方案数据处理
            tableDataFormat_solution (items) {
                this.data_solutionList = [];
                this.data_solutionList.push({
                    id: items.id,
                    apiGroupName: util.empty_handler(this.data_name_handler(items.apiGroupCode, 'apiGroup')),
                    apiGroupCode: util.empty_handler(items.apiGroupCode),
                    errorCodeType: util.empty_handler(this.data_name_handler(items.type, 'errorCodeType')),
                    // APIName : '修改结算卡信息',
                    APIURI: util.empty_handler(items.apiUri),
                    errorCode: util.empty_handler(items.errorCode),
                    errorCodeDes: util.empty_handler(this.data_name_handler(items.errorCode, 'errorCode')),
                    subErrorCode: util.empty_handler(items.subErrorCode),
                    subErrorCodeDes: util.empty_handler(items.subErrorMsg),
                    createTime: util.empty_handler(items.createdDateTime),
                    lastModifyTime: util.empty_handler(items.lastModifiedDateTime)
                });
            },
            // 关联API
            relevance_api (row) {
                this.$refs.addApiDia.show_model(row);
            },
            // 新增错误码
            errorCode_add () {
                this.create_orEdit = true;
                this.formData_reset();
                let that = this
                setTimeout(function () {
                    that.formCustom.apiUri = '123';
                    that.formCustom.errorCode = '123';
                }, 300);
                this.formCustom.apiUri = '123';
                this.formCustom.errorCode = '123';
                this.plateform_public = false;
                this.api_specific = false;
                this.modal_create_errorCode = true;
            },
            // 批量导入错误码
            errorCode_add_batch () {
                this.errorCode_batch_content = '';
                this.modal_import_batch = true;
            },
            // 批量导入错误码提交
            errorCode_batch_submit () {
                if(this.errorCode_batch_content.trim() === ''){
                    this.$Modal.warning({
                        title: '警告',
                        content: '内容不能为空'
                    });
                }else{
                    this.show_loading_errorCode_batch = true;
                    let param = new FormData();
                    param.append('csvText',this.errorCode_batch_content);
                    api.yop_errorCode_add_batch(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        if(response.data.result.length === 0){
                                            this.$ypMsg.notice_success(this,'错误码批量导入成功');
                                            this.modal_import_batch = false;
                                            this.search_interface();
                                        }else{
                                            let failureReason = response.data.result;
                                            this.$ypMsg.notice_scroll(this,'error','格式问题',this.errorCode_batch_result_handler(failureReason),0);
                                        }
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        this.modal_import_batch = false;
                                    }
                                    this.show_loading_errorCode_batch = false;
                                }
                            )
                }
            },
            // 错误码提示批量处理
            errorCode_batch_result_handler (arr) {
                let result = ''
                for(var i in arr){
                    result = result + '第'+arr[i].lineNo+'行：'+arr[i].reason+'<br/>';
                }
                return result;
            },
            // 批量导入错误码关闭
            errorCode_batch_close () {
                this.modal_import_batch = false;
                this.modal_solution = false
            },
            errorCode_handler () {
                if(this.formCustom.errorCodeType === '平台公共'){
                    return this.formCustom.errorCode
                }else{
                    return '40044'
                }
            },
            // 弹窗确定按钮功能函数
            ok_create_apiGroup (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        if(this.create_orEdit){
                            let  param= {
                                type : this.data_code_handler(this.formCustom.errorCodeType,'errorCodeType'),
                                apiGroupCode : this.formCustom.apiGroup,
                                apiUri : this.formCustom.apiUri,
                                errorCode : this.errorCode_handler(),
                                subErrorCode: this.formCustom.subErrorCode,
                                subErrorMsg: this.formCustom.subErrorCodeDes,
                                innerSolution : this.$refs.solution_in.getContent(),
                                outerSolution : this.$refs.solution_out.getContent()
                            }
                            if(this.plateform_public){
                                delete param['apiGroupCode'];
                                delete param['apiUri'];
                            }else if(!this.plateform_public && !this.api_specific){
                                delete param['apiUri'];
                            }
                            util.paramFormat(param);
                            api.yop_errorCode_add(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        if(response.data.result === 'success'){
                                            this.$ypMsg.notice_success(this,'创建成功');
                                            this.modal_create_errorCode = false;
                                            this.search_interface();
                                        }else{
                                            this.$ypMsg.notice_warning(this,'子错误码已存在');
                                        }
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        this.modal_create_errorCode = false;
                                    }
                                }
                            )
                        }else{
                            let param = {
                                id : this.formCustom.id,
                                version : this.formCustom.version,
                                subErrorCode: this.formCustom.subErrorCode,
                                subErrorMsg: this.formCustom.subErrorCodeDes,
                                innerSolution : this.$refs.solution_in.getContent(),
                                outerSolution : this.$refs.solution_out.getContent()
                            }
                            util.paramFormat(param);
                            //可能需要对密钥处理剔除
                            api.yop_errorCode_update(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        if(response.data.result === 'success'){
                                            this.$ypMsg.notice_success(this,'修改成功');
                                            this.modal_create_errorCode = false;
                                            this.search_interface();
                                        }else{
                                            this.$ypMsg.notice_warning(this,'子错误码已存在');
                                        }
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        this.modal_create_errorCode = false;
                                    }
                                }
                            )
                        }

                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 弹窗确定按钮功能函数
            cancel_create_apiGroup () {
                this.modal_create_errorCode = false;
                this.formData_reset();
            },
            // 表单数据重置函数
            formData_reset() {
                this.formCustom.id ='';
                this.formCustom.errorCodeType = 'API分组公共';
                this.formCustom.apiUri = '';
                this.$refs.select_ag_m.resetSelected();
                this.$refs.select_ec_m.resetSelected();
                // this.formCustom.apiGroup = '';
                // this.formCustom.errorCode = '';
                this.formCustom.subErrorCode ='';
                this.formCustom.version = '';
                this.formCustom.subErrorCodeDes ='';
                this.$refs.solution_in.setContent(' ');
                this.$refs.solution_out.setContent(' ');
                this.tabNow = 'solution_in',
                this.formCheck_reset();
            },
            // 表单状态还原
            formCheck_reset(){
                this.$refs.formCustom.resetFields();
            },
            // 表单改变
            typeChange (val) {
                if (val === '平台公共') {
                    this.formCheck_reset();
                    this.$refs.select_ec_m.resetSelected();
                    this.formCustom.apiGroup ='123';
                    this.formCustom.apiUri = '123';
                    this.formCustom.errorCode = '';
                    this.plateform_public = true;
                    this.api_specific = false;
                    this.formCustom.errorCodeType = '平台公共';
                    this.$refs.solution_in.setContent(' ');
                    this.$refs.solution_out.setContent(' ');
                } else if (val === 'API分组公共') {
                    this.formCheck_reset();
                    this.$refs.select_ag_m.resetSelected();
                    this.formCustom.errorCodeType = 'API分组公共';
                    this.formCustom.apiUri = '123';
                    this.formCustom.errorCode = '123';
                    this.plateform_public = false;
                    this.api_specific = false;
                    this.$refs.solution_in.setContent(' ');
                    this.$refs.solution_out.setContent(' ');
                } else if (val === 'API特有') {
                    this.formCheck_reset();
                    this.$refs.select_ag_m.resetSelected();
                    this.formCustom.errorCodeType = 'API特有';
                    this.formCustom.errorCode = '123';
                    this.plateform_public = false;
                    this.api_specific = true;
                    this.$refs.solution_in.setContent(' ');
                    this.$refs.solution_out.setContent(' ');
                }
            },
            // 解决方案关闭
            solution_close () {
                this.modal_solution = false;
            }

        }
    };
</script>

<style scoped>

</style>
