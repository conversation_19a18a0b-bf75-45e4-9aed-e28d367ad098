<style lang="less">
    @import '../../styles/common.less';
    @import '../API_Management_Open/api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
    .gray{
        color: gray;
    }
    .blue{
        color: blue;
    }
    .green{
        color: #31AD37;
    }
    .detail-tab span{
        // color: #2d8cf0;
    }
    .padding-none{
        padding: 0;
    }
    .hide{
        display: none!important;
    }
    .over-scroll{
        .ivu-modal{
            max-height: 600px;
            overflow-y: scroll;
        }
    }
    .dis-block{
        display: block;
        text-align: center;
        width: 100%;
    }
    .cursor-pointer{
        cursor: pointer;
    }
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="24">
                <Col span="6" >
                <Col  span="8">
                <Col class="margin-top-10" >API分组：&nbsp;</Col>
                </Col>
                <Col span="14">
                <!--<apigroup @on-update="updateSelect_apiGroup"> </apigroup>-->
                <common-select id="select_ag_1" ref="select_ag" @on-update="updateSelect_apiGroup"
                               type="combo"
                               keyWord="result"
                               code="apiGroupCode"
                               title="apiGroupName"
                               group="apiGroup"
                               holder="请选择（默认全部）"
                               :default="this.data_select_apiGroup"
                               :uri="this.$store.state.select.apiGroup.uri"></common-select>
                </Col>
                </Col>
                <Col span="6" >
                <Col  span="8">
                <Col class="margin-top-10" >服务提供方：&nbsp;</Col>
                </Col>
                <Col span="14">
                <common-select id="select_ag_2" ref="select_sp" @on-update="updateSelect_serviceSupplier"
                               type="combo"
                               keyWord="result"
                               code="spCode"
                               title="spName"
                               group="serviceSupplier"
                               :default="this.data_select_serviceSupplier"
                               @on-loaded="select_callBack"
                               holder="请选择（默认全部）"
                               :uri="this.$store.state.select.serviceSupplier.uri"></common-select>
                </Col>
                </Col>
                <Col span="8" >
                <Col  span="6">
                <Col class="margin-top-10">创建时间：&nbsp;</Col>
                </Col>
                <date-picker ref="datepicker" @on-start-change="update_start_date"
                             @on-end-change="update_end_date"
                             :default_start="this.dateStart"
                             :default_end="this.dateEnd"
                ></date-picker>
                <!--<Col span="8">-->
                <!--<DatePicker  class="margin-top-5" :options="optionsStart" :clearable="false" v-model="requestDateStart" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择起始日期" ></DatePicker>-->
                <!--</Col>-->
                <!--<Col class="margin-top-10" span="1">—</Col>-->
                <!--<Col span="8">-->
                <!--<DatePicker  class="margin-top-5" :options="optionsStart" :clearable="false" v-model="requestDateEnd" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择结束日期" ></DatePicker>-->
                <!--</Col>-->
                </Col>
                <Col class="margin-top-5" span="4" style="text-align: center">
                <Button class="margin-right-20" id='btn_ag_1' v-url="{url:'/rest/api-group/list'}" type="primary" @click="search_Interface()">查询</Button>
                <Button  id='btn_ag_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
                <Col class="margin-top-20" span = "8">
                <Button id='btn_ag_3' type="primary" v-url="{url:'/rest/api-group/create'}" @click="create_apiGroup">新增API分组</Button>
                </Col>
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_ag_1' border ref="selection" :columns="columns_ApiGroupList" :data="data_ApiGroupList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="search_Interface"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_apiGroup"></loading>
            </Row>
            <Modal id="modal_ag_1" v-model="modal_create_apiGroup" width="1300" :closable="false" :mask-closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" v-show="create_orEdit">新增api分组</span>
                    <span style="color:black" v-show="!create_orEdit">编辑api分组</span>
                </p>
                <div>
                    <Form ref="formCustom" :model="formCustom" :rules="ruleCustom" :label-width="150">
                        <!--<p style="line-height:40px;"> <star_red></star_red>服务提供方：-->
                        <!--<serverSupplier size="small" @on-update="updateSelect_serviceSupplier" style="width:70%"> </serverSupplier>-->
                        <!--</p>-->
                        <FormItem label="服务提供方：" prop="supplier">
                            <common-select id="modal_sg_select_1" ref="select_sp_m" @on-update="updateSelect_serviceSupplier_inModal"
                                           type="combo"
                                           keyWord="result"
                                           code="spCode"
                                           title="spName"
                                           group="serviceSupplier"
                                           :default="this.formCustom.supplier"
                                           size="small"
                                           :uri="this.$store.state.select.serviceSupplier.uri" style="width:85%"></common-select>
                        </FormItem>
                        <p class="yop-explain">
                        没有合适的服务提供方？<a @click="create_service_supplier_jump">新建></a></p>
                        <FormItem label="API分组编码：" prop="apiGroupCode" v-show="create_orEdit">
                            <Input id="modal_sg_input_1"  type="text" size="small" v-model="formCustom.apiGroupCode" style="width:85%"></Input>
                        </FormItem><p class="yop-explain-150" v-show="create_orEdit" >编码首位必须为字母，支持小写字母、数字、中横线，最大支持输入24字符</p>
                        <FormItem label="API分组编码：" v-show="!create_orEdit">
                            <p> {{formCustom.apiGroupCode}}</p>
                        </FormItem>
                        <FormItem label="API分组名称：" prop="apiGroup">
                            <Input id="modal_sg_input_2" type="text" size="small" v-model="formCustom.apiGroup" style="width:85%"></Input>
                        </FormItem><p class="yop-explain-150">最大支持16个汉字</p>
                        <FormItem label="接口描述：" prop="description">
                            <text-editor ref="apiGroupDes"  :id="'apiDes'" :size="'800'" style="display: inline-flex"></text-editor>

                        </FormItem>
                        <!-- <p class="yop-explain-130">请用500个字以内描述接口</p> -->
                    </Form>

                </div>
                <div slot="footer">
                    <Button id="modal_sg_btn_2" type="primary" @click="ok_create_apiGroup('formCustom')">确定</Button>
                    <Button id="modal_sg_btn_1" type="ghost"  @click="cancel_create_apiGroup('formCustom')">取消</Button>
                </div>
            </Modal>

    <!-- 点击灰度规则弹窗 -->
            <Modal id="modal_apiGroup_1" v-model="modal_apiGroup_list" width="800" :closable="false" title="用户列表">
                <p slot="header" style="color:#2d8cf0;height:auto">
                    <span style="color:black" >API分组：{{modal_apiGroup}}</span>
                    <Button id='btn_apiGroup_1' class="margin-right-10" style="float:right;" type="primary" @click="apiGroup_create">新增</Button>
                </p>
                <div>
                    <Row class="margin-top-10">
                        <Col span="24">
                        <Table  border :columns="columns_data_apiGroupList" :data="data_apiGroupList_apiGroup"></Table>
                        </Col>
                        <loading :show="show_loading_apiGroupList"></loading>
                    </Row>
                </div>
                <div slot="footer">
                    <Button  id="modal_apiGroup_btn_1" type="primary" @click="ok_add_apiGroupList">确定
                    </Button>
                    <Button id="modal_acl_role_btn_9"  type="ghost" @click="cancel_apiGroup_grayLevel">关闭</Button>
                </div>
            </Modal>
    <!-- 新增灰度规则 -->
            <Modal id="modal_apiGroup_2" v-model="modal_add_apiGroup" width="800" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" >{{policyName_default}}</span>
                </p>
                <div class="tipPart">
                    <p>1.请根据正常环境、灰度环境的机器数合理配置流量权重；</p>
                    <p>2.为降低系统性风险默认最大生效权重为50%；</p>
                    <p>3.创建、修改规则后大约1分钟生效；</p>
                    <p>4.灰度过程请逐步放量，密切观察监控数据；</p>
                    <p>5.灰度结束后请务必先关闭灰度规则后再结束YCE灰度环境，避免流量打垮灰度环境从而影响商户交易。</p>
                </div>
                
                <div>
                    <Form ref="form_apiGroup" :model="form_apiGroup" :rules="rule_apiGroup" :label-width="120">
                        <FormItem label="API分组：">
                           {{modal_apiGroup}}
                        </FormItem>
                        <FormItem label="条件：">
                            所有
                        </FormItem>
                        <FormItem label="比例：" prop="weight_default">
                            <Input type="text" v-model="form_apiGroup.weight_default"
                           style="width:13%" max="100" min= "0"></Input>%
                        </FormItem>
                        <FormItem label="描述：" prop="policyDesc_default">
                            <Input type="textarea" v-model="form_apiGroup.policyDesc_default" style="width:55%" maxLength="60"></Input>
                        </FormItem>
                       
                    </Form>
                </div>
                <div slot="footer">
                    <Button id="modal_acl_role_btn_1" type="primary" @click="ok_add_apiGroup('form_apiGroup')">确定
                    </Button>
                    <Button id="modal_acl_role_btn_2" type="ghost" @click="cancel_add_apiGroup">取消</Button>
                </div>
                <loading :show="show_loading_add_apiGroup"></loading>
            </Modal>
        <!-- git 同步配置弹框 -->
        
        <Modal id="modal_apiGroup_2" v-model="gitConfigFlag" :transfer='false' width="800" :closable="false">
            <p slot="header" style="color:#2d8cf0;height:auto">
                <span style="color:black" >Git同步配置</span>
            </p>
            <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                    <p>API分组： {{currentApiGroup.apiGroupName}}</p>
                    <p class="margin-top-10">同步开关：<Checkbox v-model="gitConfigData.syncOpen"></Checkbox> </p>
                    <div class="margin-top-10">Git配置： 
                        <p style="display:inline-block;" v-if="qaProductFlag && createConfigFlag"> 依赖qa推送的信息</p>
                        
                        <Form ref="" v-else :label-width="150" style="width: 696px;" inline>
                            <FormItem label="Repository"  class="width-100-perc" >
                                <Input style="width:290px;" :disabled="qaProductFlag && !createConfigFlag" @on-blur="getGitBranchList" v-model="gitConfigData.gitRepository" ></Input>
                                <span class="margin-left-5">Group/Project,示例:notifier/notifier-api </span>  <i @click="showTip()" class="iconfont cursor-pointer green">&#xe6d1;</i> 
                            </FormItem>
                            <FormItem  label="Branch"  class="width-100-perc" >
                                <Select :disabled="qaProductFlag && !createConfigFlag"  v-model="gitConfigData.gitBranch"  style="width:290px;">
                                    <Option v-for="item in branchList" :value="item.value" :key="item.label">{{item.label}}</Option>
                                </Select>
                            </FormItem>
                            <FormItem label="文件路径"  class="width-100-perc" >
                                <Input :disabled="qaProductFlag && !createConfigFlag" style="width:290px;" @on-blur="checkUpdate" v-model="gitConfigData.filePath" ></Input>
                            </FormItem>
                            <br>
                            <br>
                            <FormItem label="当前commitId"  class="width-100-perc" >
                                <Input disabled style="width:290px;" v-model="gitConfigData.currentCommitId" placeholder="无" ></Input>
                            </FormItem>
                            <FormItem label="最新commitId"  class="width-100-perc" >
                                <Input disabled style="width:290px;"  v-model="gitConfigData.latestCommitId" placeholder="无" ></Input>
                            <Button id="git_config_btn1" v-if="!qaProductFlag" v-url="{url:'/rest/api-group/git-sync/latest-commit'}" class="margin-left-10" @click="checkUpdate" type="primary">检查更新</Button>
                            </FormItem>
                        </Form>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <Button  id="modal_apiGroup_btn_2" type="primary" v-url="{url:'/rest/api-group/git-sync/config'}" @click="saveGitConfig">保存
                </Button>
                <Button id="modal_acl_role_btn_10"  type="ghost" @click="cancelGitConfig">取消</Button>
            </div>
        </Modal>
        <!-- 同步确认（对比详情） -->
        <Modal v-model="synchronizationFlag" :transfer='false' class="over-scroll" width="900">
            <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
                <Icon type="ios-information-circle"></Icon>
                <span style="color:black" >Git同步</span>
            </p>
            <p >当前版本: {{gitConfigData.currentCommitId ? gitConfigData.currentCommitId : '无'}}</p>
            <p class="margin-top-10">最新版本: {{gitConfigData.latestCommitId ? gitConfigData.latestCommitId : '无'}}</p>
            <p class="margin-top-10">变更详情：</p>
            <div class="change-detail-con margin-top-10">
                <CodeDiff :old-string="currentContent" :new-string="latestContent" outputFormat='side-by-side' :context="10" />
            </div>
            <div slot="footer">
                <Button  id="modal_apiGroup_btn_3" type="primary" v-url="{url:'/rest/api-group/git-sync/sync'}" @click="confirmCheckoutResult">确定
                </Button>
                <Button id="modal_acl_role_btn_11"  type="ghost" @click="synchronizationFlag = false">取消</Button>
            </div>
        </Modal>
        <!-- 同步结果 -->
        <Modal v-model="resultFlag" :transfer='false'  width="900">
            <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
                <Icon type="ios-information-circle"></Icon>
                <span style="color:black" >同步结果</span>
            </p>
            <div v-if="syncResult.apiSyncResult.total && syncResult.apiSyncResult.total != '0' && syncResult.apiSyncResult.total != 0" class="margin-top-10 detail-tab"> <h3> API: </h3>
                <p class="margin-top-10">总计：<span>{{syncResult.apiSyncResult.total}}</span></p>
                <p class="margin-top-10">成功：<span>{{syncResult.apiSyncResult.success ? syncResult.apiSyncResult.success.length : 0}}</span> <Button id="git_config_detail_btn1" v-if="syncResult.apiSyncResult.success && syncResult.apiSyncResult.success.length > 0" size="small" type="primary" @click="showDetail('apiSyncResult','success')">详情</Button></p>
                <p class="margin-top-10">失败：<span>{{syncResult.apiSyncResult.failed ? syncResult.apiSyncResult.failed.length : 0}}</span> <Button id="git_config_detail_btn2" v-if="syncResult.apiSyncResult.failed && syncResult.apiSyncResult.failed.length > 0"  size="small" type="primary"  @click="showDetail('apiSyncResult','failed')">详情</Button></p>
                <p class="margin-top-10">忽略：<span>{{syncResult.apiSyncResult.ignored ? syncResult.apiSyncResult.ignored.length : 0}}</span> <Button id="git_config_detail_btn3" v-if="syncResult.apiSyncResult.ignored && syncResult.apiSyncResult.ignored.length > 0" size="small" type="primary"  @click="showDetail('apiSyncResult','ignored')">详情</Button></p>
            </div>
            <div v-if="syncResult.spiSyncResult.total && syncResult.spiSyncResult.total != '0' && syncResult.spiSyncResult.total != 0" class="margin-top-10 detail-tab"> <h3> SPI: </h3>
                <p class="margin-top-10">总计：<span>{{syncResult.spiSyncResult.total}}</span></p>
                <p class="margin-top-10">成功：<span>{{syncResult.spiSyncResult.success ? syncResult.spiSyncResult.success.length : 0}}</span> <Button id="git_config_detail_btn4" v-if="syncResult.spiSyncResult.success && syncResult.spiSyncResult.success.length > 0" size="small" type="primary" @click="showDetail('spiSyncResult','success')">详情</Button></p>
                <p class="margin-top-10">失败：<span>{{syncResult.spiSyncResult.failed ? syncResult.spiSyncResult.failed.length : 0}}</span> <Button id="git_config_detail_btn5" v-if="syncResult.spiSyncResult.failed && syncResult.spiSyncResult.failed.length > 0" size="small" type="primary"  @click="showDetail('spiSyncResult','failed')">详情</Button></p>
                <p class="margin-top-10">忽略：<span>{{syncResult.spiSyncResult.ignored ? syncResult.spiSyncResult.ignored.length : 0}}</span> <Button id="git_config_detail_btn5" v-if="syncResult.spiSyncResult.ignored && syncResult.spiSyncResult.ignored.length > 0" size="small" type="primary"  @click="showDetail('spiSyncResult','ignored')">详情</Button></p>
                <p class="margin-top-10">未使用：<span>{{syncResult.spiSyncResult.unused ? syncResult.spiSyncResult.unused.length : 0}}</span> <Button id="git_config_detail_btn7" v-if="syncResult.spiSyncResult.unused && syncResult.spiSyncResult.unused.length > 0" size="small" type="primary"  @click="showDetail('spiSyncResult','unused')">详情</Button></p>
            </div>
            <div v-if="syncResult.modelSyncResult.total && syncResult.modelSyncResult.total != '0' && syncResult.modelSyncResult.total != 0"  class="margin-top-10 detail-tab"><h3>  MODEL: </h3>
                <p class="margin-top-10">总计：<span >{{syncResult.modelSyncResult.total}}</span></p>
                <p class="margin-top-10">成功：<span>{{syncResult.modelSyncResult.success ? syncResult.modelSyncResult.success.length : 0}}</span> <Button id="git_config_detail_btn8" v-if="syncResult.modelSyncResult.success && syncResult.modelSyncResult.success.length > 0" size="small" type="primary" @click="showDetail('modelSyncResult','success')">详情</Button></p>
                <p class="margin-top-10">失败：<span>{{syncResult.modelSyncResult.failed ? syncResult.modelSyncResult.failed.length : 0}}</span> <Button id="git_config_detail_btn9" v-if="syncResult.modelSyncResult.failed && syncResult.modelSyncResult.failed.length > 0" size="small" type="primary"  @click="showDetail('modelSyncResult','failed')">详情</Button></p>
                <p class="margin-top-10">忽略：<span>{{syncResult.modelSyncResult.ignored ? syncResult.modelSyncResult.ignored.length : 0}}</span> <Button id="git_config_detail_btn10" v-if="syncResult.modelSyncResult.ignored && syncResult.modelSyncResult.ignored.length > 0" size="small" type="primary"  @click="showDetail('modelSyncResult','ignored')">详情</Button></p>
                <p class="margin-top-10">未使用：<span>{{syncResult.modelSyncResult.unused ? syncResult.modelSyncResult.unused.length : 0}}</span> <Button id="git_config_detail_btn11" v-if="syncResult.modelSyncResult.unused && syncResult.modelSyncResult.unused.length > 0" size="small" type="primary"  @click="showDetail('modelSyncResult','unused')">详情</Button></p>
            </div>
            <div slot="footer">
                <Button  id="modal_apiGroup_btn_4" type="primary" @click="confirmResult">确认
                </Button>
            </div>
        </Modal>
        <!-- 详情展示弹框 -->
        <Modal v-model="detailFlag" :transfer='false'  width="500">
            <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
                <Icon type="ios-information-circle"></Icon>
                <span style="color:black" >详情</span>
            </p>
                <!-- 失败情况下列表展示 -->
                <div v-if="detailListTable" >
                    <Table id='table_ag_2' border :columns="columns_detailFailList" :data="detailList"></Table>
                </div>
                <!-- 非失败情况 直接展示 -->
                <div v-else>
                    <div v-for="(item, index) in detailList" :key="index">
                        <p class="margin-top-5">{{item}}</p>
                    </div>
                </div>
            <div slot="footer">
                <Button  id="modal_apiGroup_btn_4" type="primary" @click="detailFlag = false">确认
                </Button>
            </div>
        </Modal>
        <!-- 提示对话框 -->
        <Modal v-model="tipFlag" width="600">
            <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
                <Icon type="ios-information-circle"></Icon>
                <span style="color:black" >提示</span>
            </p>
            <div>
                <p>1、请将project的visibility设置为public</p>
                <p>2、在project->setting->Integrations新增webhook，以便yop能够及时获取最新变更，配置说明如下：</p>
                <p> &nbsp;&nbsp;&nbsp;&nbsp;URL：http://ycetest.yeepay.com:30275/rest/api-group/git-sync/git-event/push?apiGroup={apiGroupCode}</p>
                <p> &nbsp;&nbsp;&nbsp;&nbsp;Secret Token：xxxxxxx，具体的token请咨询yop运营</p>
                <p>&nbsp;&nbsp;&nbsp;&nbsp;Trigger：只需要PushEvent</p>
                <p>&nbsp;&nbsp;&nbsp;&nbsp;SSL verification：否</p>
            </div>
            <div slot="footer">
                <Button  id="modal_apiGroup_btn_5" type="primary" @click="tipFlag = false">确认
                </Button>
            </div>
        </Modal>
        </Card>
        <modal-security ref="modal_security"></modal-security>
    </div>
</template>

<script>
    import modalSecurity from './modal/securityModal';
    import apigroup from '../common-components/select-components/selectApiGroup'
    import serverSupplier from '../common-components/select-components/selectServiceSupplier'
    import datePicker from '../common-components/date-components/date-picker'
    import loading from '../my-components/loading/loading'
    import star_red from '../common-components/icon-components/star_red'
    import commonSelect from '../common-components/select-components/selectCommon'
    import textEditor from '../my-components/text-editor/text-editor-size';
    import CodeDiff from 'vue-code-diff'

    import util from '../../libs/util'
    import api from '../../api/api'
    export default {
        name: 'api_group',
        components:{
            apigroup,
            serverSupplier,
            modalSecurity,
            datePicker,
            loading,
            star_red,
            commonSelect,
            textEditor,
            CodeDiff
        },
        data(){
            const validate_apiGroupCode = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('api分组编码不能为空'));
                }
                // 模拟异步验证效果
                setTimeout(() => {
                    if (util.getLength(value.trim()) > 24) {
                        callback(new Error('长度不能大于24字符'));
                    } else if(util.formatCheck(value)){
                        callback(new Error('编码格式问题：首位必须为字母，且由小写字母、数字、中横线组成'));
                    }else {
                        if(this.create_orEdit){
                            api.yop_apiGroup_checkExist({
                                apiGroupCode : value
                            }).then(
                                (response) =>{
                                    if(response.data.status === 'success'){
                                        let result = response.data.data.result
                                        if(result){
                                            callback(new Error('该编码已存在'));
                                        }else{
                                            callback();
                                        }
                                    }else{
                                        callback(new Error('验重失败'))
                                    }
                                }
                            )
                        }else{
                            callback();
                        }
                    }
                }, 300);
            };
            const validate_security = (rule, value, callback) => {
                if(value){
                    let had = false
                    for(var i in value){
                        if(value[i].check){
                            had = true;
                            break;
                        }
                    }
                    if(had){
                        callback();
                    }else{
                        callback(new Error('请选择api分组安全需求'));
                    }
                }else{
                    callback();
                }
            };
            const validate_apiGroup =(rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('api分组名称不能为空'));
                }else if(util.getLength(value.trim()) > 16){
                    callback(new Error('api分组名称过长'));
                }else{
                    callback();
                }
            };
            // 解决方案验证
            const validate_description = (rule, value, callback) => {

                // if(util.getLength(this.$refs.apiGroupDes.getContent_text()) > 500){
                //     callback(new Error('解决方案(对内)不能超过500字'));
                // }else{
                //     callback();
                // }
                callback();
            };
    // 灰度规则比例
            const validate_apiGroup_weight = (rule, value, callback) => {
                var weightRange = /^(\d|[1-9]\d|100)(\.\d{1,2})?$/ //灰度规则比例范围
                // if (isNaN(value.trim())) {
                //     return callback(new Error('请输入数字'));
                // }else 
                var value = ""+value
                if(!(weightRange).test(value.trim()) || parseFloat(value) > 100){
                    return callback(new Error('比例在0%~100%之间,最多两位小数'));
                }else{
                    callback()
                }
            };
            return {
                // 新建还是修改标识 true 为新建
                create_orEdit :true,
                // 继承分组的安全需求
                request_common : [
                    // {
                    //     name : "YOP-RSA2048-SHA256",
                    //     custom : [{
                    //         name: 'read',
                    //         check :true
                    //     },{
                    //         name: 'write',
                    //         check :false
                    //     }
                    //     ],
                    //     desc: 'aaaaa'
                    // },
                    // {
                    //     name : "YOP-OAUTH2",
                    //     custom : [{
                    //         name: 'read1',
                    //         check :true
                    //     },{
                    //         name: 'write2',
                    //         check :false
                    //     }
                    //     ],
                    //     desc: 'bbbbb'
                    // },
                    // {
                    //     name : "YOP-RSA2048-SHA256",
                    //     desc: 'ccccc'
                    // }
                ],
                // 与表相关的下拉框数目
                count_select_related: 1,
                // api分组选择数据绑定
                data_select_apiGroup : '',
                // 服务提供方选择数据绑定
                data_select_serviceSupplier : '',
                // 分页总数
                pageTotal: 0,
                // 当前页数
                pageNo : 1,
                // 表格头数据绑定
                columns_ApiGroupList: [
                    {
                        title: 'API分组',
                        width: 140,
                        render:(h,params) =>{
                            return h('div',[
                                h('p',params.row.apiGroupName),
                                h('p','('+params.row.apiGroupCode+')')
                            ])
                        },
                        align: 'center'
                    },
                    {
                        renderHeader: (h,params) =>{
                            return h ('div',[
                                h('p', '服务提供方'),
                                h('p', '服务提供方编码')
                            ])
                        },
                        width : 130,
                        align: 'center',
                        render:(h,params) =>{
                            return h('div',[
                                h('p',params.row.serviceSupName),
                                h('p','('+params.row.serviceSupCode+')')
                            ])
                        },

                    },
                    {
                        title: '描述',
                        key: 'Desc',
                        'min-width': 120,
                        type: 'html'

                    },
                    {
                        title: 'Git同步状态',
                        width: 180,
                        key: 'gitSyncInfo',
                        // type: 'html',
                        render: (h,params) => {
                            return h('div',[
                                // 绿色对号
                                h('div',[
                                    h('Button', {
                                        domProps:{
                                            id: "git_status_btn1"
                                        },
                                        class: `padding-none ${params.row.gitSyncInfo ? (params.row.gitSyncInfo.syncOpen ? (params.row.gitSyncInfo.currentCommitId === params.row.gitSyncInfo.latestCommitId ? '' : 'hide') : 'hide') : 'hide'}`,
                                        props: {
                                            type: 'text',
                                            size: 'small',
                                        },
                                        domProps: { // 添加标签,使用自己引入的iconfont图标
                                            innerHTML: '<i class="iconfont green">&#xe617;</i>'
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api-group/git-sync/latest-commit'}
                                        }],
                                        on: {
                                            click: () => {
                                                // 调用检查更新
                                                // 生产模式 无操作
                                                this.currentApiGroup = params.row
                                                if(!this.qaProductFlag){
                                                    this.justCheckUpdate(params.row)
                                                }
                                            }
                                        }
                                    }, ''),
                                    // 同步按钮
                                    h('Button', {
                                        domProps:{
                                            id: "git_status_btn2"
                                        },
                                        class: `padding-none ${this.qaProductFlag ? 'hide' : ''} ${params.row.gitSyncInfo ? (params.row.gitSyncInfo.syncOpen ? (params.row.gitSyncInfo.currentCommitId === params.row.gitSyncInfo.latestCommitId ? '' : 'hide') : 'hide') : 'hide'}`,
                                        props: {
                                            type: 'primary',
                                            size: 'small',
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api-group/git-sync/commit-to-production'}
                                        }],
                                        on: {
                                            click: () => {
                                                this.$Modal.confirm({
                                                    title: '同步到生产',
                                                    content: `<p>确定要将api分组${params.row.apiGroupName}的最新commit：${params.row.gitSyncInfo.latestCommitId}同步到生产吗？</p>`,
                                                    onOk: () => {
                                                        this.syncProduct(params.row)
                                                    },
                                                    onCancel: () => {
                                                        
                                                    }
                                                });
                                            }
                                        }
                                    }, '同步到生产'),
                                ]),
                                h('div',[
                                    // 灰色圆圈
                                    h('Button', {
                                        domProps:{
                                            id: "git_status_btn3"
                                        },
                                        class: `padding-none  ${params.row.gitSyncInfo ? (params.row.gitSyncInfo.syncOpen ? 'hide': '') : ''}`,
                                        props: {
                                            type: 'text',
                                            size: 'small',
                                        },
                                        domProps: { // 添加标签,使用自己引入的iconfont图标
                                            innerHTML: '<i class="iconfont gray">&#xe615;</i>'
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api-group/git-sync/config'}
                                        }],
                                        on: {
                                            click: () => {
                                                this.gitConfig(params.row)
                                            }
                                        }
                                    }, ''),
                                ]),
                                h('div',[
                                    // 蓝色箭头
                                    h('Button', {
                                        domProps:{
                                            id: "git_status_btn4"
                                        },
                                        class: `padding-none  ${params.row.gitSyncInfo ? (params.row.gitSyncInfo.syncOpen ? (params.row.gitSyncInfo.currentCommitId === params.row.gitSyncInfo.latestCommitId ? 'hide' : '') : 'hide') : 'hide'}`,
                                        props: {
                                            type: 'text',
                                            size: 'small',
                                        },
                                        domProps: { // 添加标签,使用自己引入的iconfont图标
                                            innerHTML: '<i class="iconfont blue">&#xe608;</i>'
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api-group/git-sync/diff'}
                                        }],
                                        on: {
                                            click: () => {
                                                // 调起同步确认
                                                this.checkResult(params.row)
                                            }
                                        }
                                    }, ''),
                                    // 同步按钮
                                    h('Button', {
                                        domProps:{
                                            id: "git_status_btn5"
                                        },
                                        class: `padding-none  ${this.qaProductFlag ? 'hide' : ''}  ${params.row.gitSyncInfo ? (params.row.gitSyncInfo.syncOpen ? (params.row.gitSyncInfo.currentCommitId === params.row.gitSyncInfo.latestCommitId ? 'hide' : '') : 'hide') : 'hide'}`,
                                        props: {
                                            type: 'primary',
                                            size: 'small',
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api-group/git-sync/commit-to-production'}
                                        }],
                                        on: {
                                            click: () => {
                                                this.$Modal.confirm({
                                                    title: '同步到生产',
                                                    content: `<p>确定要将api分组${params.row.apiGroupName}的最新commit：${params.row.gitSyncInfo.latestCommitId}同步到生产吗？</p>`,
                                                    onOk: () => {
                                                        this.syncProduct(params.row)
                                                    },
                                                    onCancel: () => {
                                                        
                                                    }
                                                });
                                            }
                                        }
                                    }, '同步到生产'),
                                ]),
                            ])
                        },
                        align: 'center'
                    },
                    {
                        renderHeader: (h,params) =>{
                            return h ('div',[
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ])
                        },
                        key: 'Date',
                        width:160,
                        align: 'center',
                        render: (h,params) => {
                            return h('div',[h('p',params.row.createTime),
                                h('p',params.row.lastModifyTime)])
                        }
                    },
                    {
                        title: '操作',
                        align:'center',
                        key: 'operations',
                        width: 260,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    domProps:{
                                        id: "api_edit_btn1"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api-group/update'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.apiGroup_modify(params.row.apiGroupCode);
                                        }
                                    }
                                }, '编辑'),
                                h('Button', {
                                    domProps:{
                                        id: "api_edit_btn2"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api-group/delete'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.apiGroup_delete(params.row.apiGroupCode);
                                        }
                                    }
                                }, '删除'),
                                h('Button', {
                                    domProps:{
                                        id: "api_edit_btn3"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.apiGroup_ceph(params.row.apiGroupCode);
                                        }
                                    }
                                }, '文件存储配置')
                                ,
                                h('Button', {
                                    domProps:{
                                        id: "api_edit_btn4"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api-group/error-code/list'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.apiGroup_errorCode(params.row.apiGroupCode);
                                        }
                                    }
                                }, '错误码'),
                                ,
                                h('Button', {
                                    domProps:{
                                        id: "api_edit_btn5"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api-group/gray-policy/query'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.apiGroup_grayLevel(params.row.apiGroupCode);
                                        }
                                    }
                                }, '灰度规则')
                                ,
                                h('Button', {
                                    domProps:{
                                        id: "api_edit_btn6"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/model/list'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.model_manage(params.row.apiGroupCode);
                                        }
                                    }
                                }, '模型管理')
,
                                h('Button', {
                                    domProps:{
                                        id: "api_edit_btn7"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/spi/list'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.spi_manage(params.row.apiGroupCode);
                                        }
                                    }
                                }, 'SPI管理'),

                                h('Button', {
                                    domProps:{
                                        id: "api_edit_btn8"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api-group/git-sync/config'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.gitConfig(params.row)
                                        }
                                    }
                                }, 'Git同步配置'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api/security-req'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.$refs.modal_security.modal_show('', params.row.apiGroupCode);
                                        }
                                    }
                                }, '安全需求')

                            ]);
                        }


                    }
                ],
                // 表格数据绑定
                data_ApiGroupList:[
                    // {
                    //     apiGroupCode: 'apiGroupCode',
                    //     apiGroupName: 'API分组名称',
                    //     serviceSupName: '服务提供方',
                    //     serviceSupCode: 'spCode',
                    //     apiGroup_security: '128<br/>256<br/>512',
                    //     Desc:'sertrstyrtydrtdrtfcggchcbvgh',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-03 18:40:09'
                    // },
                    // {
                    //     apiGroupCode: 'apiGroupCode',
                    //     apiGroupName: 'apiGroupName',
                    //     serviceSupName: '服务提供方',
                    //     serviceSupCode: 'spCode',
                    //     apiGroup_security: '128<br/>256<br/>512',
                    //     Desc:'sertrstyrtydrtdrtfcggchcbvgh',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-03 18:40:09'
                    // }
                ],
                // 加载动画数据绑定
                show_loading_apiGroup: false,
                // 新增api分组窗口显示
                modal_create_apiGroup: false,
                formCustom: {
                    apiGroupCode: '',
                    apiGroup : '',
                    supplier: '',

                    version:''
                },
                ruleCustom: {
                    apiGroupCode: [
                        {required :true, message :'api分组编码不能为空'},
                        { validator: validate_apiGroupCode, trigger: 'blur' }
                    ],
                    apiGroup: [
                        {required :true, message :'api分组名称不能为空'},
                        { validator: validate_apiGroup, trigger: 'blur' }
                    ],
                    supplier:[
                        {required :true, message :'服务提供方不能为空', trigger: 'change'},
                    ],
                    description :[
                        { validator: validate_description}
                    ]
                },
    // 灰度规则弹窗
                // 确定是编辑
                edit_apiGroup:false,
                // 新增灰度弹框
                policyName_default :"比例灰度新增",
                // 正在编辑的apiGroup列
                apiGroup_create_index:0,
                // 灰度加载 
                show_loading_apiGroupList : false,
                // 灰度新增 
                show_loading_add_apiGroup:false,
                // 当前状态
                currentStatus:true,
                // 新增apiGroup校验规则
                rule_apiGroup : {
                    weight_default : [
                        {required: true, message: '比例不能为空'},
                        { validator: validate_apiGroup_weight, trigger: 'blur' }
                    ],
                    policyDesc_default : [
                        {required: true, message: '描述不能为空'},
                    ],
                },
                form_apiGroup : {
                    weight_default: "",
                    policyDesc_default : '',
                },
                modal_apiGroup_list:false,
                modal_add_apiGroup:false,
                // 同步配置问号提示
                tipFlag: false,
                // 创建或更新表示 默认更新-->false 
                createConfigFlag: true,
                // qa 生产模式标识 true->生产 
                qaProductFlag: false,
                // 当前api group
                currentApiGroup: {},
                // git同步配置弹框
                gitConfigFlag: false,
                // git 配置数据
                gitConfigData:{
                    id: '',
                    apiGroup: '',
                    syncOpen: true,
                    gitRepository: '',
                    gitBranch: '',
                    filePath: '',
                    latestCommitId: '',
                    currentCommitId: '',
                    lastModifiedDate: '',
                    version: '',
                },
                // 提交同步信息id
                requestId: '',
                // 对比内容 
                latestContent: "",
                currentContent: "",
                //同步确认弹框
                synchronizationFlag: false,
                // 详情弹框
                detailFlag: false,
                // 同步结果内容部分
                syncResult:{
                    apiSyncResult: {
                        total: 0,
                        success: [],
                        failed: [],
                        ignored: [],
                    },
                    spiSyncResult: {
                        total: 0,
                        success: [],
                        failed: [],
                        ignored: [],
                        unused: [],
                    },
                    modelSyncResult: {
                        total: 0,
                        success: [],
                        failed: [],
                        ignored: [],
                        unused: [],

                    }
                },
                // 同步结果详情 table展示 默认子串
                detailListTable: false,
                //  同步结果详情 失败列表
                columns_detailFailList:[
                    {
                        title: '失败项',
                        key: 'key',
                        'width': 110,

                    },
                    {
                        title: '操作类型',
                        key: 'syncOperation',
                        'width': 90,

                    },
                    {
                        title: '原因',
                        key: 'reason',
                        'min-width': 120,

                    },
                ],
                // 确认结果
                resultFlag: false,
                // 单个详情列表
                detailList:[],
                branchList:[],
                columns_data_apiGroupList:[
                    {
                        title: '条件',
                        width: 180,
                        render: (h, params) => {
                            return h('div', [
                                h('p', "所有"),
                            ]);
                        },
                        align: 'center'
                    },
                    {
                        title: '比例',
                        // key: 'weight' ,
                        width: 150,
                        render: (h,params) => {
                            return h('div',[
                                h('p',parseFloat(params.row.weight)/100+"%")
                            ])
                        },
                        align: 'center'
                    },
                    {
                        title: '描述',
                        width: 100,
                        render: (h,params) => {
                            return h('div',[
                                h('p',params.row.policyDesc)
                            ])
                        },
                        align: 'center'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'operations',
                        // width:  320,
                        render: (h, params) => {
                            let hasClick = false;
                            let option = "禁用";
                            let optionAction = false;
                            let url = "/rest/api-group/gray-policy/update";
                            if (params.row.enabled) {
                                option = "启用";
                                optionAction = false;
                                url = "/rest/api-group/gray-policy/update";
                            } else  {
                                option = "禁用";
                                optionAction = true;
                                url = "/rest/api-group/gray-policy/update";
                            }
                            return h('div', [
                                 h('Button', {
                                    domProps:{
                                        id: "api_gray_rule_edit_btn1"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api-group/gray-policy/update'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.apiGroup_create(params.row);
                                        }
                                    }
                                }, '编辑'),
                                h('Button', {
                                    domProps:{
                                        id: "api_gray_rule_edit_btn2"
                                    },
                                        props: {
                                        type: "text",
                                        size: "small"
                                        },
                                        directives: [
                                        {
                                            name: "url",
                                            value: { url: url }
                                        }
                                        ],
                                        on: {
                                            click: () => {
                                                if (params.row.enabled) {
                                                    this.cert_option(params.row)
                                                } else {
                                                    this.cert_option_1(params.row)
                                                }
                                            }
                                        }
                                }, params.row.enabled?"禁用":"启用"),
                                h('Button', {
                                    domProps:{
                                        id: "api_gray_rule_edit_btn3"
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api-group/gray-policy/update'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.delete_apiGroupList(params.row.id,true);
                                        }
                                    }
                                }, '删除'),

                               
                            ]);
                        }

                    }
                ],
                data_apiGroupList_apiGroup_copy:[],
                data_apiGroupList_apiGroup:[
                    // {
                    //     id : '1',
                    //     policyName : '所有',
                    //     policyDesc : 'xuqian-3',
                    //     criteria : "\"*\"",
                    //     weight : '50%',
                    //     enabled : true,
                    // },
                    // {
                    //     id : '2',
                    //     policyName : '所有',
                    //     policyDesc : 'baitao.ji',
                    //     criteria : "所有",//criteria
                    //     weight : '30%',
                    //     enabled : false,
                    // },
                    // {
                    //     id : '3',
                    //     policyName : '所有',
                    //     policyDesc : 'lele',
                    //     criteria : "所有",
                    //     weight : '100%',
                    //     enabled : true,
                    // },
                    // {
                    //     id : '4',
                    //     policyName : '所有',
                    //     policyDesc : 'dan.wang-2',
                    //     criteria : "所有",
                    //     weight : '1%',
                    //     enabled : true,
                    // }
                ],
                modal_apiGroup:"",
                // 开始日期绑定
                dateStart: '',
                //结束日期绑定
                dateEnd: ''

            }
        },
        methods:{
            // 页面初始化
            init(){
                // 获取Git配置 模式
                this.getGitSyncMode()
            },
            // 页面请求
            pageRefresh (){

            },
            // 页面查询按钮
            search_Interface (val){
                this.show_loading_apiGroup =true;
                let params={
                    apiGroupCode:this.data_select_apiGroup,
                    spCode: this.data_select_serviceSupplier,
                    createdStartDate: util.dateFormat_component(this.dateStart),
                    createdEndDate: util.dateFormat_component_end(this.dateEnd),
                    _pageNo:1,
                    _pageSize:10
                }
                if (val) {
                    params._pageNo = val;
                }
                util.paramFormat(params)
                api.yop_apiGroup_list(params).then(
                    (response) =>{
                        let status = response.data.status
                        if(response.data.status === 'success'){
                            this.tableDataFormat(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                            this.pageTotal = response.data.data.page.totalPageNum * 10;
                            this.show_loading_apiGroup = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_apiGroup = false;
                        }
                    }
                )
            },
            // 页面列表数据处理函数
            tableDataFormat (data) {
                this.data_ApiGroupList =[];
                if(data && data.length >0){
                    data.forEach(
                        (item)=>{
                            this.data_ApiGroupList.push({
                                id: item.id,
                                apiGroupCode: item.apiGroupCode,
                                apiGroupName: item.apiGroupName,
                                serviceSupName: this.data_spCode_handler(item.spCode),
                                serviceSupCode: item.spCode,
                                gitSyncInfo: item.gitSyncInfo,
                                Desc:item.description,
                                createTime : item.createdDate,
                                lastModifyTime : item.lastModifiedDate
                            })
                        }
                    )
                }
            },
            // spcode处理函数
            data_spCode_handler (spcode){
                let group = this.$store.state.select['serviceSupplier'].data
                for(var i in group){
                    if(group[i].value === spcode){
                        return  group[i].name
                    }
                }
            },
            // 安全需求处理函数
            data_security_handler(securities){
                let data_security = ''
                if(securities && securities.length > 0){
                    for(var i in securities){
                        if(i === securities.length){
                            data_security = data_security +securities[i]
                        }else{
                            data_security = data_security +securities[i]+'<br/>'
                        }
                    }
                    return data_security;
                }else{
                    return '';
                }
            },
            // 页面重置按钮
            reset_Interface () {
                this.$refs.select_ag.resetSelected();
                // this.data_select_apiGroup = '';

                this.$refs.datepicker.reset();

                this.$refs.select_sp.resetSelected();
                // this.data_select_serviceSupplier = '';
                this.dateStart = '';
                this.dateEnd = '';
            },
            // 同步更新选择api分组函数
            updateSelect_apiGroup (val) {
                this.data_select_apiGroup = val;
            },
            // 同步更新选择api分组函数
            updateSelect_serviceSupplier (val) {
                this.data_select_serviceSupplier = val;
                this.formCustom.supplier =val;
            },
            // 同步更新选择api分组函数
            updateSelect_serviceSupplier_inModal (val) {
                this.formCustom.supplier =val;
            },
            // api分组修改
            apiGroup_modify (code){
                this.reset_form_data();
                // this.show_loading_apiGroup = true;
                this.create_orEdit = false;
                this.apiGroup_detail_request(code)
            },
            cache_return () {
                return this.$store.state.security_cache;
            },
            //
            apiGroup_detail_request (code){
                this.modal_create_apiGroup = true;
                this.show_loading_apiGroup = true;
                api.yop_apiGroup_detail({apiGroupCode:code}).then(
                    (response) =>{
                        let status =response.data.status
                        if(status === 'success'){
                            let data = response.data.data.result;
                            this.formCustom.apiGroupCode =data.apiGroupCode;
                            this.formCustom.apiGroup = data.apiGroupName;
                            this.formCustom.supplier = data.spCode;
                            this.formCustom.version = data.version;
                            this.$refs.apiGroupDes.setContent(data.description);
                            this.detail_security_handler(data.securities);
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.modal_create_apiGroup = false
                        }
                        this.show_loading_apiGroup = false;
                    }
                )
            },
            // 安全需求返回数据处理函数
            detail_security_handler (security) {
                for(var i in this.formCustom.security){
                    for(var j in security){
                        if(this.formCustom.security[i].name === security[j].name){
                            this.formCustom.security[i].check =true;
                            if(security[j].scopes && security[j].scopes.length > 0){
                                this.special_security_handler(this.formCustom.security[i].scopes,security[j].scopes);
                            }
                            if(security[j].extensions){
                                this.special_security_extensions_handler(this.formCustom.security[i].extensions,security[j].extensions);
                            }
                            // else{
                            //     this.formCustom.security[i].needEncrypt.defaultValue = false;
                            //     this.formCustom.security[i].authority.defaultValue = [];
                            //     this.formCustom.security[i].forceEncryptAppDate.defaultValue = "";
                            // }
                        }
                    }
                }
            },
            // 安全需求特殊处理
            special_security_handler (all,custom){
                for(var i in all){
                    for(var j in custom){
                        if(all[i].name === custom[j]){
                            all[i].check = true
                        }
                    }
                }
            },
            // extensions特殊处理
            special_security_extensions_handler (extensions,result){
                extensions.needEncrypt.defaultValue = result.needEncrypt;
                extensions.authority.defaultValue = result.authority;
                extensions.forceEncryptAppDate.defaultValue = result.forceEncryptAppDate;
            },
            // 安全需求清空
            security_clear (){
                if(this.formCustom.security && this.formCustom.security.length >0){
                    for(var i in this.formCustom.security){
                        this.formCustom.security[i].check = false;
                        if(this.formCustom.security[i].scopes && this.formCustom.security[i].scopes.length > 0){
                            for(var j in this.formCustom.security[i].scopes ){
                                this.formCustom.security[i].scopes[j].check =false;
                            }
                        }
                        if(this.formCustom.security[i].extensions){
                            this.formCustom.security[i].extensions.authority.defaultValue = [];
                            this.formCustom.security[i].extensions.needEncrypt.defaultValue = false;
                            this.formCustom.security[i].extensions.forceEncryptAppDate.defaultValue = "";
                        }
                    }
                }
            },
            // 安全需求的获取处理
            basic_security_handler(security){
                let securityTemp =[]
                for(var i in security){
                    securityTemp.push({
                        name : security[i].name,
                        extensions: this.extensions_handler(security[i].extensions),
                        scopes : this.security_subitem_handler(security[i].scopes),
                        desc : security[i].desc,
                        check : false
                    })
                }
                return securityTemp;
                this.cache_handler(securityTemp);
            },
            cache_handler (val) {
                this.$store.state.security_cache = val;
            },
            // 扩展项处理
            extensions_handler (extentions) {
                let extentions_temp = {
                    authority: {
                    title: "",
                    optionValues: [],
                    defaultValue: []
                },
                needEncrypt: {
                    title: "",
                    defaultValue: false
                },
                forceEncryptAppDate: {
                    title: "",
                    defaultValue: "",
                    remark: ""
                }
                }
                if(extentions){
                    if(extentions.authority){
                        extentions_temp.authority.title = extentions.authority.title;
                        extentions_temp.authority.optionValues = extentions.authority.optionValues;
                        extentions_temp.authority.defaultValue = extentions.authority.defaultValue;
                    }
                    if(extentions.needEncrypt){
                        extentions_temp.needEncrypt.title = extentions.needEncrypt.title;
                        extentions_temp.needEncrypt.defaultValue = extentions.needEncrypt.defaultValue;
                    }
                    if(extentions.forceEncryptAppDate){
                        extentions_temp.forceEncryptAppDate.title = extentions.forceEncryptAppDate.title;
                        extentions_temp.forceEncryptAppDate.remark = extentions.forceEncryptAppDate.remark;
                    }
                }
                return extentions_temp;
            },
            // 安全需求子项处理函数
            security_subitem_handler(items){
                let data_custom = []
                if(items && items.length >0){
                    for(var i in items){
                        data_custom.push({
                            name: items[i],
                            check: false
                        });
                    }
                }
                return data_custom;
            },
            // 安全需求获取函数
            security_get(){
                if(this.$store.state.security_cache && this.$store.state.security_cache.length>0){
                    this.formCustom.security = [];
                    this.formCustom.security = this.cache_return();
                }else{
                    api.yop_apiGroup_security().then(
                        (response) =>{
                            this.formCustom.security =[]
                            this.formCustom.security = this.basic_security_handler(response.data.data.result);
                        }
                    )
                }
            },
            // 安全需求反向处理
            security_return () {
                let security_final = []
                if(this.formCustom.security){
                    this.formCustom.security.forEach(
                        (item) =>{
                            let scopesTemp =[]
                            if(item.check){
                                if(item.scopes){
                                    item.scopes.forEach(
                                        (subitem) =>{
                                            if(subitem.check){
                                                scopesTemp.push(subitem.name)
                                            }
                                        }
                                    )
                                }
                                if(scopesTemp.length > 0){
                                    security_final.push({
                                        name: item.name,
                                        scopes: scopesTemp
                                    })
                                }else if(item.extensions && item.name === "YOP-RSA2048-SHA256"){
                                    security_final.push({
                                        name: item.name,
                                        extensions: {
                                            needEncrypt : item.extensions.needEncrypt.defaultValue,
                                            authority : item.extensions.authority.defaultValue,
                                            forceEncryptAppDate : this.date_handler(item.extensions.forceEncryptAppDate.defaultValue)
                                        }
                                    })
                                }else{
                                    security_final.push({
                                        name: item.name
                                    })
                                }
                            }
                        }
                    )
                }
                return security_final;
            },
            date_handler(val) {
                if(val){
                    return util.dateFormat(val,'DAILY')
                }else{
                    return ''
                }
            },
            // api分组删除
            apiGroup_delete (code) {
                this.$Modal.confirm({
                    title: '删除API分组',
                    content: "确定删除该API分组吗？",
                    'ok-text':'删除',
                    onOk: () =>{
                        api.yop_apiGroup_delete({apiGroupCode:code}).then(
                            (response) =>{
                                if(response.status === 'success'){
                                    this.$ypMsg.notice_success(this,'API分组：'+code+'删除成功');
                                    this.updateList_apiGroup();
                                    this.current_check(code);
                                }else{
                                    this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                }
                            }
                        )
                    }
                });
            },
            // api分组文件存储配置
            apiGroup_ceph (group) {
                this.$store.state.current_apiGroup = group;
                this.$router.replace({
                    name: '/filestore/api-group/list'
                });
            },
            // api分组错误码
            apiGroup_errorCode (group) {
                this.$store.state.current_apiGroup = group;
                this.$router.replace({
                    name: '/api-group/error-code/list'
                });
            },
    // NEW
            // api灰度规则
            apiGroup_grayLevel (group){
                var param = {
                    apiGroup : group
                }
                api.yop_apiGroup_grayLevelList(param).then(
                        (response) =>{
                            //需要填写
                            if(response.status === 'success'){
                                var apiGroupList = response.data.result
                                this.data_apiGroupList_apiGroup = apiGroupList
                                // this.$ypMsg.notice_success(this,'修改成功');
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                this.modal_apiGroup_list = true
                this.modal_apiGroup = group
            },
            // api灰度规则窗口关闭
            cancel_apiGroup_grayLevel () {
                this.modal_apiGroup_list = false;
            },
            // 修改灰度规则列表
            ok_add_apiGroupList(){
                if(this.data_apiGroupList_apiGroup.length){
                    var params = {
                        "apiGroup":this.modal_apiGroup,
                        "policyList":this.data_apiGroupList_apiGroup
                    }
                }else{
                    var params = {
                        "apiGroup":this.modal_apiGroup,
                    }
                }
                
                api.yop_apiGroup_grayLevelList_update(params).then((response) =>{
                    //需要填写
                    if(response.status === 'success'){
                        this.$ypMsg.notice_success(this,'成功');
                        this.modal_apiGroup_list = false
                    }else{
                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                    }
                })
            },
            // 新增,编辑
            apiGroup_create (row){
                if(row.weight || row.weight === 0){
                    this.edit_apiGroup = true
                    this.policyName_default = "比例灰度修改"
                    this.apiGroup_create_index = row._index
                    this.form_apiGroup = row;
                    this.form_apiGroup.weight_default = parseFloat(row.weight)/100;
                    this.form_apiGroup.policyDesc_default = row.policyDesc;
                }else{
                    this.$refs.form_apiGroup.resetFields();
                    this.policyName_default = "比例灰度新增"
                    this.edit_apiGroup = false
                }
                this.modal_add_apiGroup = true
            },
            // 关闭新增窗口
            cancel_add_apiGroup(){
                this.modal_add_apiGroup = false
            },
            // 确定新增
            ok_add_apiGroup(val){
                var row = this.form_apiGroup
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        if(this.edit_apiGroup){//编辑
                            this.delete_apiGroupList(row.id,false);
                            this.data_apiGroupList_apiGroup.splice(this.edit_apiGroup?this.apiGroup_create_index:0,0,{//插入的位置
                                id : row.id,
                                // policyName : this.edit_apiGroup?"比例灰度修改":"比例灰度新增",
                                policyDesc : row.policyDesc_default,
                                // criteria : "所有",
                                weight : parseFloat(row.weight_default)*100,
                                enabled : row.enabled,
                            });
                        }else{
                            this.data_apiGroupList_apiGroup.splice(this.edit_apiGroup?this.apiGroup_create_index:0,0,{//插入的位置
                                // policyName : this.edit_apiGroup?"比例灰度修改":"比例灰度新增",
                                policyDesc : row.policyDesc_default,
                                // criteria : "所有",
                                weight : parseFloat(row.weight_default)*100,
                                enabled : false,
                            });
                        }
                        
                        this.modal_add_apiGroup = false
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 密钥启用、禁用
            // 启用
            cert_option(row, optionAction) {
                this.$Modal.confirm({
                    title: "禁用",
                    content: "<p style='color:red'>禁用规则，交易无法转发到灰度环境！</p><p>确定禁用该规则？</p>",
                    "ok-text": "确认",
                    onOk: () => {
                        this.data_apiGroupList_apiGroup[row._index].enabled = false
                        this.$set(row, 'enabled', false)
                    }
                });
            },
            // 禁用
            cert_option_1(row, optionAction) {
                this.$Modal.confirm({
                    title: "启用",
                    content: "<p>确定启用该规则？</p>",
                    "ok-text": "确认",
                    onOk: () => {
                        this.data_apiGroupList_apiGroup[row._index].enabled = true
                        this.$set(row, 'enabled', true)
                    }
                });
            },
            // 删除一项apiGroup
            delete_apiGroupList(id,create_orEdit){
                if(create_orEdit){
                    this.$Modal.confirm({
                        title: '提示',
                        content: '确定删除吗？',
                        'ok-text':'确认',
                        onOk: () =>{
                            for(var i = 0; i < this.data_apiGroupList_apiGroup.length; i++){
                                if(this.data_apiGroupList_apiGroup[i].id === id){
                                    this.data_apiGroupList_apiGroup.splice(i,1);
                                }
                            }
                        }
                    });
                }else{
                    for(var i = 0; i < this.data_apiGroupList_apiGroup.length; i++){
                        if(this.data_apiGroupList_apiGroup[i].id === id){
                            this.data_apiGroupList_apiGroup.splice(i,1);
                        }
                    }
                }
            },
            
            // 模型管理
            model_manage(apiGroupCode){
                this.$router.push({
                    path: '/model/list'
                });
                localStorage.apiGroup_model = apiGroupCode
            },
            // spi管理
            spi_manage(apiGroupCode){
                this.$router.replace({
                    name: 'spi/list'
                });
                localStorage.apiGroup_spi = apiGroupCode
            },
            // 获取git 同步模式
            getGitSyncMode(){
                api.yop_apiGroup_gitSync_mode({}).then(
                    (response) =>{
                        if(response.data.status === 'success'){
                            // 被动模式 即生产模式
                            if(response.data.data.result === "PASSIVE"){
                                this.qaProductFlag = true
                            }else{
                                this.qaProductFlag = false
                            }
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                    }
                )
            },
            // 将获取来数据处理
            resetApiGropuData(data){
                this.gitConfigData.apiGroup = data.apiGroup
                this.gitConfigData.filePath = data.filePath
                this.gitConfigData.createdDate = data.createdDate
                this.gitConfigData.gitBranch = data.gitBranch
                this.gitConfigData.gitRepository = data.gitRepository
                this.gitConfigData.id = data.id
                this.gitConfigData.lastModifiedDate = data.lastModifiedDate
                this.gitConfigData.currentCommitId = data.currentCommitId
                this.gitConfigData.latestCommitId = data.latestCommitId
                this.gitConfigData.syncOpen = data.syncOpen
                this.gitConfigData.version = data.version
            },
            // 展示提示
            showTip(){
                this.tipFlag = true;
            },
            // Git查看同步配置
            gitConfig(row){
                this.currentApiGroup = row
                let param={
                    apiGroup: row.apiGroupCode
                };
                api.yop_apiGroup_gitSync_config(param).then(
                    (response) =>{
                        let resData = response.data;
                        if(resData.status == "success"){
                            this.gitConfigFlag = true;
                            if(!!resData.data.result){
                                // 表示当前为更新 git配置
                                this.createConfigFlag = false
                                this.resetApiGropuData(response.data.data.result)
                                // 主动模式下请求
                                if(!this.qaProductFlag){
                                    // 请求branch 列表
                                    this.getGitBranchList()
                                }
                            }else{
                                // 表示当前为创建 git配置
                                this.createConfigFlag = true

                                this.gitConfigData.syncOpen = true
                                this.gitConfigData.gitRepository = ''
                                this.gitConfigData.gitBranch = ''
                                this.gitConfigData.filePath = ''
                                this.gitConfigData.latestCommitId = ''
                                this.gitConfigData.currentCommitId = ''
                                this.gitConfigData.createdDate = ''
                                this.gitConfigData.lastModifiedDate = ''
                                this.gitConfigData.id = ''
                                this.gitConfigData.version = ''
                                
                            }
                        }else{
                            this.gitConfigFlag = false;
                            this.$ypMsg.notice_error(this,'错误',resData.message,resData.solution);
                        }
                    }
                )
            },
            // 保存同步配置信息
            saveGitConfig(){
                let param = {
                    id: this.gitConfigData.id,
                    apiGroup: this.currentApiGroup.apiGroupCode,
                    syncOpen: this.gitConfigData.syncOpen,
                    gitRepository: this.gitConfigData.gitRepository,
                    gitBranch: this.gitConfigData.gitBranch,
                    filePath: this.gitConfigData.filePath,
                    currentCommitId: this.gitConfigData.currentCommitId,
                    latestCommitId: this.gitConfigData.latestCommitId,
                    version: this.gitConfigData.version
                };
                api.yop_apiGroup_gitSync_save(param).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.gitConfigFlag = false;
                            // 主动模式下 请求最新状态
                            /* if(!this.qaProductFlag){
                                this.justCheckUpdate(this.currentApiGroup)
                            } */
                            this.$ypMsg.notice_success(this,'保存成功');
                            // 更新列表状态
                            this.search_Interface()
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);

                        }
                    }
                )

            },
            // 同步配置检查更新
            checkUpdate(){
                // 生产模式 禁止更新
                if(this.qaProductFlag){
                    this.$Message.warning('生产暂不支持同步更新');
                    return false;
                }
                if(!this.gitConfigData.gitRepository || !this.gitConfigData.filePath || !this.gitConfigData.gitBranch){
                    this.$Message.warning('请填写完整信息');
                    return false
                }
                // 请求检查更新接口
                let param = {
                    apiGroup: this.currentApiGroup.apiGroupCode,
                    repository: this.gitConfigData.gitRepository,
                    branch: this.gitConfigData.gitBranch,
                    filePath: this.gitConfigData.filePath
                }
                api.yop_apiGroup_gitSync_latestCommit(param).then(
                    (response) =>{
                        let resData = response.data;
                        if(resData.status === 'success'){
                            // 如果最新的commitId跟当前的latestCommitId一样，提示“无最新变更”，否则提示，“已更新”
                            if(this.gitConfigData.latestCommitId === resData.data.result.latestCommitId){
                                this.$Message.info('无最新变更');
                            }else{
                                this.$Message.success('已更新');
                            }
                            this.gitConfigData.currentCommitId = resData.data.result.currentCommitId ? resData.data.result.currentCommitId : this.gitConfigData.currentCommitId
                            this.gitConfigData.latestCommitId = resData.data.result.latestCommitId ? resData.data.result.latestCommitId : this.gitConfigData.latestCommitId 
                            
                        }else{
                            this.$ypMsg.notice_error_nocopy(this,'错误',resData.message,resData.solution);
                        }
                    }
                )
                
            },
            // 获取过状态后 检查更新
            justCheckUpdate(row){
                this.currentApiGroup = row
                let param = {
                        apiGroup: this.currentApiGroup.apiGroupCode,
                    }
                    api.yop_apiGroup_gitSync_latestCommit(param).then(
                        (response) =>{
                            let resData = response.data;
                            if(resData.status === 'success'){
                                // 是否更新最新commitid
                                if(resData.data.result.currentCommitId != resData.data.result.latestCommitId){
                                    // 局部更新这条数据 使其显示篮箭头
                                    this.data_ApiGroupList.forEach(item => {
                                        if(item.apiGroupCode == this.currentApiGroup.apiGroupCode){
                                            item.gitSyncInfo.currentCommitId = resData.data.result.currentCommitId
                                            item.gitSyncInfo.latestCommitId = resData.data.result.latestCommitId
                                        }
                                    });
                                }
                                
                            }else{
                                this.$ypMsg.notice_error(this,'错误',resData.message,resData.solution);
                            }
                        }
                    )
            },
            // 取消Git 同步配置
            cancelGitConfig(){
                this.gitConfigFlag = false
            },
            // 查看结果
            checkResult(row){
                // 先获取下gitconfig 信息
                this.currentApiGroup = row
                let param={
                    apiGroup: row.apiGroupCode
                };
                api.yop_apiGroup_gitSync_config(param).then(
                    (response) =>{
                        let resData = response.data;
                        if(resData.status == "success"){
                            if(!!resData.data.result){
                                this.resetApiGropuData(response.data.data.result)
                                // 请求结果对比接口
                                let params = {
                                    apiGroup: this.currentApiGroup.apiGroupCode,
                                    currentCommitId: this.currentApiGroup.gitSyncInfo.currentCommitId ? this.currentApiGroup.gitSyncInfo.currentCommitId : '',
                                    latestCommitId: this.currentApiGroup.gitSyncInfo.latestCommitId ? this.currentApiGroup.gitSyncInfo.latestCommitId : '',
                                };
                                api.yop_apiGroup_gitSync_diff(params).then(
                                    (response) =>{
                                        let resData = response.data;
                                        if(resData.status === 'success'){
                                            this.synchronizationFlag = true
                                            this.currentContent = response.data.data.result.currentContent
                                            this.latestContent = response.data.data.result.latestContent
                                            this.requestId =  response.data.data.result.requestId
                                        }else{
                                            this.synchronizationFlag = false
                                            this.$ypMsg.notice_error(this,'错误',resData.message,resData.solution);
                                        }
                                    }
                                )
                            }
                            
                        }else{
                            this.$ypMsg.notice_error(this,'错误',resData.message,resData.solution);
                        }
                    }
                )
            },
            showDetail(type,result){
                this.detailFlag = true;
                if(result == 'failed'){
                    this.detailListTable = true
                }else{
                    this.detailListTable = false
                }
                this.detailList = this.syncResult[type][result]
            },
            // 发起同步结果确认
            confirmCheckoutResult(){
                let param = {
                    requestId: this.requestId,
                    apiGroup: this.gitConfigData.apiGroup
                }
                api.yop_apiGroup_gitSync_sync(param).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.resultFlag = true;
                            if(!!response.data.result){
                                let result = response.data.result;
                                this.syncResult.apiSyncResult = result.apiSyncResult ? result.apiSyncResult : this.syncResult.apiSyncResult
                                this.syncResult.spiSyncResult = result.spiSyncResult ? result.spiSyncResult : this.syncResult.spiSyncResult
                                this.syncResult.modelSyncResult = result.modelSyncResult ? result.modelSyncResult : this.syncResult.modelSyncResult
                            }
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                    }
                )
            },
            // 确认结果
            confirmResult(){
                // 将对比详情关闭
                this.synchronizationFlag = false;

                this.resultFlag = false
                // 更新页面状态
                this.search_Interface()
            },
            // git brnach列表
            getGitBranchList(){
                if(!this.gitConfigData.gitRepository){
                    return false
                }
                let param = {
                    repository: this.gitConfigData.gitRepository,
                }
                api.yop_apiGroup_gitSync_loadBranch(param).then(
                    (response) =>{
                        let resData = response.data
                        if(resData.status === 'success'){
                            if(!!response.data.data.result){
                                this.branchList = []
                                response.data.data.result.forEach(element => {
                                    this.branchList.push({
                                        label: element,
                                        value: element
                                    })
                                });
                            }
                        }else{
                            this.branchList = []
                            this.$ypMsg.notice_error_nocopy(this,'错误',resData.message,resData.solution);
                        }
                    }
                )
            },
            // 同步到生产按钮 弹框
            // 同步到生产
            syncProduct(row){
                this.currentApiGroup = row
                // 先请求到最新 commitid
                let param = {
                        apiGroup: this.currentApiGroup.apiGroupCode,
                    }
                    api.yop_apiGroup_gitSync_latestCommit(param).then(
                        (response) =>{
                            let resData = response.data;
                            if(resData.status === 'success'){
                                // 再去同步到 生产
                                let params = {
                                    apiGroup: this.currentApiGroup.apiGroupCode,
                                    commitId: this.currentApiGroup.gitSyncInfo.latestCommitId ? this.currentApiGroup.gitSyncInfo.latestCommitId : ''
                                }
                                api.yop_apiGroup_gitSync_commitProduction(params).then(
                                    (response) =>{
                                        if(response.status === 'success'){
                                            this.$ypMsg.notice_success(this,'同步成功');
                                            // 更新列表状态
                                            this.search_Interface()
                                        }else{
                                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        }
                                    }
                                )
                                
                            }else{
                                this.$ypMsg.notice_error(this,'错误',resData.message,resData.solution);
                            }
                        }
                    )
            },
            // API分组列表更新
            updateList_apiGroup () {
                this.$refs.select_ag.updateList();
            },
            // 新增api分组
            create_apiGroup () {
                this.reset_form_data();
                // this.security_get();
                this.create_orEdit = true;
                this.modal_create_apiGroup =true;
            },
            // 新增api分组确定按钮
            ok_create_apiGroup (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        if(this.create_orEdit){
                            let param = {
                                spCode : this.formCustom.supplier,
                                apiGroupCode : this.formCustom.apiGroupCode.trim(),
                                apiGroupName : this.formCustom.apiGroup.trim(),
                                securities : this.security_return(),
                                description : this.$refs.apiGroupDes.getContent()
                            }
                            api.yop_apiGroup_add(param).then(
                                (response) =>{
                                    if(response.status === 'success'){
                                        if(localStorage.needAudit === 'true'){
                                            this.$ypMsg.notice_info(this,response.message);
                                        }else{
                                            this.$ypMsg.notice_success(this,'添加成功');
                                        }
                                        this.updateList_apiGroup();
                                        this.search_Interface();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    }
                                }
                            )
                        }else{
                            let param = {
                                spCode : this.formCustom.supplier,
                                apiGroupCode : this.formCustom.apiGroupCode.trim(),
                                apiGroupName : this.formCustom.apiGroup.trim(),
                                securities : this.security_return() ,
                                description : this.$refs.apiGroupDes.getContent(),
                                version: this.formCustom.version
                            }
                            //可能需要对密钥处理剔除
                            api.yop_apiGroup_edit(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        if(localStorage.needAudit === 'true'){
                                            this.$ypMsg.notice_info(this,response.message);
                                        }else{
                                            this.$ypMsg.notice_success(this,'修改成功');
                                        }
                                        this.updateList_apiGroup();
                                        this.search_Interface();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    }
                                }
                            )
                        }
                        this.modal_create_apiGroup = false;
                    } else {
                        this.$Message.error('请检查');
                    }
                })
                // this.modal_create_apiGroup = false;
            },
            // 判断删除是否为当前api分组
            current_check (code) {
                if(this.data_select_apiGroup === code){
                    this.$refs.select_ag.resetSelected();
                    this.data_select_apiGroup = '';
                }
                this.search_Interface();
            },
            // 新增api分组取消按钮
            cancel_create_apiGroup () {
                this.reset_form_data();
                this.modal_create_apiGroup = false;
            },
            //表格数据初始化
            reset_form_data(){
                this.formCustom.apiGroupCode = '';
                this.formCustom.apiGroup = '';
                this.formCustom.version = '';
                this.$refs.select_sp_m.resetSelected();
                // this.formCustom.supplier = '';
                this.$refs.apiGroupDes.setContent(' ');
                this.security_clear();
                this.formCheck_reset();
            },
            // 开始日期更新
            update_start_date (val) {
                this.dateStart = val;
            },
            // 结束日期更新
            update_end_date (val) {
                this.dateEnd = val;
            },
            // 下拉框加载完处理函数
            select_callBack(){
                this.count_select_related--;
                if(this.count_select_related === 0){
                    this.search_Interface();
                }
            },
            // 表单状态还原
            formCheck_reset(){
                this.$refs.formCustom.resetFields();
            },
            // 新建服务提供方跳转
            create_service_supplier_jump () {
                this.$store.state.create_service_supplier_label = true;
                this.$router.replace({
                    name: "/isp/list"
                });
            }
        },
        mounted () {
            this.init();
        }
    };
</script>

<style scoped>
    .tipPart {
        color: #856404;
        background: #fff3cd;
        border-color: #ffeeba;
        padding: .75rem 1.25rem;
        border-radius: .25rem;
        margin-bottom:20px;
    }
</style>
