<style lang="less">
    @import '../../../styles/common.less';
    @import '../regression.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="8">
                <Col span="9" class="margin-top-5">
                <span >API分组（多选）：&nbsp;</span>
                </Col>
                <Col span="15">
                <Select v-model="model_API_Group" filterable multiple   placeholder="请选择API分组" @on-change="querylist" :disabled="select_disabled">
                    <Option v-for="item in APIGroup_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>

                <Col span="6" >
                <Col offset="1" span="7" class="margin-top-5">
                <span >API URI：&nbsp;</span>
                </Col>
                <Col span="16">
                <Input  v-model="data_api_uri" placeholder="API URI" @on-enter="querylist"></Input>
                </Col>
                </Col>

                <Col span="5">
                <Col offset="1" span="8"  style="align-self: center" class="margin-top-5">
                <span >测试环境：&nbsp;</span>
                </Col>
                <Col span="15">
                <Select v-model="model_test_env" >
                    <Option v-for="item in test_env_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>


                <Col span="5" style="text-align: center">
                <Button type="primary" style="margin-right: 10px" @click="one_Regression" v-url="{url:'/rest/regression-test/test/execute-batch'}" :loading="one_regression_loading">一键回归</Button>
                <!--</Col>-->
                <!--<Col offset="1" span="1">-->
                <Button type="ghost" @click="resetChoosen">重置</Button>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24" class="margin-bottom-10">    
                    <Button type="primary" class="margin-right-20"  @click="batch_export_cases()">批量导出回归用例</Button>
                    <Button type="primary" class="margin-right-20"  @click="batch_import_cases()">批量导入回归用例</Button> 
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Alert show-icon>已选择 <span style="color:#2d8cf0">{{items_choosen}}</span> 项 &nbsp;&nbsp;回归用例调用总计：<span style="font-weight: bold">{{items_total_Regression}}</span> 个</Alert>
                </Col>
            </Row>
            <Row>
                <Col span="24">
                <Table  border ref="selection" :columns="columns_ApiList" :data="data_ApiList" @on-selection-change="handleselection"></Table>
                <loading :show="loading_table"></loading>
                </Col>
            </Row>
        </Card>
        <!--<Modal v-model="modal_Show_open" width="360">-->
        <!--<p slot="header" style="color:#ff9900;">-->
        <!--<Icon type="alert-circled" ></Icon>&nbsp;&nbsp;-->
        <!--<span style="color:black">警告</span>-->
        <!--</p>-->
        <!--<div style="text-align:center">-->
        <!--<p style="color:red">回归过程可能会影响接口的幂等性，您还要开启吗</p>-->
        <!--</div>-->
        <!--<div slot="footer">-->
        <!--<Button type="ghost"  @click="cancel_open">取消</Button>-->
        <!--<Button type="primary" @click="ok_open">确定</Button>-->
        <!--</div>-->
        <!--</Modal>-->
        <!--<Modal v-model="modal_Show_close" width="360">-->
        <!--<p slot="header" style="color:#2d8cf0;">-->
        <!--<Icon type="information-circled" ></Icon>&nbsp;&nbsp;-->
        <!--<span style="color:black">提示</span>-->
        <!--</p>-->
        <!--<div style="text-align:center">-->
        <!--<p style="color:gray">请确认是否关闭回归验证?</p>-->
        <!--</div>-->
        <!--<div slot="footer">-->
        <!--<Button type="ghost"  @click="cancel_close">取消</Button>-->
        <!--<Button type="primary" @click="ok_close">确定</Button>-->
        <!--</div>-->
        <!--</Modal>-->
        <Modal v-model="modal_Show_regression" width="600">
            <p slot="header" style="color:#2d8cf0;">
                <span  class="margin-right-10">回归详情</span> <span style="color:gray;font-size: 12px;">{{this.modal_regression_time}}</span>
            </p>
            <div v-model="regression_Result">
                <div v-for="item in regression_Result">
                    <p style="color : gray">{{item.title}}</p>
                    <p v-show="!item.errorTag" class="yop-text-indent-20" v-for="subitem in item.assertionList" :style="subitem.color">{{subitem.title}}  : {{subitem.result}}</p>
                    <p v-show="item.errorTag" class="yop-text-indent-20" style="color:red">执行失败 : {{item.errorMsg}}</p>
                </div>
            </div>
            <div slot="footer">
                <Button type="primary" @click="close_regression">关闭</Button>
            </div>
            <loading :show="log_loading"></loading>
        </Modal>
        <batch_import_cases ref="batch_import_cases"></batch_import_cases>
        <batch_export_cases ref="batch_export_cases"></batch_export_cases>
        <modal_showMsg ref="modal_showMsg" :exportMsg="exportMsg" :exportIcon="exportIcon"></modal_showMsg>    
    </div>
</template>

<script>
    import api from'../../../api/api'
    import Vue from 'vue';
    import qs from 'qs';
    import loading from '../../my-components/loading/loading';
    import modal_showMsg from '../../common-components/modal-components/modal-showMsg';
    import batch_import_cases from './modals/batch_import_cases';
    import batch_export_cases from './modals/batch_export_cases';

    export default {
        name: 'list',
        components :{
            loading,
            batch_import_cases,
            batch_export_cases,
            modal_showMsg
        },
        data () {
            return {
                data_api_uri: '',
                // 回归详情查询弹窗显示
                modal_Show_regression : false,
                // 详情日志加载动画绑定
                log_loading:false,
                // 回归详情弹窗时间绑定参数
                modal_regression_time: '2017-12-20 14:30:30',
                // switch点击行数
                switch_index: 0,
                // switch打开对话框显示
                modal_Show_open: false,
                // switch打开对话框显示
                modal_Show_close: false,
                // API分组选项数据
                APIGroup_List: [],
                test_env_List: [
                    // {
                    //     value: 'QA',
                    //     label: 'QA环境'
                    // },
                    // {
                    //     value: 'test',
                    //     label: '内测环境'
                    // },
                    // {
                    //     value: 'pro',
                    //     label: '生产环境'
                    // }
                ],
                // api分组下拉框数据绑定
                model_API_Group: [],
                // 测试环境数据绑定
                model_test_env: '',
                // 已选择项
                items_choosen: 0,
                // 回归用例调用总计
                items_total_Regression: 0,
                // api回归测试列表列属性
                columns_ApiList: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    {
                        title: 'API名称',
                        key: 'apiTitle'
                    },
                    {
                        title: 'API URI',
                        key: 'apiUri'
                    },
                    {
                        title: 'API分组',
                        key: 'apiGroup',
                        type: 'html',
                        align:'center'
                    },
                    {
                        title: '回归用例',
                        key: 'caseCount',
                        width: 120,
                        sortable: true,
                        render: (h, params) => {
                            var color = 'red';
                            if (params.row.caseCount > 0) {
                                color = 'green';
                            }
                            return h('div', [
                                h('a', {
                                    style: {
                                        width: '16px',
                                        height: '16px',
                                        display: 'inline-block',
                                        'text-align': 'center',
                                        color: color,
                                        'text-decoration': 'none'
                                    }
                                }, '●'),
                                h('span', {
                                    style: {
                                        'line-heigth': '16px',
                                        display: 'inline-block',
                                        'text-align': 'center',
                                        'text-decoration': 'none',
                                        'margin-left': '10px'
                                    }
                                }, params.row.caseCount + '个')
                            ]);
                        }
                    },
                    {
                        title: '回归结果',
                        key: 'regressionResult',
                        width: 190,
                        sortable: true,
                        render: (h, params) => {
                            if (params.row.regressionResult.length > 0 && (params.row.regressionResult[0] !== '')&& ((params.row.regressionResult[0]+params.row.regressionResult[1]) !== 0)) {
                                return h('ButtonGroup',[h('i-button', {
                                    props: {
                                        type: 'success'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/regression-test/test/log'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.regressionResult_Detail(params.row.apiUri,params.row.batchExecutionId,params.row.lastRegressionTime);
                                        }
                                    }
                                }, '成功 : ' + params.row.regressionResult[0]), h('i-button', {
                                    props: {
                                        type: 'error'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/regression-test/test/log'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.regressionResult_Detail(params.row.apiUri,params.row.batchExecutionId,params.row.lastRegressionTime);
                                        }
                                    }
                                }, '失败 : ' + params.row.regressionResult[1]) ]);
                            }
                            return '';
                        }
                    },
                    {
                        title: '最后测试时间',
                        key: 'lastRegressionTime'
                    },
                    /**
                     *  暂时隐藏回归验证功能**/
                    /*
                    {
                        title: '回归验证',
                        key: 'regression_Validation',
                        sortable: true,
                        render: (h, params) => {
                            return h('i-switch', {
                                props: {
                                    value: params.row.regression_Validation,
                                    size: 'large'
                                },
                                on: {
                                    'on-change': (value) => {
                                        if (value === true) {
                                            this.modal_Show_open = true;
                                            this.switch_index = params.index;
                                        } else if (value === false) {
                                            this.modal_Show_close = true;
                                            this.switch_index = params.index;
                                        }
                                    }
                                }
                            }, [
                                h('span', {
                                    props: {
                                        'slot': 'open'
                                    }
                                }, '开'),
                                h('span', {
                                    props: {
                                        'slot': 'close'
                                    }
                                }, '关')
                            ]);
                        }
                    },
                     */
                    {
                        title: '操作',
                        key: 'operations',
                        width: 200,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        disabled: params.row.caseCount>0?false:true,
                                        type: 'primary',
                                        size: 'small',
                                        loading: params.row.loading
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/regression-test/test/execute-batch'}
                                    }],
                                    style: {
                                        marginRight: '10px'
                                    },
                                    on: {
                                        click: () => {
                                            this.regression(params.row.apiUri,params.index);
                                        }
                                    }
                                }, '执行用例'),
                                h('Button', {
                                    props: {
                                        type: 'primary',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.test_Template(params.row.apiId,params.row.apiUri,params.row.apiGroupCode);
                                        }
                                    }
                                }, '用例管理')
                            ]);
                        }
                    }
                ],
                // 表格数据
                data_ApiList: [
                    // {
                    //     apiId : 'api1',//唯一表示
                    //     apiTitle: 'TradeCode21',//api名称
                    //     apiUri: '这是一段描述',//api uri
                    //     apiGroup: '29.6w',//api分组
                    //     caseCount: 4,//回归用例结果
                    //     regressionResult: [3, 1],//回归结果 前面成狗后面失败
                    //     lastRegressionTime: '2016-09-21 08:50:08',//最后测试时间
                    //     regression_Validation: true,//这个是是否开启验证 需求被砍掉 现在可以不设计 不返回
                    //     operations: false //是否可以回归
                    // },
                    // {
                    //     apiId : 'api2',
                    //     apiTitle: 'TradeCode22',
                    //     apiUri: '这是一段描述',
                    //     apiGroup: '29.6w',
                    //     caseCount: 0,
                    //     regressionResult: ['', ''],
                    //     lastRegressionTime: '2016-09-21 08:50:09',
                    //     regression_Validation: false,
                    //     operations: true
                    // }
                ],
                // 表格loading
                loading_table: false,
                // 表格选中数据
                multiSelectedData: [],
                // websocket 实例
                websocket : null,
                // 回归测试结果（模态窗口内容数组）
                regression_Result : [
                    {
                        title: '测试用例1',
                        assertionList : [{
                            color : 'color : red',
                            title : '断言名称1',
                            result : '文本结果1'
                        },{
                            color : 'color : gray',
                            title : '断言名称2',
                            result : '文本结果2'
                        }]
                    },{
                        title: '测试用例1',
                        assertionList : [{
                            color : 'color : red',
                            title : '断言名称1',
                            result : '文本结果1'
                        },{
                            color : 'color : gray',
                            title : '断言名称2',
                            result : '文本结果2'
                        }]
                    }
                ],
                // 一健回归按钮loading数据绑定
                one_regression_loading: false,
                // 下拉分组禁用
                select_disabled :false,
                apiUrisArr :[],
                exportMsg:"",
                exportIcon:""
            };
        },
        methods: {
            batch_export_cases(){
                // this.$Modal.warning({
                //     title: "232323",
                //     content: "2323233"
                // });
                // this.$Modal.confirm({
                //     title: '提示',
                //     content: '测试用例未保存，编辑其他测试用例会丢失确认离开吗？',
                //     'ok-text':'确认',
                //     onOk: () =>{
                //     }
                // });
                // this.$Modal.error({
                //     title: '错误',
                //     content: "121212"
                // });
                // this.$Modal.success({
                //     title: '成功',
                //     content: 'api修改成功'
                // });
                var self = this
                if(!this.items_choosen){ //为零的时候
                    this.$Modal.warning({
                        title: "批量导出回归用例",
                        content: "至少要选择一个回归用例！"
                    });
                    // 至少要选择一个回归用例！
                    // this.$refs.batch_export_cases.atleastOne = true
                    // this.$refs.batch_export_cases.form_reset();
                    // this.$refs.batch_export_cases.modal_token_manage = true;
                }else{//调用导出ajax
                    // this.$refs.modal_showMsg.modal_show = true
                    this.apiUrisArr = []
                    this.multiSelectedData.map((value,index) => this.apiUrisArr.push(value.apiUri))
                    let params = {
                        apiUris:this.apiUrisArr
                    }
                    this.exportMsg = "复制到粘贴板失败，请重试！"
                    this.exportIcon = "ios-checkmark"
                    /*ios-checkmark  ios-close-circle*/
                    api.yop_regression_test_case_export(params).then(
                        (response) => {
                        var result = response.data.result;//返回的额json
                        var status = response.status
                        if(status == "success"){
                            this.$Modal.success({
                                title: '',
                                content: '已复制到粘贴板'
                            });
                            this.cert_copy(JSON.stringify(result))
                        }else {
                            this.$Modal.error({
                                title: '',
                                content: "复制到粘贴板失败，请重试！"
                            });
                        }
                        this.log_loading=false;
                    })
                }
            },
            // 复制到粘贴板
            cert_copy(text) {
                var aux = document.createElement("input");  
                var _content = text;
                aux.setAttribute("value", _content);
                document.body.appendChild(aux);
                aux.select();
                document.execCommand("Copy");
                document.body.removeChild(aux);
    
            },
            batch_import_cases(){
                this.$refs.batch_import_cases.create_or_edit = false;
                this.$refs.batch_import_cases.modal_token_manage = true;
                this.$refs.batch_import_cases.tokenId_set('');//设置当前tokenId
            },
            // 回归测试结果详情
            regressionResult_Detail (uri,bId,time) {
                this.modal_Show_regression = true;
                this.log_loading=true;
                this.modal_regression_time =time;
                // * 数据需要动态化
                let params = {
                    apiUri : uri,
                    batchExecutionId:bId//TODO   
                }
                this.regression_Result = [];
                api.yop_dashboard_checkLog(params).then(
                    (response) => {
                        var log = response.data.data.log;
                        var result=[];
                        for(var i in log){
                            if(log[i].errorMsg){
                                result.push({
                                    title : log[i].title,
                                    errorMsg: log[i].errorMsg,
                                    errorTag : true
                                })
                            }else{
                                var assert =[]
                                for(var j in log[i].assertionList){
                                    var color  = '';
                                    if(log[i].assertionList[j].status === 'success'){
                                        color = 'color : gray'
                                    }else{
                                        color = 'color : red'//需要判断替他情况
                                    }
                                    assert.push({
                                        color : color,
                                        title : log[i].assertionList[j].title,
                                        result : log[i].assertionList[j].expression
                                    })
                                }
                                result.push({
                                    title : log[i].title,
                                    assertionList : assert,
                                    errorTag : false
                                })
                            }

                        }
                        this.regression_Result = result;
                        this.log_loading=false;
                    })
            },
            // 回归结果查询弹窗关闭
            close_regression () {
                this.modal_Show_regression = false;
            },
            // 重置选择
            resetChoosen () {
                this.model_API_Group = [];
                this.data_api_uri = ""
            },
            // 分组选项内容改变搜索
            querylist () {
                var transfer = {
                    params: {apiGroups : this.model_API_Group,apiUri : this.data_api_uri.trim()},
                    paramsSerializer: function(params) {
                        return qs.stringify(params, {arrayFormat: 'repeat'})
                    }
                }
                this.queryApi_and_Refresh(transfer);
            },
            // 回归测试按钮调用方法
            regression (val,index) {
                this.select_disabled = true;
                this.data_ApiList[index].loading = true;
                this.regres_empty(val);
                var temp =[];
                temp.push(val);
                var params = {
                    environment : this.model_test_env,
                    apiUriList : temp//TODO
                }
                // this.threadPoxi (params);
                this.exec_template(params);
            },
            // 测试用例执行函数
            exec_template (params){
                api.yop_dashboard_executeBatch(params).then(
                    (response) => {
                        let batchId =response.data.batchExecutionId;
                        let params ={
                            batchExecutionId: batchId//TODO
                        }
                        this.deployTimeOutID=setInterval(()=> {
                            this.exec_query(params);
                        },2000)
                    }
                )
            },
            // 测试用例查询
            exec_query (params) {
                api.yop_dashboard_queryExecuteBatchResult(params).then(
                    (response) =>{
                        let status = response.data.data.executionResult.status;
                        if(status === 'finished'){
                            window.clearInterval(this.deployTimeOutID);
                            let executionList = response.data.data.executionResult.executionList;
                            let executionTime = response.data.data.executionResult.executionTime;
                            let batchExecutionId = response.data.data.executionResult.batchExecutionId;
                            this.data_update(executionList,executionTime,batchExecutionId);
                            this.select_disabled =false;
                            this.one_regression_loading = false;
                        }else{
                            let executionList = response.data.data.executionResult.executionList;
                            if (executionList.length > 0){
                                let executionTime = response.data.data.executionResult.executionTime;
                                let batchExecutionId = response.data.data.executionResult.batchExecutionId;
                                this.data_update(executionList,executionTime,batchExecutionId);
                            }
                        }
                    }
                )
            },
            // 遍历数据更新响应参数
            data_update (result,time,bId) {
                for( var i in result) {
                    let apiUri = result[i].apiUri;
                    let successResult = result[i].successCount;
                    let failureResult = result[i].failureCount;
                    this.data_update_apiId(apiUri,successResult,failureResult,time,bId);
                }
            },
            // 根据apiId修改结果
            data_update_apiId (apiUri,sr,fr,time,bId){
                for(var i in this.data_ApiList){
                    if(apiUri === this.data_ApiList[i].apiUri){
                        // this.data_ApiList[i].regressionResult[0] = sr;
                        // this.data_ApiList[i].regressionResult[1] = fr;
                        this.$set(this.data_ApiList[i].regressionResult,0,sr);
                        this.$set(this.data_ApiList[i].regressionResult,1,fr);
                        this.data_ApiList[i].lastRegressionTime = time;
                        this.data_ApiList[i].loading =false;
                        this.data_ApiList[i].batchExecutionId =bId;
                        break;
                    }
                }

            },
            // 测试用例按钮调用方法
            test_Template (val,apiUri,apiGroupCode) {
                localStorage.currentApiId = val;
                localStorage.currentApiUri = apiUri;
                localStorage.currentApiGroupCode =apiGroupCode;
                this.$router.push({
                    name: '/regression-test/case/list'
                });
            },
            // 查询api并刷行api列表表格数据
            queryApi_and_Refresh (params) {
                this.loading_table =true;
                if(params === ''){
                    api.yop_dashboard_apilist(params).then(
                        (response) => {
                            this.data_ApiList=[];
                            let listData = response.data.data.list;
                            let data_real = this.data_set(listData)
                            this.data_ApiList = data_real;
                            this.loading_table = false;
                        }
                    )
                }else{
                    api.yop_dashboard_apilist_query(params).then(
                        (response) => {
                            this.data_ApiList=[];
                            let listData = response.data.data.list;
                            let data_real = this.data_set(listData)
                            this.data_ApiList = data_real;
                            this.loading_table = false;
                        }
                    )
                }
            },
            // 数据处理
            data_set (data) {
                let dataTmp =[];
                for(var i in data){
                    dataTmp.push({
                        apiId : data[i].apiId,//唯一表示
                        apiTitle: data[i].apiTitle,//api名称
                        apiUri: data[i].apiUri,//api uri
                        apiGroup: data[i].apiGroupTitle+'<br/>'+'('+data[i].apiGroupCode+')',//api分组
                        apiGroupCode : data[i].apiGroupCode,
                        caseCount: data[i].caseCount,//回归用例结果
                        regressionResult : [0, 0],//回归结果 前面成功后面失败
                        lastRegressionTime: '',//最后测试时间
                        loading : false,
                        batchExecutionId : 0
                    })
                }
                return dataTmp;
            },
            // 界面初始化函数
            init () {
                // var that=this;
                // api.yop_dashboard_checkAccesstoken();
                if(localStorage.rergession_api){
                    this.data_api_uri = localStorage.rergession_api;
                    this.queryApi_and_Refresh({
                        params: {apiUri : localStorage.rergession_api.trim()},
                        paramsSerializer: function(params) {
                            return qs.stringify(params, {arrayFormat: 'repeat'})
                        }
                    });
                    localStorage.removeItem('rergession_api');
                }else if(localStorage.rergession_apiGroup){
                    this.model_API_Group = localStorage.rergession_apiGroup;
                    this.queryApi_and_Refresh({
                        params: {apiGroups : localStorage.rergession_apiGroup},
                        paramsSerializer: function(params) {
                            return qs.stringify(params, {arrayFormat: 'repeat'})
                        }
                    });
                    localStorage.removeItem('rergession_apiGroup');
                }else{
                    this.queryApi_and_Refresh('')
                }


                api.yop_dashboard_apis_list().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.APIGroup_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].apiGroupCode,
                                label: resultTemp[i].apiGroupCode+'('+resultTemp[i].apiGroupName+')'
                            })
                        }
                        this.APIGroup_List = dataTemp;
                    }
                )
                api.yop_dashboard_envList().then(
                    (response) => {
                        this.test_env_List=[];
                        for(var item in response.data.data.env ){
                            this.test_env_List.push({
                                value : response.data.data.env[item].name,
                                label : response.data.data.env[item].title
                            })
                        }
                        this.model_test_env = response.data.data.env[0].name;
                    }
                );
            },
            // 开关归位函数
            switch_Close (value) {
            },
            // switch开对话框确认函数
            ok_open () {
                this.modal_Show_open = false;
            },
            // switch开对话框取消函数
            cancel_open () {
                this.data_ApiList[this.switch_index].regression_Validation = false;
                // 手动刷新表格开关变换
                Vue.set(this.data_ApiList, this.switch_index, this.data_ApiList[this.switch_index]);
                this.modal_Show_open = false;
            },
            // switch关对话框确认函数
            ok_close () {
                this.modal_Show_close = false;
            },
            // switch关对话框取消函数
            cancel_close () {
                this.data_ApiList[this.switch_index].regression_Validation = true;
                // 手动刷新表格开关变换
                Vue.set(this.data_ApiList, this.switch_index, this.data_ApiList[this.switch_index]);
                this.modal_Show_close = false;
            },
            // 表格内容改动时
            handleselection(value){
                this.multiSelectedData= value;
                this.items_choosen=value.length;
                var total = 0;
                value.forEach(val=>{
                    total = total + val.caseCount;
                });
                this.items_total_Regression=total;
            },
            // 一键回归按钮函数
            one_Regression () {
                if (this.multiSelectedData === null || this.multiSelectedData.length === 0){
                    //还有其他函数判定
                    this.$Modal.warning({
                        title: '提示',
                        content: '请选择数据后再点击',
                    });
                }else {
                    var temp =[];
                    this.multiSelectedData.forEach( m => {
                        temp.push(m.apiUri);
                        this.regres_empty(m.apiUri);
                    });
                    var params = {
                        environment : this.model_test_env,
                        apiUriList : temp
                    }
                    this.one_regression_loading =true;
                    this.select_disabled = true;
                    this.loading_set(temp);
                    this.exec_template(params);
                    // this.threadPoxi (params);
                    // api.yop_dashboard_executeBatch (params) .then(
                    //     (response) => {
                    //         console.log(response);
                    //         alert(response.data.batchExecutionId);
                    //     }
                    // )
                }
            },
            // 列表致空
            regres_empty (apiUri) {
                for(var i in this.data_ApiList){
                    if( apiUri === this.data_ApiList[i].apiUri){
                        this.data_ApiList[i].regressionResult = [0,0];
                        this.data_ApiList[i].lastRegressionTime = '';
                        break;
                    }
                }

            },
            // 选择行置为执行loading
            loading_set (items) {
                items.forEach(item =>{
                    this.data_ApiList.forEach(val =>{
                        if(val.apiUri === item){
                            val.loading = true;
                        }
                    })
                })
            },
            /**
             * websocket长链接操作所有函数
             */
            threadPoxi (message) {  // 实际调用的方法
                //参数
                const agentData = message;
                //若是ws开启状态
                if (this.websock.readyState === this.websock.OPEN) {
                    this.websocketsend(agentData)
                }
                // 若是 正在开启状态，则等待300毫秒
                else if (this.websock.readyState === this.websock.CONNECTING) {
                    let that = this;//保存当前对象this
                    setTimeout(function () {
                        that.websocketsend(agentData)
                    }, 300);
                }
                // 若未开启 ，则等待500毫秒
                else {
                    this.initWebSocket();
                    let that = this;//保存当前对象this
                    setTimeout(function () {
                        that.websocketsend(agentData)
                    }, 500);
                }
            },
            initWebSocket(){ //初始化weosocket
                //ws地址
                const wsuri =  'ws://172.19.100.95:8001/yop-boss/rest/regression-test/execute-batch';
                this.websock = new WebSocket(wsuri);
                this.websock.onmessage = this.websocketonmessage;
                this.websock.onclose = this.websocketclose;
            },
            websocketonmessage(e){ //数据接收
                // const redata = JSON.parse(e.data);
                console.log(e)

            },
            websocketsend(agentData){//数据发送
                this.websock.send(agentData);
            },
            websocketclose(e){  //关闭
                console.log("connection closed (" + e.code + ")");
            }
        },
        mounted () {
            // 挂载时调用页面初始化函数
            this.init();

        },
        // beforeRouteLeave (to, from, next) {
        //     if(localStorage.rergession_api){
        //
        //     }else if(localStorage.rergession_apiGroup){
        //         localStorage.removeItem('rergession_apiGroup');
        //     }
        //
        //     next();
        // },
    };
</script>

<style scoped>

</style>
