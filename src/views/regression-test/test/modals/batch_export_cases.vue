<style lang="less">
    @import '../../../../styles/common.less';
</style>
<template>
        <Modal id="modal_token_1" v-model="modal_token_manage" width="650" :closable="false">
            <p slot="header" style="color:#2d8cf0;" v-show="atleastOne">
                <span style="color:black">批量导出回归用例</span>
            </p>
            <div v-if="atleastOne">
                <div><Icon type="ios-alert" color="#f8ac2f" size="24"></Icon>{{"至少要选择一个回归用例！"}}</div>
            </div>
            <div slot="footer">
                <Button id="modal_token_btn_1" type="ghost" @click="cancel_create_token">取消</Button>
            </div>
            <loading :show="show_loading_tokenname"></loading>
        </Modal>
</template>
    <script>
        import loading from '../../../my-components/loading/loading'
        import commonSelect from '../../../common-components/select-components/selectCommon_params_single'
        import util from '../../../../libs/util'
        import api from '../../../../api/api'
        export default {
            name: 'modal_tokenName_manage',
            components: {
                commonSelect,
                loading
            },
            data() {
                return {
                    // 创建和修改标识 true 为修改 false为创建
                    create_or_edit : true,
                    atleastOne :true,
                    // loading 数据绑定
                    show_loading_tokenname : false,
                    // 窗口显示绑定
                    modal_token_manage : false,
                    // 表单数据绑定
                    form_tokenname : {
                        name : '',
                        scope : '',
                        tokenname : '',
                        tokenId : ''
                    },
                    // 数据规则
                    rule_tokenname : {

                    }
                }
            },
            methods:{
                // 生成token确定
                // ok_create_token (val) {
                //     this.$refs[val].validate((valid) => {
                //     if (valid) {
                //         let param = {
                //             tokenId : this.form_tokenname.tokenId,
                //             grantType : 'PasswordCredentials',
                //             username: this.form_tokenname.username,
                //             tokenName: this.form_tokenname.tokenname,
                //             scope: this.form_tokenname.scope
                //         }
                //         util.paramFormat(param);
                //         api.yop_dashboard_token_update(param).then(
                //             (response) => {
                //                 //需要填写
                //                 if (response.status === 'success') {
                //                     if(create_or_edit){
                //                         this.$ypMsg.notice_success(this,'添加成功'); 
                //                     }else{
                //                         this.$ypMsg.notice_success(this,'修改成功'); 
                //                     }
                //                 } else {
                //                     this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
                //                 }
                //             }
                //         )
                //     } else {
                //         this.$Message.error('请检查');
                //     }
                // })
                // },
                // 取消token确定
                cancel_create_token (){
                    this.form_reset();
                    this.modal_token_manage = false;
                },
                // tokenid设置
                tokenId_set (id){
                    this.form_tokenname.tokenId = id;
                },
                // 修改设置
                edit_switch () {
                    this.create_or_edit = false;
                    this.form_data_get();
                },
                // 表单数据获取
                form_data_get () {
                    let param = {
                            tokenId : this.form_tokenname.tokenId
                        }
                        api.yop_dashboard_token_detail(param).then(
                            (response) => {
                                //需要填写
                                if (response.data.status === 'success') {
                                    this.tableDataFormat(response.data.data.result);
                                } else {
                                    this.$ypMsg.notice_error(this, '错误', response.data.message, response.data.solution);
                                }
                            }
                        )
                },
                // 表格数据处理函数
                tableDataFormat (result) {
                    this.form_tokenname.tokenname = result.tokenName;
                    this.form_tokenname.username = result.username;
                    this.form_tokenname.scope = result.scope;
                },
                // 表单重制
                form_reset () {
                    this.create_or_edit = true;
                    this.form_tokenname.name = '';
                    this.form_tokenname.scope = '';
                    this.form_tokenname.tokenname = '';
                    // this.$refs.form_tokenname.resetFields();
                },
                // 用户名改变事件
                userName_change () {
                    if(this.form_tokenname.name && this.form_tokenname.name!== ''){
                        this.form_tokenname.tokenname = localStorage.currentApiGroupCode +'-PC-'+this.form_tokenname.name
                    }else{
                        this.form_tokenname.tokenname = '';
                    }
                }
            }
        };
    </script>