<template>
        <Modal id="modal_token_1" v-model="modal_token_manage" width="650" :closable="false">
            <p slot="header" style="color:#2d8cf0;">
                <span style="color:black">批量导入回归用例</span>
            </p>
            <div>
                <div style="color:red;">注意：请将导出复制到粘贴板的回归用例，粘贴到如下文本框中，完成用例导入！</div>
                <p style="display:inline-block;vertical-align:top;">导入JSON：</p>
                <Form style="display:inline-block;" ref="form_tokenname" :model="form_tokenname" :rules="rule_tokenname" :label-width="140">
                    <Input v-model="exportCases" name="exportCases" type="textarea" :autosize="{minRows: 10,maxRows:10}" placeholder="请输入" style="width:500px"></Input>
                </Form>
            </div>
            <div slot="footer">
                <Button id="modal_token_btn_2" type="primary" @click="ok_create_token('form_tokenname')">确定</Button>
                <Button id="modal_token_btn_1" type="ghost" @click="cancel_create_token">取消</Button>
            </div>
            <loading :show="show_loading_tokenname"></loading>
        </Modal>
</template>
    <script>
        import loading from '../../../my-components/loading/loading'
        import commonSelect from '../../../common-components/select-components/selectCommon_params_single'
        import util from '../../../../libs/util'
        import api from '../../../../api/api'
        export default {
            name: 'modal_tokenName_manage',
            components: {
                commonSelect,
                loading
            },
            data() {
                return {
                    // 创建和修改标识 true 为修改 false为创建
                    create_or_edit : true,
                    // loading 数据绑定
                    show_loading_tokenname : false,
                    // 窗口显示绑定
                    modal_token_manage : false,
                    // 表单数据绑定
                    form_tokenname : {
                        name : '',
                        scope : '',
                        tokenname : '',
                        tokenId : ''
                    },
                    // 数据规则
                    rule_tokenname : {

                    },
                    exportCases:""
                }
            },
            methods:{
                // 检测是否是json格式
                isJsonString(str) {
                    try {
                        JSON.parse(str)
                        return true
                    } catch (err) {
                        return false
                    }
                },
                // 生成token确定
                ok_create_token (val) {
                    if(this.isJsonString(this.exportCases)){
                        let params = {
                            caseList:JSON.parse(this.exportCases)
                        }
                        api.yop_regression_test_case_import(params).then(
                            (response) => {
                            var status = response.status;//返回的额json
                            if(status == "success"){
                                this.$Modal.success({
                                    title: '成功',
                                    content: '导入成功'
                                });
                                this.modal_token_manage = false
                            }else{
                                this.$Modal.error({
                                    title: '错误',
                                    content: response.message
                                });
                            }
                            this.log_loading=false;
                        })
                    }else{
                        this.$ypMsg.notice_warning(this,'请导入json格式的数据！');
                    }
                },
                // 取消token确定
                cancel_create_token (){
                    this.form_reset();
                    this.modal_token_manage = false;
                    this.exportCases = ""
                },
                // tokenid设置
                tokenId_set (id){
                    this.form_tokenname.tokenId = id;
                },
                // 表格数据处理函数
                tableDataFormat (result) {
                    this.form_tokenname.tokenname = result.tokenName;
                    this.form_tokenname.username = result.username;
                    this.form_tokenname.scope = result.scope;
                },
                // 表单重制
                form_reset () {
                    this.create_or_edit = true;
                    this.form_tokenname.name = '';
                    this.form_tokenname.scope = '';
                    this.form_tokenname.tokenname = '';
                    this.$refs.form_tokenname.resetFields();
                },
                // 用户名改变事件
                userName_change () {
                    if(this.form_tokenname.name && this.form_tokenname.name!== ''){
                        this.form_tokenname.tokenname = localStorage.currentApiGroupCode +'-PC-'+this.form_tokenname.name
                    }else{
                        this.form_tokenname.tokenname = '';
                    }
                }
            },
            created(){
            }
        };
    </script>