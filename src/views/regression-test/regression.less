// api列表标签下移
.yop-api-list-label{
  margin-top: 6px;
  width: 130px;
}
// api在线测试标题左右间隔10
.yop-api-collapse-title-10{
  margin: 0px 10px;
}
// 测试用例名称可编辑框属性
.yop-api-collapse-input{
  height: 80%;
  width:250px;
  border-radius: 5px;
  z-index: 99999;
  margin: 0px 10px;
}
// 滚动块边框现已不用改为嵌入card框中
.yop-scroll{
  border:1px solid #eee;
}
// 测试用例内部模块标题样式
.yop-template-in-title {
  border-bottom: 1px solid #eee;
  padding: 0px 0px 10px 0px;
  font-weight: bold;
}
// 首行缩进
.yop-text-indent-20 {
  text-indent: 20px;
}
// 下部外边距10
.yop-margin-bottom-10 {
  margin-bottom: 10px;
}
