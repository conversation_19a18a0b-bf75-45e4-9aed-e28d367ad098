<template>
        <Modal id="modal_token_1" v-model="modal_token_manage" width="650" :closable="false">
            <p slot="header" style="color:#2d8cf0;">
                <span style="color:black" v-show="create_or_edit">新增TokenName</span>
                <span style="color:black" v-show="!create_or_edit">修改TokenName</span>
            </p>
            <div>
                <Form ref="form_tokenname" :model="form_tokenname" :rules="rule_tokenname" :label-width="140">
                    <FormItem label="授权类型">
                        <p>PasswordCredentials（密码模式）</p>
                    </FormItem>
                    <FormItem label="用户名：" prop="name" :rules="{required:true,message:'用户名不能为空'}">
                        <Input type="text" placeholder="UserId" size="small" v-model="form_tokenname.name"  @on-change="userName_change" style="width:80%"></Input>
                    </FormItem>
                    <FormItem label="授权范围：" prop="scope">
                        <Input type="text" placeholder="scope" size="small" v-model="form_tokenname.scope" style="width:80%"></Input>
                    </FormItem>
                    <FormItem label="TokenName：" prop="token">
                            <Tag type="border">{{form_tokenname.tokenname}}</Tag>
                    </FormItem>
                </Form>
            </div>
            <div slot="footer">
                <Button id="modal_token_btn_2" type="primary" @click="ok_create_token('form_tokenname')">确定</Button>
                <Button id="modal_token_btn_1" type="ghost" @click="cancel_create_token">取消</Button>
            </div>
            <loading :show="show_loading_tokenname"></loading>
        </Modal>
</template>
    <script>
        import loading from '../../../my-components/loading/loading'
        import commonSelect from '../../../common-components/select-components/selectCommon_params_single'
        import util from '../../../../libs/util'
        import api from '../../../../api/api'
        export default {
            name: 'modal_tokenName_manage',
            components: {
                commonSelect,
                loading
            },
            data() {
                return {
                    // 创建和修改标识 true 为修改 false为创建
                    create_or_edit : true,
                    passApiGroup:"" || localStorage.currentApiGroupCode,
                    passAppkey:"",
                    // loading 数据绑定
                    show_loading_tokenname : false,
                    // 窗口显示绑定
                    modal_token_manage : false,
                    // 表单数据绑定
                    form_tokenname : {
                        name : '',
                        scope : '',
                        tokenname : '',
                        tokenId : ''
                    },
                    // 数据规则
                    rule_tokenname : {

                    }
                }
            },
            methods:{
                // 生成token确定
                ok_create_token (val) {
                    this.$refs[val].validate((valid) => {
                    if (valid) {
                        let param = {
                            tokenId : this.create_or_edit ? "" :this.form_tokenname.tokenId,
                            grantType : 'PasswordCredentials',
                            username: this.form_tokenname.name,
                            tokenName: this.form_tokenname.tokenname,
                            scope: this.form_tokenname.scope,
                            appKey:this.passAppkey,
                            apiGroup:this.passApiGroup
                        }
                        util.paramFormat(param);
                        api.yop_dashboard_token_update(param).then(
                            (response) => {
                                //需要填写
                                if (response.status === 'success') {
                                    if(this.create_or_edit){
                                        this.$ypMsg.notice_success(this,'添加成功'); 
                                    }else{
                                        this.$ypMsg.notice_success(this,'修改成功'); 
                                    }
                                    console.log(response)
                                    this.modal_token_manage = false
                                    this.$emit('getTokenList',this.form_tokenname.tokenname);//调用父级方法
                                } else {
                                    this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
                                }
                            }
                        )
                    } else {
                        this.$Message.error('请检查');
                    }
                })
                },
                // 取消token确定
                cancel_create_token (){
                    this.form_reset();
                    this.modal_token_manage = false;
                },
                // tokenid设置
                tokenId_set (id){
                    this.form_tokenname.tokenId = id;
                },
                // 修改设置
                edit_switch () {
                    this.create_or_edit = false;
                    this.form_data_get();
                },
                // 表单数据获取
                form_data_get () {
                    let param = {
                            tokenId : this.form_tokenname.tokenId
                        }

                        api.yop_dashboard_token_detail(param).then(
                            (response) => {
                                //需要填写
                                if (response.data.status === 'success') {
                                    this.tableDataFormat(response.data.data.result);
                                } else {
                                    this.$ypMsg.notice_error(this, '错误', response.data.message, response.data.solution);
                                }   
                            }
                        )
                },
                // 表格数据处理函数
                tableDataFormat (result) {
                    this.form_tokenname.tokenname = result.tokenName;
                    this.form_tokenname.name = result.username;
                    this.form_tokenname.scope = result.scope;
                    
                },
                // 表单重制
                form_reset () {
                    this.create_or_edit = true;
                    this.form_tokenname.name = '';
                    this.form_tokenname.scope = '';
                    this.form_tokenname.tokenname = '';
                    this.$refs.form_tokenname.resetFields();
                },
                // 用户名改变事件
                userName_change () {
                    if(this.form_tokenname.name && this.form_tokenname.name!== ''){
                        this.form_tokenname.tokenname = localStorage.currentApiGroupCode +'-PC-'+this.form_tokenname.name
                    }else{
                        this.form_tokenname.tokenname = '';
                    }
                }
            }
        };
    </script>