<style lang="less">
    @import '../../../styles/common.less';
    @import '../regression.less';
</style>
<template>
    <div style="background: #eee;padding: 8px" id="caseList">
        <!--<Card :bordered="false">-->
        <Row>
            <Col span="4">
            <Select v-model="item_menuSelected" filterable @on-change="selectChange">
                <Option v-for="item in currentAllGroups" :value="item.code" :key="item.code">{{ item.label }}</Option>
            </Select>
            <div v-if="menuShow">
                <Menu ref="side_menu" :active-name="active_menu" accordion :open-names="menu_open" width="auto" @on-select="click_Submenu" @on-open-change="menu_opened">
                    <Submenu  v-for="item in MenuList_filter(menu_datalist)" :key="item.code" :id="'anchor-'+item.code" :name="item.code">
                        <template slot="title">
                            <Icon type="ios-people"></Icon>
                            {{item.name}}
                        </template>
                        <MenuItem v-for="subitem in item.child" :key="subitem.apiUri" :name="subitem.apiUri" >{{subitem.name}}<Tag style="margin-left: 5px" v-show="subitem.regres" color="blue">回</Tag></MenuItem>
                    </Submenu>
                </Menu>
            </div>
            <loading :show="menu_loading"></loading>
            </Col>
            <Col id='regrsPanel' span="20" class="padding-left-10">
            <Button type="primary" class="margin-bottom-10" @click="addTestTemplate" v-show="show_add_template">+ 添加测试用例</Button>
            <Button type="primary" class="margin-bottom-10" @click="deleteTestTemplate"  v-show="show_add_template">批量删除</Button>
            <div style="border-bottom: 1px solid #e9e9e9;padding-bottom:6px;margin-bottom:6px;">
                <Checkbox
                    :indeterminate="indeterminate"
                    :value="checkAll"
                    @click.prevent.native="handleCheckAll">全选</Checkbox>
            </div>
            <Col v-if="collapse_datalist.length > 0">
            <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
                <Collapse v-model="collapse_label" v-for="(item,index) in collapse_datalist" :key="index" accordion @on-change="collapse_change('',item.id)" >
                    <Checkbox :label="item.id"></Checkbox>
                    <Panel :name="index.toString()">
                        <span> <input class="yop-api-collapse-input" v-show="template_name_editable(index.toString(),collapse_label,collapse_input_editable)" v-model="item.name" type="text" @click="stopProp">
                            <span class="yop-api-collapse-title-10" v-show="!template_name_editable(index.toString(),collapse_label,collapse_input_editable)">{{item.name}}</span>
                            <Button type="text" v-show="index.toString()==collapse_label" @click="editable_Control"> <Icon :type="edit_or_check" ></Icon></Button>
                            </span>
                        <span class="yop-api-collapse-title-10" style="float:right" v-show="index.toString()!=collapse_label">断言：{{item.assertionCount}}&nbsp;&nbsp;&nbsp;&nbsp;用例修改时间：{{item.lastModifiedDateTime}}
                            &nbsp;&nbsp;&nbsp;&nbsp;<Button type="text" @click="copyTemplate(item.name,item.id,$event)">复制</Button> <Button type="text" @click="deleteTemplate(item.id,$event)">删除</Button></span>
                        <p slot="content" v-model="data_Template">
                            <Row>
                                <Col span="24" class="margin-bottom-10">
                                <Card dis-hover>
                                    <Row>
                                        <Col class="yop-template-in-title">
                                        {{data_Template.main.title}}
                                        </Col>
                                    </Row>
                                    <Row class="margin-top-10" type="flex" align="middle" justify="center">
                                            <Col span="18">
                                                <Col span="12" class="padding-bottom-10">
                                                    <Col span="8" class="padding-top-5">
                                                    <span class="margin-right-10"><star_red></star_red> 测试应用</span>
                                                    </Col>
                                                    <Col span="12" >
                                                    <Select ref="appName" filterable clearable v-model="default_name"  placeholder="请选择测试应用" @on-change="namechange" >
                                                        <Option v-for="items in data_Template.main.name" :value="items.name" :key="items.name">{{ items.title }}</Option>
                                                    </Select>
                                                    </Col>
                                                    <Col span="4" class="padding-top-5">
                                                    <span style="margin-left:5px;"><Button type="success" size="small" icon="plus" @click="new_app_regression"></Button></span>
                                                    </Col>
                                                </Col>
                                                <Col span="12" class="padding-bottom-10">
                                                    <Col span="8" class="padding-top-5">
                                                    <span class="margin-right-10"><star_red></star_red> API加签方式</span>
                                                    </Col>
                                                    <Col span="12" >
                                                    <Select v-model="default_security" placeholder="请选择API加签方式" @on-change="showTokenPart($event)">
                                                        <Option v-for="item1 in data_Template.main.securities" :value="item1.name" :key="item1.name">{{ item1.title }}</Option>
                                                    </Select>
                                                    </Col>
                                                </Col>
                                                <Col span="24" class="padding-bottom-10" v-show="op_showTokenPart">
                                                    <Col span="4" class="padding-top-5">
                                                    <span class="margin-right-10" >AccessToken </span>
                                                    </Col>
                                                    <Col span="18" >
                                                        <Input v-model="default_accesstoken" placeholder="请输入accessToken"></Input>
                                                    </Col>
                                                    <Col span="2" class="yop-info">
                                                        <Tooltip content="该AccessToken仅用于用例执行时传值，不会做保存"><Icon type="information-circled"></Icon>
                                                            <div slot="content">
                                                                    <p>该AccessToken仅用于用例执行时传值，</p>
                                                                    <p>不会做保存</p>
                                                            </div>
                                                        </script>
                                                        </Tooltip>
                                                    </Col>
                                                </Col>
                                                <Col span="24" class="padding-bottom-10" v-show="op_showTokenPart">
                                                    <Col span="4" class="padding-top-5">
                                                        <span class="margin-right-10">TokenName</span>
                                                    </Col>
                                                    <Col span="6" >
                                                        <Select ref="select_tokenname"  @on-change="tokenname_change($event)" :label="default_tokenname" v-model="default_tokenname" filterable clearable placeholder="请选择tokenName" >
                                                            <Option v-for="item1 in data_Template.main.tokenname" :value="item1.tokenName" :key="item1.tokenName" @click.native="setTokenId(item1.tokenId)">{{ item1.tokenName }}</Option>
                                                        </Select>
                                                        </Col>
                                                        <Col offset="1" span="10" class="padding-top-5">
                                                            <span style="margin-left:5px;">
                                                                    <Tooltip content="该操作针对于tokenName">
                                                                    <ButtonGroup>
                                                                <Button type="success"  icon="plus" size="small" @click="tokenname_create">新增</Button>
                                                                <Button type="warning" v-show="tokenname_choosen" icon="edit" size="small" @click="tokenname_manage">管理</Button>
                                                                <Button type="error"  v-show="tokenname_choosen" icon="minus"  size="small" @click="tokenname_delete">删除</Button>
                                                                    </ButtonGroup>
                                                                </Tooltip>
                                                            </span>
                                                            <!-- <span style="margin-left:5px;"></span>
                                                            <span style="margin-left:5px;"><Button type="error" size="small" icon="minus" @click="new_app_regression">删除</Button></span> -->
                                                        </Col>
                                                </Col>
                                                <Col span="12">
                                                    <Col span="8" class="padding-top-5">
                                                        <span class="margin-right-10"><star_red></star_red> 用例类型</span>
                                                    </Col>
                                                    <Col span="12" >
                                                    <Select v-model="default_type" placeholder="请选择用例类型">
                                                        <Option v-for="item in template_type_list" :value="item.name" :key="item.name">{{item.name}}</Option>
                                                    </Select>
                                                    </Col>
                                                </Col>
                                                <Col span="12">
                                                    <Col span="8" class="padding-top-5">
                                                        <span class="margin-right-10 padding-top-5"><star_red></star_red> 执行环境</span>
                                                    </Col>
                                                    <Col span="12" >
                                                        <Select v-model="model_test_env" placeholder="请选择测试环境">
                                                            <Option v-for="item in test_env_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                                                        </Select>
                                                    </Col>
                                                </Col>
                                            </Col>
                                        <!-- <Col span="9">
                                        <br/><br/>
                                        </Col>
                                        <Col span="9"><br/><br/>
                                        </Col> -->
                                        <!--<Col span="4"></Col>-->
                                        <!--<Col span="4"></Col>-->

                                        <Col span="6">
                                        <Tooltip content="带*的参数为必填项">
                                            <Button type="ghost" class="margin-right-5" v-url="{url:'/rest/regression-test/case/save'}" @click="save_Template">保存用例</Button>
                                        </Tooltip>
                                        <Button type="primary" class="margin-left-10" v-url="{url:'/rest/regression-test/case/execute-single-by-raw'}" @click="run_Template" :loading="run_loading" :disabled="run_disable">{{run_btn_text}}</Button></Col>
                                    </Row>
                                </Card>
                                </Col>
                                <Col span="14" >
                                <Card style="height:300px;" dis-hover>
                                    <Col class="yop-template-in-title">
                                    请求参数
                                    </Col>
                                    <Tabs v-model="tab_now" style="height:280px;">
                                        <TabPane label="FORM格式" name="FORM"> <Scroll height="190">
                                            <Col span="24" v-for="(itemp, tIndex) in data_Template.params.params_normal" :key="tIndex">
                                            <Col span="9">
                                            <Tooltip  :content="itemp.name+'('+itemp.title+')'" placement="right">
                                                <span v-show="itemp.required"><star_red></star_red> </span><span>{{itemp.name}}:</span>
                                            </Tooltip>
                                            </Col>
                                            <Col span="14">
                                            <Input  :placeholder="itemp.title" v-model="itemp.value" class="padding-left-20 margin-bottom-10"></Input>
                                            </Col>
                                            </Col>
                                        </Scroll></TabPane>
                                        <TabPane label="JSON格式" name="JSON"><Input class="padding-left-5" v-model="data_Template.params.params_JSON" type="textarea" :rows="8" placeholder="请输入json参数"></Input></TabPane>
                                    </Tabs>
                                </Card>
                                </Col>
                                <Col span="10" class="padding-left-10">
                                <Card style="height:300px;" dis-hover>
                                    <Col class="yop-template-in-title">
                                    响应结果
                                    </Col>
                                    <Scroll v-show="JSON.stringify(data_Template.result_Response) != '{}'" height="250">
                                        <pre>{{data_Template.result_Response}}</pre>
                                    </Scroll>
                                    <loading :show="run_loading"></loading>
                                </Card>
                                </Col>
                            </Row>
                            <Row class="margin-top-10">
                                <Col span="14" >
                                <Card style="height:300px;" dis-hover>
                                    <Col class="yop-template-in-title">
                                    断言
                                    </Col>
                                    <Scroll>
                                        <Table height="183" border :columns="editInlineColumns" :data="editInlineData"></Table>
                                        <Button type="dashed" long @click="table_newAssertion">+ 新增断言</Button>
                                    </Scroll>

                                </Card>
                                </Col>
                                <Col span="10" class="padding-left-10">
                                <Card style="height:300px;" dis-hover>
                                    <Col class="yop-template-in-title">
                                    断言结果
                                    </Col>
                                    <Scroll v-show="data_Template.result_Assertion.result != ''" height="250">
                                        <Col span="24"><Tag :color="data_Template.result_Assertion.result === 'success'?'green':'red'">{{data_Template.result_Assertion.result === 'success'?'成功':'失败'}}</Tag></Col>
                                        <Col span="24" v-for="(itemc, cIndex) in data_Template.result_Assertion.content" :key="cIndex" :style="itemc.result">
                                        <pre>{{itemc.title}} : {{itemc.content}} </pre> </Col>
                                    </Scroll>
                                    <loading :show="run_loading"></loading>
                                </Card>
                                </Col>
                            </Row>
                            <loading :show="detail_loading"></loading>
                        </p>
                    </Panel>
                </Collapse>
             </CheckboxGroup>
            <loading :show="collapse_loading"></loading>
            </Col>
            </Col>
            <Modal v-model="modal_Show_newapp" width="360">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black">新建测试应用</span>
                </p>
                <div >
                    <div style="text-align:center">
                        <p style="color:gray">测试商编：
                            <Input v-model="test_businessCode" style="width:200px;"></Input>
                        </p>
                    </div>
                    <div><p style="color:gray;margin-left:100px;">Appkey和AppSecret自动生成</p></div>
                </div>

                <div slot="footer">
                    <Button type="ghost"  @click="cancel_newapp">取消</Button>
                    <Button type="primary" v-url="{url:'/rest/regression-test/case/app/create'}" @click="ok_newapp">确定</Button>
                </div>
            </Modal>
            <Modal v-model="modal_Show_assertion" width="450">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" v-show="assertion_modAdd">编辑断言</span>
                    <span style="color:black" v-show="!assertion_modAdd">新建断言</span>
                </p>
                <div>
                    <p class="yop-margin-bottom-10">断言名称:</p>
                    <p class="yop-margin-bottom-10">
                        <Input type="text" v-model="assertion_Name"></Input>
                    </p>
                    <p class="yop-margin-bottom-10">表达式：
                        <span style="float:right">
                            <Select v-model="Normal_expressions" style="width:230px;" size="small" clearable placeholder="常用表达式" @on-change="expression_choose">
                                <Option v-for="item in array_expressions" :value="item.name" :key="item.name">{{ item.title }}</Option>
                            </Select>
                        </span>
                    </p>
                    <p class="yop-margin-bottom-10">
                        <Input type="textarea" v-model="assertion_Expression"></Input>
                    </p>
                </div>
                <div slot="footer">
                    <Button type="ghost"  @click="cancel_assertion">取消</Button>
                    <Button type="primary" @click="ok_assertion">确定</Button>
                </div>
            </Modal>
            </Col>
        </Row>
        <modal_token ref="modal_token" @getTokenList="getTokenList"></modal_token>
        <!--</Card>-->
    </div>
</template>

<script>
    import star_red from '../../common-components/icon-components/star_red'
    import api from'../../../api/api';
    import loading from '../../my-components/loading/loading';
    import util from '../../../libs/util.js';
    import modal_token from './modals/modal_tokenName_manage';
    export default {
        name: 'list_single',
        components :{
            loading,
            star_red,
            modal_token
        },
        watch:{
            tab_now : {
                handler(nval,oval) {
                    if(nval){
                        this.run_btn_text = '执行'+nval+'用例'
                    }
                },
                deep : true
            }
        },
        data () {
            return {
                // 多选（删除）
                indeterminate: true,
                checkAll: false,
                checkAllGroup:[],
                checkAllGroupArr:[],
                // 是否选择token
                tokenname_choosen : false,
                // 当前执行按钮文案
                run_btn_text : '执行FORM用例',
                // 当前tab页
                tab_now : 'FORM',
                // 添加测试用例展示
                show_add_template : false,
                // 当前折叠面板id
                current_collapse_id: [],
                // 测试用例执行按钮禁用
                run_disable : false,
                // 详细信息loading
                detail_loading :false,
                // 折叠面板loading
                collapse_loading : false,
                // 菜单loading
                menu_loading : false,
                // 执行用例loading
                run_loading : false,
                // menu 显示隐藏全部闭合用
                menuShow: true,
                // 当前api用例数目统计
                currentTemplateCount:0,
                // 所有分组数据
                currentAllGroups:[],
                // 表格数据
                editInlineColumns: [
                    {
                        title: '断言名称',
                        key: 'name',
                        width: 100,
                        editable: true
                    },
                    {
                        title: '表达式(groovy语法)',
                        key: 'assertion',
                        'min-width': 200,
                        editable: true
                    },
                    {
                        title: '操作',
                        align: 'center',
                        width: 150,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'primary',
                                        size: 'small'
                                    },
                                    style: {
                                        marginRight: '10px'
                                    },
                                    on: {
                                        click: () => {
                                            //*（需要删除）
                                            console.log(params.row+params.index);
                                            // this.regression(params.row);
                                            this.table_editLine(params.row,params.index)
                                        }
                                    }
                                }, '编辑'),
                                h('Button', {
                                    props: {
                                        type: 'error',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            //*（需要删除）
                                            // this.test_Template(params.row);
                                            console.log(params.row+params.index);
                                            this.table_deleteLine(params.row,params.index)
                                        }
                                    }
                                }, '删除')
                            ]);
                        }
                    }],
                editInlineData: [
                    //     {
                    //     name : '断言1',
                    //     assertion : 'xxxxxxxxx=xaxsadsadasdsaassdsad'
                    // },{
                    //     name : '断言2',
                    //     assertion : 'xxxxxxxxx=xaxsadsadasdsaassdsad'
                    // },{
                    //     name : '断言3',
                    //     assertion : 'xxxxxxxxx=xaxsadsadasdsaassdsad'
                    // },{
                    //     name : '断言4',
                    //     assertion : 'xxxxxxxxx=xaxsadsadasdsaassdsad'
                    // },{
                    //     name : '断言5',
                    //     assertion : 'xxxxxxxxx=xaxsadsadasdsaassdsad'
                    // },{
                    //     name : '断言6',
                    //     assertion : 'xxxxxxxxx=xaxsadsadasdsaassdsad'
                    // }
                ],
                // 断言修改/添加显示
                modal_Show_assertion : false,
                // 断言修改和添加标签(true 为修改，false为添加)
                assertion_modAdd: true,
                // 断言名称数据绑定
                assertion_Name : '',
                // 断言表达式数据绑定
                assertion_Expression : '',
                // 当前编辑行
                current_EditLine : '',
                // 默认API加签方式
                default_security : '',
                // API方式是否致灰，如果唯一则致灰
                // security_only : true,
                // 默认用例类型
                default_type : '',
                // 默认应用名称
                default_name : '',
                // 默认accesstoken
                default_accesstoken : '',
                // 默认tokenname
                default_tokenname : '',
                // 新建窗口显示标签
                modal_Show_newapp: false,
                // 测试应用选择名称
                select_testApp: '',
                // 标题编辑图标
                edit_or_check: 'android-create',
                // 是否显示编辑框
                collapse_input_editable: false,
                // 折叠面板默认展开项数据绑定
                collapse_label: ['0'],
                // 折叠面板数据
                collapse_datalist: [
                    // {
                    //     index: '1',
                    //     name: '测试用例1',
                    //     lastModifiedDateTime: '2017-07-14 20:20:30',
                    //     assertionCount : '10'
                    // },
                    // {
                    //     index: '3',
                    //     name: '测试用例2',
                    //     lastModifiedDateTime: '2017-07-15 20:20:30',
                    //     assertionCount : '9'
                    //
                    // },
                    // {
                    //     index: '3',
                    //     name: '测试用例3',
                    //     lastModifiedDateTime: '2017-07-16 20:20:30',
                    //     assertionCount : '8'
                    // }
                ],
                // 导航列表的数据
                menu_datalist: [
                    // {
                    //     name: '全部',
                    //     label: '全部'
                    // }, {
                    //     name: '对账',
                    //     label: '对账',
                    //     child: [{
                    //         name: '快照查询（账号版)',
                    //         regres: false
                    //     }, {
                    //         name: '快照查询（商编版）',
                    //         regres: true,
                    //         apiId: ''
                    //     }]
                    // }, {
                    //     name: '航旅钱麦项目',
                    //     label: '航旅钱麦项目',
                    //     child: [{
                    //         name: '用户系统-商户余额查询',
                    //         regres: true
                    //     }, {
                    //         name: '获取银卡key',
                    //         regres: false
                    //     }]
                    // }, {
                    //     name: '联盟app',
                    //     label: '联盟app',
                    //     child: [{
                    //         name: '查询展业新版app',
                    //         regres: false
                    //     }, {
                    //         name: '查询展业系统消息',
                    //         regres: true
                    //     }]
                    // }
                ],
                // 导航选项
                item_menuSelected: '全部',
                // 导航打开项
                menu_open: [],
                // 导航当前激活项
                active_menu: '',
                // 导航当前uri，
                currentUri : '',
                // 导航当前的apiId
                current_apiId : '',
                currentApiUri : '',
                // 导航当前是否可以回归
                current_regres : false,
                // 当前id
                current_id : '',
                // 导航当前api分组
                current_apiGroup: '',
                passApiGroup:'',
                // 测试用例数据
                data_Template: {
                    main: {
                        title: '',//第一部分名称
                        securities : [
                            // {
                            //     name : 'YOP-HMAC-AES128',//下拉列表的value
                            //     title : 'AES128'//下拉列表的title
                            // },
                            // {
                            //     name : 'YOP-RSA2048-SHA256',
                            //     title : 'RSA2048 SHA256'
                            // }
                        ],
                        tokenname:[

                        ],
                        accesstoken: '',
                        name: [
                            // {
                            //     name : 'create',//下拉列表的value
                            //     customerNo : 'create',
                            //     title : '+新增测试应用'//下拉列表的title
                            // }
                            // ,
                            // {
                            //     name : '10004555233-test122',//下拉列表的value
                            //     customerNo : 'asdasda',
                            //     title : '10004555233-test11'//下拉列表的title
                            // },
                            // {
                            //     name : '10004555233-test122321',
                            //     customerNo : 'asdasdasd',
                            //     title : '10004555233-test122321'
                            // }
                        ],//测试应用名称
                        interface_label: 'RSA2048 SHA256',//API加签方式 剩下的2项没写可以自定义
                        regressionType : false
                    },
                    params: {
                        params_normal: [
                            //     {
                            //     name: 'tradeAuthorizationDirectories',
                            //     title: '参数1',
                            //     required:true,
                            //     value :'dassdsadas'
                            // },{
                            //     name: 'param2',
                            //     title: '参数2',
                            //     required:false,
                            //     value :'sssss'
                            // }, {
                            //     name: 'param3',
                            //     title: '参数3',
                            //     required:true,
                            //     value :''
                            // }
                        ],//请求参数的参数
                        params_JSON: ''
                    },
                    result_Response: {
                        // code : '130000',
                        // name : '河北省'
                    },//响应结果内容
                    assertion: [{
                        name: '午觉好',
                        expression: 'asdsad',
                    }],//断言格式可以自定义按照api列表的格式来
                    result_Assertion: {
                        result: '',//断言结果标识
                        content: [{
                            result : 'color : grey',
                            title : 'x',
                            content:'xxxxxxxx'},
                            {
                                result : 'color : red',
                                title :'y',
                                content:'xxxxxsss'}
                        ]//断言结果内容
                    }
                },
                // 新建测试应用测试商编
                test_businessCode : '',
                // 测试环境数据列表
                test_env_List: [],
                // 用例类型数据列表
                template_type_list : [
                    {
                        name : '一般用例',
                        title: '一般用例'
                    },
                    {
                        name: '回归用例',
                        title: '回归用例'
                    }],
                // 测试环境数据绑定
                model_test_env: '',
                // 常用表达式数据绑定
                Normal_expressions : '',
                // 常用表达式数组
                array_expressions :[
                    {
                        name : "assert bizResponse['x']=='y'",
                        title: "assert bizResponse['x']=='y'"
                    },
                    {
                        name : "assert bizResponse['x']!='y'",
                        title: "assert bizResponse['x']!='y'"
                    },
                    {
                        name : "assert bizResponse['x']>'y'",
                        title: "assert bizResponse['x']>'y'"
                    },
                    {
                        name : "assert bizResponse['x']<'y'",
                        title: "assert bizResponse['x']<'y'"
                    },
                    {
                        name : "assert bizResponse['x1']['x2']=='y'",
                        title: "assert bizResponse['x1']['x2']=='y'"
                    },
                    {
                        name : "assert bizResponse['x1']['x2']!='y'",
                        title: "assert bizResponse['x1']['x2']!='y'"
                    }
                ],
                op_showTokenPart:false,
                deleteTokenId:"",
                passAppkey:""

            };
        },
        mounted () {
            this.init();
            api.yop_dashboard_envList().then(
                (response) => {
                    this.test_env_List=[];
                    for(var item in response.data.data.env ){
                        this.test_env_List.push({
                            value : response.data.data.env[item].name,
                            label : response.data.data.env[item].title
                        })
                    }
                    this.model_test_env = response.data.data.env[0].name;
                }
            );
            // this.editInlineColumns = tableData.editInlineColumns;
            // this.editInlineData = tableData.editInlineData;
        },
        methods: {
            // 全选删除
            handleCheckAll () {
                if (this.indeterminate) {
                    this.checkAll = false;
                } else {
                    this.checkAll = !this.checkAll;
                }
                this.indeterminate = false;
                if (this.checkAll) {
                    this.checkAllGroup = this.checkAllGroupArr;
                    this.checkAll = true;
                } else {
                    this.checkAllGroup = [];
                }
            },
            checkAllGroupChange (data) {
                this.checkAllGroup = data
                if (data.length === this.collapse_datalist.length) {
                    this.indeterminate = false;
                    this.checkAll = true;
                } else if (data.length > 0) {
                    this.indeterminate = true;
                    this.checkAll = false;
                } else {
                    this.indeterminate = false;
                    this.checkAll = false;
                }
            },
            deleteTestTemplate(){
                var param = {
                    caseIds : this.checkAllGroup
                }
                if(!this.checkAllGroup.length){
                    this.$Modal.warning({
                        title: "",
                        content: "请选择要删除的用例"
                    });
                }else{
                    this.$Modal.confirm({
                        title: '提示',
                        content: '确认删除测试用例吗？',
                        'ok-text':'确认',
                        onOk: () =>{
                            api.yop_dashboard_deleteTemplate(param).then(
                                (response) =>{
                                    if(response.status !== 'success'){
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    }else{
                                        this.$ypMsg.notice_success(this,'删除成功');
                                        this.click_Submenu(this.currentApiUri);
                                    }
                                }
                            )
                        }
                    });
                }
                
            },
            // YOP-OAUTH2显示tokenPart
            showTokenPart(e){
                if(e == "YOP-OAUTH2" || this.default_security == "YOP-OAUTH2"){
                    this.op_showTokenPart = true
                    this.getTokenList();
                    this.$refs.modal_token.passAppkey = this.default_name
                }else{
                    this.op_showTokenPart = false
                }
            },
            // tokenname 新增
            tokenname_create () {
                this.$refs.modal_token.create_or_edit = true;
                this.$refs.modal_token.form_reset();
                this.$refs.modal_token.modal_token_manage = true;
            },
            // tokenname 管理
            tokenname_manage () {
                this.$refs.modal_token.create_or_edit = false;
                this.$refs.modal_token.modal_token_manage = true;
                this.$refs.modal_token.tokenId_set(this.deleteTokenId);//设置当前tokenId
                this.$refs.modal_token.form_data_get();
            },
            // tokenname 删除
            tokenname_delete () {
                var param = {
                    tokenId : this.deleteTokenId
                }
                this.$Modal.confirm({
                    title: '提示',
                    content: '确认删除么？',
                    'ok-text':'确认',
                    onOk: () =>{
                        api.yop_dashboard_token_delete(param).then(
                            (response) =>{
                                if(response.status !== 'success'){
                                    this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                }else{
                                    this.$ypMsg.notice_success(this,'删除成功');
                                    this.default_tokenname = "";
                                    this.getTokenList();
                                    for(var i in this.$refs.select_tokenname){
                                        this.$refs.select_tokenname[i].clearSingleSelect();
                                    }
                                }
                            }
                        )
                    }
                });
                
            },
            // tokenname改变函数
            tokenname_change (e) {
                console.log(e)
                this.deleteTokenId = ""
                this.tokenname_choosen = this.manage_delete_show();
            },
            // 判断是否显示管理删除
            manage_delete_show () {
                return this.default_tokenname?true:false;
            },
            // 请求参数非空校验
            requestParam_checkNull () {
                for( var i in this.data_Template.params.params_normal){
                    if(this.data_Template.params.params_normal[i].required){
                        if(util.isNull(this.data_Template.params.params_normal[i].value)){
                            return true;
                        }
                    }
                }
                return false;
            },
            // 填写信息非空检测
            submitInfo_checkNull (data) {
                if(data){

                    if(util.isNull(this.default_name) || util.isNull(this.default_type) ||util.isNull(this.default_security) || util.isNull(this.model_test_env) || this.requestParam_checkNull()  || util.isNull(this.titleGet(this.current_id))){
                        this.$ypMsg.notice_error_simple(this,'请检查必填项是否填写完全');
                        return true;
                    }else{
                        return false;
                    }
                }else{
                    if(util.isNull(this.default_name) || util.isNull(this.default_type) ||util.isNull(this.default_security) || util.isNull(this.model_test_env) ||
                        (this.requestParam_checkNull() && this.tab_now === 'FORM') || (util.isNull(this.data_Template.params.params_JSON) && this.tab_now === 'JSON') || util.isNull(this.titleGet(this.current_id))){
                        this.$ypMsg.notice_error_simple(this,'请检查必填项是否填写完全');
                        return true;
                    }else{
                        return false;
                    }
                }

            },
            // 常用表达式选取
            expression_choose () {
                this.assertion_Expression = this.Normal_expressions;
            },
            // 删除测试用例
            deleteTemplate (bid,e) {
                e.stopPropagation();
                if(bid === null){
                    this.$Message.warning('保存后删除');
                }else{
                    this.$Modal.confirm({
                        title: '提示',
                        content: '确定要删除测试用例吗？',
                        'ok-text':'删除',
                        onOk: () =>{
                            var param = {
                                caseIds : bid
                            }
                            api.yop_dashboard_deleteTemplate(param).then(
                                (response) =>{
                                    if(response.status !== 'success'){
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    }else{
                                        this.$ypMsg.notice_success(this,'删除成功');
                                        this.click_Submenu(this.currentApiUri);
                                    }
                                }
                            )
                        }
                    });
                }
            },
            // 复制测试用例
            copyTemplate (name,id,e) {
                e.stopPropagation();
                this.collapse_change('copy',id);
                this.run_disable = false;
                this.current_id = null;
                var param ={
                    params : {
                        apiId : this.current_apiId
                    }
                }
                this.collapse_datalist.unshift({
                    index: this.currentTemplateCount.toString(),
                    id: null,
                    name: name+'-复制',
                    lastModifiedDateTime: '',
                    assertionCount : ''
                });
                this.collapse_label=[];
                // let a =9 ;
                this.collapse_label.push('0');
                // this.$nextTick(()=>{
                //
                // });
                this.currentTemplateCount++;
                this.data_Template.result_Response = {};
                this.data_Template.result_Assertion.result = '';
                this.data_Template.result_Assertion.content = [];

            },
            // 数组添加测试用例
            addTestTemplate (e) {
                e.stopPropagation();
                this.addTestTemplateOption();
            },
            // addTestTemplateOption
            addTestTemplateOption (){
                this.collapse_label=[];
                // let a =9 ;
                this.collapse_label.push('0');
                this.current_id = null;
                var param ={
                    params : {
                        apiUri : this.currentApiUri
                    }
                }
                this.detail_loading =true;
                this.run_disable = false;
                this.data_Template.result_Response = {};
                this.data_Template.result_Assertion.result = '';
                this.data_Template.result_Assertion.content = [];
                if(this.collapse_datalist.length === 0){
                    this.$set(this.collapse_datalist,0,{
                        index: this.currentTemplateCount.toString(),
                        id: null,
                        name: '测试用例'+(this.currentTemplateCount+1),
                        lastModifiedDateTime: '',
                        assertionCount : ''
                    });
                    this.currentTemplateCount++;
                    this.format_new(param);
                }else{
                    this.collapse_datalist.unshift({
                        index: this.currentTemplateCount.toString(),
                        id: null,
                        name: '测试用例'+(this.currentTemplateCount+1),
                        lastModifiedDateTime: '',
                        assertionCount : ''
                    });
                    this.currentTemplateCount++;
                    this.format_new(param);
                }
            },
            // 添加测试用例数据清空
            addTestTemplate_clearData () {

            },
            // 监听应用名称改变下拉框
            namechange (e) {
                this.default_name = e
                this.$refs.modal_token.passAppkey = e
                // if(this.default_name === 'create'){
                //     this.default_name = '';
                //     this.$refs.appName[0].clearSingleSelect();
                //     this.modal_Show_newapp =true;
                // }
            },
            // 监听分组下拉框选取
            selectChange () {
                this.menu_loading = true;
                this.menu_open = [];
                if(this.item_menuSelected !== '全部'){
                    // this.menu_open.push(this.item_menuSelected);
                    // let anchor = '#anchor-'+this.item_menuSelected
                    // this.goAnchor(anchor);
                    this.menuCheck();
                }else{
                    this.menu_datalist =[];
                    this.menu_open = [];
                    // this.menu_open.splice(0,this.menu_open.length);
                    this.menu_datalist = this.currentAllGroups;
                    this.menuShow = false;
                    this.menuShow = true;
                    this.menu_loading =false;
                }
                // this.$nextTick(() => {
                //     this.$refs.side_menu.updateOpened();
                //     this.$refs.side_menu.updateActiveName();
                // })
            },
            // 锚点定位跳转
            goAnchor(selector) {
                var anchor = this.$el.querySelector(selector);
                let scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
                anchor.scrollIntoView();
            },
            // 初始化函数获取左侧菜单api分组
            init () {
                this.getMenuList();
                this.showTokenPart();
            },
            // 菜单打开
            menu_opened (code){
                if(code[0]){
                    this.passApiGroup = code[0]
                    localStorage.currentApiGroupCode = code[0]
                    this.$refs.modal_token.passApiGroup = code[0]
                }
                if(!this.child_notNull_check(code)){
                    let param = {
                        apiGroupCode : code[0]
                    }
                    api.yop_dashboard_caseList(param).then(
                        (response) =>{
                            let childrenTemp = [];
                            for(var i in response.data.data['api-group-query-list']){
                                    childrenTemp.push({
                                        name : response.data.data['api-group-query-list'][i].apiTitle,
                                        regres: response.data.data['api-group-query-list'][i].idempotent,
                                        apiId: response.data.data['api-group-query-list'][i].apiId,
                                        apiUri: response.data.data['api-group-query-list'][i].apiUri
                                    })
                            }
                            this.apiGroup_data_set(code[0],childrenTemp);
                        }
                    )
                }
            },
            // 查找api分组并放入
            apiGroup_data_set (code,data) {
                if(!!this.menu_datalist){
                    for(var i in this.menu_datalist){
                        if(this.menu_datalist[i].code === code){
                            this.$set(this.menu_datalist[i],'child',data);
                            break;
                        }
                    }
                }
            },
            // 查找是否已有数据
            child_notNull_check (code) {
                let codeTmp =code[0];
                if(code && code.length > 0){
                    if(!!this.menu_datalist){
                        for(var i in this.menu_datalist){
                            if(this.menu_datalist[i].code === codeTmp){
                                if(this.menu_datalist[i].child && this.menu_datalist[i].child.length >0){
                                    return true;
                                }else{
                                    return false;
                                }
                            }
                        }
                    }
                    return false;
                }else{
                    return true;
                }
            },
            // 获取左侧菜单api分组
            getMenuList () {
                this.menu_loading = true;
                // let param = {
                //     params:{
                //         withChild : true
                //     }
                // }
                // api.yop_dashboard_caseList(param).then(
                //     (response) =>{
                //         this.menu_datalist =[]
                //         let group = [{
                //             name : '全部',
                //             label : '全部',
                //             code : '全部'
                //         }];
                //         for(var i in response.data.data['api-group-query-list']){
                //             let childrenTemp = [];
                //             for(var j in response.data.data['api-group-query-list'][i].children){
                //                 childrenTemp.push({
                //                     name : response.data.data['api-group-query-list'][i].children[j].apiTitle,
                //                     regres: response.data.data['api-group-query-list'][i].children[j].idempotent,
                //                     apiId: response.data.data['api-group-query-list'][i].children[j].apiId,
                //                     apiUri: response.data.data['api-group-query-list'][i].children[j].apiUri
                //                 })
                //             }
                //             group.push({
                //                 name: response.data.data['api-group-query-list'][i].groupTitle,
                //                 label: response.data.data['api-group-query-list'][i].groupTitle,
                //                 code: response.data.data['api-group-query-list'][i].groupCode,
                //                 child: childrenTemp
                //             })
                //         }
                //         this.menu_datalist = group;
                //         this.menu_loading = false;
                //         this.currentAllGroups =group;
                //         if(localStorage.currentApiId) this.apiGroupSet_apiUri(localStorage.currentApiId);
                //     }
                // )
                api.yop_dashboard_apis_list().then(
                    (response) =>{
                        let status =response.data.status
                        if(status === 'success'){
                            let resultTemp = response.data.data.result;
                            this.menu_datalist = [];
                            let group = [{
                                name: '全部',
                                label: '全部',
                                code: '全部'
                            }];
                            for(var i in resultTemp){
                                group.push({
                                    code: resultTemp[i].apiGroupCode,
                                    label: resultTemp[i].apiGroupName,
                                    name: resultTemp[i].apiGroupName,
                                })
                            }
                            this.menu_datalist = group;
                            this.currentAllGroups =group;
                        }else{
                            this.$ypMsg.no
                            tice_error(this,'api列表数据获取失败,请刷新重试',response.data.message,response.data.solution);
                        }
                        this.menu_loading = false;
                        // if(this.passApiGroup){
                        //     localStorage.currentApiGroupCode = this.passApiGroup
                        // }
                        if(localStorage.currentApiId && localStorage.currentApiGroupCode) this.apiGroupSet_apiUri(localStorage.currentApiId,this.currentApiUri,localStorage.currentApiGroupCode);
                    }
                )
            },
            // 冒泡阻止，防止点击输入框，触发手风琴操作
            stopProp (e) {
                e.stopPropagation();
            },
            // 点击编辑图标编辑,阻止冒泡
            editable_Control (e) {
                e.stopPropagation();
                if (this.edit_or_check === 'android-create') {
                    this.collapse_input_editable = true;
                    this.edit_or_check = 'checkmark';
                } else if (this.edit_or_check === 'checkmark') {
                    this.collapse_input_editable = false;
                    this.edit_or_check = 'android-create';
                }
            },
            // 筛选剔除后的数组
            MenuList_filter (list) {
                return list.filter(function (item) {
                    return item.name !== '全部';
                });
            },
            // 返回用例名称可编辑结果
            template_name_editable (openindex,index,collapse_input_editable) {
                if(openindex==index){
                    if(collapse_input_editable){
                        return true;
                    }else{
                        return false;
                    }
                }else{
                    return false;
                }
            },
            // 面板改版触发函数
            collapse_change (op,id) {
                this.data_Template.result_Response = {};
                this.data_Template.result_Assertion.result = '';
                this.data_Template.result_Assertion.content = [];
                this.run_disable = false;
                if(id !== '' && id !== null){
                    if(this.current_collapse_id !== id){
                        this.current_collapse_id =id;
                        this.detail_loading = true;
                        this.collapse_input_editable = false;
                        this.edit_or_check = 'android-create';
                        var param ={
                            params : {
                                id : id
                            }
                        }
                        this.current_id = id;
                        this.format_detail(param);
                    } else {
                        this.current_collapse_id = null;
                    }
                }else{
                    // if(this.current_id !== id){
                    //     this.$Modal.confirm({
                    //         title: '提示',
                    //         content: '测试用例未保存，编辑其他测试用例会丢失确认离开吗？',
                    //         'ok-text':'确认',
                    //         onOk: () =>{
                    //
                    //         }
                    //     });
                    // }
                    this.detail_loading = true;
                    this.current_collapse_id = null;
                    this.format_new({
                        params : {
                            apiUri : this.currentApiUri
                        }
                    });
                    this.collapse_input_editable = false;
                    this.edit_or_check = 'android-create';
                    this.current_id = null;
                }
            },
            format_copy (param) {
                api.yop_dashboard_regressionDetail(param).then(
                    (response) => {
                        //*（需要删除）
                        console.log(response);
                    }
                )
            },
            // 新建用例详细信息获取处理
            format_new (param) {
                api.yop_dashboard_regressionDetail(param).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            let detail = response.data.data.detail;
                            this.data_Template.main.title = this.current_apiGroup + ' - ' + this.currentUri
                            this.default_security = '';
                            this.default_tokenname = '';
                            this.$refs.select_tokenname[0].clearSingleSelect();
                            this.tokenname_choosen = this.manage_delete_show();
                            this.default_type = ''
                            this.editInlineData = [];
                            let params_form = this.format_params(detail.params.form);
                            this.data_Template.params.params_normal = params_form;
                            if(detail.params.json){
                                let params_json = detail.params.json;
                                this.data_Template.params.params_JSON =params_json;
                            }else{
                                this.data_Template.params.params_JSON ='';
                            }
                            let apps =detail.apps
                            if(apps && apps.length > 0){
                                this.format_appName(apps);
                                this.default_name = detail.appKey;
                            }else{
                                this.data_Template.main.name = []
                                // this.data_Template.main.name = [
                                //     {
                                //         name : 'create',//下拉列表的value
                                //         customerNo : 'create',
                                //         title : '+新增测试应用'//下拉列表的title
                                //     }
                                // ]
                            }
                            this.model_test_env = this.test_env_List[0] ? this.test_env_List[0].value : '';
                            let securities = detail.securities || [];
                            this.data_Template.main.securities=securities;
                            this.default_security = detail.securities[0] ? detail.securities[0].name : '';
                            this.showTokenPart()
                            // if(this.length_count(this.data_Template.main.securities) > 1){
                            //     this.security_only = false;
                            // }else {
                            //     this.security_only = true;
                            // }
                            this.detail_loading = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                        }
                    }
                )
            },
            // 测试用例详细信息获取处理
            format_detail (param){
                api.yop_dashboard_regressionDetail(param).then(
                    (response) => {
                        let detail = response.data.data.detail;
                        this.data_Template.main.title = this.current_apiGroup + ' - ' + this.currentUri;
                        this.default_security = detail.security;
                        this.showTokenPart()
                        if(detail.regressive === true){
                            this.data_Template.main.regressionType = true;
                            this.default_type = '回归用例'
                        }else{
                            this.data_Template.main.regressionType = false;
                            this.default_type = '一般用例'
                        }
                        let assertionlist = this.format_assertionList(detail.assertionList);
                        this.editInlineData = assertionlist;
                        let params_form = this.format_params(detail.params.form);
                        this.data_Template.params.params_normal = params_form;
                        let params_json = detail.params.json;
                        this.data_Template.params.params_JSON =params_json;
                        let apps =detail.apps
                        if(apps.length === 0){
                            this.data_Template.main.name = []
                            // [
                            //     {
                            //         name : 'create',//下拉列表的value
                            //         customerNo : 'create',
                            //         title : '+新增测试应用'//下拉列表的title
                            //     }
                            // ]
                        }else{
                            this.format_appName(apps);
                            this.default_name = detail.appKey;
                        }
                        this.model_test_env =detail.env;
                        let securities = detail.securities;
                        this.data_Template.main.securities=securities;
                        this.default_security = detail.security;
                        this.default_tokenname = detail.tokenName;
                        this.tokenname_choosen = true;
                        this.deleteTokenId = detail.tokenId;
                        this.showTokenPart()

                        // if(this.length_count(this.data_Template.main.securities) > 1){
                        //     this.security_only = false;
                        // }else {
                        //     this.security_only = true;
                        // }
                        this.detail_loading = false;
                        if(!this.security_Check(detail.security,detail.securities)){
                            this.$Modal.warning({
                                title: '警告',
                                content: '测试用例当前的API加签方式（'+detail.security+'）与API的安全需求不匹配,请修改!'
                            });
                            this.default_security = detail.securities[0].name;
                            this.run_disable = true;
                        }
                    }
                )
            },
            // getTokenId 
            setTokenId(tokenId){
                this.deleteTokenId = tokenId
            },
            // 获取tokenName列表
            getTokenList(default_tokenname){
                let param = {
                    apiGroup : this.passApiGroup || localStorage.currentApiGroupCode,
                }
                api.yop_token_list(param).then(
                    (response) => {
                        //需要填写
                        var result = response.data.data.result
                        let tokenname = result;
                        this.data_Template.main.tokenname=tokenname;
                        this.$nextTick(() => {
                            if(default_tokenname){
                                for(var i in tokenname){
                                    if(tokenname[i].tokenName == default_tokenname){
                                        this.deleteTokenId = tokenname[i].tokenId
                                    }
                                }
                                this.default_tokenname = default_tokenname;
                                this.tokenname_choosen = true;
                            }
                        })
                    }
                )
            },
            // 检查加签方式
            security_Check (single,set){
                for(var i in set){
                    if(set[i].name === single){
                        return true;
                    }
                }
                return false;
            },
            // 加签方式数组格式处理
            format_securities (sts){
                let stsTmp = [];
                sts.forEach(val =>{
                    stsTmp.push({
                        name : val.name,
                        title : val.title
                    })
                })
                return stsTmp;
            },
            // 测试应用数组格式处理
            format_appName (appNames) {
                this.data_Template.main.name = []
                // [
                //     {
                //         name : 'create',//下拉列表的value
                //         customerNo : 'create',
                //         title : '+新增测试应用'//下拉列表的title
                //     }
                // ]
                appNames.forEach( val =>{
                    this.data_Template.main.name.push({
                        name : val.appKey,
                        title : val.appKey,
                        customerNo : val.customerNo
                    })
                })
            },
            // 参数列表数组格式处理
            format_params (params) {
                let paramsTmp = [];
                params.forEach(val =>{
                    paramsTmp.push({
                        name: params.name,
                        title: params.title,
                        required : params.required,
                        value : params.value
                    })
                })
                return params;
            },

            // 断言列表数组处理
            format_assertionList (list){
                let listTemp = []
                if(list && list.length>0){
                    list.forEach(val=>{
                        listTemp.push({
                            name : val.title,
                            assertion : val.expression
                        })
                    })
                }
                return listTemp;
            },
            // 统计json数组的长度
            length_count (array) {
                var count = 0;
                for (var num in array){
                    count++;
                }
                return count;
            },
            // 新建测试确定函数
            ok_newapp () {
                if(util.isNull(this.test_businessCode)){
                    this.$Message.warning('商编不能为空');
                }else{
                    let params = {
                        customerNo: this.test_businessCode ,
                        apiId :this.current_apiId
                    }
                    api.yop_dashboard_appCreate(params).then(
                        (response) =>{
                            if(response.status !== 'success'){
                                this.$Modal.error({
                                    title: '错误',
                                    content: response.message
                                });
                            }else{
                                this.data_Template.main.name = []
                                // this.data_Template.main.name = [
                                //     {
                                //         name : 'create',//下拉列表的value
                                //         customerNo : 'create',
                                //         title : '+新增测试应用'//下拉列表的title
                                //     }
                                // ];
                                this.$Modal.success({
                                    title: '成功',
                                    content: '测试应用添加成功'
                                });
                                this.data_Template.main.name.push({
                                    name : response.data.appKey,//下拉列表的value
                                    customerNo : params.customerNo,
                                    title : response.data.appKey//下拉列表的title
                                })
                            }
                        }
                    )
                    this.test_businessCode ='';
                    this.default_name = '';
                    this.modal_Show_newapp = false;
                }
            },
            // 取消新建确定函数
            cancel_newapp () {
                this.default_name = '';
                this.modal_Show_newapp = false;
            },
            // 遍历获取当前api是否可以回归
            regression_check (name) {
                for(var i in this.menu_datalist){
                    if(this.menu_datalist[i].child){
                        for(var j in this.menu_datalist[i].child){
                            if(this.menu_datalist[i].child[j].apiUri.toString() === name.toString()){
                                return this.menu_datalist[i].child[j].regres;
                            }
                        }
                    }
                }
            },
            // 点击左侧子菜单
            click_Submenu (name,savedId) {
                localStorage.currentApiUri = name
                this.show_add_template =true;
                this.collapse_loading =true;
                this.currentTemplateCount = 0;
                this.goAnchor('#regrsPanel');
                this.currentApiUri = name;
                this.current_regres = this.regression_check(name);
                let param = {
                        apiUri : name 
                }
                this.apiUriNameSet(name);//
                this.getTokenList()
                api.yop_dashboard_testTemplateList(param).then(
                    (response) =>{
                        this.collapse_datalist = [];
                        for(var i in response.data.list){
                            if(i === '0'){
                                this.collapse_change('',savedId || response.data.list[i].id);
                            }
                            this.collapse_datalist.push({
                                index : i,
                                id : response.data.list[i].id,
                                name : response.data.list[i].title,
                                lastModifiedDateTime: response.data.list[i].lastModifiedDateTime,
                                assertionCount : response.data.list[i].assertionCount,
                                regressive: response.data.list[i].regressive
                            });
                            // TODO
                            this.checkAllGroupArr.push(response.data.list[i].id)
                            this.currentTemplateCount++;
                        }
                        this.collapse_loading =false;
                    }
                )
                this.get_currentUri(name);
            },
            // json匹配函数获取uri
            get_currentUri (apiUri) {
                for(var i in this.menu_datalist){
                    for (var j in this.menu_datalist[i].child){
                        if (apiUri === this.menu_datalist[i].child[j].apiUri){
                            this.currentUri = this.menu_datalist[i].child[j].apiUri
                        }
                    }
                }
            },
            // 保存参数信息处理
            format_saveParams () {
                let param ={
                    id : this.current_id,
                    apiUri : this.currentUri,
                    title : this.titleGet(this.current_id),
                    appKey : this.default_name,
                    security : this.default_security,
                    env : this.model_test_env,
                    regressive : this.format_template_type(),
                    params: {
                        form : this.format_runParams_formData(),
                        json : this.data_Template.params.params_JSON
                    },
                    assertionList : this.format_runParams_assertion(),
                    tokenId:this.deleteTokenId
                }
                return param;
            },
            // 用例
            format_template_type () {
                if(this.default_type === '回归用例'){
                    // this.current_regres = true//TODO
                    return true;
                }else if (this.default_type === '一般用例'){
                    return false;
                }
            },
            // 测试用例标题返回
            titleGet (id) {
                if(id !== null){
                    for( var i in this.collapse_datalist){
                        if(id ===this.collapse_datalist[i].id){
                            return this.collapse_datalist[i].name;
                        }
                    }
                }else{
                    for( var i in this.collapse_datalist){
                        if((this.currentTemplateCount-1).toString() === this.collapse_datalist[i].index){
                            return this.collapse_datalist[i].name;
                        }
                    }
                }
            },
            // 执行参数信息处理
            format_runParams () {
                let paramTemp = {
                    form : this.format_runParams_formData(),
                    json : this.data_Template.params.params_JSON
                }
                if(this.tab_now === 'FORM'){
                    delete paramTemp['json']
                }else{
                    delete paramTemp['form']
                }
                let param ={
                    apiUri : this.currentUri,
                    appKey : this.default_name,
                    security : this.default_security,
                    env : this.model_test_env,
                    params: paramTemp,
                    assertionList : this.format_runParams_assertion()
                    ,accessToken:this.default_accesstoken
                    ,tokenId:this.deleteTokenId
                }
                return param;
            },
            // formData处理
            format_runParams_formData () {
                let formData = []
                this.data_Template.params.params_normal.forEach(val => {
                    formData.push({
                        name : val.name,
                        value: val.value
                    })
                })
                return formData;
            },
            // 断言列表处理
            format_runParams_assertion () {
                let asst = [];
                this.editInlineData.forEach(val =>{
                    asst.push({
                        title : val.name,
                        expression : val.assertion
                    })
                })
                return asst;
            },
            // 编辑断言表格内容
            table_editLine (row,index) {
                this.assertion_modAdd =true;
                this.current_EditLine = index;
                this.modal_Show_assertion =true;
                this.assertion_Name = row.name;
                this.assertion_Expression = row.assertion;
            },
            // 删除断言表格内容
            table_deleteLine (row,index) {
                this.editInlineData.splice(index,1);
            },
            // 断言窗口取消
            cancel_assertion () {
                this.modal_Show_assertion = false;
                this.assertion_Name = '';
                this.assertion_Expression = '';
                this.Normal_expressions = '';
            },
            // 断言窗口确定
            ok_assertion () {
                if(this.assertion_Name ===''||this.assertion_Name===null){
                    this.$Message.warning('断言名称不能为空');
                    return false;
                }else if(this.assertion_Expression ==='' || this.assertion_Expression === null){
                    this.$Message.warning('表达式不能为空');
                    return false;
                }else{
                    if(this.assertion_modAdd){
                        this.editInlineData[this.current_EditLine].name = this.assertion_Name;
                        this.editInlineData[this.current_EditLine].assertion = this.assertion_Expression;
                    }else{
                        this.editInlineData.push({
                            name : this.assertion_Name,
                            assertion : this.assertion_Expression
                        })
                    }
                    this.modal_Show_assertion = false;
                    this.assertion_Name = '';
                    this.assertion_Expression = '';
                    this.Normal_expressions = '';
                }
            },
            // 新增断言
            table_newAssertion () {
                this.assertion_modAdd = false;
                this.modal_Show_assertion = true;
            },
            // 遍历获取到数组展开转过来的api以及设置当前apiURi/apiName
            apiGroupSet_apiUri (id,apiUri,code) {
                if(apiUri){
                    localStorage.currentApiUri = apiUri
                }
                var apiUri = apiUri || localStorage.currentApiUri
                let codeTmp = [code];
                if(!this.child_notNull_check(codeTmp)){
                    let param = {
                        apiGroupCode : codeTmp[0]
                    }
                    api.yop_dashboard_caseList(param).then(
                        (response) =>{
                            let childrenTemp = [];
                            for(var i in response.data.data['api-group-query-list']){
                                childrenTemp.push({
                                    name : response.data.data['api-group-query-list'][i].apiTitle,
                                    regres: response.data.data['api-group-query-list'][i].idempotent,
                                    apiId: response.data.data['api-group-query-list'][i].apiId,
                                    apiUri: response.data.data['api-group-query-list'][i].apiUri
                                })
                            }
                            this.apiGroup_data_set(codeTmp[0],childrenTemp);
                            this.active_menu = Number(id);
                            this.MenuList_filter(this.menu_datalist).forEach(value => {
                                if(value.child && value.child.length>0){
                                    value.child.forEach(subValue =>{
                                        if(subValue.apiUri === apiUri){
                                            this.currentUri = subValue.apiUri;
                                            this.current_apiGroup = subValue.name;
                                            this.menu_open =[];
                                            this.menu_open.push(value.code);
                                            this.$nextTick(() => {
                                                this.$refs.side_menu.updateOpened();
                                                this.$refs.side_menu.updateActiveName();
                                            })
                                        }
                                    })
                                }
                            })
                            this.click_Submenu(localStorage.currentApiUri);
                        }
                    )
                }
            },
            // 设置当前apiURi/apiName
            apiUriNameSet(apiUri) {
                this.MenuList_filter(this.menu_datalist).forEach(value => {
                    if(value.child && value.child.length > 0){
                        value.child.forEach(subValue =>{
                            if(subValue.apiUri === apiUri){
                                this.currentUri = subValue.apiUri;
                                this.current_apiGroup = subValue.name;
                            }
                        })
                    }
                })
            },
            // menu 筛选
            menuCheck () {
                this.MenuList_filter(this.currentAllGroups).forEach(val=>{
                    if(val.code === this.item_menuSelected){
                        this.menu_datalist = [];
                        this.menu_datalist.push(val);
                        this.menu_open.push(val.code);
                        this.menu_opened(this.menu_open);
                        this.$nextTick(() => {
                            this.$refs.side_menu.updateOpened();
                            this.$refs.side_menu.updateActiveName();
                        })
                        this.menu_loading =false;
                    }
                })
            },
            // 保存用例
            save_Template () {
                if(!this.submitInfo_checkNull(true)){
                    if(this.default_type === '回归用例'){
                        if(this.editInlineData.length === 0){
                            this.$ypMsg.notice_error_simple(this,'错误','您还没有添加断言，无法开启回归验证');
                        } else if (!this.current_regres){
                            this.$ypMsg.notice_error_simple(this,'错误','该API未标记为幂等，不支持回归测试');
                        }else{
                            let params=this.format_saveParams();
                            api.yop_dashboard_saveTemplate(params).then(
                                (response) =>{
                                    if(response.status !== 'success'){
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    }else{
                                        this.$ypMsg.notice_success(this,'保存成功');
                                        this.click_Submenu(this.currentApiUri,params.id);
                                    }
                                }
                            )
                        }
                    }else
                    {
                        let params=this.format_saveParams();
                        api.yop_dashboard_saveTemplate(params).then(
                            (response) =>{

                                if(response.status !== 'success'){
                                    this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                }else{
                                    this.$ypMsg.notice_success(this,'保存成功');
                                    this.click_Submenu(this.currentApiUri,params.id);
                                }
                            }
                        )
                    }
                }
            },
            // 执行用例
            run_Template () {
                if(this.tab_now === 'JSON'){
                    if( this.default_security === 'YOP-RSA2048-SHA256'){
                        this.run_template_handler();
                    }else{
                        this.$Modal.warning({
                            title: '警告',
                            content: '执行JSON用例，仅支持选择YOP-RSA2048-SHA256 API加签方式'
                        });
                    }
                }else{
                    this.run_template_handler();
                }

            },
            // 执行用例执行
            run_template_handler () {
                if(!this.submitInfo_checkNull(false)) {
                    this.run_loading = true;
                    let params = this.format_runParams();
                    api.yop_dashboard_executeSingle(params).then(
                        (response) => {
                            if (response.status !== 'success') {
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                this.data_Template.result_Response = {}
                                this.data_Template.result_Assertion.content = [];
                                this.run_loading = false;
                            } else {
                                let status = response.data.executionResult.status;
                                let rawResponse = response.data.executionResult.rawResponse;
                                let assertionList = response.data.executionResult.assertionList;
                                this.data_Template.result_Response = rawResponse;
                                if (status === 'success') {
                                    this.data_Template.result_Assertion.result = 'success';
                                } else {
                                    this.data_Template.result_Assertion.result = 'failure';
                                }
                                this.data_Template.result_Assertion.content = [];
                                if(assertionList && assertionList.length > 0){
                                    assertionList.forEach(val => {
                                        if (val.status || val.result) {
                                            let resultTmp = '';
                                            if (val.status === 'success') {
                                                resultTmp = 'color : grey'
                                            } else {
                                                resultTmp = 'color : red'
                                            }
                                            let resultData = '';
                                            if (!val.result && val.expression) {
                                                resultData = val.expression
                                            } else {
                                                resultData = val.result
                                            }
                                            this.data_Template.result_Assertion.content.push({
                                                result: resultTmp,
                                                title: val.title,
                                                content: resultData
                                            })
                                        }

                                    })
                                }
                                this.run_loading = false;
                            }
                        }
                    )
                }
            },
            // 新建测试用例跳转
            new_app_regression () {
                // this.$store.state.test_template_label = true;
                // this.$router.replace({
                // name: "/app/list"
                // });
                let tips = localStorage.test_template_tips ? localStorage.test_template_tips : this.$store.state.test_template_tips;
                let mail = localStorage.mail_operation ? localStorage.mail_operation : this.$store.state.mail_operation;
                let content = '<p>'+tips+'('+mail+")   <a href='mailto:"+mail+"'>去发邮件>></a></p>" 
                this.$Modal.info({
                    title: "新增回归测试应用说明",
                    content: content
                });
            }

        }
    };
</script>

<style>
    #caseList ul.ivu-menu {
        z-index: 90 !important;
    }
    #caseList .ivu-select-dropdown {
        z-index: 999;
    }

    #caseList .ivu-checkbox+span, #caseList .ivu-checkbox-wrapper+span{
        display:none!important;

    }
    #caseList .ivu-checkbox-group-item{
        float:left;
        margin:8px 0 0 8px;
    }
     #caseList .ivu-checkbox-group-item .ivu-checkbox+span {
        display:none!important;
     }

</style>
