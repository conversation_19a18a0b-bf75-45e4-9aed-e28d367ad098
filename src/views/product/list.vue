<style lang="less">
@import "../../styles/common.less";
@import "../API_Management_Open/api_Mangement_Open.less";
.demo-badge-alone {
  background: #5cb85c !important;
}
.round {
  width: 16px;
  height: 16px;
  display: inline-block;
  font-size: 20px;
  line-heigth: 16px;
  text-align: center;
  color: #f00;
  text-decoration: none;
}
.reasonType {
    vertical-align: top;
    display: inline-block;
    font-size: 12px;
    margin-top: 5px;
}
.ivu-modal-header ,.ivu-modal-footer{
  border:0;
}
.ivu-icon-help-circled:before{
  content:"\F142";
}
</style>
<template>
  <div style="background: #eee;padding:8px">
    <Card :bordered="false">
      <Row type="flex" align="middle">
        <Col span="20">
          <Col span="7">
            <Col span="8" class="margin-top-10">
              <span>产品编码:</span>
            </Col>
            <Col span="16">
              <Input

                id="input_serviceAuth_1"
                class="margin-top-5"
                v-model="data_product_code"
                placeholder="产品编码"
                @on-enter="search_interface(false)"
              ></Input>
            </Col>
          </Col>

          <Col offset="1" span="7">
            <Col span="8" class="margin-top-10">
              <span>产品名称:</span>
            </Col>
            <Col span="16">
              <Input
                id="input_serviceAuth_2"
                class="margin-top-5"
                v-model="data_product_name"
                placeholder="产品名称"
                @on-enter="search_interface(false)"
              ></Input>
            </Col>
          </Col>

          <Col offset="1" span="7">
            <Col span="8" class="margin-top-10">
              <span>产品类型:</span>
            </Col>
            <Col span="16">
              <common-select
                ref="product_type"
                @on-update="updateSelect_type"
                type="normal"
                keyWord="result"
                holder="请选择（默认全部）"
                code="code"
                title="name"
                group="productType"
                @on-loaded="select_callBack"
                :default="this.data_select_type"
                :uri="this.$store.state.select.productType.uri"
              ></common-select>
            </Col>
          </Col>

          <Col class="margin-top-5" span="7">
            <Col span="8" class="margin-top-10">
              <span>服务提供方:</span>
            </Col>
            <Col span="16">
              <common-select
                ref="select_provider"
                @on-update="updateSelect_provider"
                type="combo"
                keyWord="result"
                holder="请选择（默认全部）"
                code="spCode"
                title="spName"
                group="serviceSupplier"
                @on-loaded="select_callBack"
                :default="this.data_select_provider"
                :uri="this.$store.state.select.serviceSupplier.uri"
              ></common-select>
            </Col>
          </Col>

          <Col class="margin-top-5" offset="1" span="7">
            <Col span="8" class="margin-top-10">
              <span>状态:</span>
            </Col>
            <Col span="16">
              <common-select
                ref="select_status"
                @on-update="updateSelect_status"
                type="normal"
                keyWord="result"
                holder="请选择（默认全部）"
                code="code"
                title="name"
                group="product_status"
                @on-loaded="select_callBack"
                :default="this.data_select_status"
                :uri="this.$store.state.select.product_status.uri"
              ></common-select>
            </Col>
          </Col>
          <Col class="margin-top-5" offset="1" span="7">
            <Col span="7">
              <Col class="margin-top-10">创建时间:</Col>
            </Col>
            <date-picker
              ref="datepicker"
              @on-start-change="update_start_date"
              @on-end-change="update_end_date"
              :default_start="this.dateStart"
              :default_end="this.dateEnd"
            ></date-picker>
          </Col>
          <Col span="7"  class="margin-top-5" >
            <Col span="8" class="margin-top-10">
              <span>API URI:</span>
            </Col>
            <Col span="16">
              <Input
                id="input_apiM_1"
                class="margin-top-5"
                clearable
                v-model="data_product_apiUri"
                placeholder="API URI"
                @on-enter="search_Interface"
              ></Input>
            </Col>
          </Col>
        </Col>

        <Col span="4">
          <Col class="margin-top-10" span="11" style="text-align:center">
            <Button id="btn_apiM_1" type="primary" @click="search_interface(false)">查询</Button>
          </Col>
          <Col class="margin-top-10" span="11" style="text-align:center">
            <Button id="btn_apiM_2" type="ghost" @click="reset_Interface">重置</Button>
          </Col>
        </Col>
      </Row>
      <Row class="margin-top-20">
        <Col span="24">
          <Button class="margin-right-10" type="primary" v-url="{url:'/rest/product/create'}" @click="product_add">新增产品</Button><!--  -->
          <!-- <Button class="margin-right-10" style="background:#20cb9a;border:0;" type="primary" @click="update_product_list">更新同步</Button> -->
          <Button class="margin-right-10" style="background:#20cb9a;border:0;" type="primary"  v-url="{url:'/rest/product/change/list'}" @click="product_change_record">产品管理变更记录</Button><!-- -->

        </Col>
      </Row>
      <Row class="margin-top-10">
        <Col span="24">
          <Table
            id="table_1"
            border
            :columns="columns_productCodeList"
            :data="data_productCodeList"
          ></Table>
          <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
            <Page
              class="margin-top-10"
              style="float: right"
              :total="pageTotal"
              :page-size="10"
              :current="pageNo"
              show-elevator
              @on-change="pageRefresh"
            ></Page>
          </Tooltip>
        </Col>
        <loading :show="show_loading_serviceAuthList"></loading>
      </Row>



      <Modal id="modal_ag_1" v-model="modal_add_product" width="630" :transfer=false :closable="false" :mask-closable="false">
            <Card class="margin-left-16" style="width:600px;margin:0 auto;" dis-hover >
                <p v-show="create_orEdit" slot="title">
                    新增产品
                </p>
                <p v-show="!create_orEdit" slot="title">
                    编辑产品
                </p>
                <a slot="extra" @click.prevent="add_close">
                    <Icon type="close"></Icon>
                </a>
                <Form ref="form_detail" :model="form_detail" :rules="rule_detail" :label-width="120">
                    <FormItem label="产品编码：" v-show="!create_orEdit">
                        {{form_detail.product_code_default}}
                    </FormItem>
                    <FormItem label="产品编码：" v-show="create_orEdit" prop="product_code_default">
                    <Input id="product_id_01" type="text" size="small" v-model="form_detail.product_code_default"
                                style="width:85%"></Input>
                    </FormItem>
                    <p class="yop-explain-120">支持数字、大小写字母、下划线，最长支持输入64个字符。</p>
                    <FormItem label="产品名称：" prop="product_name_default">
                        <Input id="product_id_02" type="text" size="small" v-model="form_detail.product_name_default"
                                style="width:85%"></Input>
                    </FormItem>
                    <p class="yop-explain-120">支持汉字、大小写字母、数字，最长支持输入64个字符</p>
                    <FormItem label="产品类型：" prop="productType">
                         <common-select ref="select_sp_code" @on-update="updateSelect_productType_model"
                                        v-model="form_detail.productType"
                                        type="combo"
                                        keyWord="result"
                                        holder="请选择产品类型"
                                        code="code"
                                        title="name"
                                        size="small"
                                        group="productType"
                                        id="product_id_03"
                                        @on-loaded="select_callBack_type"
                                        :default="this.data_product_type"
                                        :uri="this.$store.state.select.productType.uri"
                                        style="width:85%"></common-select>
                    </FormItem>
                    <FormItem label="服务提供方编码：" prop="spCode">
                        <common-select ref="select_sp_m" @on-update="updateSelect_sp_model"
                                        v-model="form_detail.spCode"
                                        type="combo"
                                        keyWord="result"
                                        holder="请选择服务提供方编码"
                                        code="spCode"
                                        title="spName"
                                        size="small"
                                        group="serviceSupplier"
                                        id="product_id_04"
                                        @on-loaded="select_callBack_service"
                                        :default="this.serviceSupplier"
                                        :uri="this.$store.state.select.serviceSupplier.uri"
                                        style="width:85%"></common-select>
                        <!-- <Button type="primary" shape="circle" size="small" icon="loop" style="margin-left:5px;" @click="refresh_sp"></Button> -->
                    </FormItem>
                    <p class="yop-explain-120">如果没有合适sp,<a @click="sp_jump">新增SP>></a></p>
                    <FormItem label="描述：" prop="description">
                        <Input id="product_id_05" type="textarea" size="small" v-model="form_detail.description"
                                style="width:85%"></Input>
                    </FormItem>
                    <p class="yop-explain-120">最长支持输入300个字符</p>
                    <FormItem label="icon：" prop="description" v-if="form_detail.productType === 'DOCKING'">
                      <Upload
                        :before-upload="(file) => { form_detail.icon = file; return false }"
                        action="#"
                        accept="image/jpg"
                        :show-upload-list="true"
                      >
                        <div v-if="form_detail.icon !==null">{{ form_detail.icon.name }}</div>
                        <Button type="primary">上传</Button>
                      </Upload>
                      <p>仅支持.jpg格式</p>
                    </FormItem>
                </Form>
            </Card>
            <div slot="footer">
              <Button type="primary" class="margin-right-10" @click="add_submit('form_detail')">确定</Button>
              <Button type="ghost" @click="add_close">关闭</Button>
            </div>
      </Modal>
<!-- 删除/恢复/下线 -->
      <Modal v-model="delete_or_normal_modal_show" width="500"  :closable="false">
          <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
              <Icon type="ios-information-circle"></Icon>
              <span v-show="deleteProduct">删除</span>   
              <span v-show="normalProduct">恢复</span> 
              <span v-show="offlineProduct">下线</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;">
                <i class="ivu-icon ivu-icon-help-circled"></i>
              </div>
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                <p v-show="deleteProduct" style="color:red">请确认该产品无商户授权，否则删除后无法恢复！</p>
                <p v-show="deleteProduct">确定删除?</p>
                <p v-show="normalProduct">确定恢复?</p>
                <p v-show="offlineProduct" style="color:red">如果下线产品码，新商户不能授权已下线产品码及调用API接口。</p>
                <p v-show="offlineProduct">确定下线?</p>
                <label for="" class="reasonType"><span style="color:red;font-size: 12x;">*</span>原因：</label>
                <Input id="product_id_06" type="textarea" v-model="cause" style="width:85%;font-size: 12x;margin-top:8px;" placeholder="" placeholder-style="font-size:12px;" :maxlength="60"></Input>
                <p style="color:#888;font-size:12px;margin-top:10px;margin-left:50px">最长可输入60字符</p>
              </div>
          </div>
          <div slot="footer" style="border:0;">
              <Button type="ghost"  @click="hide_model">取消</Button>
              <Button type="primary"  @click="sure_model">确定</Button>
          </div>
      </Modal>
      
      <modal-edit-not-top ref="card_edit_sg"></modal-edit-not-top>
      <modal-link-api-group ref="link_api_group"></modal-link-api-group>
      <modal-link-group ref="link_group"></modal-link-group>
      <modal-change-record ref="change_record"></modal-change-record>
      <modal-sga-new-auth ref="modal_sga_new_auth" @update-list="update_serviceAuthList"></modal-sga-new-auth>
    </Card>
  </div>
</template>

<script>
import loading from '../my-components/loading/loading';
import commonSelect from '../common-components/select-components/selectCommon';
import util from '../../libs/util';
import api from '../../api/api';
import datePicker from '../common-components/date-components/date-picker';
import modalEditNotTop from './modals/modal-edit-not-top'
import modalLinkApiGroup from './modals/modal-link-api-group';
import modalLinkGroup from './modals/modal-link-api-group';
import modalChangeRecord from './modals/modal-change-record'
import modalSgaNewAuth from '../common-components/modal-components/modal-sga-new-auth'
export default {
  name: 'isp-list',
  components: {
    loading,
    commonSelect,
    modalEditNotTop,
    modalLinkApiGroup,
    modalLinkGroup,
    modalChangeRecord,
    datePicker,
    modalSgaNewAuth
  },
  data () {
    // 产品名称验证
    const validate_sgName = (rule, value, callback) => {
      if (util.format_check_common(value, /^[0-9a-zA-Z\u4e00-\u9fa5]+?$/)) {
        callback(new Error('格式不正确'));
      } else if (util.getLength(value) > 64) {
        callback(new Error('长度不能大于64'));
      } else {
        callback();
      }
    };
      // 产品编码验证
    const validate_sgCode = (rule, value, callback) => {
      if (util.format_check_common(value, /^[0-9a-zA-Z_]+?$/)) {
        callback(new Error('格式不正确'));
      } else
      if (util.getLength(value) > 64) {
        callback(new Error('长度不能大于64'));
      } else {
        callback();

        // // 添加重复校验接口
        // if(this.create_orEdit){
        //     api.yop_service_group_check_exist({
        //         code : value
        //     }).then(
        //         (response) =>{
        //             if(response.data.status === 'success'){
        //                 let result = response.data.data.result
        //                 if(result){
        //                     callback(new Error('该服务分组编码已存在'));
        //                 }else{
        //                     callback();
        //                 }
        //             }else{
        //                 callback(new Error('验重失败'))
        //             }
        //         }
        //     )
        // }else{
        //     callback();
        // }
      }
    };
    return {

      // new
      product_code: '',
      cause: '',
      deleteProduct: false,
      normalProduct: false,
      offlineProduct: false,
      delete_or_normal_modal_show: false,
      serviceSupplier: '',
      productType: '',
      // 新增产品
      modal_add_product: false,
      // 是否是编辑
      create_orEdit: true,
      form_detail: {
        productType: '',
        product_name_default: '',
        type: '',
        serviceGroupName: '',
        authType: '',
        description: '',
        spCode: '',
        icon: null
      },
      rule_detail: {
        product_code_default: [
          {required: true, message: '产品编码不能为空', trigger: 'blur'}, {validator: validate_sgCode, trigger: 'blur'}
        ],
        product_name_default: [
          {required: true, message: '产品名称不能为空', trigger: 'blur'}, {validator: validate_sgName, trigger: 'blur'}
        ],
        productType: [
          {required: true, message: '产品类型不能为空'}
        ],
        spCode: [
          {required: true, message: '服务提供方不能为空'}
        ],
        description: [
          // {validator: validate_resourceDes, trigger : 'blur'}
        ]
      },
      // 是否是顶级资源
      top_resource: false,
      // 授权类型列表
      type_list: [],
      // 列表loading
      show_loading_serviceAuthList: false,
      // 产品编码输入框数据绑定
      data_product_code: '',
      data_product_name: '',
      data_product_apiUri: '',
      data_product_type: '',
      service_provision_code: '',
      // 授权状态下拉框数据绑定
      data_select_status: '',
      // 授权有效期数据绑定
      data_select_type: '',
      data_select_provider: '',
      // 下拉框个数
      count_select_related: 2,
      // 总数
      pageTotal: 10,
      // 当前页码
      pageNo: 1,
      // 服务授权列表表头
      columns_productCodeList: [
        {
          title: '产品名称',
          render: (h, params) => {
            if (params.row.name) {
              return h('div', [
                h('p', params.row.name),
                h('p', '(' + params.row.code + ')')
              ]);
            } else {
              return h('div', params.row.code);
            }
          },
          align: 'center'
        },
        {
          title: '产品类型',
          key: 'typeName',
          render: (h, params) => {
            if (params.row.typeName) {
              return h('p', params.row.typeName)
            } else {
              return h('p', '---')
            }
          },
          align: 'center'
        },
        {
          title: '服务提供方',
          render: (h, params) => {
            if (params.row.spCode) {
              return h('div', [
                h('p', params.row.spName),
                h('p', '(' + params.row.spCode + ')')
              ]);
            }
          },
          align: 'center'
        },
        // {
        //   title: "授权有效期",
        //   width: 130,
        //   align: "center",
        //   render: (h, params) => {
        //     if (
        //       params.row.authTimeStart === "" &&
        //       params.row.authTimeEnd === ""
        //     ) {
        //       return h("div", "永久有效");
        //     } else {
        //       return h("div", [
        //         h("p", util.empty_handler(params.row.authTimeStart)),
        //         h("p", util.empty_handler(params.row.authTimeEnd))
        //       ]);
        //     }
        //   }
        // },
        {
          title: '状态',
          render: (h, params) => {
            let color = 'red';
            let status = '已授权';
            if (params.row.status === 'NORMAL') {
              color = 'green';
              status = '正常';
            } else if (params.row.status === 'OFFLINE') {
              color = 'grey';
              status = '已下线';
            }
            //  else {
            //   color = "red";
            //   status = "已过期";
            // }
            return h('div', [
              h(
                'Tag',
                {
                  style: {
                    align: 'center'
                  },
                  props: {
                    color: color
                  }
                },
                status
              )
            ]);
          },
          width: 120,
          align: 'center'
        },
        {
          renderHeader: (h, params) => {
            return h('div', [h('p', '创建时间'), h('p', '最后修改时间')]);
          },
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('p', params.row.createdDate),
              h('p', params.row.lastModifiedDate)
            ]);
          }
        },
        {
          title: '操作',
          align: 'center',
          key: 'operations',
          render: (h, params) => {
            if (params.row.status === 'OFFLINE') {
              return h('div', [
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [{
                      name: 'url',
                      value: {url: '/rest/product/api/list'}
                    }],
                    on: {
                      click: () => {
                        this.linkApi(params.row);
                      }
                    }
                  },
                  '关联API'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [
                      {
                        name: 'show',
                        value: params.row.status === 'AUTHORIZED'
                      }
                    ],
                    // directives: [{
                    //     name: 'url',
                    //     value: {url: '/rest/backend-app/delete'}
                    // }],
                    on: {
                      click: () => {
                        this.add_product_documentation(params.row.id);
                      }
                    }
                  },
                  '产品文档'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [{
                      name: 'url',
                      value: {url: '/rest/product/edit'}
                    }],
                    // directives: [
                    //   {
                    //     name: "show",
                    //     value: params.row.status === "CANCELLED"
                    //   }
                    // ],
                    on: {
                      click: () => {
                        this.product_edit(params.row.code);
                      }
                    }
                  },
                  '编辑'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [{
                      name: 'url',
                      value: {url: '/rest/product/delete'}
                    }],
                    on: {
                      click: () => {
                        this.delete_product(params.row.code);
                      }
                    }
                  },
                  '删除'
                ),

                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [{
                      name: 'url',
                      value: {url: '/rest/product/normal'}
                    }],
                    on: {
                      click: () => {
                        this.change_if_normal(params.row.code);
                      }
                    }
                  },
                  '恢复'
                )
                // h(
                //   "Button",
                //   {
                //     props: {
                //       type: "text",
                //       size: "small"
                //     },
                //     // directives: [{
                //     //     name: 'url',
                //     //     value: {url: '/rest/backend-app/delete'}
                //     // }],
                //     // directives: [
                //     //   {
                //     //     name: "show",
                //     //     value: params.row.status === "CANCELLED"
                //     //   }
                //     // ],
                //     on: {
                //       click: () => {
                //         this.get_product_recode(params.row.id);
                //       }
                //     }
                //   },
                //   "变更记录"
                // )
              ]);
            } else {
              return h('div', [
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [{
                      name: 'url',
                      value: {url: '/rest/product/api/list'}
                    }],
                    on: {
                      click: () => {
                        this.linkApi(params.row);
                      }
                    }
                  },
                  '关联API'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [
                      {
                        name: 'show',
                        value: params.row.status === 'AUTHORIZED'
                      }
                    ],
                    // directives: [{
                    //     name: 'url',
                    //     value: {url: '/rest/backend-app/delete'}
                    // }],
                    on: {
                      click: () => {
                        this.add_product_documentation(params.row.id);
                      }
                    }
                  },
                  '产品文档'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [{
                      name: 'url',
                      value: {url: '/rest/product/edit'}
                    }],
                    // directives: [
                    //   {
                    //     name: "show",
                    //     value: params.row.status === "CANCELLED"
                    //   }
                    // ],
                    on: {
                      click: () => {
                        this.product_edit(params.row.code);
                      }
                    }
                  },
                  '编辑'
                ),

                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [{
                      name: 'url',
                      value: {url: '/rest/product/offline'}
                    }],
                    on: {
                      click: () => {
                        this.change_if_offline(params.row.code);
                      }
                    }
                  },
                  '下线'
                )
                // h(
                //   "Button",
                //   {
                //     props: {
                //       type: "text",
                //       size: "small"
                //     },
                //     // directives: [{
                //     //     name: 'url',
                //     //     value: {url: '/rest/backend-app/delete'}
                //     // }],
                //     // directives: [
                //     //   {
                //     //     name: "show",
                //     //     value: params.row.status === "CANCELLED"
                //     //   }
                //     // ],
                //     on: {
                //       click: () => {
                //         this.get_product_recode(params.row.id);
                //       }
                //     }
                //   },
                //   "变更记录"
                // )
              ]);
            }
          }
        }
      ],
      // 服务授权数据
      data_productCodeList: [
      //           {
      //       "code" : "产品编码",
      //       "name" : "产品名称",
      //       "type" : "产品类型",
      //       "providerCode" : "服务提供方编码",
      //       "status" : "NORMAL",
      //   /*
      //   NORMAL 正常
      //   OFFLINE 已下线
      //   */
      //       "createdDate" : "2018-05-31 15:46:16",
      //       "lastModifiedDate" : "2018-05-31 15:46:16"
      // }
      ],
      // 开始日期绑定
      dateStart: '',
      // 结束日期绑定
      dateEnd: '',
      data_interface_uri: '',
      search_Interface: '',
      productCode: ''
    };
  },
  methods: {
    // 更新同步
    update_product_list () {
      this.$Modal.confirm({
        title: '',
        content: '<p>部分同步成功！</p><p>同步失败产品码总数：XX条；失败前5条如下所示</p><p>同步失败产品码总数：XX条qdqw下所示</p><p>同步失败产品码总数：XX条qdqw下所示</p><p>同步失败产品码总数：XX条qdqw下所示</p><p>同步失败产品码总数：XX条qdqw下所示</p>',
        okText: '确定',
        cancelText: '复制',
        onOk: () => {
          console.log(1)
        },
        onCancel: () => {
          console.log(2)
        }
      });
    },
    product_change_record () {
      this.$refs.change_record.show_model();
      this.$refs.change_record.search_Interface(false);
      this.$refs.change_record.setProductCode();
      this.$refs.change_record.reset_Interface()
    },
    // refresh_sp(){
    //     this.$refs.select_sp_m.updateList();
    // },
    // 开始日期更新
    update_start_date (val) {
      this.dateStart = val;
    },
    // 结束日期更新
    update_end_date (val) {
      this.dateEnd = val;
    },
    // 产品列表查询函数
    search_interface (val) {
      console.log(val)
      this.show_loading_serviceAuthList = true;
      let params = {
        code: this.data_product_code.trim(),
        name: this.data_product_name.trim(),
        type: this.data_select_type || '',
        spCode: this.data_select_provider || '',
        status: this.data_select_status || '',
        apiUri: this.data_product_apiUri,
        createdStartDate: util.dateFormat_component(this.dateStart),
        createdEndDate: util.dateFormat_component_end(this.dateEnd),

        _pageNo: 1
        // _pageSize: 10
      };
      if (val) {
        params._pageNo = val;
      }
      util.paramFormat(params);
      api.yop_product_list(params).then(response => {
        if (response.data.status === 'success') {
          this.tableDataFormat(response.data.data.page.items);
          this.pageNo = response.data.data.page.pageNo;
          if (
            response.data.data.page.items &&
            response.data.data.page.items.length > 0
          ) {
            if (response.data.data.page.items.length < 10) {
              this.pageTotal = response.data.data.page.items.length;
            } else {
              this.pageTotal = NaN;
            }
          } else {
            this.pageTotal = NaN;
          }
        } else {
          this.$ypMsg.notice_error(
            this,
            '列表获取错误',
            response.data.message,
            response.data.solution
          );
        }
        this.show_loading_serviceAuthList = false;
      });
    },
    // 列表数据处理
    tableDataFormat (items) {
      this.data_productCodeList = [];
      for (var i in items) {
        this.data_productCodeList.push({
          code: util.empty_handler2(items[i].code),
          name: util.empty_handler(items[i].name),
          providerCode: util.empty_handler2(items[i].providerCode),
          status: util.empty_handler2(items[i].status),
          type: util.empty_handler2(items[i].type),
          typeName: util.empty_handler2(items[i].typeName),
          spCode: util.empty_handler2(items[i].spCode),
          spName: util.empty_handler2(items[i].spName),
          createdDate: util.empty_handler2(items[i].createdDate),
          lastModifiedDate: util.empty_handler(items[i].lastModifiedDate)
        });
      }
    },
    // 重置查询添加函数
    reset_Interface () {
      this.data_product_code = '';
      this.data_product_name = '';
      this.data_product_apiUri = '';
      this.dateStart = '';
      this.dateEnd = '';
      this.$refs.select_provider.resetSelected();
      this.$refs.select_status.resetSelected();
      this.$refs.product_type.resetSelected();
      // select_provider
    },
    // 新增产品函数
    product_add () {
      this.create_orEdit = true
      // this.$refs.modal_sga_new_auth.preview_show();
      // this.$refs.modal_sga_new_auth.init();
      // this.$refs.modal_sga_new_auth.date_picker_init();
      // if(row.id){
      this.form_detail.product_name_default = ''
      this.form_detail.product_code_default = ''
      this.form_detail.description = ''
      this.form_detail.icon = null
      this.$refs.select_sp_m.resetSelected();
      this.$refs.select_sp_code.resetSelected();
      this.$refs.form_detail.resetFields();

      // }
      this.modal_add_product = true
    },
    select_callBack_type (resultTemp) {
      if (resultTemp) {
        this.form_detail.productType = resultTemp.type;
        this.data_product_type = resultTemp.type;
        console.log(this.data_product_type, 'kkkkk')
      }
    },
    select_callBack_service (resultTemp) {
      if (resultTemp) {
        this.form_detail.spCode = resultTemp.spCode;
        this.serviceSupplier = resultTemp.spCode;
        console.log(this.serviceSupplier, 'lllll')
      }
    },
    product_edit (code) {
      this.create_orEdit = false
      this.form_detail.icon = null
      this.$refs.form_detail.resetFields();
      api.yop_product_detail({code: code}).then(response => {
        let resultTemp = response.data.data.result;
        this.form_detail.product_name_default = resultTemp.name
        this.form_detail.product_code_default = resultTemp.code
        this.form_detail.description = resultTemp.desc
        this.select_callBack_type(resultTemp)
        this.select_callBack_service(resultTemp)
      });
      this.modal_add_product = true
    },
    // 下拉框加载完处理函数
    select_callBack () {
      this.count_select_related--;
      if (this.count_select_related === 0) {
        this.search_interface();
      }
    },
    // 授权状态数据更新函数
    updateSelect_status (val) {
      this.data_select_status = val;
    },
    // 产品类型更新函数
    updateSelect_type (val) {
      this.data_select_type = val;
    },
    // 授权有效期数据更新函数
    updateSelect_provider (val) {
      this.data_select_provider = val;
    },
    // 页面刷新
    pageRefresh (val) {
      this.search_interface(val)
    },
    // 初始化页面
    init () {
      this.search_interface();
    },
    // 数据类型获取
    type_list_get () {
      api.yop_service_group_auth_type().then(response => {
        let resultTemp = response.data.data.result;
        this.type_list = [];
        let dataTemp = [];
        if (resultTemp && resultTemp.length > 0) {
          resultTemp.forEach(item => {
            dataTemp.push({
              label: item.name,
              name: item.code
            });
          });
        }
        this.type_list = dataTemp;
      });
    },

    // 产品
    // 关联API
    linkApi (row) {
      this.$refs.card_edit_sg.model_show = true;
      this.$refs.card_edit_sg.get_scene(row.code);
      this.$refs.card_edit_sg.setProductCode(row.code);
      this.$refs.card_edit_sg.search_Interface(1);
    },
    add_close () {
      this.modal_add_product = false;
    },
    // 新增，编辑产品
    add_submit (val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let param = {}
          param = this.param_add_product_handler();
          this.add_product_handler(param);
        } else {
          this.$Message.error('请检查');
        }
      })
    },
    // 新增产品参数处理函数
    param_add_product_handler () {
      let param = {
        code: this.form_detail.product_code_default,
        spCode: this.form_detail.spCode,
        name: this.form_detail.product_name_default,
        type: this.form_detail.productType,
        desc: this.form_detail.description,
        icon: this.form_detail.icon,
        cause: '运营后台创建产品码'
      }
      const formData = new FormData()
      Object.keys(param).forEach(key => {
        if(key === 'icon' && !param[key])return
        formData.append(key, param[key])
      })
      return formData
    },
    // 新增、编辑产品调用接口
    add_product_handler (param) {
      console.log('调用ajax')
      var yopFunc = ''
      if (this.create_orEdit) {
        yopFunc = 'yop_product_create'
      } else {
        yopFunc = 'yop_product_edit'
      }
      // 如果此按钮需要审核则执行
      if (util.checkAudit('/rest/product/create') || util.checkAudit('/rest/product/edit')) {
        this.modal_add_product = false;
      }
      api[yopFunc](param).then(response => {
        if (response.status === 'success') {
          if (localStorage.needAudit === 'true') {
            this.$ypMsg.notice_info(this, response.message);
          } else {
            this.$ypMsg.notice_success(this, '成功');
          }
          this.modal_add_product = false
        } else {
          this.$ypMsg.notice_error(
            this,
            '错误',
            response.message,
            response.solution
          );
        }
        this.search_interface(false);
      });
    },
    // 服务提供方页面跳转
    sp_jump () {
      const {href} = this.$router.resolve({
        name: '/isp/list'
      });
      window.open(href, '_blank');
    },
    // 产品类型
    updateSelect_productType_model (val) {
      this.form_detail.productType = val;
      this.data_product_type = val
    },
    // 弹窗服务提供方数据更新
    updateSelect_sp_model (val) {
      this.serviceSupplier = val;
      this.form_detail.spCode = val
    },
    delete_product (code) {
      this.productCode = code
      this.deleteProduct = true
      this.offlineProduct = false
      this.normalProduct = false

      this.delete_or_normal_modal_show = true
      this.cause = ''
    },
    // 下线
    change_if_offline (code) {
      this.productCode = code
      this.offlineProduct = true
      this.deleteProduct = false
      this.normalProduct = false
      this.delete_or_normal_modal_show = true
      this.cause = ''
    },
    // 恢复
    change_if_normal (code) {
      this.productCode = code
      this.normalProduct = true
      this.deleteProduct = false
      this.offlineProduct = false
      this.delete_or_normal_modal_show = true
      this.cause = ''
    },

    // 新增产品文档
    add_product_documentation () {
      // $router.push()
    },
    // 产品变更记录
    get_product_recode (id) {
      let params = {
        // code:this.operatorCode,
        // type:this.operatorType,
        // operator:this.operator,
        // createdStartDate:this.operatorDateStart,
        // createdEndDate:this.operatorDateEnd,
        // _pageNo:this._pageNo,
      }
      util.paramFormat(params)
      api.yop_product_change().then(response => {
        if (response.status === 'success') {
          this.$ypMsg.notice_success(this, '取消授权成功');
        } else {
          this.$ypMsg.notice_error(
            this,
            '取消授权错误',
            response.message,
            response.solution
          );
        }
        this.search_interface(false);
      });
    },
    update_serviceAuthList (code, name) {
      this.reset_Interface();
      this.data_product_code = code;
      this.data_product_name = name;
      this.search_interface();
    },
    sure_model () {
      var yop_function = ''
      var title = ''
      if (this.deleteProduct) {
        yop_function = 'yop_product_delete'
        title = '删除'
      } else if (this.normalProduct) {
        yop_function = 'yop_product_normal'
        title = '恢复'
      } else if (this.offlineProduct) {
        yop_function = 'yop_product_offline'
        title = '下线'
      }
      if (this.cause) {
        var param = {
          code: this.productCode,
          cause: this.cause
        }
        if (this.deleteProduct) {
          var param = {
            code: this.productCode,
            cause: this.cause,
            force: false
          }
        }
        console.log(param, title)
        util.paramFormat(param);
        // 如果此按钮需要审核则执行
        if (util.checkAudit('/rest/product/offline') || util.checkAudit('/rest/product/normal') || util.checkAudit('/rest/product/delete')) {
          this.delete_or_normal_modal_show = false;
        }
        api[yop_function](param).then(
          (response) => {
            if (response.status === 'success') {
              this.$ypMsg.notice_success(this, '成功');
              this.search_interface()
              // this.tabledataGet(response.data.page.items);
              // this.pageNo = response.data.result.pageNo;
              // this.pageTotal = response.data.result.totalPageNum * 10;
              this.delete_or_normal_modal_show = false;
            } else {
              this.$Modal.error({
                title: '错误',
                content: response.message
              });
              this.delete_or_normal_modal_show = false;
              this.search_interface(false);
            }
          }
        );
      } else {
        this.$Modal.warning({
          title: '错误',
          content: '请输入原因'
        });
      }
    },
    hide_model () {
      this.delete_or_normal_modal_show = false;
    }
  },
  mounted () {
    this.init();
  }
};
</script>

<style scoped>
</style>
