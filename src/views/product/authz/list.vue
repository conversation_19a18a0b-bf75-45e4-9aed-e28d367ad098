<style lang="less">
@import "../../../styles/common.less";
@import "../../API_Management_Open/api_Mangement_Open.less";
.demo-badge-alone {
	background: #5cb85c !important;
}
.round {
	width: 16px;
	height: 16px;
	display: inline-block;
	font-size: 20px;
	line-heigth: 16px;
	text-align: center;
	color: #f00;
	text-decoration: none;
}
</style>
<template>
	<div style="background: #eee;padding:8px">
		<Card :bordered="false">
			<Row type="flex" align="middle">
				<Col span="20">
					<Col span="7">
						<Col span="8" class="margin-top-10">
							<span>商户编码:</span>
						</Col>
						<Col span="16">
							<Input
								id="input_serviceAuth_1"
								class="margin-top-5"
								v-model="data_serviceAuth_code"
								placeholder="商户编码"
								@on-enter="search_interface(false)"
							></Input>
						</Col>
					</Col>

					<Col offset="1" span="7">
						<Col span="8" class="margin-top-10">
							<span>应用标识:</span>
						</Col>
						<Col span="16">
							<Input
								id="input_serviceAuth_2"
								class="margin-top-5"
								v-model="data_serviceAuth_appKey"
								placeholder="应用标识"
								@on-enter="search_interface(false)"
							></Input>
						</Col>
					</Col>
					<Col offset="1"  span="7">
						<Col span="8" class="margin-top-10">
							<span>授权状态:</span>
						</Col>
						<Col span="16">
							<common-select
								ref="select_status"
								@on-update="updateSelect_status"
								type="normal"
								keyWord="result"
								holder="请选择（默认全部）"
								code="code"
								title="name"
								group="service_authz_status"
								@on-loaded="select_callBack"
								:default="this.data_select_status"
                :uri="this.$store.state.select.service_authz_status.uri"
							></common-select>
						</Col>
					</Col>

					<Col class="margin-top-5" span="7">
						<Col span="8" class="margin-top-10">
							<span>产品编码:</span>
						</Col>
						<Col span="16">
							<Input
								id="input_serviceAuth_3"
								class="margin-top-5"
								v-model="data_serviceAuth_serviceGroup"
								placeholder="产品编码"
								@on-enter="search_interface(false)"
							></Input>
						</Col>
					</Col>
          <Col offset="1" class="margin-top-5"  span="7">
						<Col span="8" class="margin-top-10">
							<span>产品类型:</span>
						</Col>
						<Col span="16">
							<common-select
								ref="select_type"
								@on-update="updateSelect_type"
								type="normal"
								keyWord="result"
								holder="请选择（默认全部）"
								code="code"
								title="name"
								group="productType"
								@on-loaded="select_callBack"
								:default="this.data_select_type"
								uri="/rest/product/commons/type"
							></common-select>
						</Col>
					</Col>
					<Col class="margin-top-5" offset="1" span="7">
						<Col span="8" class="margin-top-10">
							<span>过期时间(天):</span>
						</Col>
						<Col span="16">
							<Input
								id="input_serviceAuth_7"
								class="margin-top-5"
								v-model="overdueTime"
								placeholder="30以内的数字"
								@on-enter="search_interface(false)"
								@on-focus="clear_overdueTime"
							></Input>
						</Col>
					</Col>

					<Col class="margin-top-5" offset="1" span="8"> </Col>

					<Col class="margin-top-5" offset="1" span="7"> </Col>
				</Col>

				<Col span="4">
					<Col class="margin-top-10" span="11" style="text-align:center">
						<Button id="btn_apiM_1" type="primary" @click="search_interface(false)">查询</Button>
					</Col>
					<Col class="margin-top-10" span="11" style="text-align:center">
						<Button id="btn_apiM_2" type="ghost" @click="reset_Interface">重置</Button>
					</Col>
				</Col>
			</Row>
			<Row class="margin-top-20">
				<Col span="24">
					<Button
						class="margin-right-10"
						type="primary"
						v-url="{ url: '/rest/product/authz/batch-auth' }"
						@click="serviceAuth_add"
						>新增产品授权</Button
					>
					<Button
						class="margin-right-10"
						style="background:#20cb9a;border:0;"
						v-url="{ url: '/rest/product/authz/change/list' }"
						type="primary"
						@click="product_change_record"
						>产品授权变更记录</Button
					>
					<!-- <div class="plus" id="plusScene"  @click="plus">+</div>
                <div class="plus" v-show="timeList.length" id="minusTime" style="background:red" @click="minus">-</div> -->
				</Col>
			</Row>
			<Row class="margin-top-10">
				<Col span="24">
					<Table id="table_1" border :columns="columns_serviceAuthList" :data="data_serviceAuthList"></Table>
					<Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
						<Page
							class="margin-top-10"
							style="float: right"
							:total="pageTotal"
							:page-size="10"
							:current="pageNo"
							show-elevator
							@on-change="pageRefresh"
						></Page>
					</Tooltip>
				</Col>
				<loading :show="show_loading_serviceAuthList"></loading>
			</Row>
			<Modal v-model="add_scene_modal_show" width="500" :transfer="false">
				<p slot="header" style="color:#000;text-align:left;font-size: 18px;">
					<Icon type="ios-information-circle"></Icon>
					<span>新增授权时间</span>
				</p>
				<div style="text-align:left;font-size: 12px;">
					<label for="" class="reasonType"><span style="color:red">*</span>自定义过期时间：</label>
					<Input
						type="text"
						v-model="overdueTime"
						style="width:60%;font-size: 12x;"
						placeholder="请输入"
						:maxlength="30"
					></Input
					>天
					<p style="color:#888;font-size:14px;margin-top:10px;margin-left:104px;">最长可输入30字符</p>
				</div>
				<div slot="footer" style="margin-top:-46px">
					<Button type="primary" id="sureTime" @click="sure_model">确定</Button>
					<Button type="ghost" @click="hide_model">取消</Button>
				</div>
			</Modal>
			<modal-change-record ref="change_record"></modal-change-record>
			<modal-sp-preview ref="modal_preview"></modal-sp-preview>
			<modal-sga-expires ref="modal_sga_expand" @update-list="update_serviceAuthList"></modal-sga-expires>
			<modal-sga-new-auth ref="modal_sga_new_auth" @update-list="update_serviceAuthList"></modal-sga-new-auth>
		</Card>
	</div>
</template>

<script>
import loading from "../../my-components/loading/loading";
import commonSelect from "../../common-components/select-components/selectCommon";
import util from "../../../libs/util";
import api from "../../../api/api";
import modalSpPreview from "../../common-components/modal-components/modal-sp-preview";
import modalSgaExpires from "../../common-components/modal-components/modal-sga-expand-expires";
import modalSgaNewAuth from "../../common-components/modal-components/modal-sga-new-auth";
import modalChangeRecord from "./modal-change-record";

export default {
	name: "isp-list",
	components: {
		loading,
		commonSelect,
		modalSpPreview,
		modalSgaExpires,
		modalSgaNewAuth,
		modalChangeRecord,
	},
	data() {
		return {
			activeClass: -1,
			overdueTime: "",
			timeList: [1, 15, 30],
			// 授权类型列表
			type_list: [],
			// 列表loading
			show_loading_serviceAuthList: false,
			// 商户编码输入框数据绑定
			data_serviceAuth_code: "",
			// 应用标识输入框数据绑定
			data_serviceAuth_appKey: "",
			// 服务分组输入框数据绑定
			data_serviceAuth_serviceGroup: "",
			// 授权状态下拉框数据绑定
			data_select_status: "",
			data_select_type: "",
			// 授权有效期数据绑定
			data_select_validty: "",
			// 下拉框个数
			count_select_related: 2,
			// 总数
			pageTotal: 10,
			// 当前页码
			pageNo: 1,
			// 服务授权列表表头
			columns_serviceAuthList: [
				{
					title: "商户编码",
					render: (h, params) => {
						// if(params.row.merchantName){
						//     return h('div', [
						//         h('p', params.row.merchantName),
						//         h('p', '(' + params.row.merchantCode + ')')
						//     ]);
						// }else{
						//     return h('div',  params.row.merchantCode);
						// }
						return h("div", params.row.customerNo);
					},
					align: "center",
				},
				{
					title: "应用标识",
					render: (h, params) => {
						if (util.emptyCheck(params.row.appId)) {
							// if(params.row.appKeyName){
							//     return h('div', [
							//         h('p', params.row.appKeyName),
							//         h('p', '(' + params.row.appKeyCode + ')')
							//     ]);
							// }else{
							//     return h('div', params.row.appKeyCode);
							// }
							return h("div", params.row.appId);
						} else {
							return h("div", "全部");
						}
					},
					align: "center",
				},
				{
					title: "产品名称",
					render: (h, params) => {
						let _this = this;
						return h("div", [h("p", params.row.productName), h("p", "(" + params.row.productCode + ")")]);
					},
					align: "center",
				},
				{
					title: "产品类型",
					render: (h, params) => {
            const item = this.typeList.find(t => t.value === params.row.type)
						return h("div", item.label);
					},
					align: "center",
				},
				{
					title: "授权有效期",
					align: "center",
					width: 200,
					render: (h, params) => {
						if (!params.row.effectiveDate && !params.row.overdueDate) {
							return h("div", "永久有效");
						}
						if (!params.row.effectiveDate && params.row.overdueDate) {
							return h("div", [h("p", "---"), h("p", params.row.overdueDate.split(" ")[0])]);
						}
						if (!params.row.overdueDate && params.row.effectiveDate) {
							return h("div", [h("p", params.row.effectiveDate.split(" ")[0]), h("p", "---")]);
						} else {
							return h("div", [
								h("p", params.row.effectiveDate.split(" ")[0]),
								h("p", params.row.overdueDate.split(" ")[0]),
							]);
						}
					},
				},
				{
					title: "状态",
					render: (h, params) => {
						let color = "red";
						let status = "已授权";
						if (params.row.status === "AUTH") {
							color = "green";
							status = "已授权";
						} else if (params.row.status === "UNAUTH") {
							color = "grey";
							status = "已取消";
						} else if (params.row.status === "OVERDUE") {
							color = "red";
							status = "已过期";
						} else if (params.row.status === "PREAUTH") {
							color = "#fd9827";
							status = "预授权";
						}
						return h("div", [
							h(
								"Tag",
								{
									style: {
										align: "center",
									},
									props: {
										color: color,
									},
								},
								status
							),
						]);
					},
					align: "center",
				},
				{
					renderHeader: (h, params) => {
						return h("div", [h("p", "创建时间"), h("p", "最后修改时间")]);
					},
					align: "center",
					render: (h, params) => {
						return h("div", [h("p", params.row.createdDate), h("p", params.row.lastModifiedDate)]);
					},
				},
				{
					title: "操作",
					align: "center",
					key: "operations",
					"min-width": 150,
					render: (h, params) => {
						if (params.row.status === "PREAUTH") {
							return h("div", [
								h(
									"Button",
									{
										props: {
											type: "text",
											size: "small",
										},
										directives: [
											{
												name: "url",
												value: { url: "/rest/product/authz/confirm" },
											},
										],
										on: {
											click: () => {
												this.sure_auth(params.row);
											},
										},
									},
									"确认授权"
								),
								//    h('Button', {
								//         props: {
								//             type: 'text',
								//             size: 'small'
								//         },
								//         directives: [{
								//             name: 'url',
								//             value: {url: '/rest/product/authz/defer'}
								//         }],
								//         on: {
								//             click: () => {
								//                 this.expandExpires(params.row);
								//             }
								//         }
								//     }, '展期'),
							]);
						} else if (params.row.status === "OVERDUE") {
							return h("div", [
								h(
									"Button",
									{
										props: {
											type: "text",
											size: "small",
										},
										directives: [
											{
												name: "url",
												value: { url: "/rest/product/authz/reauth" },
											},
										],
										on: {
											click: () => {
												this.reAuth(params.row);
											},
										},
									},
									"重新授权"
								),
							]);
						} else if (params.row.status == "AUTH") {
							return h("div", [
								h(
									"Button",
									{
										props: {
											type: "text",
											size: "small",
										},
										directives: [
											{
												name: "url",
												value: { url: "/rest/product/authz/defer" },
											},
										],
										on: {
											click: () => {
												this.expandExpires(params.row);
											},
										},
									},
									"展期"
								),
								h(
									"Button",
									{
										props: {
											type: "text",
											size: "small",
										},
										directives: [
											{
												name: "url",
												value: { url: "/rest/product/authz/unauth" },
											},
										],
										on: {
											click: () => {
												this.cancel_auth(params.row.id);
											},
										},
									},
									"取消授权"
								),
							]);
						} else if (params.row.status == "UNAUTH") {
							return h("div", [
								// h('Button', {
								//     props: {
								//         type: 'text',
								//         size: 'small'
								//     },
								//     directives: [{
								//         name: 'url',
								//         value: {url: '/rest/product/authz/defer'}
								//     }],
								//     on: {
								//         click: () => {
								//             this.expandExpires(params.row);
								//         }
								//     }
								// }, '展期'),
								h(
									"Button",
									{
										props: {
											type: "text",
											size: "small",
										},
										directives: [
											{
												name: "url",
												value: { url: "/rest/product/authz/recover" },
											},
										],
										on: {
											click: () => {
												this.recover_auth(params.row.id);
											},
										},
									},
									"恢复授权"
								),
							]);
						}
					},
				},
			],
			// 服务授权数据
			data_serviceAuthList: [],
			add_scene_modal_show: false,
		};
  },
  computed: {
    typeList() {
      return this.$store.state.select.productType.data
    }
  },
	methods: {
		chooseTime(value, index) {
			console.log(this.overdueTime);
			if (this.overdueTime == value) {
				this.activeClass = -1;
				this.overdueTime = "";
			} else {
				this.overdueTime = value;
				this.activeClass = index;
			}
			this.search_interface(false);
		},
		clear_overdueTime() {
			this.activeClass = -1;
		},
		minus() {
			console.log(this.activeClass, 1);

			if (this.activeClass == -1) {
				this.$ypMsg.notice_warning(this, "请选择删除时间");
				return;
			} else {
				var self = this;
				this.$Modal.confirm({
					title: "删除",
					content: "<p>确定删除？</p>",
					okText: "确定",
					cancelText: "取消",
					onOk: () => {
						this.overdueTime = "";
						this.timeList.splice(this.activeClass, 1);
						this.activeClass = -1;
						this.$forceUpdate();
						this.search_interface(false);
					},
					onCancel: () => {},
				});
			}
		},
		handleDel(item, index) {},
		sure_model() {
			if (isNaN(this.overdueTime)) {
				this.overdueTime = this.overdueTime.replace(/[^\d]/g, "");
				this.$ypMsg.notice_warning(this, "请输入数字");
				return false;
			}
			this.timeList.push(this.overdueTime);
			this.add_scene_modal_show = false;
			this.$forceUpdate();
			// var param = {
			//     productCode:this.productCode,
			//     name:this.sceneName
			// }
			// api.yop_product_scene_create(param).then(
			//     (response) => {
			//     console.log(response,"添加场景")
			//         if(response.status == "success"){
			//             this.$ypMsg.notice_success(this,'添加成功');
			//             console.log(this.productCode)
			//             this.get_scene(this.productCode);
			//             this.add_scene_modal_show = false
			//         }else{
			//             this.$ypMsg.notice_warning(this,'添加失败');
			//         }
			//     }
			// )
		},
		hide_model() {
			this.add_scene_modal_show = false;
		},
		plus() {
			this.add_scene_modal_show = true;
			this.overdueTime = "";
		},
		product_change_record() {
			this.$refs.change_record.show_model();
			this.$refs.change_record.search_Interface(false);
			this.$refs.change_record.setProductCode();
			this.$refs.change_record.reset_Interface();
		},
		// 服务授权列表查询函数
		search_interface(val) {
			this.show_loading_serviceAuthList = true;
			let params = {
				customerNo: this.data_serviceAuth_code,
				appId: this.data_serviceAuth_appKey,
				productCode: this.data_serviceAuth_serviceGroup,
				status: this.data_select_status,
				type: this.data_select_type,
				overdueTime: this.overdueTime,
				_pageNo: 1,
			};
			if (val) {
				params._pageNo = val;
			}
			util.paramFormat(params);
			api.yop_product_authz_list(params).then((response) => {
				if (response.data.status === "success") {
					this.tableDataFormat(response.data.data.page.items);
					this.pageNo = response.data.data.page.pageNo;
					if (response.data.data.page.items && response.data.data.page.items.length > 0) {
						if (response.data.data.page.items.length < 10) {
							this.pageTotal = response.data.data.page.items.length;
						} else {
							this.pageTotal = NaN;
						}
					} else {
						this.pageTotal = NaN;
					}
				} else {
					this.$ypMsg.notice_error(this, "服务分组授权获取错误", response.data.message, response.data.solution);
				}
				this.show_loading_serviceAuthList = false;
			});
		},
		// 列表数据处理
		tableDataFormat(items) {
			this.data_serviceAuthList = [];
			for (var i in items) {
				this.data_serviceAuthList.push({
					id: util.empty_handler2(items[i].id),
					customerNo: util.empty_handler2(items[i].customerNo),
					appId: util.empty_handler2(items[i].appId),
					productName: util.empty_handler2(items[i].productName),
					productCode: util.empty_handler2(items[i].productCode),
					effectiveDate: util.empty_handler2(items[i].effectiveDate),
					overdueDate: util.empty_handler2(items[i].overdueDate),
					createdDate: util.empty_handler2(items[i].createdDate),
					lastModifiedDate: util.empty_handler2(items[i].lastModifiedDate),
					type: util.empty_handler2(items[i].type),

					// merchantCode: util.empty_handler(items[i].customerNo),
					// merchantName: util.empty_handler2(items[i].customerName),
					// appKeyCode : util.empty_handler2(items[i].appKey),
					// appKeyName : util.empty_handler2(items[i].appName),
					// serviceGroupCode  : util.empty_handler(items[i].code),
					// serviceGroupName : util.empty_handler(items[i].name),
					// authTimeStart : util.empty_handler2(items[i].startDate),
					// authTimeEnd : util.empty_handler2(items[i].endDate),
					status: util.empty_handler(items[i].status),
					// createTime : util.empty_handler(items[i].createDate),
					// lastModifyTime : util.empty_handler(items[i].lastModifiedDate)
				});
			}
		},
		// 重置查询添加函数
		reset_Interface() {
			this.data_serviceAuth_code = "";
			this.data_serviceAuth_appKey = "";
			this.data_serviceAuth_serviceGroup = "";
			// this.$refs.select_validty.resetSelected();
			this.$refs.select_status.resetSelected();
			this.$refs.select_type.resetSelected();
			this.overdueTime = "";
			this.activeClass = -1;
		},
		// 新增服务授权函数
		serviceAuth_add() {
			this.$router.push({
				name: "product/authz/batch-auth",
				// params: argu
			});
			// this.$refs.modal_sga_new_auth.preview_show();
			// this.$refs.modal_sga_new_auth.init();
			// this.$refs.modal_sga_new_auth.date_picker_init();
			// if(this.data_serviceAuth_code){
			//     this.$refs.modal_sga_new_auth.customer_no_set(this.data_serviceAuth_code);
			// }
		},
		// 下拉框加载完处理函数
		select_callBack() {
			this.count_select_related--;
			if (this.count_select_related === 0) {
				this.search_interface();
			}
		},
		// 授权状态数据更新函数
		updateSelect_status(val) {
			this.data_select_status = val;
		},
		// 授权状态数据更新函数
		updateSelect_type(val) {
			this.data_select_type = val;
		},
		// 授权有效期数据更新函数
		updateSelect_validty(val) {
			this.data_select_validty = val;
		},
		// 页面刷新
		pageRefresh(val) {
			this.search_interface(val);
		},
		// 初始化页面
		init() {
			// this.type_list_get();
			this.search_interface();
		},
		// 数据类型获取
		type_list_get() {
			api.yop_service_group_auth_type().then((response) => {
				let resultTemp = response.data.data.result;
				this.type_list = [];
				let dataTemp = [];
				if (resultTemp && resultTemp.length > 0) {
					resultTemp.forEach((item) => {
						dataTemp.push({
							label: item.name,
							name: item.code,
						});
					});
				}
				this.type_list = dataTemp;
			});
		},
		// 服务编码点击
		serviceGroupCode_click(code) {
			this.$refs.modal_preview.manage_show();
			this.$refs.modal_preview.tab_init();
			this.$refs.modal_preview.preview_show(false);
			this.$refs.modal_preview.detail_info_get(code, this.type_list);
		},
		// 重新授权
		reAuth(data) {
			this.$refs.modal_sga_expand.current_func = false;
			this.expandExpires_init(data);
			// 授权
			// api.yop_service_authz_create(param).then(
			//     (response) =>{
			//         if (response.status === 'success') {
			//             if(response.data.result === 'success'){
			//                 this.$Notice.success({
			//                     title : '成功',
			//                     desc : '删除成功',
			//                     duration : 10
			//                 });
			//                 this.remove(node, data);
			//                 this.$refs.card_edit_sg.preview_cancel();
			//             }else{
			//                 // 请求
			//                 setTimeout (() =>{
			//                     let paramTemp = {
			//                         codes : [data.code],
			//                         force : true
			//                     }
			//                     this.delete_admit(node,data,paramTemp);
			//                 },500);
			//             }
			//         } else {
			//             this.$Notice.error({
			//                 title : '删除错误',
			//                 desc : response.message,
			//                 duration : 10
			//             });
			//         }
			//     }
			// );
		},
		// 展期
		expandExpires(data) {
			this.$refs.modal_sga_expand.current_func = true;
			this.expandExpires_init(data);
		},
		// 初始化 展期/重新授权 界面
		expandExpires_init(data) {
			console.log(data, 111111);
			this.$refs.modal_sga_expand.set_current_id(data.id);
			this.$refs.modal_sga_expand.preview_show();
			if (!data.overdueDate && !data.effectiveDate) {
				this.$refs.modal_sga_expand.reset();
			} else {
				this.$refs.modal_sga_expand.date_picker_init(data.effectiveDate, data.overdueDate);
			}
		},
		// 恢复授权
		recover_auth(id) {
			this.$Modal.confirm({
				title: "恢复授权",
				content: "<p style='color:red'>恢复授权后，商户可以调用该产品码的API接口！</p>" + "<p>确定恢复授权？</p>",
				"ok-text": "确定",
				onOk: () => {
					api.yop_product_authz_recover({ id: id, cause: "运营后台人工恢复授权" }).then((response) => {
						if (response.status === "success") {
							this.$ypMsg.notice_success(this, "恢复授权成功");
						} else {
							this.$ypMsg.notice_error(this, "错误", response.message, response.solution);
						}
						this.search_interface(false);
					});
				},
			});
		},

		sure_auth(row) {
			this.$Modal.confirm({
				title: "确认授权",
				content: "<p style='color:red'>确认授权后，商户可以调用授权产品码的API接口！</p>" + "<p>确定授权？</p>",
				"ok-text": "确定",
				onOk: () => {
					api.yop_product_authz_confirm({ id: row.id, cause: "运营后台人工确认授权产品码" }).then((response) => {
						if (response.status === "success") {
							this.$ypMsg.notice_success(this, "授权成功");
						} else {
							this.$ypMsg.notice_error(this, "授权错误", response.message, response.solution);
						}
						this.search_interface(false);
					});
				},
			});
		},

		// 取消授权
		cancel_auth(id) {
			this.$Modal.confirm({
				title: "取消授权",
				content: "<p style='color:red'>取消授权后，商户无法调用该产品码的API接口！</p><p>确定取消授权？</p>",
				"ok-text": "确认",
				onOk: () => {
					// 删除操作
					api.yop_product_authz_unauth({ id: id, cause: "运营后台人工取消授权产品码" }).then((response) => {
						if (response.status === "success") {
							this.$ypMsg.notice_success(this, "取消授权成功");
						} else {
							this.$ypMsg.notice_error(this, "取消授权错误", response.message, response.solution);
						}
						this.search_interface(false);
					});
				},
			});
		},
		update_serviceAuthList(custom, appKey) {
			this.reset_Interface();
			this.data_serviceAuth_code = custom;
			this.data_serviceAuth_appKey = appKey;
			this.search_interface();
		},
	},
	mounted() {
		this.init();
	},
};
</script>

<style scoped>
.plus {
	border: 1px solid #64be6f;
	cursor: pointer;
	color: #fff;
	border-radius: 4px;
	background: #64be6f;
	vertical-align: middle;
	margin-top: -2px;
	width: 25px;
	text-align: center;
	display: inline-block;
}
.timeItem {
	position: relative;
	display: inline-block;
	min-width: 30%;
	height: 30px;
	line-height: 30px;
	text-align: center;
	border: 1px solid #64be6f;
	vertical-align: middle;
	cursor: pointer;
	border-radius: 4px;
	margin: 0 1%;
	background: #eee;
	margin-top: 7px;
}
.timeItem i {
	position: absolute;
	top: 8px;
	right: 3px;
}
.actived {
	background: #64be6f;
	color: #fff;
	font-weight: bolder;
}
</style>
