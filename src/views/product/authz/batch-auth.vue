<style scoped>
    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;
    }

    .el-checkbox__inner {
        width: 12px !important;
        height: 12px !important;
    }

    .custom-tree-node {
        font-size: 12px;
    }

    .yop-explain-120 {
        font-size: 12px;
        color: grey;
        padding-left: 120px;
        line-height: 30px;
    }
    .red{
        color:red
    }
    .green{
        color:green
    }
    /*@import '../API_Management_Open/api_Mangement_Open.less';*/
    @import '../../../styles/common.less';
</style>
<template>
    <div>
        <div style="background: #eee;padding:8px">
           
            <Card :bordered="false">
                <Row type="flex" align="middle">
                    
                    <Col span="24">
                        
                        <div>
                            <p slot="header" style="color:#2d8cf0;height:34px;border-bottom:1px solid #e9eaec;margin-bottom:20px;">
                                <span style="color:black">新增产品授权</span>
                            </p>
                            <Col span="16">
                                <Form ref="form_detail" :model="form_detail" :rules="rule_detail" :label-width="150">
                                    <FormItem label="商户编码：" prop="code">
                                        <Input type="text" size="small" v-model="form_detail.code" @on-blur="customerNo_blur" @on-focus="customerNo_focus"
                                            style="width:85%"></Input>
                                    </FormItem>
                                    <FormItem label="应用标识：">
                                        <common-select ref="select_appKey_m" @on-update="updateSelect_appKey_model"
                                                    type="combo"
                                                    keyWord="result"
                                                    holder="请选择应用标识（默认全部）"
                                                    code="appId"
                                                    title="name"
                                                    size="small"
                                                    group="app_authz"
                                                    @on-loaded="select_callBack"
                                                    :default="this.form_detail.appId"
                                                    :uri="this.$store.state.select.app_authz.uri"
                                                    style="width:85%;margin-top:0;"></common-select>
                                        <span><Button type="primary"  v-url="{url:'/rest/app/create'}" size="small" @click="addApp()">新增应用</Button></span>
                                    </FormItem>
                                    <FormItem label="产品名称：">
                                        <label slot="label"> <span style="color:red"> * </span>产品名称</label>
                                        <Card dis-hover style="width:85%">
                                            <Scroll height="250">
                                            <Input size="small" class="margin-bottom-10" placeholder="产品名称搜索"
                                                v-model="filterText"></Input>
                                            <el-tree
                                                    draggable
                                                    ref="tree_sg"
                                                    :data="data_tree"
                                                    show-checkbox
                                                    node-key="code"
                                                    :default-expand-all="false"
                                                    :default-checked-keys="midPwList"
                                                    :filter-node-method="filterNode"
                                                    :expand-on-click-node="false"
                                                    @check-change="handleCheckChange"
                                            >
                                                <span class="custom-tree-node" slot-scope="{ node, data }">
                                                <span><Button v-show="node.data.enableStatus && node.data.depth!==1 " style="color: green" type="text" size="small" icon="ios-albums-outline"></Button>
                                                <Button v-show="!node.data.enableStatus && node.data.depth!==1" style="color: red" type="text" size="small" icon="ios-albums-outline"></Button>
                                                    <Button v-show="node.data.depth ===1" style="color: grey" type="text" size="small" icon="ios-albums-outline"></Button>{{node.label}}</span>
                                            </span>
                                            </el-tree>
                                            </Scroll>
                                        </Card>
                                        <loading :show="show_loading_tree"></loading>
                                    </FormItem>
                                    <FormItem label="授权有效期：" prop="authType">
                                        <RadioGroup v-model="form_detail.authType">
                                            <Radio label="指定授权时间段"></Radio>
                                            <Radio label="永久授权"></Radio>
                                        </RadioGroup>
                                        <date-picker v-if="form_detail.authType == '指定授权时间段'" ref="datepicker"
                                                    @on-start-change="update_start_date"
                                                    @on-end-change="update_end_date"
                                                    :default_start="this.dateStart"
                                                    :default_end="this.dateEnd"
                                        ></date-picker>
                                    </FormItem>
                                </Form>
                                <Row>
                                    <div class="margin-top-20" style="text-align: center">
                                        <Button type="primary" @click="expires_submit('form_detail')">确定</Button>
                                        <Button type="ghost" @click="expires_cancel">取消</Button>
                                    </div>
                                </Row>
                            </Col>

                        </div>
                       
                        <loading :show="show_loading_preview"></loading>

                    </Col>
                </Row>
                <modal-sga-new-app ref="modal_sga_new_app" @update-list="customerNo_blur"></modal-sga-new-app>
            </Card>
        </div>
    </div>
</template>

<script>
    import util from '../../../libs/util';
    import api from '../../../api/api';
    import loading from '../../my-components/loading/loading';
    import datePicker from '../../common-components/date-components/date-picker-set';
    import commonSelect from '../../common-components/select-components/selectCommon_params_single';
    import modalSgaNewApp from './modal-sga-new-app'
    export default {
        name: 'modal-sga-new-auth',
        props: {
            size: String,
        },
        watch: {
            filterText (val) {
                // if(val || val === 0){
                //     this.service_group_top_ctrl(true)
                // }else{
                //     this.service_group_top_ctrl(false)
                // }
                this.$refs.tree_sg.filter(val);
            }
        },
        components: {
            loading,
            datePicker,
            commonSelect,
            modalSgaNewApp
        },
        data () {

            return {
                midPwList:[],
                // 服务分组loading
                show_loading_tree : false,
                // 筛选文字
                 filterText : '',
                // 开始日期绑定
                dateStart: '',
                //结束日期绑定
                dateEnd: '',
                // 基本信息数据绑定
                form_detail: {
                    appId:"",
                    authType: '指定授权时间段',
                    code : '',
                },
                rule_detail: {
                    code :[
                        {required: true, message: '商编不能为空', trigger: 'blur'},
                    ],
                    appId:[
                        {required: true, message: '应用标识：不能为空', trigger: 'blur'},
                    ],
                    authType:[
                        {required: true, message: '授权类型不能为空',trigger: 'blur'},
                    ],
                    // filterText:[
                    //     {required: true, message: '产品名称不能为空',trigger: 'blur'},
                    // ]
                },
                // 窗口展示
                modal_show: false,
                show_loading_preview: false,
                data_tree: [],
                // 上一次请求的商编
                last_customerNo : '',
                productCodes:[]
            };
        },
        methods: {
            
            handleCheckChange () {
                let res = this.$refs.tree_sg.getCheckedNodes()
                res.forEach((item) => {
                    this.productCodes.push(item.code)
                })
                // if(this.productCodes.length != 0){
                //     this.filterText = "1"
                // }else{
                //     this.filterText = ""
                // }
            },
            // 启用/禁用所有1级服务分组
            service_group_top_ctrl(option){
                this.data_tree.forEach(
                    item =>{
                        item.disabled = option;
                    }
                )
            },
            // 组件初始化获取
            init () {
                this.$refs.form_detail.resetFields();
                this.form_detail.code = '';
                this.$refs.select_appKey_m.resetSelected();
                this.$refs.select_appKey_m.data_List = [];
                this.$store.state.select.app_authz.data = [];
                this.data_tree = [];
                // this.information_tree_get();
            },
            expires_submit (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        let checked_sgs = this.$refs.tree_sg.getCheckedKeys();
                        if(checked_sgs.length === 0){
                            this.$ypMsg.notice_warning(this,'请至少选择一个产品');
                        }else{
                            let param = {checked_sgs}
                            if(this.form_detail.authType === '永久授权'){
                                param ={
                                    customerNo: this.form_detail.code,
                                    appId: this.form_detail.appId,
                                    productCodes: checked_sgs,
                                    overdueDate : '',
                                    effectiveDate:'',
                                    cause : '运营后台人工新增授权产品码'
                                }
                            }else{
                                param ={
                                    customerNo: this.form_detail.code,
                                    appId : this.form_detail.appId,
                                    productCodes: checked_sgs,
                                    effectiveDate : util.dateFormat_component(this.dateStart),
                                    overdueDate : util.dateFormat_component(this.dateEnd),
                                    cause:"运营后台人工新增授权产品码"
                                }
                            }
                            if(param.productCodes.length === 0){
                                this.$Notice.warning({
                                    title : '警告',
                                    desc :'请至少包含一个产品',
                                    duration : 10
                                })
                            }else {
                                console.log(1)
                                api.yop_product_authz_batch_auth(param).then(
                                    (response) =>{
                                        if (response.status === 'success') {
                                            let result = response.data.result;
                                            if(result.failedAuthz && result.failedAuthz.length>0){
                                                /* this.$Modal.success({
                                                    title : '',
                                                    content : "新增产品授权成功",//待改
                                                    duration : 15
                                                }); */
                                                result.failedAuthz.forEach(item => {
                                                    this.$ypMsg.notice_scroll(this,'error','',item.errorMsg);
                                                });
                                            }else{
                                                this.$Notice.success({
                                                    title : '成功',
                                                    desc : '服务分组全部授权成功',
                                                    duration : 5
                                                });
                                            //     this.preview_cancel();
                                           
                                            }
                                                 this.$router.push({
                                                    path: 'list',
                                                    // params: argu
                                                })
                                            this.$emit('update-list',this.form_detail.code,this.form_detail.appKeyCode)
                                        } else {
                                            this.$Modal.error({
                                                title : '',
                                                content : "新增产品授权失败，请重试！",
                                                duration : 10
                                            });
                                        }
                                    });
                            }
                        }
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 去除父节点
            parentNode_clear (arr){
                let serviceGroup_top = this.tree_parentNode_get();
                for (let i = arr.length - 1; i >= 0; i--) {
                    if (util.IsInArray(serviceGroup_top,arr[i])) {
                        arr.splice(i, 1);
                    }
                }
                return arr;
            },
            // 获取树的一级节点
            tree_parentNode_get (){
                let resultTemp = []
                for(var i in this.data_tree){
                    resultTemp.push(this.data_tree[i].code)
                }
                return resultTemp;
            },
            // 部分授权失败字段处理
            failed_reason_handler(result){
                let resultTemp = '';
                for (var i in result) {
                    if (i === result.length - 1) {
                        resultTemp = resultTemp + (parseInt(i) + 1) + '.<span style=\'color:red\'>' + result[i].code + '</span>: ' + result[i].reason;
                    } else {
                        resultTemp = resultTemp + (parseInt(i) + 1) + '.<span style=\'color:red\'>' + result[i].code + '</span>:' + result[i].reason + '<br/>';
                    }
                }
                return resultTemp;
            },
            expires_cancel () {
                this.preview_cancel();
                this.$router.push({
                    path: 'list',
                    // params: argu
                })
            },
            // 窗口关闭按钮
            preview_cancel () {
                this.modal_show = false;
            },
            // 窗口显示按钮
            preview_show () {
                this.form_detail.authType = '永久授权';
                this.modal_show = true;
            },
            // 开始日期更新
            update_start_date (val) {
                this.dateStart = val;
            },
            // 结束日期更新
            update_end_date (val) {
                this.dateEnd = val;
            },
            // 日期初始化
            date_picker_init (){
              this.$refs.datepicker.date_now_set();
            },
            // 设置商编
            customer_no_set (data) {
                this.form_detail.code = data;
            },
            // 新增应用函数
            addApp () {
                this.$refs.modal_sga_new_app.formData_reset();
                this.$refs.modal_sga_new_app.set_modal_status(true);
                this.$refs.modal_sga_new_app.set_modal_preview(true);
            },
            // 应用标识数据更新
            updateSelect_appKey_model (val) {
                this.form_detail.appId = val;
                let param ={}
                if(val){
                    param ={
                        customerNo: this.form_detail.code,
                    }
                }else{
                    param ={
                        customerNo: this.form_detail.code,
                    }
                }
                this.information_tree_get(param);
            },
            filterNode (value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            },
            select_callBack (){

            },
            // 树的结构信息获取
            information_tree_get (param) {
                this.show_loading_tree = true;
                api.yop_product_group_list().then(
                    (response) =>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.data_tree = [];
                            this.data_tree = this.information_tree_handler(result,0);
                            console.log(this.data_tree,"resultresultresult")
                        }else{
                            this.$ypMsg.notice_error(this,'服务分组列表数据获取失败,请刷新重',response.data.message,response.data.solution);
                        }
                        this.show_loading_tree = false;
                    }
                )
            },
            // 树的结构数据处理
            information_tree_handler (data,index) {
                let topTemp =true;
                if(index === 0){
                    topTemp = true;
                }else{
                    topTemp =false;
                }
                let returnData = [];
                index ++;
                if(data){
                    data.forEach(
                        (item) =>{
                            let enableStatusTemp = true
                            let statusTemp = '启用'
                            let optionTemp = '禁用'
                            let colorTemp = 'green'
                            let nameTemp = ''
                            if(item.status === 'ENABLE'){
                                statusTemp = '启用'
                                optionTemp = '禁用'
                                colorTemp = 'green'
                                enableStatusTemp = true
                            }else{
                                statusTemp = '禁用'
                                optionTemp = '启用'
                                colorTemp = 'red'
                                enableStatusTemp = false
                            }
                            if(util.getLength(item.name) > 10){
                                nameTemp = item.name.substring(0,10)+'...'
                            }else{
                                nameTemp = item.name
                            }
                            if(!!item.children){
                                returnData.push({
                                    code: item.code,
                                    top : topTemp,
                                    disabled : false,
                                    label : nameTemp+"("+item.code+")",
                                    name : item.name,
                                    depth: item.level,
                                    status : statusTemp,
                                    option : optionTemp,
                                    color : colorTemp,
                                    enableStatus : enableStatusTemp,
                                    children : this.information_tree_handler(item.children,index)
                                })
                            }else{
                                returnData.push({
                                    // id: item.id,
                                    // pid : item.pid,
                                    code: item.code,
                                    top : topTemp,
                                    label : nameTemp+"("+item.code+")",
                                    name : item.name,
                                    depth: item.level,
                                    disabled : false,
                                    status : statusTemp,
                                    option : optionTemp,
                                    color : colorTemp,
                                    enableStatus : enableStatusTemp,
                                    children : []
                                })
                            }
                        }
                    )
                }
                return returnData;
            },
            // 商户编号改变请求应用标识 和服务分组列表
            customerNo_blur () {
                if((this.form_detail.code || this.form_detail.code === 0)&& (this.form_detail.code !== this.last_customerNo)){
                    let param ={
                        customerNo:this.form_detail.code,
                    }
                    this.$refs.select_appKey_m.updateList(param);
                    this.information_tree_get(param);
                }else{
                    this.data_tree = []
                    this.$store.state.select.app_authz.data = [];
                }
            },
            // 应用标识请求

            // 商户编号聚焦时
            customerNo_focus (){
                this.last_customerNo = this.form_detail.code
            },
            update_serviceAuthList(code, name) {
                this.reset_Interface();
                this.data_product_code = code;
                this.data_product_name = name;
                this.search_interface();
            },
            reset_Interface() {
                this.data_product_code = "";
                this.data_product_name = "";
                this.data_product_apiUri = "";
                this.dateStart = '';
                this.dateEnd = '';
                this.$refs.select_sp_m.resetSelected();
                this.$refs.select_status.resetSelected();
                this.$refs.product_type.resetSelected(); 
            },
        },
        mounted () {
            // this.init();
        }
    };
</script>

<style scoped>

</style>
