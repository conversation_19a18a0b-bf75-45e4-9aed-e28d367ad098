<template>
  <Modal id="modal_request_1" v-model="modal_app" width="600" :closable="false">
    <p slot="header" style="color:#2d8cf0;">
      <span style="color:black" v-show="create_orEdit">新增应用</span>
      <span style="color:black" v-show="!create_orEdit">编辑应用</span>
    </p>
    <div>
      <Form ref="form_app" :model="form_app" :rules="rule_app" :label-width="120">
        <FormItem label="商户编码：" prop="customerNo" v-if="create_orEdit">
          <Input type="text" size="small" v-model="form_app.customerNo" style="width:80%"></Input>
        </FormItem>
        <FormItem label="商户编码：" v-show="!create_orEdit">{{form_app.customerNo}}</FormItem>
        <FormItem label="客户编码：" v-show="!create_orEdit">{{form_app.subjectNo}}</FormItem>
        <FormItem label="应用标识：" v-show="!create_orEdit">{{current_appId}}</FormItem>
        <FormItem label="应用类型：" prop="appType" v-show="create_orEdit">
          <common-select
            ref="select_appType_m"
            @on-update="updateSelect_appType_m"
            type="normal"
            keyWord="result"
            holder="请选择应用类型"
            code="code"
            title="name"
            group="app_type"
            @on-loaded="select_callBack"
            :default="this.form_app.appType"
            size="small"
            :uri="this.$store.state.select.app_type.uri"
            style="width:80%"
          ></common-select>
        </FormItem>
        <FormItem label="应用标识：" v-if="form_app.appType === 'PARTNER' && create_orEdit" prop="appId">
          <Input placeholder="输入合作方应用标识" type="text" size="small" v-model="form_app.appId" style="width:80%"></Input>
        </FormItem>
        <p class="yop-explain-120" style="margin-bottom:10px;" v-if="form_app.appType === 'PARTNER' && create_orEdit">支持输入大小写字母、数字、中文、特殊字符，最长可输入60个字符</p>
        <FormItem label="应用名称：" prop="appName">
          <Input type="text" size="small" v-model="form_app.appName" style="width:80%"></Input>
        </FormItem>
         <p class="yop-explain-120" style="margin-bottom:10px;">支持输入大小写字母、数字、中文、特殊字符，最长可输入60个字符</p>
        <FormItem
          label="应用类型："
          v-show="!create_orEdit"
        >{{data_name_handler(form_app.appType,'app_type')}}</FormItem>
        <FormItem label="描述：" prop="appDesc">
          <Input type="textarea" size="small" v-model="form_app.appDesc" style="width:80%"></Input>
        </FormItem>
        <p class="yop-explain-120">最长可输入100个字符</p>
      </Form>
    </div>
    <div slot="footer">
      <Button id="modal_app_btn_2" type="primary" @click="ok_create_app('form_app')">确定</Button>
      <Button id="modal_app_btn_1" type="ghost" @click="cancel_create_app">取消</Button>
    </div>
    <loading :show="show_loading_createApp"></loading>
  </Modal>
</template>

<script>
import loading from "../../my-components/loading/loading";
import commonSelect from "../../common-components/select-components/selectCommon";
import util from "../../../libs/util";
import api from "../../../api/api";

export default {
  name: "modal_create_edit_app",
  components: {
    commonSelect,
    loading
  },
  data() {
    // app名称验证
    const validate_app_appName = (rule, value, callback) => {
      if (util.getLength(value) > 60) {
        callback(new Error("长度不能大于60"));
      } else {
        callback();
      }
    };
    // 描述输入验证
    const validate_app_appDesc = (rule, value, callback) => {
      if (util.getLength(value) > 100) {
        callback(new Error("长度不能大于100"));
      } else {
        callback();
      }
    };
    const validate_app_customerNo = (rule, value, callback) => {
      setTimeout(() => {
        if (util.getLength(value) > 60) {
          callback(new Error("长度不能大于60"));
        } else {
          api
            .yop_isv_exists({
              customerNo: value.trim()
            })
            .then(response => {
              if (response.data.status === "success") {
                let result = response.data.data.result;
                if (result) {
                  callback();
                } else {
                  callback(new Error("该商编不存在"));
                }
              } else {
                callback(new Error("商编验重失败"));
              }
            });
        }
      }, 300);
    };
    // app标识验证
    const validate_app_appId = (rule, value, callback) => {
      setTimeout(() => {
        if (util.getLength(value) > 60) {
          callback(new Error("长度不能大于60"));
        } else {
          api
            .yop_app_exists({
              appId: value.trim(),
              ignoreDeleted : false
            })
            .then(response => {
              if (response.data.status === "success") {
                let result = response.data.data.result;
                if (!result) {
                  callback();
                } else {
                  callback(new Error("该应用标识已存在"));
                }
              } else {
                callback(new Error("应用标识验重失败"));
              }
            });
        }
      }, 300);
    };
    return {
      // 当前是创建还是编辑 创建：true 编辑：false
      create_orEdit: true,
      // 窗口显示绑定
      modal_app: false,
      // 表单数据绑定
      form_app: {
        customerNo: "",
        appType: "",
        appName: "",
        appDesc: "",
        subjectNo: "",
        appId: ""
      },
      // 数据限制
      rule_app: {
        customerNo: [
          { required: true, message: "商户编码不能为空", trigger: "blur" },
          { validator: validate_app_customerNo, trigger : "blur"}
        ],
        appType: [{ required: true, message: "应用类型不能为空" }],
        appName: [
          { required: true, message: "应用名称不能为空", trigger: "blur" },
          { validator: validate_app_appName, trigger: "blur" }
        ],
        appDesc: [{ validator: validate_app_appDesc, trigger: "blur" }],
        appId : [
          { required: true, message: "应用标识不能为空", trigger: "blur" },
          { validator: validate_app_appId, trigger: "blur" }
        ]
      },
      // 当前appId
      current_appId: "",
      // loading 新建应用
      show_loading_createApp: ""
    };
  },
  methods: {
    // 窗口显示设置
    set_modal_preview(val) {
      this.modal_app = val;
    },
    // 设置当前为新建或编辑
    set_modal_status(val) {
      this.create_orEdit = val;
    },
    // 设置当前appId
    set_appId(val) {
      this.current_appId = val;
    },
    //设置当前表格数据
    set_current_form_data(data) {
      this.form_app.customerNo = data.customerNo;
      this.form_app.appType = data.type;
      this.form_app.appName = util.empty_handler2(data.name);
      this.form_app.appDesc = util.empty_handler2(data.desc);
      this.form_app.subjectNo = data.subjectNo;
    },
    // 下拉框加载完处理函数
    select_callBack() {
      this.count_select_related--;
      if (this.count_select_related === 0) {
        this.search();
      }
    },
    // 返回相应编码的名字
    data_name_handler(code, name) {
      if (code) {
        if (
          this.$store.state.select[name].data &&
          this.$store.state.select[name].data.length > 0
        ) {
          let group = this.$store.state.select[name].data;
          for (var i in group) {
            if (group[i].value === code) {
              return group[i].name;
            }
          }
        } else {
          return code;
        }
      } else {
        return "";
      }
    },
    // 应用类型数据更新
    updateSelect_appType_m(val) {
      this.form_app.appType = val;
    },
    // 新增应用确定
    ok_create_app(val) {
      this.$refs[val].validate(valid => {
        if (valid) {
          this.show_loading_createApp = true;
          if (this.create_orEdit) {
            let param = {
              customerNo: this.form_app.customerNo.trim(),
              type: this.form_app.appType,
              name: this.form_app.appName.trim(),
              desc: this.form_app.appDesc.trim(),
              appId: this.form_app.appId.trim()
            };
            util.paramFormat(param);
            // 如果此按钮需要审核则执行
            if(util.checkAudit('/rest/app/create')){
                this.show_loading_createApp = false;
                this.modal_app = false;
            }
            api.yop_app_create(param).then(response => {
              //需要填写
              if (response.status === "success") {
                let present_content =
                  "商户编码:" +
                  param.customerNo +
                  "<br/>客户编码:" +
                  response.data.result.subjectNo +
                  "<br/>应用类型:" +
                  this.data_name_handler(param.type, "app_type") +
                  "<br/>应用名称:" +
                  param.name +
                  "<br/>应用标识:" +
                  response.data.result.appId;
                this.$ypMsg.notice_success(
                  this,
                  present_content,
                  "添加成功",
                  10
                );
                this.$emit("update-list");
              } else {
                this.$ypMsg.notice_error(
                  this,
                  "错误",
                  response.message,
                  response.solution
                );
              }
              this.show_loading_createApp = false;
              this.modal_app = false;
            });
          } else {
            let param = {
              appId: this.current_appId,
              name: this.form_app.appName.trim(),
              desc: this.form_app.appDesc.trim()
            };
            console.log(param);
            //可能需要对密钥处理剔除
            api.yop_app_edit(param).then(response => {
              let present_content =
                "应用标识:" + param.appId + "<br/>应用名称:" + param.name;
              //需要填写
              if (response.status === "success") {
                this.$ypMsg.notice_success(
                  this,
                  present_content,
                  "编辑应用成功",
                  10
                );
                this.$emit("search_app_list");
              } else {
                this.$ypMsg.notice_error(
                  this,
                  "错误",
                  response.message,
                  response.solution
                );
              }
              this.show_loading_createApp = false;
              this.modal_app = false;
            });
          }
        } else {
          this.$Message.error("请检查");
        }
      });
    },
    // 新增应用取消
    cancel_create_app() {
      this.modal_app = false;
      this.formData_reset();
    },
    // 列表数据重置
    formData_reset() {
      this.$refs.form_app.resetFields();
      this.$refs.select_appType_m.resetSelected();
      this.form_app.customerNo = "";
      this.form_app.appType = "";
      this.form_app.appName = "";
      this.form_app.appDesc = "";
    }
  }
};
</script>

<style scoped>
</style>
