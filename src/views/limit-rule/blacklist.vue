<style lang="less" scoped>
    @import "../../styles/common.less";
    .edit-btn-con{
        text-align: center;
    }
    .edit-btn{
        float: right;
        margin-right: 10px;
    }
</style>
<template>
    <div style="background: #eee;padding:8px">
         <Card :bordered="false">
            <!-- 黑名单列表 table部分 -->
            <Row class="margin-top-10">
                <Row>
                    <Button id="buttonBlackList_0" type="primary" class="margin-bottom-10" @click="addIp">新增黑名单</Button>
                    <Col span="24">
                        <Table  border :columns="columns_blackList" width="780" :data="data_blackList"></Table>
                    </Col>
                    <loading :show="show_loading_blackList"></loading>
                </Row>
                
                <Row>
                    <Col class="edit-btn-con" span="16">
                        <Button id="buttonBlackList_1" class="margin-top-10 edit-btn cancel"  type="ghost"  @click="cancelEdit">取消</Button>
                        <Button id="buttonBlackList_2" class="margin-top-10 edit-btn confirm"  type="primary"  @click="goEdit">确定</Button>
                    </Col>
                </Row>
                
            </Row>

            <!-- 启用 禁用  删除 弹框 -->
            <Modal v-model="disableEnableDelFlag" :closable="false" :mask-closable="false">
                <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
                    <span>{{disableEnableDelTitle}}</span>   
                </p>
                <div style="text-align:left;font-size: 12px;">
                    <div style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;">
                        <i class="ivu-icon ivu-icon-help-circled"></i>
                    </div>
                    <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                        <p>{{disableEnableDelTitle}}吗？</p>
                        <label for="" class="reasonType"><span style="color:red;font-size: 12x;">*</span>原因：</label>
                        <Input type="textarea" v-model="commonCause" style="width:85%;font-size: 12x;margin-top:8px;" placeholder="" placeholder-style="font-size:12px;"></Input>
                    </div>
                </div>
                <div slot="footer" style="border:0;">
                    <Button id="buttonBlackList_4" type="primary" v-url="{url:'/rest/limit-rule/blacklist/update'}"  @click="confirmDisableEnableDel(disableEnableDelTitle)">确定</Button>
                    <Button id="buttonBlackList_3" type="ghost"  @click="cancelModel">取消</Button>
                </div>
            </Modal>
         </Card>
    </div>
</template>

<script>
import loading from '../my-components/loading/loading'
import api from "../../api/api";
import util from "../../libs/util"
    export default {
        name: 'limit_rule_blacklist',
        components:{
            loading
        },
        data() {
            return {
                // 类型
                searchType: 'blacklist',
                currentId: '',
                // loading
                show_loading_blackList: false,
                // 删除 启用 禁用二次确认弹框标识
                disableEnableDelFlag: false,
                // 删除 启用 禁用二次确认弹框 标题
                disableEnableDelTitle: '',
                // 操作原因
                commonCause: '',

                // 编辑白名单 id标识 
                addEditWhiteCurrentBar: '',
                // 编辑当前的内容存放
                addEditWhiteCurrentDes: '',
                addEditWhiteCurrentIp: '',

                // 白名单列表数据
                data_blackList:[
                    /* {
                        ip: '192.101.293.12' ,
                        description: '窗前有两棵枣树',
                        status: '启用',
                    },
                    {
                        ip: '192.222.323.666' ,
                        description: '大江东去，浪淘尽',
                        status: '禁用',
                    } */
                ],
                // 白名单列表标题
                columns_blackList:[{
                        title: 'IP',
                        key: 'ip',
                        align: 'center',
                        render: (h, params) => {
                            // const { id, score } = p.row
                            const inp = h('input', {
                            style: {
                                width: '100%',
                                padding: '4px 2px',
                                borderRadius: '4px',
                                border: '1px solid #e9eaec',
                                textAlign: 'center'
                            },
                            attrs: {
                                // maxlength: 16
                            },
                            domProps: {
                                value: params.row.ip
                            },
                            on: {
                                    input: (event) => {
                                    this.addEditWhiteCurrentIp = event.target.value
                                }
                            }
                            })
                            return this.addEditWhiteCurrentBar === params.index ? inp : h('span', params.row.ip)

                        }
                    },
                    {
                        title: '状态',
                        key: 'status',
                        align: 'center',
                        render:(h,params) =>{
                            return h("div",[
                                h('p', params.row.status == "ENABLED" ? '已启用' : '已禁用'),
                            ])
                        }
                    },
                    {
                        title: '描述',
                        key: 'description',
                        align: 'center',
                        render: (h, params) => {
                            // const { id, score } = p.row
                            const inp = h('input', {
                            style: {
                                width: '100%',
                                padding: '4px 2px',
                                borderRadius: '4px',
                                border: '1px solid #e9eaec',
                                textAlign: 'center'
                            },
                            attrs: {
                                maxlength: 20
                            },
                            domProps: {
                                value: params.row.description
                            },
                            on: {
                                    input: (event) => {
                                    this.addEditWhiteCurrentDes = event.target.value
                                }
                            }
                            })
                            return this.addEditWhiteCurrentBar === params.index ? inp : h('span', params.row.description)

                        }
                    },
                    {
                        title: "操作",
                        align: "center",
                        key: "operations",
                        render: (h, params) => {
                            // const { addEditWhiteCurrentBar } = this
                            const btnEdit = [
                                h("Button",{
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "inline-block" : 'none'}`
                                    },
                                    on: {
                                        click: () => {
                                            this.data_blackList.forEach((item,index) => {
                                                if(params.row._index == index){
                                                    item.status = 'DISABLED'
                                                }
                                            });
                                        }
                                    }
                                }, "禁用"),

                                h("Button",{
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "none" : 'inline-block'}`
                                    },
                                    on: {
                                        click: () => {
                                            this.data_blackList.forEach((item,index) => {
                                                if(params.row._index == index){
                                                    item.status = 'ENABLED'
                                                }
                                            });
                                        }
                                    }
                                }, "启用"),

                                h("Button",{
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "none" : 'inline-block'}`
                                    },
                                    on: {
                                        click: () => {
                                            this.$Modal.confirm({
                                                title: '确认删除',
                                                content: '<p>确定要删除吗？</p>',
                                                onOk: () => {
                                                    // 是否需要删除ip
                                                    this.deleteBlackList(params)
                                                },
                                                onCancel: () => {}
                                            });
                                        }
                                    }
                                },"删除"),
                                h('i-button', {
                                    props: {
                                            type: 'text',
                                            size: "small"
                                        },
                                    on: {
                                        click: () => {
                                            this.addEditWhiteCurrentBar = params.index
                                        }
                                    }
                                }, '修改'),
                            ]; 
                            const btnSaveCancel = [
                                h('i-button', {
                                    props: {
                                        type: 'success',
                                        size: "small"
                                    },
                                    on: {
                                        click: () => {
                                            this.handleSave(params)
                                        }
                                    }
                                }, '保存'),

                                h('i-button', {
                                    props: {
                                        size: "small"
                                    },
                                    on: {
                                        click: () => {
                                            this.addEditWhiteCurrentBar = ''
                                        }
                                    }
                                }, '取消')];

                            return h("div", [
                                // 保存取消 按钮
                                this.addEditWhiteCurrentBar === params.index ? h('p', btnSaveCancel) : btnEdit
                                
                            ]);
                        }
                    }
                ],
                
            }
        },
        created() {
            
        },
        mounted() {
            // 获取列表
            this.getLimitRuleList()
        },
        methods: {
            // 获取应用列表
            getLimitRuleList(){
                this.show_loading_blackList = true;
                let params = {};
                api.yop_app_limit_rule_blacklist(params,this.searchType).then(
                    (response) =>{
                        this.show_loading_blackList = false;
                        if(response.data.status === 'success'){
                            this.data_blackList = response.data.data.result ? response.data.data.result.blacklist : []
                            this.currentId = response.data.data.result ? response.data.data.result.id : ''
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            // 提交 弹框
            goEdit(){
                this.disableEnableDelFlag = true
                this.disableEnableDelTitle = '确认提交'
            },
            // 取消提交
            cancelEdit(){
                this.data_blackList = []
                this.getLimitRuleList()
                this.addEditWhiteCurrentBar = ''
            },
            // 启用 禁用  删除弹框确认
            confirmDisableEnableDel(type){
                // 校验里面的ip是否有空，有空则不通过
                let checkHasIp = true,
                    ipArr = [];
                this.data_blackList.forEach(item => {
                    if(!item.ip || item.ip == ""){
                        checkHasIp = false
                        return checkHasIp;
                    }
                });
                if(!checkHasIp){
                    this.$Message.warning('请检查ip');
                    return false;
                }
                
                // 检查IP是否重复 start
                this.data_blackList.forEach(item => {
                    ipArr.push(item.ip);
                });
                function isRepeat(arr){
                    let  hash = {};
                    for(let i in arr) {
                        if(hash[arr[i]]) {
                            return true;
                　　     }
                        hash[arr[i]] = true;
                    }
                    return false;
                } 
                if(isRepeat(ipArr)){
                    this.$Message.warning('ip有重复，请检查');
                    return false;
                }
                // 检查IP是否重复 end
                if(!this.commonCause || this.commonCause == ""){
                    this.$Message.warning('请输入原因');
                    return false;
                }
                let params = {
                    id: this.currentId,
                    cause: this.commonCause
                };
                if(type == '确认提交'){
                    if(!this.currentId){
                        this.$Message.warning('暂时无法提交');
                        return false
                    }
                   let param = {
                        id: this.currentId,
                        blacklist: this.data_blackList,
                        cause: this.commonCause
                    };
                    api.yop_app_limit_rule_update(param,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.disableEnableDelFlag = false
                                this.commonCause = ''
                                this.$ypMsg.notice_success(this,'提交成功');
                                this.getLimitRuleList()
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    ) 
                }
                
            },
            //弹框取消
            cancelModel(type){
                this.disableEnableDelFlag = false
                this.commonCause = ""
            },
            // 启用 网段/ip
            activeIp(row){
                api.yop_app_limit_rule_enable(params,this.searchType).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'启用成功');
                            this.getLimitRuleList()
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                    }
                )
            },
            // 删除 ip/网段
            deleteBlackList(params){
                this.data_blackList.splice(params.index,1)
            },
            // 新增ip或网段
            addIp(){
                this.data_blackList.push({
                    ip: '' ,
                    description: '',
                    status: 'ENABLED',
                });
                this.addEditWhiteCurrentBar = this.data_blackList.length - 1
            },
            // 保存编辑
            handleSave (params) {
                let isIpFlag = true;
                this.data_blackList.forEach((item,index) => {
                    if(params.index == index){
                        if(!this.addEditWhiteCurrentIp){
                            this.addEditWhiteCurrentIp = params.row.ip
                        }
                        if(!this.addEditWhiteCurrentDes){
                            this.addEditWhiteCurrentDes = params.row.description
                        }
                        // 校验ip  FF01::1101
                        if(util.checkIP(this.addEditWhiteCurrentIp)){
                            
                        }else{
                            isIpFlag = false;
                            return isIpFlag;
                        }
                        item.ip = this.addEditWhiteCurrentIp
                        item.description = this.addEditWhiteCurrentDes
                    }
                    
                });
                if(!isIpFlag){
                    this.$Message.warning('ip或网段格式不正确');
                    return false;
                }
                this.addEditWhiteCurrentBar = ''
                this.addEditWhiteCurrentDes = ''
                this.addEditWhiteCurrentIp = ''
            }
        },
    }
</script>
