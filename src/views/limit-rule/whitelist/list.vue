<style lang="less" scoped>
    @import "../../../styles/common.less";
</style>
<template>
    <div style="background: #eee;padding:8px">
         <Card :bordered="false">
             <Row type="flex" align="middle">
                 <!-- 搜索条件 -->
                <Col span="24">
                    <Col span="6" >
                        <Col span="5" class="margin-top-10" >商编：&nbsp;</Col>
                        <Col span="14"> 
                            <Input
                                id="inputCustomerNo"
                                class="margin-top-5"
                                v-model="searchCustomerNo"
                                placeholder="商编"
                                clearable
                            ></Input> 
                        </Col>
                    </Col>
                    
                    <Col span="6" >
                        <Col span="7" class="margin-top-10" >应用标识：&nbsp;</Col>
                        <Col span="14"> 
                            <Input
                                id="inputAppBar"
                                class="margin-top-5"
                                v-model="searchAppId"
                                placeholder="应用标识"
                                clearable
                            ></Input> 
                        </Col>
                    </Col>
                    
                    <Col span="6" >
                        <Col span="5" class="margin-top-10" >状态：&nbsp;</Col>
                        <Col span="14"> 
                            <Select class="margin-top-5" ref="select_search_status"  id='select_apiM_1' @on-change="changeAppStatus" v-model="searchStatus" placeholder="请选择（默认全部）" clearable>
                                <Option v-for="item in appStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                            </Select>
                        </Col>
                    </Col>

                    <!-- 查询按钮 -->
                    <Col class="margin-top-5" span="4" style="text-align: center">
                        <Button class="margin-right-20" id='search_app_1'  type="primary" @click="getLimitRuleList">查询</Button>
                        <Button id='search_app_2' type="ghost" @click="resetAppList">重置</Button>
                    </Col>
                </Col>
                <Button id="buttonLimt_3" class="margin-top-10" type="primary" @click="addEditApp('add')">新增白名单</Button>
            </Row>
            
            <!-- 应用列表 table部分 -->
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_ag_1' border ref="selection" :columns="columns_AppList" :data="data_AppList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="searchAppList"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_appList"></loading>
            </Row>

            <!-- 新增编辑黑、白名单 弹框 -->
            <Modal id="modal_alert_1" v-model="addEditWhiteFlag" width="800" :closable="false" :mask-closable="false">
                <p slot="header" style="color:#2d8cf0;height:auto">
                    <span style="color:block;">{{addEditTitle}}</span>
                </p>
                <div>
                    <Row v-if="addEditTitle != '新增白名单'"  class="margin-bottom-10">
                        <Col span="10">
                            <Col span="5">
                                商编：
                            </Col>
                            <Col span="18">
                                {{currentAppInfo.customerNo}}
                            </Col>
                        </Col>
                    </Row>

                    <Row>
                        <Row class="margin-bottom-10" v-if="addEditTitle == '编辑白名单'">
                            <Col span="10">
                                <Col span="5">
                                    应用标识：
                                </Col>
                                <Col span="18">
                                    {{currentAppInfo.appId}}
                                </Col>
                            </Col>
                        </Row>

                        <Row class="margin-bottom-10" v-if="addEditTitle == '新增白名单'">
                            <Col span="10">
                                <Col span="5">
                                    <span style="color:red;font-size: 12x;">*</span>应用标识：
                                </Col>
                                <Col span="18">
                                   <Input
                                        style="width: 200px;font-size: 12x;"
                                        id="inputCustomerId"
                                        @on-blur="appIsExist"
                                        v-model="appId"
                                        placeholder="应用标识"
                                        clearable
                                    ></Input> 
                                </Col>
                            </Col>
                        </Row>

                        <Row class="margin-bottom-10">
                            <Col span="10">
                                <Col span="5">
                                    白名单：
                                </Col>
                                <Col span="18">
                                    <Button id="buttonLimt_0" type="primary" size="small" @click="addIp">新增ip或网段</Button> 
                                </Col>
                            </Col>
                        </Row>
                        
                    </Row>
                    
                    <Row class="margin-top-10">
                        <Col span="24">
                        <Table  border :columns="columns_whiteList" width="780" :data="data_whiteList"></Table>
                        </Col>
                        <loading :show="show_loading_whiteList"></loading>
                    </Row>

                    <Row class="margin-top-10">
                        <Col span="10">
                            <Col span="5">
                                <span style="color:red;font-size: 12x;">*</span>原因：
                            </Col>
                            <Col span="18">
                                <Input id="inputLimit_1"
                                    v-model="addEditCause" 
                                    style="width:200px;font-size: 12x;" 
                                    placeholder="请输入原因" 
                                    placeholder-style="font-size:12px;">
                                </Input>
                            </Col>
                        </Col>
                    </Row>
                </div>
                <div slot="footer">
                    <Button id="modal_btn_1" type="primary" @click="ok_addEditWhiteList">确定</Button>
                    <Button id="modal_btn_2" type="ghost" @click="cancle_addEditWhiteList">关闭</Button>
                </div>
            </Modal>

            <!-- 启用 禁用  删除 弹框 -->
            <Modal v-model="disableEnableDelFlag" :closable="false" :mask-closable="false">
                <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
                    <span>{{disableEnableDelTitle}}</span>   
                </p>
                <div style="text-align:left;font-size: 12px;">
                    <div style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;">
                        <i class="ivu-icon ivu-icon-help-circled"></i>
                    </div>
                    <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                        <p>{{disableEnableDelTitle}}吗？</p>
                        <label for="" class="reasonType"><span style="color:red;font-size: 12x;">*</span>原因：</label>
                        <Input id="inputLimit_2" type="textarea" v-model="commonCause" style="width:85%;font-size: 12x;margin-top:8px;" placeholder="" placeholder-style="font-size:12px;"></Input>
                    </div>
                </div>
                <div slot="footer" style="border:0;">
                    <Button id="buttonLimt_1" type="ghost"  @click="cancelModel('disableEnableDelFlag')">取消</Button>
                    <Button id="buttonLimt_2" type="primary"  @click="confirmDisableEnableDel(disableEnableDelTitle)">确定</Button>
                </div>
            </Modal>
         </Card>
    </div>
</template>

<script>
import loading from '../../my-components/loading/loading'
import api from "../../../api/api";
import util from "../../../libs/util"
    export default {
        name: 'limit_rule_whitelist',
        components:{
            loading
        },
        data() {
            return {
                // 搜索条件 商编
                searchCustomerNo: '',
                // 应用标识
                searchAppId: '',
                // 状态
                searchStatus: '',
                // 类型
                searchType: 'whitelist',
                // 新增应用时 应用标识
                appId: '',
                // 状态列表
                appStatusList: [
                    // {label: "一启用", value: 'ENABLED'},
                    // {label: "一禁用", value: 'DISABLED'}
                ],
                // loading
                show_loading_appList: false,
                show_loading_whiteList: false,
                // 分页总数
                pageTotal: 0,
                // 当前页
                pageNo: 1,
                // 删除 启用 禁用二次确认弹框标识
                disableEnableDelFlag: false,
                // 删除 启用 禁用二次确认弹框 标题
                disableEnableDelTitle: '',
                // 操作原因
                commonCause: '',
                // 新增和编辑原因
                addEditCause: '',
                // 编辑新增 标题
                addEditTitle:'',
                // 当前app 节点信息
                currentAppInfo: {
                    // 商编
                    // customerNo: '12ioij2388',
                    // appId: 'aap_lkdfjas'
                },
                // 当前白名单字段信息
                currentIpInfo:{},

                // 编辑白名单 id标识 
                addEditWhiteCurrentBar: '',
                // 编辑当前的内容存放
                addEditWhiteCurrentDes: '',
                addEditWhiteCurrentIp: '',

                // 新增编辑白名单 开关
                addEditWhiteFlag: false,
                // 新增ip/网段标题
                addEditIpTitle: '',
                addIpFrom:{
                    // ip: '192.120.302.43',
                    // status: '已禁用',
                    // description: '世界上本没有路',
                },
                // 应用列表数据
                data_AppList:[],
                // 白名单列表数据
                data_whiteList:[],
                // 应用列表标题
                columns_AppList:[
                    {
                        title: '商编',
                        key: 'customerNo',
                        align: 'center'
                    },
                    {
                        title: '应用标识',
                        key: 'appId',
                        align: 'center'
                    },
                    {
                        title: '状态',
                        key: 'status',
                        width: 100,
                        align: 'center',
                        render:(h,params) =>{
                            return h("div",[
                                h('p', params.row.status == "ENABLED" ? '已启用' : '已禁用'),
                            ])
                        }
                    },
                    {
                        renderHeader: (h, params) => {
                            return h("div", [h("p", "创建时间"), h("p", "最后修改时间")]);
                        },
                        width: 180,
                        align: "center",
                        render: (h, params) => {
                            return h("div", [
                                h("p", params.row.createdDate),
                                h("p", params.row.lastModifiedDate)
                            ]);
                        }
                    },
                    {
                        title: "操作",
                        align: "center",
                        key: "operations",
                        render: (h, params) => {
                            return h("div", [
                                h("Button",{
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "none" : 'inline-block'}`
                                    },
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/limit-rule/whitelist/enable'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.currentAppInfo = params.row
                                            this.disableEnableDelFlag = true
                                            this.disableEnableDelTitle = '确认启用'
                                        }
                                    }
                                },"启用"),

                                h("Button",{
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "inline-block" : 'none'}`
                                    },
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/limit-rule/whitelist/disable'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.currentAppInfo = params.row
                                            this.disableEnableDelFlag = true
                                            this.disableEnableDelTitle = '确认禁用'
                                        }
                                    }
                                }, "禁用"),

                                h("Button",{
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/limit-rule/whitelist/update'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.currentAppInfo = params.row
                                            this.editApp(params.row)
                                        }
                                    }
                                },"修改"),

                                h("Button",{
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "none" : 'inline-block'}`
                                    },
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/limit-rule/whitelist/delete'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.currentAppInfo = params.row
                                            this.disableEnableDelFlag = true
                                            this.disableEnableDelTitle = '确认删除'
                                        }
                                    }
                                },"删除"),
                            ]);
                        }
                    }
                ],
                // 白名单列表标题
                columns_whiteList:[{
                        title: 'IP',
                        key: 'ip',
                        align: 'center',
                        render: (h, params) => {
                            // const { id, score } = p.row
                            const inp = h('input', {
                            style: {
                                width: '100%',
                                padding: '4px 2px',
                                borderRadius: '4px',
                                border: '1px solid #e9eaec',
                                textAlign: 'center'
                            },
                            attrs: {
                                // maxlength: 16
                            },
                            domProps: {
                                value: params.row.ip
                            },
                            on: {
                                    input: (event) => {
                                    this.addEditWhiteCurrentIp = event.target.value
                                }
                            }
                            })
                            return this.addEditWhiteCurrentBar === params.index ? inp : h('span', params.row.ip)

                        }
                    },
                    {
                        title: '状态',
                        key: 'status',
                        align: 'center',
                        render:(h,params) =>{
                            return h("div",[
                                h('p', params.row.status == "ENABLED" ? '已启用' : '已禁用'),
                            ])
                        }
                    },
                    {
                        title: '描述',
                        key: 'description',
                        align: 'center',
                        render: (h, params) => {
                            // const { id, score } = p.row
                            const inp = h('input', {
                            style: {
                                width: '100%',
                                padding: '4px 2px',
                                borderRadius: '4px',
                                border: '1px solid #e9eaec',
                                textAlign: 'center'
                            },
                            attrs: {
                                maxlength: 20
                            },
                            domProps: {
                                value: params.row.description
                            },
                            on: {
                                    input: (event) => {
                                    this.addEditWhiteCurrentDes = event.target.value
                                }
                            }
                            })
                            return this.addEditWhiteCurrentBar === params.index ? inp : h('span', params.row.description)

                        }
                    },
                    {
                        title: "操作",
                        align: "center",
                        key: "operations",
                        render: (h, params) => {
                            // const { addEditWhiteCurrentBar } = this
                            // const { id } = params.row
                            const btnEdit = [
                                h("Button",{
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "inline-block" : 'none'}`
                                    },
                                    on: {
                                        click: () => {
                                            this.disableIp(params.row)
                                            this.data_whiteList.forEach((item,index) => {
                                                if(params.row._index == index){
                                                    item.status = 'DISABLED'
                                                }
                                            });
                                        }
                                    }
                                }, "禁用"),

                                h("Button",{
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "none" : 'inline-block'}`
                                    },
                                    on: {
                                        click: () => {
                                            this.data_whiteList.forEach((item,index) => {
                                                if(params.row._index == index){
                                                    item.status = 'ENABLED'
                                                }
                                            });
                                        }
                                    }
                                }, "启用"),

                                h("Button",{
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "none" : 'inline-block'}`
                                    },
                                    on: {
                                        click: () => {
                                            this.$Modal.confirm({
                                                title: '确认删除',
                                                content: '<p>确定要删除吗？</p>',
                                                onOk: () => {
                                                    // 是否需要删除ip
                                                    this.deleteApp('ip',params)
                                                },
                                                onCancel: () => {}
                                            });
                                        }
                                    }
                                },"删除"),
                                h('i-button', {
                                    props: {
                                            type: 'text',
                                            size: "small"
                                        },
                                    on: {
                                        click: () => {
                                            this.addEditWhiteCurrentBar = params.index
                                        }
                                    }
                                }, '修改')
                            ];

                            const btnSaveCancel = [
                                h('i-button', {
                                    props: {
                                        type: 'success',
                                        size: "small"
                                    },
                                    on: {
                                        click: () => {
                                            this.handleSave(params)
                                        }
                                    }
                                }, '保存'),

                                h('i-button', {
                                    props: {
                                        size: "small"
                                    },
                                    on: {
                                        click: () => {
                                            this.addEditWhiteCurrentBar = ''
                                        }
                                    }
                                }, '取消')];

                            return h("div", [
                                // 保存取消 按钮
                                this.addEditWhiteCurrentBar === params.index ? h('p', btnSaveCancel) : btnEdit
                                
                            ]);
                        }
                    }
                ],
                
            }
        },
        created() {
            
        },
        mounted() {
            // 获取列表
            this.getLimitRuleList()
            // 获取状态
            this.getCommonStatus()
        },
        methods: {
            // 获取应用列表
            getLimitRuleList(){
                this.show_loading_appList = true;
                let  customerNo = this.searchCustomerNo? this.searchCustomerNo : '',
                    appId = this.searchAppId? this.searchAppId : '',
                    status = this.searchStatus? this.searchStatus : '',
                    _pageNo = this.pageNo;
                let params = {
                    _pageNo,
                    customerNo,
                    appId,
                    status
                };
                api.yop_app_limit_rule_list(params,this.searchType).then(
                    (response) =>{
                        this.show_loading_appList = false;
                        if(response.data.status === 'success'){
                            this.data_AppList = response.data.data.page.items;
                            this.pageNo = response.data.data.page.pageNo;
                            if(response.data.data.page.items){
                                if(response.data.data.page.items.length < 10){
                                    this.pageTotal=response.data.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            // 获取状态
            getCommonStatus(){
                let params = {};
                api.yop_app_limit_rule_commons_status(params).then(
                    (response) =>{
                        if(response.data.status === 'success'){
                            this.appStatusList = [];
                            response.data.data.result.forEach(item => {
                                this.appStatusList.push({
                                    label: item.name,
                                    value: item.code
                                })
                            });
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            
            changeAppStatus(val){
            },
            // 查询列表
            searchAppList(page){
                this.show_loading_appList = true;
                let  customerNo = this.searchCustomerNo? this.searchCustomerNo : '',
                    appId = this.searchAppId? this.searchAppId : '',
                    status = this.searchStatus? this.searchStatus : '',
                    _pageNo = page;
                let params = {
                    _pageNo,
                    customerNo,
                    appId,
                    status
                };
                api.yop_app_limit_rule_list(params,this.searchType).then(
                    (response) =>{
                        this.show_loading_appList = false;
                        if(response.data.status === 'success'){
                            this.data_AppList = response.data.data.page.items;
                            this.pageNo = response.data.data.page.pageNo;
                            if(response.data.data.page.items){
                                if(response.data.data.page.items.length < 10){
                                    this.pageTotal=response.data.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            // 重置查询
            resetAppList(){
                this.searchCustomerNo = ''
                this.searchAppId = ''
                this.searchStatus = ''
                this.$refs.select_search_status.clearSingleSelect();
            },
            // 启用 禁用  删除弹框确认
            confirmDisableEnableDel(type){
                if(!this.commonCause || this.commonCause == ""){
                    this.$Message.warning('请输入原因');
                    return false;
                }
                let params = {
                    id: this.currentAppInfo.id,
                    cause: this.commonCause
                };
                if(type == '确认启用'){
                    api.yop_app_limit_rule_enable(params,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.disableEnableDelFlag = false
                                this.commonCause = ""
                                this.$ypMsg.notice_success(this,'启用成功');
                                this.getLimitRuleList()
                            }else{
                                this.disableEnableDelFlag = false
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }
                if(type == '确认禁用'){
                    api.yop_app_limit_rule_disable(params,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.disableEnableDelFlag = false
                                this.commonCause = ""
                                this.$ypMsg.notice_success(this,'禁用成功');
                                this.getLimitRuleList()
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }
                if(type == '确认删除'){
                    api.yop_app_limit_rule_delete(params,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.disableEnableDelFlag = false
                                this.commonCause = ""
                                this.$ypMsg.notice_success(this,'删除成功');
                                this.getLimitRuleList()
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }
                
            },
            //弹框取消
            cancelModel(type){
                this.commonCause = ""
                if(type === 'disableEnableDelFlag'){
                    this.disableEnableDelFlag = false
                }
            },
            // 启用 应用
            activeIp(row){
                
            },
            // 禁用 应用
            disableIp(row){

            },
            // 修改 应用
            editApp(){
                this.addEditApp('edit')
            },
            // 删除 应用
            deleteApp(type,params){
                if(type === 'ip'){
                    this.data_whiteList.splice(params.index,1)
                }else{

                }
            },
            // 校验应用是否存在
            appIsExist(){
                if(!this.appId || this.appId == ""){
                    return false
                }
                api.yop_app_exists({
                    appId: this.appId.trim()
                }).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            let result = response.data.data.result;
                            if (result) {
                            } else {
                                this.$Message.error('该应用标识不存在');
                            }
                        } else {
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                );
            },
            // 新增编辑 应用
            addEditApp(type){
                this.addEditWhiteFlag = true
                if(type === 'add'){
                    this.addEditTitle = '新增白名单'
                    this.data_whiteList = []
                }else{
                    this.addEditTitle = '编辑白名单'
                    this.addEditWhiteCurrentBar = ''
                    this.getRuleDetail() 
                }
            },
            // 获取白名单详情
            getRuleDetail(){
                let params = {
                    id: this.currentAppInfo.id
                }
                api.yop_app_limit_rule_detail(params,this.searchType).then(
                    (response) =>{
                        if(response.data.status === 'success'){
                            this.data_whiteList = response.data.data.result.whitelist
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            // 新增编辑 白名单确定
            ok_addEditWhiteList(){
                if(this.addEditTitle === '新增白名单'){
                    if(!this.appId || this.appId == ""){
                        this.$Message.warning('请输入应用标识');
                        return false
                    }
                }
                if(!this.addEditCause){
                    this.$Message.warning('请输入原因');
                    return false
                }
                // 校验至少一条ip或网段
                if(this.data_whiteList.length < 1){
                    this.$Message.warning('至少有一条ip或网段');
                    return false;
                }
                // 校验里面的ip是否有空，有空则不通过
                let checkHasIp = true,
                    ipArr = [];

                this.data_whiteList.forEach(item => {
                    if(!item.ip || item.ip == ""){
                        checkHasIp = false
                        return checkHasIp;
                    }
                });
                if(!checkHasIp){
                    this.$Message.warning('请检查ip');
                    return false;
                }
                // 检查IP是否重复 start
                this.data_whiteList.forEach(item => {
                    ipArr.push(item.ip);
                });
                function isRepeat(arr){
                    let  hash = {};
                    for(let i in arr) {
                        if(hash[arr[i]]) {
                            return true;
                　　     }
                        hash[arr[i]] = true;
                    }
                    return false;
                } 
                if(isRepeat(ipArr)){
                    this.$Message.warning('ip有重复，请检查');
                    return false;
                }
                // 检查IP是否重复 end
                // 发送新增请求
                if(this.addEditTitle === '新增白名单'){
                    let params = {
                        appId: this.appId,
                        whitelist: this.data_whiteList,
                        cause: this.addEditCause
                    };
                    api.yop_app_limit_rule_create(params,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.appId = ''
                                this.addEditWhiteFlag = false
                                this.addEditCause = ''
                                this.$ypMsg.notice_success(this,'新增成功');
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }else{
                    let params = {
                        id: this.currentAppInfo.id,
                        whitelist: this.data_whiteList,
                        cause: this.addEditCause
                    };
                    api.yop_app_limit_rule_update(params,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.appId = ''
                                this.addEditWhiteFlag = false
                                this.addEditCause = ''
                                this.$ypMsg.notice_success(this,'修改成功');
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }
                
            },
            // 新增编辑 白名单取消
            cancle_addEditWhiteList(){
                this.addEditWhiteFlag = false
                this.appId = ''
                this.addEditCause = ''
            },
            // 新增ip或网段
            addIp(){
                this.data_whiteList.push({
                    ip: '' ,
                    description: '',
                    status: 'ENABLED',
                });
                this.addEditWhiteCurrentBar = this.data_whiteList.length - 1
            },
            // 保存编辑
            handleSave (params) {
                // ip FF01::1101
                let isIpFlag = true;
                this.data_whiteList.forEach((item,index) => {
                    if(params.index == index){
                        if(!this.addEditWhiteCurrentIp){
                            this.addEditWhiteCurrentIp = params.row.ip
                        }
                        if(!this.addEditWhiteCurrentDes){
                            this.addEditWhiteCurrentDes = params.row.description
                        }
                        // 校验ip
                        if(util.checkIP(this.addEditWhiteCurrentIp)){
                            
                        }else{
                            isIpFlag = false;
                            return isIpFlag;
                        }
                        item.ip = this.addEditWhiteCurrentIp
                        item.description = this.addEditWhiteCurrentDes
                    }
                    
                });
                if(!isIpFlag){
                    this.$Message.warning('ip或网段格式不正确');
                    return false;
                }
                this.addEditWhiteCurrentBar = ''
                this.addEditWhiteCurrentDes = ''
                this.addEditWhiteCurrentIp = ''
            }
        },
    }
</script>
