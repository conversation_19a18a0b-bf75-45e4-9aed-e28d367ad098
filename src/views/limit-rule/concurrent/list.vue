<style lang="less" scoped>
    @import "../../../styles/common.less";
</style>
<template>
    <div style="background: #eee;padding:8px">
         <Card :bordered="false">
             <Row type="flex" align="middle">
                 <!-- 搜索条件 -->
                <Col span="24">
                    <Col span="5" >
                        <Col span="8" class="margin-top-10" >商编：&nbsp;</Col>
                        <Col span="14"> 
                            <Input
                                id="inputCustomerNo"
                                class="margin-top-5"
                                v-model="searchCustomerNo"
                                placeholder="商编"
                                clearable
                            ></Input> 
                        </Col>
                    </Col>
                    
                    <Col span="5" >
                        <Col span="8" class="margin-top-10" >应用标识：&nbsp;</Col>
                        <Col span="14"> 
                            <Input
                                id="inputAppBar"
                                class="margin-top-5"
                                v-model="searchAppId"
                                placeholder="应用标识"
                                clearable
                            ></Input> 
                        </Col>
                    </Col>

                    <Col span="5" >
                        <Col span="8" class="margin-top-10" >请求路径：&nbsp;</Col>
                        <Col span="14"> 
                            <Input
                                id="inputPath"
                                class="margin-top-5"
                                v-model="searchPath"
                                placeholder="请求路径"
                                clearable
                            ></Input> 
                        </Col>
                    </Col>
                    
                    <Col span="5" >
                        <Col span="8" class="margin-top-10" >状态：&nbsp;</Col>
                        <Col span="14"> 
                            <Select class="margin-top-5" ref="select_search_status"  id='select_apiM_1' @on-change="changeAppStatus" v-model="searchStatus" placeholder="请选择（默认全部）" clearable>
                                <Option v-for="item in appStatusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                            </Select>
                        </Col>
                    </Col>

                    <!-- 查询按钮 -->
                    <Col class="margin-top-5" span="4" style="text-align: center">
                        <Button class="margin-right-20" id='search_concurrent_1'  type="primary" @click="getLimitRuleList">查询</Button>
                        <Button  id='search_concurrent_2' type="ghost" @click="resetAppList">重置</Button>
                    </Col>
                </Col>
                <Button class="margin-top-10" id="addCon" type="primary" @click="addEditApp('add')">新增并发数</Button>
            </Row>
            
            <!-- 应用列表 table部分 -->
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_ag_1' border ref="selection" :columns="columns_AppList" :data="data_AppList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="searchAppList"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_appList"></loading>
            </Row>

            <!-- 新增编辑黑、并发数 弹框 -->
            <Modal id="modal_alert_1" v-model="addEditWhiteFlag" width="800" :closable="false" :mask-closable="false">
                <p slot="header" style="color:#2d8cf0;height:auto">
                    <span style="color:block;">{{addEditTitle}}</span>
                </p>
                <div>
                    <Row class="margin-bottom-10"  v-if="addEditTitle != '新增并发数'">
                        <Col span="20">
                            <Col span="3">
                                商编：
                            </Col>
                            <Col span="15">
                                {{currentAppInfo.customerNo ? currentAppInfo.customerNo : '无'}}
                            </Col>
                        </Col>
                    </Row>

                    <Row>
                        <Row class="margin-bottom-10"  v-if="addEditTitle == '编辑并发数'">
                            <Col span="20">
                                <Col span="3">
                                    应用标识：
                                </Col>
                                <Col span="15">
                                    {{currentAppInfo.appId ? currentAppInfo.appId : '无'}}
                                </Col>
                            </Col>
                        </Row>
                        
                        <Row class="margin-bottom-10" v-if="addEditTitle == '新增并发数'">
                            <Col span="20">
                                <Col span="3">
                                    应用标识：
                                </Col>
                                <Col span="15">
                                   <Input
                                        style="width: 200px;font-size: 12x;"
                                        id="inputCustomerId"
                                        @on-blur="appIsExist"
                                        v-model="appId"
                                        placeholder="应用标识"
                                        clearable
                                    ></Input> 
                                </Col>
                            </Col>
                        </Row>

                        <Row class="margin-bottom-10">
                            <Col span="20">
                                <Col span="3">
                                    方法：
                                </Col>
                                <Col span="15">
                                    <Select :disabled="disableFlag" :clearable="!disableFlag" ref="select_getPost_method" style="width: 200px;"  id='select_apiM_2' @on-change="changeGetPost" v-model="addEditMethod" placeholder="请选择" >
                                        <Option  v-for="item in postGetList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                                    </Select>
                                </Col>
                            </Col>
                        </Row>

                        <Row class="margin-bottom-10">
                            <Col span="20">
                                <Col span="3">
                                    路径：
                                </Col>
                                <Col span="15">
                                   <Input id="concuId_1" 
                                        v-model="addEditPath" 
                                        @on-blur="apiIsExist"
                                        style="width: 200px;" 
                                        placeholder="" 
                                        :disabled="disableFlag" 
                                        :clearable="!disableFlag"
                                        >
                                    </Input>
                                    <!-- <p style="color:gray">eg：/rest/v1.0/{api-group}/{api}  推荐 中划线 连接单词</p> -->
                                </Col>
                            </Col>
                        </Row>

                        <Row class="margin-bottom-10">
                            <Col span="20">
                                <Col span="3">
                                    <span style="color:red;font-size: 12x;">*</span>并发数：
                                </Col>
                                <Col span="15">
                                   <Input id="concuId_2" 
                                        style="width: 200px;" 
                                        v-model="rate" 
                                        placeholder="请输入正整数" 
                                        clearable>
                                   </Input>
                                </Col>
                            </Col>
                        </Row>

                        <Row class="margin-bottom-10">
                            <Col span="20">
                                <Col span="3">
                                    <span style="color:red;font-size: 12x;">*</span>原因：
                                </Col>
                                <Col span="15">
                                    <Input id="concuId_3"
                                        v-model="addEditCause" 
                                        style="width:285px;font-size: 12x;" 
                                        placeholder="请输入原因" 
                                        placeholder-style="font-size:12px;">
                                    </Input>
                                </Col>
                            </Col>
                        </Row>
                    </Row>
                    
                </div>
                <div slot="footer">
                    <Button id="modal_btn_1" type="primary" @click="ok_addEditWhiteList">确定</Button>
                    <Button id="modal_btn_2" type="ghost" @click="cancle_addEditWhiteList">关闭</Button>
                </div>
            </Modal>

            <!-- 启用 禁用  删除 弹框 -->
            <Modal v-model="disableEnableDelFlag" :closable="false" :mask-closable="false">
                <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
                    <span>{{disableEnableDelTitle}}</span>   
                </p>
                <div style="text-align:left;font-size: 12px;">
                    <div style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;">
                        <i class="ivu-icon ivu-icon-help-circled"></i>
                    </div>
                    <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                        <p>{{disableEnableDelTitle}}吗？</p>
                        <label for="" class="reasonType"><span style="color:red;font-size: 12x;">*</span>原因：</label>
                        <Input type="textarea" id="concuId_4" v-model="commonCause" style="width:85%;font-size: 12x;margin-top:8px;" placeholder="" placeholder-style="font-size:12px;"></Input>
                    </div>
                </div>
                <div slot="footer" style="border:0;">
                    <Button id="model_btn_3" type="ghost"  @click="cancelModel('disableEnableDelFlag')">取消</Button>
                    <Button id="model_btn_4" type="primary"  @click="confirmDisableEnableDel(disableEnableDelTitle)">确定</Button>
                </div>
            </Modal>
         </Card>
    </div>
</template>

<script>
import loading from '../../my-components/loading/loading'
import api from "../../../api/api";
import util from "../../../libs/util"
    export default {
        name: 'limit_rule_concurrentList',
        components:{
            loading
        },
        data() {
            return {
                // 搜索条件 商编
                searchCustomerNo: '',
                // 应用标识
                searchAppId: '',
                // 状态
                searchStatus: '',
                // 类型
                searchType: 'concurrent',
                // 路径
                searchPath: '',
                // 新增应用时 应用标识
                appId: '',
                // 状态列表
                appStatusList: [],
                // loading
                show_loading_appList: false,
                show_loading_whiteList: false,
                // 分页总数
                pageTotal: 0,
                // 当前页
                pageNo: 1,
                // 删除 启用 禁用二次确认弹框标识
                disableEnableDelFlag: false,
                // 删除 启用 禁用二次确认弹框 标题
                disableEnableDelTitle: '',
                // 操作原因
                commonCause: '',
                // 新增和编辑原因
                addEditCause: '',
                // 编辑新增 标题
                addEditTitle:'',
                // 当前app 节点信息
                currentAppInfo: {
                    // 商编
                    // customerNo: '12ioij2388',
                    // appId: 'aap_lkdfjas'
                },
                // 禁止修改标识
                disableFlag: false,
                // 当前并发数字段信息
                currentIpInfo:{},
                // 方法
                postGetList:[
                    {
                        label: 'POST',
                        value: 'POST'
                    },
                    {
                        label: 'GET',
                        value: 'GET'
                    },
                    {
                        label: 'PUT',
                        value: 'PUT'
                    }
                ],

                // 新增编辑并发数 开关
                addEditWhiteFlag: false,
                // 新增ip/网段标题
                addEditIpTitle: '',
                // 请求方法
                addEditMethod: '',
                // 请求路径
                addEditPath: '',
                // 并发数
                rate: '',

                // 应用列表数据
                data_AppList:[],
                // 应用列表标题
                columns_AppList:[
                    {
                        title: '商编',
                        key: 'customerNo',
                        align: 'center'
                    },
                    {
                        title: '应用标识',
                        key: 'appId',
                        align: 'center'
                    },
                    {
                        title: '方法/请求路径',
                        align: 'center',
                        width: 240,
                        render:(h,params) =>{
                            return h("div",[
                                h('Tag',{
                                    style:{
                                        display: `${params.row.requestMethod ? 'inline-block' : 'none'}`,
                                        align: 'center'
                                    },
                                    props:{
                                        color: 'blue'
                                    }
                                },params.row.requestMethod),
                                h('span',params.row.requestPath),
                            ])
                        }
                    },
                    {
                        title: '状态',
                        key: 'status',
                        width: 100,
                        align: 'center',
                        render:(h,params) =>{
                            return h("div",[
                                h('p', params.row.status == "ENABLED" ? '已启用' : '已禁用'),
                            ])
                        }
                    },
                    {
                        renderHeader: (h, params) => {
                            return h("div", [h("p", "创建时间"), h("p", "最后修改时间")]);
                        },
                        width: 180,
                        align: "center",
                        render: (h, params) => {
                            return h("div", [
                                h("p", params.row.createdDate),
                                h("p", params.row.lastModifiedDate)
                            ]);
                        }
                    },
                    {
                        title: "操作",
                        align: "center",
                        key: "operations",
                        render: (h, params) => {
                            return h("div", [
                                h("Button",{
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "none" : 'inline-block'}`
                                    },
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/limit-rule/concurrent/enable'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.currentAppInfo = params.row
                                            this.disableEnableDelFlag = true
                                            this.disableEnableDelTitle = '确认启用'
                                        }
                                    }
                                },"启用"),

                                h("Button",{
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "inline-block" : 'none'}`
                                    },
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/limit-rule/concurrent/disable'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.currentAppInfo = params.row
                                            this.disableEnableDelFlag = true
                                            this.disableEnableDelTitle = '确认禁用'
                                        }
                                    }
                                }, "禁用"),

                                h("Button",{
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/limit-rule/concurrent/update'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.disableFlag = true
                                            this.currentAppInfo = params.row
                                            this.editApp(params.row)
                                        }
                                    }
                                },"修改"),

                                h("Button",{
                                    style:{
                                        'display': `${params.row.status == "ENABLED" ? "none" : 'inline-block'}`
                                    },
                                    props: {
                                        type: "text",
                                        size: "small"
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/limit-rule/concurrent/delete'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.currentAppInfo = params.row
                                            this.disableEnableDelFlag = true
                                            this.disableEnableDelTitle = '确认删除'
                                        }
                                    }
                                },"删除"),
                            ]);
                        }
                    }
                ],
                
            }
        },
        created() {
            
        },
        mounted() {
            // 获取列表
            this.getLimitRuleList()
            // 获取状态
            this.getCommonStatus()
        },
        methods: {
            // 获取应用列表
            getLimitRuleList(){
                this.show_loading_appList = true;
                let  customerNo = this.searchCustomerNo? this.searchCustomerNo : '',
                    appId = this.searchAppId? this.searchAppId : '',
                    status = this.searchStatus? this.searchStatus : '',
                    requestPath = this.searchPath? this.searchPath : '',
                    _pageNo = this.pageNo;
                let params = {
                    _pageNo,
                    customerNo,
                    appId,
                    status,
                    requestPath
                };
                api.yop_app_limit_rule_list(params,this.searchType).then(
                    (response) =>{
                        this.show_loading_appList = false;
                        if(response.data.status === 'success'){
                            this.data_AppList = response.data.data.page.items;
                            this.pageNo = response.data.data.page.pageNo;
                            if(response.data.data.page.items){
                                if(response.data.data.page.items.length < 10){
                                    this.pageTotal=response.data.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            // 获取状态
            getCommonStatus(){
                let params = {};
                api.yop_app_limit_rule_commons_status(params).then(
                    (response) =>{
                        if(response.data.status === 'success'){
                            this.appStatusList = [];
                            response.data.data.result.forEach(item => {
                                this.appStatusList.push({
                                    label: item.name,
                                    value: item.code
                                })
                            });
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            
            changeAppStatus(val){
                
            },
            changeGetPost(val){
                
            },
            // 查询列表
            searchAppList(page){
                this.show_loading_appList = true;
                let  customerNo = this.searchCustomerNo? this.searchCustomerNo : '',
                    appId = this.searchAppId? this.searchAppId : '',
                    status = this.searchStatus? this.searchStatus : '',
                    requestPath = this.searchPath? this.searchPath : '',
                    _pageNo = page;
                let params = {
                    _pageNo,
                    customerNo,
                    appId,
                    status,
                    requestPath
                };
                api.yop_app_limit_rule_list(params,this.searchType).then(
                    (response) =>{
                        this.show_loading_appList = false;
                        if(response.data.status === 'success'){
                            this.data_AppList = response.data.data.page.items;
                            this.pageNo = response.data.data.page.pageNo;
                            if(response.data.data.page.items){
                                if(response.data.data.page.items.length < 10){
                                    this.pageTotal=response.data.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            // 重置查询
            resetAppList(){
                this.searchCustomerNo = ''
                this.searchAppId = ''
                this.searchStatus = ''
                this.searchPath = ''
                this.$refs.select_search_status.clearSingleSelect();
            },
            // 启用 禁用  删除弹框确认
            confirmDisableEnableDel(type){
                if(!this.commonCause || this.commonCause == ""){
                    this.$Message.warning('请输入原因');
                    return false;
                }
                let params = {
                    id: this.currentAppInfo.id,
                    cause: this.commonCause
                };

                if(type == '确认启用'){
                    api.yop_app_limit_rule_enable(params,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.disableEnableDelFlag = false
                                this.commonCause = ""
                                this.$ypMsg.notice_success(this,'启用成功');
                                this.getLimitRuleList()
                            }else{
                                this.disableEnableDelFlag = false
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }
                if(type == '确认禁用'){
                    api.yop_app_limit_rule_disable(params,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.disableEnableDelFlag = false
                                this.commonCause = ""
                                this.$ypMsg.notice_success(this,'禁用成功');
                                this.getLimitRuleList()
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }
                if(type == '确认删除'){
                    api.yop_app_limit_rule_delete(params,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.disableEnableDelFlag = false
                                this.commonCause = ""
                                this.$ypMsg.notice_success(this,'删除成功');
                                this.getLimitRuleList()
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }
                
            },
            //弹框取消
            cancelModel(type){
                this.commonCause = ""
                if(type === 'disableEnableDelFlag'){
                    this.disableEnableDelFlag = false
                }
            },
            // 修改 应用
            editApp(){
                this.addEditApp('edit')
            },
            // 校验应用是否存在
            appIsExist(){
                if(!this.appId){
                    return false
                }
                api.yop_app_exists({
                    appId: this.appId.trim()
                }).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            let result = response.data.data.result;
                            if (result) {
                            } else {
                                this.$Message.error('该应用标识不存在');
                            }
                        } else {
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                );
            },
            // 校验api 是否存在
            apiIsExist(){
                if(!this.addEditPath){
                    return false
                }
                api.yop_api_exists({
                    httpMethod: this.addEditMethod,
                    path: this.addEditPath.trim()
                }).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            let result = response.data.data.result;
                            if (result) {
                            } else {
                                this.$Message.error('该应api不存在');
                            }
                        } else {
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                );
            },
            // 新增编辑 应用
            addEditApp(type){
                this.addEditWhiteFlag = true
                if(type === 'add'){
                    this.disableFlag = false
                    this.addEditTitle = '新增并发数'
                    this.addEditMethod = ''
                    this.addEditPath = ''
                    this.rate = ''
                }else{
                    this.addEditTitle = '编辑并发数'
                    this.addEditWhiteCurrentBar = ''
                    this.getRuleDetail() 
                }
            },
            // 获取并发数详情
            getRuleDetail(){
                this.addEditPath = this.currentAppInfo.requestPath
                this.addEditMethod = this.currentAppInfo.requestMethod
                this.appId = this.currentAppInfo.appId
                let params = {
                    id: this.currentAppInfo.id
                }
                api.yop_app_limit_rule_detail(params,this.searchType).then(
                    (response) =>{
                        if(response.data.status === 'success'){
                            this.rate = response.data.data.result.rate
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            // 新增编辑 并发数确定
            ok_addEditWhiteList(){
                // 应用标识 和 api 不能同时为空 
                if(!this.appId && !this.addEditPath){
                    this.$Message.warning('应用标识和api不能同时为空');
                    return false
                }
                // 校验路径格式
                // let str = this.addEditPath.trim();
                // if(!(/\/(rest|yos)\/v([1-9][0-9]*)\.(0|[1-9][0-9]*)\/([a-z][a-z\-]*)*[a-z]+(\/([a-z][a-z\-]*)*[a-z]+)+$/.test(str))){
                //     this.$Message.warning('路径格式不对请检查');
                //     return false;
                // }
                
                
                // 并发数必填
                if(!this.rate){
                    this.$Message.warning('请输入并发数');
                    return false
                }
                
                // 原因必填
                if(!this.addEditCause){
                    this.$Message.warning('请输入原因');
                    return false
                }
                // 并发数 为正整数
                let reg = /^[1-9]\d*$/;
                if(!reg.test(this.rate)){
                    this.$Message.warning('并发数为正整数，请检查');
                    return false;
                }
                // 发送新增请求
                if(this.addEditTitle === '新增并发数'){
                    let params = {
                        appId: this.appId,
                        requestMethod: this.addEditMethod,
                        requestPath: this.addEditPath,
                        rate: Number(this.rate),
                        cause: this.addEditCause,
                    };
                    api.yop_app_limit_rule_create(params,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.addEditWhiteFlag = false
                                this.$ypMsg.notice_success(this,'新增成功');
                                this.clearParams()
                                this.getLimitRuleList()
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }else{
                    let params = {
                        id: this.currentAppInfo.id,
                        requestMethod: this.addEditMethod,
                        requestPath: this.addEditPath,
                        rate: Number(this.rate),
                        cause: this.addEditCause,
                    };
                    api.yop_app_limit_rule_update(params,this.searchType).then(
                        (response) =>{
                            if(response.status === 'success'){
                                this.addEditWhiteFlag = false
                                this.$ypMsg.notice_success(this,'修改成功');
                                this.clearParams()
                                this.getLimitRuleList()
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }
                
            },
            // 新增编辑 并发数取消
            cancle_addEditWhiteList(){
                this.addEditWhiteFlag = false
                this.clearParams()
            },
            // 清空编辑框参数
            clearParams(){
                this.appId = ''
                this.addEditPath = ''
                this.addEditMethod = ''
                this.rate = ''
                this.addEditCause = ''
            }
        },
    }
</script>
