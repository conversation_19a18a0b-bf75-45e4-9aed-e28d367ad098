<style lang="less">
    @import '../../styles/common.less';
    @import '../API_Management_Open/api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card dis-cover :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">

                <Col span="7">
                <Col span="8" class="margin-top-10">
                <span>附件名称:</span>
                </Col>
                <Col span="16">
                <Input id='input_attachment_1' class="margin-top-5" v-model="data_filename" placeholder="附件名称"
                       @on-enter="search_interface(false)"></Input>
                </Col>
                </Col>

                <Col offset="1" span="7">
                <Col span="8" class="margin-top-10">
                <span>附件类型:</span>
                </Col>
                <Col span="16">
                <common-select id='select_attachment_1' ref="select_file_type" @on-update="updateSelect_attachment"
                               holder=" 请选择（默认全部）"
                               type="sub"
                               keyWord="result"
                               group="attachment_fileType"
                               code="code"
                               title="name"
                               subCode="formats"
                               @on-loaded="select_callBack"
                               :default="this.data_select_file_type"
                               :uri="this.$store.state.select.attachment_fileType.uri"></common-select>
                </Col>
                </Col>

                <Col offset="1" span="7">
                <Col span="8" class="margin-top-10">
                <span>附件编号:</span>
                </Col>
                <Col span="16">
                <Input id='input_attachment_2' class="margin-top-5" v-model="data_file_address" placeholder="附件编号"
                       @on-enter="search_interface(false)"></Input>
                </Col>
                </Col>

                </Col>

                <Col span="4">
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_attachment_1' type="primary" v-url="{url:'/rest/attachments/list'}" @click="search_interface(false)" >查询</Button>
                </Col>
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_attachment_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Button id='btn_attachment_3' class="margin-right-10" v-url="{url:'/rest/attachments/upload'}" :disabled="disabled_upload" type="primary" @click="upload_attachments">上传附件</Button>
                </Col>
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_attachment_1' border :columns="columns_attachmentList" :data="data_attachmentList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10"
                          :current="pageNo" show-elevator @on-change="search_interface"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_attachmentList"></loading>
            </Row>
            <Modal id="modal_attachment_1" v-model="modal_upload" width="500" :closable="false" :mask-closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" v-show="create_edit">上传附件</span>
                    <span style="color:black" v-show="!create_edit">编辑附件</span>
                </p>
                <div>
                    <Form ref="formCustom" :model="formCustom" :rules="ruleCustom" :label-width="90">

                        <FormItem label="上传附件">
                            <Upload
                                    v-if="create_edit"
                                    ref="upload"
                                    style="width:85%;"
                                    :action="specificAction"
                                    type="drag"
                                    :name="name_upload"
                                    :show-upload-list="true"
                                    :data="upload_data_static"
                                    :headers="header_upload"
                                    :format="format_limit"
                                    :on-format-error="handleFormatError"
                                    :before-upload="handleBeforeUpload_modal"
                                    :on-progress="handleProgress"
                                    :on-success="handleSuccess"
                                    :on-error="handleError"
                            >
                                <div style="padding: 20px 0" >
                                    <Icon type="ios-cloud-upload" size="20" style="color: #3399ff"></Icon>
                                    <p>点击这里或拖拽上传附件</p>
                                </div>
                            </Upload>
                            <Upload
                                    v-if="!create_edit"
                                    ref="upload"
                                    style="width:85%;"
                                    :action="specificAction2"
                                    type="drag"
                                    :name="name_upload"
                                    :show-upload-list="true"
                                    :data="upload_data_static"
                                    :headers="header_upload"
                                    :format="format_limit"
                                    :on-format-error="handleFormatError"
                                    :before-upload="handleBeforeUpload_modal"
                                    :on-progress="handleProgress"
                                    :on-success="handleSuccess"
                                    :on-error="handleError"
                            >
                                <div style="padding: 20px 0" >
                                    <Icon type="ios-cloud-upload" size="20" style="color: #3399ff"></Icon>
                                    <p>点击这里或拖拽上传附件</p>
                                </div>
                            </Upload>
                        </FormItem>
                        <FormItem label="附件名称：" prop="fileName">
                            <Input  size="small" v-model="formCustom.fileName" style="width:85%"></Input>
                        </FormItem>
                        <p class="yop-explain-100">最大支持输入30字符</p>
                        <FormItem label="附件格式：" v-show="!create_edit">
                            <div style="width:85%;word-wrap: break-word;">{{formCustom.fileType}}</div>
                        </FormItem>
                        <FormItem label="附件编号：" v-show="!create_edit">
                            <div style="width:85%;word-wrap: break-word;">{{formCustom.fileUrl}}</div>
                        </FormItem>
                    </Form>
                </div>
                <div slot="footer">
                    <Button id="modal_request_btn_2" type="primary" @click="ok_upload('formCustom')">确定</Button>
                    <Button id="modal_request_btn_1" type="ghost"  @click="cancel_upload">取消</Button>
                </div>
                <loadingFinal :content="loading_upload_content" :show="show_loading_upload"></loadingFinal>
            </Modal>
        </Card>
    </div>
</template>

<script>
    import loading from '../my-components/loading/loading'
    import loadingFinal from '../my-components/loading/loadingFinal'
    import commonSelect from '../common-components/select-components/selectCommon'
    import util from '../../libs/util'
    import api from '../../api/api'
    import Clipboard from "clipboard"
    export default {
        name: 'attachments-list',
        components:{
            loading,
            commonSelect,
            loadingFinal
        },
        data(){
            const validate_name = (rule, value, callback) => {
                if(util.format_check_common(value,/^[0-9\u4e00-\u9fa5a-zA-Z-_]+?$/)){
                    callback(new Error('格式不正确'));
                }else if(util.getLength(value) > 30){
                    callback(new Error('长度不能大于30'));
                }else{
                    callback();
                }
            };
            return {
                /**
                 * 主页面
                 */
                // 所有禁用项目
                total_limit: [],
                // 上传按钮禁用
                disabled_upload : true,
                // 列表loading
                show_loading_attachmentList : false,
                // 附件名称输入框数据绑定
                data_filename: '',
                // 下拉框个数
                count_select_related : 1,
                // 当前选定的数据类型绑定
                data_select_file_type : '' ,
                // 附件地址输入框数据绑定
                data_file_address : '',
                // 附件列表表头数据绑定
                columns_attachmentList : [
                    {
                        title: '附件名称',
                        width: 200,
                        key: 'fileName',
                        align: 'center'
                    },
                    {
                        title: '附件类型',
                        width: 130,
                        key: 'fileType',
                        align: 'center'
                    },
                    {
                        title: '附件编号',
                        key: 'fileAddress',
                        'min-width': 200,
                        align: 'center'
                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ]);
                        },
                        width: 160,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [h('p', params.row.createTime),
                                h('p', params.row.lastModifyTime)]);
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        'width': 200,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/attachments/modify'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.attachment_modify(params.row.id);
                                        }
                                    }
                                }, '编辑'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/attachments/delete'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.attachment_delete(params.row.id);
                                        }
                                    }
                                }, '删除'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    on: {
                                        click: () => {
                                            this.link_copy(params.row);
                                        }
                                    }
                                }, '复制链接')
                            ]);
                        }

                    }
                ],
                // 附件列表内容数据绑定
                data_attachmentList :[],
                // 列表总数目
                pageTotal : 1,
                // 当前页数
                pageNo : 1,
                /**
                 * 附件上传/编辑接口
                 */
                // 上传还是修改
                create_edit : true,
                // 附件类型上传限制
                format_limit: [],
                // 上传表单数据绑定
                formCustom: {
                    id: '',
                    fileName : '',
                    fileType : '',
                    fileUrl: ''
                },
                ruleCustom: {
                    fileName :[
                        { validator: validate_name, trigger: 'blur' }
                    ]
                },
                specificAction : localStorage.remoteIP+'/rest/attachments/upload',
                specificAction2 : localStorage.remoteIP+'/rest/attachments/modify',
                // 上传窗口显示绑定
                modal_upload :false,
                // 上传名称绑定
                name_upload:'fileStream',
                // 上传的数据
                upload_data_static: {
                    id : '',
                    fileName : ''
                },
                // 上传的header
                header_upload:{
                    Authorization: 'Bearer ' + localStorage.accesstoken
                },
                uploadFile: [], // 需要上传的附件List,
                // 附件上传loading
                show_loading_upload : false,
                // 加载动画内容
                loading_upload_content : ''
            }
        },
        methods:{
            /**
             * 主页面
             */
            // 页面初始化函数
            init () {
                if(this.$store.state.select.attachment_fileType.data && this.$store.state.select.attachment_fileType.data.length>0){
                    this.disabled_upload =false;
                }
            },
            // 查询附件列表接口
            search_interface (val){
                this.show_loading_attachmentList = true;
                let params = {
                    fileName: this.data_filename.trim(),
                    fileType: this.data_select_file_type,
                    fileId: this.data_file_address.trim(),
                    _pageNo: 1,
                    _pageSize: 10
                };
                if (val) {
                    params._pageNo = val;
                }
                util.paramFormat(params);
                api.yop_attachment_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            if(response.data.data.result.items && response.data.data.result.items.length > 0){
                                if(response.data.data.result.items.length < 10){
                                    this.pageTotal=response.data.data.result.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                        }
                        this.show_loading_attachmentList = false;
                    }
                );
            },
            // 列表数据处理
            tableDataFormat (items){
                this.data_attachmentList = [];
                for (var i in items) {
                    this.data_attachmentList.push({
                        id : util.empty_handler(items[i].id),
                        fileName: util.empty_handler(items[i].fileName),
                        fileTypeCode : util.empty_handler(items[i].fileType),
                        fileType : util.empty_handler(this.data_name_handler(items[i].fileType, 'attachment_fileType')),
                        fileAddress : util.empty_handler(items[i].fileId),
                        fileUrl : util.empty_handler(items[i].fileUrl),
                        createTime : util.empty_handler(items[i].createdDate),
                        lastModifyTime : util.empty_handler(items[i].lastModifiedDate),
                    });
                }
            },
            // 返回相应编码的名字
            data_name_handler (code, name){
                if (code) {
                    let group = this.$store.state.select[name].data;
                    for (var i in group) {
                        if (group[i].value === code) {
                            return group[i].name;
                        }
                    }
                } else {
                    return '';
                }
            },
            // 重置附件列表查询条件
            reset_Interface (){
                this.data_filename = '';
                this.data_file_address = '';
                this.$refs.select_file_type.resetSelected();
                this.data_select_file_type = '';
            },
            // 下拉框回调函数
            select_callBack (){
                this.format_limit = [];
                this.total_limit = [];
                let data_fileType = this.$store.state.select.attachment_fileType.data;
                data_fileType.forEach(
                    (item) =>{
                        if(item.subItems && item.subItems.length > 0){
                            item.subItems.forEach(
                                (subItem) =>{
                                    this.format_limit.push(subItem);
                                    this.total_limit.push(subItem);
                                }
                            )
                        }
                    }
                )
                this.disabled_upload = false;
                this.count_select_related--;
                if (this.count_select_related === 0) {
                    this.search_interface();
                }
            },
            // 编辑附件
            attachment_modify (id) {
                this.create_edit =false;
                this.reset_upload();
                this.modal_upload = true;
                this.loading_upload_content = '数据获取中...';
                this.show_loading_upload = true;
                api.yop_attachment_detail({id:id}).then(
                    (response)=>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.detail_format(result)

                        }else{
                            this.$ypMsg.notice_error(this,'附件详细信息获取失败,请刷新重试',response.data.message,response.data.solution);
                            this.modal_upload= false;
                        }
                        this.show_loading_upload = false;
                    }
                )
            },
            // 详细信息处理
            detail_format (result) {
                this.formCustom.fileName = util.empty_handler(result.fileName);
                this.formCustom.fileType = util.empty_handler(this.data_name_handler(result.fileType, 'attachment_fileType'));
                this.formCustom.fileUrl = util.empty_handler(result.fileId);
                this.formCustom.id = util.empty_handler(result.id);
                this.format_limit = this.limit_handler(result.fileType);
            },
            // 格式限制处理
            limit_handler(type){
                if (type) {
                    let group = this.$store.state.select.attachment_fileType.data;
                    for (var i in group) {
                        if (group[i].value === type) {
                            return group[i].subItems;
                        }
                    }
                } else {
                    return [];
                }
            },
            // 删除附件
            attachment_delete (id) {
                this.$Modal.confirm({
                    title: '删除附件',
                    content: '<p style=\'color:red\'>删除附件后，则之前链接到此附件地址将无法打开</p><p>确定删除该附件版本？</p>',
                    'ok-text': '确认',
                    onOk: () => {
                        api.yop_attachment_delete({ids: [id]}).then(
                            (response) => {
                                if (response.status === 'success') {
                                    this.$ypMsg.notice_success(this,'附件删除成功');
                                    setTimeout(() => {
                                        this.search_interface();
                                    },1000);
                                } else {
                                    this.$ypMsg.notice_error(this,'附件删除错误',response.message,response.solution);
                                }
                            }
                        );
                    }
                });
            },
            // 复制链接
            link_copy (data) {
                // let container = this.$refs.container
                this.$copyText(data.fileUrl);
                this.$ypMsg.notice_success(this,'复制完成');
                // api.yop_attachment_markdown_copy({id:data.id}).then(
                //     (response)=>{
                //         let status =response.data.status;
                //         if(status === 'success'){
                //             let result = response.data.data.result;
                //             // this.$nextTick(()=>{
                //             //     this.$copyText(result.toString(),container);
                //             // })
                //             this.$copyText(data.fileUrl);
                //             this.$Notice.success({
                //                 title: '成功',
                //                 desc: '复制完成',
                //                 duration: 5
                //             });
                //         }else{
                //             this.$Notice.error({
                //                 title: '复制链接失败,请刷新重试',
                //                 desc: response.data.message,
                //                 duration: 10
                //             });
                //
                //         }
                //     }
                // )

                // if(data.fileTypeCode === 'IMAGE'){
                //     this.$copyText('![]('+data.fileAddress+')<br>', container);
                // }else{
                //     // this.$copyText('['+data.fileName+']('+data.fileAddress+')', container);
                //     this.$copyText('<a href="'+data.fileAddress+'" download="'+data.fileName+'">'+data.fileName+'</a>', container);
                // }
                // this.$Notice.success({
                //     title: '成功',
                //     desc: '复制完成',
                //     duration: 5
                // });

            },

            // 更新下拉框数据
            updateSelect_attachment (val) {
                this.data_select_file_type = val;
            },
            upload_attachments (){
                this.format_limit = this.total_limit;
                this.create_edit =true;
                this.modal_upload = true;
            },

            /**
             * 附件上传/编辑接口
             */
            //附件上传状态绑定函数
            handleFormatError (file) {
                this.$ypMsg.notice_warning(this,'附件 ' + file.name + ' 格式不正确,已自动清空','附件格式不正确');
            },
            handleProgress (event, file) {

            },
            handleSuccess (event, file) {
                let status = event.status
                if(status === 'success'){
                    let temp = ''
                    if(this.create_edit){
                        temp = '上传'
                    }else{
                        temp = '编辑'
                    }
                    this.$ypMsg.notice_success(this,'附件'+file.name+'<br/>'+temp+'成功','附件'+temp+'成功');
                    this.search_interface();
                }else{
                    this.$ypMsg.notice_error(this,'错误',event.message,event.solution);
                }
                this.show_loading_upload = false;
                this.modal_upload =false;
                this.reset_upload();
            },
            handleError (event, file,fileList) {
                let temp = ''
                if(this.create_edit){
                    temp = '上传'
                }else{
                    temp = '编辑'
                }
                this.$ypMsg.notice_error_simple(this,'附件'+temp+'失败','附件 ' + fileList.name + temp+'失败');
                this.show_loading_upload = false;
                this.modal_upload =false;
                this.reset_upload();
            },
            handleBeforeUpload_modal (file) {
                // let keyID = Math.random().toString().substr(2);
                // file['keyID'] = keyID;
                // this.file.push(file);
                // this.uploadFile.push(file)
                let limit = this.formatString_handler();
                let message =''
                if(limit){
                    message = '</br>支持的格式为:<br/><span style="width:252px;word-wrap: break-word">'+limit+'</span>'
                }
                if(this.fileType_check(this.fileType_handler(file.name),this.format_limit) === -1){
                    this.$ypMsg.notice_warning(this,'添加失败，附件格式不正确'+message,'提示');
                }else{
                    this.$ypMsg.notice_info(this,'附件：'+file.name+'<br/>已添加','提示');
                    this.uploadFile = [];
                    this.uploadFile.push(file);
                    if(this.formCustom.fileName === ''){
                        this.formCustom.fileName = this.fileName_handler(file.name);
                    }
                }
                return false;
            },
            // 格式处理
            formatString_handler(){
              if(this.format_limit && this.format_limit.length >0){
                  let result = '';
                  for(var i=0;i<this.format_limit.length;i++){
                      if( i === this.format_limit.length-1){
                          result = result + this.format_limit[i]
                      }else{
                          result = result + this.format_limit[i]+','
                      }
                  }
                  return result;
              }else{
                  return ''
              }
            },
            // 名字处理
            fileName_handler(fileName){
                let arr = fileName.split('.')
                let name = ''
                for(var i = 0;i< arr.length-1;i++){
                    if(i == arr.length-2){
                        name = name + arr[i]
                    }else{
                        name= name+arr[i]+'.';
                    }
                }
                return name;
            },
            // 判断附件格式是否符合
            fileType_check (type,arr){
                if (!Array.indexOf) {
                    Array.prototype.indexOf = function (obj) {
                        for (var i = 0; i < this.length; i++) {
                            if (this[i] == obj) {
                                return i;
                            }
                        }
                        return -1;
                    }
                }
                return arr.indexOf(type);
            },
            // 后缀处理
            fileType_handler (fileName) {
                let arr = fileName.split('.');
                 return arr[arr.length-1];
            },
            // 重置页面
            reset_upload () {
                this.$refs.upload.clearFiles();
                this.formCustom.fileName = '';
                this.formCustom.fileType = '';
                this.formCustom.fileUrl = '';
                this.$refs.formCustom.resetFields();
            },
            // 确定上传
            ok_upload (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {

                            if(this.create_edit){
                                // this.upload_data_static = {
                                //     fileName: this.formCustom.fileName
                                // }
                                if(this.uploadFile.length === 0 ) {
                                    this.$Message.error('未选择上传附件')
                                    return false
                                }else {
                                    this.upload_data_static.fileName = this.formCustom.fileName;
                                    delete this.upload_data_static.id;
                                }
                                // this.specificAction = localStorage.remoteIP+'/rest/attachments/upload';
                            }else{
                                // this.upload_data_static = {
                                //     id: this.formCustom.id,
                                //     fileName: this.formCustom.fileName
                                // }
                                this.upload_data_static.fileName = this.formCustom.fileName;
                                this.upload_data_static.id = this.formCustom.id;
                                if(this.uploadFile.length === 0){
                                    let param = new FormData();
                                    param.append('id',this.formCustom.id);
                                    param.append('fileName',this.formCustom.fileName);
                                    api.yop_attachment_modify(param).then(
                                        (response) =>{
                                            if (response.status === 'success') {
                                                this.$Notice.success({
                                                    title : '附件编辑成功',
                                                    desc : '附件'+this.formCustom.fileName+'<br/>编辑成功',
                                                    duration : 5
                                                });
                                                this.search_interface();
                                            } else {
                                                this.$Notice.error({
                                                    title: '附件编辑失败',
                                                    desc: '附件'+this.formCustom.fileName+'<br/>编辑失败',
                                                    duration : 10
                                                });
                                            }
                                            this.show_loading_upload = false;
                                            this.modal_upload =false;
                                            this.reset_upload();
                                        });
                                }
                                // this.specificAction = localStorage.remoteIP+'/rest/attachments/modify';
                            }
                            if(this.uploadFile.length > 0){
                                for (let i = 0; i < this.uploadFile.length; i++) {
                                    let item = this.uploadFile[i]
                                    this.$refs.upload.post(item);
                                    // this.uploadFile.pop()
                                }
                            }
                            this.uploadFile= [];
                            this.loading_upload_content = '附件上传中..'
                            this.show_loading_upload = true;
                        // this.modal_upload =false;
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 取消上传
            cancel_upload () {
                this.modal_upload=false;
                this.uploadFile= [];
                this.reset_upload();
            },
        },
        mounted () {
            this.init();
        },

    };
</script>

<style scoped>

</style>
