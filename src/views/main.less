.lock-screen-back{
    border-radius: 50%;
    z-index: -1;
    box-shadow: 0 0 0 0 #667aa6 inset;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    transition: all 3s;
}
.main{
    position: absolute;
    width: 100%;
    height: 100%;
    .unlock-con{
        width: 0px;
        height: 0px;
        position: absolute;
        left: 50%;
        top: 50%;
        z-index: 11000;
    }
    .sidebar-menu-con{
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 21;
        transition: width .3s;
    }
    .layout-text{
        display: inline-block;
        white-space:nowrap;
        position: absolute;
    }
    .main-hide-text .layout-text{
        display: none;
    }
    &-content-container{
        position: relative;
    }
    &-header-con{
        box-sizing: border-box;
        position: fixed;
        display: block;
        padding-left: 200px;
        width: 100%;
        //height: 100px;
        height: 60px;
        z-index: 20;
        box-shadow: 0 2px 1px 1px rgba(100,100,100,.1);
        transition: padding .3s;
    }
    &-breadcrumb{
        padding: 8px 15px 0;
    }
    &-menu-left{
        background: #464c5b;
        height: 100%;
    }
    .tags-con{
        height: 40px;
        z-index: -1;
        overflow: hidden;
        background: #f0f0f0;
        .tags-outer-scroll-con{
            position: relative;
            box-sizing: border-box;
            padding-right: 120px;
            width: 100%;
            height: 100%;
            .tags-inner-scroll-body{
                position: absolute;
                padding: 2px 10px;
                overflow: visible;
                white-space: nowrap;
                transition: left .3s ease;
            }
            .close-all-tag-con{
                position: absolute;
                right: 0;
                top: 0;
                box-sizing: border-box;
                padding-top: 8px;
                text-align: center;
                width: 110px;
                height: 100%;
                background: white;
                box-shadow: -3px 0 15px 3px rgba(0, 0, 0, .1);
                z-index: 10;
            }
        }
    }
    &-header{
        height: 60px;
        background: #fff;
        box-shadow: 0 2px 1px 1px rgba(100,100,100,.1);
        position: relative;
        z-index: 11;
        .navicon-con{
            margin: 6px;
            display: inline-block;
        }
        .header-middle-con{
            position: absolute;
            left: 60px;
            top: 0;
            right: 340px;
            bottom: 0;
            padding: 10px;
            overflow: hidden;
        }
        .header-avator-con{
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            width: 300px;
            .switch-theme-con{
                display: inline-block;
                width: 40px;
                height: 100%;
            }
            .message-con{
                display: inline-block;
                width: 30px;
                padding: 18px 0;
                text-align: center;
                cursor: pointer;
                i{
                    vertical-align: middle;
                }
            }
            .change-skin{
                font-size: 14px;
                font-weight: 500;
                padding-right: 5px;
            }
            .switch-theme{
                height: 100%;
            }
            .user-dropdown{
                &-menu-con{
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 150px;
                    height: 100%;
                    .main-user-name{
                        display: inline-block;
                        width: 80px;
                        word-break: keep-all;
                        white-space: nowrap;
                        vertical-align: middle;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        text-align: right;
                    }
                }
                &-innercon{
                    height: 100%;
                    padding-right: 14px;
                }
            }
            .full-screen-btn-con{
                display: inline-block;
                width: 30px;
                padding: 18px 0;
                text-align: center;
                cursor: pointer;
                i{
                    vertical-align: middle;
                }
            }
            .lock-screen-btn-con{
                display: inline-block;
                width: 30px;
                padding: 18px 0;
                text-align: center;
                cursor: pointer;
                i{
                    vertical-align: middle;
                }
            }
            .logout-btn-con{
                float:right;
                display: inline-block;
                padding: 18px 30px;
                text-align: center;
                cursor: pointer;
                i{
                    vertical-align: middle;
                }
            }
        }
    }
    .single-page-con{
        // position: absolute;
        //top: 100px;
        // top: 60px;
        // right: 0;
        // bottom: 0;
        margin-top: 60px;
        overflow: auto;
        background-color: #F0F0F0;
        z-index: 25;
        transition: left .3s;
        .single-page{
            margin: 10px;
        }
    }
    &-copy{
        text-align: center;
        padding: 10px 0 20px;
        color: #9ea7b4;
    }
}
.taglist-moving-animation-move{
    transition: transform .3s;
}
.logo-con{
    padding: 8px;
    text-align: center;
    img{
        height: 44px;
        width: auto;
    }
}
/*主页标题样式*/
.yp-header-title {
    color:#ffffff;
    font-size: 24px;
    //margin-left: 6%;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
}
.publish-drawer .el-drawer__header {
  padding-bottom: 12px;
  padding-top: 12px;
  margin-bottom: 0;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
}
.ivu-notice {
  z-index: 3000;
}