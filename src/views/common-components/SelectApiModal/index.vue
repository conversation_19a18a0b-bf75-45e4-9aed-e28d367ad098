<template>
	<Modal id="modal_request_4" v-model="model_show" :closable="false" width="75%">
		<p slot="header" style="color: #2d8cf0">
			<span style="color: black">关联添加API</span>
		</p>
		<Card dis-hover :bordered="false">
			<Row type="flex" align="middle">
				<Col span="24">
					<Col span="8">
						<Col span="6" class="margin-top-5">
							<span>API分组:</span>
						</Col>
						<Col span="18">
							<Select
								ref="select_apiM_1"
								id="select_apiM_1"
								class="margin-top-5"
								v-model="params.apiGroupCode"
								filterable
								clearable
								placeholder="请选择（默认全部）"
							>
								<Option
									v-for="item in apiGroupList"
									:value="item.apiGroupCode"
									:key="`${item.apiGroupCode}-${item.apiGroupName}`"
								>{{ item.apiGroupName }}({{item.apiGroupCode}})</Option>
							</Select>
						</Col>
					</Col>
					<Col offset="1" span="7">
						<Col span="6" class="margin-top-5">
							<span>API名称:</span>
						</Col>
						<Col span="18">
							<Input clearable v-model="params.apiTitle" id="inputApiName" placeholder="API名称"></Input>
						</Col>
					</Col>
					<Col offset="1" span="7">
						<Col span="6" class="margin-top-5">
							<span>API URL:</span>
						</Col>
						<Col span="18">
							<Input clearable v-model="params.apiUri" id="inputApiUrl" placeholder="API URL"></Input>
						</Col>
					</Col>
				</Col>
				<Col :span="24" class="margin-top-5">
					<Col span="8">
						<Col span="6" class="margin-top-5">
							<span>API类型:</span>
						</Col>
						<Col span="18">
							<Select
								id="selectApiType"
								ref="doc_status"
								v-model="params.apiType"
								placeholder="请选择（默认全部）"
								clearable
							>
								<Option v-for="item in apiTypeOptions" :value="item.value" :key="item.value">{{ item.desc }}</Option>
							</Select>
						</Col>
					</Col>
					<Col span="7" offset="1">
						<Col span="6" class="margin-top-5">
							<span>API状态:</span>
						</Col>
						<Col span="18">
							<Select
								id="selectApiStatus"
								ref="doc_status"
								v-model="params.apiStatus"
								placeholder="请选择（默认全部）"
								clearable
							>
								<Option
									v-for="item in apiStatusOptions"
									:value="item.value"
									:key="item.value"
								>{{ item.label }}</Option>
							</Select>
						</Col>
					</Col>
					<Col span="7" offset="1">
						<Col span="6" class="margin-top-5">
							<span>API版本:</span>
						</Col>
						<Col span="18">
							<Select
								id="select_apiM_version"
								v-model="params.apiVersion"
								placeholder="请选择"
								@on-change="toggleApiVersion"
							>
								<Option value="V1" key="V1">旧版API</Option>
								<Option value="V2" key="V2">新版API</Option>
							</Select>
						</Col>
					</Col>
				</Col>
			</Row>
			<Row type="flex" justify="space-between" class="margin-top-20">
				<Col :span="4">
					<Button id="btnReset" type="ghost" @click="batchAddApi" :disabled="addApiList.length < 1">批量添加</Button>
				</Col>
				<Col :span="4" type="flex" align="end">
					<Button id="btnSearch" type="primary" @click="getAllApi" style="margin-right: 10px">查询</Button>
					<Button id="btnReset" type="ghost" @click="reset_Interface">重置</Button>
				</Col>
			</Row>
			<Row class="margin-top-20">
				<Table
					id="table2"
					border
					ref="singleTable"
					:columns="columns_ApiGroupList"
					:data="allApiData"
					@on-selection-change="handleAddApiselection"
					row-key="id"
				></Table>
				<loading :show="showLoadingAllApi"></loading>
			</Row>
			<Row class="margin-top-20" type="flex" justify="end">
				<Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
					<Page
						:total="params.total"
						:current="params.pageNo"
						show-elevator
						@on-change="onAllApiPageChange"
					/>
				</Tooltip>
			</Row>
		</Card>
		<div slot="footer">
			<Button type="ghost" id="popupCancel" @click="close_modal">取消</Button>
		</div>
	</Modal>
</template>
<script>
import Api from '~/api/api';
import loading from '~/views/my-components/loading/loading';
export default {
  name: 'modal-add-api-dialog',
  components: {
    loading
  },
  data () {
    return {
      model_show: false,
      showLoadingAllApi: false,
      params: {
        apiVersion: 'V1',
        apiGroupCode: null,
        apiTitle: null,
        apiUri: null,
        apiStatus: '',
        joinCode: '',
        joinValue: '',
        apiType: null,
        pageNo: 1,
        total: 10
      },
      addApiPageParams: {},
      allApiData: [],
      deleteApiList: [],
      addApiList: [],
      apiTypeOptions: [],
      apiStatusOptions: [],
      apiGroupList: [],
      // 表格头数据绑定
      columns_ApiGroupList: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: 'API分组',
          key: 'apiGroupCode',
          align: 'center'
        },
        {
          title: 'API名称',
          key: 'apiTitle',
          align: 'center'
        },
        {
          title: 'API URL',
          render: (h, params) => {
            return h('div', [
              h('p', params.row.apiName),
              h('p', params.row.apiUri)
            ]);
          },
          align: 'center'
        },
        {
          title: 'API类型',
          width: 100,
          render: (h, params) => {
            const { desc } = this.apiTypeOptions.find(
              (item) => item.value === params.row.apiType
            );
            return h('div', [h('p', desc)]);
          },
          align: 'center'
        },
        {
          title: 'API状态',
          render: (h, params) => {
            let color = 'green';
            let statusDesc = '';
            const { apiStatus } = params.row;
            const apiStatusItem = this.v1v2ApiStausList.find(item => item.value === apiStatus);
            if (apiStatusItem) {
              statusDesc = apiStatusItem.label;
              color = apiStatusItem.color;
            }
            return h('div', [
              h(
                'Tag',
                {
                  style: {
                    align: 'center'
                  },
                  props: {
                    color: color
                  }
                },
                statusDesc
              )
            ]);
          },
          width: 120,
          align: 'center'
        },
        {
          title: '操作',
          width: 100,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.confirmOneAddApi(params.row.apiId);
                  }
                }
              }, '添加关联')
            ])
          }
        }
      ]
    };
  },
  computed: {
    v1v2ApiStausList () {
      return this.$store.state.v1v2ApiStausList
    },
    v1ApiStausList () {
      return this.$store.state.v1ApiStausList
    },
    api_status_List () {
      return this.$store.state.api_status_List
    }
  },
  mounted () {
    this.getApiGroupList()
  },
  methods: {
    toggleApiVersion (version) {
      this.params.apiVersion = version;
      this.apiTypeOptions = [];
      this.apiStatusOptions = [];
      this.allApiData = [];
      this.getApiType();
      this.getApiStatus();
    },
    reset_Interface () {
      this.toggleApiVersion('V1');
      this.params.apiStatus = '';
      this.params.apiType = null;
      this.params.apiGroupCode = null;
      this.params.apiTitle = null;
      this.params.apiUri = null;
      this.params.pageNo = 1;
      this.params.total = 10;
      // 清楚输入框搜索
      this.$refs.select_apiM_1.clearSingleSelect()
    },
    computPageTotal (pageNo, pageSize, totalPageNum, arr, cb) {
      if (!arr) return 0;
      if (!totalPageNum) return 0;
      if (pageNo > totalPageNum && totalPageNum > 0) {
        this.params.pageNo = totalPageNum;
        cb();
        return;
      }
      if (pageNo <= totalPageNum - 1) {
        return NaN;
      }
      return (totalPageNum - 1) * pageSize + arr.length;
    },
    getAllApi () {
      this.showLoadingAllApi = true;
      Api.yop_errorCode_join_api(this.params)
        .then((res) => {
          const { status, message, solutions, data } = res.data;
          if (status === 'success') {
            const { items, pageNo, totalPageNum } = data.page;
            this.params.pageNo = pageNo;
            this.allApiData = items;
            this.params.total = this.computPageTotal(
              pageNo,
              10,
              totalPageNum,
              items,
              () => {
                this.getAllApi();
              }
            );
          } else {
            this.$ypMsg.notice_error(this, '列表获取错误', message, solutions);
          }
        })
        .finally(() => {
          this.showLoadingAllApi = false;
        });
    },
    getApiType () {
      return Api.yop_errorCode_get_api_type({
        apiVersion: this.params.apiVersion
      }).then((res) => {
        const { status, message, solutions, data } = res.data;
        if (status === 'success') {
          this.apiTypeOptions = data.result;
        } else {
          this.$ypMsg.notice_error(this, '列表获取错误', message, solutions);
        }
      });
    },
    getApiStatus () {
      if (this.params.apiVersion === 'V1') {
        this.apiStatusOptions = this.v1ApiStausList
      } else {
        this.apiStatusOptions = this.api_status_List
      }
    },
    close_modal () {
      this.model_show = false;
    },
    show_model (data) {
      this.model_show = true;
      this.params.joinCode = data.joinCode
      this.params.joinValue = data.joinValue
      this.addApiList = [];
      this.toggleApiVersion('V1');
      this.reset_Interface()
    },
    batchAddApi () {
      if (this.addApiList.length === 0) {
        this.$ypMsg.notice_warning(this, '请选择需要添加API后再点击');
        return;
      }
      this.$emit('confirm', this.addApiList.map((item) => item.apiId))
      this.model_show = false;
    },
    confirmOneAddApi (apiId) {
      this.$emit('confirm', [apiId])
      this.model_show = false;
    },
    handleselection (val) {
      this.deleteApiList = val;
    },
    handleAddApiselection (val) {
      this.addApiList = val;
    },
    onAllApiPageChange (val) {
      this.params.pageNo = val;
      this.getAllApi();
    },
    getApiGroupList () {
      Api.yop_dashboard_apis_list().then(
        (res) => {
          const { status, message, solutions, data } = res.data;
          if (status === 'success') {
            this.apiGroupList = data.result;
          } else {
            this.$ypMsg.notice_error(this, '列表获取错误', message, solutions);
          }
        }
      )
    }
  }
};
</script>

<style scoped>
</style>

