<style>
.button-icon{
    width: 45px;
}
.button-col {
    margin: 10px 0;
}
.input-icon-filter{
    width : 90%;
    margin-bottom: 20px;
}
.icon-preview {
    margin-left: 20px;
    width: 80px;
    height: 80px;
    font-size: 80px;
}
.button-icon-clear {
  margin-bottom: 50px;
  margin-left: 30px;
}
</style>
<template>
  <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="900">
      <p slot="header" style="color:#2d8cf0;">
            <span style="color:black" >图标选择</span>
        </p>
        <div>
            <Form ref="form_detail" :model="form_detail" :label-width="100">
                <FormItem label="当前图标：">
                    <Icon :type="form_detail.currentIcon" v-show="form_detail.currentIcon" class="icon-preview"></Icon>
                    <Button class="button-icon-clear" v-show="form_detail.currentIcon" type="primary" shape="circle" icon="close" @click="icon_clear"></Button>
                    <p v-show="!form_detail.currentIcon">暂未选择图标</p>
                </FormItem>
                <FormItem label="图标选取：">
                    <Row>
                        <Col span="24"><Input type="text" placeholder="输入英文关键词搜索，例如：success"  v-model="form_detail.filterText" class="input-icon-filter"></Input></Col>
                        <Col span="22">
                            <Col v-for="(item, index) in filterIcons" :key="index" span="2" class="button-col">
                                <Button class="button-icon" type="ghost" :icon="item.name" size="large" @click="icon_handler(item.name)"></Button>
                            </Col>
                        </Col>
                    </Row>
                </FormItem>
            </Form>
        </div>
        <div slot="footer">
            <Button type="primary" @click="submit">确定</Button>
            <Button type="ghost" @click="cancel">取消</Button>
        </div>
  </Modal>
</template>

<script>
import util from "../../../libs/util";
import api from "../../../api/api";
export default {
  name: "icon-selector",
  components: {},
  data() {
    return {
        // 窗口是否显示
        modal_show : false,
        // form表单数据绑定
        form_detail : {
            currentIcon: '',
            filterText : '' //筛选输入框绑定
        },
      // 所有图标数组
      icon_all: [
        {
          name: "ionic",
          pack: "default",
          tag: "badass, framework, sexy, hawt, ionic"
        },
        {
          name: "arrow-up-a",
          pack: "default",
          tag: ", arrow-up-a"
        },
        {
          name: "arrow-right-a",
          pack: "default",
          tag: ", arrow-right-a"
        },
        {
          name: "arrow-down-a",
          pack: "default",
          tag: ", arrow-down-a"
        },
        {
          name: "arrow-left-a",
          pack: "default",
          tag: ", arrow-left-a"
        },
        {
          name: "arrow-up-b",
          pack: "default",
          tag: ", arrow-up-b"
        },
        {
          name: "arrow-right-b",
          pack: "default",
          tag: ", arrow-right-b"
        },
        {
          name: "arrow-down-b",
          pack: "default",
          tag: ", arrow-down-b"
        },
        {
          name: "arrow-left-b",
          pack: "default",
          tag: ", arrow-left-b"
        },
        {
          name: "arrow-up-c",
          pack: "default",
          tag: ", arrow-up-c"
        },
        {
          name: "arrow-right-c",
          pack: "default",
          tag: ", arrow-right-c"
        },
        {
          name: "arrow-down-c",
          pack: "default",
          tag: ", arrow-down-c"
        },
        {
          name: "arrow-left-c",
          pack: "default",
          tag: ", arrow-left-c"
        },
        {
          name: "arrow-return-right",
          pack: "default",
          tag: ", arrow-return-right"
        },
        {
          name: "arrow-return-left",
          pack: "default",
          tag: ", arrow-return-left"
        },
        {
          name: "arrow-swap",
          pack: "default",
          tag: "switch, flip, arrow-swap"
        },
        {
          name: "arrow-shrink",
          pack: "default",
          tag: "pinch, arrow-shrink"
        },
        {
          name: "arrow-expand",
          pack: "default",
          tag: "fullscreen, arrow-expand"
        },
        {
          name: "arrow-move",
          pack: "default",
          tag: "drag, arrow-move"
        },
        {
          name: "arrow-resize",
          pack: "default",
          tag: "drag, arrow-resize"
        },
        {
          name: "chevron-up",
          pack: "default",
          tag: "arrow, up, chevron-up"
        },
        {
          name: "chevron-right",
          pack: "default",
          tag: "arrow, right, chevron-right"
        },
        {
          name: "chevron-down",
          pack: "default",
          tag: "arrow, down, chevron-down"
        },
        {
          name: "chevron-left",
          pack: "default",
          tag: "arrow, left, chevron-left"
        },
        {
          name: "navicon-round",
          pack: "default",
          tag: "menu, hamburger, slide menu, navicon-round"
        },
        {
          name: "navicon",
          pack: "default",
          tag: "menu, hamburger, slide menu, navicon"
        },
        {
          name: "drag",
          pack: "default",
          tag: "reorder, move, drag, drag"
        },
        {
          name: "log-in",
          pack: "default",
          tag: "sign in, , log-in"
        },
        {
          name: "log-out",
          pack: "default",
          tag: "sign out, log-out"
        },
        {
          name: "checkmark-round",
          pack: "default",
          tag: "complete, finished, success, on, checkmark-round"
        },
        {
          name: "checkmark",
          pack: "default",
          tag: "complete, finished, success, on, checkmark"
        },
        {
          name: "checkmark-circled",
          pack: "default",
          tag: "complete, finished, success, on, checkmark-circled"
        },
        {
          name: "close-round",
          pack: "default",
          tag: "delete, trash, kill, x, close-round"
        },
        {
          name: "close",
          pack: "default",
          tag: "delete, trash, kill, x, close"
        },
        {
          name: "close-circled",
          pack: "default",
          tag: "delete, trash, kill, x, close-circled"
        },
        {
          name: "plus-round",
          pack: "default",
          tag: "add, include, new, invite, +, plus-round"
        },
        {
          name: "plus",
          pack: "default",
          tag: "add, include, new, invite, +, plus"
        },
        {
          name: "plus-circled",
          pack: "default",
          tag: "add, include, new, invite, +, plus-circled"
        },
        {
          name: "minus-round",
          pack: "default",
          tag: "hide, remove, minimize, -, minus-round"
        },
        {
          name: "minus",
          pack: "default",
          tag: "hide, remove, minimize, -, minus"
        },
        {
          name: "minus-circled",
          pack: "default",
          tag: "hide, remove, minimize, -, minus-circled"
        },
        {
          name: "information",
          pack: "default",
          tag: "help, more, tooltip, information"
        },
        {
          name: "information-circled",
          pack: "default",
          tag: "help, more, tooltip, information-circled"
        },
        {
          name: "help",
          pack: "default",
          tag: "question, ?, help"
        },
        {
          name: "help-circled",
          pack: "default",
          tag: "question, ?, help-circled"
        },
        {
          name: "backspace-outline",
          pack: "default",
          tag: "delete, remove, back, backspace-outline"
        },
        {
          name: "backspace",
          pack: "default",
          tag: "delete, remove, back, backspace"
        },
        {
          name: "help-buoy",
          pack: "default",
          tag: "question, ?, help-buoy"
        },
        {
          name: "asterisk",
          pack: "default",
          tag: "favorite, mark, star, asterisk"
        },
        {
          name: "alert",
          pack: "default",
          tag: "attention, warning, notice, !, exclamation, alert"
        },
        {
          name: "alert-circled",
          pack: "default",
          tag: "attention, warning, notice, !, exclamation, alert-circled"
        },
        {
          name: "refresh",
          pack: "default",
          tag: "reload, renew, refresh"
        },
        {
          name: "loop",
          pack: "default",
          tag: "refresh, loop"
        },
        {
          name: "shuffle",
          pack: "default",
          tag: "random, shuffle"
        },
        {
          name: "home",
          pack: "default",
          tag: "house, home"
        },
        {
          name: "search",
          pack: "default",
          tag: "magnifying glass, search"
        },
        {
          name: "flag",
          pack: "default",
          tag: "favorite, mark, star, flag"
        },
        {
          name: "star",
          pack: "default",
          tag: "favorite, star"
        },
        {
          name: "heart",
          pack: "default",
          tag: "love, heart"
        },
        {
          name: "heart-broken",
          pack: "default",
          tag: "love, heart-broken"
        },
        {
          name: "gear-a",
          pack: "default",
          tag: "settings, options, cog, gear-a"
        },
        {
          name: "gear-b",
          pack: "default",
          tag: "settings, options, cog, gear-b"
        },
        {
          name: "toggle-filled",
          pack: "default",
          tag: "settings, options, switch, toggle-filled"
        },
        {
          name: "toggle",
          pack: "default",
          tag: "settings, options, switch, toggle"
        },
        {
          name: "settings",
          pack: "default",
          tag: "options, tools, settings"
        },
        {
          name: "wrench",
          pack: "default",
          tag: "settings, options, tools, wrench"
        },
        {
          name: "hammer",
          pack: "default",
          tag: "settings, options, tools, hammer"
        },
        {
          name: "edit",
          pack: "default",
          tag: "change, update, write, type, pencil, edit"
        },
        {
          name: "trash-a",
          pack: "default",
          tag: "delete, remove, dump, trash-a"
        },
        {
          name: "trash-b",
          pack: "default",
          tag: "delete, remove, dump, trash-b"
        },
        {
          name: "document",
          pack: "default",
          tag: "paper, file, document"
        },
        {
          name: "document-text",
          pack: "default",
          tag: "paper, file, document-text"
        },
        {
          name: "clipboard",
          pack: "default",
          tag: "write, clipboard"
        },
        {
          name: "scissors",
          pack: "default",
          tag: "cut, scissors"
        },
        {
          name: "funnel",
          pack: "default",
          tag: "sort, funnel"
        },
        {
          name: "bookmark",
          pack: "default",
          tag: "favorite, tag, save, bookmark"
        },
        {
          name: "email",
          pack: "default",
          tag: "snail, mail, inbox, email"
        },
        {
          name: "email-unread",
          pack: "default",
          tag: "snail, mail, inbox, email-unread"
        },
        {
          name: "folder",
          pack: "default",
          tag: "snail, mail, folder"
        },
        {
          name: "filing",
          pack: "default",
          tag: "mail, filing"
        },
        {
          name: "archive",
          pack: "default",
          tag: "mail, archive"
        },
        {
          name: "reply",
          pack: "default",
          tag: "mail, undo, reply"
        },
        {
          name: "reply-all",
          pack: "default",
          tag: "mail, reply-all"
        },
        {
          name: "forward",
          pack: "default",
          tag: "mail, redo, forward"
        },
        {
          name: "share",
          pack: "default",
          tag: "outbound, share"
        },
        {
          name: "paper-airplane",
          pack: "default",
          tag: "outbound, mail, letter, send, paper-airplane"
        },
        {
          name: "link",
          pack: "default",
          tag: "chain, anchor, href, attach, link"
        },
        {
          name: "paperclip",
          pack: "default",
          tag: "attach, paperclip"
        },
        {
          name: "compose",
          pack: "default",
          tag: "write, compose, type, compose"
        },
        {
          name: "briefcase",
          pack: "default",
          tag: "store, organize, briefcase"
        },
        {
          name: "medkit",
          pack: "default",
          tag: "health, medkit"
        },
        {
          name: "at",
          pack: "default",
          tag: "@, at"
        },
        {
          name: "pound",
          pack: "default",
          tag: "hashtag, #, pound"
        },
        {
          name: "quote",
          pack: "default",
          tag: "chat, quotation, quote"
        },
        {
          name: "cloud",
          pack: "default",
          tag: "storage, cloud"
        },
        {
          name: "upload",
          pack: "default",
          tag: "storage, cloud, upload"
        },
        {
          name: "more",
          pack: "default",
          tag: "circles, more"
        },
        {
          name: "grid",
          pack: "default",
          tag: "menu, grid"
        },
        {
          name: "calendar",
          pack: "default",
          tag: "date, time, month, year, calendar"
        },
        {
          name: "clock",
          pack: "default",
          tag: "time, watch, hours, minutes, seconds, clock"
        },
        {
          name: "compass",
          pack: "default",
          tag: "location, directions, navigation, compass"
        },
        {
          name: "pinpoint",
          pack: "default",
          tag: "gps, navigation, pinpoint"
        },
        {
          name: "pin",
          pack: "default",
          tag: "gps, navigation, pin"
        },
        {
          name: "navigate",
          pack: "default",
          tag: "gps, location pin, navigate"
        },
        {
          name: "location",
          pack: "default",
          tag: "gps, navigation, pin, location"
        },
        {
          name: "map",
          pack: "default",
          tag: "gps, navigation, pin, map"
        },
        {
          name: "lock-combination",
          pack: "default",
          tag: "padlock, security, lock-combination"
        },
        {
          name: "locked",
          pack: "default",
          tag: "padlock, security, locked"
        },
        {
          name: "unlocked",
          pack: "default",
          tag: "padlock, security, unlocked"
        },
        {
          name: "key",
          pack: "default",
          tag: "access, key"
        },
        {
          name: "arrow-graph-up-right",
          pack: "default",
          tag: "stats, arrow-graph-up-right"
        },
        {
          name: "arrow-graph-down-right",
          pack: "default",
          tag: "stats, arrow-graph-down-right"
        },
        {
          name: "arrow-graph-up-left",
          pack: "default",
          tag: "stats, arrow-graph-up-left"
        },
        {
          name: "arrow-graph-down-left",
          pack: "default",
          tag: "stats, arrow-graph-down-left"
        },
        {
          name: "stats-bars",
          pack: "default",
          tag: "data, stats-bars"
        },
        {
          name: "connection-bars",
          pack: "default",
          tag: "data, stats, connection-bars"
        },
        {
          name: "pie-graph",
          pack: "default",
          tag: "stats, pie-graph"
        },
        {
          name: "chatbubble",
          pack: "default",
          tag: "talk, chatbubble"
        },
        {
          name: "chatbubble-working",
          pack: "default",
          tag: "talk, chatbubble-working"
        },
        {
          name: "chatbubbles",
          pack: "default",
          tag: "talk, chatbubbles"
        },
        {
          name: "chatbox",
          pack: "default",
          tag: "talk, chatbox"
        },
        {
          name: "chatbox-working",
          pack: "default",
          tag: "talk, chatbox-working"
        },
        {
          name: "chatboxes",
          pack: "default",
          tag: "talk, chatboxes"
        },
        {
          name: "person",
          pack: "default",
          tag: "users, staff, head, human, person"
        },
        {
          name: "person-add",
          pack: "default",
          tag: "users, staff, head, human, member, new, person-add"
        },
        {
          name: "person-stalker",
          pack: "default",
          tag: "people, human, users, staff, person-stalker"
        },
        {
          name: "woman",
          pack: "default",
          tag: "female, lady, girl, dudette, woman"
        },
        {
          name: "man",
          pack: "default",
          tag: "male, guy, boy, dude, man"
        },
        {
          name: "female",
          pack: "default",
          tag: "lady, girl, dudette, female"
        },
        {
          name: "male",
          pack: "default",
          tag: "male, guy, boy, dude, male"
        },
        {
          name: "transgender",
          pack: "default",
          tag: ", transgender"
        },
        {
          name: "fork",
          pack: "default",
          tag: "food, drink, eat, fork"
        },
        {
          name: "knife",
          pack: "default",
          tag: "food, drink, eat, knife"
        },
        {
          name: "spoon",
          pack: "default",
          tag: "food, drink, eat, spoon"
        },
        {
          name: "soup-can-outline",
          pack: "default",
          tag: "food, drink, eat, soup-can-outline"
        },
        {
          name: "soup-can",
          pack: "default",
          tag: "food, drink, eat, soup-can"
        },
        {
          name: "beer",
          pack: "default",
          tag: "food, drink, eat, beer"
        },
        {
          name: "wineglass",
          pack: "default",
          tag: "food, drink, eat, wineglass"
        },
        {
          name: "coffee",
          pack: "default",
          tag: "food, drink, eat, caffeine, coffee"
        },
        {
          name: "icecream",
          pack: "default",
          tag: "food, drink, eat, icecream"
        },
        {
          name: "pizza",
          pack: "default",
          tag: "food, drink, eat, pizza"
        },
        {
          name: "power",
          pack: "default",
          tag: "on, off, power"
        },
        {
          name: "mouse",
          pack: "default",
          tag: "computer, mouse"
        },
        {
          name: "battery-full",
          pack: "default",
          tag: ", battery-full"
        },
        {
          name: "battery-half",
          pack: "default",
          tag: ", battery-half"
        },
        {
          name: "battery-low",
          pack: "default",
          tag: ", battery-low"
        },
        {
          name: "battery-empty",
          pack: "default",
          tag: ", battery-empty"
        },
        {
          name: "battery-charging",
          pack: "default",
          tag: ", battery-charging"
        },
        {
          name: "wifi",
          pack: "default",
          tag: "internet, connection, wifi"
        },
        {
          name: "bluetooth",
          pack: "default",
          tag: "connection, cloud, bluetooth"
        },
        {
          name: "calculator",
          pack: "default",
          tag: "math, arithmatic, numbers, addition, subtraction, calculator"
        },
        {
          name: "camera",
          pack: "default",
          tag: "photo, camera"
        },
        {
          name: "eye",
          pack: "default",
          tag: "view, see, creeper, eye"
        },
        {
          name: "eye-disabled",
          pack: "default",
          tag: "view, see, creeper, eye-disabled"
        },
        {
          name: "flash",
          pack: "default",
          tag: "lightning, weather, whether, flash"
        },
        {
          name: "flash-off",
          pack: "default",
          tag: ", flash-off"
        },
        {
          name: "qr-scanner",
          pack: "default",
          tag: "reader, qr-scanner"
        },
        {
          name: "image",
          pack: "default",
          tag: "photo, image"
        },
        {
          name: "images",
          pack: "default",
          tag: "photo, images"
        },
        {
          name: "wand",
          pack: "default",
          tag: "images, levels, light, dark, settings, wand"
        },
        {
          name: "contrast",
          pack: "default",
          tag: "images, levels, light, dark, settings, contrast"
        },
        {
          name: "aperture",
          pack: "default",
          tag: "images, levels, light, dark, settings, aperture"
        },
        {
          name: "crop",
          pack: "default",
          tag: "images, levels, light, dark, settings, crop"
        },
        {
          name: "easel",
          pack: "default",
          tag: "images, art, create, color, easel"
        },
        {
          name: "paintbrush",
          pack: "default",
          tag: "images, art, create, color, paintbrush"
        },
        {
          name: "paintbucket",
          pack: "default",
          tag: "images, art, create, color, paintbucket"
        },
        {
          name: "monitor",
          pack: "default",
          tag: "thunderbolt, screen, monitor"
        },
        {
          name: "laptop",
          pack: "default",
          tag: "macbook, apple, osx, laptop"
        },
        {
          name: "ipad",
          pack: "default",
          tag: "tablet, mobile, apple, retina, device, ipad"
        },
        {
          name: "iphone",
          pack: "default",
          tag: "smartphone, mobile, apple, retina, device, iphone"
        },
        {
          name: "ipod",
          pack: "default",
          tag: "music, player, apple, retina, device, ipod"
        },
        {
          name: "printer",
          pack: "default",
          tag: "paper, printer"
        },
        {
          name: "usb",
          pack: "default",
          tag: "digital, computer, usb"
        },
        {
          name: "outlet",
          pack: "default",
          tag: "digital, computer, electricity, outlet"
        },
        {
          name: "bug",
          pack: "default",
          tag: "develop, program, hacker, error, bug"
        },
        {
          name: "code",
          pack: "default",
          tag: "develop, program, hacker, code"
        },
        {
          name: "code-working",
          pack: "default",
          tag: "develop, program, hacker, code-working"
        },
        {
          name: "code-download",
          pack: "default",
          tag: "develop, program, hacker, code-download"
        },
        {
          name: "fork-repo",
          pack: "default",
          tag: "develop, program, hacker, github, fork-repo"
        },
        {
          name: "network",
          pack: "default",
          tag: "develop, program, hacker, github, network"
        },
        {
          name: "pull-request",
          pack: "default",
          tag: "develop, program, hacker, github, pull-request"
        },
        {
          name: "merge",
          pack: "default",
          tag: "develop, program, hacker, github, merge"
        },
        {
          name: "xbox",
          pack: "default",
          tag: "fun, games, xbox"
        },
        {
          name: "playstation",
          pack: "default",
          tag: "fun, games, playstation"
        },
        {
          name: "steam",
          pack: "default",
          tag: "fun, games, steam"
        },
        {
          name: "closed-captioning",
          pack: "default",
          tag: "movie, film, television, closed-captioning"
        },
        {
          name: "videocamera",
          pack: "default",
          tag: "movie, film, television, videocamera"
        },
        {
          name: "film-marker",
          pack: "default",
          tag: "film, cut, action, film-marker"
        },
        {
          name: "disc",
          pack: "default",
          tag: "cd, vinyl, disc"
        },
        {
          name: "headphone",
          pack: "default",
          tag: "music, earbuds, beats, headphone"
        },
        {
          name: "music-note",
          pack: "default",
          tag: "songs, music-note"
        },
        {
          name: "radio-waves",
          pack: "default",
          tag: "music, sound, speaker, radio-waves"
        },
        {
          name: "speakerphone",
          pack: "default",
          tag: "sound, speaker, loud, amplify, speakerphone"
        },
        {
          name: "mic-a",
          pack: "default",
          tag: "sound, talk, speaker, mic-a"
        },
        {
          name: "mic-b",
          pack: "default",
          tag: "sound, talk, speaker, mic-b"
        },
        {
          name: "mic-c",
          pack: "default",
          tag: "sound, talk, speaker, mic-c"
        },
        {
          name: "volume-high",
          pack: "default",
          tag: "sound, noise, volume-high"
        },
        {
          name: "volume-medium",
          pack: "default",
          tag: "sound, volume-medium"
        },
        {
          name: "volume-low",
          pack: "default",
          tag: "sound, volume-low"
        },
        {
          name: "volume-mute",
          pack: "default",
          tag: "sound, volume-mute"
        },
        {
          name: "levels",
          pack: "default",
          tag: "options, toggles, sound, mixer, levels"
        },
        {
          name: "play",
          pack: "default",
          tag: "music, watch, arrow, right, play"
        },
        {
          name: "pause",
          pack: "default",
          tag: "music, break, hold, freeze, pause"
        },
        {
          name: "stop",
          pack: "default",
          tag: "music, square, hold, freeze, stop"
        },
        {
          name: "record",
          pack: "default",
          tag: "music, circle, record"
        },
        {
          name: "skip-forward",
          pack: "default",
          tag: "music, next, skip-forward"
        },
        {
          name: "skip-backward",
          pack: "default",
          tag: "music, previous, skip-backward"
        },
        {
          name: "eject",
          pack: "default",
          tag: "music, dvd, remove, eject"
        },
        {
          name: "bag",
          pack: "default",
          tag: "shopping, price, cart, money, container, $, bag"
        },
        {
          name: "card",
          pack: "default",
          tag: "credit, price, debit, money, shopping, cash, dollars, $, card"
        },
        {
          name: "cash",
          pack: "default",
          tag: "credit, price, debit, money, shopping, dollars, $, cash"
        },
        {
          name: "pricetag",
          pack: "default",
          tag: "credit, debit, money, shopping, cash, dollars, $, pricetag"
        },
        {
          name: "pricetags",
          pack: "default",
          tag: "credit, debit, money, shopping, cash, dollars, $, pricetags"
        },
        {
          name: "thumbsup",
          pack: "default",
          tag: "like, fun, yes, thumbsup"
        },
        {
          name: "thumbsdown",
          pack: "default",
          tag: "dislike, boring, no, thumbsdown"
        },
        {
          name: "happy-outline",
          pack: "default",
          tag: "good, like, fun, yes, happy-outline"
        },
        {
          name: "happy",
          pack: "default",
          tag: "good, like, fun, yes, happy"
        },
        {
          name: "sad-outline",
          pack: "default",
          tag: "cry, bad, no, sad-outline"
        },
        {
          name: "sad",
          pack: "default",
          tag: "cry, bad, no, sad"
        },
        {
          name: "bowtie",
          pack: "default",
          tag: "tie, shirt, dress, clothing, bowtie"
        },
        {
          name: "tshirt-outline",
          pack: "default",
          tag: "tie, shirt, dress, clothing, tshirt-outline"
        },
        {
          name: "tshirt",
          pack: "default",
          tag: "tie, shirt, dress, clothing, tshirt"
        },
        {
          name: "trophy",
          pack: "default",
          tag: "competition, compete, win, lose, award, trophy"
        },
        {
          name: "podium",
          pack: "default",
          tag: "competition, compete, win, lose, award, podium"
        },
        {
          name: "ribbon-a",
          pack: "default",
          tag: "competition, compete, win, lose, award, trophy, ribbon-a"
        },
        {
          name: "ribbon-b",
          pack: "default",
          tag: "competition, compete, win, lose, award, trophy, ribbon-b"
        },
        {
          name: "university",
          pack: "default",
          tag: "graduate, education, school, tassle, university"
        },
        {
          name: "magnet",
          pack: "default",
          tag: "sticky, attraction, magnet"
        },
        {
          name: "beaker",
          pack: "default",
          tag: "mixture, potion, flask, beaker"
        },
        {
          name: "erlenmeyer-flask",
          pack: "default",
          tag: "mixture, potion, beaker, potion, erlenmeyer-flask"
        },
        {
          name: "egg",
          pack: "default",
          tag: "birth, twitter, bird, baby, egg"
        },
        {
          name: "earth",
          pack: "default",
          tag: "nature, globe, home, planet, earth"
        },
        {
          name: "planet",
          pack: "default",
          tag: "nature, globe, home, planet, space, planet"
        },
        {
          name: "lightbulb",
          pack: "default",
          tag: "idea, new, aha!, lightbulb"
        },
        {
          name: "cube",
          pack: "default",
          tag: "box, square, container, cube"
        },
        {
          name: "leaf",
          pack: "default",
          tag: "green, recycle, plant, nature, leaf"
        },
        {
          name: "waterdrop",
          pack: "default",
          tag: "nature, clean, recycle, fresh, wet, rain, waterdrop"
        },
        {
          name: "flame",
          pack: "default",
          tag: "fire, hot, heat, flame"
        },
        {
          name: "fireball",
          pack: "default",
          tag: "hot, heat, fireball"
        },
        {
          name: "bonfire",
          pack: "default",
          tag: "hot, heat, bonfire"
        },
        {
          name: "umbrella",
          pack: "default",
          tag: "wet, rain, dry, shelter, umbrella"
        },
        {
          name: "nuclear",
          pack: "default",
          tag: "danger, warning, hazard, nuclear"
        },
        {
          name: "no-smoking",
          pack: "default",
          tag: "danger, warning, cigarette, cancer, no-smoking"
        },
        {
          name: "thermometer",
          pack: "default",
          tag: "hot, cold, heat, temperature, mercury, thermometer"
        },
        {
          name: "speedometer",
          pack: "default",
          tag: "travel, accelerate, speedometer"
        },
        {
          name: "model-s",
          pack: "default",
          tag: "navigation, car, drive, transportation, tesla, sexy, model-s"
        },
        {
          name: "plane",
          pack: "default",
          tag: "fly, jet, plane"
        },
        {
          name: "jet",
          pack: "default",
          tag: "fly, plane, jet"
        },
        {
          name: "load-a",
          pack: "default",
          tag: "spinner, waiting, refresh, load-a"
        },
        {
          name: "load-b",
          pack: "default",
          tag: "spinner, waiting, refresh, load-b"
        },
        {
          name: "load-c",
          pack: "default",
          tag: "spinner, waiting, refresh, load-c"
        },
        {
          name: "load-d",
          pack: "default",
          tag: "spinner, waiting, refresh, load-d"
        },
        {
          name: "ios-ionic-outline",
          pack: "ios",
          tag: "badass, framework, sexy, ios-ionic-outline"
        },
        {
          name: "ios-arrow-back",
          pack: "ios",
          tag: "chevron, left, ios-arrow-back"
        },
        {
          name: "ios-arrow-forward",
          pack: "ios",
          tag: "chevron, right, ios-arrow-forward"
        },
        {
          name: "ios-arrow-up",
          pack: "ios",
          tag: "chevron, ios-arrow-up"
        },
        {
          name: "ios-arrow-right",
          pack: "ios",
          tag: "chevron, ios-arrow-right"
        },
        {
          name: "ios-arrow-down",
          pack: "ios",
          tag: "chevron, ios-arrow-down"
        },
        {
          name: "ios-arrow-left",
          pack: "ios",
          tag: "chevron, ios-arrow-left"
        },
        {
          name: "ios-arrow-thin-up",
          pack: "ios",
          tag: "chevron, ios-arrow-thin-up"
        },
        {
          name: "ios-arrow-thin-right",
          pack: "ios",
          tag: "chevron, ios-arrow-thin-right"
        },
        {
          name: "ios-arrow-thin-down",
          pack: "ios",
          tag: "chevron, ios-arrow-thin-down"
        },
        {
          name: "ios-arrow-thin-left",
          pack: "ios",
          tag: "chevron, ios-arrow-thin-left"
        },
        {
          name: "ios-circle-filled",
          pack: "ios",
          tag: "checkmark, radio, dot, on, selected, button, ios-circle-filled"
        },
        {
          name: "ios-circle-outline",
          pack: "ios",
          tag: "checkmark, radio, dot, off, button, ios-circle-outline"
        },
        {
          name: "ios-checkmark-empty",
          pack: "ios",
          tag: "success, confirmed, on, finished, complete, ios-checkmark-empty"
        },
        {
          name: "ios-checkmark-outline",
          pack: "ios",
          tag:
            "success, confirmed, on, finished, complete, ios-checkmark-outline"
        },
        {
          name: "ios-checkmark",
          pack: "ios",
          tag: "success, confirmed, on, finished, complete, ios-checkmark"
        },
        {
          name: "ios-plus-empty",
          pack: "ios",
          tag: "add, include, new, invite, +, ios-plus-empty"
        },
        {
          name: "ios-plus-outline",
          pack: "ios",
          tag: "add, include, new, invite, +, ios-plus-outline"
        },
        {
          name: "ios-plus",
          pack: "ios",
          tag: "add, include, new, invite, +, ios-plus"
        },
        {
          name: "ios-close-empty",
          pack: "ios",
          tag: "delete, remove, trash, end, stop, x, ios-close-empty"
        },
        {
          name: "ios-close-outline",
          pack: "ios",
          tag: "delete, remove, trash, end, stop, x, ios-close-outline"
        },
        {
          name: "ios-close",
          pack: "ios",
          tag: "delete, remove, trash, end, stop, x, ios-close"
        },
        {
          name: "ios-minus-empty",
          pack: "ios",
          tag: "hide, remove, minimize, -, ios-minus-empty"
        },
        {
          name: "ios-minus-outline",
          pack: "ios",
          tag: "hide, remove, minimize, -, ios-minus-outline"
        },
        {
          name: "ios-minus",
          pack: "ios",
          tag: "hide, remove, minimize, -, ios-minus"
        },
        {
          name: "ios-information-empty",
          pack: "ios",
          tag: "help, question, ios-information-empty"
        },
        {
          name: "ios-information-outline",
          pack: "ios",
          tag: "help, question, ios-information-outline"
        },
        {
          name: "ios-information",
          pack: "ios",
          tag: "help, question, ios-information"
        },
        {
          name: "ios-help-empty",
          pack: "ios",
          tag: "question, information, ?, ios-help-empty"
        },
        {
          name: "ios-help-outline",
          pack: "ios",
          tag: "question, information, ?, ios-help-outline"
        },
        {
          name: "ios-help",
          pack: "ios",
          tag: "question, information, ?, ios-help"
        },
        {
          name: "ios-search",
          pack: "ios",
          tag: "find, seek, look, magnifying glass, ios-search"
        },
        {
          name: "ios-search-strong",
          pack: "ios",
          tag: "find, seek, look, magnifying glass, ios-search-strong"
        },
        {
          name: "ios-star",
          pack: "ios",
          tag: "favorite, rate, ios-star"
        },
        {
          name: "ios-star-half",
          pack: "ios",
          tag: "favorite, rate, ios-star-half"
        },
        {
          name: "ios-star-outline",
          pack: "ios",
          tag: "favorite, rate, ios-star-outline"
        },
        {
          name: "ios-heart",
          pack: "ios",
          tag: "love, ios-heart"
        },
        {
          name: "ios-heart-outline",
          pack: "ios",
          tag: "love, ios-heart-outline"
        },
        {
          name: "ios-more",
          pack: "ios",
          tag: "menu, ios-more"
        },
        {
          name: "ios-more-outline",
          pack: "ios",
          tag: "menu, ios-more-outline"
        },
        {
          name: "ios-home",
          pack: "ios",
          tag: "house, ios-home"
        },
        {
          name: "ios-home-outline",
          pack: "ios",
          tag: "house, ios-home-outline"
        },
        {
          name: "ios-cloud",
          pack: "ios",
          tag: "storage, weather, whether, ios-cloud"
        },
        {
          name: "ios-cloud-outline",
          pack: "ios",
          tag: "storage, weather, whether, ios-cloud-outline"
        },
        {
          name: "ios-cloud-upload",
          pack: "ios",
          tag: "storage, ios-cloud-upload"
        },
        {
          name: "ios-cloud-upload-outline",
          pack: "ios",
          tag: "storage, ios-cloud-upload-outline"
        },
        {
          name: "ios-cloud-download",
          pack: "ios",
          tag: "storage, ios-cloud-download"
        },
        {
          name: "ios-cloud-download-outline",
          pack: "ios",
          tag: "storage, ios-cloud-download-outline"
        },
        {
          name: "ios-upload",
          pack: "ios",
          tag: "share, import, ios-upload"
        },
        {
          name: "ios-upload-outline",
          pack: "ios",
          tag: "share, import, ios-upload-outline"
        },
        {
          name: "ios-download",
          pack: "ios",
          tag: "save, export, ios-download"
        },
        {
          name: "ios-download-outline",
          pack: "ios",
          tag: "save, export, ios-download-outline"
        },
        {
          name: "ios-refresh",
          pack: "ios",
          tag: "reload, renew, reset, ios-refresh"
        },
        {
          name: "ios-refresh-outline",
          pack: "ios",
          tag: "reload, renew, reset, ios-refresh-outline"
        },
        {
          name: "ios-refresh-empty",
          pack: "ios",
          tag: "reload, renew, ios-refresh-empty"
        },
        {
          name: "ios-reload",
          pack: "ios",
          tag: "renew, reset, ios-reload"
        },
        {
          name: "ios-loop-strong",
          pack: "ios",
          tag: "reload, renew, reset, ios-loop-strong"
        },
        {
          name: "ios-loop",
          pack: "ios",
          tag: "reload, renew, reset, ios-loop"
        },
        {
          name: "ios-bookmarks",
          pack: "ios",
          tag: "favorite, ios-bookmarks"
        },
        {
          name: "ios-bookmarks-outline",
          pack: "ios",
          tag: "favorite, ios-bookmarks-outline"
        },
        {
          name: "ios-book",
          pack: "ios",
          tag: "favorite, read, literature, ios-book"
        },
        {
          name: "ios-book-outline",
          pack: "ios",
          tag: "favorite, read, literature, ios-book-outline"
        },
        {
          name: "ios-flag",
          pack: "ios",
          tag: "marker, favorite, ios-flag"
        },
        {
          name: "ios-flag-outline",
          pack: "ios",
          tag: "marker, favorite, ios-flag-outline"
        },
        {
          name: "ios-glasses",
          pack: "ios",
          tag: "steve, reading, look, see, ios-glasses"
        },
        {
          name: "ios-glasses-outline",
          pack: "ios",
          tag: "steve, reading, look, see, ios-glasses-outline"
        },
        {
          name: "ios-browsers",
          pack: "ios",
          tag: "square, ios-browsers"
        },
        {
          name: "ios-browsers-outline",
          pack: "ios",
          tag: "square, ios-browsers-outline"
        },
        {
          name: "ios-at",
          pack: "ios",
          tag: "@, ios-at"
        },
        {
          name: "ios-at-outline",
          pack: "ios",
          tag: "@, ios-at-outline"
        },
        {
          name: "ios-pricetag",
          pack: "ios",
          tag: "shopping, money, items, commerce, $, ios-pricetag"
        },
        {
          name: "ios-pricetag-outline",
          pack: "ios",
          tag: "shopping, money, items, commerce, $, ios-pricetag-outline"
        },
        {
          name: "ios-pricetags",
          pack: "ios",
          tag: "shopping, money, items, commerce, $, ios-pricetags"
        },
        {
          name: "ios-pricetags-outline",
          pack: "ios",
          tag: "shopping, money, items, commerce, $, ios-pricetags-outline"
        },
        {
          name: "ios-cart",
          pack: "ios",
          tag: "shopping, money, items, commerce, $, ios-cart"
        },
        {
          name: "ios-cart-outline",
          pack: "ios",
          tag: "shopping, money, items, commerce, $, ios-cart-outline"
        },
        {
          name: "ios-chatboxes",
          pack: "ios",
          tag: "talk, ios-chatboxes"
        },
        {
          name: "ios-chatboxes-outline",
          pack: "ios",
          tag: "talk, ios-chatboxes-outline"
        },
        {
          name: "ios-chatbubble",
          pack: "ios",
          tag: "talk, ios-chatbubble"
        },
        {
          name: "ios-chatbubble-outline",
          pack: "ios",
          tag: "talk, ios-chatbubble-outline"
        },
        {
          name: "ios-cog",
          pack: "ios",
          tag: "settings, gear, options, ios-cog"
        },
        {
          name: "ios-cog-outline",
          pack: "ios",
          tag: "settings, gear, options, ios-cog-outline"
        },
        {
          name: "ios-gear",
          pack: "ios",
          tag: "cog, settings, options, ios-gear"
        },
        {
          name: "ios-gear-outline",
          pack: "ios",
          tag: "cog, settings, options, ios-gear-outline"
        },
        {
          name: "ios-settings",
          pack: "ios",
          tag: "cog, settings, options, ios-settings"
        },
        {
          name: "ios-settings-strong",
          pack: "ios",
          tag: "cog, settings, options, ios-settings-strong"
        },
        {
          name: "ios-toggle",
          pack: "ios",
          tag: "settings, options, switch, ios-toggle"
        },
        {
          name: "ios-toggle-outline",
          pack: "ios",
          tag: "settings, options, switch, ios-toggle-outline"
        },
        {
          name: "ios-analytics",
          pack: "ios",
          tag: "metrics, track, data, ios-analytics"
        },
        {
          name: "ios-analytics-outline",
          pack: "ios",
          tag: "metrics, track, data, ios-analytics-outline"
        },
        {
          name: "ios-pie",
          pack: "ios",
          tag: "cog, settings, options, ios-pie"
        },
        {
          name: "ios-pie-outline",
          pack: "ios",
          tag: "cog, settings, options, ios-pie-outline"
        },
        {
          name: "ios-pulse",
          pack: "ios",
          tag: "live, hot, rate, ios-pulse"
        },
        {
          name: "ios-pulse-strong",
          pack: "ios",
          tag: "live, hot, rate, ios-pulse-strong"
        },
        {
          name: "ios-filing",
          pack: "ios",
          tag: "archive, ios-filing"
        },
        {
          name: "ios-filing-outline",
          pack: "ios",
          tag: "archive, ios-filing-outline"
        },
        {
          name: "ios-box",
          pack: "ios",
          tag: "archive, ios-box"
        },
        {
          name: "ios-box-outline",
          pack: "ios",
          tag: "archive, ios-box-outline"
        },
        {
          name: "ios-compose",
          pack: "ios",
          tag: "write, type, create, ios-compose"
        },
        {
          name: "ios-compose-outline",
          pack: "ios",
          tag: "write, type, create, ios-compose-outline"
        },
        {
          name: "ios-trash",
          pack: "ios",
          tag: "delete, remove, dispose, waste, basket, dump, kill, ios-trash"
        },
        {
          name: "ios-trash-outline",
          pack: "ios",
          tag:
            "delete, remove, dispose, waste, basket, dump, kill, ios-trash-outline"
        },
        {
          name: "ios-copy",
          pack: "ios",
          tag: "duplicate, paper, ios-copy"
        },
        {
          name: "ios-copy-outline",
          pack: "ios",
          tag: "duplicate, paper, ios-copy-outline"
        },
        {
          name: "ios-email",
          pack: "ios",
          tag: "snail, mail, ios-email"
        },
        {
          name: "ios-email-outline",
          pack: "ios",
          tag: "snail, mail, ios-email-outline"
        },
        {
          name: "ios-undo",
          pack: "ios",
          tag: "reply, ios-undo"
        },
        {
          name: "ios-undo-outline",
          pack: "ios",
          tag: "reply, ios-undo-outline"
        },
        {
          name: "ios-redo",
          pack: "ios",
          tag: "forward, ios-redo"
        },
        {
          name: "ios-redo-outline",
          pack: "ios",
          tag: "forward, ios-redo-outline"
        },
        {
          name: "ios-paperplane",
          pack: "ios",
          tag: "send, ios-paperplane"
        },
        {
          name: "ios-paperplane-outline",
          pack: "ios",
          tag: "send, ios-paperplane-outline"
        },
        {
          name: "ios-folder",
          pack: "ios",
          tag: "file, ios-folder"
        },
        {
          name: "ios-folder-outline",
          pack: "ios",
          tag: "file, ios-folder-outline"
        },
        {
          name: "ios-paper",
          pack: "ios",
          tag: "feed, paper, ios-paper"
        },
        {
          name: "ios-paper-outline",
          pack: "ios",
          tag: "feed, paper, ios-paper-outline"
        },
        {
          name: "ios-list",
          pack: "ios",
          tag: "todo, feed, paper, ios-list"
        },
        {
          name: "ios-list-outline",
          pack: "ios",
          tag: "todo, feed, paper, ios-list-outline"
        },
        {
          name: "ios-world",
          pack: "ios",
          tag: "globe, earth, ios-world"
        },
        {
          name: "ios-world-outline",
          pack: "ios",
          tag: "globe, earth, ios-world-outline"
        },
        {
          name: "ios-alarm",
          pack: "ios",
          tag: "wake, ring, ios-alarm"
        },
        {
          name: "ios-alarm-outline",
          pack: "ios",
          tag: "wake, ring, ios-alarm-outline"
        },
        {
          name: "ios-speedometer",
          pack: "ios",
          tag: "speed, drive, level, ios-speedometer"
        },
        {
          name: "ios-speedometer-outline",
          pack: "ios",
          tag: "speed, drive, level, ios-speedometer-outline"
        },
        {
          name: "ios-stopwatch",
          pack: "ios",
          tag: "time, speed, ios-stopwatch"
        },
        {
          name: "ios-stopwatch-outline",
          pack: "ios",
          tag: "time, speed, ios-stopwatch-outline"
        },
        {
          name: "ios-timer",
          pack: "ios",
          tag: "cooking, alarm, buzz, ios-timer"
        },
        {
          name: "ios-timer-outline",
          pack: "ios",
          tag: "cooking, alarm, buzz, ios-timer-outline"
        },
        {
          name: "ios-clock",
          pack: "ios",
          tag: "time, date, hours, minutes, seconds, watch, ios-clock"
        },
        {
          name: "ios-clock-outline",
          pack: "ios",
          tag: "time, date, hours, minutes, seconds, watch, ios-clock-outline"
        },
        {
          name: "ios-time",
          pack: "ios",
          tag: "clock, watch, hour, minute, second, ios-time"
        },
        {
          name: "ios-time-outline",
          pack: "ios",
          tag: "clock, watch, hour, minute, second, ios-time-outline"
        },
        {
          name: "ios-calendar",
          pack: "ios",
          tag: "date, time, month, year, ios-calendar"
        },
        {
          name: "ios-calendar-outline",
          pack: "ios",
          tag: "date, time, month, year, ios-calendar-outline"
        },
        {
          name: "ios-photos",
          pack: "ios",
          tag: "images, stills, square, ios-photos"
        },
        {
          name: "ios-photos-outline",
          pack: "ios",
          tag: "images, stills, square, ios-photos-outline"
        },
        {
          name: "ios-albums",
          pack: "ios",
          tag: "square, boxes, slides, ios-albums"
        },
        {
          name: "ios-albums-outline",
          pack: "ios",
          tag: "square, boxes, slides, ios-albums-outline"
        },
        {
          name: "ios-camera",
          pack: "ios",
          tag: "picture, ios-camera"
        },
        {
          name: "ios-camera-outline",
          pack: "ios",
          tag: "picture, ios-camera-outline"
        },
        {
          name: "ios-reverse-camera",
          pack: "ios",
          tag: "picture, ios-reverse-camera"
        },
        {
          name: "ios-reverse-camera-outline",
          pack: "ios",
          tag: "picture, ios-reverse-camera-outline"
        },
        {
          name: "ios-eye",
          pack: "ios",
          tag: "view, see, exposed, look, ios-eye"
        },
        {
          name: "ios-eye-outline",
          pack: "ios",
          tag: "view, see, exposed, look, ios-eye-outline"
        },
        {
          name: "ios-bolt",
          pack: "ios",
          tag: "flash, lightning, ios-bolt"
        },
        {
          name: "ios-bolt-outline",
          pack: "ios",
          tag: "flash, lightning, ios-bolt-outline"
        },
        {
          name: "ios-color-wand",
          pack: "ios",
          tag: "camera, picture, edit, magic, ios-color-wand"
        },
        {
          name: "ios-color-wand-outline",
          pack: "ios",
          tag: "camera, picture, edit, magic, ios-color-wand-outline"
        },
        {
          name: "ios-color-filter",
          pack: "ios",
          tag: "camera, picture, ios-color-filter"
        },
        {
          name: "ios-color-filter-outline",
          pack: "ios",
          tag: "camera, picture, ios-color-filter-outline"
        },
        {
          name: "ios-grid-view",
          pack: "ios",
          tag: "camera, picture, ios-grid-view"
        },
        {
          name: "ios-grid-view-outline",
          pack: "ios",
          tag: "camera, picture, ios-grid-view-outline"
        },
        {
          name: "ios-crop-strong",
          pack: "ios",
          tag: "camera, picture, edit, ios-crop-strong"
        },
        {
          name: "ios-crop",
          pack: "ios",
          tag: "camera, picture, edit, ios-crop"
        },
        {
          name: "ios-barcode",
          pack: "ios",
          tag: "reader, camera, ios-barcode"
        },
        {
          name: "ios-barcode-outline",
          pack: "ios",
          tag: "reader, camera, ios-barcode-outline"
        },
        {
          name: "ios-briefcase",
          pack: "ios",
          tag: "organize, folder, ios-briefcase"
        },
        {
          name: "ios-briefcase-outline",
          pack: "ios",
          tag: "organize, folder, ios-briefcase-outline"
        },
        {
          name: "ios-medkit",
          pack: "ios",
          tag: "health, case, first aid, sick, disease, ios-medkit"
        },
        {
          name: "ios-medkit-outline",
          pack: "ios",
          tag: "health, case, first aid, sick, disease, ios-medkit-outline"
        },
        {
          name: "ios-medical",
          pack: "ios",
          tag: "health, case, first aid, sick, disease, ios-medical"
        },
        {
          name: "ios-medical-outline",
          pack: "ios",
          tag: "health, case, first aid, sick, disease, ios-medical-outline"
        },
        {
          name: "ios-infinite",
          pack: "ios",
          tag: "forever, loop, ios-infinite"
        },
        {
          name: "ios-infinite-outline",
          pack: "ios",
          tag: "forever, loop, ios-infinite-outline"
        },
        {
          name: "ios-calculator",
          pack: "ios",
          tag: "math, arithmatic, ios-calculator"
        },
        {
          name: "ios-calculator-outline",
          pack: "ios",
          tag: "math, arithmatic, ios-calculator-outline"
        },
        {
          name: "ios-keypad",
          pack: "ios",
          tag: "type, grid, circle, ios-keypad"
        },
        {
          name: "ios-keypad-outline",
          pack: "ios",
          tag: "type, grid, circle, ios-keypad-outline"
        },
        {
          name: "ios-telephone",
          pack: "ios",
          tag: "oldschool, call, ios-telephone"
        },
        {
          name: "ios-telephone-outline",
          pack: "ios",
          tag: "oldschool, call, ios-telephone-outline"
        },
        {
          name: "ios-drag",
          pack: "ios",
          tag: "reorder, move, drag, ios-drag"
        },
        {
          name: "ios-location",
          pack: "ios",
          tag: "navigation, map, gps, pin, ios-location"
        },
        {
          name: "ios-location-outline",
          pack: "ios",
          tag: "navigation, map, gps, pin, ios-location-outline"
        },
        {
          name: "ios-navigate",
          pack: "ios",
          tag: "location, map, gps, pin, ios-navigate"
        },
        {
          name: "ios-navigate-outline",
          pack: "ios",
          tag: "location, map, gps, pin, ios-navigate-outline"
        },
        {
          name: "ios-locked",
          pack: "ios",
          tag: "security, padlock, safe, ios-locked"
        },
        {
          name: "ios-locked-outline",
          pack: "ios",
          tag: "security, padlock, safe, ios-locked-outline"
        },
        {
          name: "ios-unlocked",
          pack: "ios",
          tag: "security, padlock, safe, ios-unlocked"
        },
        {
          name: "ios-unlocked-outline",
          pack: "ios",
          tag: "security, padlock, safe, ios-unlocked-outline"
        },
        {
          name: "ios-monitor",
          pack: "ios",
          tag: "thunderbolt, display, screen, ios-monitor"
        },
        {
          name: "ios-monitor-outline",
          pack: "ios",
          tag: "thunderbolt, display, screen, ios-monitor-outline"
        },
        {
          name: "ios-printer",
          pack: "ios",
          tag: "paper, ios-printer"
        },
        {
          name: "ios-printer-outline",
          pack: "ios",
          tag: "paper, ios-printer-outline"
        },
        {
          name: "ios-game-controller-a",
          pack: "ios",
          tag: "gaming, nintendo, play, ios-game-controller-a"
        },
        {
          name: "ios-game-controller-a-outline",
          pack: "ios",
          tag: "gaming, nintendo, play, ios-game-controller-a-outline"
        },
        {
          name: "ios-game-controller-b",
          pack: "ios",
          tag: "gaming, nintendo, play, ios-game-controller-b"
        },
        {
          name: "ios-game-controller-b-outline",
          pack: "ios",
          tag: "gaming, nintendo, play, ios-game-controller-b-outline"
        },
        {
          name: "ios-americanfootball",
          pack: "ios",
          tag: "nfl, games, sports, fun, play, ios-americanfootball"
        },
        {
          name: "ios-americanfootball-outline",
          pack: "ios",
          tag: "nfl, games, sports, fun, play, ios-americanfootball-outline"
        },
        {
          name: "ios-baseball",
          pack: "ios",
          tag: "mlb, games, sports, fun, play, ios-baseball"
        },
        {
          name: "ios-baseball-outline",
          pack: "ios",
          tag: "mlb, games, sports, fun, play, ios-baseball-outline"
        },
        {
          name: "ios-basketball",
          pack: "ios",
          tag: "nba, games, sports, fun, play, ios-basketball"
        },
        {
          name: "ios-basketball-outline",
          pack: "ios",
          tag: "nba, games, sports, fun, play, ios-basketball-outline"
        },
        {
          name: "ios-tennisball",
          pack: "ios",
          tag: "games, sports, fun, play, ios-tennisball"
        },
        {
          name: "ios-tennisball-outline",
          pack: "ios",
          tag: "games, sports, fun, play, ios-tennisball-outline"
        },
        {
          name: "ios-football",
          pack: "ios",
          tag: "mls, soccer, games, sports, fun, play, ios-football"
        },
        {
          name: "ios-football-outline",
          pack: "ios",
          tag: "mls, soccer, games, sports, fun, play, ios-football-outline"
        },
        {
          name: "ios-body",
          pack: "ios",
          tag: "person, users, staff, head, human, ios-body"
        },
        {
          name: "ios-body-outline",
          pack: "ios",
          tag: "person, users, staff, head, human, ios-body-outline"
        },
        {
          name: "ios-person",
          pack: "ios",
          tag: "users, staff, head, human, ios-person"
        },
        {
          name: "ios-person-outline",
          pack: "ios",
          tag: "users, staff, head, human, ios-person-outline"
        },
        {
          name: "ios-personadd",
          pack: "ios",
          tag: "users, staff, head, human, new, invite, ios-personadd"
        },
        {
          name: "ios-personadd-outline",
          pack: "ios",
          tag: "users, staff, head, human, new, invite, ios-personadd-outline"
        },
        {
          name: "ios-people",
          pack: "ios",
          tag: "stalker, person, users, head, human, ios-people"
        },
        {
          name: "ios-people-outline",
          pack: "ios",
          tag: "stalker, person, users, head, human, ios-people-outline"
        },
        {
          name: "ios-musical-notes",
          pack: "ios",
          tag: "sound, noise, listening, play, ios-musical-notes"
        },
        {
          name: "ios-musical-note",
          pack: "ios",
          tag: "sound, noise, listening, play, ios-musical-note"
        },
        {
          name: "ios-bell",
          pack: "ios",
          tag: "right, noise, alarm, sound, music, ios-bell"
        },
        {
          name: "ios-bell-outline",
          pack: "ios",
          tag: "right, noise, alarm, sound, music, ios-bell-outline"
        },
        {
          name: "ios-mic",
          pack: "ios",
          tag: "sound, noise, speaker, talk, ios-mic"
        },
        {
          name: "ios-mic-outline",
          pack: "ios",
          tag: "sound, noise, speaker, talk, ios-mic-outline"
        },
        {
          name: "ios-mic-off",
          pack: "ios",
          tag: "sound, noise, speaker, talk, ios-mic-off"
        },
        {
          name: "ios-volume-high",
          pack: "ios",
          tag: "sound, noise, listen, music, ios-volume-high"
        },
        {
          name: "ios-volume-low",
          pack: "ios",
          tag: "sound, noise, listen, music, ios-volume-low"
        },
        {
          name: "ios-play",
          pack: "ios",
          tag: "music, watch, arrow, right, ios-play"
        },
        {
          name: "ios-play-outline",
          pack: "ios",
          tag: "music, watch, arrow, right, ios-play-outline"
        },
        {
          name: "ios-pause",
          pack: "ios",
          tag: "music, break, hold, freeze, ios-pause"
        },
        {
          name: "ios-pause-outline",
          pack: "ios",
          tag: "music, break, hold, freeze, ios-pause-outline"
        },
        {
          name: "ios-recording",
          pack: "ios",
          tag: "film, tape, voicemail, ios-recording"
        },
        {
          name: "ios-recording-outline",
          pack: "ios",
          tag: "film, tape, voicemail, ios-recording-outline"
        },
        {
          name: "ios-fastforward",
          pack: "ios",
          tag: "next, skip, jump, ios-fastforward"
        },
        {
          name: "ios-fastforward-outline",
          pack: "ios",
          tag: "next, skip, jump, ios-fastforward-outline"
        },
        {
          name: "ios-rewind",
          pack: "ios",
          tag: "music, previous, back, ios-rewind"
        },
        {
          name: "ios-rewind-outline",
          pack: "ios",
          tag: "music, previous, back, ios-rewind-outline"
        },
        {
          name: "ios-skipbackward",
          pack: "ios",
          tag: "music, previous, ios-skipbackward"
        },
        {
          name: "ios-skipbackward-outline",
          pack: "ios",
          tag: "music, previous, ios-skipbackward-outline"
        },
        {
          name: "ios-skipforward",
          pack: "ios",
          tag: "music, next, ios-skipforward"
        },
        {
          name: "ios-skipforward-outline",
          pack: "ios",
          tag: "music, next, ios-skipforward-outline"
        },
        {
          name: "ios-shuffle-strong",
          pack: "ios",
          tag: "music, next, ios-shuffle-strong"
        },
        {
          name: "ios-shuffle",
          pack: "ios",
          tag: "music, next, ios-shuffle"
        },
        {
          name: "ios-videocam",
          pack: "ios",
          tag: "film, movie, camera, ios-videocam"
        },
        {
          name: "ios-videocam-outline",
          pack: "ios",
          tag: "film, movie, camera, ios-videocam-outline"
        },
        {
          name: "ios-film",
          pack: "ios",
          tag: "film, movie, camera, ios-film"
        },
        {
          name: "ios-film-outline",
          pack: "ios",
          tag: "film, movie, camera, ios-film-outline"
        },
        {
          name: "ios-flask",
          pack: "ios",
          tag: "options, mixer, liquid, ios-flask"
        },
        {
          name: "ios-flask-outline",
          pack: "ios",
          tag: "options, mixer, liquid, ios-flask-outline"
        },
        {
          name: "ios-lightbulb",
          pack: "ios",
          tag: "idea, new, bright, aha!, ios-lightbulb"
        },
        {
          name: "ios-lightbulb-outline",
          pack: "ios",
          tag: "idea, new, bright, aha!, ios-lightbulb-outline"
        },
        {
          name: "ios-wineglass",
          pack: "ios",
          tag: "alcohol, drink, food, glass, drunk, cheers, ios-wineglass"
        },
        {
          name: "ios-wineglass-outline",
          pack: "ios",
          tag:
            "alcohol, drink, food, glass, drunk, cheers, ios-wineglass-outline"
        },
        {
          name: "ios-pint",
          pack: "ios",
          tag: "alcohol, drink, food, beer, drunk, cheers, ios-pint"
        },
        {
          name: "ios-pint-outline",
          pack: "ios",
          tag: "alcohol, drink, food, beer, drunk, cheers, ios-pint-outline"
        },
        {
          name: "ios-nutrition",
          pack: "ios",
          tag: "health, carrot, food, ios-nutrition"
        },
        {
          name: "ios-nutrition-outline",
          pack: "ios",
          tag: "health, carrot, food, ios-nutrition-outline"
        },
        {
          name: "ios-flower",
          pack: "ios",
          tag: "nature, spring, leaf, garden, ios-flower"
        },
        {
          name: "ios-flower-outline",
          pack: "ios",
          tag: "nature, spring, leaf, garden, ios-flower-outline"
        },
        {
          name: "ios-rose",
          pack: "ios",
          tag: "nature, spring, leaf, garden, flower, ios-rose"
        },
        {
          name: "ios-rose-outline",
          pack: "ios",
          tag: "nature, spring, leaf, garden, flower, ios-rose-outline"
        },
        {
          name: "ios-paw",
          pack: "ios",
          tag: "nature, animal, pet, outdoor, track, ios-paw"
        },
        {
          name: "ios-paw-outline",
          pack: "ios",
          tag: "nature, animal, pet, outdoor, track, ios-paw-outline"
        },
        {
          name: "ios-flame",
          pack: "ios",
          tag: "fire, hot, burn, ios-flame"
        },
        {
          name: "ios-flame-outline",
          pack: "ios",
          tag: "fire, hot, burn, ios-flame-outline"
        },
        {
          name: "ios-sunny",
          pack: "ios",
          tag: "weather, whether, light, sky, ios-sunny"
        },
        {
          name: "ios-sunny-outline",
          pack: "ios",
          tag: "weather, whether, light, sky, ios-sunny-outline"
        },
        {
          name: "ios-partlysunny",
          pack: "ios",
          tag: "light, weather, whether, cloudy, ios-partlysunny"
        },
        {
          name: "ios-partlysunny-outline",
          pack: "ios",
          tag: "light, weather, whether, cloudy, ios-partlysunny-outline"
        },
        {
          name: "ios-cloudy",
          pack: "ios",
          tag: "weather, whether, overcast, ios-cloudy"
        },
        {
          name: "ios-cloudy-outline",
          pack: "ios",
          tag: "weather, whether, overcast, ios-cloudy-outline"
        },
        {
          name: "ios-rainy",
          pack: "ios",
          tag: "whether, weather, water, cloud, ios-rainy"
        },
        {
          name: "ios-rainy-outline",
          pack: "ios",
          tag: "whether, weather, water, cloud, ios-rainy-outline"
        },
        {
          name: "ios-thunderstorm",
          pack: "ios",
          tag:
            "whether, weather, sky, lightning, rain, cloudy, overcast, storm, ios-thunderstorm"
        },
        {
          name: "ios-thunderstorm-outline",
          pack: "ios",
          tag:
            "whether, weather, sky, lightning, rain, cloudy, overcast, storm, ios-thunderstorm-outline"
        },
        {
          name: "ios-snowy",
          pack: "ios",
          tag: "cold, weather, whether, overcast, ios-snowy"
        },
        {
          name: "ios-moon",
          pack: "ios",
          tag: "sky, night, dark, ios-moon"
        },
        {
          name: "ios-moon-outline",
          pack: "ios",
          tag: "sky, night, dark, ios-moon-outline"
        },
        {
          name: "ios-cloudy-night",
          pack: "ios",
          tag: "weather, whether, overcast, ios-cloudy-night"
        },
        {
          name: "ios-cloudy-night-outline",
          pack: "ios",
          tag: "weather, whether, overcast, ios-cloudy-night-outline"
        },
        {
          name: "android-arrow-up",
          pack: "android",
          tag: "chevron, navigation, android-arrow-up"
        },
        {
          name: "android-arrow-forward",
          pack: "android",
          tag: "chevron, navigation, android-arrow-forward"
        },
        {
          name: "android-arrow-down",
          pack: "android",
          tag: "chevron, navigation, android-arrow-down"
        },
        {
          name: "android-arrow-back",
          pack: "android",
          tag: "chevron, navigation, android-arrow-back"
        },
        {
          name: "android-arrow-dropup",
          pack: "android",
          tag: "chevron, navigation, android-arrow-dropup"
        },
        {
          name: "android-arrow-dropup-circle",
          pack: "android",
          tag: "chevron, navigation, android-arrow-dropup-circle"
        },
        {
          name: "android-arrow-dropright",
          pack: "android",
          tag: "chevron, navigation, android-arrow-dropright"
        },
        {
          name: "android-arrow-dropright-circle",
          pack: "android",
          tag: "chevron, navigation, android-arrow-dropright-circle"
        },
        {
          name: "android-arrow-dropdown",
          pack: "android",
          tag: "chevron, navigation, android-arrow-dropdown"
        },
        {
          name: "android-arrow-dropdown-circle",
          pack: "android",
          tag: "chevron, navigation, android-arrow-dropdown-circle"
        },
        {
          name: "android-arrow-dropleft",
          pack: "android",
          tag: "chevron, navigation, android-arrow-dropleft"
        },
        {
          name: "android-arrow-dropleft-circle",
          pack: "android",
          tag: "chevron, navigation, android-arrow-dropleft-circle"
        },
        {
          name: "android-add",
          pack: "android",
          tag: "plus, include, invite, android-add"
        },
        {
          name: "android-add-circle",
          pack: "android",
          tag: "plus, include, invite, android-add-circle"
        },
        {
          name: "android-remove",
          pack: "android",
          tag: "minus, subtract, delete, android-remove"
        },
        {
          name: "android-remove-circle",
          pack: "android",
          tag: "minus, subtract, delete, android-remove-circle"
        },
        {
          name: "android-close",
          pack: "android",
          tag: "delete, remove, android-close"
        },
        {
          name: "android-cancel",
          pack: "android",
          tag: "delete, remove, android-cancel"
        },
        {
          name: "android-radio-button-off",
          pack: "android",
          tag: ", android-radio-button-off"
        },
        {
          name: "android-radio-button-on",
          pack: "android",
          tag: ", android-radio-button-on"
        },
        {
          name: "android-checkmark-circle",
          pack: "android",
          tag: ", android-checkmark-circle"
        },
        {
          name: "android-checkbox-outline-blank",
          pack: "android",
          tag: ", android-checkbox-outline-blank"
        },
        {
          name: "android-checkbox-outline",
          pack: "android",
          tag: ", android-checkbox-outline"
        },
        {
          name: "android-checkbox-blank",
          pack: "android",
          tag: ", android-checkbox-blank"
        },
        {
          name: "android-checkbox",
          pack: "android",
          tag: ", android-checkbox"
        },
        {
          name: "android-done",
          pack: "android",
          tag: ", android-done"
        },
        {
          name: "android-done-all",
          pack: "android",
          tag: ", android-done-all"
        },
        {
          name: "android-menu",
          pack: "android",
          tag: ", android-menu"
        },
        {
          name: "android-more-horizontal",
          pack: "android",
          tag: "options, menu, android-more-horizontal"
        },
        {
          name: "android-more-vertical",
          pack: "android",
          tag: "options, menu, android-more-vertical"
        },
        {
          name: "android-refresh",
          pack: "android",
          tag: ", android-refresh"
        },
        {
          name: "android-sync",
          pack: "android",
          tag: ", android-sync"
        },
        {
          name: "android-wifi",
          pack: "android",
          tag: "internet,connection, bars, android-wifi"
        },
        {
          name: "android-call",
          pack: "android",
          tag: "telephone, android-call"
        },
        {
          name: "android-apps",
          pack: "android",
          tag: ", android-apps"
        },
        {
          name: "android-settings",
          pack: "android",
          tag: "options, android-settings"
        },
        {
          name: "android-options",
          pack: "android",
          tag: "settings, mixer, android-options"
        },
        {
          name: "android-funnel",
          pack: "android",
          tag: ", android-funnel"
        },
        {
          name: "android-search",
          pack: "android",
          tag: "magnifying glass, android-search"
        },
        {
          name: "android-home",
          pack: "android",
          tag: ", android-home"
        },
        {
          name: "android-cloud-outline",
          pack: "android",
          tag: ", android-cloud-outline"
        },
        {
          name: "android-cloud",
          pack: "android",
          tag: ", android-cloud"
        },
        {
          name: "android-download",
          pack: "android",
          tag: ", android-download"
        },
        {
          name: "android-upload",
          pack: "android",
          tag: ", android-upload"
        },
        {
          name: "android-cloud-done",
          pack: "android",
          tag: ", android-cloud-done"
        },
        {
          name: "android-cloud-circle",
          pack: "android",
          tag: ", android-cloud-circle"
        },
        {
          name: "android-favorite-outline",
          pack: "android",
          tag: "favorite, like, rate, android-favorite-outline"
        },
        {
          name: "android-favorite",
          pack: "android",
          tag: "favorite, like, rate, android-favorite"
        },
        {
          name: "android-star-outline",
          pack: "android",
          tag: "favorite, like, rate, android-star-outline"
        },
        {
          name: "android-star-half",
          pack: "android",
          tag: "favorite, like, rate, android-star-half"
        },
        {
          name: "android-star",
          pack: "android",
          tag: "favorite, like, rate, android-star"
        },
        {
          name: "android-calendar",
          pack: "android",
          tag: "clock, android-calendar"
        },
        {
          name: "android-alarm-clock",
          pack: "android",
          tag: "clock, android-alarm-clock"
        },
        {
          name: "android-time",
          pack: "android",
          tag: "clock, android-time"
        },
        {
          name: "android-stopwatch",
          pack: "android",
          tag: ", android-stopwatch"
        },
        {
          name: "android-watch",
          pack: "android",
          tag: ", android-watch"
        },
        {
          name: "android-locate",
          pack: "android",
          tag: ", android-locate"
        },
        {
          name: "android-navigate",
          pack: "android",
          tag: ", android-navigate"
        },
        {
          name: "android-pin",
          pack: "android",
          tag: ", android-pin"
        },
        {
          name: "android-compass",
          pack: "android",
          tag: ", android-compass"
        },
        {
          name: "android-map",
          pack: "android",
          tag: ", android-map"
        },
        {
          name: "android-walk",
          pack: "android",
          tag: ", android-walk"
        },
        {
          name: "android-bicycle",
          pack: "android",
          tag: "move, bike, transportation, maps, android-bicycle"
        },
        {
          name: "android-car",
          pack: "android",
          tag: ", android-car"
        },
        {
          name: "android-bus",
          pack: "android",
          tag: ", android-bus"
        },
        {
          name: "android-subway",
          pack: "android",
          tag: ", android-subway"
        },
        {
          name: "android-train",
          pack: "android",
          tag: ", android-train"
        },
        {
          name: "android-boat",
          pack: "android",
          tag: ", android-boat"
        },
        {
          name: "android-plane",
          pack: "android",
          tag: ", android-plane"
        },
        {
          name: "android-restaurant",
          pack: "android",
          tag: ", android-restaurant"
        },
        {
          name: "android-bar",
          pack: "android",
          tag: "wine, drink, food, dinner, android-bar"
        },
        {
          name: "android-cart",
          pack: "android",
          tag: ", android-cart"
        },
        {
          name: "android-camera",
          pack: "android",
          tag: ", android-camera"
        },
        {
          name: "android-image",
          pack: "android",
          tag: ", android-image"
        },
        {
          name: "android-film",
          pack: "android",
          tag: ", android-film"
        },
        {
          name: "android-color-palette",
          pack: "android",
          tag: ", android-color-palette"
        },
        {
          name: "android-create",
          pack: "android",
          tag: ", android-create"
        },
        {
          name: "android-mail",
          pack: "android",
          tag: ", android-mail"
        },
        {
          name: "android-drafts",
          pack: "android",
          tag: ", android-drafts"
        },
        {
          name: "android-send",
          pack: "android",
          tag: ", android-send"
        },
        {
          name: "android-archive",
          pack: "android",
          tag: ", android-archive"
        },
        {
          name: "android-delete",
          pack: "android",
          tag: ", android-delete"
        },
        {
          name: "android-attach",
          pack: "android",
          tag: ", android-attach"
        },
        {
          name: "android-share",
          pack: "android",
          tag: ", android-share"
        },
        {
          name: "android-share-alt",
          pack: "android",
          tag: ", android-share-alt"
        },
        {
          name: "android-bookmark",
          pack: "android",
          tag: ", android-bookmark"
        },
        {
          name: "android-document",
          pack: "android",
          tag: ", android-document"
        },
        {
          name: "android-clipboard",
          pack: "android",
          tag: ", android-clipboard"
        },
        {
          name: "android-list",
          pack: "android",
          tag: ", android-list"
        },
        {
          name: "android-folder-open",
          pack: "android",
          tag: ", android-folder-open"
        },
        {
          name: "android-folder",
          pack: "android",
          tag: ", android-folder"
        },
        {
          name: "android-print",
          pack: "android",
          tag: ", android-print"
        },
        {
          name: "android-open",
          pack: "android",
          tag: ", android-open"
        },
        {
          name: "android-exit",
          pack: "android",
          tag: ", android-exit"
        },
        {
          name: "android-contract",
          pack: "android",
          tag: ", android-contract"
        },
        {
          name: "android-expand",
          pack: "android",
          tag: ", android-expand"
        },
        {
          name: "android-globe",
          pack: "android",
          tag: ", android-globe"
        },
        {
          name: "android-chat",
          pack: "android",
          tag: "talk, text, android-chat"
        },
        {
          name: "android-textsms",
          pack: "android",
          tag: "talk, text, android-textsms"
        },
        {
          name: "android-hangout",
          pack: "android",
          tag: ", android-hangout"
        },
        {
          name: "android-happy",
          pack: "android",
          tag: ", android-happy"
        },
        {
          name: "android-sad",
          pack: "android",
          tag: ", android-sad"
        },
        {
          name: "android-person",
          pack: "android",
          tag: ", android-person"
        },
        {
          name: "android-people",
          pack: "android",
          tag: ", android-people"
        },
        {
          name: "android-person-add",
          pack: "android",
          tag: ", android-person-add"
        },
        {
          name: "android-contact",
          pack: "android",
          tag: ", android-contact"
        },
        {
          name: "android-contacts",
          pack: "android",
          tag: ", android-contacts"
        },
        {
          name: "android-playstore",
          pack: "android",
          tag: ", android-playstore"
        },
        {
          name: "android-lock",
          pack: "android",
          tag: ", android-lock"
        },
        {
          name: "android-unlock",
          pack: "android",
          tag: ", android-unlock"
        },
        {
          name: "android-microphone",
          pack: "android",
          tag: "recorder, speak, noise, music, sound, android-microphone"
        },
        {
          name: "android-microphone-off",
          pack: "android",
          tag:
            "recorder, speak, noise, music, sound, mute, android-microphone-off"
        },
        {
          name: "android-notifications-none",
          pack: "android",
          tag: ", android-notifications-none"
        },
        {
          name: "android-notifications",
          pack: "android",
          tag: ", android-notifications"
        },
        {
          name: "android-notifications-off",
          pack: "android",
          tag: ", android-notifications-off"
        },
        {
          name: "android-volume-mute",
          pack: "android",
          tag: ", android-volume-mute"
        },
        {
          name: "android-volume-down",
          pack: "android",
          tag: ", android-volume-down"
        },
        {
          name: "android-volume-up",
          pack: "android",
          tag: ", android-volume-up"
        },
        {
          name: "android-volume-off",
          pack: "android",
          tag: ", android-volume-off"
        },
        {
          name: "android-hand",
          pack: "android",
          tag: "stop, android-hand"
        },
        {
          name: "android-desktop",
          pack: "android",
          tag: ", android-desktop"
        },
        {
          name: "android-laptop",
          pack: "android",
          tag: ", android-laptop"
        },
        {
          name: "android-phone-portrait",
          pack: "android",
          tag: ", android-phone-portrait"
        },
        {
          name: "android-phone-landscape",
          pack: "android",
          tag: ", android-phone-landscape"
        },
        {
          name: "android-bulb",
          pack: "android",
          tag: ", android-bulb"
        },
        {
          name: "android-sunny",
          pack: "android",
          tag: ", android-sunny"
        },
        {
          name: "android-alert",
          pack: "android",
          tag: ", android-alert"
        },
        {
          name: "android-warning",
          pack: "android",
          tag: ", android-warning"
        },
        {
          name: "social-twitter",
          pack: "social",
          tag: "follow, post, share, social-twitter"
        },
        {
          name: "social-twitter-outline",
          pack: "social",
          tag: "follow, post, share, social-twitter-outline"
        },
        {
          name: "social-facebook",
          pack: "social",
          tag: "like, post, share, social-facebook"
        },
        {
          name: "social-facebook-outline",
          pack: "social",
          tag: "like, post, share, social-facebook-outline"
        },
        {
          name: "social-googleplus",
          pack: "social",
          tag: "follow, post, share, social-googleplus"
        },
        {
          name: "social-googleplus-outline",
          pack: "social",
          tag: "follow, post, share, social-googleplus-outline"
        },
        {
          name: "social-google",
          pack: "social",
          tag: "follow, post, share, social-google"
        },
        {
          name: "social-google-outline",
          pack: "social",
          tag: "follow, post, share, social-google-outline"
        },
        {
          name: "social-dribbble",
          pack: "social",
          tag: "design, social-dribbble"
        },
        {
          name: "social-dribbble-outline",
          pack: "social",
          tag: "design, social-dribbble-outline"
        },
        {
          name: "social-octocat",
          pack: "social",
          tag: "code, github, fork, merge, clone, social-octocat"
        },
        {
          name: "social-github",
          pack: "social",
          tag: "code, fork, merge, clone, social-github"
        },
        {
          name: "social-github-outline",
          pack: "social",
          tag: "code, fork, merge, clone, social-github-outline"
        },
        {
          name: "social-instagram",
          pack: "social",
          tag: "photo, camera, facebook, social-instagram"
        },
        {
          name: "social-instagram-outline",
          pack: "social",
          tag: "photo, camera, facebook, social-instagram-outline"
        },
        {
          name: "social-whatsapp",
          pack: "social",
          tag: "text, sharing, private, facebook, social-whatsapp"
        },
        {
          name: "social-whatsapp-outline",
          pack: "social",
          tag: "text, sharing, private, facebook, social-whatsapp-outline"
        },
        {
          name: "social-snapchat",
          pack: "social",
          tag: "photos, app, social-snapchat"
        },
        {
          name: "social-snapchat-outline",
          pack: "social",
          tag: "photos, app, social-snapchat-outline"
        },
        {
          name: "social-foursquare",
          pack: "social",
          tag: "checkin, social-foursquare"
        },
        {
          name: "social-foursquare-outline",
          pack: "social",
          tag: "checkin, social-foursquare-outline"
        },
        {
          name: "social-pinterest",
          pack: "social",
          tag: "social, social-pinterest"
        },
        {
          name: "social-pinterest-outline",
          pack: "social",
          tag: "social, social-pinterest-outline"
        },
        {
          name: "social-rss",
          pack: "social",
          tag: "blogging, social-rss"
        },
        {
          name: "social-rss-outline",
          pack: "social",
          tag: "blogging, social-rss-outline"
        },
        {
          name: "social-tumblr",
          pack: "social",
          tag: "blogging, social-tumblr"
        },
        {
          name: "social-tumblr-outline",
          pack: "social",
          tag: "blogging, social-tumblr-outline"
        },
        {
          name: "social-wordpress",
          pack: "social",
          tag: "blogging, social-wordpress"
        },
        {
          name: "social-wordpress-outline",
          pack: "social",
          tag: "blogging, social-wordpress-outline"
        },
        {
          name: "social-reddit",
          pack: "social",
          tag: "news, upvotes, karma, social-reddit"
        },
        {
          name: "social-reddit-outline",
          pack: "social",
          tag: "news, upvotes, karma, social-reddit-outline"
        },
        {
          name: "social-hackernews",
          pack: "social",
          tag: "discuss, upvotes, karma, social-hackernews"
        },
        {
          name: "social-hackernews-outline",
          pack: "social",
          tag: "discuss, upvotes, karma, social-hackernews-outline"
        },
        {
          name: "social-designernews",
          pack: "social",
          tag: "design, post, social-designernews"
        },
        {
          name: "social-designernews-outline",
          pack: "social",
          tag: "design, post, social-designernews-outline"
        },
        {
          name: "social-yahoo",
          pack: "social",
          tag: ", social-yahoo"
        },
        {
          name: "social-yahoo-outline",
          pack: "social",
          tag: ", social-yahoo-outline"
        },
        {
          name: "social-buffer",
          pack: "social",
          tag: "share, social-buffer"
        },
        {
          name: "social-buffer-outline",
          pack: "social",
          tag: "share, social-buffer-outline"
        },
        {
          name: "social-skype",
          pack: "social",
          tag: "call, social-skype"
        },
        {
          name: "social-skype-outline",
          pack: "social",
          tag: "call, social-skype-outline"
        },
        {
          name: "social-linkedin",
          pack: "social",
          tag: "connect, social-linkedin"
        },
        {
          name: "social-linkedin-outline",
          pack: "social",
          tag: "connect, social-linkedin-outline"
        },
        {
          name: "social-vimeo",
          pack: "social",
          tag: "video, watch, share, view, social-vimeo"
        },
        {
          name: "social-vimeo-outline",
          pack: "social",
          tag: "video, watch, share, view, social-vimeo-outline"
        },
        {
          name: "social-twitch",
          pack: "social",
          tag:
            "gaming, games, live, streaming, video, watch, share, view, social-twitch"
        },
        {
          name: "social-twitch-outline",
          pack: "social",
          tag:
            "gaming, games, live, streaming, video, watch, share, view, social-twitch-outline"
        },
        {
          name: "social-youtube",
          pack: "social",
          tag: "video, watch, share, view, social-youtube"
        },
        {
          name: "social-youtube-outline",
          pack: "social",
          tag: "video, watch, share, view, social-youtube-outline"
        },
        {
          name: "social-dropbox",
          pack: "social",
          tag: "upload, social-dropbox"
        },
        {
          name: "social-dropbox-outline",
          pack: "social",
          tag: "upload, social-dropbox-outline"
        },
        {
          name: "social-apple",
          pack: "social",
          tag: "mac, mobile, social-apple"
        },
        {
          name: "social-apple-outline",
          pack: "social",
          tag: "mac, mobile, social-apple-outline"
        },
        {
          name: "social-android",
          pack: "social",
          tag: "mobile, social-android"
        },
        {
          name: "social-android-outline",
          pack: "social",
          tag: "mobile, social-android-outline"
        },
        {
          name: "social-windows",
          pack: "social",
          tag: "pc, social-windows"
        },
        {
          name: "social-windows-outline",
          pack: "social",
          tag: "pc, social-windows-outline"
        },
        {
          name: "social-html5",
          pack: "social",
          tag: "code, html, css, js, developer, social-html5"
        },
        {
          name: "social-html5-outline",
          pack: "social",
          tag: "code, html, css, js, developer, social-html5-outline"
        },
        {
          name: "social-css3",
          pack: "social",
          tag: "code, html, css, js, developer, social-css3"
        },
        {
          name: "social-css3-outline",
          pack: "social",
          tag: "code, html, css, js, developer, social-css3-outline"
        },
        {
          name: "social-javascript",
          pack: "social",
          tag: "code, html, css, js, developer, social-javascript"
        },
        {
          name: "social-javascript-outline",
          pack: "social",
          tag: "code, html, css, js, developer, social-javascript-outline"
        },
        {
          name: "social-angular",
          pack: "social",
          tag: "code, mobile, js, angularjs, ionic, social-angular"
        },
        {
          name: "social-angular-outline",
          pack: "social",
          tag: "code, mobile, js, angularjs, ionic, social-angular-outline"
        },
        {
          name: "social-nodejs",
          pack: "social",
          tag: "code, js, javascript, developer, social-nodejs"
        },
        {
          name: "social-sass",
          pack: "social",
          tag: "code, css, social-sass"
        },
        {
          name: "social-python",
          pack: "social",
          tag: "code, social-python"
        },
        {
          name: "social-chrome",
          pack: "social",
          tag: "code, mobile, js, angularjs, ionic, social-chrome"
        },
        {
          name: "social-chrome-outline",
          pack: "social",
          tag: "code, mobile, js, angularjs, ionic, social-chrome-outline"
        },
        {
          name: "social-codepen",
          pack: "social",
          tag: "testing, js, developer, social-codepen"
        },
        {
          name: "social-codepen-outline",
          pack: "social",
          tag: "testing, js, developer, social-codepen-outline"
        },
        {
          name: "social-markdown",
          pack: "social",
          tag: "code, testing, text, developer, social-markdown"
        },
        {
          name: "social-tux",
          pack: "social",
          tag: "code, linux, opensource, social-tux"
        },
        {
          name: "social-freebsd-devil",
          pack: "social",
          tag: "code, opensource, unix, social-freebsd-devil"
        },
        {
          name: "social-usd",
          pack: "social",
          tag: "currency, trade, money, cash, social-usd"
        },
        {
          name: "social-usd-outline",
          pack: "social",
          tag: "currency, trade, money, cash, social-usd-outline"
        },
        {
          name: "social-bitcoin",
          pack: "social",
          tag: "currency, trade, money, social-bitcoin"
        },
        {
          name: "social-bitcoin-outline",
          pack: "social",
          tag: "currency, trade, money, social-bitcoin-outline"
        },
        {
          name: "social-yen",
          pack: "social",
          tag: "currency, trade, money, japanese, social-yen"
        },
        {
          name: "social-yen-outline",
          pack: "social",
          tag: "currency, trade, money, japanese, social-yen-outline"
        },
        {
          name: "social-euro",
          pack: "social",
          tag: "currency, trade, money, europe, social-euro"
        },
        {
          name: "social-euro-outline",
          pack: "social",
          tag: "currency, trade, money, europe, social-euro-outline"
        }
      ]
    };
  },
  methods: {
      // 图标选择
      icon_handler (val){
          this.form_detail.currentIcon = val;
      },
      //提交
      submit () {
        this.$emit('icon_update',this.form_detail.currentIcon);
        this.modal_show = false;
      },
      //取消
      cancel () {
        this.modal_show = false;
        this.modal_reset();
      },
      // 图标选取界面初始化
      modal_reset () {
        this.form_detail.currentIcon = '';
        this.form_detail.filterText = '';
      },
      // 已选取图标赋值
      icon_set (val) {
        this.form_detail.currentIcon = val;
      },
      modal_preview(){
        this.modal_show = true;
      },
      // 图标清空
      icon_clear (){
        this.form_detail.currentIcon = '';
      }
  },
  computed: {
				filterIcons: function() {
					var a = this;
					return this.icon_all.filter(function(e) {
						return e.tag.indexOf(a.form_detail.filterText) > -1
					})
				}
 }
};
</script>

<style scoped>
</style>
