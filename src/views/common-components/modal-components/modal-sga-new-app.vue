<template>
    <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="700">
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">新增商户应用标识</span>
        </p>
        <div>
            <Form ref="form_detail" :model="form_detail" :label-width="150">
                <FormItem label="商户编码：" prop="code">
                    <Input type="text" size="small" v-model="form_detail.code"
                           style="width:85%"></Input>
                </FormItem>
                <FormItem label="应用类型：">
                    <common-select ref="select_appType_m" @on-update="updateSelect_appType_model"
                                   type="combo"
                                   keyWord="result"
                                   holder="请选择应用标识（默认全部）"
                                   code="spCode"
                                   title="spName"
                                   size="small"
                                   group="serviceSupplier"
                                   @on-loaded="select_callBack"
                                   :default="this.form_detail.appKeyType"
                                   :uri="this.$store.state.select.serviceSupplier.uri"
                                   style="width:85%"></common-select>
                </FormItem>
                <FormItem v-show="this.form_detail.appKeyType === 'TestZJ'" label="应用标识：" prop="appKeyCode">
                    <Input type="text" size="small" v-model="form_detail.appKeyCode"
                           style="width:85%"></Input>
                </FormItem>
                <FormItem label="应用名称：" prop="appKeyName">
                    <Input type="text" size="small" v-model="form_detail.appKeyName"
                           style="width:85%"></Input>
                </FormItem>
                <FormItem label="应用描述：" prop="description">
                    <Input type="textarea" size="small" v-model="form_detail.description"
                           style="width:85%"></Input>
                </FormItem>
            </Form>
        </div>
        <div slot="footer">
            <Button type="primary" @click="new_app_submit">确定</Button>
            <Button type="ghost" @click="new_app_cancel">取消</Button>
        </div>
        <loading :show="show_loading_preview"></loading>
    </Modal>
</template>

<script>
    import api from '../../../api/api';
    import loading from '../../my-components/loading/loading';
    import commonSelect from '../../common-components/select-components/selectCommon';

    export default {
        name: 'modal-sga-new-app',
        components: {
            loading,
            commonSelect
        },
        data () {
            return {
                // 筛选文字
                filterText : '',
                // 开始日期绑定
                dateStart: '',
                //结束日期绑定
                dateEnd: '',
                // 基本信息数据绑定
                form_detail: {
                    code : '',
                    appKeyCode : '',
                    appKeyType : '',
                    appKeyName : '',
                    description : ''
                },
                // 窗口展示
                modal_show: false,
                show_loading_preview: false,
            };
        },
        methods: {
            // 商户应用标识更新
            updateSelect_appType_model (val) {
                this.form_detail.appKeyType = val
            },
            // 加载完成调用
            select_callBack () {

            },
            // 新建应用取消
            new_app_cancel () {
                this.modal_show = false;
            },
            // 新建应用提交
            new_app_submit () {

            },
            // 设置商户编码
            set_code_modal (val) {
                this.form_detail.code = val;
            },
            // 页面显示
            modal_preview(){
                this.modal_show = true;
            }
        }
    };
</script>

<style scoped>

</style>
