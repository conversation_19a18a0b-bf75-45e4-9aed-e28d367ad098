<style scoped>
    .yop-explain-150 {
        font-size: 12px;
        color: grey;
        padding-left: 150px;
        line-height: 30px;
    }
</style>
<template>
    <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="700" >
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">{{title}}</span>
        </p>
        <div>
            <Form ref="form_detail" :model="form_detail" :rules="rule_detail" :label-width="150">
                <FormItem label="" >
                    <p style="color:red">{{warning_red}}</p>
                    <p>{{warning_black}}</p>
                </FormItem>
                <FormItem v-if="reason_show" label="原因" prop="reason">
                    <Input id="reason_id_001" type="textarea" size="small" v-model="form_detail.reason"
                           style="width:80%"></Input>
                </FormItem>
                <p class="yop-explain-150">{{desc}}</p>
            </Form>
        </div>
        <div slot="footer">
            <Button id="confirm_id_01" type="primary" @click="expires_submit('form_detail')">确定</Button>
            <Button id="confirm_id_02" type="ghost" @click="expires_cancel">取消</Button>
        </div>
        <loading :show="show_loading_preview"></loading>
    </Modal>
</template>

<script>
    import util from '../../../libs/util'
    import api from'../../../api/api'
    import loading from '../../my-components/loading/loading'
    import datePicker from '../../common-components/date-components/date-picker-set'
    export default {
        name: 'modal-confirm',
        props: {
            title: String,
            warning_red: String,
            warning_black: String,
            reason_fail: String,
            desc: String,
            reason_length: Number,
            reason_show: Boolean
        },
        components:{
            loading,
            datePicker
        },
        data () {
            // 描述输入验证
            const validate_reason = (rule, value, callback) => {
                if(util.getLength(value.trim()) > this.reason_length){
                    callback(new Error('长度不能大于'+this.reason_length));
                }else{
                    callback();
                }
            };
            return {
                // 基本信息数据绑定
                form_detail: {
                    reason: ''
                },
                rule_detail:{
                    reason:[
                        {required:true,message:'原因不能为空',trigger: 'blur'},
                        {validator: validate_reason, trigger:'blur'}
                    ]
                },
                // 当前id
                current_id : '',
                // 窗口展示
                modal_show: false,
                show_loading_preview : false
            }
        },
        methods: {
            expires_submit (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        let param = {
                            id: this.current_id,
                            reason: this.form_detail.reason.trim(),
                            title : this.title
                        };
                        this.$emit('confirm_handler', param);
                        this.preview_cancel();
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            expires_cancel () {
                this.preview_cancel();
            },
            // 窗口关闭按钮
            preview_cancel () {
                this.modal_show = false;
                this.$refs.form_detail.resetFields();
                this.form_detail.reason = '';
            },
            // 窗口显示按钮
            preview_show () {
                this.modal_show = true;

            },
            // 当前id赋值
            set_current_id (id){
                this.current_id = id;
            }
        }
    }
</script>

<style scoped>

</style>
