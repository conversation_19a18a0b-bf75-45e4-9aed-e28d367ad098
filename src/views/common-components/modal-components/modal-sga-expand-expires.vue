<template>
    <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="700" >
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black" >展期</span>
        </p>
        <div>
            <Form ref="form_detail" :model="form_detail" :label-width="150">
                <FormItem  >
                    <label slot="label"> <span style="color:red"> * </span>授权有效期</label>
                    <RadioGroup v-model="form_detail.authType">
                        <Radio label="指定授权时间段"></Radio>
                        <Radio label="永久授权"></Radio>
                    </RadioGroup>
                    <date-picker v-show="form_detail.authType === '指定授权时间段'" ref="datepicker" @on-start-change="update_start_date"
                                 @on-end-change="update_end_date"
                                 :default_start="this.dateStart"
                                 :default_end="this.dateEnd"
                    ></date-picker>
                </FormItem>
            </Form>
        </div>
        <div slot="footer">
            <Button type="primary" @click="expires_submit">确定</Button>
            <Button type="ghost" @click="expires_cancel">取消</Button>
        </div>
        <loading :show="show_loading_preview"></loading>
    </Modal>
</template>

<script>
    import util from '../../../libs/util'
    import api from'../../../api/api'
    import loading from '../../my-components/loading/loading'
    import datePicker from '../../common-components/date-components/date-picker-set'
    export default {
        name: 'modal-sga-expand-expires',
        props: {
            size: String,
        },
        components:{
            loading,
            datePicker
        },
        data () {
            return {
                // 当前是展期还是重新授权 展期为true
                current_func : true,
                // 开始日期绑定
                dateStart: "",
                //结束日期绑定
                dateEnd: '',
                // 基本信息数据绑定
                form_detail: {
                    authType: '永久授权'
                },
                // 当前重新授权参数
                current_pararm: {},
                // 当前id
                current_id : '',
                // 窗口展示
                modal_show: false,
                show_loading_preview : false
            }
        },
        methods: {
            // 组件初始化获取
            init () {

            },
            reset(){
                console.log("reset")
                this.$refs.datepicker.reset();
            },
            expires_submit () {
                let param ={}
                if(this.form_detail.authType === '永久授权'){
                    param ={
                        id:this.current_id,
                        startDate : '',
                        endDate : '',
                        cause:"运营后台人工展期"
                    }
                }else{
                    param ={
                        id:this.current_id,
                        effectiveDate : util.dateFormat_component(this.dateStart),
                        overdueDate : util.dateFormat_component_end(this.dateEnd),
                        cause:"运营后台人工展期"
                    }
                }
                if(this.current_func){
                    api.yop_product_authz_defer(param).then(
                        (response) =>{
                            if (response.status === 'success') {
                                this.$ypMsg.notice_success(this,'展期成功');
                                this.$emit('update-list')
                            } else {
                                this.$ypMsg.notice_error(this,'展期错误',response.message,response.solution);
                            }
                            this.modal_show = false
                        }
                    );
                }else{
                    api.yop_product_authz_defer_reauth(param).then(
                        (response) =>{
                            if (response.status === 'success') {
                                this.$ypMsg.notice_success(this,'重新授权成功');
                                this.$emit('update-list')
                            } else {
                                this.$ypMsg.notice_error(this,'重新授权错误',response.message,response.solution);
                            }
                            this.modal_show = false
                        }
                    );
                }
            },
            expires_cancel () {
              this.preview_cancel();
            },
            // 窗口关闭按钮
            preview_cancel () {
                this.modal_show = false;
            },
            // 窗口显示按钮
            preview_show () {
                // this.form_detail.authType = '永久授权'
                this.modal_show = true;
                // this.date_picker_init(start,end);
            },
            // 日期初始化
            date_picker_init (start,end){
                // this.$refs.datepicker.date_now_set();
                if(start || end){
                    console.log(start,end);
                    this.form_detail.authType = '指定授权时间段';
                    this.$refs.datepicker.date_start_set(start);
                    this.$refs.datepicker.date_end_set(end);
                    this.dateStart = start;
                    this.dateEnd = end;
                }else{
                    this.form_detail.authType = '永久授权';
                }
            },
            // 开始日期更新
            update_start_date (val) {
                this.dateStart = val;
            },
            // 结束日期更新
            update_end_date (val) {
                this.dateEnd = val;
            },
            // 当前id赋值
            set_current_id (id){
                this.current_id = id;
            },
            // 当前重新赋值参数
            set_current_pararm (param){
                this.current_pararm = param;
            }
        },
        mounted () {
            this.init();
        }
    }
</script>

<style scoped>

</style>
