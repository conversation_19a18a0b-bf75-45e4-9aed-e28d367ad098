<template>
    <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="700" >
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">{{title}}</span>
        </p>
        <div>
            <Form ref="form_detail" :model="form_detail" :rules="rule_detail" :label-width="150">
                <FormItem  >
                    <label slot="label"> <span style="color:red"> * </span>{{subTitle}}</label>
                    <RadioGroup v-model="form_detail.authType">
                        <Radio :label="option1"></Radio>
                        <Radio :label="option2"></Radio>
                    </RadioGroup>
                    <date-picker v-show="form_detail.authType === option2" ref="datepicker" @on-start-change="update_start_date"
                                 @on-end-change="update_end_date"
                                 :default_start="this.dateStart"
                                 :default_end="this.dateEnd"
                    ></date-picker>
                </FormItem>
                <FormItem v-if="reason_show" label="原因" prop="reason">
                    <Input type="textarea" size="small" v-model="form_detail.reason"
                           style="width:80%"></Input>
                </FormItem>
            </Form>
        </div>
        <div slot="footer">
            <Button type="primary" @click="expires_submit('form_detail')">确定</Button>
            <Button type="ghost" @click="expires_cancel">取消</Button>
        </div>
        <loading :show="show_loading_preview"></loading>
    </Modal>
</template>

<script>
    import util from '../../../libs/util'
    import api from'../../../api/api'
    import loading from '../../my-components/loading/loading'
    import datePicker from '../../common-components/date-components/date-picker-set'
    export default {
        name: 'modal-expand-common',
        props: {
            size: String,
            title: String,
            subTitle: String,
            option1: String,
            option2: String,
            reason_fail: String,
            desc: String,
            reason_length: String,
            reason_show: Boolean
        },
        components:{
            loading,
            datePicker
        },
        data () {
            // 描述输入验证
            const validate_reason = (rule, value, callback) => {
                if(util.getLength(value) > this.reason_length){
                    callback(new Error('长度不能大于'+this.reason_length));
                }else{
                    callback();
                }
            };
            return {
                // 开始日期绑定
                dateStart: '',
                //结束日期绑定
                dateEnd: '',
                // 基本信息数据绑定
                form_detail: {
                    authType: this.option1,
                    reason: ''
                },
                rule_detail:{
                    reason:[
                        {required:true,message:this.reason_fail,trigger: 'blur'},
                        {validator: validate_reason, trigger:'blur'}
                    ]
                },
                // 当前重新授权参数
                current_pararm: {},
                // 当前id
                current_id : '',
                // 窗口展示
                modal_show: false,
                show_loading_preview : false
            }
        },
        methods: {
            // 组件初始化获取
            init () {

            },
            expires_submit (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        let param ={}
                        if(this.form_detail.authType === this.option1){
                            param ={
                                id:this.current_id,
                                startDate : '',
                                endDate : '',
                                reason: this.form_detail.reason.trim()
                            }
                        }else{
                            console.log(this.dateStart,this.dateEnd,util.dateFormat_component(this.dateStart),util.dateFormat_component_end(this.dateEnd))
                            param ={
                                id:this.current_id,
                                startDate : util.dateFormat_component(this.dateStart),
                                endDate : util.dateFormat_component_end(this.dateEnd),
                                reason: this.form_detail.reason.trim()
                            }
                        }
                        this.$emit('expand_handler',param);
                        this.preview_cancel();
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            expires_cancel () {
                this.preview_cancel();
            },
            // 窗口关闭按钮
            preview_cancel () {
                this.modal_show = false;
                this.$refs.form_detail.resetFields();
                this.form_detail.reason = '';
                this.$refs.datepicker.reset();
            },
            // 窗口显示按钮
            preview_show () {
                // this.form_detail.authType = '永久授权'
                this.modal_show = true;
                // this.date_picker_init(start,end);
            },
            // 日期初始化
            date_picker_init (start,end){
                // this.$refs.datepicker.date_now_set();
                if(start || end){
                    this.form_detail.authType = this.option2;
                    this.$refs.datepicker.date_start_set(start);
                    this.dateStart = start;
                    this.$refs.datepicker.date_end_set(end);
                    this.dateEnd = end;
                }else{
                    this.form_detail.authType = this.option1;
                }
            },
            // 开始日期更新
            update_start_date (val) {
                this.dateStart = val;
            },
            // 结束日期更新
            update_end_date (val) {
                this.dateEnd = val;
            },
            // 当前id赋值
            set_current_id (id){
                this.current_id = id;
            },
            // 当前重新赋值参数
            set_current_pararm (param){
                this.current_pararm = param;
            }
        },
        mounted () {
            this.init();
        }
    }
</script>

<style scoped>

</style>
