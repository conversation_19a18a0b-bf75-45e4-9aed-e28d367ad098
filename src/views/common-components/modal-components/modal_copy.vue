<template>
    <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="75%" >
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">{{title}}</span>
        </p>
        <Card dis-hover :bordered="false">
            <Row class="margin-top-20">
                <Col span="24">
                <Table id='table_1' border ref="selection" :columns="columns_list" :data="data_list" ></Table>
                </Col>
                <loading :show="show_loading"></loading>
            </Row>
        </Card>
        <div slot="footer">
            <Button type="primary" @click="modal_hide">关闭</Button>
        </div>
    </Modal>
</template>

<script>
    import loading from '../../my-components/loading/loading'
    import util from '../../../libs/util'
    import api from '../../../api/api'
    export default {
        name: 'modal_copy',
        components:{
            loading
        },
        props:{
            title : String,
        },
        data () {
            return {
                // 列1
                col1 : '',
                // 列2
                col2 : '',
                // 弹窗显示
                modal_show: false,
                // loading
                show_loading: false,
                // 表样式
                columns_list : [
                ],
                // 表数据
                data_list : []
            };
        },
        methods: {
            // loading展示
            loading_preview () {
                this.show_loading = true;
            },
            // loading隐藏
            loading_hide () {
                this.show_loading = false;
            },
            // 设置表格头
            col_set (col1,col2){
                this.col1 = col1;
                this.col2 = col2;
                this.columns_list = [
                    {
                        title: this.col1,
                        key: 'type',
                        width: 130,
                        align: 'center'
                    },
                    {
                        title: this.col2,
                        key: 'value',
                        'min-width': 150,
                        align: 'center'
                    },
                    {
                        title: '操作',
                        key: 'type',
                        width: 130,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },

                                    on: {
                                        click: () => {
                                            this.copy_value(params.row.value);
                                        }
                                    }
                                }, '复制')
                            ]);

                        }
                    }
                ]
            },
            // 表格单一内容设置
            data_set  (t,v) {
                this.data_list = [];
                this.data_list.push(
                    {
                        type : t,
                        value : v
                    }
                )
                this.loading_hide();
            },
            // 表格数组内容设置
            data_set_arr (arr) {
                this.data_list = [];
                for(var i in arr){
                    this.data_list.push({
                        type : arr[i].type,
                        value : arr[i].value
                    })
                }
                this.loading_hide();
            },
            // 窗口显示
            modal_preview () {
                this.modal_show = true;
            },
            // 窗口隐藏
            modal_hide () {
                this.modal_show =false;
                this.data_list = [];
            },
            // 复制公钥值
            copy_value (value) {
                this.$copyText(value);
                this.$ypMsg.notice_success(this,'复制完成');
            }
        }
    };
</script>

<style scoped>

</style>
