<template>
    <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="30%" >
        <div  show-icon><Icon :type="exportIcon"/>{{exportMsg}}</div>
        <Icon type="ios-checkmark" />
        <div slot="footer">
            <Button type="primary" @click="modal_hide">关闭</Button>
        </div>
    </Modal>
</template>

<script>
    import loading from '../../my-components/loading/loading'
    import util from '../../../libs/util'
    import api from '../../../api/api'
    export default {
        name: 'modal_showMsg',
        components:{
            loading
        },
        props:{
            title : String,
            exportMsg :String,
            exportIcon:String
        },
        data () {
            return {
                // 列1
                col1 : '',
                // 列2
                col2 : '',
                // 弹窗显示
                modal_show: false,
                // loading
                show_loading: false,
                // 表样式
                columns_list : [
                ],
                // 表数据
                data_list : []
            };
        },
        methods: {
            // loading展示
            loading_preview () {
                this.show_loading = true;
            },
            // loading隐藏
            loading_hide () {
                this.show_loading = false;
            },
            // 设置表格头
          
            // 窗口显示
            modal_preview () {
                this.modal_show = true;
            },
            // 窗口隐藏
            modal_hide () {
                this.modal_show =false;
                this.data_list = [];
            },
            // 复制公钥值
            copy_value (value) {
                this.$copyText(value);
                this.$ypMsg.notice_success(this,'复制完成');
            }
        }
    };
</script>

<style scoped>

</style>
