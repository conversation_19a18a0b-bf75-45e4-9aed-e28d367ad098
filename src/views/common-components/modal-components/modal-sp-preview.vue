<template>
    <Modal id="modal_request_1" v-model="model_show" :closable="false" width="70%" >
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">查看服务分组</span>
        </p>
        <Card  dis-hover>
        <p slot="title">
            基本信息
        </p>
            <Form ref="form_detail" :model="form_detail" :label-width="150">
                <FormItem label="上级服务分组名称：" v-show="!top_resource">
                    {{form_detail.PServiceGroup}}
                </FormItem>
                <FormItem label="服务分组编码：">
                    {{form_detail.serviceGroupCode}}
                </FormItem>
                <FormItem label="服务分组名称：">
                    {{form_detail.serviceGroupName}}
                </FormItem>
                <FormItem label="服务提供方：" v-show="top_resource">
                    {{form_detail.spName}}{{form_detail.spCode}}
                </FormItem>
                <FormItem label="授权方式：" v-show="!top_resource">
                    {{form_detail.authType}}
                </FormItem>
                <FormItem label="描述：">
                    {{form_detail.description}}
                </FormItem>
            </Form>
        </Card>
        <Card  dis-hover v-show="model_manage_show">
            <p slot="title">
                服务分组API管理
            </p>
            <Tabs :value="tabNow">
                <TabPane id="tab_basic_1" label="API接口" name="api_interface">
                    <Row class="margin-top-10">
                        <Col span="24">
                        <Table id='table_1' border ref="api_interface" :columns="columns_ApiInterfaceList" :data="data_ApiInterfaceList" ></Table>
                        <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                            <Page class="margin-top-10" style="float: right" :total="pageTotalInterface" :page-size="10" :current="pageNoInteface" show-elevator @on-change="pageRefreshInteface"></Page>
                        </Tooltip>
                        </Col>
                    </Row>
                    <loading :show="show_loading_interface"></loading>
                </TabPane>
                <TabPane id="tab_basic_2" label="API分组" name="api_group">
                    <Row class="margin-top-10">
                        <Col span="24">
                        <Table id='table_2' border ref="api_group" :columns="columns_ApiGroupList" :data="data_ApiGroupList" ></Table>
                        <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                            <Page class="margin-top-10" style="float: right" :total="pageTotalGroup" :page-size="10" :current="pageNoGroup" show-elevator @on-change="pageRefreshGroup"></Page>
                        </Tooltip>
                        </Col>
                    </Row>
                    <loading :show="show_loading_group"></loading>
                </TabPane>
            </Tabs>
        </Card>
        <div slot="footer">
            <Button type="ghost" @click="preview_cancel">关闭</Button>
        </div>
        <loading :show="show_loading_preview"></loading>
    </Modal>
</template>

<script>
    import api from'../../../api/api'
    import loading from '../../my-components/loading/loading'
    import util from '../../../libs/util'
    export default {
        name: 'modal-sp-preview',
        props: {
            size: String,
        },
        components:{
            loading
        },
        data () {
            return {
                model_manage_show : true,
                // loading interface
                show_loading_interface : false,
                // loading apiGroup
                show_loading_group : false,
                // 是否为顶级资源 true为顶级 false为非顶级
                top_resource : false,
                // 弹窗显示隐藏
                model_show : false,
                // loading
                show_loading_preview : false,
                // api分组列表
                data_serviceSupplier_List: [],
                // 选择数据绑定
                select_data_serviceSupplier: '',
                // 基本信息数据绑定
                form_detail: {
                    PServiceGroup: '',
                    PServiceGroupCode: '',
                    serviceGroupCode: '',
                    serviceGroupName: '',
                    authType: '',
                    authTypeCode : '',
                    spName : '',
                    spCode : '',
                    description: ''
                },
                tabNow:'api_interface',
                // api接口列表
                columns_ApiInterfaceList : [
                    {
                        title: 'API名称',
                        key: 'name',
                        width: 150,
                        align: 'center'
                    },
                    {
                        title: 'API URI',
                        'min-width': 200,
                        key: 'uri',
                        align: 'center'
                    },
                    {
                        title: '版本',
                        width: 180,
                        key: 'version',
                        align: 'center'
                    },
                    {
                        title: '状态',
                        render: (h,params) => {
                            if(params.row.status){
                                let color = 'red';
                                let status = '已删除';
                                if (params.row.status === 'ACTIVE') {
                                    color = 'green';
                                    status = '活动中';
                                } else if (params.row.status === 'FROZEN') {
                                    color = 'grey';
                                    status = '已冻结';
                                } else {
                                    color = 'red';
                                    status = '已删除';
                                }
                                return h('div', [
                                    h('Tag', {
                                        style: {
                                            align: 'center'
                                        },
                                        props: {
                                            color: color
                                        }
                                    }, status)
                                ]);
                            }else{
                                return h('div','--');
                            }
                        },
                        width: 120,
                        align: 'center'
                    },
                    {
                        title: '创建时间',
                        width: 150,
                        align: 'center',
                        key : 'createTime'
                    },
                ],
                // api接口列表数据
                data_ApiInterfaceList : [
                    // {
                    //     id : '1',
                    //     name : ' E账通支付',
                    //     uri : '/rest/v1.0/balance/yop-group-remit/transfer-query',
                    //     version : '1.0',
                    //     status : 'ACTIVE',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-01 10:30:00'
                    // },
                    //
                    // {
                    //     id : '2',
                    //     name : ' 订单查询',
                    //     uri : '/rest/v1.0/balance/cash',
                    //     version : '1.1',
                    //     status : 'FROZEN',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id : '3',
                    //     name : ' 退款\n' +
                    //     '\n',
                    //     uri : '/rest/v1.0/balance/yop-group-remit/transfer-query',
                    //     version : '1.0',
                    //     status : 'DELETE',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id : '4',
                    //     name : ' E账通支付',
                    //     uri : '/rest/v1.0/balance/yop-group-remit/transfer-query',
                    //     version : '1.0',
                    //     status : 'ACTIVE',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-01 10:30:00'
                    // }

                ],
                // api接口总页数
                pageTotalInterface : 10,
                // api接口当前页数
                pageNoInteface : 1,
                // api接口列表
                columns_ApiGroupList : [
                    {
                        title: 'API分组',
                        width: 140,
                        render:(h,params) =>{
                            return h('div',[
                                h('p',params.row.apiGroupName),
                                h('p','('+params.row.apiGroupCode+')')
                            ])
                        },
                        align: 'center'
                    },

                    {
                        title: 'API分组安全需求',
                        width: 180,
                        key: 'apiGroup_security',
                        align: 'center',
                        type :'html'
                    },
                    {
                        title: '描述',
                        'min-width': 200,
                        key: 'description',
                        align: 'center'
                    },
                    {
                        title: '创建时间',
                        width: 150,
                        align: 'center',
                        key : 'createTime'
                    },
                ],
                // api接口列表数据
                data_ApiGroupList : [
                    // {
                    //     apiGroupCode: 'test_doc_wy',
                    //     apiGroupName: '双因子',
                    //     apiGroup_security: 'YOP-HMAC-AES128<br/>YOP-HMAC-AES256<br/>YOP-HMAC-AES512',
                    //     description:'XX子系统',
                    //     createTime : '2018-02-01 10:30:00',
                    //
                    // },
                    // {
                    //     apiGroupCode: 'duanxin',
                    //     apiGroupName: '短信',
                    //     apiGroup_security: 'YOP-HMAC-AES128<br/>YOP-HMAC-AES256<br/>YOP-HMAC-AES512',
                    //     description:'sertrstyrtydrtdrtfcggchcbvgh',
                    //     createTime : '2018-02-01 10:30:00',
                    //
                    // }
                ],
                // api接口总页数
                pageTotalGroup : 10,
                // api接口当前页数
                pageNoGroup : 1,
                // 当前服务分组编码
                current_code: ''


            }
        },
        methods: {
            // 组件初始化获取
            init () {

            },
            // 更新数据
            updateData () {
                this.$emit('on-update', this.select_data_serviceSupplier);
            },
            manage_show () {
                this.model_manage_show = true;
            },
            manage_hide () {
                this.model_manage_show = false;
            },
            // 窗口关闭按钮
            preview_cancel () {
                this.model_show = false;
            },
            // 窗口显示按钮
            preview_show (top) {
                this.top_resource= top;
                this.model_show = true;
                this.show_loading_preview = true;
                this.tabNow ='api_interface';
            },
            // 获取详细信息
            detail_info_get (code,list) {
                // 详细信息接口
                this.current_code = code;
                api.yop_service_group_detail({code:code}).then(
                    response =>{
                        let status = response.data.status;
                        if (status === 'success') {
                            let result = response.data.data.result;
                            this.detail_info_handler(result,list);
                        } else {
                            this.$ypMsg.notice_error(this,'服务分组详细信息获取失败,请刷新重试',response.data.message,response.data.solution);
                            this.add_resource = false;
                        }
                        this.show_loading_preview = false;
                    }
                )
                this.pageRefreshInteface();
                this.pageRefreshGroup();
            },
            // 详细信息处理
            detail_info_handler (result,type_list) {
                this.data_add_top_init();
                this.form_detail.PServiceGroup = result.pName;
                this.form_detail.serviceGroupCode = result.code;
                this.form_detail.serviceGroupName = result.name;
                this.form_detail.authType = this.data_name_handler(result.authorizeType, false,type_list);
                this.form_detail.spName = this.data_name_handler(result.spCode, 'serviceSupplier');
                this.form_detail.spCode = result.spCode;
                this.form_detail.description = result.description;
            },
            data_add_top_init(){
                this.form_detail.PServiceGroup = '';
                this.form_detail.serviceGroupCode = '';
                this.form_detail.serviceGroupName = '';
                this.form_detail.authType = '';
                this.form_detail.spName = '';
                this.form_detail.spCode = '';
                this.form_detail.description = '';
            },
            // 返回相应编码的名字
            data_name_handler (code, name,list) {
                if (code) {
                    let group = []
                    if(name){
                         group = this.$store.state.select[name].data;
                        for (var i in group) {
                            if (group[i].value === code) {
                                return group[i].name;
                            }
                        }
                    }else{
                        group = list
                        for (var i in group) {
                            if (group[i].name === code) {
                                return group[i].label;
                            }
                        }
                    }

                } else {
                    return '';
                }
            },
            // tab初始化
            tab_init (){
              this.tabNow = 'api_interface'
            },
            pageRefreshInteface (val) {
                this.show_loading_interface = true;
                let params = {
                    code : this.current_code,
                    _pageNo: 1,
                    _pageSize: 10
                };
                if (val) {
                    params._pageNo = val;
                }
                util.paramFormat(params);
                api.yop_service_group_api_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat_interface(response.data.data.page.items);
                            this.pageNoInterface = response.data.data.page.pageNo;
                            this.pageTotalInterface = response.data.data.page.totalPageNum * 10;
                            this.show_loading_interface = false;
                        } else {
                            this.$Modal.error({
                                title: 'API接口加载错误',
                                content: response.data.message
                            });
                            this.show_loading_interface = false;
                        }
                    }
                );
            },
            // 关联接口数据处理
            tableDataFormat_interface (items){
                this.data_ApiInterfaceList = [];
                for (var i in items) {
                    this.data_ApiInterfaceList.push({
                        name : util.empty_handler(items[i].apiName),
                        uri : util.empty_handler(items[i].apiUri),
                        version : util.empty_handler(items[i].version),
                        status : items[i].status,
                        createTime : util.empty_handler(items[i].createdDate)
                    });
                }
            },
            pageRefreshGroup (val) {
                this.show_loading_group = true;
                let params = {
                    code : this.current_code,
                    _pageNo: 1,
                    _pageSize: 10
                };
                if (val) {
                    params._pageNo = val;
                }
                util.paramFormat(params);
                api.yop_service_group_api_group_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat_group(response.data.data.page.items);
                            this.pageNoGroup = response.data.data.page.pageNo;
                            this.pageTotalGroup = response.data.data.page.totalPageNum * 10;
                            this.show_loading_group = false;
                        } else {
                            this.$Modal.error({
                                title: 'API分组加载错误',
                                content: response.data.message
                            });
                            this.show_loading_group = false;
                        }
                    }
                );
            },
            // 关联api分组数据处理
            tableDataFormat_group (items){
                this.data_ApiGroupList = [];
                for (var i in items) {
                    this.data_ApiGroupList.push({
                        apiGroupCode : util.empty_handler(items[i].code),
                        apiGroupName : util.empty_handler(items[i].name),
                        apiGroup_security : util.empty_handler(items[i].security),//可能是个数组
                        description : items[i].description,
                        createTime : util.empty_handler(items[i].createdDate)
                    });
                }
            },
        },
        mounted () {
            this.init();
        }
    }
</script>

<style scoped>

</style>
