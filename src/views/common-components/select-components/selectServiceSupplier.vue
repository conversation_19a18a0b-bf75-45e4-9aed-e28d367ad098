<style lang="less">
    @import '../../../styles/common.less';
</style>
<template>
    <Select  :size="size" class="margin-top-5" v-model="select_data_serviceSupplier" filterable placeholder="请选择（默认全部）" clearable @on-change="updateData()">
        <Option v-for="item in data_serviceSupplier_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
    </Select>
</template>

<script>
    import api from'../../../api/api'
    export default {
        name: 'select-service-supplier',
        props:{
            size: String
        },
        data () {
            return{
                // api分组列表
                data_serviceSupplier_List: [],
                // 选择数据绑定
                select_data_serviceSupplier: ''
            }
        },
        methods:{
            // 组件初始化获取
            init () {
                api.yop_dashboard_interfaceMenuList().then(
                    (response) => {
                        let resultTemp = response.data.data['api-group-query-list']
                        this.data_serviceSupplier_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].groupCode,// 需要修改
                                label: resultTemp[i].groupCode+'('+resultTemp[i].groupTitle+')' //需要修改
                            })
                        }
                        this.data_serviceSupplier_List = dataTemp;
                    }
                )
            },
            // 更新数据
            updateData (){
                this.$emit('on-update',this.select_data_serviceSupplier);
            }
        },
        mounted (){
            this.init();
        }
    };
</script>

<style scoped>

</style>
