<style lang="less">
    @import '../../../styles/common.less';
</style>
<template>
    <Select ref="commonSelect" :size="size" class="margin-top-5" v-model="select_data" filterable :placeholder="this.holder" clearable @on-change="updateData()">
        <Option v-for="item in data_List" :value="item.value" :key="item.label">{{ item.label }}</Option>
    </Select>
</template>

<script>
    import api from'../../../api/api'
    export default {
        name: 'select-common',
        props: {
            size : String, //窄框、普通框设置
            type: String, //下拉框类型3种（暂时）：1.combo编码组合型 2.code只显示编码，显示传输一致 3.编码 显示分开非组合 4.sub normal 带子数据
            keyWord: String, //解析字段
            code: String, //code字段
            title: String, //name字段
            uri: String, // 获取uri
            holder: String, //placeholder,
            default: String, //默认选中字段
            group: String,
            subCode : String // 子项字段
        },
        data () {
            return{
                // api分组列表
                data_List: [],
                // 选择数据绑定
                select_data: ''
            }
        },
        methods:{
            // 组件初始化获取
            init () {
                this.select_data =this.default;
                let data = this.$store.state.select[this.group].data
                if(data && data.length > 0){
                    this.data_List = data;
                }else{
                    this.updateList();
                }
            },
            // 默认数据设置
            default_select_set(code){
                this.select_data = code;
                this.$emit('on-update',this.select_data);
                const data = this.data_List.find(item => item.value === this.select_data)
                console.log(data)
                this.$emit('on-update-', data.name);
            },
            // 更新数据
            updateData (){
                this.$emit('on-update',this.select_data);
                const data = this.data_List.find(item => item.value === this.select_data)
                this.$emit('on-update-name', data.name);
            },
            // 清空选择
            resetSelected () {
                // this.select_data = '';
                this.$refs.commonSelect.clearSingleSelect();
            },
            // 刷新列表
            updateList () {
                api.yop_common_getlist(this.uri).then(
                    (response) => {
                        let resultTemp = response.data.data[this.keyWord]
                        this.data_List = [];
                        let dataTemp = [];
                        if(resultTemp && resultTemp.length >0) {
                            if(this.type === 'combo') {
                                resultTemp.forEach(
                                    (item)=> {
                                        dataTemp.push(
                                            {
                                                value: item[this.code],
                                                label: item[this.code] + '(' + item[this.title] + ')',
                                                name : item[this.title]
                                            }
                                        )
                                    })
                            }else if(this.type === 'code'){
                                resultTemp.forEach(
                                    (item)=> {
                                        dataTemp.push(
                                            {
                                                value: item[this.code],
                                                label: item[this.code],
                                                name :  item[this.code]
                                            }
                                        )
                                    })
                            }else if (this.type === 'normal'){
                                resultTemp.forEach(
                                    (item)=> {
                                        dataTemp.push(
                                            {
                                                value: item[this.code],
                                                label: item[this.title],
                                                name :  item[this.title]
                                            }
                                        )
                                    })
                            }else if(this.type === 'sub'){
                                resultTemp.forEach(
                                    (item)=> {
                                        dataTemp.push(
                                            {
                                                value: item[this.code],
                                                label: item[this.title],
                                                name :  item[this.title],
                                                subItems: item[this.subCode]
                                            }
                                        )
                                    })
                            }
                        }
                        this.$store.state.select[this.group].data = dataTemp;
                        this.data_List = dataTemp;
                        this.$emit('on-loaded');
                    }
                )
            }

        },
        watch:{
            default : {
                handler(nval,oval) {
                    this.select_data =nval;
                },
                immediate: true,
                deep : true
            }
        },
        mounted (){
            this.init();
        }
    };
</script>


<style scoped>

</style>
