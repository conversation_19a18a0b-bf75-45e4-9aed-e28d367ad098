<template>
    <div >
    <codemirror ref="myCm"
                :value="code"
                :options="cmOptions"
                @input="codeChange"
    >
    </codemirror>
    </div>
</template>

<script>
    import {codemirror} from 'vue-codemirror'
    import 'vue-codemirror/node_modules/codemirror/lib/codemirror.css'
    import 'codemirror/theme/monokai.css'
    import 'vue-codemirror/node_modules/codemirror/mode/clike/clike'
    import 'vue-codemirror/node_modules/codemirror/mode/javascript/javascript'
    import 'vue-codemirror/node_modules/codemirror/mode/php/php'
    import 'vue-codemirror/node_modules/codemirror/mode/python/python'
    export default {
        name: 'code-editor',
        components : {
            codemirror
        },
        props: {
            cmOptions : Object,
            code : String,
            index: Number
        },
        data () {
            return {

            }
        },
        methods:{
            contentGet(){
                return this.code
            },
            codeChange(val){
                let data= {
                    index : this.index,
                    code : val
                }
                this.$emit('codeModify',data);
            }
        }
    };
</script>

<style scoped>

</style>
