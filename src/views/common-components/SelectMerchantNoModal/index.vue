<template>
	<Modal id="modal_request_4" v-model="model_show" :closable="false" width="1000">
		<p slot="header" style="color: #2d8cf0">
			<span style="color: black">添加订阅</span>
		</p>
    <Form ref="form" :model="formData" :label-width="150" v-if="model_show">
        <FormItem label="商编编码" prop="customerNo" :rules="[{ required: true, message: '商编编码必填' }]">
            <Input 
              id="reason_id_001" size="small" 
              v-model="formData.customerNo"
              style="width:80%"
              @on-blur="getAppList"
            ></Input>
        </FormItem>
        <FormItem label="应用标识" prop="appId" :rules="[{ required: true, message: '应用标识必填' }]">
          <Select 
            size="small"  v-model="formData.appId" filterable clearable placeholder="选择应用标识" 
            style="width:80%"
          >
              <Option v-for="item in appIdList" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </FormItem>
        <FormItem label="通知地址" prop="url" :rules="[{ required: true, validator: checkUrl }]">
            <Input size="small" v-model="formData.url"
                    style="width:80%"></Input>
        </FormItem>
    </Form>
		<div slot="footer">
			<Button type="ghost" id="popupCancel" @click="close_modal">取消</Button>
			<Button type="primary" id="popupCancel" @click="confirm" :loading="loading">确定</Button>
		</div>
	</Modal>
</template>
<script>
import Api from '~/api/spi';
export default {
  data () {
    return {
      model_show: false,
      loading: false,
      spiName: '',
      formData: {
        customerNo: '',
        appId: '',
        url: ''
      },
      appIdList: []
    };
  },
  methods: {
    close_modal () {
      this.model_show = false;
    },
    show_model (spiName) {
      this.spiName = spiName;
      this.formData = {
        customerNo: '',
        appId: '',
        url: ''
      }
      this.appIdList = []
      this.model_show = true
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          Api.subscribe({
            spiName: this.spiName,
            destinations: [this.formData]
          })
            .then((res) => {
              const { status, message, solutions } = res;
              if (status === 'success') {
                this.$emit('refresh')
                this.close_modal()
              } else {
                this.$ypMsg.notice_error(this, '错误', message, solutions);
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      })
    },
    getAppList() {
      this.formData.appId = ''
      this.appIdList = []
      return Api.getAppList({
        spiName: this.spiName,
        customerNo: this.formData.customerNo
      }).then((res) => {
        const { status, message, solutions, data } = res.data;
        if (status === 'success') {
          this.appIdList = data.result;
        } else {
          this.$ypMsg.notice_error(this, '列表获取错误', message, solutions);
        }
      });
    },
    checkUrl(rule, value, callback) {
      if (!value) {
        return callback(new Error('通知地址不能为空'))
      }
      Api.checkUrl({
        url: value,
        appId: this.formData.appId
      }).then((res) => {
        const { status, message } = res.data;
        if (status === 'success') {
          callback()
        } else {
          callback(new Error(message))
        }
      });
    },
  }
};
</script>

<style scoped>
</style>

