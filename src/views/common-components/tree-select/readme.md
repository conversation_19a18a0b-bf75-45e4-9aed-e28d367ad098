### VueTreeselect

版本：0.4.0

[文档链接](https://www.vue-treeselect.cn/)


适应需求修改了item，点击可以选择文字内容
```
<div class="vue-treeselect__multi-value-item-container">
  <div class={itemClass}>
    <span class="vue-treeselect__multi-value-label">{ label<PERSON><PERSON><PERSON> }</span>
    <span class="vue-treeselect__icon vue-treeselect__value-remove" onMousedown={this.handleMouseDown}><DeleteIcon /></span>
  </div>
</div>
```

修改了最外层onMouseMown
```
export function onLeftClick(mouseDownHandler) {
  return function onMouseDown(evt, ...args) {
    if (evt.target.className === 'vue-treeselect__multi-value-label') {
      return
    }
    if (evt.type === 'mousedown' && evt.button === 0) {
      mouseDownHandler.call(this, evt, ...args)
    }
  }
}
```
修改item的hover样式
```
  // .vue-treeselect__multi-value-item:hover & {
  //   color: @treeselect-multi-value-remove-color-hover;
  // }
  &:hover {
    color: @treeselect-multi-value-remove-color-hover;
  }
```




