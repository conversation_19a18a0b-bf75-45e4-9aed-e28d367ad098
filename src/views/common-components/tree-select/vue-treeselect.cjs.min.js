/*!
 * vue-treeselect v0.4.0 | (c) 2017-2022 <PERSON><PERSON><PERSON>
 * Released under the MIT License.
 * https://vue-treeselect.js.org/
 */
module.exports=function(e){var t={};function n(i){if(t[i])return t[i].exports;var r=t[i]={i:i,l:!1,exports:{}};return e[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(i,r,function(t){return e[t]}.bind(null,r));return i},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/",n(n.s=16)}([function(e,t){e.exports=require("@babel/runtime/helpers/defineProperty")},function(e,t){e.exports=require("babel-helper-vue-jsx-merge-props")},function(e,t){e.exports=require("@babel/runtime/helpers/toConsumableArray")},function(e,t){e.exports=require("lodash/noop")},function(e,t){e.exports=require("lodash/debounce")},function(e,t){e.exports=require("is-promise")},function(e,t){e.exports=require("lodash/once")},function(e,t){e.exports=require("lodash/identity")},function(e,t){e.exports=require("lodash/constant")},function(e,t){e.exports=require("lodash/last")},function(e,t){e.exports=require("@babel/runtime/helpers/slicedToArray")},function(e,t){e.exports=require("fuzzysearch")},function(e,t){e.exports=require("watch-size")},function(e,t){e.exports=require("@babel/runtime/helpers/typeof")},function(e,t){e.exports=require("vue")},function(e,t,n){},function(e,t,n){"use strict";n.r(t),n.d(t,"Treeselect",(function(){return nt})),n.d(t,"treeselectMixin",(function(){return de})),n.d(t,"LOAD_ROOT_OPTIONS",(function(){return Q})),n.d(t,"LOAD_CHILDREN_OPTIONS",(function(){return Y})),n.d(t,"ASYNC_SEARCH",(function(){return K})),n.d(t,"VERSION",(function(){return it}));var i=n(10),r=n.n(i),s=n(2),o=n.n(s),a=n(0),l=n.n(a),c=n(11),u=n.n(c),d=n(3),h=n.n(d).a;function p(e){return function(t){if("vue-treeselect__multi-value-label"!==t.target.className&&"mousedown"===t.type&&0===t.button){for(var n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];e.call.apply(e,[this,t].concat(i))}}}function f(e,t){var n=e.getBoundingClientRect(),i=t.getBoundingClientRect(),r=t.offsetHeight/3;i.bottom+r>n.bottom?e.scrollTop=Math.min(t.offsetTop+t.clientHeight-e.offsetHeight+r,e.scrollHeight):i.top-r<n.top&&(e.scrollTop=Math.max(t.offsetTop-r,0))}var v,m=n(4),g=n.n(m),S=n(12),O=n.n(S);function y(e,t){var n=e.indexOf(t);-1!==n&&e.splice(n,1)}var b=[];function _(e){var t=e.$el,n=e.listener,i=e.lastWidth,r=e.lastHeight,s=t.offsetWidth,o=t.offsetHeight;i===s&&r===o||(e.lastWidth=s,e.lastHeight=o,n({width:s,height:o}))}function E(e,t){var n={$el:e,listener:t,lastWidth:null,lastHeight:null};return b.push(n),_(n),v=setInterval((function(){b.forEach(_)}),100),function(){y(b,n),b.length||(clearInterval(v),v=null)}}function N(e,t){var n=9===document.documentMode,i=!0,r=(n?E:O.a)(e,(function(){return i||t.apply(void 0,arguments)}));return i=!1,r}function L(e){var t=getComputedStyle(e),n=t.overflow,i=t.overflowX,r=t.overflowY;return/(auto|scroll|overlay)/.test(n+r+i)}function w(e,t){var n=function(e){for(var t=[],n=e.parentNode;n&&"BODY"!==n.nodeName&&n.nodeType===document.ELEMENT_NODE;)L(n)&&t.push(n),n=n.parentNode;return t.push(window),t}(e);return window.addEventListener("resize",t,{passive:!0}),n.forEach((function(e){e.addEventListener("scroll",t,{passive:!0})})),function(){window.removeEventListener("resize",t,{passive:!0}),n.forEach((function(e){e.removeEventListener("scroll",t,{passive:!0})}))}}function C(e){return e!=e}var x=n(5),D=n.n(x),M=n(6),I=n.n(M),A=n(7),T=n.n(A),R=n(8),$=n.n(R),B=function(){return Object.create(null)},z=n(13),V=n.n(z);function k(e){return null!=e&&"object"===V()(e)&&Object.getPrototypeOf(e)===Object.prototype}function F(e,t){if(k(t))for(var n=Object.keys(t),i=0,r=n.length;i<r;i++)s=e,o=n[i],k(a=t[n[i]])?(s[o]||(s[o]={}),F(s[o],a)):s[o]=a;var s,o,a;return e}var P=n(9),H=n.n(P);function j(e,t){return-1!==e.indexOf(t)}function W(e,t,n){for(var i=0,r=e.length;i<r;i++)if(t.call(n,e[i],i,e))return e[i]}function q(e,t){if(e.length!==t.length)return!0;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!0;return!1}var Q="LOAD_ROOT_OPTIONS",Y="LOAD_CHILDREN_OPTIONS",K="ASYNC_SEARCH",X=8,U=13,J=27,G=35,Z=36,ee=37,te=38,ne=39,ie=40,re=46;function se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?se(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ae(e,t){for(var n=0;;){if(e.level<n)return-1;if(t.level<n)return 1;if(e.index[n]!==t.index[n])return e.index[n]-t.index[n];n++}}function le(e,t,n){return e?u()(t,n):j(n,t)}function ce(e){return e.message||String(e)}var ue=0,de={provide:function(){return{instance:this}},props:{allowClearingDisabled:{type:Boolean,default:!1},allowSelectingDisabledDescendants:{type:Boolean,default:!1},alwaysOpen:{type:Boolean,default:!1},appendToBody:{type:Boolean,default:!1},async:{type:Boolean,default:!1},autoFocus:{type:Boolean,default:!1},autoLoadRootOptions:{type:Boolean,default:!0},autoDeselectAncestors:{type:Boolean,default:!1},autoDeselectDescendants:{type:Boolean,default:!1},autoSelectAncestors:{type:Boolean,default:!1},autoSelectDescendants:{type:Boolean,default:!1},backspaceRemoves:{type:Boolean,default:!0},beforeClearAll:{type:Function,default:$()(!0)},branchNodesFirst:{type:Boolean,default:!1},cacheOptions:{type:Boolean,default:!0},clearable:{type:Boolean,default:!0},clearAllText:{type:String,default:"Clear all"},clearOnSelect:{type:Boolean,default:!1},clearValueText:{type:String,default:"Clear value"},closeOnSelect:{type:Boolean,default:!0},defaultExpandLevel:{type:Number,default:0},defaultOptions:{default:!1},deleteRemoves:{type:Boolean,default:!0},delimiter:{type:String,default:","},flattenSearchResults:{type:Boolean,default:!1},disableBranchNodes:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},disableFuzzyMatching:{type:Boolean,default:!1},flat:{type:Boolean,default:!1},instanceId:{default:function(){return"".concat(ue++,"$$")},type:[String,Number]},joinValues:{type:Boolean,default:!1},limit:{type:Number,default:1/0},limitText:{type:Function,default:function(e){return"and ".concat(e," more")}},loadingText:{type:String,default:"Loading..."},loadOptions:{type:Function},matchKeys:{type:Array,default:$()(["label"])},maxHeight:{type:Number,default:300},multiple:{type:Boolean,default:!1},name:{type:String},noChildrenText:{type:String,default:"No sub-options."},noOptionsText:{type:String,default:"No options available."},noResultsText:{type:String,default:"No results found..."},normalizer:{type:Function,default:T.a},openDirection:{type:String,default:"auto",validator:function(e){return j(["auto","top","bottom","above","below"],e)}},openOnClick:{type:Boolean,default:!0},openOnFocus:{type:Boolean,default:!1},options:{type:Array},placeholder:{type:String,default:"Select..."},required:{type:Boolean,default:!1},retryText:{type:String,default:"Retry?"},retryTitle:{type:String,default:"Click to retry"},searchable:{type:Boolean,default:!0},searchNested:{type:Boolean,default:!1},searchPromptText:{type:String,default:"Type to search..."},showCount:{type:Boolean,default:!1},showCountOf:{type:String,default:"ALL_CHILDREN",validator:function(e){return j(["ALL_CHILDREN","ALL_DESCENDANTS","LEAF_CHILDREN","LEAF_DESCENDANTS"],e)}},showCountOnSearch:null,sortValueBy:{type:String,default:"ORDER_SELECTED",validator:function(e){return j(["ORDER_SELECTED","LEVEL","INDEX"],e)}},tabIndex:{type:Number,default:0},value:null,valueConsistsOf:{type:String,default:"BRANCH_PRIORITY",validator:function(e){return j(["ALL","BRANCH_PRIORITY","LEAF_PRIORITY","ALL_WITH_INDETERMINATE"],e)}},valueFormat:{type:String,default:"id"},zIndex:{type:[Number,String],default:999}},data:function(){return{trigger:{isFocused:!1,searchQuery:""},menu:{isOpen:!1,current:null,lastScrollPosition:0,placement:"bottom"},forest:{normalizedOptions:[],nodeMap:B(),checkedStateMap:B(),selectedNodeIds:this.extractCheckedNodeIdsFromValue(),selectedNodeMap:B()},rootOptionsStates:{isLoaded:!1,isLoading:!1,loadingError:""},localSearch:{active:!1,noResults:!0,countMap:B()},remoteSearch:B()}},computed:{selectedNodes:function(){return this.forest.selectedNodeIds.map(this.getNode)},internalValue:function(){var e,t=this;if(this.single||this.flat||this.disableBranchNodes||"ALL"===this.valueConsistsOf)e=this.forest.selectedNodeIds.slice();else if("BRANCH_PRIORITY"===this.valueConsistsOf)e=this.forest.selectedNodeIds.filter((function(e){var n=t.getNode(e);return!!n.isRootNode||!t.isSelected(n.parentNode)}));else if("LEAF_PRIORITY"===this.valueConsistsOf)e=this.forest.selectedNodeIds.filter((function(e){var n=t.getNode(e);return!!n.isLeaf||0===n.children.length}));else if("ALL_WITH_INDETERMINATE"===this.valueConsistsOf){var n,i=[];e=this.forest.selectedNodeIds.slice(),this.selectedNodes.forEach((function(t){t.ancestors.forEach((function(t){j(i,t.id)||j(e,t.id)||i.push(t.id)}))})),(n=e).push.apply(n,i)}return"LEVEL"===this.sortValueBy?e.sort((function(e,n){return function(e,t){return e.level===t.level?ae(e,t):e.level-t.level}(t.getNode(e),t.getNode(n))})):"INDEX"===this.sortValueBy&&e.sort((function(e,n){return ae(t.getNode(e),t.getNode(n))})),e},hasValue:function(){return this.internalValue.length>0},single:function(){return!this.multiple},visibleOptionIds:function(){var e=this,t=[];return this.traverseAllNodesByIndex((function(n){if(e.localSearch.active&&!e.shouldOptionBeIncludedInSearchResult(n)||t.push(n.id),n.isBranch&&!e.shouldExpand(n))return!1})),t},hasVisibleOptions:function(){return 0!==this.visibleOptionIds.length},showCountOnSearchComputed:function(){return"boolean"==typeof this.showCountOnSearch?this.showCountOnSearch:this.showCount},hasBranchNodes:function(){return this.forest.normalizedOptions.some((function(e){return e.isBranch}))},shouldFlattenOptions:function(){return this.localSearch.active&&this.flattenSearchResults}},watch:{alwaysOpen:function(e){e?this.openMenu():this.closeMenu()},branchNodesFirst:function(){this.initialize()},disabled:function(e){e&&this.menu.isOpen?this.closeMenu():e||this.menu.isOpen||!this.alwaysOpen||this.openMenu()},flat:function(){this.initialize()},internalValue:function(e,t){q(e,t)&&this.$emit("input",this.getValue(),this.getInstanceId())},matchKeys:function(){this.initialize()},multiple:function(e){e&&this.buildForestState()},options:{handler:function(){this.async||(this.initialize(),this.rootOptionsStates.isLoaded=Array.isArray(this.options))},deep:!0,immediate:!0},"trigger.searchQuery":function(){this.async?this.handleRemoteSearch():this.handleLocalSearch(),this.$emit("search-change",this.trigger.searchQuery,this.getInstanceId())},value:function(){var e=this.extractCheckedNodeIdsFromValue();q(e,this.internalValue)&&this.fixSelectedNodeIds(e)}},methods:{verifyProps:function(){var e=this;if(h((function(){return!e.async||e.searchable}),(function(){return'For async search mode, the value of "searchable" prop must be true.'})),null!=this.options||this.loadOptions||h((function(){return!1}),(function(){return'Are you meant to dynamically load options? You need to use "loadOptions" prop.'})),this.flat&&h((function(){return e.multiple}),(function(){return'You are using flat mode. But you forgot to add "multiple=true"?'})),!this.flat){["autoSelectAncestors","autoSelectDescendants","autoDeselectAncestors","autoDeselectDescendants"].forEach((function(t){h((function(){return!e[t]}),(function(){return'"'.concat(t,'" only applies to flat mode.')}))}))}},resetFlags:function(){this._blurOnSelect=!1},initialize:function(){var e=this.async?this.getRemoteSearchEntry().options:this.options;if(Array.isArray(e)){var t=this.forest.nodeMap;this.forest.nodeMap=B(),this.keepDataOfSelectedNodes(t),this.forest.normalizedOptions=this.normalize(null,e,t),this.fixSelectedNodeIds(this.internalValue)}else this.forest.normalizedOptions=[]},getInstanceId:function(){return null==this.instanceId?this.id:this.instanceId},getValue:function(){var e=this;if("id"===this.valueFormat)return this.multiple?this.internalValue.slice():this.internalValue[0];var t=this.internalValue.map((function(t){return e.getNode(t).raw}));return this.multiple?t:t[0]},getNode:function(e){return h((function(){return null!=e}),(function(){return"Invalid node id: ".concat(e)})),null==e?null:e in this.forest.nodeMap?this.forest.nodeMap[e]:this.createFallbackNode(e)},createFallbackNode:function(e){var t=this.extractNodeFromValue(e),n={id:e,label:this.enhancedNormalizer(t).label||"".concat(e," (unknown)"),ancestors:[],parentNode:null,isFallbackNode:!0,isRootNode:!0,isLeaf:!0,isBranch:!1,isDisabled:!1,isNew:!1,index:[-1],level:0,raw:t};return this.$set(this.forest.nodeMap,e,n)},extractCheckedNodeIdsFromValue:function(){var e=this;return null==this.value?[]:"id"===this.valueFormat?this.multiple?this.value.slice():[this.value]:(this.multiple?this.value:[this.value]).map((function(t){return e.enhancedNormalizer(t)})).map((function(e){return e.id}))},extractNodeFromValue:function(e){var t=this,n={id:e};return"id"===this.valueFormat?n:W(this.multiple?Array.isArray(this.value)?this.value:[]:this.value?[this.value]:[],(function(n){return n&&t.enhancedNormalizer(n).id===e}))||n},fixSelectedNodeIds:function(e){var t=this,n=[];if(this.single||this.flat||this.disableBranchNodes||"ALL"===this.valueConsistsOf)n=e;else if("BRANCH_PRIORITY"===this.valueConsistsOf)e.forEach((function(e){n.push(e);var i=t.getNode(e);i.isBranch&&t.traverseDescendantsBFS(i,(function(e){n.push(e.id)}))}));else if("LEAF_PRIORITY"===this.valueConsistsOf)for(var i=B(),r=e.slice();r.length;){var s=r.shift(),o=this.getNode(s);n.push(s),o.isRootNode||(o.parentNode.id in i||(i[o.parentNode.id]=o.parentNode.children.length),0==--i[o.parentNode.id]&&r.push(o.parentNode.id))}else if("ALL_WITH_INDETERMINATE"===this.valueConsistsOf)for(var a=B(),l=e.filter((function(e){var n=t.getNode(e);return n.isLeaf||0===n.children.length}));l.length;){var c=l.shift(),u=this.getNode(c);n.push(c),u.isRootNode||(u.parentNode.id in a||(a[u.parentNode.id]=u.parentNode.children.length),0==--a[u.parentNode.id]&&l.push(u.parentNode.id))}q(this.forest.selectedNodeIds,n)&&(this.forest.selectedNodeIds=n),this.buildForestState()},keepDataOfSelectedNodes:function(e){var t=this;this.forest.selectedNodeIds.forEach((function(n){if(e[n]){var i=oe(oe({},e[n]),{},{isFallbackNode:!0});t.$set(t.forest.nodeMap,n,i)}}))},isSelected:function(e){return!0===this.forest.selectedNodeMap[e.id]},traverseDescendantsBFS:function(e,t){if(e.isBranch)for(var n=e.children.slice();n.length;){var i=n[0];i.isBranch&&n.push.apply(n,o()(i.children)),t(i),n.shift()}},traverseDescendantsDFS:function(e,t){var n=this;e.isBranch&&e.children.forEach((function(e){n.traverseDescendantsDFS(e,t),t(e)}))},traverseAllNodesDFS:function(e){var t=this;this.forest.normalizedOptions.forEach((function(n){t.traverseDescendantsDFS(n,e),e(n)}))},traverseAllNodesByIndex:function(e){!function t(n){n.children.forEach((function(n){!1!==e(n)&&n.isBranch&&t(n)}))}({children:this.forest.normalizedOptions})},toggleClickOutsideEvent:function(e){e?document.addEventListener("mousedown",this.handleClickOutside,!1):document.removeEventListener("mousedown",this.handleClickOutside,!1)},getValueContainer:function(){return this.$refs.control.$refs["value-container"]},getInput:function(){return this.getValueContainer().$refs.input},focusInput:function(){this.getInput().focus()},blurInput:function(){this.getInput().blur()},handleMouseDown:p((function(e){(e.preventDefault(),e.stopPropagation(),this.disabled)||(this.getValueContainer().$el.contains(e.target)&&!this.menu.isOpen&&(this.openOnClick||this.trigger.isFocused)&&this.openMenu(),this._blurOnSelect?this.blurInput():this.focusInput(),this.resetFlags())})),handleClickOutside:function(e){this.$refs.wrapper&&!this.$refs.wrapper.contains(e.target)&&(this.blurInput(),this.closeMenu())},handleLocalSearch:function(){var e=this,t=this.trigger.searchQuery,n=function(){return e.resetHighlightedOptionWhenNecessary(!0)};if(!t)return this.localSearch.active=!1,n();this.localSearch.active=!0,this.localSearch.noResults=!0,this.traverseAllNodesDFS((function(t){var n;t.isBranch&&(t.isExpandedOnSearch=!1,t.showAllChildrenOnSearch=!1,t.isMatched=!1,t.hasMatchedDescendants=!1,e.$set(e.localSearch.countMap,t.id,(n={},l()(n,"ALL_CHILDREN",0),l()(n,"ALL_DESCENDANTS",0),l()(n,"LEAF_CHILDREN",0),l()(n,"LEAF_DESCENDANTS",0),n)))}));var i=t.trim().toLocaleLowerCase(),r=i.replace(/\s+/g," ").split(" ");this.traverseAllNodesDFS((function(t){e.searchNested&&r.length>1?t.isMatched=r.every((function(e){return le(!1,e,t.nestedSearchLabel)})):t.isMatched=e.matchKeys.some((function(n){return le(!e.disableFuzzyMatching,i,t.lowerCased[n])})),t.isMatched&&(e.localSearch.noResults=!1,t.ancestors.forEach((function(t){return e.localSearch.countMap[t.id].ALL_DESCENDANTS++})),t.isLeaf&&t.ancestors.forEach((function(t){return e.localSearch.countMap[t.id].LEAF_DESCENDANTS++})),null!==t.parentNode&&(e.localSearch.countMap[t.parentNode.id].ALL_CHILDREN+=1,t.isLeaf&&(e.localSearch.countMap[t.parentNode.id].LEAF_CHILDREN+=1))),(t.isMatched||t.isBranch&&t.isExpandedOnSearch)&&null!==t.parentNode&&(t.parentNode.isExpandedOnSearch=!0,t.parentNode.hasMatchedDescendants=!0)})),n()},handleRemoteSearch:function(){var e=this,t=this.trigger.searchQuery,n=this.getRemoteSearchEntry(),i=function(){e.initialize(),e.resetHighlightedOptionWhenNecessary(!0)};if((""===t||this.cacheOptions)&&n.isLoaded)return i();this.callLoadOptionsProp({action:K,args:{searchQuery:t},isPending:function(){return n.isLoading},start:function(){n.isLoading=!0,n.isLoaded=!1,n.loadingError=""},succeed:function(r){n.isLoaded=!0,n.options=r,e.trigger.searchQuery===t&&i()},fail:function(e){n.loadingError=ce(e)},end:function(){n.isLoading=!1}})},getRemoteSearchEntry:function(){var e=this,t=this.trigger.searchQuery,n=this.remoteSearch[t]||oe(oe({},{isLoaded:!1,isLoading:!1,loadingError:""}),{},{options:[]});if(this.$watch((function(){return n.options}),(function(){e.trigger.searchQuery===t&&e.initialize()}),{deep:!0}),""===t){if(Array.isArray(this.defaultOptions))return n.options=this.defaultOptions,n.isLoaded=!0,n;if(!0!==this.defaultOptions)return n.isLoaded=!0,n}return this.remoteSearch[t]||this.$set(this.remoteSearch,t,n),n},shouldExpand:function(e){return this.localSearch.active?e.isExpandedOnSearch:e.isExpanded},shouldOptionBeIncludedInSearchResult:function(e){return!!e.isMatched||(!(!e.isBranch||!e.hasMatchedDescendants||this.flattenSearchResults)||!(e.isRootNode||!e.parentNode.showAllChildrenOnSearch))},shouldShowOptionInMenu:function(e){return!(this.localSearch.active&&!this.shouldOptionBeIncludedInSearchResult(e))},getControl:function(){return this.$refs.control.$el},getMenu:function(){var e=(this.appendToBody?this.$refs.portal.portalTarget:this).$refs.menu.$refs.menu;return e&&"#comment"!==e.nodeName?e:null},setCurrentHighlightedOption:function(e){var t=this,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.menu.current;if(null!=i&&i in this.forest.nodeMap&&(this.forest.nodeMap[i].isHighlighted=!1),this.menu.current=e.id,e.isHighlighted=!0,this.menu.isOpen&&n){var r=function(){var n=t.getMenu(),i=n.querySelector('.vue-treeselect__option[data-id="'.concat(e.id,'"]'));i&&f(n,i)};this.getMenu()?r():this.$nextTick(r)}},resetHighlightedOptionWhenNecessary:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.menu.current;!e&&null!=t&&t in this.forest.nodeMap&&this.shouldShowOptionInMenu(this.getNode(t))||this.highlightFirstOption()},highlightFirstOption:function(){if(this.hasVisibleOptions){var e=this.visibleOptionIds[0];this.setCurrentHighlightedOption(this.getNode(e))}},highlightPrevOption:function(){if(this.hasVisibleOptions){var e=this.visibleOptionIds.indexOf(this.menu.current)-1;if(-1===e)return this.highlightLastOption();this.setCurrentHighlightedOption(this.getNode(this.visibleOptionIds[e]))}},highlightNextOption:function(){if(this.hasVisibleOptions){var e=this.visibleOptionIds.indexOf(this.menu.current)+1;if(e===this.visibleOptionIds.length)return this.highlightFirstOption();this.setCurrentHighlightedOption(this.getNode(this.visibleOptionIds[e]))}},highlightLastOption:function(){if(this.hasVisibleOptions){var e=H()(this.visibleOptionIds);this.setCurrentHighlightedOption(this.getNode(e))}},resetSearchQuery:function(){this.trigger.searchQuery=""},closeMenu:function(){!this.menu.isOpen||!this.disabled&&this.alwaysOpen||(this.saveMenuScrollPosition(),this.menu.isOpen=!1,this.toggleClickOutsideEvent(!1),this.resetSearchQuery(),this.$emit("close",this.getValue(),this.getInstanceId()))},openMenu:function(){this.disabled||this.menu.isOpen||(this.menu.isOpen=!0,this.$nextTick(this.resetHighlightedOptionWhenNecessary),this.$nextTick(this.restoreMenuScrollPosition),this.options||this.async||this.loadRootOptions(),this.toggleClickOutsideEvent(!0),this.$emit("open",this.getInstanceId()))},toggleMenu:function(){this.menu.isOpen?this.closeMenu():this.openMenu()},toggleExpanded:function(e){var t;this.localSearch.active?(t=e.isExpandedOnSearch=!e.isExpandedOnSearch)&&(e.showAllChildrenOnSearch=!0):t=e.isExpanded=!e.isExpanded,t&&!e.childrenStates.isLoaded&&this.loadChildrenOptions(e)},buildForestState:function(){var e=this,t=B();this.forest.selectedNodeIds.forEach((function(e){t[e]=!0})),this.forest.selectedNodeMap=t;var n=B();this.multiple&&(this.traverseAllNodesByIndex((function(e){n[e.id]=0})),this.selectedNodes.forEach((function(t){n[t.id]=2,e.flat||e.disableBranchNodes||t.ancestors.forEach((function(t){e.isSelected(t)||(n[t.id]=1)}))}))),this.forest.checkedStateMap=n},enhancedNormalizer:function(e){return oe(oe({},e),this.normalizer(e,this.getInstanceId()))},normalize:function(e,t,n){var i=this,s=t.map((function(e){return[i.enhancedNormalizer(e),e]})).map((function(t,s){var o=r()(t,2),a=o[0],c=o[1];i.checkDuplication(a),i.verifyNodeShape(a);var u=a.id,d=a.label,p=a.children,f=a.isDefaultExpanded,v=null===e,m=v?0:e.level+1,g=Array.isArray(p)||null===p,S=!g,O=!!a.isDisabled||!i.flat&&!v&&e.isDisabled,y=!!a.isNew,b=i.matchKeys.reduce((function(e,t){return oe(oe({},e),{},l()({},t,(n=a[t],"string"==typeof n?n:"number"!=typeof n||C(n)?"":n+"").toLocaleLowerCase()));var n}),{}),_=v?b.label:e.nestedSearchLabel+" "+b.label,E=i.$set(i.forest.nodeMap,u,B());if(i.$set(E,"id",u),i.$set(E,"label",d),i.$set(E,"level",m),i.$set(E,"ancestors",v?[]:[e].concat(e.ancestors)),i.$set(E,"index",(v?[]:e.index).concat(s)),i.$set(E,"parentNode",e),i.$set(E,"lowerCased",b),i.$set(E,"nestedSearchLabel",_),i.$set(E,"isDisabled",O),i.$set(E,"isNew",y),i.$set(E,"isMatched",!1),i.$set(E,"isHighlighted",!1),i.$set(E,"isBranch",g),i.$set(E,"isLeaf",S),i.$set(E,"isRootNode",v),i.$set(E,"raw",c),g){var N,L=Array.isArray(p);i.$set(E,"childrenStates",oe(oe({},{isLoaded:!1,isLoading:!1,loadingError:""}),{},{isLoaded:L})),i.$set(E,"isExpanded","boolean"==typeof f?f:m<i.defaultExpandLevel),i.$set(E,"hasMatchedDescendants",!1),i.$set(E,"hasDisabledDescendants",!1),i.$set(E,"isExpandedOnSearch",!1),i.$set(E,"showAllChildrenOnSearch",!1),i.$set(E,"count",(N={},l()(N,"ALL_CHILDREN",0),l()(N,"ALL_DESCENDANTS",0),l()(N,"LEAF_CHILDREN",0),l()(N,"LEAF_DESCENDANTS",0),N)),i.$set(E,"children",L?i.normalize(E,p,n):[]),!0===f&&E.ancestors.forEach((function(e){e.isExpanded=!0})),L||"function"==typeof i.loadOptions?!L&&E.isExpanded&&i.loadChildrenOptions(E):h((function(){return!1}),(function(){return'Unloaded branch node detected. "loadOptions" prop is required to load its children.'}))}if(E.ancestors.forEach((function(e){return e.count.ALL_DESCENDANTS++})),S&&E.ancestors.forEach((function(e){return e.count.LEAF_DESCENDANTS++})),v||(e.count.ALL_CHILDREN+=1,S&&(e.count.LEAF_CHILDREN+=1),O&&(e.hasDisabledDescendants=!0)),n&&n[u]){var w=n[u];E.isMatched=w.isMatched,E.showAllChildrenOnSearch=w.showAllChildrenOnSearch,E.isHighlighted=w.isHighlighted,w.isBranch&&E.isBranch&&(E.isExpanded=w.isExpanded,E.isExpandedOnSearch=w.isExpandedOnSearch,w.childrenStates.isLoaded&&!E.childrenStates.isLoaded?E.isExpanded=!1:E.childrenStates=oe({},w.childrenStates))}return E}));if(this.branchNodesFirst){var o=s.filter((function(e){return e.isBranch})),a=s.filter((function(e){return e.isLeaf}));s=o.concat(a)}return s},loadRootOptions:function(){var e=this;this.callLoadOptionsProp({action:Q,isPending:function(){return e.rootOptionsStates.isLoading},start:function(){e.rootOptionsStates.isLoading=!0,e.rootOptionsStates.loadingError=""},succeed:function(){e.rootOptionsStates.isLoaded=!0,e.$nextTick((function(){e.resetHighlightedOptionWhenNecessary(!0)}))},fail:function(t){e.rootOptionsStates.loadingError=ce(t)},end:function(){e.rootOptionsStates.isLoading=!1}})},loadChildrenOptions:function(e){var t=this,n=e.id,i=e.raw;this.callLoadOptionsProp({action:Y,args:{parentNode:i},isPending:function(){return t.getNode(n).childrenStates.isLoading},start:function(){t.getNode(n).childrenStates.isLoading=!0,t.getNode(n).childrenStates.loadingError=""},succeed:function(){t.getNode(n).childrenStates.isLoaded=!0},fail:function(e){t.getNode(n).childrenStates.loadingError=ce(e)},end:function(){t.getNode(n).childrenStates.isLoading=!1}})},callLoadOptionsProp:function(e){var t=e.action,n=e.args,i=e.isPending,r=e.start,s=e.succeed,o=e.fail,a=e.end;if(this.loadOptions&&!i()){r();var l=I()((function(e,t){e?o(e):s(t),a()})),c=this.loadOptions(oe(oe({id:this.getInstanceId(),instanceId:this.getInstanceId(),action:t},n),{},{callback:l}));D()(c)&&c.then((function(){l()}),(function(e){l(e)})).catch((function(e){console.error(e)}))}},checkDuplication:function(e){var t=this;h((function(){return!(e.id in t.forest.nodeMap&&!t.forest.nodeMap[e.id].isFallbackNode)}),(function(){return"Detected duplicate presence of node id ".concat(JSON.stringify(e.id),". ")+'Their labels are "'.concat(t.forest.nodeMap[e.id].label,'" and "').concat(e.label,'" respectively.')}))},verifyNodeShape:function(e){h((function(){return!(void 0===e.children&&!0===e.isBranch)}),(function(){return"Are you meant to declare an unloaded branch node? `isBranch: true` is no longer supported, please use `children: null` instead."}))},select:function(e){if(!this.disabled&&!e.isDisabled){this.single&&this.clear();var t=this.multiple&&!this.flat?0===this.forest.checkedStateMap[e.id]:!this.isSelected(e);t?this._selectNode(e):this._deselectNode(e),this.buildForestState(),t?this.$emit("select",e.raw,this.getInstanceId()):this.$emit("deselect",e.raw,this.getInstanceId()),this.localSearch.active&&t&&(this.single||this.clearOnSelect)&&this.resetSearchQuery(),this.single&&this.closeOnSelect&&(this.closeMenu(),this.searchable&&(this._blurOnSelect=!0))}},clear:function(){var e=this;this.hasValue&&(this.single||this.allowClearingDisabled?this.forest.selectedNodeIds=[]:this.forest.selectedNodeIds=this.forest.selectedNodeIds.filter((function(t){return e.getNode(t).isDisabled})),this.buildForestState())},_selectNode:function(e){var t=this;if(this.single||this.disableBranchNodes)return this.addValue(e);if(this.flat)return this.addValue(e),void(this.autoSelectAncestors?e.ancestors.forEach((function(e){t.isSelected(e)||e.isDisabled||t.addValue(e)})):this.autoSelectDescendants&&this.traverseDescendantsBFS(e,(function(e){t.isSelected(e)||e.isDisabled||t.addValue(e)})));var n=e.isLeaf||!e.hasDisabledDescendants||this.allowSelectingDisabledDescendants;if(n&&this.addValue(e),e.isBranch&&this.traverseDescendantsBFS(e,(function(e){e.isDisabled&&!t.allowSelectingDisabledDescendants||t.addValue(e)})),n)for(var i=e;null!==(i=i.parentNode)&&i.children.every(this.isSelected);)this.addValue(i)},_deselectNode:function(e){var t=this;if(this.disableBranchNodes)return this.removeValue(e);if(this.flat)return this.removeValue(e),void(this.autoDeselectAncestors?e.ancestors.forEach((function(e){t.isSelected(e)&&!e.isDisabled&&t.removeValue(e)})):this.autoDeselectDescendants&&this.traverseDescendantsBFS(e,(function(e){t.isSelected(e)&&!e.isDisabled&&t.removeValue(e)})));var n=!1;if(e.isBranch&&this.traverseDescendantsDFS(e,(function(e){e.isDisabled&&!t.allowSelectingDisabledDescendants||(t.removeValue(e),n=!0)})),e.isLeaf||n||0===e.children.length){this.removeValue(e);for(var i=e;null!==(i=i.parentNode)&&this.isSelected(i);)this.removeValue(i)}},addValue:function(e){this.forest.selectedNodeIds.push(e.id),this.forest.selectedNodeMap[e.id]=!0},removeValue:function(e){y(this.forest.selectedNodeIds,e.id),delete this.forest.selectedNodeMap[e.id]},removeLastValue:function(){if(this.hasValue){if(this.single)return this.clear();var e=H()(this.internalValue),t=this.getNode(e);this.select(t)}},saveMenuScrollPosition:function(){var e=this.getMenu();e&&(this.menu.lastScrollPosition=e.scrollTop)},restoreMenuScrollPosition:function(){var e=this.getMenu();e&&(e.scrollTop=this.menu.lastScrollPosition)}},created:function(){this.verifyProps(),this.resetFlags()},mounted:function(){this.autoFocus&&this.focusInput(),this.options||this.async||!this.autoLoadRootOptions||this.loadRootOptions(),this.alwaysOpen&&this.openMenu(),this.async&&this.defaultOptions&&this.handleRemoteSearch()},destroyed:function(){this.toggleClickOutsideEvent(!1)}};function he(e){return"string"==typeof e?e:null==e||C(e)?"":JSON.stringify(e)}function pe(e,t,n,i,r,s,o,a){var l,c="function"==typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=n,c._compiled=!0),i&&(c.functional=!0),s&&(c._scopeId="data-v-"+s),o?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),r&&r.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(o)},c._ssrRegister=l):r&&(l=a?function(){r.call(this,(c.functional?this.parent:this).$root.$options.shadowRoot)}:r),l)if(c.functional){c._injectStyles=l;var u=c.render;c.render=function(e,t){return l.call(t),u(e,t)}}else{var d=c.beforeCreate;c.beforeCreate=d?[].concat(d,l):[l]}return{exports:e,options:c}}var fe=pe({name:"vue-treeselect--hidden-fields",inject:["instance"],functional:!0,render:function(e,t){var n=arguments[0],i=t.injections.instance;if(!i.name||i.disabled||!i.hasValue)return null;var r=i.internalValue.map(he);return i.multiple&&i.joinValues&&(r=[r.join(i.delimiter)]),r.map((function(e,t){return n("input",{attrs:{type:"hidden",name:i.name},domProps:{value:e},key:"hidden-field-"+t})}))}},void 0,void 0,!1,null,null,null);fe.options.__file="src/components/HiddenFields.vue";var ve=fe.exports,me=n(1),ge=n.n(me),Se=[U,G,Z,ee,te,ne,ie],Oe=pe({name:"vue-treeselect--input",inject:["instance"],data:function(){return{inputWidth:5,value:""}},computed:{needAutoSize:function(){var e=this.instance;return e.searchable&&!e.disabled&&e.multiple},inputStyle:function(){return{width:this.needAutoSize?"".concat(this.inputWidth,"px"):null}}},watch:{"instance.trigger.searchQuery":function(e){this.value=e},value:function(){this.needAutoSize&&this.$nextTick(this.updateInputWidth)}},created:function(){this.debouncedCallback=g()(this.updateSearchQuery,200,{leading:!0,trailing:!0})},methods:{clear:function(){this.onInput({target:{value:""}})},focus:function(){this.instance.disabled||this.$refs.input&&this.$refs.input.focus()},blur:function(){this.$refs.input&&this.$refs.input.blur()},onFocus:function(){var e=this.instance;e.trigger.isFocused=!0,e.openOnFocus&&e.openMenu()},onBlur:function(){var e=this.instance,t=e.getMenu();if(t&&document.activeElement===t)return this.focus();e.trigger.isFocused=!1,e.closeMenu()},onInput:function(e){var t=e.target.value;this.value=t,t?this.debouncedCallback():(this.debouncedCallback.cancel(),this.updateSearchQuery())},onKeyDown:function(e){var t=this.instance,n="which"in e?e.which:e.keyCode;if(!(e.ctrlKey||e.shiftKey||e.altKey||e.metaKey)){if(!t.menu.isOpen&&j(Se,n))return e.preventDefault(),t.openMenu();switch(n){case X:t.backspaceRemoves&&!this.value.length&&t.removeLastValue();break;case U:if(e.preventDefault(),null===t.menu.current)return;var i=t.getNode(t.menu.current);if(i.isBranch&&t.disableBranchNodes)return;t.select(i);break;case J:this.value.length?this.clear():t.menu.isOpen&&t.closeMenu();break;case G:e.preventDefault(),t.highlightLastOption();break;case Z:e.preventDefault(),t.highlightFirstOption();break;case ee:var r=t.getNode(t.menu.current);r.isBranch&&t.shouldExpand(r)?(e.preventDefault(),t.toggleExpanded(r)):!r.isRootNode&&(r.isLeaf||r.isBranch&&!t.shouldExpand(r))&&(e.preventDefault(),t.setCurrentHighlightedOption(r.parentNode));break;case te:e.preventDefault(),t.highlightPrevOption();break;case ne:var s=t.getNode(t.menu.current);s.isBranch&&!t.shouldExpand(s)&&(e.preventDefault(),t.toggleExpanded(s));break;case ie:e.preventDefault(),t.highlightNextOption();break;case re:t.deleteRemoves&&!this.value.length&&t.removeLastValue();break;default:t.openMenu()}}},onMouseDown:function(e){this.value.length&&e.stopPropagation()},renderInputContainer:function(){var e=this.$createElement,t=this.instance,n={},i=[];return t.searchable&&!t.disabled&&(i.push(this.renderInput()),this.needAutoSize&&i.push(this.renderSizer())),t.searchable||F(n,{on:{focus:this.onFocus,blur:this.onBlur,keydown:this.onKeyDown},ref:"input"}),t.searchable||t.disabled||F(n,{attrs:{tabIndex:t.tabIndex}}),e("div",ge()([{class:"vue-treeselect__input-container"},n]),[i])},renderInput:function(){var e=this.$createElement,t=this.instance;return e("input",{ref:"input",class:"vue-treeselect__input",attrs:{type:"text",autocomplete:"off",tabIndex:t.tabIndex,required:t.required&&!t.hasValue},domProps:{value:this.value},style:this.inputStyle,on:{focus:this.onFocus,input:this.onInput,blur:this.onBlur,keydown:this.onKeyDown,mousedown:this.onMouseDown}})},renderSizer:function(){return(0,this.$createElement)("div",{ref:"sizer",class:"vue-treeselect__sizer"},[this.value])},updateInputWidth:function(){this.inputWidth=Math.max(5,this.$refs.sizer.scrollWidth+15)},updateSearchQuery:function(){this.instance.trigger.searchQuery=this.value}},render:function(){return this.renderInputContainer()}},void 0,void 0,!1,null,null,null);Oe.options.__file="src/components/Input.vue";var ye=Oe.exports,be=pe({name:"vue-treeselect--placeholder",inject:["instance"],render:function(){var e=arguments[0],t=this.instance,n={"vue-treeselect__placeholder":!0,"vue-treeselect-helper-zoom-effect-off":!0,"vue-treeselect-helper-hide":t.hasValue||t.trigger.searchQuery};return e("div",{class:n},[t.placeholder])}},void 0,void 0,!1,null,null,null);be.options.__file="src/components/Placeholder.vue";var _e=be.exports,Ee=pe({name:"vue-treeselect--single-value",inject:["instance"],methods:{renderSingleValueLabel:function(){var e=this.instance,t=e.selectedNodes[0],n=e.$scopedSlots["value-label"];return n?n({node:t}):t.label}},render:function(){var e=arguments[0],t=this.instance,n=this.$parent.renderValueContainer,i=t.hasValue&&!t.trigger.searchQuery;return n([i&&e("div",{class:"vue-treeselect__single-value"},[this.renderSingleValueLabel()]),e(_e),e(ye,{ref:"input"})])}},void 0,void 0,!1,null,null,null);Ee.options.__file="src/components/SingleValue.vue";var Ne=Ee.exports,Le=function(){var e=this.$createElement,t=this._self._c||e;return t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 348.333 348.333"}},[t("path",{attrs:{d:"M336.559 68.611L231.016 174.165l105.543 105.549c15.699 15.705 15.699 41.145 0 56.85-7.844 7.844-18.128 11.769-28.407 11.769-10.296 0-20.581-3.919-28.419-11.769L174.167 231.003 68.609 336.563c-7.843 7.844-18.128 11.769-28.416 11.769-10.285 0-20.563-3.919-28.413-11.769-15.699-15.698-15.699-41.139 0-56.85l105.54-105.549L11.774 68.611c-15.699-15.699-15.699-41.145 0-56.844 15.696-15.687 41.127-15.687 56.829 0l105.563 105.554L279.721 11.767c15.705-15.687 41.139-15.687 56.832 0 15.705 15.699 15.705 41.145.006 56.844z"}})])};Le._withStripped=!0;var we=pe({name:"vue-treeselect--x"},Le,[],!1,null,null,null);we.options.__file="src/components/icons/Delete.vue";var Ce=we.exports,xe=pe({name:"vue-treeselect--multi-value-item",inject:["instance"],props:{node:{type:Object,required:!0}},methods:{handleMouseDown:p((function(){var e=this.instance,t=this.node;e.select(t)}))},render:function(){var e=arguments[0],t=this.instance,n=this.node,i={"vue-treeselect__multi-value-item":!0,"vue-treeselect__multi-value-item-disabled":n.isDisabled,"vue-treeselect__multi-value-item-new":n.isNew},r=t.$scopedSlots["value-label"],s=r?r({node:n}):n.label;return e("div",{class:"vue-treeselect__multi-value-item-container"},[e("div",{class:i},[e("span",{class:"vue-treeselect__multi-value-label"},[s]),e("span",{class:"vue-treeselect__icon vue-treeselect__value-remove",on:{mousedown:this.handleMouseDown}},[e(Ce)])])])}},void 0,void 0,!1,null,null,null);xe.options.__file="src/components/MultiValueItem.vue";var De=xe.exports,Me=pe({name:"vue-treeselect--multi-value",inject:["instance"],methods:{renderMultiValueItems:function(){var e=this.$createElement,t=this.instance;return t.internalValue.slice(0,t.limit).map(t.getNode).map((function(t){return e(De,{key:"multi-value-item-".concat(t.id),attrs:{node:t}})}))},renderExceedLimitTip:function(){var e=this.$createElement,t=this.instance,n=t.internalValue.length-t.limit;return n<=0?null:e("div",{class:"vue-treeselect__limit-tip vue-treeselect-helper-zoom-effect-off",key:"exceed-limit-tip"},[e("span",{class:"vue-treeselect__limit-tip-text"},[t.limitText(n)])])}},render:function(){var e=arguments[0],t=this.$parent.renderValueContainer,n={props:{tag:"div",name:"vue-treeselect__multi-value-item--transition",appear:!0}};return t(e("transition-group",ge()([{class:"vue-treeselect__multi-value"},n]),[this.renderMultiValueItems(),this.renderExceedLimitTip(),e(_e,{key:"placeholder"}),e(ye,{ref:"input",key:"input"})]))}},void 0,void 0,!1,null,null,null);Me.options.__file="src/components/MultiValue.vue";var Ie=Me.exports,Ae=function(){var e=this.$createElement,t=this._self._c||e;return t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 292.362 292.362"}},[t("path",{attrs:{d:"M286.935 69.377c-3.614-3.617-7.898-5.424-12.848-5.424H18.274c-4.952 0-9.233 1.807-12.85 5.424C1.807 72.998 0 77.279 0 82.228c0 4.948 1.807 9.229 5.424 12.847l127.907 127.907c3.621 3.617 7.902 5.428 12.85 5.428s9.233-1.811 12.847-5.428L286.935 95.074c3.613-3.617 5.427-7.898 5.427-12.847 0-4.948-1.814-9.229-5.427-12.85z"}})])};Ae._withStripped=!0;var Te=pe({name:"vue-treeselect--arrow"},Ae,[],!1,null,null,null);Te.options.__file="src/components/icons/Arrow.vue";var Re=Te.exports,$e=pe({name:"vue-treeselect--control",inject:["instance"],computed:{shouldShowX:function(){var e=this.instance;return e.clearable&&!e.disabled&&e.hasValue&&(this.hasUndisabledValue||e.allowClearingDisabled)},shouldShowArrow:function(){var e=this.instance;return!e.alwaysOpen||!e.menu.isOpen},hasUndisabledValue:function(){var e=this.instance;return e.hasValue&&e.internalValue.some((function(t){return!e.getNode(t).isDisabled}))}},methods:{renderX:function(){var e=this.$createElement,t=this.instance,n=t.multiple?t.clearAllText:t.clearValueText;return this.shouldShowX?e("div",{class:"vue-treeselect__x-container",attrs:{title:n},on:{mousedown:this.handleMouseDownOnX}},[e(Ce,{class:"vue-treeselect__x"})]):null},renderArrow:function(){var e=this.$createElement,t={"vue-treeselect__control-arrow":!0,"vue-treeselect__control-arrow--rotated":this.instance.menu.isOpen};return this.shouldShowArrow?e("div",{class:"vue-treeselect__control-arrow-container",on:{mousedown:this.handleMouseDownOnArrow}},[e(Re,{class:t})]):null},handleMouseDownOnX:p((function(e){e.stopPropagation(),e.preventDefault();var t=this.instance,n=t.beforeClearAll(),i=function(e){e&&t.clear()};D()(n)?n.then(i):setTimeout((function(){return i(n)}),0)})),handleMouseDownOnArrow:p((function(e){e.preventDefault(),e.stopPropagation();var t=this.instance;t.focusInput(),t.toggleMenu()})),renderValueContainer:function(e){return(0,this.$createElement)("div",{class:"vue-treeselect__value-container"},[e])}},render:function(){var e=arguments[0],t=this.instance,n=t.single?Ne:Ie;return e("div",{class:"vue-treeselect__control",on:{mousedown:t.handleMouseDown}},[e(n,{ref:"value-container"}),this.renderX(),this.renderArrow()])}},void 0,void 0,!1,null,null,null);$e.options.__file="src/components/Control.vue";var Be=$e.exports,ze=pe({name:"vue-treeselect--tip",functional:!0,props:{type:{type:String,required:!0},icon:{type:String,required:!0}},render:function(e,t){var n=arguments[0],i=t.props,r=t.children;return n("div",{class:"vue-treeselect__tip vue-treeselect__".concat(i.type,"-tip")},[n("div",{class:"vue-treeselect__icon-container"},[n("span",{class:"vue-treeselect__icon-".concat(i.icon)})]),n("span",{class:"vue-treeselect__tip-text vue-treeselect__".concat(i.type,"-tip-text")},[r])])}},void 0,void 0,!1,null,null,null);ze.options.__file="src/components/Tip.vue";var Ve,ke,Fe,Pe=ze.exports,He={name:"vue-treeselect--option",inject:["instance"],props:{node:{type:Object,required:!0}},computed:{shouldExpand:function(){var e=this.instance,t=this.node;return t.isBranch&&e.shouldExpand(t)},shouldShow:function(){var e=this.instance,t=this.node;return e.shouldShowOptionInMenu(t)}},methods:{renderOption:function(){var e=this.$createElement,t=this.instance,n=this.node;return e("div",{class:{"vue-treeselect__option":!0,"vue-treeselect__option--disabled":n.isDisabled,"vue-treeselect__option--selected":t.isSelected(n),"vue-treeselect__option--highlight":n.isHighlighted,"vue-treeselect__option--matched":t.localSearch.active&&n.isMatched,"vue-treeselect__option--hide":!this.shouldShow},on:{mouseenter:this.handleMouseEnterOption},attrs:{"data-id":n.id}},[this.renderArrow(),this.renderLabelContainer([this.renderCheckboxContainer([this.renderCheckbox()]),this.renderLabel()])])},renderSubOptionsList:function(){var e=this.$createElement;return this.shouldExpand?e("div",{class:"vue-treeselect__list"},[this.renderSubOptions(),this.renderNoChildrenTip(),this.renderLoadingChildrenTip(),this.renderLoadingChildrenErrorTip()]):null},renderArrow:function(){var e=this.$createElement,t=this.instance,n=this.node;if(t.shouldFlattenOptions&&this.shouldShow)return null;if(n.isBranch){var i={"vue-treeselect__option-arrow":!0,"vue-treeselect__option-arrow--rotated":this.shouldExpand};return e("div",{class:"vue-treeselect__option-arrow-container",on:{mousedown:this.handleMouseDownOnArrow}},[e("transition",{props:{name:"vue-treeselect__option-arrow--prepare",appear:!0}},[e(Re,{class:i})])])}return t.hasBranchNodes?(Ve||(Ve=e("div",{class:"vue-treeselect__option-arrow-placeholder"},[" "])),Ve):null},renderLabelContainer:function(e){return(0,this.$createElement)("div",{class:"vue-treeselect__label-container",on:{mousedown:this.handleMouseDownOnLabelContainer}},[e])},renderCheckboxContainer:function(e){var t=this.$createElement,n=this.instance,i=this.node;return n.single||n.disableBranchNodes&&i.isBranch?null:t("div",{class:"vue-treeselect__checkbox-container"},[e])},renderCheckbox:function(){var e=this.$createElement,t=this.instance,n=this.node,i=t.forest.checkedStateMap[n.id],r={"vue-treeselect__checkbox":!0,"vue-treeselect__checkbox--checked":2===i,"vue-treeselect__checkbox--indeterminate":1===i,"vue-treeselect__checkbox--unchecked":0===i,"vue-treeselect__checkbox--disabled":n.isDisabled};return ke||(ke=e("span",{class:"vue-treeselect__check-mark"})),Fe||(Fe=e("span",{class:"vue-treeselect__minus-mark"})),e("span",{class:r},[ke,Fe])},renderLabel:function(){var e=this.$createElement,t=this.instance,n=this.node,i=n.isBranch&&(t.localSearch.active?t.showCountOnSearchComputed:t.showCount),r=i?t.localSearch.active?t.localSearch.countMap[n.id][t.showCountOf]:n.count[t.showCountOf]:NaN,s=t.$scopedSlots["option-label"];return s?s({node:n,shouldShowCount:i,count:r,labelClassName:"vue-treeselect__label",countClassName:"vue-treeselect__count"}):e("label",{class:"vue-treeselect__label"},[n.label,i&&e("span",{class:"vue-treeselect__count"},["(",r,")"])])},renderSubOptions:function(){var e=this.$createElement,t=this.node;return t.childrenStates.isLoaded?t.children.map((function(t){return e(He,{attrs:{node:t},key:t.id})})):null},renderNoChildrenTip:function(){var e=this.$createElement,t=this.instance,n=this.node;return!n.childrenStates.isLoaded||n.children.length?null:e(Pe,{attrs:{type:"no-children",icon:"warning"}},[t.noChildrenText])},renderLoadingChildrenTip:function(){var e=this.$createElement,t=this.instance;return this.node.childrenStates.isLoading?e(Pe,{attrs:{type:"loading",icon:"loader"}},[t.loadingText]):null},renderLoadingChildrenErrorTip:function(){var e=this.$createElement,t=this.instance,n=this.node;return n.childrenStates.loadingError?e(Pe,{attrs:{type:"error",icon:"error"}},[n.childrenStates.loadingError,e("a",{class:"vue-treeselect__retry",attrs:{title:t.retryTitle},on:{mousedown:this.handleMouseDownOnRetry}},[t.retryText])]):null},handleMouseEnterOption:function(e){var t=this.instance,n=this.node;e.target===e.currentTarget&&t.setCurrentHighlightedOption(n,!1)},handleMouseDownOnArrow:p((function(){var e=this.instance,t=this.node;e.toggleExpanded(t)})),handleMouseDownOnLabelContainer:p((function(){var e=this.instance,t=this.node;t.isBranch&&e.disableBranchNodes?e.toggleExpanded(t):e.select(t)})),handleMouseDownOnRetry:p((function(){var e=this.instance,t=this.node;e.loadChildrenOptions(t)}))},render:function(){var e=arguments[0],t=this.node,n=this.instance.shouldFlattenOptions?0:t.level,i=l()({"vue-treeselect__list-item":!0},"vue-treeselect__indent-level-".concat(n),!0),r={props:{name:"vue-treeselect__list--transition"}};return e("div",{class:i},[this.renderOption(),t.isBranch&&e("transition",r,[this.renderSubOptionsList()])])}},je=pe(He,void 0,void 0,!1,null,null,null);je.options.__file="src/components/Option.vue";var We=je.exports,qe={top:"top",bottom:"bottom",above:"top",below:"bottom"},Qe=pe({name:"vue-treeselect--menu",inject:["instance"],computed:{menuStyle:function(){return{maxHeight:this.instance.maxHeight+"px"}},menuContainerStyle:function(){var e=this.instance;return{zIndex:e.appendToBody?null:e.zIndex}}},watch:{"instance.menu.isOpen":function(e){e?this.$nextTick(this.onMenuOpen):this.onMenuClose()}},created:function(){this.menuSizeWatcher=null,this.menuResizeAndScrollEventListeners=null},mounted:function(){this.instance.menu.isOpen&&this.$nextTick(this.onMenuOpen)},destroyed:function(){this.onMenuClose()},methods:{renderMenu:function(){var e=this.$createElement,t=this.instance;return t.menu.isOpen?e("div",{ref:"menu",class:"vue-treeselect__menu",on:{mousedown:t.handleMouseDown},style:this.menuStyle},[this.renderBeforeList(),t.async?this.renderAsyncSearchMenuInner():t.localSearch.active?this.renderLocalSearchMenuInner():this.renderNormalMenuInner(),this.renderAfterList()]):null},renderBeforeList:function(){var e=this.instance.$scopedSlots["before-list"];return e?e():null},renderAfterList:function(){var e=this.instance.$scopedSlots["after-list"];return e?e():null},renderNormalMenuInner:function(){var e=this.instance;return e.rootOptionsStates.isLoading?this.renderLoadingOptionsTip():e.rootOptionsStates.loadingError?this.renderLoadingRootOptionsErrorTip():e.rootOptionsStates.isLoaded&&0===e.forest.normalizedOptions.length?this.renderNoAvailableOptionsTip():this.renderOptionList()},renderLocalSearchMenuInner:function(){var e=this.instance;return e.rootOptionsStates.isLoading?this.renderLoadingOptionsTip():e.rootOptionsStates.loadingError?this.renderLoadingRootOptionsErrorTip():e.rootOptionsStates.isLoaded&&0===e.forest.normalizedOptions.length?this.renderNoAvailableOptionsTip():e.localSearch.noResults?this.renderNoResultsTip():this.renderOptionList()},renderAsyncSearchMenuInner:function(){var e=this.instance,t=e.getRemoteSearchEntry(),n=""===e.trigger.searchQuery&&!e.defaultOptions,i=!n&&(t.isLoaded&&0===t.options.length);return n?this.renderSearchPromptTip():t.isLoading?this.renderLoadingOptionsTip():t.loadingError?this.renderAsyncSearchLoadingErrorTip():i?this.renderNoResultsTip():this.renderOptionList()},renderOptionList:function(){var e=this.$createElement,t=this.instance;return e("div",{class:"vue-treeselect__list"},[t.forest.normalizedOptions.map((function(t){return e(We,{attrs:{node:t},key:t.id})}))])},renderSearchPromptTip:function(){var e=this.$createElement,t=this.instance;return e(Pe,{attrs:{type:"search-prompt",icon:"warning"}},[t.searchPromptText])},renderLoadingOptionsTip:function(){var e=this.$createElement,t=this.instance;return e(Pe,{attrs:{type:"loading",icon:"loader"}},[t.loadingText])},renderLoadingRootOptionsErrorTip:function(){var e=this.$createElement,t=this.instance;return e(Pe,{attrs:{type:"error",icon:"error"}},[t.rootOptionsStates.loadingError,e("a",{class:"vue-treeselect__retry",on:{click:t.loadRootOptions},attrs:{title:t.retryTitle}},[t.retryText])])},renderAsyncSearchLoadingErrorTip:function(){var e=this.$createElement,t=this.instance,n=t.getRemoteSearchEntry();return e(Pe,{attrs:{type:"error",icon:"error"}},[n.loadingError,e("a",{class:"vue-treeselect__retry",on:{click:t.handleRemoteSearch},attrs:{title:t.retryTitle}},[t.retryText])])},renderNoAvailableOptionsTip:function(){var e=this.$createElement,t=this.instance;return e(Pe,{attrs:{type:"no-options",icon:"warning"}},[t.noOptionsText])},renderNoResultsTip:function(){var e=this.$createElement,t=this.instance;return e(Pe,{attrs:{type:"no-results",icon:"warning"}},[t.noResultsText])},onMenuOpen:function(){this.adjustMenuOpenDirection(),this.setupMenuSizeWatcher(),this.setupMenuResizeAndScrollEventListeners()},onMenuClose:function(){this.removeMenuSizeWatcher(),this.removeMenuResizeAndScrollEventListeners()},adjustMenuOpenDirection:function(){var e=this.instance;if(e.menu.isOpen){var t=e.getMenu(),n=e.getControl(),i=t.getBoundingClientRect(),r=n.getBoundingClientRect(),s=i.height,o=window.innerHeight,a=r.top,l=window.innerHeight-r.bottom>s+40,c=a>s+40;r.top>=0&&r.top<=o||r.top<0&&r.bottom>0?"auto"!==e.openDirection?e.menu.placement=qe[e.openDirection]:e.menu.placement=l||!c?"bottom":"top":e.closeMenu()}},setupMenuSizeWatcher:function(){var e=this.instance.getMenu();this.menuSizeWatcher||(this.menuSizeWatcher={remove:N(e,this.adjustMenuOpenDirection)})},setupMenuResizeAndScrollEventListeners:function(){var e=this.instance.getControl();this.menuResizeAndScrollEventListeners||(this.menuResizeAndScrollEventListeners={remove:w(e,this.adjustMenuOpenDirection)})},removeMenuSizeWatcher:function(){this.menuSizeWatcher&&(this.menuSizeWatcher.remove(),this.menuSizeWatcher=null)},removeMenuResizeAndScrollEventListeners:function(){this.menuResizeAndScrollEventListeners&&(this.menuResizeAndScrollEventListeners.remove(),this.menuResizeAndScrollEventListeners=null)}},render:function(){var e=arguments[0];return e("div",{ref:"menu-container",class:"vue-treeselect__menu-container",style:this.menuContainerStyle},[e("transition",{attrs:{name:"vue-treeselect__menu--transition"}},[this.renderMenu()])])}},void 0,void 0,!1,null,null,null);Qe.options.__file="src/components/Menu.vue";var Ye=Qe.exports,Ke=n(14),Xe=n.n(Ke);function Ue(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}var Je,Ge={name:"vue-treeselect--portal-target",inject:["instance"],watch:{"instance.menu.isOpen":function(e){e?this.setupHandlers():this.removeHandlers()},"instance.menu.placement":function(){this.updateMenuContainerOffset()}},created:function(){this.controlResizeAndScrollEventListeners=null,this.controlSizeWatcher=null},mounted:function(){this.instance.menu.isOpen&&this.setupHandlers()},methods:{setupHandlers:function(){this.updateWidth(),this.updateMenuContainerOffset(),this.setupControlResizeAndScrollEventListeners(),this.setupControlSizeWatcher()},removeHandlers:function(){this.removeControlResizeAndScrollEventListeners(),this.removeControlSizeWatcher()},setupControlResizeAndScrollEventListeners:function(){var e=this.instance.getControl();this.controlResizeAndScrollEventListeners||(this.controlResizeAndScrollEventListeners={remove:w(e,this.updateMenuContainerOffset)})},setupControlSizeWatcher:function(){var e=this,t=this.instance.getControl();this.controlSizeWatcher||(this.controlSizeWatcher={remove:N(t,(function(){e.updateWidth(),e.updateMenuContainerOffset()}))})},removeControlResizeAndScrollEventListeners:function(){this.controlResizeAndScrollEventListeners&&(this.controlResizeAndScrollEventListeners.remove(),this.controlResizeAndScrollEventListeners=null)},removeControlSizeWatcher:function(){this.controlSizeWatcher&&(this.controlSizeWatcher.remove(),this.controlSizeWatcher=null)},updateWidth:function(){var e=this.instance,t=this.$el,n=e.getControl().getBoundingClientRect();t.style.width=n.width+"px"},updateMenuContainerOffset:function(){var e=this.instance,t=e.getControl(),n=this.$el,i=t.getBoundingClientRect(),r=n.getBoundingClientRect(),s="bottom"===e.menu.placement?i.height:0,o=Math.round(i.left-r.left)+"px",a=Math.round(i.top-r.top+s)+"px";this.$refs.menu.$refs["menu-container"].style[W(["transform","webkitTransform","MozTransform","msTransform"],(function(e){return e in document.body.style}))]="translate(".concat(o,", ").concat(a,")")}},render:function(){var e=arguments[0],t=this.instance,n=["vue-treeselect__portal-target",t.wrapperClass],i={zIndex:t.zIndex};return e("div",{class:n,style:i,attrs:{"data-instance-id":t.getInstanceId()}},[e(Ye,{ref:"menu"})])},destroyed:function(){this.removeHandlers()}},Ze=pe({name:"vue-treeselect--menu-portal",created:function(){this.portalTarget=null},mounted:function(){this.setup()},destroyed:function(){this.teardown()},methods:{setup:function(){var e=document.createElement("div");document.body.appendChild(e),this.portalTarget=new Xe.a(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ue(Object(n),!0).forEach((function(t){l()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ue(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({el:e,parent:this},Ge))},teardown:function(){document.body.removeChild(this.portalTarget.$el),this.portalTarget.$el.innerHTML="",this.portalTarget.$destroy(),this.portalTarget=null}},render:function(){var e=arguments[0];return Je||(Je=e("div",{class:"vue-treeselect__menu-placeholder"})),Je}},void 0,void 0,!1,null,null,null);Ze.options.__file="src/components/MenuPortal.vue";var et=Ze.exports,tt=pe({name:"vue-treeselect",mixins:[de],computed:{wrapperClass:function(){return{"vue-treeselect":!0,"vue-treeselect--single":this.single,"vue-treeselect--multi":this.multiple,"vue-treeselect--searchable":this.searchable,"vue-treeselect--disabled":this.disabled,"vue-treeselect--focused":this.trigger.isFocused,"vue-treeselect--has-value":this.hasValue,"vue-treeselect--open":this.menu.isOpen,"vue-treeselect--open-above":"top"===this.menu.placement,"vue-treeselect--open-below":"bottom"===this.menu.placement,"vue-treeselect--branch-nodes-disabled":this.disableBranchNodes,"vue-treeselect--append-to-body":this.appendToBody}}},render:function(){var e=arguments[0];return e("div",{ref:"wrapper",class:this.wrapperClass},[e(ve),e(Be,{ref:"control"}),this.appendToBody?e(et,{ref:"portal"}):e(Ye,{ref:"menu"})])}},void 0,void 0,!1,null,null,null);tt.options.__file="src/components/Treeselect.vue";var nt=tt.exports,it=(n(15),t.default=nt,"0.4.0")}]);
//# sourceMappingURL=vue-treeselect.cjs.min.js.map