<template>
    <Row>
        <Col span="8">
        <DatePicker  confirm clearable ref="start" class="margin-top-5" :options="optionsStart" :clearable="false" v-model="DateStart" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择起始日期" @on-change="date_start"></DatePicker>
        </Col>
        <Col class="margin-top-10" span="1">—</Col>
        <Col span="8">
        <DatePicker confirm clearable ref="end" class="margin-top-5" :options="optionsEnd" :clearable="false" v-model="DateEnd" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择结束日期" @on-change="date_end"></DatePicker>
        </Col>
    </Row>
</template>

<script>
    import util from '../../../libs/util'
    export default {
        name: 'date-picker-set',
        props:{
            gapDays : Number,
            default_start : String,
            default_end : String,

        },
        data(){
            return{
                // 开始日期限定
                optionsStart : {
                    disabledDate: date => {
                        let curTime = Date.now() - 24*60*60*1000;
                        if(this.DateEnd){
                            let startTime = new Date(this.DateEnd).valueOf();
                            return date && (date.valueOf() > startTime || date.valueOf() < curTime)
                        }else{
                            return date && (date.valueOf() < curTime)
                        }
                    }
                },
                // 结束日期限定
                optionsEnd : {
                    disabledDate: date => {
                        let curTime = Date.now() - 24*60*60*1000;
                        let endTime = this.DateStart ? new Date(this.DateStart).valueOf(): Date.now();
                        if(this.DateStart){
                            return date && ((date.valueOf() < endTime ) ||(date.valueOf() < curTime))
                        }else{
                            return date && (date.valueOf() < curTime)
                        }
                    }
                },
                // 开始日期数据绑定
                DateStart : '',
                // 结束日期数据绑定
                DateEnd : ''
            }
        },
        methods:{
            //组件初始化函数
            init () {
                // this.DateStart = this.default_start;
                // this.DateEnd = this.default_end;
                // this.date_now_set();
            },
            date_start (val){
                this.$emit('on-start-change',val);
            },
            date_end (val) {
                this.$emit('on-end-change',val);
            },
            reset () {
                this.$refs.start.handleClear();
                this.$refs.end.handleClear();
            },
            // 设置初始时间为当日
            date_now_set(){
                this.DateStart = util.date_oneDay_string().after;
                this.$refs.end.handleClear();
            },
            // 开始时间设置
            date_start_set (date){
                this.DateStart = date;
            },
            // 结束时间设置
            date_end_set (date) {
                this.DateEnd = date;
            }
        },
        mounted () {

        },
        watch:{
            default_start : {
                handler(nval,oval) {
                    this.DateStart =nval;
                },
                immediate: true,
                deep : true
            },
            default_end : {
                handler(nval,oval) {
                    this.DateEnd =nval;
                },
                immediate: true,
                deep : true
            },

        }
    };
</script>

<style scoped>

</style>
