<template>
    
    <div>
        <Modal id="modal_request_4" v-model="model_show" :closable="false" width="75%">
          <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">发布记录</span>
          </p>
          <Card  dis-hover :bordered="false">
            <Row type="flex" align="middle">

                <Col span="20">
                  <Row type="flex" align="middle">
                    <Col span="24">
                      <Col span="7"  class="margin-right-10">
                        <Col span="7" class="margin-top-10">
                          <span>文档编码:</span>
                        </Col>
                        <Col span="17">
                          <Input
                            id="inputDocNoRecord"
                            class="margin-top-5"
                            clearable
                            v-model="docNo"
                            placeholder="文档编码"
                            @on-enter="search_Interface"
                          ></Input>
                        </Col>
                      </Col>
                      <Col span="9">
                        <Col span="6">
                          <Col class="margin-top-10">操作时间:</Col>
                        </Col>
                        <date-picker
                          id="inputDocDate"
                          ref="datepicker"
                          @on-start-change="update_start_date"
                          @on-end-change="update_end_date"
                          :default_start="this.dateStart"
                          :default_end="this.dateEnd"
                        ></date-picker>
                      </Col>
                      <Col span="7">
                        <Col span="7">
                          <Col class="margin-top-10">操作类型:</Col>
                        </Col>
                        <Col span="17">
                        <Select id='selectDocOperateType' ref="select_ag" class="margin-top-5" v-model="data_product_type" @on-change="changeOperatorType"  placeholder="请选择（默认全部）" clearable>
                            <Option v-for="item in data_product_type_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                        </Select>
                        </Col>
                      </Col>
                    </Col>
                  </Row>
                  <Row class="margin-top-10">
                      <Col span="7">
                        <Col span="7" class="margin-top-10">
                          <span>操作人:</span>
                        </Col>
                        <Col span="17">
                          <Input
                            id="inputOperator"
                            class="margin-top-5"
                            clearable
                            v-model="operator"
                            placeholder="操作人"
                            @on-enter="search_Interface"
                          ></Input>
                        </Col>
                      </Col>
                  </Row>
                </Col>

                <Col class="margin-top-5" span="4" style="text-align: center">
                  <!--<Button class="margin-right-20" id='btn_ag_1' v-url="{url:'/rest/api-group/list'}" type="primary" @click="search_Interface()">查询</Button>-->
                  <Button
                    class="margin-right-10"
                    id="btnDocSearchRecord"
                    type="primary"
                    @click="search_Interface(false)"
                  >查询</Button>
                  <Button id="btnDocResetRecord" type="ghost" @click="reset_Interface">重置</Button>
                </Col>
            </Row>
            <Row class="margin-top-20">
              <Col span="24">
                <Table
                  id="table_ag_1"
                  border
                  ref="selection"
                  :columns="columns_ApiGroupList"
                  :data="data_ApiGroupList"
                  @on-selection-change="handleselection"
                ></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                  <Page
                    class="margin-top-10"
                    style="float: right"
                    :total="pageTotal"
                    :page-size="10"
                    :current="pageNo"
                    show-elevator
                    @on-change="search_Interface"
                  ></Page>
                </Tooltip>
              </Col>
              <loading :show="show_loading_apiGroup"></loading>
            </Row>
          </Card>
          <div slot="footer">
            <!-- <Button type="primary" @click="close_modal">确定</Button> -->
            <Button type="ghost" id="btnDocClose" @click="close_modal">关闭</Button>
          </div>
          
        </Modal>
        <modal_confirm :transfer="false" ref="modal_confirm" :title="modal_confirm_title" :warning_red="modal_confirm_warning_red"
        :warning_black="modal_confirm_warning_black" :reason_fail="modal_confirm_reason_fail" :desc="modal_confirm_desc"
        :reason_length="modal_confirm_reason_length" :reason_show="modal_confirm_reason_show"
        @confirm_handler="confirm_handler"></modal_confirm>
    </div>
</template>

<script>
import api from "../../../api/api";
import datePicker from "../../common-components/date-components/date-picker";
import loading from "../../my-components/loading/loading";
import commonSelect from "../../common-components/select-components/selectCommon";
import util from "../../../libs/util";
import modal_confirm from "../../common-components/modal-components/modal_confirm";

export default {
  name: "modal-link-api-group",
  components: {
    loading,
    datePicker,
    commonSelect,
    modal_confirm
  },
  props: {
    code: String //当前服务分组
  },
  data() {
    return {
      //  * 确认弹窗部分
        // 确认弹窗标题
        modal_confirm_title: "",
        // 确认弹窗红色提示部分
        modal_confirm_warning_red: "",
        // 确认弹窗黑色提示部分
        modal_confirm_warning_black: "",
        // 确认弹窗必填原因规范提示
        modal_confirm_reason_fail: "",
        // 确认弹窗原因描述
        modal_confirm_desc: "",
        // 确认弹窗原因长度限制
        modal_confirm_reason_length: 100,
        // 确认弹窗原因是否展示
        modal_confirm_reason_show: true,
      // 产品码
      docNo:"",
      // 操作人
      operater:"",
      data_interface_name:"",
      // 窗口显示
      model_show: false,
      // 与表相关的下拉框数目
      count_select_related: 1,
      // api分组选择数据绑定
      data_product_type: "",
      data_product_type_List:[
        {label:"发布",value:"PUBLISH"},
        {label:"回滚",value:"RECOVER"}
      ],
      operator:"",
      // 服务提供方选择数据绑定
      data_select_serviceSupplier: "",
      // 表格选中数据
      multiSelectedData: [],
      // 分页总数
      pageTotal: 0,
      // 当前页数
      pageNo: 1,
      // 表格头数据绑定
      columns_ApiGroupList: [
        {
          title: "文档名称",
          width: 140,
          render: (h, params) => {
            return h("div", [
              h("p", params.row.title),
              h("p", "(" + params.row.docNo + ")")
            ]);
          },
          align: "center"
        },
        {
          title: "发布原因",
          width: 140,
          key: "publishReason",
          align: "center"
        },
        {
          title: "操作时间",
          width: 180,
          render: (h, params) => {
            return h("div", [
              h("p", params.row.createdDate),
            ]);
          },
          align: "center"
        },
        {
          title: "操作类型",
          render: (h, params) => {
            var operType = ""
            if(params.row.operType == "PUBLISH"){
              operType = "发布"
            }
            if(params.row.operType == "RECOVER"){
              operType = "回滚"
            }
           
            return h("div", [
              h("p", operType),
            ]);
          },
          align: "center"
        },
        {
          title: "操作人",
          align: "center",
          render: (h, params) => {
            return h("div", [
              h("p", params.row.operator)
            ]);
          }
        },

        {
          renderHeader: (h, params) => {
            return h("div", [h("p", "版本号")]);
          },
          minWidth:180,
          key: "docVersion",
          type: "html",
          align: "center",

        },
        // {
        //   title: "操作",
        //   align: "center",
        //   key: "operations",
        //   render: (h, params) => {
        //     return h("div", [
        //         h(
        //           "Button",
        //           {
        //             class:"btnDocRecover",
        //             props: {
        //               type: "error",
        //               size: "small",
        //               disabled: params.row.operType == "RECOVER" ? true : false
        //             },
        //             directives: [{
        //                 name: 'url',
        //                 value: {url: '/rest/doc/recover'}
        //             }],
        //             on: {
        //               click: () => {
        //                   this.$Modal.confirm({
        //                     title: '确认发布',
        //                     content: '<p>确定要回滚吗？</p>',
        //                     onOk: () => {
        //                       this.confirm_handler(params.row)
        //                     },
        //                     onCancel: () => {
        //                       this.$Message.info('取消成功');
        //                     }
        //                 });
        //               }
        //             }
        //           },
        //         "回滚"
        //         ),
        //     ])
        //   }  
        // }
      ],
      // 表格数据绑定
      data_ApiGroupList: [
      ],
      // 加载动画数据绑定
      show_loading_apiGroup: false,
      // 开始日期绑定
      dateStart: "",
      //结束日期绑定
      dateEnd: "",
    };
  },
  methods: {
    changeOperatorType(value){
      this.data_product_type = value
    },
    rollback(row){
      var dataTemp = {
            title: "确认回滚",
            warning_red: "确认要回滚到所选的已发布版本？",
            warning_black: "",
            reason_fail: "回滚原因必填！",
            desc: "最长可输入300字符。",
            length: 300,
            show: true
          };
        this.modal_confirm_info_init(dataTemp);
        this.$refs.modal_confirm.set_current_id(row.id);
        this.$refs.modal_confirm.preview_show();
    },
    // 确认弹窗初始化函数
    modal_confirm_info_init(data) {
      this.modal_confirm_title = data.title;
      this.modal_confirm_warning_red = data.warning_red;
      this.modal_confirm_warning_black = data.warning_black;
      this.modal_confirm_reason_fail = data.reason_fail;
      this.modal_confirm_desc = data.desc;
      this.modal_confirm_reason_length = data.length;
      this.modal_confirm_reason_show = data.show;
    },
    confirm_handler(data) {
      var params = {
        id:data.id,
        // cause:data.reason,
      }
      api.yop_doc_recover(params).then(response => {
        if (response.status === "success") {
            this.$Modal.success({
                title: '',
                content: '回滚成功'
            });
            this.search_Interface();
        } else {
          this.$Modal.warning({
              title: '',
              content: response.message
          });
        }
      });
    },
    setProductCode(val){
        this.docNo = val
    },
    show_model() {
      this.model_show = true;
    },
    close_modal() {
      this.model_show = false;
    },
  
  
    // 页面查询按钮
    search_Interface(val) {
      this.show_loading_apiGroup = true;
      let params = {
        docNo: this.docNo.trim(),
        operType: this.data_product_type,
        operator:this.operator.trim(),
        createdDateStart: util.dateFormat_component(this.dateStart),
        createdDateEnd: util.dateFormat_component_end(this.dateEnd),
        _pageNo: 1,
        // _pageSize: 10
      };
      if (val) {
        params._pageNo = val;
      }
      util.paramFormat(params);
      console.log(params,"变更记录的params")
      api.yop_doc_publish_list(params).then(response => {
        let status = response.data.status;
        if (response.data.status === "success") {
          this.tableDataFormat(response.data.data.page.items);
          this.pageNo = response.data.data.page.pageNo;
          if (
            response.data.data.page.items &&
            response.data.data.page.items.length > 0
          ) {
            if (response.data.data.page.items.length < 10) {
              this.pageTotal = response.data.data.page.items.length;
            } else {
              this.pageTotal = NaN;
            }
          } else {
            this.pageTotal = NaN;
          }

          this.show_loading_apiGroup = false;
        } else {
          this.$Modal.error({
            title: "错误",
            content: response.data.message
          });
          this.show_loading_apiGroup = false;
        }
      });
    },
    // 页面列表数据处理函数
    tableDataFormat(data) {
      this.data_ApiGroupList = [];
      if (data && data.length > 0) {
        data.forEach(item => {
          this.data_ApiGroupList.push({
            id: util.empty_handler2(item.id),
            docNo: util.empty_handler(item.docNo),
            title: util.empty_handler2(item.docTitle),
            operType:util.empty_handler2(item.operType),
            operator: util.empty_handler2(item.operator),
            docVersion: util.empty_handler2(item.docVersion),
            cause: util.empty_handler2(item.cause),
            createdDate: util.empty_handler2(item.createdDate),
            publishReason: util.empty_handler2(item.publishReason),
          });
        });
      }
    },
    // spcode处理函数
    data_spCode_handler(spcode) {
      let group = this.$store.state.select["serviceSupplier"].data;
      for (var i in group) {
        if (group[i].value === spcode) {
          return group[i].name;
        }
      }
    },
    // 安全需求处理函数
    data_security_handler(securities) {
      let data_security = "";
      if (securities && securities.length > 0) {
        for (var i in securities) {
          if (i === securities.length) {
            data_security = data_security + securities[i];
          } else {
            data_security = data_security + securities[i] + "<br/>";
          }
        }
        return data_security;
      } else {
        return "";
      }
    },
    // 页面重置按钮
    reset_Interface() {
      this.$refs.select_ag.clearSingleSelect();
      this.$refs.datepicker.reset();
      this.dateStart = "";
      this.dateEnd = "";
      this.operator = "";
      this.docNo = "";
    },
    // 开始日期更新
    update_start_date(val) {
      this.dateStart = val;
    },
    // 结束日期更新
    update_end_date(val) {
      this.dateEnd = val;
    },
    // 下拉框加载完处理函数
    select_callBack() {
      this.count_select_related--;
      if (this.count_select_related === 0) {
        this.search_Interface();
      }
    },

    // 同步更新选择api分组函数
    updateSelect_serviceSupplier(val) {
      this.data_select_serviceSupplier = val;
      this.formCustom.supplier = val;
    },
    // 表格内容改动时
    handleselection(value) {
      this.multiSelectedData = value;
    }
  },
  mounted () {
  },
};
</script>

<style scoped>
</style>

