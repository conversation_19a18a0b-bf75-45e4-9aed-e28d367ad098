<template>
	<Row>
		<Col :span="11">
			<Select
				id="btn_module_DocCategorySelect"
				:disabled="disabled"
				:clearable="!disabled"
				v-model="level1"
				style="width:100%"
				@on-change="onLevel1Change"
			>
				<Option v-for="item in level1List" :value="item.id" :key="item.id" :disabled="item.name === '对接产品'">{{
					item.name
				}}</Option>
			</Select>
			<Spin fix v-if="level1Show">加载中...</Spin>
		</Col>
		<Col :span="11" offset="2">
			<Select
				id="btn_module_DocCategorySelect2"
				:disabled="disabled"
				:clearable="!disabled"
				v-model="level2"
				style="width:100%"
				@on-change="onLevel2Change"
			>
				<Option v-for="item in level2List" :value="item.id" :key="item.id">{{ item.name }}</Option>
			</Select>
			<Spin fix v-if="level2Show">加载中...</Spin>
		</Col>
	</Row>
</template>

<script>
import Api from "~/api/doc/createdDoc";
export default {
	props: ["value"],
	data() {
		return {
			level1Show: false,
			level2Show: false,
			level1: "",
			level1List: [],
			level2: "",
			level2List: [],
			disabled: false,
		};
	},
	created() {
		this.getDocCategoryList(-1, 1);
	},
	methods: {
		setValue(arr, disabled = false) {
			this.level1 = arr[0];
			this.disabled = disabled;
			this.getDocCategoryList(this.level1, 2).then((res) => {
				this.level2 = arr[1];
			});
		},
		resetValue() {
			this.level1 = "";
			this.level2 = "";
			this.disabled = false;
		},
		getDocCategoryList(pid, level) {
			this[`level${level}Show`] = true;
			return Api.getDocCategoryList({ pid })
				.then((response) => {
					if (response.data.status === "success") {
						this[`level${level}List`] = response.data.data.result;
					} else {
						this.$ypMsg.notice_error(this, "错误", response.data.message);
					}
					return "done";
				})
				.finally(() => {
					this[`level${level}Show`] = false;
				});
		},
		onLevel1Change(value) {
			this.$emit("input", [value, ""]);
			this.level2 = "";
			this.level2List = [];
			if (!this.level1) return;
			this.getDocCategoryList(this.level1, 2);
		},
		onLevel2Change(value) {
			this.$emit("input", [this.level1, value]);
		},
	},
};
</script>

<style></style>
