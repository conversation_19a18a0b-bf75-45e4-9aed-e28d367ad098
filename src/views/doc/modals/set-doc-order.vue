<template>
  <Modal id="modal_request_4" v-model="model_show" :closable="false" width="75%">
    <p slot="header" style="color:#2d8cf0;">
      <span style="color:black">设置产品文档排序</span>
    </p>
    <Card  dis-hover :bordered="false">
      <Row type="flex">
        <Col span="20">
          <Col class="margin-top-10"   span="2">
            <span>文档分类:</span>
          </Col>
          <Col class="margin-top-5"    span="14">
              <DocCategorySelect ref="DocCategorySelect" v-model="categoryId" />
          </Col>
        </Col>

        <Col span="4">
          <Col class="margin-top-10" span="11" style="text-align:center">
            <Button id="btnDocSearch" type="primary" @click="search_Interface(false)">查询</Button>
          </Col>
          <Col class="margin-top-10" span="11" style="text-align:center">
            <Button id="btnDocReset" type="ghost" @click="reset_Interface">重置</Button>
          </Col>
        </Col>
      </Row>
      <Row class="margin-top-20">
        <Table
            id="sortable_table"
            border
             ref="singleTable"
            :columns="columns_ApiGroupList"
            :data="tableData"
            @on-selection-change="handleselection"
             row-key="id"
          ></Table>
        <loading :show="show_loading_apiGroup"></loading>
      </Row>
    </Card>
    <div slot="footer">
      <Button type="primary" v-url="{url:'/rest/doc/arrange'}" id="btnSureOrder" @click="sure_modal">确定排序</Button>
      <Button type="ghost" id="btnCancelOrder"  @click="close_modal">取消</Button>
    </div>
  </Modal>
</template>
<script>
import api from "../../../api/api";
import loading from "../../my-components/loading/loading";
import util from "../../../libs/util";
import Sortable from "sortablejs";
import DocCategorySelect from './DocCategorySelect'
export default {
  name: "modal-link-api-group",
  components: {
    loading,
    DocCategorySelect,
  },
  props: {
    code: String //当前服务分组
  },
  data() {
    return {
      categoryId:[],
      // 窗口显示
      model_show: false,
      // 表格选中数据
      multiSelectedData: [],
      // 分页总数
      pageTotal: 0,
      currentPage:0,
      // 当前页数
      pageNo: 1,
      // 表格头数据绑定
      columns_ApiGroupList: [
        {
          title: "序号",
          width: 80,
          align: "center",
          render: (h, params) => {
            console.log(params)
            return h("span", params.index + 1)
          }
        },
         {
          title: "文档名称",
          "min-width": 240,
          render: (h, params) => {
            return h("div", [
              h("p", params.row.title),
              h("p", "(" + params.row.docNo + ")")
            ]);
          },
          align: "center"
        },
        {
          title: "状态",
          render: (h, params) => {
            let color = "red";
            let status = "已删除";
            if (params.row.status === "PUBLISHED") {
              color = "green";
              status = "已发布";
            }else if(params.row.status === "EDIT"){
              color = "grey";
              status = "编辑中";
            }else if (params.row.status === "DRAFT") {
              color = "grey";
              status = "草稿";
            }
            return h("div", [
              h(
                "Tag",
                {
                  style: {
                    align: "center"
                  },
                  props: {
                    color: color
                  }
                },
                status
              )
            ]);
          },
          align: "center"
        },
        {
          renderHeader: (h, params) => {
            return h("div", [h("p", "创建时间"), h("p", "最后修改时间")]);
          },
          align: "center",
          render: (h, params) => {
            return h("div", [
              h("p", params.row.createdDate),
              h("p", params.row.lastModifiedDate),
            ]);
          },

        },
        {
          title: "操作",
          key:'Action',
          align: "center",
          render: (h, params) => {
              return h('div', [
                  h('Icon', {
                      class:"iconDocOrder",
                      props: {
                       type:"arrow-move"
                      },
                      style:{
                        color:'#ad8893',
                        cursor: 'move',
                        marginRight: '5px',
                        fontSize:'18px'
                      },
                  }),
              ]);
          }

        },
      ],
      // 表格数据绑定
      tableData: [],
      tableOldData: [],
      // 加载动画数据绑定
      show_loading_apiGroup: false,
    };
  },
  methods: {
    initModel() {
      this.categoryId = []
      this.tableData = []
      this.tableOldData = []
      this.$refs.DocCategorySelect.resetValue()
    },
    show_model() {
      this.model_show = true;
      this.initModel()
      
    },
    close_modal() {
      this.model_show = false;
    },
    sure_modal(){
      var param = [];
      for (var i = 0; i < this.tableData.length; i++) {
        if(this.tableData[i]){
           param.push({
            id: this.tableData[i].id,
            seq: i + 1,
            category: this.tableData[i].category,
          });
        }
       
      }
      api.yop_doc_category_arrange(param).then(response => {
        if (response.status === "success") {
            this.close_modal();
            this.$Modal.success({
                title: '',
                content: '确认排序成功'
            });
        } else {
          this.$Modal.warning({
              title: '',
              content: response.message
          });
          // this.search_Interface(false);排序失败，是否恢复
        }
      });
    },
    current_change:function(currentPage){
      this.currentPage = currentPage;
    },
    // 页面查询按钮
    search_Interface(val) {
      if(!this.categoryId[1]) {
        this.$Modal.warning({
          content: '请选择产品分类'
        })
        return
      }
      this.show_loading_apiGroup = true;
      let params = {
        categoryId: this.categoryId[1],
      };
      if (val) {
        params._pageNo = val;
      }
      api.yop_doc_category_list(params).then(response => {
        if (response.data.status === "success") {
          this.tableOldData = this.tableDataFormat(response.data.data.result);
          this.tableData = this.tableOldData.slice();
          this.show_loading_apiGroup = false;
        } else {
          this.$Modal.error({
            title: "错误",
            content: response.data.message
          });
          this.show_loading_apiGroup = false;
        }
      });
    },
    // 页面列表数据处理函数
    tableDataFormat(data) {
      let formatData = [];
      if (data && data.length > 0) {
        data.forEach((item,index) => {
          formatData.push({
            id:item.id,
            indexOld:index,
            docNo: item.docNo,
            title:item.title,
            category:item.category,
            categoryPathName:item.categoryPathName,
            visible:item.visible,
            status:item.status,
            lastModifiedDate: util.empty_handler(item.lastModifiedDate),
            createdDate: util.empty_handler(item.createdDate),
            seq:item.seq,
            spCode:item.spCode,
            spName:item.spName
          });
        });
      }
      return formatData;
    },
  
    // // 页面重置按钮
    reset_Interface() {
      this.categoryId = [];
      this.$refs.DocCategorySelect.resetValue()
    },
    // 表格内容改动时
    handleselection(value) {
      this.multiSelectedData = value;
    },
    //表格拖动排序
    dragSort() {
      const tbody = document.querySelector('#sortable_table tbody')
      const _this = this
      Sortable.create(tbody, {
        onEnd({ newIndex, oldIndex }) {
          const currRow = _this.tableData.splice(oldIndex, 1)[0]
          _this.tableData.splice(newIndex, 0, currRow)
          var new_tableData = _this.tableData
          _this.$forceUpdate()          
        }
      })
    },
  },
  mounted () {
      this.dragSort();
  },
};
</script>

<style scoped>
</style>

