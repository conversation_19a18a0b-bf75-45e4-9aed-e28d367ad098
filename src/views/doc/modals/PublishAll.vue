<template>
	<el-drawer
		title="发布文档"
		:visible.sync="show"
		:wrapperClosable="false"
		direction="rtl"
		size="60%"
		class="publish-drawer"
	>
		<Form ref="form" style="height: 100%;" :model="params" :rules="ruleValidate">
			<div style="padding: 16px 16px 0;">
				<Alert type="warning" show-icon>请确认信息无误后再进行发布。</Alert>
			</div>
			<div class="publish-all-form">
				<div style="flex: 1;">
					<FormItem prop="pageIds" label="请选择文档进行发布：" label-position="top">
						<div style="display: inline-block; width: 100%;">
							<div style="margin-bottom: 12px;display: flex;align-items: center;">
								<Input
									v-model="searchKeyWord"
									placeholder="请输入文档名称/负责人名称"
									style="margin-right: 12px;"
								/>
								<Button style="margin-right: 12px;" @click="reset" type="ghost">重置</Button>
								<Button type="primary" @click="query">查询</Button>
							</div>
							<Table
								:height="clientHeight - 300"
								v-loading="loading"
								:columns="columns"
								:data="tableData"
								@on-selection-change="onSelectionChange"
							></Table>
						</div>
					</FormItem>
				</div>
				<div class="line"></div>
				<div style="width: 300px;">
					<FormItem label="发布原因：" prop="publishReason">
						<Input
							v-model="params.publishReason"
							:maxlength="100"
							type="textarea"
							placeholder="请输入发布原因"
						/>
					</FormItem>
					<FormItem label="通知人(支持姓名、邮箱前缀等模糊搜索)：">
						<NotifiersSelect v-model="params.notifiers" />
					</FormItem>
				</div>
			</div>
		</Form>
		<div class="footer-drawer">
			<Button type="ghost" @click="cancel" style="margin-right: 12px;">取消</Button>
			<Button type="primary" @click="confirm" :disabled="disabled" :loading="confirmLoading"
				>确认发布</Button
			>
		</div>
	</el-drawer>
</template>

<script>
import Api from '~/api/doc/apiClass'
import NotifiersSelect from '../components/NotifiersSelect.vue'
import Loading from '../../my-components/loading/loading'
export default {
	components: {
		NotifiersSelect,
		Loading,
	},
	data() {
		return {
			show: false,
			loading: false,
			confirmLoading: false,
			tableData: [],
			originTableData: [],
			columns: [
				{
					type: 'selection',
					width: 60,
					align: 'center',
				},
				{
					title: '文档名称',
					key: 'pageFullTitle',
				},
				{
					title: '负责人',
					key: 'ownerName',
				},
			],
			params: {
				notifiers: [],
				publishReason: '',
				pageIds: [],
			},
			ruleValidate: {
				pageIds: [{ required: true, message: '请选择文档', trigger: 'change', type: 'array' }],
				publishReason: [{ required: true, message: '请输入发布原因', trigger: 'change' }],
			},
			docNo: '',
			searchKeyWord: '',
			clientHeight: 788,
		}
	},
	computed: {
		disabled() {
			const { pageIds, publishReason } = this.params
			return pageIds.length < 1 || !publishReason
		},
	},
	created() {
		this.clientHeight = document.documentElement.clientHeight
	},
	methods: {
		query() {
			let data = this.originTableData
			if (this.searchKeyWord) {
				data = this.originTableData.filter((o) => {
					if (o.ownerName && o.ownerName.includes(this.searchKeyWord)) {
						return true
					}
					return o.pageFullTitle.includes(this.searchKeyWord)
				})
			}
			this.tableData = data.map((o) => {
				o._checked = this.params.pageIds.includes(o.pageId)
				return o
			})
		},
		reset() {
			this.searchKeyWord = ''
			this.query()
		},
    onSelectionChange(selection) {
      const pageIds = selection.map((s) => s.pageId)
      this.tableData.forEach(t => {
        if (t._checked && !pageIds.includes(t.pageId)) {
          this.params.pageIds = this.params.pageIds.filter(id => id !== t.pageId)
        }
      })
      this.params.pageIds = [...new Set(this.params.pageIds.concat(pageIds))]
      this.query()
		},
		showModal(id, docNo) {
      this.$refs.form && this.$refs.form.resetFields()
      this.clientHeight = document.documentElement.clientHeight
			this.show = true
			this.docNo = docNo
			this.searchKeyWord = ''
			this.params = {
				notifiers: [],
				pageIds: [],
				id,
				publishReason: '',
				operator: localStorage.userName,
			}
			this.listForPublish()
		},
		listForPublish() {
			if (!this.docNo) return
			this.loading = true
			Api.listForPublish({
				docNo: this.docNo,
			})
				.then((res) => {
					this.tableData = res.data.data.result
					this.originTableData = res.data.data.result
					this.loading = false
				})
				.catch((err) => {
					this.loading = false
					this.$ypMsg.notice_error(this, '错误', err.message, err.solution)
				})
		},
		confirm() {
			this.$refs.form.validate((valid) => {
				if (valid) {
					this.publish()
				}
			})
		},
		async publish() {
			try {
				this.confirmLoading = true
				let params = JSON.parse(JSON.stringify(this.params))
				if (this.params.pageIds.length === this.originTableData.length) {
					params.pageIds = []
				}
				const res = await Api.checkForPublish(params)
        if (res.status !== 'success') {
          this.confirmLoading = false
          this.$ypMsg.notice_error(this, '错误', res.message, res.solution)
          return
				}
				let invalidApis = res.data.result.invalidApis
				if (invalidApis.length > 0) {
					let solution = ''
					invalidApis.forEach((element) => {
						solution += `<p>${element}</p>`
          })
          this.confirmLoading = false
          this.$ypMsg.notice_error(this, '错误', '请检查以下API的描述信息', solution)
          return
				}
				const resPublish = await Api.publish(params)
				if (resPublish.status !== 'success') {
					return Promise.reject(resPublish)
				}
				this.$ypMsg.notice_success(this, '发布成功')
				this.cancel()
				this.confirmLoading = false
      } catch (err) {
				this.confirmLoading = false
				if (this.$store.state.ifShowCheckFlag) {
					this.show = false
					return
				}
				this.$ypMsg.notice_error(this, '错误', err.message, err.solution)
			}
		},
		cancel() {
			this.show = false
		},
	},
}
</script>
<style>
.publish-all-form {
	height: calc(100vh - 200px);
}
</style>
<style scoped lang="less">
.publish-all-form {
	padding: 0 16px 16px;
	display: flex;
	.line {
		width: 1px;
		height: 100%;
		background-color: #eee;
		margin: 0 16px;
		flex-shrink: 0;
	}
}
.footer-drawer {
	display: flex;
	justify-content: flex-end;
	padding: 16px;
	background-color: #fff;
	border-top: 1px solid #e8e8e8;
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
}
</style>
