<template>
  <div class="mark-con">
      <div class="mark" v-for="(item, index) in currentTreeNodeBlocks" :key="index" >
        <h2 style="margin-bottom: 8px">{{item.title}}</h2>
        <template v-if="item.id === 'scenes' && pageType === 'API'">
          <Input v-model="item.data" type="textarea" :rows="12" placeholder="填写API的主要功能。发布后将展示到产品详情“API列表”的描述字段中" />
        </template>
        <template v-else>
          <div :id="'vditor'+index" ></div>
        </template>
        <div v-if="!ifNewAddMarkdown && pageType !== 'API'" class="text-center delete-bar margin-top-5 margin-bottom-5">
          <Button size="small" @click="addMarkDown('markdown',index+1)" type="text" ><i style='font-size: 18px;' class="iconfont ">&#xe645;</i></Button>  
          <Button size="small" @click="addMarkDown('warning',index+1)" type="text" ><i style='font-size: 18px;' class="iconfont ">&#xe61b;</i></Button>
          <Button size="small" @click="deleteMarkDown(index)" class="delete-btn" type="text"> <i style="font-size: 18px;" class="iconfont">&#xe60f;</i> </Button>
        </div>
      </div>
      <Modal v-model="addTitle" :closable="false" title="添加模块">
          <Form ref="formInline" inline :label-width="80">
              <FormItem label="模块标题：">
                  <Input type="text" v-model="formData.title" placeholder="请输入模块标题" style="width: 300px">
                  </Input>
              </FormItem>
          </Form>
        <div slot="footer">
          <Button id="btn_resList_17" type="ghost" @click="addTitle = false">取消</Button>
          <Button id="btn_resList_16" type="primary" @click="onOk">确认</Button>
        </div>
      </Modal>
  </div>
</template>

<script>
  import Vditor from 'vditor'
  import bus from '../../../libs/bus'
  import api from '../../../api/api'
  export default {
    name: 'editorMarkdown',
    props: ['currentTreeNodeBlocks', 'ifNewAddMarkdown', 'pageType'],
    data () {
      return {
        addTitle: false,
        formData: {
          title: '',
          types: '',
          index: ''
        },
        // 实例vidtor
        vditor: [],
        // 存放markdown数据
        markdownArr: [],
        // 当前选中markdown数据
        currentMarkdown: ''
      }
    },
    created () {
  
    },
    mounted () {
      bus.$on('getMarkdown', this.returnMarkdown);
      setTimeout(() => {
        this.$nextTick(() => {
          this.initVditor();
        })
      }, 10);
    },
    methods: {
      initVditor () {
        const that = this;
        this.currentTreeNodeBlocks.forEach((item, index, options) => {
          if (item.id === 'scenes' && this.pageType === 'API') return
          item.data = item.data.replace(/&lt;/g, '<').replace(/&gt;/g, '>')
          // 实例化 vidtor
          this.vditor[index] = new Vditor(`vditor${index}`, {
            toolbar: [
              // "emoji",
              'headings',
              'bold',
              'italic',
              'strike',
              'link',
              '|',
              'list',
              'ordered-list',
              'check',
              'outdent',
              'indent',
              '|',
              'quote',
              'line',
              'code',
              'inline-code',
              // "insert-before",
              // "insert-after",
              '|',
              'upload',
              // "record",
              'table',
              '|',
              'undo',
              'redo',
              '|',
              'edit-mode',
              'fullscreen',
              'help'
            ],
            placeholder: item.id === 'invoking' ? '填写接口调用说明、注意事项等相关信息。发布后将展示到“API详情”页面的“使用说明”字段中' : '',
            toolbarConfig: {
              pin: true
            },
            cdn: 'https://img.yeepay.com/fe-resources/vditor',
            preview: {
              theme: {
                path: 'https://img.yeepay.com/fe-resources/vditor/dist/css/content-theme'
              }
            },
            focus: (val) => {
              this.currentMarkdown = val
            },
            upload: {
              max: 20 * 1024 * 1024,
              handler (file) {
                let formData = new FormData();
                let fileName = '';
                for (let i in file) {
                  formData.append('fileStream', file[i])
                  fileName = file[i].name
                }
                api.yop_attachment_upload(formData).then(
                  (response) => {
                    if (response.status === 'success') {
                      let imgMdStr = `![${fileName}](${localStorage.remoteIP}${response.data.fileUrl})`;
                      that.currentTreeNodeBlocks.forEach((item, index) => {
                        if (that.vditor[index].getValue().indexOf(that.currentMarkdown) >= 0) {
                          that.vditor[index].insertValue(imgMdStr)
                        }
                      });
                    } else {
                      that.$ypMsg.notice_error(that, '图片上传错误', response.message, response.solution);
                    }
                  }
                )
              }
            },
            after: () => {
              this.vditor[index].setValue(item.data)
            },
            cache: {
              enable: false
            }
          })
          // this.vditor[index].setValue(item.data)
        });
        // 设置预览区内容
      },
      onOk () {
        const { title, types, index } = this.formData
        if (!title) {
          this.$Message.error('请输入模块标题')
          return
        }
        this.$emit('addMarkDown', types, index, title);
      },
      addMarkDown (types, index) {
        this.formData.title = ''
        this.formData.types = types
        this.formData.index = index
        this.addTitle = true
      },
      deleteMarkDown (index) {
        this.$emit('deleteMarkDown', index);
      },
      returnMarkdown () {
        this.vditor.forEach((item, index) => {
          if (this.currentTreeNodeBlocks[index]) {
            this.currentTreeNodeBlocks[index].data = item.getValue().replace(/</g, '&lt;').replace(/>/g, '&gt;')
          }
        });
        this.$emit('getChildMark', this.currentTreeNodeBlocks);
      }
    }
  
  }
</script>
<style scoped> 
.text-center{
    text-align: center;
}
.mark{
  position: relative;
}
.delete-btn{
  position: absolute;
  right: -27px;
  top: 0;
}
</style>
<style lang="less">
  .vditor {
    border: 1px solid #ccc;
    margin-bottom: 10px;
    min-height: 400px;
  }
  .vditor-preview {
    padding: 0 20px;
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.1);
  }
  .vditor-reset h1 {
    // text-align: center;
  }
</style>