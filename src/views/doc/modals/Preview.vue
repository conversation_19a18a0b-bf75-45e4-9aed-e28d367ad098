<template>
	<iframe
		:src="url"
		style="width: 100%; height: calc(100vh - 200px); border: none;"
		frameborder="0"
		ref="iframe"
	></iframe>
</template>

<script>
export default {
	props: ['type', 'html'],
	data() {
		return {
			url: '',
		}
	},
	mounted() {
		const that = this
		window.addEventListener(
			'message',
			(event) => {
				if (event.data === 'getDocsData') {
					that.$refs.iframe.contentWindow.postMessage(
						{
							type: 'docsData',
							html: that.html,
						},
						'*'
					)
				}
			},
			false
		)
		const origin =
			window.location.origin === 'boss3g.yeepay.com'
				? 'https://open.yeepay.com'
				: 'https://qaopen.yeepay.com'
		this.url = `${origin}/preview/${this.type}`
	},
}
</script>
