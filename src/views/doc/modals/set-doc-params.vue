<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.el-checkbox__inner {
  width: 12px !important;
  height: 12px !important;
}

.custom-tree-node {
  font-size: 12px;
}

.yop-explain-120 {
  font-size: 12px;
  color: grey;
  padding-left: 120px;
  line-height: 30px;
}
.red {
  color: red;
}
.green {
  color: green;
}
/*@import '../API_Management_Open/api_Mangement_Open.less';*/
@import "../../../styles/common.less";
</style>
<template>
  <div>
    <Modal id="modal_request_4" v-model="model_show" :closable="false" width="60%">
      <p slot="header" style="color:#2d8cf0;">
        <span style="color:black">设置文档属性</span>
      </p>
        <Card dis-hover :bordered="false">
          <Row type="flex" align="middle">
            <Col span="20">
            <div>
              <Form ref="form_detail" :model="form_detail" :rules="rule_detail" :label-width="150">
                <FormItem label="文档类型：">
                  <p  v-show="this.doc_model_show_type === 'PRODUCT'">产品文档（产品+API+SPI）</p>
                  <p v-show="this.doc_model_show_type === 'PLATFORM'">平台文档</p>
                </FormItem>
                <FormItem v-show="this.doc_model_show_type === 'PRODUCT'" label="产品编码：">
                  <p v-for="(item ,index ) in form_detail.products">{{item.productName}}({{item.productCode}})</p>
                </FormItem>
                <FormItem label="文档版本：">
                  <p>{{form_detail.version}}</p>
                </FormItem>
                <FormItem label="文档编码：">
                  <p>{{form_detail.docNo}}</p>
                </FormItem>
                <FormItem label="文档名称：" prop="title">
                  <Input
                    id="inputSetDocName"
                    type="text"
                    size="small"
                    v-model="form_detail.title"
                    @on-focus="customerNo_focus"
                    style="width:85%"
                  ></Input>
                </FormItem>
                <p  class="yop-explain-150 margin-left-30">最长支持输入30个字符</p>
                <!-- <FormItem  v-show="this.doc_model_show_type === 'PRODUCT'" >
                  <label slot="label">
                    <span style="color:red">*</span>文档标签：
                  </label>
                  <Card dis-hover style="width:85%">
                    <Scroll height="250">
                      <Input
                        id="inputDocLabel"
                        size="small"
                        class="margin-bottom-10"
                        placeholder="文档标签搜索"
                        v-model="filterText"
                      ></Input>
                      <el-tree
                        draggable
                        ref="tree_sg"
                        :data="data_tree"
                        show-checkbox
                        node-key="code"
                        :default-expand-all="false"
                        :default-checked-keys="form_detail.categories"
                        :filter-node-method="filterNode"
                        :expand-on-click-node="false"
                        @check-change="handleCheckChange"

                      >
                        <span class="custom-tree-node" slot-scope="{ node, data }">
                          <span>
                            <Button
                              v-show="node.data.enableStatus && node.data.depth!==1 "
                              style="color: green"
                              type="text"
                              size="small"
                              icon="ios-albums-outline"
                            ></Button>
                            <Button
                              v-show="!node.data.enableStatus && node.data.depth!==1"
                              style="color: red"
                              type="text"
                              size="small"
                              icon="ios-albums-outline"
                            ></Button>
                            <Button
                              v-show="node.data.depth ===1"
                              style="color: grey"
                              type="text"
                              size="small"
                              icon="ios-albums-outline"
                            ></Button>
                            {{node.label}}
                          </span>
                        </span>
                      </el-tree>
                    </Scroll>
                  </Card>
                  <loading :show="show_loading_tree"></loading>
                </FormItem> -->
                
                <!-- <FormItem label="文档标签：">
                  <common-select
                    ref="select_appKey_m"
                    @on-update="updateSelect_appKey_model"
                    type="combo"
                    keyWord="result"
                    holder="请选择文档标签（默认全部）"
                    code="appKey"
                    title="appName"
                    size="small"
                    group="service_authz_appKey"
                    @on-loaded="select_callBack"
                    :default="this.form_detail.appKeyCode"
                    :uri="this.$store.state.select.service_authz_appKey.uri"
                    style="width:55%"
                  ></common-select>
                  <Input class="edit-detail-textarea margni-top-5" type="text"
                    size="small" v-model="customLabel" @on-blur="onblur" @on-focus="onfocus" placeholder="自定义标签" style="width:20%"></Input><div class="operate-editable" v-show="show_operate"><Button type="primary" size="small" icon="ios-checkmark-empty" @click="sure_add_label"></Button><Button size="small" icon="ios-close-empty" @click="close_operate"></Button></div>
                </FormItem>
              -->
                <FormItem  v-show="this.doc_model_show_type === 'PRODUCT'" label="文档可见性：" prop="visible">
                  <RadioGroup id="radioDocVisible" v-model="form_detail.visible"  @on-change="defaultChange">
                    <Radio id="rbtn_resList_3" label="PUBLIC">公开</Radio>
                    <Radio id="rbtn_resList_4" label="PROTECTED">授权可见</Radio>
                    <!-- <Radio id="rbtn_resList_4" label="PRIVATE">私有</Radio>  -->
                  </RadioGroup>
                </FormItem>
                <!-- 文档属性 是否展示先注释 默认传true -->
                <!-- <FormItem  v-show="this.doc_model_show_type === 'PRODUCT'" label="是否展示：" prop="display">
                  <RadioGroup id="radioDocDisplay" v-model="form_detail.display"  @on-change="displayChange">
                    <Radio id="rbtn_resList_5" label="true">是</Radio>
                    <Radio id="rbtn_resList_6" label="false">否</Radio>
                  </RadioGroup>
                </FormItem> -->

                <FormItem  v-show="this.doc_model_show_type === 'PLATFORM'" label="文档可见性：">
                  <p v-show="this.doc_model_show_type === 'PLATFORM'?form_detail.visible = 'PUBLIC':''">公开</p>
                  <p  class="margin-left-30" style="color:red">注意：公开即文档发布审核通过后，所有商户可见。</p>
                </FormItem>

                <FormItem v-show="this.doc_model_show_type === 'PRODUCT'" label="文档分类：">
                  <Select id='select_doc_category1' ref="doc_category1" class="margin-top-5" v-model="data_select_category1" @on-change="changeCategory1"  placeholder="请选择（默认全部）" style="width: auto;" clearable>
                  <Option v-for="item in categories1" :value="item.code" :key="item.code">{{ item.name }}</Option>
                  </Select>
                  <Select id='select_doc_category2' ref="doc_category2" class="margin-top-5" v-show="show_doc_category2" v-model="data_select_category2" @on-change="changeCategory2"  placeholder="请选择（默认全部）" style="width: auto;" clearable>
                      <Option v-for="item in categories2" :value="item.code" :key="item.code">{{ item.name }}</Option>
                  </Select>
                </FormItem>

                <!-- <FormItem label="上传文档LOGO：">
                    <div>
                        <div class="demo-upload-list" v-for="(item,index) in uploadList" :key="index">
                          <Input type="text" size="small" v-model="item.url"></Input>
                            <div class="demo-upload-list-cover">
                                <Icon type="ios-trash-outline" @click.native="handleRemove(item)"></Icon>
                            </div>
                        </div>
                        <Upload ref="upload" :show-upload-list="false" :format="['jpg','jpeg','png']" :max-size="2048" :before-upload="handleBeforeUpload" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize" type="drag" action="//jsonplaceholder.typicode.com/posts/" style="display: inline-block;width:58px;">
                            <div style="width: 58px;height:58px;line-height: 58px;">
                                <Icon type="camera" size="20"></Icon>
                            </div>
                        </Upload>
                    </div>
                </FormItem> -->
                <FormItem  v-show="this.doc_model_show_type === 'PRODUCT'" label="描述：" prop="desc">
                    <Input id="inputDocDecs" type="textarea" size="small" v-model="form_detail.desc"
                            style="width:85%"></Input>
                </FormItem>
                <p  v-show="this.doc_model_show_type === 'PRODUCT'" class="yop-explain-150 margin-left-30">最长支持输入300个字符</p>
              </Form>
            </div>
            
            <loading :show="show_loading_preview"></loading>
          </Col>
        </Row>
        </Card>
        <div slot="footer">
              <Button type="primary" v-url="{url:'/rest/doc/settings/edit'}"  id="btnSaveDocParams" @click="expires_submit('form_detail')">保存</Button>
              <Button type="ghost"  id="btnCancelSave" @click="expires_cancel">取消</Button>
            </div>
        <!-- <modal-sga-new-app ref="modal_sga_new_app"></modal-sga-new-app> -->
    </Modal>
  </div>
</template>

<script>
import util from "../../../libs/util";
import api from "../../../api/api";
import loading from "../../my-components/loading/loading";
import datePicker from "../../common-components/date-components/date-picker-set";
import commonSelect from "../../common-components/select-components/selectCommon_params_single";
// import modalSgaNewApp from './modal-sga-new-app'


export default {
  name: "set_doc",
  props: {
    size: String
  },
  watch: {
    filterText(val) {
      if (val || val === 0) {
        this.service_group_top_ctrl(true);
      } else {
        this.service_group_top_ctrl(false);
      }
      this.$refs.tree_sg.filter(val);
    }
  },
  components: {
    loading,
    datePicker,
    commonSelect
    // modalSgaNewApp
  },
  data() {
    return {
      midPwList:[],
      doc_model_show_type:"",
      model_show:false,
      // 显示自定义标签的编辑操作
      show_operate:false,
      // 自定义标签
      customLabel:"自定义标签",
      // 是否可编辑
      // 文件
      uploadList: [],
      // 服务分组loading
      show_loading_tree: false,
      // 筛选文字
      filterText: "",
      // 开始日期绑定
      dateStart: "",
      //结束日期绑定
      dateEnd: "",
      // 文档分类
      data_select_category1:"",
      data_select_category2:"",
      categories1:[],
      categories2:[],
      categoryMap:{},
      // 基本信息数据绑定
      form_detail: {
        "id": "",
        "products": [],
        "docNo": "",
        "title": "",
        "type": "", //PLATFORM：平台文档，PRODUCT：产品文档
        //是否展示 
        'display': 'true',
        "visible": "", //文档可见性 PUBLIC：公开 PRIVATE：不可见 PROTECTED：受保护
        "status": "", //DRAFT:草稿，PUBLISHED：已发布，DELETED：已删除
        "docVersion": "",
        "category": "",
        "categoryVo":{},
        "logo": "",
        "desc": "",
        "version": "",
        "createdDate": "",
        "lastModifiedDate": ""
      },
      rule_detail: {
        title: [{ required: true, message: "文档名称不能为空", trigger: "blur" }],
        visible: [{ required: true, message: "文档可见性不能为空" }],
        display: [{ required: true, message: "是否展示不能为空" }],
      },
      show_loading_preview: false,
      data_tree: [],
      // 上一次请求的商编
      last_customerNo: "",
      docCodes:[],
      currentId:"",
      currentVersion:"",
    };
  },
  computed: {
    show_doc_category2: function () {
      return this.categories2 && this.categories2.length > 0;
    }
  },
  methods: {
    handleCheckChange () {
      let res = this.$refs.tree_sg.getCheckedNodes();
      this.docCodes = [];
      res.forEach((item) => {
          this.docCodes.push(item.code)
      })
      // if(this.productCodes.length != 0){
      //     this.filterText = "1"
      // }else{
      //     this.filterText = ""
      // }
    },
    // 是否显示属性
    displayChange(val){
      this.form_detail.display = val
    },
    defaultChange(val){
      this.form_detail.visible = val
    },
    show_model(row) {
        this.model_show = true;
        this.doc_model_show_type = row.type;
        this.currentId = row.id
        this.currentVersion = row.version
        api.yop_doc_settings_detail({id:this.currentId}).then(response => {
        if (response.data.status === "success") {
          let data = response.data.data.result
          this.categories2 = [];
          if(data.categoryVo && data.categoryVo.parent) {
            this.data_select_category1 = data.categoryVo.parent.code
            this.data_select_category2 = data.categoryVo.code
            this.categories2 = this.getCategoryChildren(this.data_select_category1);
          } else {
            this.data_select_category1 = data.categoryVo.code
            this.data_select_category2 = ""
          }
          this.form_detail = data
        } else {
          this.$ypMsg.notice_error(
            this,
            "失败",
            response.data.message,
            response.data.solution
          );
        }
      });
      // this.$refs.select_ag.updateList();
    },
    onfocus(){
      this.show_operate = true;
      this.customLabel = ""
    },
    // 失去焦点事件
    onblur(){
      this.show_operate = false;
      this.customLabel = ""
    },
    // 确定添加
    sure_add_label(){
      this.show_operate = false;
      this.customLabel = ""
    },
    // 不添加
    close_operate(){
      this.show_operate = false;
      this.customLabel = ""
    },
    // 上传图片
    handleBeforeUpload(file) {
        // 创建一个 FileReader 对象
        let reader = new FileReader()
        // readAsDataURL 方法用于读取指定 Blob 或 File 的内容
        // 当读操作完成，readyState 变为 DONE，loadend 被触发，此时 result 属性包含数据：URL（以 base64 编码的字符串表示文件的数据）
        // 读取文件作为 URL 可访问地址
        reader.readAsDataURL(file)

        const _this = this
        reader.onloadend = function (e) {
            file.url = reader.result
            _this.uploadList.push(file)
        }
    },
    handleRemove(file) {
        this.uploadList.splice(this.uploadList.indexOf(file), 1)
    },
    handleFormatError(file) {
      this.$Notice.warning({
        title: '文件格式不正确',
        desc: '文件 ' + file.name + ' 格式不正确，请上传 jpg 或 png 格式的图片。'
      })
    },
    handleMaxSize(file) {
      this.$Notice.warning({
        title: '超出文件大小限制',
        desc: '文件 ' + file.name + ' 太大，不能超过 2M。'
      })
    },

    // 启用/禁用所有1级服务分组
    service_group_top_ctrl(option) {
      this.data_tree.forEach(item => {
        item.disabled = option;
      });
    },
    // 组件初始化获取
    init() {
      this.$refs.form_detail.resetFields();
      this.form_detail.title = "";
      // this.$refs.select_appKey_m.resetSelected();
      // this.$refs.select_appKey_m.data_List = [];
      this.$store.state.select.service_authz_appKey.data = [];
      this.data_tree = [];
      this.loadCommonCategoryList();
    },
    expires_submit(val) {
      this.$refs[val].validate(valid => {
        if (valid) {
          // let checked_sgs = this.$refs.tree_sg.getCheckedKeys();
          // if (checked_sgs.length === 0) {
          //   this.$ypMsg.notice_warning(this, "请至少选择一个文档标签");
          // } else {
              var param = {
                "id": this.form_detail.id,
                "title": this.form_detail.title,
                "visible": this.form_detail.visible, //文档可见性 PUBLIC：公开 PRIVATE：不可见 PROTECTED：受保护
                'display':  true,
                "category": this.data_select_category2 ? this.data_select_category2 : this.data_select_category1,
                "desc": this.form_detail.desc,
                "version": this.form_detail.version
              };
              api.yop_doc_settings_edit(param).then(response => {
                if (response.status === "success") {
                  // let result = response.data.result;
                  if(localStorage.needAudit === 'true'){
                    this.$ypMsg.notice_info(this,response.message);
                  }else{
                    this.$ypMsg.notice_success(this,'设置成功','');
                  }
                  this.preview_cancel();
                  // this.$Modal.success({
                  //     title: '',
                  //     content: '设置成功'
                  // });
                  // 更新文档列表
                  this.$emit('search_interface', '');
                } else {
                  this.$Modal.warning({
                      title: '',
                      content: response.message
                  });
                }
              });
          // }
        } else {
          this.$Message.error("请检查");
        }
      });
    },

    expires_cancel() {
      this.preview_cancel();
    },
    // 窗口关闭按钮
    preview_cancel() {
      this.model_show = false;
    },
    // 窗口显示按钮
    preview_show() {
      this.form_detail.visible = "";
      this.model_show = true;
    },
    // 开始日期更新
    update_start_date(val) {
      this.dateStart = val;
    },
    // 结束日期更新
    update_end_date(val) {
      this.dateEnd = val;
    },
    // 日期初始化
    date_picker_init() {
      this.$refs.datepicker.date_now_set();
    },
 
   
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    select_callBack() {},
    // 树的结构数据处理
    information_tree_handler(data, index) {
      let topTemp = true;
      if (index === 0) {
        topTemp = true;
      } else {
        topTemp = false;
      }
      let returnData = [];
      index++;
      if (data) {
        data.forEach(item => {
          let enableStatusTemp = true;
          let statusTemp = "启用";
          let optionTemp = "禁用";
          let colorTemp = "green";
          let nameTemp = "";
          if (item.status === "ENABLE") {
            statusTemp = "启用";
            optionTemp = "禁用";
            colorTemp = "green";
            enableStatusTemp = true;
          } else {
            statusTemp = "禁用";
            optionTemp = "启用";
            colorTemp = "red";
            enableStatusTemp = false;
          }
          if (util.getLength(item) > 10) {
            nameTemp = item.substring(0, 10) + "...";
          } else {
            nameTemp = item;
          }
          if (!!item.children) {
            returnData.push({
              code: item,
              top: topTemp,
              disabled: false,
              label: nameTemp,
              name: item.name,
              depth: item.level,
              status: statusTemp,
              option: optionTemp,
              color: colorTemp,
              enableStatus: enableStatusTemp,
              children: this.information_tree_handler(item.children, index)
            });
          } else {
            returnData.push({
              // id: item.id,
              // pid : item.pid,
              code: item,
              top: topTemp,
              label: nameTemp,
              name: item.name,
              depth: item.level,
              disabled: false,
              status: statusTemp,
              option: optionTemp,
              color: colorTemp,
              enableStatus: enableStatusTemp,
              children: []
            });
          }
        });
      }
      return returnData;
    },
    // 应用标识请求

    // 商户编号聚焦时
    customerNo_focus() {
      this.last_customerNo = this.form_detail.title;
    },
    //文档分类
    changeCategory1(value){
      this.data_select_category1 = value
      let children = this.getCategoryChildren(value);
      if( children && children.length > 0) {
        this.categories2 = children;
      } else{
        this.categories2 = [];
        this.data_select_category2 = "";
      }
    },
    changeCategory2(value){
      this.data_select_category2 = value
      let needChildren = this.getCategoryChildren(this.data_select_category1);
      if(value === "" && needChildren && needChildren.length > 0) {
          this.$Modal.warning({
              title: '',
              content: '标准产品请选择二级分类'
          });
      }
    },
    getCategoryChildren(parentCode){
      let result = [];
      if(parent) {
        let parent = this.categoryMap[parentCode];
        if( parent && parent.children && parent.children.length > 0) {
          return parent.children;
        }
      }
      return result;
    },
    loadCommonCategoryList() {
      api.yop_doc_commons_category_list().then(response => {
        if (response.data.status === "success") {
            this.categories1 = [];
            this.categoryMap = {};
            let data = response.data.data.result;
            if (data && data.length > 0) {
              data.forEach((item,index) => {
                let tempItem = {
                  indexOld:index,
                  code:item.code,
                  name: item.name,
                  pathName:item.pathName,
                  children:item.children
                };
                this.categories1.push(tempItem);
                this.categoryMap[item.code] = tempItem;
            });
          }
        } else {
          this.$Modal.warning({
              title: '',
              content: response.data.message
          });
        }
      });
    }
  },
  mounted() {
    this.init();
  }
};
</script>

<style scoped>
.demo-upload-list {
    display: inline-block;
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
    margin-right: 4px;
}

.demo-upload-list img {
    width: 100%;
    height: 100%;
}

.demo-upload-list-cover {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, .6);
}

.demo-upload-list:hover .demo-upload-list-cover {
    display: block;
}

.demo-upload-list-cover i {
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    margin: 0 2px;
}

.ivu-icon {
    line-height: 58px;
}
.edit-detail-textarea {
  height: 20px;
  min-width:100px;
  line-height: 20px;
  display: inline-block;
  margin-top: 5px;
  vertical-align: middle;
  border:0;
}
.edit-detail-textarea input.ivu-input {
  border:0!important;
  margin-top: -1px;
}
.operate-editable {
  display: inline-block;
  margin-left: 10px;
  vertical-align: middle;
  margin-top: 3px;
}

</style>
