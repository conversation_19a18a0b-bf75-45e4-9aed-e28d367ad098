<template>
	<Modal title="产品模块" v-model="show" class-name="vertical-center-modal">
		<Form ref="form" :model="params" label-position="left" :label-width="100" :rules="ruleValidate">
			<FormItem label="模块名称：" prop="name">
				<Input
					v-model="params.name"
					id="btn_module_name_input"
					:maxlength="10"
					placeholder="请输入产品模块名称"
				/>
			</FormItem>
			<FormItem label="模块描述：" prop="desc">
				<Input
					v-model="params.desc"
					id="btn_module_desc_input"
					:maxlength="200"
					type="textarea"
					placeholder="请输入产品模块描述"
				/>
			</FormItem>
		</Form>
		<div class="text-center" slot="footer">
			<Button id="btn_resList_16" type="primary" @click="confirm">确认</Button>
			<Button id="btn_resList_17" type="ghost" @click="cancel">取消</Button>
		</div>
	</Modal>
</template>

<script>
import Api from '~/api/doc/apiClass'
export default {
  data() {
    return {
      show: false,
      ruleValidate:{
        name: [
          { required: true, message: '请输入模块名称', trigger: 'change' }
        ],
        desc: [
          { required: true, message: '请输入模块描述', trigger: 'change' }
        ],
      },
      params: {
        name: '',
        desc: '',
      }
    }
  },
  methods: {
    showAddModal(docNo) {
      this.$refs.form.resetFields()
      this.show = true
      this.params = {
        name: '',
        desc: '',
        type: 'DOC_API',
        docNo,
      }
    },
    showEditModal(data) {
      this.show = true
      this.params = {
        id: data.id,
        name: data.title,
        desc: data.desc,
      }
    },
    confirm() {
      this.$refs.form.validate((valid) => {
          if (valid) {
            if(this.params.id) {
              this.updateApiClass()
            } else {
              this.createApiClass()
            }
          }
      })
    },
    createApiClass() {
      Api.createApiClass(this.params)
      .then(response => {
        if(response.status === 'success'){
            this.$emit('onOk')
            this.cancel()
        }else{
            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
        }
      })
    },
    updateApiClass() {
      Api.updateApiClass(this.params)
      .then(response => {
        if(response.status === 'success'){
            this.$emit('onOk')
            this.cancel()
        }else{
            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
        }
      })
    },
    cancel() {
      this.show = false
    }
  }
}
</script>