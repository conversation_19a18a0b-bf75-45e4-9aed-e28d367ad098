<style scoped>
.yop-explain-150 {
  font-size: 12px;
  color: grey;
  padding-left: 150px;
  line-height: 30px;
}
.ivu-table-cell {
  padding-left: 10px;
  padding-right: 10px;
}

/* 拖拽 */
.dndList {
  width: 65%;
  display: inline-block;
}
.dndList-list {
  display: inline-block;
}
.list-complete-item {
  cursor: pointer;
  text-align: center;
  font-size: 14px;
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 10px;
  min-width: 100px;
  line-height: 30px;
  height: 30px;
  border: 1px solid #bfcbd9;
  border-radius: 5px;
  transition: all 1s;
}
.v-top {
  vertical-align: top;
  margin-top: 4px;
}

.list-complete-item.sortable-chosen {
  background: #4ab7bd;
}

.list-complete-item.sortable-ghost {
  background: #30b08f;
}
.undraggable {
  background-color: red;
}

.list-complete-enter,
.list-complete-leave-active {
  opacity: 0;
}
.list-complete-item-handle i {
  position: relative;
  right: 3px;
}
.default {
min-width: 50px;
  display: inline-block;
  text-align: center;
  padding: 0px 16px;
}
.plus {
  border: 1px solid #64be6f;
  cursor: pointer;
  color:#fff;
  border-radius: 4px;
  background: #64be6f;
  vertical-align: middle;
  margin-top: -4px;
  width: 25px;
  text-align: center;
  display: inline-block;
}
.actived {
    background:#64be6f;
    color:#fff;
    font-weight: bolder;
}
.margin-top-5 {
  margin-top:5px;
  vertical-align: top
}


/*@import '../../API_Management_Open/api_Mangement_Open.less';*/
@import "../../../styles/common.less";
</style>
<template>
  <div>
    <Modal id="modal_request_1" v-model="model_show" :closable="false" width="75%">
      <p slot="title">编辑服务分组</p>
      <a slot="extra" @click.prevent="preview_cancel">
        <Icon type="close"></Icon>
      </a>
      <Card dis-hover>
        <p slot="title">关联API</p>
        <div>
          <span style="margin-top:9px;vertical-align:top;display:inline-block">场景名称:</span>
          <div
                class="list-complete-item v-top"
                @click="search_Interface('',-1)"
                :class="activeClass == -1 ? 'actived':''"
              >
                <div class="default">
                  全部
                </div>
              </div>
          <div class="dndList"  :class='!list1.length ? "margin-top-5":""'>
            
            <div class="dndList-list">
              <draggable
                :list="list1"
                :options="{group:{name: falgs,pull:'clone'},sort: true, disabled: disabled,draggable:'.list-complete-item'}"
                @start="start22"
                @end="end22"
                class="dragArea11"
                style="padding: 4px 10px 10px 10px;"
              >
                <div
                  v-for="(element, index) in list1"
                  :key="element.id"
                  class="list-complete-item"
                 :class="activeClass == index ? 'actived':''"
                  @click="search_Interface(element,index)"
                >
                  <div class="list-complete-item-handle">
                    <span style="display:inline-block">{{element.name}}</span>
                    <!-- <i class="el-icon-delete" id="deleteScene" @click="handleDel(index, element.id)"></i> -->
                  </div>
                </div>
                <div class="plus" id="plusScene"  v-url="{url:'/rest/product/scene/create'}" @click="plus">+</div>
                <div class="plus" v-show="list1.length" id="minusScene" style="background:red"  v-url="{url:'/rest/product/scene/delete'}" @click="minus">-</div>

              </draggable>
            </div>
          </div>
          <Button type="primary" id="sureOrder"   v-url="{url:'/rest/product/scene/order'}" style="float:right;margin-right:19px;margin-top:10px;" @click="sureOrder">确定排序</Button><!---->
          <!-- <Button type="ghost" @click="hideOrder">取消</Button> -->
        </div>
        <Tabs :value="tabNow"  @on-click="detail_info_get">
          <TabPane id="tab_basic_1" label="API接口" name="api_interface">
            <Row type="flex" align="middle">
              <Col span="20">
                <Col offset="1" span="7">
                  <Col span="7" class="margin-top-10">
                    <span>API URI:</span>
                  </Col>
                  <Col span="17">
                    <Input
                      id="input_apiM_1"
                      class="margin-top-5"
                      clearable
                      v-model="data_interface_uri"
                      placeholder="API URI"
                      @on-enter="search_Interface"
                    ></Input>
                  </Col>
                </Col>
                <!-- <Col class="margin-top-5" offset="1" span="7">
                  <Col span="7" class="margin-top-10">
                    <span>状态:</span>
                  </Col>
                  <Col span="17">
                    <Select
                      id="select_apiM_2"
                      class="margin-top-5"
                      ref="data_select_status"
                      v-model="data_select_status"
                      placeholder="请选择（默认全部）"
                      clearable
                    >
                      <Option
                        v-for="item in data_status_List"
                        :value="item.value"
                        :key="item.value"
                      >{{ item.label }}</Option>
                    </Select>
                  </Col>
                </Col> -->
                <Col class="margin-top-5" offset="1" span="7"></Col>
              </Col>
              <Col span="4">
                <Col class="margin-top-10" span="11" style="text-align:center">
                  <Button id="btn_apiM_1" type="primary"  v-url="{url:'/rest/product/api/list'}"  @click="search_Interface('',-1)">查询</Button><!---->
                </Col>
                <Col class="margin-top-10" span="11" style="text-align:center">
                  <Button id="btn_apiM_2" type="ghost" @click="reset_Interface">重置</Button>
                </Col>
              </Col>
            </Row>
            <Row class="margin-top-10">
              <Col span="24">
                <Button
                id="addApi"
                  class="margin-right-10"
                  style="float:right"
                  size="small"
                  type="primary"
                  @click="api_link_modal_show"
                  v-url="{url:'/rest/product/api/batch-create'}"
                >添加api</Button>
                  <!--  -->

                <Button
                id="deleteApi"
                  class="margin-right-10"
                  size="small"
                  type="ghost"
                  @click="batch_unlink_api"
                  v-url="{url:'/rest/product/api/batch-delete'}"
                >批量移除</Button>
                  <!--  -->

              </Col>
            </Row>
            <Row class="margin-top-10">
              <Col span="24">
                <Table
                  id="table_1"
                  border
                  ref="api_interface"
                  :columns="columns_ApiInterfaceList"
                  :data="data_ApiInterfaceList"
                  @on-selection-change="handleselection1"
                ></Table>
                <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end"> -->
                  <Page
                    class="margin-top-10"
                    style="float: right;margin-bottom:10px;"
                    :total="pageTotalInterface"
                    :page-size="10"
                    :current="pageNoInteface"
                    show-elevator
                    @on-change="search_Interface"
                  ></Page>
                </Tooltip>
              </Col>
            </Row>
            <loading :show="show_loading_interface"></loading>
          </TabPane>
          <TabPane id="tab_basic_2" label="API分组" name="api_group">
            <Row>
              <Col span="24">
                <Button
                id="addApiGroup"
                  class="margin-right-10"
                  style="float:right"
                  size="small"
                  type="primary"
                  @click="apiGroup_link_modal_show"
                  v-url="{url:'/rest/product/api/batch-create'}"
                >添加api分组</Button>
                  <!--  -->
                
                <Button
                id="deleteApiGroup"
                  class="margin-right-10"
                  size="small"
                  type="ghost"
                  @click="batch_unlink_apiGroup"
                  v-url="{url:'/rest/product/api/batch-delete'}"
                >批量移除</Button>
                  <!--  -->
              </Col>
            </Row>
            <Row class="margin-top-10">
              <Col span="24">
                <Table
                  id="table_2"
                  border
                  ref="api_group"
                  :columns="columns_ApiGroupList"
                  :data="data_ApiGroupList"
                  @on-selection-change="handleselection2"
                ></Table>
                <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end"> -->
                  <Page
                    class="margin-top-10"
                    style="float: right;margin-bottom:10px;"
                    :total="pageTotalGroup"
                    :page-size="10"
                    :current="pageNoGroup"
                    show-elevator
                    @on-change="pageRefreshGroup"
                  ></Page>
                </Tooltip>
              </Col>
            </Row>
            <loading :show="show_loading_group"></loading>
          </TabPane>
        </Tabs>
      </Card>
      <div  slot="footer" style="text-align: right;margin-top:10px;">
        <Button type="ghost" @click="preview_cancel">关闭</Button>
      </div>
      <loading :show="show_loading_preview"></loading>
      <modal-link-api ref="modal_link_api" :code="current_code" @update-list="pageRefreshInteface"  :closable="false"></modal-link-api>
      <!-- <modal-link-api-group
        ref="modal_link_api_group"
        :code="current_code"
        @update-list="pageRefreshGroup"
         :closable="false"
      ></modal-link-api-group> -->

<!-- 添加场景 -->
        <Modal v-model="add_scene_modal_show" width="500" :transfer=false>
          <p slot="header" style="color:#000;text-align:left;font-size: 18px;">
              <Icon type="ios-information-circle"></Icon>
              <span>新增场景</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <label for="" class="reasonType"><span style="color:red">*</span>场景名称：</label>
              <Input type="text" v-model="sceneName" style="width:75%;font-size: 12x;" placeholder="请输入" :maxlength="30"></Input>
              <p style="color:#888;font-size:14px;margin-top:10px;margin-left:67px;">最长可输入30字符</p>
          </div>
          <div slot="footer">
              <Button type="primary"  @click="sure_model">确定</Button>
              <Button type="primary"  @click="hide_model">取消</Button>
          </div>
        </Modal>

    </Modal>
  </div>
</template>

<script>
import api from "../../../api/api";
import loading from "../../my-components/loading/loading";
import util from "../../../libs/util";
// import modalLinkApiGroup from "./set-doc-params";

import draggable from "vuedraggable";
export default {
  name: "modal-edit-not-top",
  components: {
    loading,
    // modalLinkApiGroup,
    draggable
  },
  data() {
    // 服务分组名称验证
    const validate_sgName = (rule, value, callback) => {
      if (util.format_check_common(value, /^[0-9\u4e00-\u9fa5]+?$/)) {
        callback(new Error("格式不正确"));
      } else if (util.getLength(value) > 64) {
        callback(new Error("长度不能大于64"));
      } else {
        callback();
      }
    };
    // 服务分组描述验证
    const validate_resourceDes = (rule, value, callback) => {
      if (value) {
        if (util.getLength(value) > 300) {
          callback(new Error("长度不能大于300"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      activeClass: -1,
      sceneName:"",
      sceneId:"",
        productCode:"",
        add_scene_modal_show:false,
      falgs: "article",
      disabled: false,
      list1: [
        {id:1,name:1},
        {id:2,name:2},
      ],
      data_interface_name: "",
      // 状态下拉框选项数据
      data_status_List: [],
      // 状态下拉框数据绑定
      data_select_status: "",
      // APIuri数据绑定
      data_interface_uri: "",

      // api选中
      multiSelectedData_api: [],
      // api分组选中
      multiSelectedData_apiGroup: [],
      // loading interface
      show_loading_interface: false,
      // loading apiGroup
      show_loading_group: false,
      // 是否为顶级资源 true为顶级 false为非顶级
      top_resource: false,
      // 编辑界面显示隐藏
      model_show: false,
      // loading
      show_loading_preview: false,
      // api分组列表
      data_serviceSupplier_List: [],
      // 选择数据绑定
      select_data_serviceSupplier: "",
      // 基本信息数据绑定
      form_detail: {
        PServiceGroup: "",
        PServiceGroupCode: "",
        serviceGroupCode: "",
        serviceGroupName: "",
        authType: "",
        authTypeCode: "",
        description: ""
      },
      // 表单验证规则数据绑定
      rule_detail: {
        serviceGroupName: [
          { required: true, message: "服务分组名称不能为空", trigger: "blur" },
          { validator: validate_sgName, trigger: "blur" }
        ],
        description: [{ validator: validate_resourceDes, trigger: "blur" }]
      },
      tabNow: "api_interface",
      // api接口列表
      columns_ApiInterfaceList: [
        {
          type: "selection",
          width: 60,
          align: "center"
        },
        {
          title: "API 接口",
          key: "name",
          "min-width": 150,
          render: (h, params) => {
            return h("div", [
              h("p", params.row.value),
            ]);
          },
          align: "center"
        },
        // {
        //     title: '版本',
        //     width: 70,
        //     key: 'version',
        //     align: 'center'
        // },
        // {
        //   title: "状态",
        //   render: (h, params) => {
        //     if (params.row.status) {
        //       let color = "red";
        //       let status = "已删除";
        //       if (params.row.status === "ACTIVE") {
        //         color = "green";
        //         status = "活动中";
        //       } else if (params.row.status === "FROZEN") {
        //         color = "grey";
        //         status = "已下线";
        //       } else {
        //         color = "red";
        //         status = "已禁用";
        //       }
        //       return h("div", [
        //         h(
        //           "Tag",
        //           {
        //             style: {
        //               align: "center"
        //             },
        //             props: {
        //               color: color
        //             }
        //           },
        //           status
        //         )
        //       ]);
        //     } else {
        //       return h("div", "--");
        //     }
        //   },
        //   width: 95,
        //   align: "center"
        // },
        {
          title: "操作",
          align: "center",
          key: "operations",
          width: 150,
          render: (h, params) => {
            return h("div", [
              // h(
              //   "Button",
              //   {
              //     props: {
              //       type: "text",
              //       size: "small"
              //     },
              //     directives: [
              //       {
              //         name: "url",
              //         value: { url: "/rest/regression-test/test/list" }
              //       }
              //     ],
              //     on: {
              //       click: () => {
              //         this.regression_jump(params.row.uri, "api");
              //       }
              //     }
              //   },
              //   "回归测试"
              // ),
              h(
                "Button",
                {
                  props: {
                    type: "text",
                    size: "small"
                  },
                  directives: [
                    {
                      name: "url",
                      value: { url: "/rest/product/api/batch-delete" }
                    }
                  ],
                  on: {
                    click: () => {
                      this.api_unlink(params.row.value, "api");
                    }
                  }
                },
                "移除"
              )
            ]);
          }
        }
      ],
      // api接口列表数据
      data_ApiInterfaceList: [
        // {
        //     id: '1',
        //     name: ' E账通支付',
        //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
        //     version: '1.0',
        //     status: 'ACTIVE',
        //     createTime: '2018-02-01 10:30:00',
        //     lastModifyTime: '2018-05-01 10:30:00'
        // },
        //
        // {
        //     id: '2',
        //     name: ' 订单查询',
        //     uri: '/rest/v1.0/balance/cash',
        //     version: '1.1',
        //     status: 'FROZEN',
        //     createTime: '2018-02-01 10:30:00',
        //     lastModifyTime: '2018-05-01 10:30:00'
        // },
        // {
        //     id: '3',
        //     name: ' 退款\n' +
        //     '\n',
        //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
        //     version: '1.0',
        //     status: 'DELETE',
        //     createTime: '2018-02-01 10:30:00',
        //     lastModifyTime: '2018-05-01 10:30:00'
        // },
        // {
        //     id: '4',
        //     name: ' E账通支付',
        //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
        //     version: '1.0',
        //     status: 'ACTIVE',
        //     createTime: '2018-02-01 10:30:00',
        //     lastModifyTime: '2018-05-01 10:30:00'
        // },
        // {
        //     id: '5',
        //     name: ' E账通支付',
        //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
        //     version: '1.0',
        //     status: 'ACTIVE',
        //     createTime: '2018-02-01 10:30:00',
        //     lastModifyTime: '2018-05-01 10:30:00'
        // },
        //
        // {
        //     id: '6',
        //     name: ' 订单查询',
        //     uri: '/rest/v1.0/balance/cash',
        //     version: '1.1',
        //     status: 'FROZEN',
        //     createTime: '2018-02-01 10:30:00',
        //     lastModifyTime: '2018-05-01 10:30:00'
        // },
        // {
        //     id: '7',
        //     name: ' 退款\n' +
        //     '\n',
        //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
        //     version: '1.0',
        //     status: 'DELETE',
        //     createTime: '2018-02-01 10:30:00',
        //     lastModifyTime: '2018-05-01 10:30:00'
        // },
        // {
        //     id: '8',
        //     name: ' E账通支付',
        //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
        //     version: '1.0',
        //     status: 'ACTIVE',
        //     createTime: '2018-02-01 10:30:00',
        //     lastModifyTime: '2018-05-01 10:30:00'
        // },
        // {
        //     id: '9',
        //     name: ' E账通支付',
        //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
        //     version: '1.0',
        //     status: 'ACTIVE',
        //     createTime: '2018-02-01 10:30:00',
        //     lastModifyTime: '2018-05-01 10:30:00'
        // },
        //
        // {
        //     id: '10',
        //     name: ' 订单查询',
        //     uri: '/rest/v1.0/balance/cash',
        //     version: '1.1',
        //     status: 'FROZEN',
        //     createTime: '2018-02-01 10:30:00',
        //     lastModifyTime: '2018-05-01 10:30:00'
        // }
      ],
      // api接口总页数
      pageTotalInterface: 10,
      // api接口当前页数
      pageNoInteface: 1,
      // api接口列表
      columns_ApiGroupList: [
        {
          type: "selection",
          width: 60,
          align: "center"
        },
        {
          title: "API分组",
          render: (h, params) => {
            return h("div", [
              h("p", params.row.value),
              h("p", "("+params.row.apiGroupName+")"),
            ]);
          },
          align: "center"
        },

        // {
        //   title: "API分组安全需求",
        //   key: "apiGroup_security",
        //   align: "center",
        //   type: "html"
        // },
        {
          title: "操作",
          align: "center",
          key: "operations",
          width: 150,
          render: (h, params) => {
            return h("div", [
              // h(
              //   "Button",
              //   {
              //     props: {
              //       type: "text",
              //       size: "small"
              //     },
              //     directives: [
              //       {
              //         name: "url",
              //         value: { url: "/rest/regression-test/test/list" }
              //       }
              //     ],
              //     on: {
              //       click: () => {
              //         this.regression_jump(params.row.apiGroupCode, "apiGroup");
              //       }
              //     }
              //   },
              //   "回归测试"
              // ),
              h(
                "Button",
                {
                  props: {
                    type: "text",
                    size: "small"
                  },
                  directives: [
                    {
                      name: "url",
                      value: { url: "/rest/product/api/batch-delete" }
                    }
                  ],
                  on: {
                    click: () => {
                      this.api_unlink(params.row.value, "apiGroup");
                    }
                  }
                },
                "移除"
              )
            ]);
          }
        }
      ],
      // api接口列表数据
      data_ApiGroupList: [
        // {
        //     apiGroupCode: 'test_doc_wy',
        //     apiGroupName: '双因子',
        //     apiGroup_security: 'YOP-HMAC-AES128<br/>YOP-HMAC-AES256<br/>YOP-HMAC-AES512',
        //     description: 'XX子系统',
        //     createTime: '2018-02-01 10:30:00',
        //
        // },
        // {
        //     apiGroupCode: 'duanxin',
        //     apiGroupName: '短信',
        //     apiGroup_security: 'YOP-HMAC-AES128<br/>YOP-HMAC-AES256<br/>YOP-HMAC-AES512',
        //     description: 'sertrstyrtydrtdrtfcggchcbvgh',
        //     createTime: '2018-02-01 10:30:00',
        //
        // }
      ],
      // api接口总页数
      pageTotalGroup: 10,
      // api接口当前页数
      pageNoGroup: 1,
      // 当前服务分组编码
      current_code: "",
      deleteIndex:"全部",
      deleteId: "全部"
      // api列表选中
    };
  },
  methods: {
    setProductCode(val){
        this.productCode = val
        this.get_scene(this.productCode)
    },
    //   添加场景
    plus(){
        this.add_scene_modal_show = true
        this.sceneName = ""
    },
    minus(){
      if(this.deleteId === "全部" || !this.deleteId){
        this.$ypMsg.notice_warning(this,'全部场景不可删除');
        return
      }
      var self = this;
      this.$Modal.confirm({
            title: '删除',
            content: '<p>确定删除'+this.sceneName+'?</p>'
            ,okText: "确定",
            cancelText: "取消",
            onOk: () =>{
                api.yop_product_scene_delete({ id: this.deleteId }).then(response => {
                // let resultTemp = response.data.data.statusList;
                // this.data_status_List = [];
                // let dataTemp = [];
                // for (var i in resultTemp) {
                //   dataTemp.push({
                //     value: resultTemp[i].value,
                //     label: resultTemp[i].desc
                //   });
                // }
                // this.data_status_List = dataTemp;
                if(response.status == "success"){
                  this.$ypMsg.notice_success(this, "删除成功");
                  self.list1.splice(this.deleteIndex, 1)
                  self.$forceUpdate();
                  if(this.deleteId == this.sceneId){
                    this.sceneId = ""
                    this.deleteIndex = -1
                    this.deleteId = ""
                    this.activeClass = -1;
                    this.sceneId = ""
                    this.sceneName = ""
                  }
                  this.search_Interface("",-1)
                }else{
                  this.$ypMsg.notice_warning(this,'删除失败');
                }
              });
            },
            onCancel :() =>{
            }
        });
    },
    sure_model(){
      if(this.sceneName){
          var param = {
            productCode:this.productCode,
            name:this.sceneName
        }
        api.yop_product_scene_create(param).then(
            (response) => {
              console.log(response,"添加场景")
                if(response.status == "success"){
                    this.$ypMsg.notice_success(this,'添加成功');
                    console.log(this.productCode)
                    this.get_scene(this.productCode);
                    this.add_scene_modal_show = false
                }else{
                    this.$ypMsg.notice_warning(this,response.message);
                }
            }
        )
      }else{
          this.$ypMsg.notice_warning(this,'请输入场景名称');
      }
    },
    hide_model(){
        this.add_scene_modal_show = false
    },
    //   获取场景

    get_scene (productCode) {
      console.log("场景", productCode);
      api.yop_product_scene_list({ productCode: productCode })
        .then(response => {
          let resultTemp = response.data.data.result;
          this.list1 = [];
          let dataTemp = [];
          for (var i in resultTemp) {
            dataTemp.push({
              id: resultTemp[i].id,
              name: resultTemp[i].name
            });
          }
          this.list1 = dataTemp;
        });
    },
    sureOrder() {
      var param = [];
      for (var i = 0; i < this.list1.length; i++) {
        if(this.list1[i]){
           param.push({
            id: this.list1[i].id,
            seq: i + 1
          });
        }
       
      }
      console.log(param, "确定排序的传参");

      api.yop_product_scene_order(param).then(response => {
        if(response.status == "success"){
          this.$ypMsg.notice_success(this, "排序成功");
        }else{
          this.$ypMsg.notice_warning(this, "排序失败");
          this.get_scene(this.productCode);
        }
        // let resultTemp = response.data.result;
        // this.list1 = [];
        // let dataTemp = [];
        // for (var i in resultTemp) {
        //   dataTemp.push({
        //     id: resultTemp[i].id,
        //     name: resultTemp[i].name
        //   });
        // }
        // this.list1 = dataTemp;
      });
    },
    hideOrder() {
      this.get_scene(productCode);
    },
    // 拖拽
    // end (ev) {
    // if (ev.to.className === 'dragArea11') {
    //     this.$set(this.list2[ev.oldIndex], 'flag', true)
    // }
    // },
    start22(event) {
      this.falgs = "notarticle";
    },
    end22(ev) {
      this.falgs = "article";
    },
    handleDel(index, id) {
      var self = this;
      this.$Modal.confirm({
            title: '删除',
            content: '<p>确定删除？</p>'
            ,okText: "确定",
            cancelText: "取消",
            onOk: () =>{
                api.yop_product_scene_delete({ id: id }).then(response => {
                // let resultTemp = response.data.data.statusList;
                // this.data_status_List = [];
                // let dataTemp = [];
                // for (var i in resultTemp) {
                //   dataTemp.push({
                //     value: resultTemp[i].value,
                //     label: resultTemp[i].desc
                //   });
                // }
                // this.data_status_List = dataTemp;
                if(response.status == "success"){
                  this.$ypMsg.notice_success(this, "删除成功");
                  if(id == this.sceneId){
                    this.sceneId = ""
                  }
                  self.list1.splice(index, 1)
                  self.$forceUpdate();
                }else{
                  this.$ypMsg.notice_warning(this,'删除失败');
                }
              });
            },
            onCancel :() =>{
            }
        });
      
    },
    // 组件初始化获取
    init() {
      // api状态列表
      api.yop_apiManagement_apiCommonStatusList().then(response => {
        let resultTemp = response.data.data.statusList;
        this.data_status_List = [];
        let dataTemp = [];
        for (var i in resultTemp) {
          dataTemp.push({
            value: resultTemp[i].value,
            label: resultTemp[i].desc
          });
        }
        this.data_status_List = dataTemp;
      });
    },
    // 更新数据
    updateData() {
      this.$emit("on-update", this.select_data_serviceSupplier);
    },
    // 窗口关闭按钮
    preview_cancel() {
      this.model_show = false;
    },
    // 窗口显示按钮
    preview_show() {
      this.model_show = true;
      this.show_loading_preview = true;
      this.tabNow = "api_interface";
    },
    // 获取详细信息
    detail_info_get(value) {
      if(value == "api_group"){
        this.pageRefreshGroup();
      }
      console.log(value,"tab值")
      this.tabNow = value
    },
 
    pageRefreshInteface(val) {
      this.show_loading_interface = true;
      let paramsTemp = {
          productCode: this.productCode,
          type: "API",
          value: this.data_interface_uri,
          sceneId: this.sceneId,
          related:true,
        };
      if (val) {
        paramsTemp._pageNo = val;
      }
      util.paramFormat(paramsTemp);
      console.log(paramsTemp,"添加api")
      api.yop_product_api_list(paramsTemp).then(response => {
        if (response.data.status === "success") {
          this.tableDataFormat_interface(response.data.data.page.items);
          this.pageNoInteface = response.data.data.page.pageNo;
          if (
            response.data.data.page.items &&
            response.data.data.page.items.length > 0
          ) {
            
              this.pageTotalInterface = NaN;
          } else {
            this.pageTotalInterface = NaN;
          }


          this.show_loading_interface = false;
        } else {
          this.$Modal.error({
            title: "API接口加载错误",
            content: response.data.message
          });
          this.data_ApiInterfaceList = [];
          this.show_loading_interface = false;
        }
      });
    },
    // 关联接口数据处理
    tableDataFormat_interface(items) {
      this.data_ApiInterfaceList = [];
      for (var i in items) {
        this.data_ApiInterfaceList.push({
          value:util.empty_handler(items[i].value),
          name: util.empty_handler(items[i].apiName),
          uri: util.empty_handler(items[i].apiUri),
          version: util.empty_handler(items[i].version),
          status: items[i].status,
          createTime: util.empty_handler(items[i].createdDate)
        });
      }
    },
    pageRefreshGroup(val) {
      let paramsTemp = {
        productCode: this.productCode,
        type: "API_GROUP",
        value: this.data_interface_uri,
        sceneId: this.sceneId || "",
        related:true,
      };
      if (val) {
        paramsTemp._pageNo = val;
      }
      util.paramFormat(paramsTemp);
      api.yop_product_api_list(paramsTemp).then(response => {
        if (response.data.status === "success") {
          this.tableDataFormat_group(response.data.data.page.items);
          this.pageNoGroup = response.data.data.page.pageNo;
           if (
            response.data.data.page.items &&
            response.data.data.page.items.length > 0
          ) {
              this.pageTotalGroup = NaN;
          } else {
            this.pageTotalGroup = NaN;
          }
          this.show_loading_group = false;
        } else {
          this.$Modal.error({
            title: "API分组加载错误",
            content: response.data.message
          });
          this.data_ApiGroupList = [];
          this.show_loading_group = false;
        }
      });
    },
    // 关联api分组数据处理
    tableDataFormat_group(items) {
      this.data_ApiGroupList = [];
      for (var i in items) {
        this.data_ApiGroupList.push({
          value:util.empty_handler(items[i].value),
          apiGroupCode: util.empty_handler(items[i].code),
          apiGroupName: util.empty_handler(items[i].name),
          apiGroup_security: this.data_security_handler(items[i].security), //可能是个数组
          description: items[i].description,
          createTime: util.empty_handler(items[i].createdDate)
        });
      }
    },
    // 安全需求处理函数
    data_security_handler(securities) {
      let data_security = "";
      if (securities && securities.length > 0) {
        for (var i in securities) {
          if (i === securities.length) {
            data_security = data_security + securities[i];
          } else {
            data_security = data_security + securities[i] + "<br/>";
          }
        }
        return data_security;
      } else {
        return "";
      }
    },
    // tab初始化
    tab_init() {
      this.tabNow = "api_interface";
    },
    // 基本信息保存
    save_basic(val) {
      this.$refs[val].validate(valid => {
        if (valid) {
          let param = this.param_edit_handler();
          this.resource_edit_handler(param);
        } else {
          this.$Message.error("请检查");
        }
      });
    },
    // 修改请求处理
    resource_edit_handler(param) {
      api.yop_service_group_edit(param).then(response => {
        if (response.status === "success") {
          this.$ypMsg.notice_success(this, "服务分组修改成功");
          // let nameTemp = param.name;
          // this.current_dataTemp.name = nameTemp
          // if(util.getLength(param.name) > 10){
          //     nameTemp = param.name.substring(0,10)+'...'
          // }else{
          //     nameTemp = param.name
          // }
          // this.current_dataTemp.label = nameTemp
          this.$emit("update-name", param.name);
        } else {
          this.$ypMsg.notice_error(
            this,
            "服务分组修改错误",
            response.message,
            response.solution
          );
        }
      });
    },
    // 资源编辑参数处理
    param_edit_handler() {
      let param = {
        code: this.form_detail.serviceGroupCode,
        pCode: this.form_detail.PServiceGroupCode,
        name: this.form_detail.serviceGroupName,
        description: this.form_detail.description,
        level: 2
      };
      return param;
    },
    // 回归测试跳转
    regression_jump(data, type) {
      if (type === "api") {
        // api跳转
        localStorage.rergession_api = data;
        this.$router.push({
          name: "/regression-test/test/list"
        });
      } else {
        // api分组跳转
        localStorage.rergession_apiGroup = data;
        this.$router.push({
          name: "/regression-test/test/list"
        });
      }
    },
    // api接口移除
    api_unlink(data, type) {
      if (type === "api") {
        //api移除
        let param = {
          productCode: this.productCode,
          sceneId: this.sceneId,
          type: "API", //"API"或者"API_GROUP",
          values: [data]
        };
        this.api_group_unlink_handler(param);
      } else {
        //api分组移除
        let param = {
          productCode: this.productCode,
          sceneId: this.sceneId,
          type: "API_GROUP", //"API"或者"API_GROUP",
          values: [data]
        };
        this.api_group_unlink_handler(param);
      }
    },
    // api接口移除执行
    api_group_unlink_handler(param) {
      var title = ""
      console.log(param.type)
      if(param.type == "API"){
        title = "API接口"
      }else{
        title = "API分组"
      }
      var sceneName = this.sceneName || "全部场景"

      this.$Modal.confirm({
          title: "移除"+title,
          content: "<p style='color:red'>如果从（"+sceneName+"）中移除"+title+"，商户无法授权调用该API接口。</p><p>确定移除？</p>",
          'ok-text':'确认',
          onOk: () =>{
              // 删除操作
                    console.log(12121)
              api.yop_product_bath_delete(param).then(response => {
                  if (response.status === "success") {
                    var result = response.data.result
                    if(result.failedValues && result.failedValues.length > 0){
                        this.$Modal.success({
                          title: "部分移除成功",
                          content:
                            "移除失败总数：<span style='color:red'>" +
                            result.failedValues.length +
                            "</span>条<br/>" +
                            "失败<span style='color:red'>前5条</span>如下所示<br/>" +
                            this.failed_reason_handler(result.failedValues), //待修改
                          duration: 150
                        });
                    }else{
                      this.$ypMsg.notice_success(this, "移除成功");
                      this.pageRefreshInteface();
                      this.pageRefreshGroup();
                    }
                    
                  } else {
                    this.$ypMsg.notice_error(
                      this,
                      "移除错误",
                      response.message,
                      response.solution
                    );
                  }
                });
          }
      });
      
    },
    failed_reason_handler(result) {
      let resultTemp = "";
      if (result.length >= 5) {
        for (var i in result) {
          if (i === 5) {
            resultTemp =
              resultTemp +
              (parseInt(i) + 1) +
              ".<span style='color:red'>" +
              result[i].value +
              "</span>: " +
              result[i].message;
          } else if (i < 5) {
            resultTemp =
              resultTemp +
              (parseInt(i) + 1) +
              ".<span style='color:red'>" +
              result[i].value +
              "</span>: " +
              result[i].message +
              "<br/>";
          }
        }
        return resultTemp;
      } else {
        for (var i in result) {
          if (i === result.length - 1) {
            resultTemp =
              resultTemp +
              (parseInt(i) + 1) +
              ".<span style='color:red'>" +
              result[i].value +
              "</span>: " +
              result[i].message;
          } else {
            resultTemp =
              resultTemp +
              (parseInt(i) + 1) +
              ".<span style='color:red'>" +
              result[i].value +
              "</span>:" +
              result[i].message +
              "<br/>";
          }
        }
        return resultTemp;
      }
    },
    // api关联显示函数
    api_link_modal_show() {
      let params  = {
        productCode:this.productCode,
        sceneId:this.sceneId,
        sceneName:this.sceneName,
        related:false,
        type:"API"

      }
      console.log(this.sceneName)
      this.$refs.modal_link_api.show_model();
      this.$refs.modal_link_api.reset_Interface();
      this.$refs.modal_link_api.setInfo(params);
      this.$refs.modal_link_api.search_Interface();
    },
    // api批量移除函数
    batch_unlink_api() {
      let apiUris = this.api_uris_selected_get();
      if (apiUris.length > 0) {
            let param = {
              productCode: this.productCode,
              sceneId: this.sceneId,
              type: "API", //"API"或者"API_GROUP",
              values: this.multiSelectedData_api
            };
            this.api_group_unlink_handler(param);
          }
    },
    // api分组关联显示函数
    apiGroup_link_modal_show() {
      let params  = {
        productCode:this.productCode,
        sceneId:this.sceneId,
        sceneName:this.sceneName,
        related:false,
        type:"API_GROUP"
      }
      console.log(this.sceneName)
      // this.$refs.modal_link_api_group.show_model();
      // this.$refs.modal_link_api_group.reset_Interface();
      // this.$refs.modal_link_api_group.setInfo(params);
      // this.$refs.modal_link_api_group.search_Interface();
    },
    // api分组批量移除函数
    batch_unlink_apiGroup() {
      let apiGroupCodes = this.api_groups_selected_get();
      if (apiGroupCodes.length > 0) {
       
            let param = {
              productCode: this.productCode,
              sceneId: this.sceneId,
              type: "API_GROUP", //"API"或者"API_GROUP",
              values: this.multiSelectedData_apiGroup
            };
            this.api_group_unlink_handler(param);
          }
    },
    // 表格内容改动时
    handleselection1(value) {
      this.multiSelectedData_api = [];
      for(var i in value){
        this.multiSelectedData_api.push(value[i].value)
      }
    },
    // 表格内容改动时
    handleselection2(value) {
      this.multiSelectedData_apiGroup = [];
      for(var j in value){
        this.multiSelectedData_apiGroup.push(value[j].value)
      }
    },
    // 已选择api uri获取
    api_uris_selected_get() {
      console.log(this.multiSelectedData_api)
      if (
        this.multiSelectedData_api == null ||
        this.multiSelectedData_api.length == 0
      ) {
        //还有其他函数判定
        this.$ypMsg.notice_warning(this, "请至少选择一个API");
        return [];
      } else {
        var temp = [];
        this.multiSelectedData_api.forEach(m => {
          temp.push(m.uri);
        });
        return temp;
      }
    },
    // 已选择api分组获取
    api_groups_selected_get() {
      if (
        this.multiSelectedData_apiGroup == null ||
        this.multiSelectedData_apiGroup.length == 0
      ) {
        //还有其他函数判定
        this.$ypMsg.notice_warning(this, "请至少选择一个API分组");
        return [];
      } else {
        var temp = [];
        this.multiSelectedData_apiGroup.forEach(m => {
          temp.push(m.apiGroupCode);
        });
        return temp;
      }
    },
    //
    // apiGroup表格赋值
    tabledataGet(items) {
      this.data_ApiGroupList = [];
      let dataTemp = [];
      for (var i in items) {
        let DateTemp =
          items[i].lastModifiedDate + "<br/>" + items[i].createdDate;
        let securityTemp = [];
        if (items[i].apiSecurities) {
          securityTemp = this.SecurityGenerate(
            "自定义",
            items[i].apiSecurities
          );
        } else {
          securityTemp = this.SecurityGenerate(
            "继承",
            items[i].apiGroupSecurities
          );
        }
        let operationsTemp = false;
        if (items[i].status === "ACTIVE") {
          operationsTemp = true;
        }
        if (items[i].status === "FORBID") {
          operationsTemp = false;
        }
        dataTemp.push({
          value:items[i].value,
          APIid: items[i].apiId,
          interface_Name: items[i].apiTitle,
          interface_URI: items[i].apiUri,
          APItype: items[i].apiType,
          APItypeDesc: items[i].apiTypeDesc,
          APIgroupTitle: items[i].apiGroupTitle,
          interface_Tag: securityTemp,
          Date: DateTemp,
          apiGroup:
            items[i].apiGroupTitle + "<br/>(" + items[i].apiGroupCode + ")",
          APIgroupCode: items[i].apiGroupCode,
          status: items[i].status,
          statusDesc: items[i].statusDesc,
          apiSecurity: items[i].apiSecurity,
          groupSecurity: items[i].groupSecurity,
          createTime: items[i].createdDate,
          lastModifyTime: items[i].lastModifiedDate,
          operations: operationsTemp
        });
        if (items[i].status === "OFFLINE") {
          delete dataTemp[i]["operations"];
        }
      }
      this.data_ApiGroupList = dataTemp;
    },
    SecurityGenerate(title, array) {
      let securityTemp = title + "<br/>";
      for (var i in array) {
        if (i === array.length) {
          securityTemp = securityTemp + array[i];
        } else {
          securityTemp = securityTemp + array[i] + "<br/>";
        }
      }
      return securityTemp;
    },
    search_Interface(ele,index) {
      if(index === 0 || index){
        this.deleteId = ele.id
        this.deleteIndex = index
        console.log(ele,index)
        this.activeClass = index;
        this.sceneId = ele.id || ""
        this.sceneName = ele.name || ""
      }
      var type = ""
      console.log(this.tabNow)
      if(this.tabNow == "api_interface"){
        type = "API"
      }else {
        type = "API_GROUP"
      }
      let paramsTemp = {
        productCode: this.productCode,
        type: type,
        value: this.data_interface_uri,
        sceneId: this.sceneId || "",
        related:true,
        _pageNo:1
       
      };
      util.paramFormat(paramsTemp);
      console.log(paramsTemp,"查询产品-api列表")
      this.current_params = paramsTemp;
      api.yop_product_api_list(paramsTemp).then(response => {
        // this.tabledataGet(response.data.data.page.items);
        if(isNaN(ele)){
          this.pageRefreshInteface();
          this.pageRefreshGroup();
        }else{
          this.pageRefreshInteface(ele);
          this.pageRefreshGroup(ele);
        }
        
        this.pageNo = response.data.data.page.pageNo;
         if (
            response.data.data.page.items &&
            response.data.data.page.items.length > 0
          ) {
              this.pageTotalInterface = NaN;
          } else {
            this.pageTotalInterface = NaN;
          }
      });
    },
    //
    // 重置函数
    reset_Interface() {
      // this.$refs.select_apiM_1.clearSingleSelect();
      this.data_interface_uri = "";
      this.$refs.data_select_status.clearSingleSelect();
      // this.current_status ={
      //     pageNo : 1,
      //     pageSize: 10
      // };
    }
  },
  mounted() {
    this.init();
  }
};
</script>

