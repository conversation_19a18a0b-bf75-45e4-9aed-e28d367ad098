<template>
	<Modal title="确认发布" v-model="show" class-name="vertical-center-modal pulish-model">
    <Alert type="warning" show-icon>请确认信息无误后再进行发布。</Alert>
		<Form class="pulish-form" ref="form" :model="params" label-position="left" :rules="ruleValidate">
			<FormItem label="发布原因：" prop="publishReason">
				<Input
					v-model="params.publishReason"
					id="btn_module_name_input"
					type="textarea"
					placeholder="请输入发布原因"
          :maxlength="100"
          :rows="4"
				/>
			</FormItem>
			<FormItem label="通知人(支持姓名、邮箱前缀等模糊搜索)：">
				<NotifiersSelect v-model="params.notifiers" />
			</FormItem>
		</Form>
		<div class="text-center" slot="footer">
			<Button id="btn_resList_17" type="ghost" @click="cancel">取消</Button>
			<Button :disabled="!params.publishReason" type="primary" @click="confirm" :loading="loading">确认发布</Button>
		</div>
	</Modal>
</template>

<script>
import Api from '~/api/doc/apiClass'
import NotifiersSelect from '../components/NotifiersSelect.vue'
export default {
	components: {
		NotifiersSelect,
	},
	data() {
		return {
			show: false,
			loading: false,
			params: {
				notifiers: [],
				publishReason: '',
				pageId: '',
			},
			ruleValidate: {
				publishReason: [{ required: true, message: '请输入发布原因', trigger: 'change' }],
			},
		}
  },
	methods: {
		showModal(id, pageIds) {
			this.$refs.form.resetFields()
      this.show = true
      this.loading = false
			this.params = {
				notifiers: [],
				pageIds,
				id,
				publishReason: '',
				operator: localStorage.userName,
			}
		},
		confirm() {
			this.$refs.form.validate((valid) => {
				if (valid) {
					this.publish()
				}
			})
		},
    async publish() {
      try {
        this.loading = true
        const res = await Api.checkForPublish(this.params)
        if (res.status !== 'success') {
          this.loading = false
          this.$ypMsg.notice_error(this, '错误', res.message, res.solution)
          return
				}
        let invalidApis = res.data.result.invalidApis;
        if (invalidApis.length > 0) {
          let solution = '';
          invalidApis.forEach(element => {
            solution += `<p>${element}</p>`
          });
          this.loading = false
          this.$ypMsg.notice_error(this, '错误', '请检查以下API的描述信息', solution)
          return
        }
        const resPublish = await Api.publish(this.params)
        if (resPublish.status !== 'success') {
          return Promise.reject(resPublish)
        }
        this.$ypMsg.notice_success(this,'发布成功');
        this.cancel()
        this.loading = false
      } catch (err) {
        this.loading = false
        if (this.$store.state.ifShowCheckFlag) {
          this.show = false
          return
        }
        this.$ypMsg.notice_error(this, '错误', err.message, err.solution)
      }
		},
		cancel() {
			this.show = false
		},
	},
}
</script>
<style scoped lang="less">
.pulish-form {
  .ivu-form-item {
    margin-bottom: 12px;
  }
}
</style>
<style lang="less">
.pulish-model {
  .ivu-modal-body {
    padding: 24px 24px 16px;
  }
}
</style>
