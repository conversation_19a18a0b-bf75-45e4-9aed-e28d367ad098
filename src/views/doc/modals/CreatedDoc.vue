<template>
	<Modal :title="title" v-model="show" @on-cancel="cancel" class-name="vertical-center-modal" width="800">
		<div style="padding: 0 40px 0 20px">
			<Form ref="form" :model="params" label-position="right" :label-width="120" :rules="ruleValidate">
				<FormItem label="文档类型：" prop="type" key="type">
					<Select id="btn_module_type_select" v-model="params.type" style="width:100%" :disabled="isEdit">
							<Option v-for="item in docTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
					</Select>
				</FormItem>
        <template v-if="params.type === 'SOLUTION'">
          <FormItem label="自定义解决方案：" prop="solutionCode" key="1">
            <Input v-if="isEdit" v-model="solutionCodeStr" disabled></Input>
            <template v-else>
              <common-select-single
                v-model="params.solutionCode"
                :default="customSolutionListForDoc"
                id="select_acl_doc_single"
                ref="select_sp_single"
                @on-update="updateSelect_custom"
                @on-update-name="updateSelect_custom_name"
                type="combo"
                keyWord="result"
                holder="请选择自定义解决方案"
                code="code"
                title="name"
                group="customSolutionListForDoc"
                @on-loaded="select_callBack_custom"
                :uri="this.$store.state.select.customSolutionListForDoc.uri">
              </common-select-single>
            </template>
          </FormItem>
          <FormItem label="文档名称：">
            <Input
              v-model="params.title"
              id="btn_module_title_input"
              :maxlength="30"
              placeholder="请输入文档名称"
              disabled
            />
            <P class="form-desc">根据自定义解决方案自动填入，不支持修改</P>
          </FormItem>
          <FormItem label="服务提供方：" prop="spCodes" key="2">
            <p>
              <span v-for="(item, index) in noAuthSpCodes" :key="item">{{item}}
                  <span v-if="index !== noAuthSpCodes.length - 1" >、</span>
              </span>
            </p>
            <common-select
              id="select_acl_doc"
              ref="select_sp"
              @on-update="updateSelect_sp"
              type="combo"
              keyWord="result"
              holder="请选择服务提供方"
              code="spCode"
              title="spName"
              group="serviceSupplier"
              :default="params.spCodes"
              :uri="this.$store.state.select.serviceSupplier.uri">
            </common-select>
          </FormItem>
          <FormItem label="文档编码：" prop="docNo"  key="3">
            <Input
              v-model="params.docNo"
              id="btn_module_docNo_input"
              :maxlength="30"
              disabled
              placeholder="请输入文档编码"
            />
            <P class="form-desc">根据自定义解决方案自动填入，不支持修改</P>
          </FormItem>
        </template>
				<template v-else>
          <FormItem key="productCodes" label="产品编码：" prop="4" v-if="params.type === 'PRODUCT'">
            <p>
              <span v-for="(item, index) in noAuthProductCodesList" :key="item">{{item}}
                  <span v-if="index !== noAuthProductCodesList.length - 1" >、</span>
              </span>
            </p>
            <Select id="btn_module_productCodes_select" v-model="params.productCodes" multiple style="width:100%" filterable @on-change="onProductCodesChange">
              <Option v-for="item in productCodesList" :value="item.productCode" :key="item.productCode">{{ item.productName }}({{ item.productCode }})</Option>
            </Select>
            <Spin fix v-if="productCodesShow">加载中...</Spin>
          </FormItem>
          <FormItem label="服务提供方：" prop="spCodes" key="5">
            <p>
              <span v-for="(item, index) in noAuthSpCodes" :key="item">{{item}}
                  <span v-if="index !== noAuthSpCodes.length - 1" >、</span>
              </span>
            </p>
            <common-select
              id="select_acl_doc"
              ref="select_sp"
              @on-update="updateSelect_sp"
              type="combo"
              keyWord="result"
              holder="请选择服务提供方"
              code="spCode"
              title="spName"
              group="serviceSupplier"
              :default="params.spCodes"
              :uri="this.$store.state.select.serviceSupplier.uri">
            </common-select>
          </FormItem>
          <FormItem label="文档名称：" prop="title" key="6">
            <Input
              v-model="params.title"
              id="btn_module_title_input"
              :maxlength="30"
              placeholder="请输入文档名称"
            />
            <P class="form-desc">将作为产品名称展示到前台，最长支持输入30个字符</P>
          </FormItem>
          <FormItem label="文档编码：" prop="docNo"  key="7">
            <Input
              v-model="params.docNo"
              :disabled="isEdit"
              id="btn_module_docNo_input"
              :maxlength="30"
              placeholder="请输入文档编码"
            />
            <P class="form-desc">将作为访问文档URL的一部分；仅支持小写字母、数字、“-”, 最长输入30个字符；创建后不支持修改；</P>
          </FormItem>
          <FormItem key="8" label="文档分类：" prop="categoryIds" v-if="params.type === 'PRODUCT'">
            <DocCategorySelect v-if="lastCode !== 'DOCKING'" ref="DocCategorySelect" v-model="params.categoryIds[0]" />
            <div v-else>{{ lastCodeLabel }}</div>
          </FormItem>
          <FormItem label="描述：" key="9">
            <Input
              v-model="params.desc"
              id="btn_module_desc_input"
              :maxlength="300"
              type="textarea"
              placeholder="请输入描述"
            />
            <P class="form-desc">最长支持输入300个字符</P>
          </FormItem>
        </template>
			</Form>
		</div>
		<div class="text-center" slot="footer">
      <Button id="btn_createdDoc_cancel" type="ghost" @click="cancel">取消</Button>
			<Button id="btn_createdDoc_confirm" type="primary" @click="confirm" :loading="confirmLoading">确认</Button>
		</div>
      <loading :show="showLoading"></loading>
	</Modal>
</template>

<script>
import loading from '../../my-components/loading/loading';
import commonSelect from '../../common-components/select-components/selectCommon_multiple';
import commonSelectSingle from '../../common-components/select-components/selectCommon';
import Api from '~/api/doc/createdDoc';
import DocCategorySelect from './DocCategorySelect'
import { debounce } from 'throttle-debounce'
import {v4 as uuidv4} from 'uuid'
export default {
  components: {
    DocCategorySelect,
    commonSelect,
    commonSelectSingle,
    loading
  },
  data () {
    const checkProductCodes = (rule, value, callback) => {
      if (value.length < 1 && this.noAuthProductCodesList.length < 1) {
        callback(new Error('请选择产品编码'))
      }
      callback()
    }
    const checkSpCodes = (rule, value, callback) => {
      if (value.length < 1 && this.noAuthSpCodes.length < 1) {
        callback(new Error('请选择服务提供方'))
      }
      callback()
    }
    const checkCategoryIds = (rule, value, callback) => {
      if (value[0].length < 2) {
        callback(new Error('请选择文档分类'))
      }
      if (!value[0][0] || !value[0][1]) {
        callback(new Error('请选择文档分类'))
      }
      callback()
    }
    const checkDocNo = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入文档编码'))
      }
      if (/[^0-9a-z-]/.test(value)) {
        callback(new Error('输入的文档编码格式有误，请重新输入'))
      }
      if (!this.isEdit) {
        Api.checkDocNo({
          docNo: value
        }).then(
          (response) => {
            if (response.data.status === 'success') {
              let result = response.data.data.result
              if (result) {
                callback(new Error('该编码已存在'));
              } else {
                callback();
              }
            } else {
              callback(new Error('验重失败'))
            }
          }
        )
      } else {
        callback();
      }
    }
    return {
      show: false,
      isEdit: false,
      confirmLoading: false,
      showLoading: false,
      productCodesShow: false,
      firstRenderSpCode: false,
      firstCustomSolutionCode: false,
      title: '创建文档',
      productCodesList: [],
      noAuthProductCodesList: [],
      totalProductCodes: [],
      docTypeList: [
        {
          value: 'OPEN',
          label: '普通文档'
        },
        {
          value: 'PRODUCT',
          label: '产品文档（产品+API+SPI）'
        },
        {
          value: 'SOLUTION',
          label: '自定义解决方案'
        }
      ],
      cityList: [],
      ruleValidate: {
        productCodes: [
          { required: true, validator: checkProductCodes, trigger: 'change' }
        ],
        spCodes: [
          { required: true, validator: checkSpCodes, trigger: 'change' }
        ],
        title: [
          { required: true, message: '请输入文档名称', trigger: 'change' }
        ],
        docNo: [
          { required: true, validator: checkDocNo, trigger: 'blur' }
        ],
        categoryIds: [
          { required: true, validator: checkCategoryIds, trigger: 'change' }
        ],
        solutionCode: [
          { required: true, message: '请选择自定义解决方案', trigger: 'change' }
        ]
      },
      noAuthSpCodes: [],
      totalSpCodes: [],
      totalCustomSolutions: [],
      showDuiJie: false,
      categoryName: '',
      params: {
        type: 'PRODUCT',
        productCodes: [],
        spCodes: [],
        solutionCode: '',
        title: '',
        docNo: '',
        categoryIds: [[]],
        desc: ''
      },
      customSolutionListForDoc: '',
      solutionCodeStr: '',
      lastCode: '',
      lastCodeLabel: '',
    };
  },
  created () {
    this.$watch('params.docNo', debounce(200, () => {
      if (!this.isEdit) {
        this.params.docNo = this.params.docNo.replace(/[^a-z\d-]/g, '')
        this.params.docNo = this.params.docNo.substring(0, 30)
      }
    }))
  },
  methods: {
    // 服务提供方数据更新
    updateSelect_sp (val) {
      if (this.firstRenderSpCode && this.totalSpCodes.length > 0) {
        this.totalSpCodes.forEach(code => {
          if (!val.find(val => val === code)) {
            this.noAuthSpCodes.push(code)
          }
        })
        this.firstRenderSpCode = false
      }
      this.params.spCodes = val;
    },
    updateSelect_custom (val) {
      this.customSolutionListForDoc = val
      this.params.solutionCode = val;
      this.params.docNo = val;
    },
    updateSelect_custom_name (val) {
      this.params.title = val
    },
    select_callBack_custom (resultTemp) {
      if (resultTemp && resultTemp.type === 'SOLUTION' && resultTemp.docNo) {
        this.params.solutionCode = resultTemp.docNo;
        this.customSolutionListForDoc = resultTemp.docNo;
        if (this.isEdit) {
          this.solutionCodeStr = `${resultTemp.docNo}(${resultTemp.title})`
        }
      }
    },
    onProductCodesChange (value) {
      if (value.length > 0 && !this.isEdit) {
        this.params.docNo = value[0].replace(/_/g, '-').toLowerCase()
        this.$refs.form.validateField('docNo')
      }
      if (value.length > 0) {
        // 对接产品码查询
        const item = this.productCodesList.find(product => product.productCode === value[0])
        if (item.productType === 'DOCKING') {
          if (!this.lastCode) {
            this.findCategory(value[0])
            this.lastCode = 'DOCKING'
          } 
          return
        } 
      }
      if (this.lastCode === 'DOCKING') {
        this.params.categoryIds = [[]]
        this.lastCode = ''
        this.lastCodeLabel = ''
        this.$refs.DocCategorySelect && this.$refs.DocCategorySelect.resetValue()
      }
    },
    findCategory (productCode) {
      Api.findCategory({ productCode })
        .then(res => {
          if (res.data.status === 'success') {
            const { id, pid, categoryName, categoryCode, pCategoryName } = res.data.data.result
            this.lastCodeLabel = pCategoryName
            if (categoryCode !== '_DEFAULT') {
              this.lastCodeLabel = `${pCategoryName}-${categoryName}`
            }
            this.params.categoryIds = [[pid, id]]
            this.$refs.form.validateField('categoryIds')
          } else {
            this.$ypMsg.notice_error(this, '错误', response.data.message);
          }
        })
    },
    getProductCodesList () {
      this.productCodesShow = true
      Api.getProductCodesList()
        .then(response => {
          if (response.data.status === 'success') {
            this.productCodesList = response.data.data.result
            // 权限分组，仅有权限的可以编辑
            if (this.totalProductCodes.length > 0) {
              this.totalProductCodes.forEach(item => {
                if (this.productCodesList.find(product => product.productCode === item)) {
                  this.params.productCodes.push(item)
                } else {
                  this.noAuthProductCodesList.push(item)
                }
              })
            }
          } else {
            this.$ypMsg.notice_error(this, '错误', response.data.message);
          }
        })
        .finally(() => {
          this.productCodesShow = false
        })
    },
    initForm () {
      this.noAuthProductCodesList = []
      this.totalProductCodes = []
      this.noAuthSpCodes = []
      this.totalSpCodes = []
      this.$refs.form.resetFields();
      this.firstRenderSpCode = false
      this.params = {
        type: 'PRODUCT',
        productCodes: [],
        spCodes: [],
        title: '',
        docNo: '',
        solutionCode: '',
        categoryIds: [[]],
        desc: ''
      };
    },
    showAddModal () {
      this.getProductCodesList()
      this.isEdit = false;
      this.showLoading = false;
      this.title = '创建文档';
      this.show = true;
      this.initForm()
    },
    showEditModal (data) {
      this.title = '设置文档属性';
      this.isEdit = true;
      this.show = true;
      this.initForm()
      this.getDocDetail(data.id)
    },
    confirm () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            this.editDoc();
          } else {
            this.createDoc();
          }
        }
      });
    },
    createDoc () {
      this.confirmLoading = true
      Api.createDoc(this.params)
        .then(response => {
          if (response.status === 'success') {
            this.$emit('refreshList')
            this.cancel();
          } else {
            this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    editDoc () {
      this.confirmLoading = true
      const params = JSON.parse(JSON.stringify(this.params))
      params.productCodes = params.productCodes.concat(this.noAuthProductCodesList)
      params.spCodes = params.spCodes.concat(this.noAuthSpCodes)
      Api.editDoc(params)
        .then(response => {
          if (response.status === 'success') {
            this.$emit('refreshList')
            this.cancel();
          } else {
            this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    getDocDetail (id) {
      this.showLoading = true
      Api.getDocDetail({id})
        .then(response => {
          console.log(this.isEdit)
          if (!this.isEdit) return
          if (response.data.status === 'success') {
            this.params = this.handelParams(response.data.data.result)
            this.$refs.DocCategorySelect && this.$refs.DocCategorySelect.setValue(this.params.categoryIds && this.params.categoryIds.length && this.params.categoryIds[0])
            this.select_callBack_custom(this.params)
            this.getProductCodesList()
          } else {
            this.$ypMsg.notice_error(this, '错误', response.data.message);
          }
        })
        .finally(() => {
          this.showLoading = false
        })
    },
    createDocCode () {
      this.params.docNo = uuidv4().substring(0, 30)
    },
    handelParams (obj) {
      const params = {
        type: obj.type,
        title: obj.title,
        desc: obj.desc,
        docNo: obj.docNo,
        id: obj.id,
        productCodes: [],
        spCodes: obj.spCodes,
        categoryIds: obj.categoryIds
      }
      this.totalProductCodes = obj.productCodes
      this.totalSpCodes = obj.spCodes
      this.firstRenderSpCode = true
      return params
    },
    cancel () {
      this.show = false;
      this.$refs.DocCategorySelect && this.$refs.DocCategorySelect.resetValue()
      this.$nextTick(() => {
        this.$refs.form.resetFields();
      })
    }
  }
};
</script>

<style lang="less" scoped>
.form-desc {
	color: #999;
}
</style>
<style lang="less">
.ivu-form-item-error {
   .form-desc {
       display:  none;
    }
}
</style>
