<template>
	<el-select
		style="width:100%;"
		size="mini"
		:value="value"
		@change="change"
		filterable
		remote
		clearable
		multiple
		placeholder="建议：请选择通知人(可多选)"
		:remote-method="getRemoteList"
		:loading="loading"
	>
		<el-option
			v-for="item in options"
			:key="item.id"
			:label="item.name"
			:value="item.id"
		>
		</el-option>
	</el-select>
</template>
<script>
import apiClass from '~/api/doc/apiClass'
export default {
	props: ['value'],
	data() {
		return {
			loading: false,
			options: [],
		}
	},
	methods: {
		getRemoteList(keyword) {
			if (!keyword) return
			apiClass
				.searchPageOwners({
					keyword,
				})
				.then((res) => {
					this.options = res.data.data.result
				}).catch(err => {
          this.$ypMsg.notice_error(this, '错误', err.message, err.solution)
        })
    },
    change(vals) {
      this.$emit('input', vals)
		},
	},
}
</script>
