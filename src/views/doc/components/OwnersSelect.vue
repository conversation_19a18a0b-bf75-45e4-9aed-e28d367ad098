<template>
	<el-select
		style="width:100%;"
		size="mini"
		:value="ownerIds"
		@change="change"
		filterable
		remote
		clearable
		multiple
		placeholder="请选择页面负责人"
		:remote-method="getRemoteList"
		:loading="loading"
	>
		<el-option
			v-for="item in options"
			:key="item.id"
			:label="item.name"
			:value="item.id"
		>
		</el-option>
	</el-select>
</template>
<script>
import apiClass from '~/api/doc/apiClass'
export default {
	props: ['pageId'],
	data() {
		return {
			loading: false,
			ownerIds: [],
			options: [],
			defaultOptions: [],
		}
	},
	watch: {
		pageId() {
			this.getList()
		},
	},
	mounted() {
		this.getList()
	},
	methods: {
    hasOwner() {
      return this.ownerIds.length > 0
    },
		getList() {
			if (!this.pageId) return
			apiClass
				.getPageOwners({
					pageId: this.pageId,
				})
				.then((res) => {
					this.options = res.data.data.result
          this.defaultOptions = res.data.data.result
          this.ownerIds = this.options.map(o => o.id)
				}).catch(err => {
          this.$ypMsg.notice_error(this, '错误', err.message, err.solution)
        })
		},
		getRemoteList(keyword) {
			if (!keyword) return
			apiClass
				.searchPageOwners({
					keyword,
				})
				.then((res) => {
					this.options = res.data.data.result
				}).catch(err => {
          this.$ypMsg.notice_error(this, '错误', err.message, err.solution)
        })
    },
    getOwnerNames(vals) {
      return vals.map(v => {
        const item = this.defaultOptions.find(d => d.id === v)
        if (item) {
          return item.name
        }
        const item2 = this.options.find(d => d.id === v)
        if (item2) {
          return item2.name
        }
      })
    },
    change(vals) {
      if (vals.length < 1) {
        this.ownerIds = []
        return
      }
			apiClass
				.updatePageOwners({
					ownerIds: vals,
					ownerNames: this.getOwnerNames(vals),
					pageId: this.pageId,
				})
        .then(() => {
          this.getList()
        }).catch(err => {
          this.$ypMsg.notice_error(this, '错误', err.message, err.solution)
        })
		},
	},
}
</script>
