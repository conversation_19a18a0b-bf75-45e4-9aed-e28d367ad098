<style lang="less">
@import "../../styles/common.less";
@import "../API_Management_Open/api_Mangement_Open.less";
.demo-badge-alone {
  background: #5cb85c !important;
}
.round {
  width: 16px;
  height: 16px;
  display: inline-block;
  font-size: 20px;
  line-height: 16px;
  text-align: center;
  color: #f00;
  text-decoration: none;
}

.reasonType {
    vertical-align: top;
    display: inline-block;
    font-size: 12px;
    margin-top: 5px;
}
.ivu-modal-header ,.ivu-modal-footer{
}
.ivu-icon-help-circled:before{
  content:"\F142";
}
</style>
<template>
  <div style="background: #eee;padding:8px">
    <Card :bordered="false">
      <Row type="flex" align="middle">
        <Col span="20">
          <Col span="7">
            <Col span="8" class="margin-top-10">
              <span>文档名称:</span>
            </Col>
            <Col span="16">
              <Input
                id="inputDocName"
                class="margin-top-5"
                v-model="data_doc_name"
                placeholder="文档名称"
                clearable
                @on-enter="search_interface(false)"
              ></Input>
            </Col>
          </Col>
          <Col offset="1" span="7">
            <Col span="8" class="margin-top-10">
              <span>文档编码:</span>
            </Col>
            <Col span="16">
              <Input
                id="inputDocCode"
                class="margin-top-5"
                v-model="data_doc_code"
                placeholder="文档编码"
                clearable
                @on-enter="search_interface(false)"
              ></Input>
            </Col>
          </Col>
          <Col offset="1"  span="7">
            <Col span="8" class="margin-top-10">
              <span>产品编码:</span>
            </Col>
            <Col span="16">
              <Input
                clearable
                id="inputProductCode"
                class="margin-top-5"
                v-model="data_product_code"
                placeholder="产品编码"
                @on-enter="search_interface(false)"
              ></Input>
            </Col>
          </Col>

          <Col span="7">
            <Col span="8" class="margin-top-10">
              <span>文档可见性:</span>
            </Col>
            <Col span="16">
            <Select id='selectDocVisible' ref="doc_visible" class="margin-top-5" v-model="data_select_visible"  @on-change="changeVisible" placeholder="请选择（默认全部）" clearable>
                  <Option v-for="item in data_select_visible_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </Col>
          </Col>

          <Col class="margin-top-5"  offset="1"  span="7">
            <Col span="8" class="margin-top-10">
              <span>文档状态:</span>
            </Col>
            <Col span="16">
            <Select id='selectDocStatus' ref="doc_status" class="margin-top-5" v-model="data_select_status" @on-change="changeStatus"  placeholder="请选择（默认全部）" clearable>
                  <Option v-for="item in data_select_status_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
              </Select>
            </Col>
          </Col>
          <Col class="margin-top-5"  offset="1"  span="7">
            <Col span="8" class="margin-top-10">
            <span>服务提供方:</span>
            </Col>
            <Col span="16">
            <common-select
              id="select_acl_doc"
              ref="select_sp"
              @on-update="updateSelect_sp"
              type="combo"
              keyWord="result"
              holder="请选择（默认全部）"
              code="spCode"
              title="spName"
              group="serviceSupplier"
              @on-loaded="select_callBack"
              :default="this.spCode"
              :uri="this.$store.state.select.serviceSupplier.uri">
            </common-select>
            </Col>
          </Col>

        </Col>

        <Col span="4">
          <Col class="margin-top-10" span="11" style="text-align:center">
            <Button id="btnDocSearch" type="primary" @click="search_interface(false)">查询</Button>
          </Col>
          <Col class="margin-top-10" span="11" style="text-align:center">
            <Button id="btnDocReset" type="ghost" @click="reset_Interface">重置</Button>
          </Col>
        </Col>
      </Row>
      <Row class="margin-top-20">
        <Col span="24">
          <Button class="margin-right-10" id="btnCreatedDoc" type="primary" v-url="{url:'/rest/doc/create'}"  @click="created_doc">创建文档</Button>
          <Button class="margin-right-10" id="btnSetDocOrder" type="primary" v-url="{url:'/rest/doc/list-for-arrange'}"  @click="set_order">设置产品文档排序</Button><!--  -->
          <Button class="margin-right-10" id="btnDocPublicList" style="background:#20cb9a;border:0;" type="primary" v-url="{url:'/rest/doc/publish/list'}"  @click="product_change_record">文档发布记录</Button><!-- -->

        </Col>
      </Row>
      <Row class="margin-top-10">
        <Col span="24">
          <Table
            id="table_1"
            border
            :columns="columns_productCodeList"
            :data="data_productCodeList"
          ></Table>
          <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
            <Page
              class="margin-top-10"
              style="float: right"
              :total="pageTotal"
              :page-size="10"
              :current="pageNo"
              show-elevator
              @on-change="pageRefresh"
            ></Page>
          </Tooltip>
        </Col>
        <loading :show="show_loading_serviceAuthList"></loading>
      </Row>

      <set-doc-params @search_interface="search_interface" ref="set_doc_params"></set-doc-params>
      <set-doc-order ref="setOrder"></set-doc-order>
      <modal-change-record ref="change_record"></modal-change-record>
    </Card>
    <CreatedDoc ref="CreatedDoc" @refreshList="search_interface" />
    <PublishAll ref="publishAll" />
  </div>
</template>

<script>
import loading from '../my-components/loading/loading';
import commonSelect from '../common-components/select-components/selectCommon';
import util from '../../libs/util';
import api from '../../api/api';
import setDocOrder from './modals/set-doc-order';// 设置文档顺序
import modalChangeRecord from './modals/modal-change-record'// 发布记录
import setDocParams from './modals/set-doc-params';
import CreatedDoc from './modals/CreatedDoc'
import PublishAll from './modals/PublishAll'

export default {
  name: 'doc-list',
  components: {
    loading,
    commonSelect,
    setDocParams,
    setDocOrder,
    modalChangeRecord,
    CreatedDoc,
    PublishAll
  },
  data () {
    return {
      //  * 确认弹窗部分
      // 确认弹窗标题
      modal_confirm_title: '',
      // 确认弹窗红色提示部分
      modal_confirm_warning_red: '',
      // 确认弹窗黑色提示部分
      modal_confirm_warning_black: '',
      // 确认弹窗必填原因规范提示
      modal_confirm_reason_fail: '',
      // 确认弹窗原因描述
      modal_confirm_desc: '',
      // 确认弹窗原因长度限制
      modal_confirm_reason_length: 100,
      // 确认弹窗原因是否展示
      modal_confirm_reason_show: true,
      // new
      product_code: '',
      cause: '',
      deleteProduct: false,
      normalProduct: false,
      offlineProduct: false,
      delete_or_normal_modal_show: false,
      serviceSupplier: '',
      productType: '',
      // 新增产品
      modal_add_product: false,
      // 是否是编辑
      create_orEdit: true,
      form_detail: {
      },
      rule_detail: {
      },
      data_select_visible_List: [
        {label: '公开', value: 'PUBLIC'},
        // {label:"私有",value:"PRIVATE"},
        {label: '授权可见', value: 'PROTECTED'}
      ],
      data_select_status_List: [
        {label: '草稿', value: 'DRAFT'},
        {label: '编辑中', value: 'EDIT'},
        {label: '已发布', value: 'PUBLISHED'},
        {label: '已删除', value: 'DELETED'}
      ],
      // 是否是顶级资源
      top_resource: false,
      // 授权类型列表
      type_list: [],
      // 列表loading
      show_loading_serviceAuthList: false,
      // 产品编码输入框数据绑定
      data_product_code: '',
      data_product_name: '',
      data_doc_name: '',
      data_doc_code: '',
      data_product_apiUri: '',
      data_product_type: '',
      service_provision_code: '',
      spCode: '',
      // 授权状态下拉框数据绑定
      data_select_status: '',
      // 授权有效期数据绑定
      data_select_type: '',
      data_select_visible: '',
      data_select_provider: '',
      // 下拉框个数
      count_select_related: 2,
      // 总数
      pageTotal: 10,
      // 当前页码
      pageNo: 1,
      // 文档列表表头
      columns_productCodeList: [
        {
          renderHeader: (h, params) => {
            return h('div', [h('p', '文档名称'), h('p', '文档编码')]);
          },
          render: (h, params) => {
            return h('div', [
              h('p', params.row.title),
              h('p', '(' + params.row.docNo + ')')
            ]);
          },
          align: 'center'
        },
        {
          renderHeader: (h, params) => {
            return h('div', [h('p', '产品名称'), h('p', '产品编码')]);
          },
          render: (h, params) => {
            if (params.row.type === 'PRODUCT') {
              var html = []
              for (var i = 0; i < params.row.products.length; i++) {
                html.push(h('p', params.row.products[i].productName), h('p', '(' + params.row.products[i].productCode + ')'))
              }
              return h('div', html);
            }
            if (params.row.type === 'PLATFORM' || params.row.type === 'OPEN' || params.row.type === 'SOLUTION') {
              return h('div', '--');
            }
          },
          align: 'center'
        },
        {
          title: '服务提供方',
          render: (h, params) => {
            const html = []
            const spInfos = params.row.spInfos || []
            if (spInfos.length < 1) {
              return h('div', '--')
            }
            for (var i = 0; i < spInfos.length; i++) {
              html.push(h('p', `${spInfos[i].spName}(${spInfos[i].spCode})`))
            }
            return h('div', html);
          },
          align: 'center'
        },
        {
          title: '文档类型',
          render: (h, params) => {
            var type = ''
            if (params.row.type === 'PLATFORM') {
              type = '平台文档'
            }
            if (params.row.type === 'OPEN') {
              type = '普通文档'
            }
            if (params.row.type === 'PRODUCT') {
              type = '产品文档'
            }
            if (params.row.type === 'SOLUTION') {
              type = '自定义解决方案'
            }
            return h('div', [
              h('p', type)
            ]);
          },
          align: 'center'
        },
        {
          title: '文档状态',
          render: (h, params) => {
            let color = 'red';
            let status = '已删除';
            if (params.row.status === 'PUBLISHED') {
              color = 'green';
              status = '已发布';
            } else if (params.row.status === 'DRAFT') {
              color = 'grey';
              status = '草稿';
            } else if (params.row.status === 'EDIT') {
              color = 'grey';
              status = '编辑中';
            }
            return h('div', [
              h(
                'Tag',
                {
                  style: {
                    align: 'center'
                  },
                  props: {
                    color: color
                  }
                },
                status
              )
            ]);
          },
          width: 120,
          align: 'center'
        },
        {
          renderHeader: (h, params) => {
            return h('div', [h('p', '创建时间'), h('p', '最后修改时间')]);
          },
          width: 180,
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('p', params.row.createdDate),
              h('p', params.row.lastModifiedDate)
            ]);
          }
        },
        {
          title: '操作',
          align: 'center',
          key: 'operations',
          width: 200,
          render: (h, params) => {
            if (!params.row.owner) {
              return h('span', '--')
            }
            return h('div', [
              h(
                'Button',
                {
                  class: 'btnEditDoc',
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  directives: [{
                    name: 'url',
                    value: {url: '/rest/doc/tree'}
                  }],
                  domProps: { // 添加标签,使用自己引入的iconfont图标
                    innerHTML: params.row.apiChanged ? '<p>编辑</p><p style=\'color:red\'>NEW</p>' : '<p>编辑</p>'
                  },
                  on: {
                    click: () => {
                      this.editDoc(params.row);
                    }
                  }
                }
              ),
              h(
                'Button',
                {
                  class: 'btnDocPublish',
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  directives: [{
                    name: 'url',
                    value: {url: '/rest/doc/publish'}
                  }],
                  on: {
                    click: () => {
                      this.$refs.publishAll.showModal(params.row.id, params.row.docNo)
                    }
                  }
                },
                '发布'
              ),
              h(
                'Button',
                {
                  class: 'btnSetDocParams',
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  directives: [{
                    name: 'url',
                    value: {url: '/rest/doc/settings/detail'}
                  }],
                  on: {
                    click: () => {
                      this.set_product_documentation(params.row);
                    }
                  }
                },
                '设置'
              ),

              h(
                'Button',
                {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  directives: [{
                    name: 'show',
                    value: false
                  }],
                  on: {
                    click: () => {
                      this.delete_doc(params.row.code);
                    }
                  }
                },
                '删除'
              ),

              h(
                'Button',
                {
                  class: 'btnDocPublishList',
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  directives: [{
                    name: 'url',
                    value: {url: '/rest/doc/publish/list'}
                  }],
                  on: {
                    click: () => {
                      this.product_change_record(params.row);
                    }
                  }
                },
                '发布记录'
              ),
              // 查看
              h(
                'Button',
                {
                  class: 'btnDocPublishList',
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  on: {
                    click: () => {
                      this.goSeeDoc(params.row);
                    }
                  }
                },
                '查看'
              )

            ]);
          }
        }
      ],

      // 服务授权数据
      data_productCodeList: [],
      // 开始日期绑定
      dateStart: '',
      // 结束日期绑定
      dateEnd: '',
      data_interface_uri: '',
      search_Interface: '',
      productCode: ''
    };
  },
  methods: {
    // 服务提供方数据更新
    updateSelect_sp (val) {
      this.spCode = val;
    },
    created_doc () {
      this.$refs.CreatedDoc.showAddModal()
    },
    changeVisible (value) {
      this.data_select_visible = value
    },
    changeStatus (value) {
      this.data_select_status = value
    },
    // 产品列表查询函数
    search_interface (val) {
      //   this.show_loading_serviceAuthList = true;
      let params = {
        productCode: this.data_product_code.trim(),
        title: this.data_doc_name.trim(),
        docNo: this.data_doc_code.trim(),
        spCode: this.spCode ? this.spCode.trim() : '',
        visible: this.data_select_visible || '', // PUBLIC/PRIVATE/PROTECTED
        status: this.data_select_status || '',
        _pageNo: 1
        // _pageSize: 10
      };
      if (val) {
        params._pageNo = val;
      }
      util.paramFormat(params);
      api.yop_doc_list(params).then(response => {
        if (response.data.status === 'success') {
          this.tableDataFormat(response.data.data.page.items);
          this.pageNo = response.data.data.page.pageNo;
          if (
            response.data.data.page.items &&
            response.data.data.page.items.length > 0
          ) {
            if (response.data.data.page.items.length < 10) {
              this.pageTotal = response.data.data.page.items.length;
            } else {
              this.pageTotal = NaN;
            }
          } else {
            this.pageTotal = NaN;
          }
        } else {
          this.$ypMsg.notice_error(
            this,
            '列表获取错误',
            response.data.message,
            response.data.solution
          );
        }
        this.show_loading_serviceAuthList = false;
      });
    },
    // 列表数据处理
    tableDataFormat (items) {
      this.data_productCodeList = [];
      for (var i in items) {
        this.data_productCodeList.push({
          id: util.empty_handler2(items[i].id),
          products: util.empty_handler2(items[i].products),
          spInfos: util.empty_handler2(items[i].spInfos),
          docNo: util.empty_handler(items[i].docNo),
          title: util.empty_handler2(items[i].title),
          apiChanged: util.empty_handler2(items[i].apiChanged),
          docUrl: util.empty_handler2(items[i].docUrl),
          docPreviewUrl: util.empty_handler2(items[i].docPreviewUrl),
          type: util.empty_handler2(items[i].type),
          owner: util.empty_handler2(items[i].owner),
          visible: util.empty_handler2(items[i].visible),
          status: util.empty_handler2(items[i].status),
          display: util.empty_handler2(items[i].display),
          version: util.empty_handler2(items[i].version),
          createdDate: util.empty_handler2(items[i].createdDate),
          lastModifiedDate: util.empty_handler(items[i].lastModifiedDate)
        });
      }
    },
    // 重置查询添加函数
    reset_Interface () {
      this.data_product_code = '';
      this.data_doc_name = '';
      this.data_doc_code = '';
      this.data_select_visible = '';
      this.data_select_status = '';
      // this.spCode = '';
      this.$refs.select_sp.resetSelected();
      this.$refs.doc_visible.clearSingleSelect();
      this.$refs.doc_status.clearSingleSelect();
      // select_provider
    },
    // 新增产品函数
    set_order () {
      this.$refs.setOrder.show_model();
      this.modal_add_product = true
    },
    // 下拉框加载完处理函数
    select_callBack () {
      this.count_select_related--;
      if (this.count_select_related === 0) {
        this.search_interface();
      }
    },
    // 授权状态数据更新函数
    updateSelect_status (val) {
      this.data_select_status = val;
    },
    // 产品类型更新函数
    updateSelect_visible (val) {
      this.data_select_visible = val;
    },
    // 页面刷新
    pageRefresh (val) {
      this.search_interface(val)
    },
    // 初始化页面
    init () {
      this.search_interface();
    },

    // 编辑文档
    editDoc (row) {
      // if (row.type === 'PRODUCT') {
        
      // } else if (row.type === 'SOLUTION') {
      //   this.$router.push({path: '/doc/edit_solution', query: { pageId: row.id }});
      // } else {
      //   this.$router.push({path: '/doc/edit_platform', query: { pageId: row.id }});
      // }
      this.$router.push({path: '/doc/edit_product', query: { pageId: row.id }});
    },

    delete_doc (code) {
      this.productCode = code
      this.deleteProduct = true
      this.offlineProduct = false
      this.normalProduct = false

      this.delete_or_normal_modal_show = true
      this.cause = ''
    },
    product_change_record (row) {
      this.$refs.change_record.show_model();
      this.$refs.change_record.reset_Interface()
      this.$refs.change_record.setProductCode(row.docNo || '');
      this.$refs.change_record.search_Interface(false);
    },
    // 查看文档
    goSeeDoc (row) {
      window.open(row.docUrl)
    },
    // 设置产品文档属性
    set_product_documentation (row) {
      this.$refs.CreatedDoc.showEditModal(row)
    },
  },
  mounted () {
    this.init();
  }
};
</script>

<style scoped>
</style>
