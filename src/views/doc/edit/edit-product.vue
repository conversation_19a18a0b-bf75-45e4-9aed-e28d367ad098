<template>
	<div style="background: #eee;padding:8px">
		<Card :bordered="false" dis-hover>
			<Row style="z-index: 25;">
				<Col span="24" class="margin-bottom-10">
					<div class="page-title" style="padding-bottom: 16px;">
						<p slot="title"> {{ showPreview ? currentTreeNode.title : '编辑文档'}}</p>
						<div class="button-wrap" v-if="!showPreview">
							<Button
								type="ghost"
								v-url="{ url: '/rest/doc/page/change-history' }"
								@click="showPageHistory"
								>页面变更记录</Button
							>
							<Button @click="onPreview" icon="eye" type="ghost" :disabled="!editDocId">预览</Button>
							<Button
								@click="confirmVisit"
								icon="document"
								v-url="{ url: '/rest/doc/page/edit' }"
								type="ghost"
                :disabled="!editDocId"
								>保存</Button
							>
							<Button
								@click="publish"
								icon="paper-airplane"
								type="primary"
                :disabled="!editDocId"
								>保存并发布</Button
							>
						</div>
						<Button v-else @click="showPreview = false" icon="eye-disabled" type="ghost"
							>取消预览</Button
						>
					</div>
				</Col>
				<div class="page-con" v-if="!showPreview">
					<div class="left">
						<Col span="">
							<Card id="card_resList_1" dis-hover>
								<div class="title-bar">
									<span>
										{{ docTitle }}
									</span>
								</div>
								<el-tree
									draggable
									ref="tree"
									:data="data_tree"
									node-key="id"
									:highlight-current="true"
									:default-expand-all="true"
									:expand-on-click-node="false"
									:allow-drop="allowDrop"
									:allowDrag="allowDrag"
									@node-drop="handleDrop"
									@node-click="nodeClick"
								>
									<span class="custom-tree-node" slot-scope="{ node, data }">
										<span>
											<Button
												v-show="node.data.enableStatus"
												style="color: green"
												type="text"
												size="small"
												:icon="node.icon"
											></Button>
											<Button
												v-show="!node.data.enableStatus"
												style="color: red"
												type="text"
												size="small"
												:icon="node.icon"
											></Button>
											<span
												:class="{
													'del-line-gray': !data.display,
													'del-line-red': data.statusDisable,
												}"
											>
												<span
													class="tree-item-name"
													:class="{ required: data.required && data.type === 'API' }"
													>{{ node.label }}</span
												>
												<span :class="data.pageVisible == 'PROTECTED' ? '' : 'hide'">🔐</span>
											</span>
										</span>
										<span>
											<!-- api分类相关 -->
											<Button
												:id="'btn_add_api_class_' + data.id"
												v-if="data.label == 'API列表' || data.label == '*API列表'"
												type="text"
												size="small"
												shape="circle"
												@click="addApiClass(data, $event)"
											>
												<i class="iconfont ">&#xe6f1;</i>
											</Button>
											<Button
												:id="'btn_edit_api_class_' + data.id"
												type="text"
												size="small"
												shape="circle"
												v-if="data.metaType === 'CATEGORY'"
												@click="editApiClass(data, $event)"
											>
												<i class="iconfont ">&#xe73b;</i>
											</Button>
											<!-- 同步 -->
											<Button
												@click="updateTogether($event)"
												v-if="data.label == 'API列表' || data.label == '*API列表'"
												v-url="{ url: '/rest/doc/tree/refresh' }"
												size="small"
												type="primary"
												>同步</Button
											>
											<!-- 新增 -->
											<Button
												:id="'btn_resList_1_' + data.id"
												type="text"
												size="small"
												shape="circle"
												@click="treeCreate(data, $event)"
												v-url="{ url: '/rest/doc/tree/add' }"
												v-if="data.canAppend && data.metaType !== 'CATEGORY'"
											>
												<i class="iconfont ">&#xe6f1;</i>
											</Button>
											<!-- 设置访问 -->
											<Button
												:id="'btn_resList_4_' + data.id"
												type="text"
												size="small"
												shape="circle"
												v-if="data.canConfigAccess || data.canEditDisplay"
												v-url="{ url: '/rest/doc/page/edit' }"
												@click="treeVisit(node, data, $event)"
											>
												<i class="iconfont ">&#xe73b;</i>
											</Button>
											<Button
												:id="'btn_resList_2_' + data.id"
												type="text"
												size="small"
												shape="circle"
												v-if="data.canDelete"
												v-url="{ url: '/rest/doc/page/delete' }"
												@click="treeDelete(node, data, $event)"
											>
												<i class="iconfont ">&#xe6a6;</i>
											</Button>
											<span v-if="data.canDragAcrossRange || data.canDragSameRange">
												<i class="iconfont">&#xe603;</i>
											</span>
										</span>
									</span>
								</el-tree>
							</Card>
							<loading :show="show_loading_tree"></loading>
						</Col>
					</div>
					<div class="right">
						<Col v-if="editDocShow" span="20">
							<Form
								class="doc-edit-form margin-left-16"
								inline
								label-position="left"
								:label-width="80"
							>
								<Form-item label="文档名称:" required>
									<Input :disabled="!canEditTitle" v-model="currentTreeNode.title" />
								</Form-item>
								<Form-item label="页面编码:" required>
									<Input
										:disabled="!canEditTitle"
										placeholder="只支持 数字 字母 下划线 横线"
										v-model="currentTreeNode.pageNo"
									/>
								</Form-item>
								<Form-item label="页面引用:">
									<el-select
										style="width:100%;"
										size="mini"
										v-model="currentTreeNode.refId"
										@change="changeRefId"
										filterable
										remote
										clearable
										placeholder="请输入关键词"
										:remote-method="getRefIdList"
										:loading="refIdLoading"
									>
										<el-option
											v-for="item in refIdList"
											:key="item.id"
											:label="item.label"
											:value="item.value"
										>
										</el-option>
									</el-select>
								</Form-item>
								<Form-item label="负责人:" required>
                  <OwnersSelect :pageId="editDocId" ref="ownersSelect" />
								</Form-item>
							</Form>
						</Col>
						<!-- 编辑文档配置项 -->
						<Col
							span="20"
							class="markdown-product-wrapper"
							v-if="editDocShow"
							:class="hasRefId ? 'hide' : ''"
						>
							<Card id="card_resList_2" class="margin-left-16 pad-right-8 margin-top-10" dis-hover>
								<!-- <Card> -->
								<p slot="title">
									编辑文档
								</p>
								<div :key="editMarkKey" v-if="showMarkdownFlag">
									<editorMarkdown
										:ifNewAddMarkdown="ifNewAddMarkdown"
										:pageType="currentTreeNode.pageType"
										@getChildMark="getChildMark"
										@addMarkDown="addMarkDown"
										@deleteMarkDown="deleteMarkDown"
										:currentTreeNodeBlocks="currentTreeNode.blocks"
									/>
								</div>

								<div
									v-if="ifNewAddMarkdown && currentTreeNode.pageType !== 'API'"
									class="text-center delete-bar margin-top-5 margin-bottom-5"
								>
									<Button size="small" @click="addMarkDown('markdown')" type="text"
										><i style="font-size: 18px;" class="iconfont ">&#xe645;</i></Button
									>
									<Button size="small" @click="addMarkDown('warning')" type="text"
										><i style="font-size: 18px;" class="iconfont ">&#xe61b;</i></Button
									>
								</div>
								<!-- </Card> -->
								<div style="text-align: center">
									<Button type="ghost" @click="detail_close">关闭</Button>
								</div>
							</Card>
							<loading :show="show_loading_preview"></loading>
						</Col>
						<!-- 设置访问 -->
						<Col span="14" v-if="showVisitFlag">
							<Card id="card_resList_6" class="margin-left-16 margin-top-10">
								<p slot="title">设置访问权限</p>
								<h4 class="display-title">设置页面是否展示</h4>
								<RadioGroup v-model="displayFlag">
									<p>
										<Radio id="rbtn_resList_3" :disabled="!showDisplayFlag" label="true">是</Radio>
									</p>
									<p>
										<Radio id="rbtn_resList_4" :disabled="!showDisplayFlag" label="false">否</Radio>
									</p>
								</RadioGroup>
								<div class="margin-top-10 text-center">
									<Button @click="showVisitFlag = false" type="ghost">取消</Button>
									<Button @click="confirmVisit('visit')" type="primary">保存</Button>
								</div>
							</Card>
						</Col>
					</div>
				</div>
				<div v-else>
					<Preview type="docs" :html="html" />
				</div>
			</Row>
			<!-- 页面变更记录 -->
			<Modal v-model="pageHistoryFlag" width="1000" title="变更记录">
				<Row>
					<Row>
						<Col>
							<Col span="2">
								<span>页面名称:</span>
							</Col>
							<Col span="4">
								<Input
									v-model="pageHistorySearch.title"
									placeholder="页面名称"
									size="small"
									@on-enter="search_interface(false)"
								></Input>
							</Col>
							<Col class="margin-left-20" span="2">
								<span>操作人:</span>
							</Col>
							<Col span="4">
								<Input
									v-model="pageHistorySearch.operator"
									placeholder="精确搜索"
									size="small"
								></Input>
							</Col>
						</Col>
					</Row>
					<Row class="margin-top-5">
						<Col>
							<Col span="2">
								<span>操作时间:</span>
							</Col>
							<Col span="12">
								<DatePicker
									type="datetime"
									size="small"
									ref="start"
									@on-change="handleChangeStartTime"
									format="yyyy-MM-dd HH:mm:ss"
									placeholder="选择开始时间"
									style="width: 200px"
								>
								</DatePicker>
								至
								<DatePicker
									type="datetime"
									size="small"
									ref="end"
									@on-change="handleChangeEndTime"
									format="yyyy-MM-dd HH:mm:ss"
									placeholder="选择结束时间"
									style="width: 200px"
								>
								</DatePicker>
							</Col>
							<Col span="4">
								<Button @click="searchPageHistory" type="primary" size="small">查询</Button>
								<Button @click="clearPageHistory" type="ghost" size="small">重置</Button>
							</Col>
						</Col>
					</Row>
				</Row>
				<Row class="margin-top-5">
					<template>
						<i-table border :columns="pageHistoryFormTitle" :data="pageHistoryTableData"> </i-table>
					</template>
				</Row>
				<Row>
					<Tooltip
						content="输入页码后点击回车键跳转页数"
						style="float: right"
						placement="bottom-end"
					>
						<Page
							class="margin-top-10"
							:page-size="10"
							style="float: right"
							:total="pageTotal"
							:current="pageNo"
							show-elevator
							@on-change="pageRefresh"
						></Page>
					</Tooltip>
				</Row>
				<div slot="footer"></div>
			</Modal>
			<!-- 页面新增 -->
			<Modal
				title="新增节点"
				:closable="false"
				v-model="addNewTreeFlag"
				class-name="vertical-center-modal"
			>
				<Row>
					<Col class="margin-bottom-10 margin-left-100" span="18">
						<Col span="5">
							<span class="must-fill" style="position: relative; top: 5px;"> 页面名称：</span>
						</Col>
						<Col span="13">
							<Input v-model="addNewTreeData.targetDocPage.title" placeholder="请输入页面名称" />
						</Col>
					</Col>
				</Row>
				<Row>
					<Col class="margin-left-100" span="18">
						<Col span="5">
							<span style="position: relative; top: 5px;">页面编码：</span>
						</Col>
						<Col span="13">
							<Input
								v-model="addNewTreeData.targetDocPage.pageNo"
								placeholder="只支持 数字 字母 下划线 横线"
							/>
						</Col>
					</Col>
				</Row>
				<div class="text-center" slot="footer">
					<Button id="btn_resList_16" type="primary" @click="confirmAddNewTree">确认</Button>
					<Button id="btn_resList_17" type="ghost" @click="cancelAddNewTree">取消</Button>
				</div>
			</Modal>
			<!-- 删除页面弹框 -->
			<Modal v-model="treeDeleteFlag" width="428">
				<p slot="header">
					<span>删除页面</span>
				</p>
				<div>
					<p class="tip-txt">
						<i class="ivu-icon ivu-icon-help-circled"></i
						>如果删除父级菜单，则关联子级菜单一并删除。确定删除此页面？
					</p>
				</div>
				<div class="text-right" slot="footer">
					<Button id="btn_resList_19" type="text" @click="cancelReasonModal('tree')">取消</Button>
					<Button id="btn_resList_18" type="primary" @click="delete_node_handler">删除</Button>
				</div>
			</Modal>
			<!-- 页面回滚弹框 -->
			<Modal v-model="recoverFlag" width="420">
				<p slot="header">
					<span>回滚页面</span>
				</p>
				<div>
					<p class="tip-txt"><i class="ivu-icon ivu-icon-help-circled"></i>确定回滚此版本？</p>
				</div>
				<div class="text-right" slot="footer">
					<Button id="btn_resList_19" type="text" @click="cancelReasonModal('recover')"
						>取消</Button
					>
					<Button id="btn_resList_18" type="primary" @click="confirmRecover">回滚</Button>
				</div>
			</Modal>
			<iconSelector ref="iconSelector" @icon_update="icon_update"></iconSelector>
		</Card>
		<AddApiClassModal ref="AddApiClassModal" @onOk="information_tree_get" />
		<Publish ref="publish" />
	</div>
</template>

<script>
import commonSelect from '../../common-components/select-components/selectCommon'
import loading from '../../my-components/loading/loading'
import api from '../../../api/api'
import apiClass from '~/api/doc/apiClass'
import util from '../../../libs/util'
import iconSelector from '../../common-components/icon-components/icon-seletor'
import bus from '../../../libs/bus'
import editorMarkdown from '../modals/editorMarkdown'
import 'mavon-editor/dist/css/index.css'
import AddApiClassModal from '../modals/AddApiClassModal'
import Publish from '../modals/Publish'
import Preview from '../modals/Preview'
import OwnersSelect from '../components/OwnersSelect.vue'
export default {
	name: 'edit-product',
	components: {
		commonSelect,
		loading,
		iconSelector,
		editorMarkdown,
		AddApiClassModal,
		Publish,
    Preview,
    OwnersSelect
	},
	data() {
		// 资源url验证
		const validate_resourceUrl = (rule, value, callback) => {
			if (util.formatCheck4(value)) {
				callback(new Error('格式只支持以/开头，支持输入小写字母、数字、字符(/.-)'))
			} else if (util.getLength(value) > 64) {
				callback(new Error('长度不能大于64'))
			} else {
				callback()
			}
		}
		// 资源描述验证
		const validate_resourceDes = (rule, value, callback) => {
			if (value) {
				if (util.getLength(value) > 100) {
					callback(new Error('长度不能大于100'))
				} else {
					callback()
				}
			} else {
				callback()
			}
		}
		return {
			docTitle: '产品文档',
			//  展示markdown开关
			showMarkdownFlag: false,
			// 渲染子组件key
			editMarkKey: 1,
			// 页面id 区分平台文档 产品文档
			pageId: '',
			// 区分产品文档 编辑文档编码
			docNo: '',
			markDownCon: '',
			// 权限规则数据绑定
			data_per_rule_List: [],
			// 当前是创建和编辑 创建
			create_orEdit: true,
			//其他原因数据绑定
			reason_other: '',
			// 当前禁用资源原因
			data_select_disabledReason: '',
			// 资源下拉框列表
			data_disabledReason_List: [
				{
					label: '该资源正在进行升级，暂停使用给您带来不便望请谅解！',
					value: '该资源正在进行升级，暂停使用给您带来不便望请谅解！',
				},
				{
					label: '该资源已迁移到旧版运营后台。暂停使用。给您带来不变望请谅解！',
					value: '该资源已迁移到旧版运营后台。暂停使用。给您带来不变望请谅解！',
				},
				{
					label: '其他',
					value: '其他',
				},
			],
			// 单个启用还是批量 单个：true 批量：false
			single_orBatch: true,
			// 单个禁用原因
			disable_reason_single: '该资源正在进行升级，暂停使用。给您带来不便望请谅解！',
			// 批量禁用原因
			disable_reason_batch: [
				{
					name: '资源一',
					reason: '该资源正在进行升级，暂停使用。给您带来不便望请谅解！',
				},
				{
					name: '资源二',
					reason: '该资源已迁移到旧版运营后台。暂停使用。给您带来不便望请谅解！',
				},
			],
			// 启用资源窗口配置
			modal_enable: false,
			// 禁用资源窗口配置
			modal_disable: false,
			// 加载树loading
			show_loading_tree: false,
			// 启用资源配置loading
			show_loading_enable: false,
			// 编辑资源loading
			show_loading_edit: false,
			// 查看资源loading
			show_loading_preview: false,
			// 查看页面显示
			editDocShow: false,
      editDocId: '',
			// 页面没有内容展示 markdown 按钮
			ifNewAddMarkdown: false,
			// 是否编辑页面标题
			canEditTitle: true,
			// 是否可以编辑页面编码
			canEditPageNo: true,
			// 新增页面显示
			add_resource: false,
			// 右侧部分显示开关
			baseDocFlag: false,
			// 回滚原因弹框 开关
			recoverFlag: false,
			// 回滚原因
			recoverCause: '',
			// 回滚当前data暂存
			recoverData: {},
			code_md5: '',
			data_tree: [],
			defaultProps: {
				children: 'children',
				label: 'label',
			},
			// 当前选择的权限规则
			data_select_permission_rule: '',
			// 是否为顶级资源
			top_resource: false,
			// 页面是否展示flag
			showDisplayFlag: true,
			// 是否能编辑授权访问
			canConfigAccessFlag: true,
			// 页面展示开关
			displayFlag: 'true',
			// 页面授权
			pageVisible: '',
			form_detail: {
				id: '',
				pid: '',
				PResource: '',
				name: '',
				type: '菜单',
				url: '',
				permission: 'YOP可见',
				permId: '',
				per_rule: '',
				status: '启用',
				description: '',
				icon: '',
			},
			// 表单验证规则数据绑定
			baseFormDataRule: {
				type: [{ required: true, message: '资源类型不能为空' }],
				url: [
					{ required: true, message: '资源url不能为空', trigger: 'blur' },
					{ validator: validate_resourceUrl, trigger: 'blur' },
				],
				permission: [{ required: true }],
				permId: [{ required: true, message: '权限规则不能为空' }],
				status: [{ required: true, message: '资源状态不能为空' }],
				description: [{ validator: validate_resourceDes, trigger: 'blur' }],
			},
			// 查看修改基本文档
			baseFormData: {
				baseDocType: '我是文档类型，基本文档',
				baseProNum: '授权扣款编码（SQKK)',
				baseDocVersion: '1.0.0',
				baseDocNum: '1.0',
				baseDocName: '授权扣款解决方案',
				baseDocVisable: '公开',
				baseDocTag: '搜付款',
				baseDocDes: '描述文档描述文档',
			},
			// 当前树的节点相关信息
			currentTreeNode: {
				id: '',
				docNo: '',
				pageNo: '',
				title: '',
				version: '',
				pageVisible: '',
				selfShow: false,
				display: true,
				blocks: [],
				//页面支持引用
				refId: '',
			},
			// 根据refid 控制显示编辑markdown
			hasRefId: '',
			addNewTreeFlag: false,
			// 新增树的节点 信息
			addNewTreeData: {
				sourcePageId: '',
				docNo: '',
				targetDocPage: {
					pageNo: '', //可指定，如果不传则系统生成
					title: '',
				},
			},
			// 页面支持引用加载 flag
			refIdLoading: false,
			// 页面支持引用列表
			refIdList: [],
			// 页面支持引用 跳转预览连接
			accessUrl: '',
			// 编辑文档
			baseDocEditFlag: false,
			// 设置访问
			showVisitFlag: false,
			// 页面变更记录
			pageHistoryFlag: false,
			// 当前节点数据
			current_nodeData: Object,
			// 当前节点数据
			current_dataTemp: Object,
			// 选中节点状态合集
			current_dataStatus: [],
			// 页面变更记录总数
			pageTotal: 10,
			// 当前页码
			pageNo: 1,
			// 是否编辑了 markdown内容
			ifEditMarkdown: false,
			// 页面变更记录表格 title
			pageHistoryFormTitle: [
				{
					title: '页面名称',
					key: 'title',
				},
				{
					title: '操作人',
					key: 'operator',
				},
				{
					title: '页面版本',
					key: 'version',
				},
				{
					title: '操作时间',
					key: 'createdDate',
				},
				{
					title: '操作类型',
					key: 'operType',
					render: (h, params) => {
						let status = '更新'
						if (params.row.operType == 'DELETE') {
							status = '删除'
						} else if (params.row.operType == 'RECOVER') {
							status = '回滚'
						}
						return h('div', [
							h(
								'Button',
								{
									props: {
										type: 'text',
									},
									on: {
										click: () => {},
									},
								},
								status
							),
						])
					},
				},
				{
					title: '操作',
					key: 'action',
					width: 150,
					align: 'center',
					render: (h, params) => {
						return h('div', [
							h(
								'Button',
								{
									props: {
										type: 'primary',
										size: 'small',
										disabled: true,
									},
									style: {
										marginRight: '5px',
									},
									on: {
										click: () => {
											// this.goBackHistory(params)
										},
									},
								},
								'预览'
							),
							h(
								'Button',
								{
									props: {
										type: 'error',
										size: 'small',
									},
									directives: [
										{
											name: 'url',
											value: { url: '/rest/doc/page/recover' },
										},
									],
									on: {
										click: () => {
											this.goBackHistory(params)
										},
									},
								},
								'回滚'
							),
						])
					},
				},
			],
			//页面变更记录 table数据
			pageHistoryTableData: [],
			// 警告弹框 markdown 属性
			waringToolbars: {
				bold: false, // 粗体
				italic: false, // 斜体
				header: true, // 标题
				underline: false, // 下划线
				mark: true, // 标记
				superscript: false, // 上角标
				quote: false, // 引用
				ol: false, // 有序列表
				ink: false, // 链接
				imagelink: false, // 图片链接
				help: true, // 帮助
				code: false, // code
				subfield: false, // 是否需要分栏
				fullscreen: false, // 全屏编辑
				readmodel: true, // 沉浸式阅读
				/* 1.3.5 */
				undo: true, // 上一步
				trash: true, // 清空
				save: true, // 保存（触发events中的save事件）
				/* 1.4.2 */
				navigation: true, // 导航目录
			},
			// 删除原因
			cause: '',
			treeDeleteFlag: false,
			// 删除页面数据
			treeDeleteData: {},
			// 页面变更记录 查询
			pageHistorySearch: {
				title: '',
				operator: '',
				operDateBegin: '',
				operDateEnd: '',
				_pageNo: '',
			},
      showPreview: false,
      html: '',
		}
	},
  methods: {
    onPreview() {
      bus.$emit('getMarkdown')
      this.html = this.currentTreeNode.blocks.reduce((str, item) => {
				str += item.data
				return str
			}, '')
      this.showPreview = true
    },
    publish() {
      this.confirmVisit('',() => {
        this.$refs.publish.showModal(this.pageId, [this.editDocId])
      })
		},
		getChildMark(val) {
			// 获取markdown
			this.currentTreeNode.blocks = val
		},
		// markdown富文本编辑
		updateDoc(markdown, html) {
			// 此时会自动将 markdown 和 html 传递到这个方法中
			this.ifEditMarkdown = true
		},
		saveDoc(markdown, html) {
			// 此时会自动将 markdown 和 html 传递到这个方法中
			this.confirmVisit()
		},
		imgAdd(pos, $file) {
			this.$Message.warning('暂不支持本地图片上传')
			return false
		},
		// 获取页面引用列表
		getRefIdList(query) {
			if (query !== '') {
				this.refIdLoading = true
				let params = {
					keywords: query,
				}
				api.yop_doc_common_page_list_forRef(params).then((response) => {
					this.refIdLoading = false
					if (response.data.status === 'success') {
						this.refIdList = []
						response.data.data.result.forEach((item) => {
							this.refIdList.push({
								label: `${item.title}（${item.docTitle}）`,
								value: item.id,
								accessUrl: item.accessUrl,
							})
						})
					} else {
						this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
					}
				})
			} else {
				this.refIdList = []
			}
		},
		goPreview() {
			if (this.accessUrl && this.currentTreeNode.refId) {
				window.open(this.accessUrl)
			}
		},
		// 改变ID后切换 连接
		changeRefId(val) {
			this.refIdList.forEach((item) => {
				if (val === item.value) {
					this.accessUrl = item.accessUrl
				}
			})
			if (val == '') {
				this.refIdList = []
			}
		},
		confirmDisplay() {
			// 将设置好的变量赋值给保存
			this.currentTreeNode.display = this.displayFlag == 'true'
			let params = this.currentTreeNode
			api.yop_doc_edit_page_edit(params).then((response) => {
				if (response.status === 'success') {
					this.$ypMsg.notice_success(this, '保存成功')
					// 更新下左侧树
					this.information_tree_get()
				} else {
					this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
				}
			})
		},
		// api添加分类
		addApiClass(data, e) {
			e.stopPropagation()
			this.$refs.AddApiClassModal.showAddModal(data.docNo)
		},
		// 删除api分类
		deleteApiClass(id) {
			apiClass
				.deleteApiClass({
					id,
				})
				.then((res) => {
					if (res.status === 'success') {
						this.treeDeleteFlag = false
						this.$ypMsg.notice_success(this, '页面删除成功')
						this.information_tree_get()
					} else {
						this.treeDeleteFlag = false
						this.$ypMsg.notice_error(this, '错误', res.message, res.solution)
					}
				})
		},
		// 编辑api分类
		editApiClass(data, e) {
			e.stopPropagation()
			this.$refs.AddApiClassModal.showEditModal(data)
		},
		// 树的添加
		treeCreate(data, e) {
			e.stopPropagation()
			// 点击新增展示弹框 页面必要信息填写
			this.addNewTreeFlag = true
			this.addNewTreeData.sourcePageId = data.id
			this.addNewTreeData.docNo = data.docNo
		},
		// 新增顶级文档
		add_top_resource() {
			this.addNewTreeFlag = true
			this.addNewTreeData.sourcePageId = -1
			this.addNewTreeData.docNo = this.docNo
		},
		// 确定新增节点
		confirmAddNewTree() {
			let params = this.addNewTreeData
			if (!this.addNewTreeData.targetDocPage.title) {
				this.$Message.error('请输入页面名称')
				return false
			}

			// 进行判断页面编码 可以为空
			if (this.addNewTreeData.targetDocPage.pageNo != '') {
				let reg = /^[A-Za-z0-9-_]+$/ // 有横线 ^[A-Za-z0-9-_]+$  , 无横线 ^[0-9a-zA-Z_]{1,}$
				if (!reg.test(this.addNewTreeData.targetDocPage.pageNo)) {
					this.$Message.error('页面编码格式不符合')
					return false
				}
			}
			api.yop_doc_edit_tree_add(params).then((response) => {
				// console.log(response);
				if (response.status === 'success') {
					this.addNewTreeData.targetDocPage.title = ''
					this.addNewTreeData.targetDocPage.pageNo = ''
					this.addNewTreeFlag = false
					// 成功后更新下 树
					this.information_tree_get()
				} else {
					this.addNewTreeData.targetDocPage.title = ''
					this.addNewTreeData.targetDocPage.pageNo = ''
					this.addNewTreeFlag = false
					this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
				}
			})
		},
		cancelAddNewTree() {
			this.addNewTreeFlag = false
			this.addNewTreeData = {
				sourcePageId: '',
				docNo: '',
				targetDocPage: {
					pageNo: '', //可指定，如果不传则系统生成
					title: '',
				},
			}
		},
		// 树的删除
		treeDelete(node, data, e) {
			e.stopPropagation()
			this.treeDeleteFlag = true
			this.treeDeleteData = data
		},
		// 取消删除
		cancelReasonModal(type) {
			if (type === 'tree') {
				this.cause = ''
				this.treeDeleteFlag = false
			} else {
				this.recoverCause = ''
				this.recoverFlag = false
			}
		},
		// 删除执行函数
		delete_node_handler() {
			// 删除api分类节点
			if (this.treeDeleteData.metaType === 'CATEGORY') {
				this.deleteApiClass(this.treeDeleteData.id)
				return
			}
			let params = {
				docNo: this.treeDeleteData.docNo,
				id: this.treeDeleteData.id,
			}
			api.yop_doc_edit_tree_delete(params).then((response) => {
				// console.log(response);
				if (response.status === 'success') {
					this.treeDeleteFlag = false
					this.$ypMsg.notice_success(this, '页面删除成功')
					this.information_tree_get()
				} else {
					this.treeDeleteFlag = false
					this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
				}
			})
		},
		// 树的编辑
		treeEdit(node, data, e) {
			e.stopPropagation()
			this.showMarkdownFlag = false
			// this.$refs.form_detail.resetFields();

			// 判断是否显示 支持引用
			this.pageType = data.type

      this.currentTreeNode.title = data.label
			if (data.canEditContent) {
        this.editDocShow = true
			} else {
				this.editDocShow = false
				this.$Message.warning('该页面暂不支持编辑')
			}
			this.add_resource = false
			this.pageHistoryFlag = false
			this.baseDocFlag = false
			this.top_resource = false
			this.showVisitFlag = false
			this.showDisplayFlag = false
			this.getNodeInfo(data, 'editMarkdown')
		},
		// 数据获取
		getNodeInfo(data, type) {
			let param = {
				id: data.id,
				docNo: data.docNo,
			}
			api.yop_doc_edit_view(param).then((response) => {
				let status = response.data.status
				if (status === 'success') {
					let result = response.data.data.result
					this.currentTreeNode.docNo = result.docNo
					this.currentTreeNode.pageNo = result.pageNo
					this.currentTreeNode.version = result.version
					this.currentTreeNode.pageType = result.pageType
					//页面引用
					this.refIdList = []
					if (result.refPage) {
						this.refIdList.push({
							label: result.refPage ? `${result.refPage.title}（${result.refPage.docTitle}）` : '',
							value: result.refPage ? result.refPage.id : '',
							accessUrl: result.refPage ? result.refPage.accessUrl : '',
						})
						this.accessUrl = result.refPage.accessUrl
					}
					setTimeout(() => {
						this.currentTreeNode.refId = result.refId ? result.refId : ''
						this.hasRefId = this.currentTreeNode.refId
					}, 0)
					// 页面引用end
					this.currentTreeNode.pageVisible = result.pageVisible
					this.currentTreeNode.selfShow = result.selfShow
					this.currentTreeNode.display = result.display
					this.displayFlag = result.display ? 'true' : 'false'
					this.pageVisible = result.pageVisible
					this.currentTreeNode.id = result.id
					this.canEditTitle = result.canEditTitle //是否编辑页面标题
					this.canEditPageNo = result.canEditPageNo //是否编辑页面编码
					// 判断 页面是否是新增
					if (!!result.blocks) {
						if (result.blocks.length > 0) {
							// 处理下图片 加上前缀 start
							let regAddUrl = new RegExp(`/attachments/access`, 'gm')
							result.blocks.forEach((item) => {
								item.data = item.data.replace(
									regAddUrl,
									`${localStorage.remoteIP}/attachments/access`
								)
							})
							// 处理下图片 加上前缀 end
							this.currentTreeNode.blocks = result.blocks
							if (type === 'editMarkdown') {
								this.showMarkdownFlag = true
							}
							this.ifNewAddMarkdown = false
						} else {
							this.currentTreeNode.blocks = []
							if (type === 'editMarkdown') {
								this.showMarkdownFlag = true
							}
							this.ifNewAddMarkdown = true
						}
					}
				} else {
					this.$ypMsg.notice_error(
						this,
						'文档列表详细信息获取失败,请刷新重试',
						response.data.message,
						response.data.solution
					)
				}
			})
		},
		treeDisplay(node, data, e) {
			e.stopPropagation()
		},
		// 树的 设置访问
		treeVisit(node, data, e) {
			e.stopPropagation()
			this.pageHistoryFlag = false
			this.baseDocFlag = false
			this.add_resource = false
			this.editDocShow = false
			this.top_resource = false
			this.showDisplayFlag = data.canEditDisplay
			this.canConfigAccessFlag = data.canConfigAccess
			this.getNodeInfo(data)
			setTimeout(() => {
				this.showVisitFlag = true
			}, 0)
		},
		// 资源树的删除执行函数
		remove(node, data) {
			const parent = node.parent
			const children = parent.data.children || parent.data
			const index = children.findIndex((d) => d.id === data.id)
			children.splice(index, 1)
		},
		nodeChange() {
			// console.log(this.$refs.tree.getHalfCheckedNodes());
		},

		nodeClick(data) {
			this.showMarkdownFlag = false
			this.currentTreeNode.title = data.title
			// 判断是否显示 支持引用
			this.pageType = data.type
			// api 和异步通知文档 不展示编辑
			if (data.canEditContent) {
        this.editDocShow = true
        this.editDocId = data.id
			} else {
        this.editDocShow = false
        this.editDocId = ''
				this.$Message.warning('该页面暂不支持编辑')
				return
			}
			// todo 请求接口 如果有就就展示编辑 否则展示新增
			this.pageHistoryFlag = false
			this.baseDocFlag = false
			this.add_resource = false
			this.top_resource = false
			this.showVisitFlag = false
			this.showDisplayFlag = false
			this.getNodeInfo(data, 'editMarkdown')
		},
		// markdown编辑
		detail_close() {
			if (this.ifEditMarkdown) {
				this.$Modal.confirm({
					title: '提示',
					content: '文档尚未保存，确定要关闭当前页面吗？',
					'ok-text': '确认',
					onOk: () => {
						// this.confirmVisit();
						this.editDocShow = false
					},
				})
			} else {
				this.editDocShow = false
			}
		},
		// 添加新的markdown
		addMarkDown(type, index, title) {
			console.log(index, '获取的索引')
			this.showMarkdownFlag = false
			bus.$emit('getMarkdown')
			let newMarkDownData = {}
			setTimeout(() => {
				if (type == 'warning') {
					newMarkDownData = { data: '## 新增警告', type: 'callout', title }
				} else {
					newMarkDownData = { data: '## 请填写内容', type: 'text', title }
				}
				//在指定位置添加元素,第一个参数指定位置,第二个参数指定要删除的元素,如果为0,则追加
				this.currentTreeNode.blocks.splice(index, 0, newMarkDownData)
				// this.currentTreeNode.blocks.push(newMarkDownData)
				// 展示添加按钮
				if (this.currentTreeNode.blocks.length == 0) {
					this.ifNewAddMarkdown = true
				} else {
					this.ifNewAddMarkdown = false
				}

				this.showMarkdownFlag = true
			}, 10)
		},
		// 删除markdown
		deleteMarkDown(index) {
			this.currentTreeNode.blocks.splice(index, 1)
			// 删完为空  则展示添加按钮
			if (this.currentTreeNode.blocks.length == 0) {
				this.ifNewAddMarkdown = true
			}
			this.editMarkKey++
		},
		// 添加提交
		add_close() {
			this.add_resource = false
		},
		// 同步更新
		updateTogether(e) {
			e.stopPropagation()
			let params = {
				id: this.pageId,
			}
			api.yop_doc_edit_tree_refresh(params).then((response) => {
				// console.log(response);
				if (response.status === 'success') {
					// 成功后更新下 树
					this.$ypMsg.notice_success(this, '同步成功')
					this.information_tree_get()
				} else {
					this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
				}
			})
		},
		// 页面变更记录
		showPageHistory() {
			this.pageHistoryFlag = true
			this.baseDocFlag = false
			this.add_resource = false
			this.editDocShow = false
			this.top_resource = false
			this.showVisitFlag = false
			// 获取变更记录列表
      this.pageHistorySearch.title = !!this.currentTreeNode.title ? this.currentTreeNode.title : ''
      this.pageHistorySearch.docNo = this.docNo
			api.yop_doc_edit_change_history(this.pageHistorySearch).then((response) => {
				if (response.data.status === 'success') {
					this.pageHistoryTableData = response.data.data.page.items
					this.pageNo = response.data.data.page.pageNo
					if (response.data.data.page.items) {
						if (response.data.data.page.items.length < 10) {
							this.pageTotal = response.data.data.page.items.length
						} else {
							this.pageTotal = NaN
						}
					} else {
						this.pageTotal = NaN
					}
				} else {
					this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
				}
			})
		},
		//查询页面变更记录 时间
		handleChangeStartTime(date) {
			this.pageHistorySearch.operDateBegin = date
		},
		handleChangeEndTime(date) {
			this.pageHistorySearch.operDateEnd = date
		},
		// 查询页面变更记录
		searchPageHistory() {
			let params = this.pageHistorySearch
			api.yop_doc_edit_change_history(params).then((response) => {
				if (response.data.status === 'success') {
					this.pageNo = response.data.data.page.pageNo
					this.pageHistoryTableData = response.data.data.page.items
					if (response.data.data.page.items) {
						if (response.data.data.page.items.length < 10) {
							this.pageTotal = response.data.data.page.items.length
						} else {
							this.pageTotal = NaN
						}
					} else {
						this.pageTotal = NaN
					}
				} else {
					this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
				}
			})
		},
		// 页面刷新
		pageRefresh(page) {
			this.pageHistorySearch._pageNo = page
			this.searchPageHistory()
		},
		// 清空查询选项
		clearPageHistory() {
			this.pageHistorySearch.title = ''
			this.pageHistorySearch.operator = ''
			this.$refs.start.handleClear()
			this.$refs.end.handleClear()
		},
		// 文档设置访问确定 // 以及文档markdown 编辑修改确定
    confirmVisit(type, cb) {
      if (!this.$refs.ownersSelect.hasOwner()) {
				this.$Message.warning('负责人必填')
				return false
			}
			bus.$emit('getMarkdown')
			this.currentTreeNode.display = this.displayFlag == 'true'
			let params = this.currentTreeNode
			if (!this.currentTreeNode.pageNo) {
				this.$Message.warning('页面编码不能为空')
				return false
			}
			// 设置访问不需 验证
			if (type != 'visit') {
				let reg = /^[A-Za-z0-9-._]+$/
				if (!reg.test(this.currentTreeNode.pageNo)) {
					this.$Message.error('页面编码格式不符合')
					return false
				}
			}
			if (params.id == params.refId) {
				this.$Message.warning('页面不能引用自身')
				return false
			}
			// 处理下图片链接中的前缀
			let regUrl = new RegExp(`${localStorage.remoteIP}`, 'gm')
			regUrl.escape = function(s) {
				return s.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&')
			}
			params.blocks.forEach((item) => {
				item.data = item.data.replace(regUrl, '')
			})
			delete params.pageVisible
			api.yop_doc_edit_page_edit(params).then((response) => {
				// console.log(response);
        if (response.status === 'success') {
          if (cb) {
            cb()
          } else {
            if (localStorage.needAudit === 'true') {
              this.$ypMsg.notice_info(this, response.message)
            } else {
              this.$ypMsg.notice_success(this, '保存成功')
            }
          }
					// 更新下左侧树
					this.information_tree_get()
					// 更新下本身内容
					this.getNodeInfo(this.currentTreeNode)
				} else {
					this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
				}
			})
		},
		// 点击回滚按钮
		goBackHistory(data) {
			this.recoverFlag = true
			this.recoverData = data
			/* this.$Modal.confirm({
                    title: '回滚页面',
                    content: `<p>确定回滚此版本吗？</p>`,
                    onOk: () => {
                        // this.$Message.info('Clicked ok');
                        // this.confirmRecover()
                        console.log(this.recoverCause,"《===原因")
                    },
                    onCancel: () => {
                        this.$Message.info('已取消');
                    }
                }); */
		},
		// 弹框 确定回滚
		confirmRecover() {
			// if(!this.recoverCause){
			//     this.$Message.error('请输入回滚原因');
			//     return false;
			// }
			let params = {
				id: this.recoverData.row.id,
				docNo: this.recoverData.row.docNo,
				// cause: this.recoverCause
			}
			api.yop_doc_edit_recover(params).then((response) => {
				if (response.status === 'success') {
					this.$ypMsg.notice_success(this, '回滚成功')
					this.recoverFlag = false
					// 更新下左侧树
					this.information_tree_get()
				} else {
					this.recoverFlag = false
					this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
				}
			})
		},
		// 批量删除
		batch_delete() {
			if (this.get_allCheckedNodes().ids.length > 0) {
				this.$Modal.confirm({
					title: '删除资源配置',
					content:
						"<p style='color:red'>如果删除父级菜单，则关联子级菜单一并删除。</p><p>确定删除资源配置？</p>",
					'ok-text': '确认',
					onOk: () => {
						// 删除操作
						let ids = this.get_allCheckedNodes().ids
						let nodes = this.get_allCheckedNodes().nodes
						let param = {
							ids: ids,
						}
						// console.log(nodes)
						this.delete_node_handler_batch(nodes, param)
					},
				})
			} else {
				this.$Modal.warning({
					title: '警告',
					content: '至少选择一个资源',
				})
			}
		},
		// 批量删除执行函数
		delete_node_handler_batch(nodes, param) {
			// nodes.forEach(
			//     item =>{
			//         this.remove(item, item.data);
			//     }
			// )
			api.yop_acl_resource_delete(param).then((response) => {
				if (response.status === 'success') {
					this.$ypMsg.notice_success(this, '资源删除成功')
					this.information_tree_get()
				} else {
					this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
				}
			})
		},
		// 初始化
		init() {
			// 获取页面 pageId
			this.pageId = this.$route.query.pageId
			this.information_tree_get()
			this.disable_reason_get()
		},
		updateSelect_permission_rule(val) {
			this.form_detail.per_rule = val
		},
		// 获取禁用原因列表
		disable_reason_get() {
			api.yop_acl_resource_disableReason_list().then((response) => {
				let status = response.data.status
				if (status === 'success') {
					let result = response.data.data.result
					this.data_disabledReason_List = []
					if (result) {
						result.forEach((item) => {
							this.data_disabledReason_List.push({
								value: item,
								label: item,
							})
						})
					}
					this.data_disabledReason_List.push({
						value: '其他',
						label: '其他',
					})
				} else {
					this.$ypMsg.notice_error(
						this,
						'禁用原因列表获取失败',
						response.data.message,
						response.data.solution
					)
				}
			})
		},
		// 获取所有选择节点
		get_allCheckedNodes() {
			let ids = []
			let nodes = this.$refs.tree.getCheckedNodes()
			if (!!nodes) {
				nodes.forEach((item) => {
					ids.push(item.id)
				})
			}
			return {
				ids: ids,
				nodes: nodes,
			}
		},
		// 树的结构信息获取
		information_tree_get() {
			this.show_loading_tree = true
			let params = {
				id: this.pageId,
			}
			api.yop_doc_edit_tree(params).then((response) => {
				let status = response.data.status
				// console.log(response.data.data,'<===树结构data')
				if (status === 'success') {
					let result = response.data.data.result.pages
					this.docNo = response.data.data.result.docNo
					this.docTitle = response.data.data.result.title
					this.data_tree = []
					this.data_tree = this.information_tree_handler(result, 0)
				} else {
					this.$ypMsg.notice_error(
						this,
						'文档数据获取失败,请刷新重试',
						response.data.message,
						response.data.solution
					)
				}
				this.show_loading_tree = false
			})
		},
		// 树的结构数据处理
		information_tree_handler(data, index) {
			let topTemp = true
			if (index === 0) {
				topTemp = true
			} else {
				topTemp = false
			}
			let returnData = []
			index++
			if (data) {
				data.forEach((item) => {
					let nameTemp = ''
					// 标题超过十个字处理
					if (util.getLength(item.title) > 16) {
						nameTemp = item.title.substring(0, 16) + '...'
					} else {
						nameTemp = item.title
					}
					// nameTemp = item.title;
					if (!!item.children) {
						returnData.push({
							id: item.id,
							label: nameTemp,
							required: item.required,
							title: item.title,
							metaType: item.metaType,
							desc: item.desc,
							depth: item.depth,
							type: item.pageType,
							docNo: item.docNo,
							pageNo: item.pageNo,
							version: item.version,
							pageVisible: item.pageVisible, //授权可见、公开
							selfShow: item.selfShow,
							createdDate: item.createdDate,
							lastModifiedDate: item.lastModifiedDate,
							pageNo: item.pageNo,
							canConfigAccess: item.canConfigAccess, //设置访问
							canEditContent: item.canEditContent, //编辑内容
							canEditDisplay: item.canEditDisplay, //控制是否展示
							display: item.display, //是否在mbr展示
							canDragAcrossRange: item.canDragAcrossRange, //跨级拖拽
							canDragSameRange: item.canDragSameRange, //同级拖拽
							canDelete: item.canDelete, //删除
							canAppend: item.canAppend, //新增
							statusDisable: item.statusDisable, // 禁用标记
							canEditTitle: item.canEditTitle, //编辑标题
							canEditPageNo: item.canEditPageNo, //编辑页面编码
							children: this.information_tree_handler(item.children, index),
						})
					} else {
						returnData.push({
							id: item.id,
							label: nameTemp,
							required: item.required,
							title: item.title,
							metaType: item.metaType,
							desc: item.desc,
							depth: item.depth,
							type: item.pageType,
							docNo: item.docNo,
							pageNo: item.pageNo,
							version: item.version,
							pageVisible: item.pageVisible, //授权可见、公开
							selfShow: item.selfShow,
							createdDate: item.createdDate,
							lastModifiedDate: item.lastModifiedDate,
							pageNo: item.pageNo,
							canConfigAccess: item.canConfigAccess, //设置访问
							canEditDisplay: item.canEditDisplay, //控制是否展示
							display: item.display, //是否在mbr展示
							canEditContent: item.canEditContent, //编辑内容
							canDragAcrossRange: item.canDragAcrossRange, //跨级拖拽
							canDragSameRange: item.canDragSameRange, //同级拖拽
							canDelete: item.canDelete, //删除
							canAppend: item.canAppend, //新增
							statusDisable: item.statusDisable,
							canEditTitle: item.canEditTitle, //编辑标题
							canEditPageNo: item.canEditPageNo, //编辑页面编码
							children: [],
						})
					}
				})
			}
			return returnData
		},
		//  拖拽操作
		nodeDrop(before, after, inner, event) {
			let pid = after.id
		},
		// 判断是否所有选中的节点状态数组
		allNodes_status(nodes, status) {
			if (!!nodes) {
				for (var i in nodes) {
					if (nodes[i].enableStatus !== status) {
						return false
					}
				}
				return true
			} else {
				return false
			}
		},
		// 左侧树拖拽
		allowDrag(draggingNode) {
			if (draggingNode.data.canDragSameRange || draggingNode.data.canDragAcrossRange) {
				return true
			} else {
				this.$Message.warning('此菜单暂不支持排序!')
				return false
			}
		},
		allowDrop(draggingNode, dropNode, type) {
			if (draggingNode.data.canDragAcrossRange || draggingNode.data.canDragSameRange) {
				return true
			}
		},
		// 拖拽后台执行函数
		handleDrop(draggingNode, dropNode, dropType, ev) {
			let point = ''
			if (dropType == 'inner') {
				point = 'APPEND'
			} else if (dropType == 'after') {
				point = 'BOTTOM'
			} else {
				point = 'TOP'
			}
			let param = {
				docNo: draggingNode.data.docNo,
				sourcePageId: draggingNode.data.id,
				targetPageId: dropNode.data.id,
				sourcePageMetaType: draggingNode.data.metaType,
				targetPageMetaType: dropNode.data.metaType,
				point: point,
			}
			this.drop_handler(param)
		},
		// 拖拽执行函数
		drop_handler(param) {
			api.yop_doc_edit_drag(param).then((response) => {
				// console.log(response);
				if (response.status === 'success') {
					// 更新下左侧树
					this.information_tree_get()
				} else {
					// 更新下左侧树
					this.information_tree_get()
					this.$ypMsg.notice_error(this, '错误', response.message, response.solution)
				}
			})
		},
		// 清空已选节点
		resetChecked() {
			this.$refs.tree.setCheckedKeys([])
		},
		// 图标选取
		icon_select() {
			if (this.form_detail.icon && this.form_detail.icon !== '') {
				this.$refs.iconSelector.icon_set(this.form_detail.icon)
			}
			this.$refs.iconSelector.modal_preview()
		},
		// 图标数据更新
		icon_update(val) {
			this.form_detail.icon = val
			this.$refs.iconSelector.modal_reset()
		},
	},
	mounted() {
		this.init()
	},
}
</script>
<style scoped lang="less">
.title-bar {
	overflow: hidden;
	background-color: #f3f3f3;
	border-radius: 5px;
	line-height: 32px;
	margin-bottom: 8px;
}
.title-bar span {
	float: left;
	font-weight: bold;
}
.title-bar button {
	float: right;
}
.custom-tree-node {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	padding-right: 8px;
	overflow-x: scroll;
	/*隐藏滚动条，当IE下溢出，仍然可以滚动*/
	-ms-overflow-style: none;
	/*火狐下隐藏滚动条*/
	overflow: -moz-scrollbars-none;
}
.custom-tree-node .color-red {
	color: red;
}
.custom-tree-node .del-line-gray {
	text-decoration: line-through;
	color: #e2e2e2;
}
.custom-tree-node .del-line-red {
	text-decoration: line-through;
	color: red;
}
.custom-tree-node::-webkit-scrollbar {
	display: none;
}

.el-checkbox__inner {
	width: 12px !important;
	height: 12px !important;
}

.custom-tree-node {
	font-size: 12px;
}

.yop-explain-120 {
	font-size: 12px;
	color: grey;
	padding-left: 120px;
	line-height: 30px;
}
.red {
	color: red;
}
.green {
	color: green;
}
.icon-preview-edit {
	width: 35px;
	height: 35px;
	font-size: 35px;
}
.yp-margin-left-15 {
	margin-left: 15px;
	vertical-align: text-bottom;
}
.page-title {
	overflow: hidden;
	border-bottom: 1px solid #ccc;
	padding-bottom: 4px;
}
.page-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.docLogo {
	width: 200px;
	height: 180px;
}
.text-center {
	text-align: center;
}
.text-right {
	text-align: right;
}
.text-left {
	text-align: left;
}
.tip-txt {
	margin-bottom: 12px;
	font-size: 12px;
	color: #495060;
	position: relative;
	top: -10px;
}
.tip-txt i {
	color: #f90;
	font-size: 36px;
	margin-right: 10px;
	position: relative;
	top: 6px;
}
.ivu-modal-content .ivu-modal-header {
	border: none;
}
.margin-left-100 {
	margin-left: 100px;
}
.doc-operter-item {
	display: inline-block;
	white-space: nowrap;
}
.must-fill::after {
	position: absolute;
	left: -9px;
	content: '*';
	display: inline-block;
	margin-right: 4px;
	line-height: 1;
	font-family: SimSun;
	font-size: 12px;
	color: #ed3f14;
}

.op-icon .op-image .dropdown-item {
	border: 1px solid lime !important;
}
.ivu-card-body .op-icon .op-image .dropdown-item:nth-child(2) {
	display: none;
}
.delete-bar {
	position: relative;
}
.pad-right-8 {
	padding-right: 8px;
}
.delete-btn {
	position: absolute;
	right: -27px;
	top: -310px;
}
.display-title {
	margin: 30px 0 10px;
}
.hide {
	display: none;
}
.tree-item-name {
	position: relative;
}
.tree-item-name.required:before {
	content: '*';
	font-size: 14px;
	position: absolute;
	left: -5px;
	top: 0px;
	color: red;
}
.markdown-product-wrapper {
	position: sticky;
	top: 30px;
}

.page-con {
	width: 100%;
	display: flex;
	.left {
		width: 30%;
		height: 850px;
		overflow: auto;
	}
	.right {
		width: 70%;
		height: 850px;
		overflow: auto;
	}
}
@import '../../../styles/common.less';
.doc-edit-form {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	/deep/ .ivu-form-item {
		margin-bottom: 12px;
	}
}
</style>
