<template>
	<Modal title="创建问题" v-model="show" class-name="vertical-center-modal" width="800">
		<Row style="margin-bottom: 12px">
			<Col>
				<Col :span="19">
					<Input v-model="question"></Input>
				</Col>
				<Col :span="4" offset="1">
					<Button type="primary" :disabled="!question" @click="ceated">创建问题</Button>
				</Col>
			</Col>
		</Row>
		<Row>
			<Col style="margin-bottom: 8px">
				<h3>相似问题</h3>
			</Col>
			<Col>
				<ul class="question-list">
					<li class="question-item">
						<div class="title">Q：我是一个有关SDK使用过程中的问题，大约不超过50个汉字？</div>
						<div
							class="content"
						>A：我是针对个问题的一段有关问答的辅助描述，用户可以通过这段话判断是否要点进去看内容，文字不超过50字我是针对个问题的一段有关问答的辅助描述，用户可以通过这段话判断是否要点进去看内容</div>
						<div>
							<Tag type="border" color="blue">标准商户收付款方案-支付宝生活号支付</Tag>
						</div>
					</li>
				</ul>
			</Col>
		</Row>
	</Modal>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      question: ''
    }
  },
  methods: {
		ceated() {
			this.$router.push({
				path: '/faq/created',
				query: {
					question: this.question
				}
			})
		},
    showModal() {
      this.show = true
    },
    cancel() {
      this.show = false
    }
  }
}
</script>

<style lang="less" scoped>
	.question-list {
		list-style: none;
		.question-item {
			margin-bottom: 12px;
			.title {
				font-size: 14px;
				font-weight: bold;
				margin-bottom: 4px;
			}
			.content {
				font-size: 12px;
				margin-bottom: 4px;
			}
		}
	}
</style>