<template>
	<div style="background: #eee;padding:8px" class="faq-wrap">
		<Card :bordered="false" dis-hover>
			<template v-if="!disabled">
				<h2 v-if="!params.id">创建问题</h2>
				<h2 v-else>编辑问题</h2>
			</template>
			<h2 v-else>查看问题</h2>
			<Row style="padding-top: 60px">
				<Form ref="form" :model="params" label-position="top" :rules="ruleValidate">
					<Form-item label="问题" prop="title">
						<Input v-model="params.title" :disabled="disabled" id="faq-title-input"></Input>
					</Form-item>
					<Form-item label="关联文章" prop="relateItems">
						<Treeselect
              id="faq-treeselect"
							:value="params.relateItems"
							@input="select"
							value-consists-of="LEAF_PRIORITY"
							:disabled="disabled"
							valueFormat="object"
							:multiple="true"
							:async="true"
							:load-options="loadOptions"
						>
							<template slot="value-label" slot-scope="{ node }">{{ node.raw.title }}</template>
							<template slot="option-label" slot-scope="{ node }">{{ node.raw.title }}</template>
						</Treeselect>
					</Form-item>
					<Form-item label="答案" prop="answer">
						<MarkDown v-model="params.answer" :disabled="disabled" />
					</Form-item>
				</Form>
			</Row>
		</Card>
		<div class="footer">
			<Button type="ghost" id="btn_faq_return" @click="returnList">返回</Button>
			<template v-if="!disabled">
				<template v-if="params.id">
					<Button id="btn_faq_update" v-url="{ url:'/rest/doc/faq/update'}" type="primary" @click="updateFaq">保存</Button>
				</template>
				<template v-else>
					<Button id="btn_faq_create" v-url="{ url:'/rest/doc/faq/create'}" type="primary" @click="createFaq">保存</Button>
				</template>
			</template>
		</div>
	</div>
</template>

<script>
import Mixin from './mixin'
import Api from '~/api/faq';
import MarkDown from './markDown.vue'
import Treeselect from '../common-components/tree-select/vue-treeselect.cjs.min.js'
import '../common-components/tree-select/vue-treeselect.min.css'
export default {
  mixins: [Mixin],
  components: {
    MarkDown,
    Treeselect
  },
  data () {
    const validateAnswer = (rule, value, callback) => {
      if (!value.trim()) {
        callback(new Error('请输入答案'));
      } else if (value.length > 512) {
        callback(new Error('答案不能大于512个字符'));
      } else {
        callback();
      }
    };
    const validateTitle = (rule, value, callback) => {
      if (!value.trim()) {
        callback(new Error('请输入问题'));
      } else if (value.length < 5 || value.length > 50) {
        callback(new Error('问题限制5-50个字符之间'));
      } else {
        callback();
      }
    };
    return {
      disabled: false,
      productCodesShow: false,
      options: [],
      params: {
        id: '',
        answer: '',
        relateItems: [],
        title: ''
      },
      ruleValidate: {
        title: [
          { required: true, validator: validateTitle, trigger: 'blur' }
        ],
        relateItems: [
          { required: true, message: '关联文章不能为空', trigger: 'blur' }
        ],
        answer: [
          { required: true, validator: validateAnswer, trigger: 'change' }
        ]
      }
    }
  },
  created () {
    if (this.$route.query.id) {
      this.params.id = this.$route.query.id
      this.getDetail()
    }
    if (this.$route.query.detail) {
      this.disabled = true
    }
  },
  methods: {
    getDetail () {
      Api.getDetail({
        id: this.params.id
      })
        .then(res => {
          if (res.data.status === 'success') {
            const {
              id,
              answer,
              title,
              relateItems
            } = res.data.data.result
            this.params = {
              id,
              answer,
              title,
              relateItems
            }
          } else {
            this.$ypMsg.notice_error(this, '错误', res.data.message);
          }
        })
    },
    createFaq () {
      Api.createFaq(this.params)
        .then(res => {
          if (res.status === 'success') {
            this.$Message.success('创建成功')
            this.returnList()
          } else {
            this.$ypMsg.notice_error(this, '错误', res.message);
          }
        })
    },
    select (value) {
      this.params.relateItems = value
    },
    updateFaq () {
      Api.updateFaq(this.params)
        .then(res => {
          if (res.status === 'success') {
            this.$Message.success('更新成功')
            this.returnList()
          } else {
            this.$ypMsg.notice_error(this, '错误', res.message);
          }
        })
    },
    loadOptions (args) {
      const { action, searchQuery, callback } = args
      if (action === 'ASYNC_SEARCH') {
        if (this.timer) {
          clearTimeout(this.timer)
        }
        this.timer = setTimeout(() => {
          this.timer = null
          Api.relateList({
            keywords: searchQuery
          })
            .then(res => {
              if (res.data.status === 'success') {
                callback(null, res.data.data.data)
              } else {
                this.$ypMsg.notice_error(this, '错误', res.data.message);
              }
            })
        }, 500)
      }
    },
    returnList () {
      this.$router.push('/faq/list')
    }
  }
}
</script>
<style lang="less" scoped>
	.faq-wrap {
		padding-bottom: 68px !important;
		h2 {
			line-height: 60px;
			padding-left: 16px;
			border-bottom: 1px solid #eee;
			left: 0;
			top: 0;
			right: 0;
			position: absolute;
		}
		.footer {
			position: fixed;
			padding-right: 50px;
			height: 60px;
			line-height: 60px;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: #fff;
		}
    /deep/ .vditor-reset {
      padding: 10px !important;
    }
	}
</style>