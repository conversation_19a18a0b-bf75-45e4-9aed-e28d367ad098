import Api from '~/api/faq';
export default {
  methods: {
    aclFaq(id, cb) {
      Api.aclFaq({ id }).then(res => {
        if (res.data.status === 'success') {
          if(res.data.data.result) {
            cb && cb()
          } else {
            this.$Message.error('您没有此操作的权限');
          }
        } else {
          this.$ypMsg.notice_error(this, '错误', res.data.message);
        }
      });
    },
    deleteFaq ({ id, title}) {
      this.$Modal.confirm({
        title: '确认删除',
        content: '删除操作后将无法撤回，是否确认删除？',
        loading: true,
        onOk: () => {
          Api.deleteFaq({ id }).then(res => {
            if (res.status === 'success') {
              this.$Message.success('删除成功');
              this.$Modal.remove();
              this.returnList && this.returnList();
              this.getList && this.getList();
            } else {
              this.$ypMsg.notice_error(this, '错误', res.message);
            }
          });
        }
    });
    },
    publish (params) {
      Api.faqPublish(params).then(res => {
        if (res.status === 'success') {
          this.$Message.success('发布成功');
          this.getList && this.getList();
        } else {
          this.$ypMsg.notice_error(this, '错误', res.message);
        }
      });
    },
    setTop (params) {
      Api.faqSetTop(params).then(res => {
        if (res.status === 'success') {
          this.$Message.success('置顶操作成功，3分钟后生效');
          this.getList && this.getList();
        } else {
          this.$ypMsg.notice_error(this, '错误', res.message);
        }
      });
    }
  }
};
