<style lang="less">
	@import '../../styles/common';
	@import '../API_Management_Open/api_Mangement_Open';

	.demo-badge-alone {
		background: #5cb85c !important;
	}

	.round {
		width: 16px;
		height: 16px;
		display: inline-block;
		font-size: 20px;
		line-height: 16px;
		text-align: center;
		color: #f00;
		text-decoration: none;
	}

	.ivu-table-cell {
		padding-left: 10px;
		padding-right: 10px;
	}
</style>
<template>
	<div style="background: #eee;padding:8px">
		<Card :bordered="false">
			<Row type="flex" align="middle">
				<Col span="8">
					<Col span="5" class="margin-top-10">
						<span>常见问题:</span>
					</Col>
					<Col span="19">
						<Input id="input_faq_1" class="margin-top-5" v-model="params.faqTitle" placeholder="请输入常见问题"></Input>
					</Col>
				</Col>

				<Col offset="1" span="7">
					<Col span="5" class="margin-top-10">
						<span>关联文章:</span>
					</Col>
					<Col span="19">
						<Input
							id="input_faq_2"
							class="margin-top-5"
							v-model="params.pageTitle"
							placeholder="请输入关联文章"
						></Input>
					</Col>
				</Col>

				<Col offset="1" span="7">
					<Col span="5" class="margin-top-10">
						<span>状态:</span>
					</Col>
					<Col span="19">
						<Select id="select_faq_1" clearable v-model="params.faqStatus" class="margin-top-5" style="width:100%">
							<Option :value="item.value" :key="item.value" v-for="item in statusMap">{{ item.label }}</Option>
						</Select>
					</Col>
				</Col>
				<Col class="margin-top-5" span="8">
					<Col span="5" class="margin-top-10">
						<span>创建时间:</span>
					</Col>
					<Col span="19">
						<Date-picker
              id="select_faq_2"
							:value="time"
							@on-change="handleDateChange"
							style="width: 100%"
							class="margin-top-5"
							type="datetimerange"
							placeholder="选择日期和时间"
							:options="{
                disabledDate: disabledDate
              }"
						></Date-picker>
					</Col>
				</Col>
				<Col class="margin-top-5" span="7" offset="1">
					<Col span="5" class="margin-top-10">
						<span>是否置顶:</span>
					</Col>
					<Col span="19">
						<Select id="select_faq_3" clearable v-model="params.top" class="margin-top-5" style="width:100%">
							<Option :value="1">是</Option>
							<Option :value="0">否</Option>
						</Select>
					</Col>
				</Col>
			</Row>
			<Row class="margin-top-20">
				<Col span="12">
          <div style="height: 30px">
					  <Button v-url="{ url: '/rest/doc/faq/create'}" id="btn_faq_3" class="margin-right-10" type="primary" @click="createdQuestion">创建常见问题</Button>
          </div>
        </Col>
				<Col span="12" type="flex" align="end">
					<Button id="btn_faq_1" type="primary" @click="search">查询</Button>
					<Button id="btn_faq_2" type="ghost" @click="reset_search">重置</Button>
				</Col>
			</Row>
			<Row class="margin-top-10">
				<Col span="24">
					<Table id="table_1" border :columns="columns" :data="list"></Table>
					<Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
						<Page
							class="margin-top-10"
							style="float: right"
							:total="pageTotal"
							:page-size="20"
							:current="params._pageNo"
							show-elevator
							@on-change="page_refresh"
						></Page>
					</Tooltip>
				</Col>
				<Loading :show="loading"></Loading>
			</Row>
		</Card>
	</div>
</template>

<script>
  import Mixin from './mixin'
  import Api from '~/api/faq'
  import Loading from '../my-components/loading/loading';
  import util from '~/libs/util'
  export default {
    mixins: [Mixin],
    name: 'cert-list',
    components: {
      Loading
    },
    data () {
      return {
        loading: false,
        statusMap: [
          {
            label: '草稿',
            value: 'DRAFT'
          },
          {
            label: '已发布',
            value: 'PUBLISHED'
          },
          {
            label: '编辑中',
            value: 'EDIT'
          },
          {
            label: '已删除',
            value: 'DELETED'
          }
        ],
        params: {
          top: '',
          faqTitle: '',
          pageTitle: '',
          createdDateStart: '',
          createdDateEnd: '',
          _pageNo: 1,
          _pageSize: 20,
          faqStatus: ''
        },
        columns: [
          {
            title: '常见问题',
            key: 'title',
            align: 'center'
          },
          {
            title: '关联文章',
            align: 'center',
            render: (h, params) => {
              if (!params.row.relatedTitle) return h('div', [])
              const list = params.row.relatedTitle.map(item => h('p', item))
              return h('div', list)
            }
          },
          {
            title: '帮助值',
            key: 'score',
            align: 'center',
            width: 80
          },
          {
            title: '置顶',
            align: 'center',
            width: 80,
            render: (h, params) => {
              return h('div', params.row.top ? '是' : '否')
            }
          },
          {
            title: '状态',
            key: 'status',
            width: 100,
            align: 'center',
            render: (h, params) => {
              const { status } = params.row
              const item = this.statusMap.find(item => item.value === status)
              if (!item) {
                return h('Tag',
                  {
                    style: {
                      align: 'center'
                    },
                    props: {
                      color: 'grey'
                    }
                  }, '--')
              }
              let color = 'green';
              if (status === 'DRAFT' || status === 'EDIT') {
                color = 'grey';
              }
              if (status === 'DELETED') {
                color = 'red';
              }
              return h('Tag', {
                style: {
                  align: 'center'
                },
                props: {
                  color: color
                }
              }, item.label)
            }
          },
          {
            renderHeader: (h, params) => {
              return h('div', [h('p', '创建时间'), h('p', '最后修改时间')]);
            },
            width: 150,
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('p', params.row.createdDate),
                h('p', params.row.lastModifiedDate)
              ]);
            }
          },
          {
            title: '操作',
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    domProps: {
                      id: 'btn_faq_edit_1'
                    },
                    directives: [
                      {
                        name: 'url',
                        value: { url: '/rest/doc/faq/detail' }
                      }
                    ],
                    on: {
                      click: () => {
                        this.lookDetail(params.row.id);
                      }
                    }
                  },
                  '查看'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    domProps: {
                      id: 'btn_faq_edit_2'
                    },
                    directives: [
                      {
                        name: 'url',
                        value: { url: '/rest/doc/faq/update' }
                      }
                    ],
                    on: {
                      click: () => {
                        this.aclFaq(params.row.id, () => {
                          this.editQuestion(params.row.id);
                        })
                      }
                    }
                  },
                  '编辑'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    domProps: {
                      id: 'btn_faq_edit_3'
                    },
                    directives: [
                      {
                        name: 'show',
                        value: params.row.status !== 'PUBLISHED'
                      },
                      {
                        name: 'url',
                        value: { url: '/rest/doc/faq/publish' }
                      }
                    ],
                    on: {
                      click: () => {
                        this.aclFaq(params.row.id, () => {
                          this.publish({
                            id: params.row.id
                          });
                        })
                      }
                    }
                  },
                  '发布'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    domProps: {
                      id: 'btn_faq_edit_4'
                    },
                    directives: [
                      {
                        name: 'url',
                        value: { url: '/rest/doc/faq/delete' }
                      }
                    ],
                    on: {
                      click: () => {
                        this.aclFaq(params.row.id, () => {
                          this.deleteFaq(params.row);
                        })
                      }
                    }
                  },
                  '删除'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    domProps: {
                      id: 'btn_faq_edit_5'
                    },
                    directives: [
                      {
                        name: 'show',
                        value: !params.row.top && params.row.status === 'PUBLISHED'
                      },
                      {
                        name: 'url',
                        value: { url: '/rest/doc/faq/set-top' }
                      }
                    ],
                    on: {
                      click: () => {
                        this.aclFaq(params.row.id, () => {
                          this.setTop({
                            id: params.row.id,
                            top: true
                          });
                        })
                      }
                    }
                  },
                  '置顶'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    domProps: {
                      id: 'btn_faq_edit_6'
                    },
                    directives: [
                      {
                        name: 'show',
                        value: params.row.top && params.row.status === 'PUBLISHED'
                      },
                      {
                        name: 'url',
                        value: { url: '/rest/doc/faq/set-top-cancel'}
                      }
                    ],
                    on: {
                      click: () => {
                        this.aclFaq(params.row.id, () => {
                          this.setTop({
                            id: params.row.id,
                            top: false
                          });
                        })
                      }
                    }
                  },
                  '取消置顶'
                )
              ]);
            }
          }
        ],
        list: [],
        pageTotal: NaN
      };
    },
    computed: {
      time () {
        if (!this.params.createdDateStart) {
          return []
        }
        return [this.params.createdDateStart, this.params.createdDateEnd]
      }
    },
    mounted () {
      const { startDate, endDate } = this.getDateRange()
      this.params.createdDateStart = startDate
      this.params.createdDateEnd = endDate
    },
    methods: {
      handleDateChange (date) {
        this.params.createdDateStart = date[0]
        this.params.createdDateEnd = date[1]
      },
      getDateRange () {
        const start = util.dateFormat(new Date(new Date().getTime() - 30 * 24 * 3600 * 1000), 'DAILY')
        const end = util.dateFormat(new Date(), 'DAILY')
        return {
          startDate: util.dateFormat_component(start),
          endDate: util.dateFormat_component_end(end)
        }
      },
      compareDate () {
        const start = new Date(this.params.notifyStartDate).getTime() + 7 * 24 * 3600 * 1000
        const end = new Date(this.params.notifyEndDate).getTime()
        return end > start
      },
      disabledDate (current) {
        return current.getTime() > new Date().getTime()
      },
      createdQuestion () {
        this.$router.push({
          path: '/faq/created'
        })
      },
      editQuestion (id) {
        this.$router.push({
          path: '/faq/editor',
          query: {
            id
          }
        })
      },
      lookDetail (id) {
        this.$router.push({
          path: '/faq/detail',
          query: {
            id,
            detail: true
          }
        })
      },
      search () {
        this.params._pageNo = 1
        this.getList();
      },
      // 检索条件初始化
      reset_search () {
        this.params = {
          top: '',
          faqTitle: '',
          pageTitle: '',
          createdDateStart: '',
          createdDateEnd: '',
          _pageNo: 1,
          _pageSize: 20,
          faqStatus: ''
        }
        const { startDate, endDate } = this.getDateRange()
        this.params.createdDateStart = startDate
        this.params.createdDateEnd = endDate
      },
      page_refresh (val) {
        if (val) {
          this.params._pageNo = val;
        }
        this.getList()
      },
      handleTop (top) {
        switch (top) {
          case 0:
            return false
          case 1:
            return true
          default:
            return ''
        }
      },
      getList () {
        Api.getList({
          ...this.params,
          top: this.handleTop(this.params.top)
        })
          .then(res => {
            if (res.data.status === 'success') {
              this.list = res.data.data.page.items
              if (
                res.data.data.page.items &&
                res.data.data.page.items.length > 0
              ) {
                if (res.data.data.page.items.length < 10) {
                  this.pageTotal = res.data.data.page.items.length;
                } else {
                  this.pageTotal = NaN;
                }
              } else {
                this.pageTotal = NaN;
              }
            } else {
              this.$ypMsg.notice_error(this, '错误', res.data.message);
            }
          })
      }
    }
  };
</script>

<style scoped>
</style>