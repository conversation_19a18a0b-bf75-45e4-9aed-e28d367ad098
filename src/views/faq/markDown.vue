<template>
	<div id="vditor"></div>
</template>

<script>
import Vditor from 'vditor'
import api from '../../api/api'
export default {
  props: ['value', 'disabled'],
  data() {
    return {
      vditor: null
    }
  },
  watch: {
    value: {
      handler(newValue, oldValue) {
        if(!oldValue && newValue) {
          this.vditor && this.vditor.setValue(newValue)
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initVditor()
    if(this.disabled) {
      this.vditor.disabled()
    }
  },
  methods: {
    initVditor() {
      const that = this;
      this.vditor = new Vditor('vditor',{
        toolbar: [
          "headings",
          "bold",
          "italic",
          "strike",
          "link",
          "|",
          "list",
          "ordered-list",
          "check",
          "outdent",
          "indent",
          "|",
          "quote",
          "line",
          "code",
          "inline-code",
          "|",
          "upload",
          "table",
          "|",
          "undo",
          "redo",
          "|",
          "edit-mode",
          "fullscreen",
          "help",
        ],
        placeholder: '',
        toolbarConfig: {
          pin: true,
        },
        cdn: 'https://img.yeepay.com/fe-resources/vditor',
        preview: {
          theme: {
            path: 'https://img.yeepay.com/fe-resources/vditor/dist/css/content-theme'
          }
        },
        focus: (val) => {
          this.$emit('focus', val)
          this.$nextTick(() => {
            this.$parent.$emit("on-form-focus")
          })
        },
        input: (val) => {
          this.$emit('input', val)
          this.$nextTick(() => {
            this.$parent.$emit("on-form-change")
          })
        },
        blur: (val) => {
          this.$emit('input', val)
          this.$nextTick(() => {
            this.$parent.$emit("on-form-blur")
          })
        },
        upload: {
          max: 10 * 1024 * 1024,
          handler(file) {
            let formData = new FormData();
            let fileName = '';
            for (let i in file) {
              formData.append('fileStream', file[i])
              fileName = file[i].name
            }
            api.yop_attachment_upload(formData)
            .then((response) =>{
                if(response.status === 'success'){
                  let imgMdStr = `![${fileName}](${localStorage.remoteIP}${response.data.fileUrl})`;
                  that.vditor.insertValue(imgMdStr)
                }else{
                  that.$ypMsg.notice_error(that,'图片上传错误',response.message,response.solution);
                }
              }
            )
          }
        },
        cache: {
          enable: false
        },
      })
    },
  }
}
</script>
<style lang="less">
	.vditor {
		border: 1px solid #ccc;
		margin-bottom: 10px;
		min-height: 400px;
	}
	.vditor-preview {
		padding: 0 20px;
		box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.1);
	}
</style>