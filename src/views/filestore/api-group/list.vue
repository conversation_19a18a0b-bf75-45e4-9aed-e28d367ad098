<style lang="less">
    @import '../../../styles/common.less';
    @import '../../API_Management_Open/api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="24">
                <Col span="6" >
                <Col  span="8">
                <Col class="margin-top-10" >API分组：&nbsp;</Col>
                </Col>
                <Col span="14">
                <!--<apigroup @on-update="updateSelect_apiGroup"> </apigroup>-->
                <common-select id="select_fs_1" ref="select_api" @on-update="updateSelect_apiGroup"
                               type="combo"
                               keyWord="result"
                               code="apiGroupCode"
                               title="apiGroupName"
                               group="apiGroup"
                               @on-loaded="select_callBack"
                               holder="请选择（默认全部）"
                               :default="data_select_apiGroup"
                               :uri="this.$store.state.select.apiGroup.uri"></common-select>
                </Col>
                </Col>

                <Col span="8" >
                <Col  span="6">
                <Col class="margin-top-10">创建时间：&nbsp;</Col>
                </Col>
                <date-picker ref="datepicker" @on-start-change="update_start_date"
                             @on-end-change="update_end_date"
                             :default_start="this.dateStart"
                             :default_end="this.dateEnd"
                ></date-picker>
                </Col>
                <Col class="margin-top-5" offset ="6" span="4" style="text-align: center">
                <Button class="margin-right-20" id='btn_fs_1' type="primary" v-url="{url:'/rest/filestore/api-group/list'}" @click="search_Interface">查询</Button>
                <Button  id='btn_fs_2' type="ghost" @click="search_reset">重置</Button>
                </Col>
                </Col>
                <Col class="margin-top-20" span = "8">
                <Button id='btn_fs_3' type="primary" v-url="{url:'/rest/filestore/api-group/create'}" @click="create_apiGroup">新增文件存储配置</Button>
                </Col>
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_fs_1' border ref="selection" :columns="columns_cephList" :data="data_cephList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="search_Interface_refresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_cephList"></loading>
            </Row>
            <Modal id="modal_fs_1" v-model="modal_create_apiGroup" width="600" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" v-show="create_orEdit">新增文件存储配置</span>
                    <span style="color:black" v-show="!create_orEdit">编辑文件存储配置</span>
                </p>
                <div>
                    <Form ref="formCustom" :model="formCustom" :rules="ruleCustom" :label-width="120">
                        <!--<p style="line-height:40px;"> <star_red></star_red>服务提供方：-->
                        <!--<serverSupplier size="small" @on-update="updateSelect_serviceSupplier" style="width:70%"> </serverSupplier>-->
                        <!--</p>-->
                        <FormItem label="API分组：" v-show="create_orEdit" prop="apiGroup">
                            <common-select id="modal_fs_select_1" ref="select_api_m" @on-update="updateSelect_apiGroup_inform"
                                           size="small"
                                           type="combo"
                                           keyWord="result"
                                           code="apiGroupCode"
                                           title="apiGroupName"
                                           group ='apiGroup'
                                           :uri="this.$store.state.select.apiGroup.uri"
                                           :default="formCustom.apiGroup"
                                           style="width:80%"
                            ></common-select>
                        </FormItem>
                        <FormItem label="API分组：" v-show="!create_orEdit">
                            <p >{{formCustom.apiGroupName}}({{formCustom.apiGroup}})</p>
                        </FormItem>
                        <FormItem label="后端存储类型：" prop="ceph">
                            <common-select id="modal_fs_select_2" ref="select_ceph_m" @on-update="updateSelect_ceph"
                                           size="small"
                                           type="code"
                                           keyWord="result"
                                           code="code"
                                           title="name"
                                           group="ceph"
                                           :default="formCustom.ceph"
                                           :uri="this.$store.state.select.ceph.uri" style="width:80%"></common-select>
                        </FormItem>

                        <FormItem v-show="create_orEdit" label="密钥：" prop="secretKey_create">
                            <Input id="modal_fs_input_1" type="password" size="small" v-model="formCustom.secretKey_create" style="width:80%"></Input>
                        </FormItem>
                        <FormItem v-show="!create_orEdit" label="密钥：">
                            <Input id="modal_fs_input_2" type="password" size="small" v-model="formCustom.secretKey_modify" style="width:80%"></Input>
                        </FormItem>
                        <FormItem label="Buckets：" prop="buckets">
                            <Input id="modal_fs_input_3" type="text" size="small" v-model="formCustom.buckets" style="width:80%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">后端存储对象中的桶&nbsp;<a :href="href_link" target="view_window">变量取值参考>></a></p>
                        <FormItem label="文件名：" prop="fileName">
                            <Input id="modal_fs_input_4" type="text" size="small" v-model="formCustom.fileName" style="width:80%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">通过变量拼接文件名，用于文件存储和下载&nbsp;<a :href="href_link" target="view_window">变量取值参考>></a></p>
                        <FormItem label="默认配置：" prop="default">
                            <RadioGroup v-model="formCustom.default" @on-change="defaultChange">
                                <Radio id="modal_fs_rbtn_1" label="否"></Radio>
                                <Radio id="modal_fs_rbtn_2" label="是"></Radio>
                            </RadioGroup>
                        </FormItem>
                    </Form>

                </div>
                <div slot="footer">
                    <Button id="modal_fs_btn_2" type="primary" @click="ok_create_apiGroup('formCustom')">确定</Button>
                    <Button id="modal_fs_btn_1" type="ghost"  @click="cancel_create_apiGroup">取消</Button>
                </div>
                <loading :show="show_loading_createCeph"></loading>
            </Modal>
        </Card>
    </div>
</template>

<script>
    import apigroup from '../../common-components/select-components/selectApiGroup'
    import serverSupplier from '../../common-components/select-components/selectServiceSupplier'
    import datePicker from '../../common-components/date-components/date-picker'
    import loading from '../../my-components/loading/loading'
    import star_red from '../../common-components/icon-components/star_red'
    import commonSelect from '../../common-components/select-components/selectCommon'
    import util from '../../../libs/util'
    import api from '../../../api/api'
    export default {
        name: 'ceph_config',
        components:{
            apigroup,
            serverSupplier,
            datePicker,
            loading,
            star_red,
            commonSelect
        },
        data(){
            const validate_notNull = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('输入内容不能为空'));
                }else{
                    callback();
                }
            };
            return {
                // 新建页面loading
                show_loading_createCeph : false,
                // 变量取值参考链接
                href_link : 'http://wiki.yeepay.com/pages/viewpage.action?pageId=53119675',
                // 开始日期
                dateStart: '',
                // 结束日期
                dateEnd:'',
                // 与表相关的下拉框数目
                count_select_related: 1,
                // 新建编辑状态 true：新建 false编辑
                create_orEdit: true,
                // api分组选择数据绑定
                data_select_apiGroup : '',
                // 服务提供方选择数据绑定
                data_select_serviceSupplier : '',
                // 分页总数
                pageTotal: 0,
                // 当前页数
                pageNo : 1,
                // 表格头数据绑定
                columns_cephList: [
                    {
                        title: 'API分组',
                        width: 130,
                        align:'center',
                        render:(h,params) =>{
                            return h('div',[
                                h('p',params.row.apiGroupName),
                                h('p','('+params.row.apiGroupCode+')')
                            ])
                        }
                    },
                    {
                        title: '后端存储类型',
                        width: 110,
                        align: 'center',
                        key: 'storage_type'
                    },
                    {
                        title: '配置',
                        key: 'config',
                        'min-width' : 130,
                        render:(h,params) =>{
                            return h('div',[
                                h('p',[
                                    h('label',{
                                        style:{
                                            fontWeight: 'bold',
                                        }
                                    },'密钥 :'),
                                    h('span',' '+params.row.secretKey),
                                ]),h('p',[
                                    h('span',{
                                        style:{
                                            fontWeight: 'bold',
                                        }
                                    },'Buckets :'),
                                    h('span',' '+params.row.Buckets)
                                ]),h('p',[
                                    h('label',{
                                        style:{
                                            fontWeight: 'bold',
                                        }
                                    },'文件名 :'),
                                    h('span',' '+params.row.fileName)
                                ])
                            ])
                        }
                    },
                    {
                        title: 'API分组默认配置',
                        key: 'default',
                        align:  'center',
                        width : 140,
                        render:(h,params) =>{
                            let default_config = '否'
                            if(params.row.default) default_config ='是'
                            return h('div',default_config)
                        }
                    },
                    {
                        renderHeader: (h,params) =>{
                            return h ('div',[
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ])
                        },
                        width:160,
                        align: 'center',
                        render: (h,params) => {
                            return h('div',[
                                h('p',params.row.lastCreateTime),
                                h('p',params.row.lastModifyTime)]
                            )
                        }
                    },
                    {
                        title: '操作',
                        key: 'operations',
                        width: 120,
                        align:'center',
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/filestore/api-group/update'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.apiGroup_modify(params.row.id);
                                        }
                                    }
                                }, '编辑'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/filestore/delete'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.apiGroup_delete(params.row.id,params.row.default);
                                        }
                                    }
                                }, '删除')


                            ]);
                        }


                    }
                ],
                // 表格数据绑定
                data_cephList:[
                    // {
                    //     apiGroupCode: 'apiGroupCode',
                    //     apiGroupName: 'apiGroupName',
                    //     storage_type: 'Ceph',
                    //     secretKey: 'nam***123',
                    //     Buckets: '${api.group}',
                    //     fileName: '${api.biz}-${context.appkey}-${sys.now}-${header.requestId}',
                    //     default: true,
                    //     lastCreateTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-03 18:40:09'
                    // },
                    // {
                    //     apiGroupCode: 'apiGroupCode',
                    //     apiGroupName: 'apiGroupName',
                    //     storage_type: 'Ceph',
                    //     secretKey: 'nam***123',
                    //     Buckets: '${api.group}',
                    //     fileName: '${api.biz}-${context.appkey}-${sys.now}-${header.requestId}',
                    //     default: false,
                    //     lastCreateTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-03 18:40:09'
                    // }
                ],
                // 加载动画数据绑定
                show_loading_cephList: false,
                // 新增api分组窗口显示
                modal_create_apiGroup: false,
                formCustom: {
                    id:'',
                    ceph: '',
                    apiGroup:'',
                    apiGroupName: '',
                    secretKey_create:'',
                    secretKey_modify:'',
                    buckets:'${api.group}',
                    fileName:'${api.biz}-${context.appkey}-${sys.now}-${header.requestId}',
                    default: '否',
                    version:''
                },
                ruleCustom: {
                    buckets: [
                        {required :true, message :'buckets不能为空'},
                        { validator: validate_notNull, trigger: 'blur' }
                    ],
                    fileName: [
                        {required :true, message :'文件名不能为空'},
                        { validator: validate_notNull, trigger: 'blur' }
                    ],
                    ceph:[
                        {required :true, message :'后端存储类型不能为空', trigger: 'change'},
                    ],
                    apiGroup:[
                        {required :true, message :'api分组不能为空', trigger: 'change'}
                    ],
                    default: [
                        {required :true, message :'默认配置不能为空'},
                    ],
                    secretKey_create :[
                        {required :true, message :'创建时密钥不能为空'},
                        { validator: validate_notNull, trigger: 'blur' }

                    ]
                }
            }
        },
        methods:{
            // 页面初始化
            init(){
                if(this.$store.state.select.apiGroup.data.length > 0){
                    this.data_select_apiGroup = this.$store.state.current_apiGroup;
                    this.search_Interface();
                }
            },
            // 页面请求
            pageRefresh (param){
                let params = Object
                if(param){
                    params  = param
                }else{
                    params = {
                        _pageSize: 10,
                        _pageNo : 1
                    }
                }
                util.paramFormat(params);
                api.yop_ceph_list(params).then(
                    (response) =>{
                        let status = response.data.status
                        if(response.data.status === 'success'){
                            this.tableDataFormat(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                            this.pageTotal = response.data.data.page.totalPageNum * 10;
                            this.show_loading_cephList = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_cephList = false;
                        }
                    }
                )
            },
            // 表格数据处理
            tableDataFormat (items) {
                this.data_cephList = [];
                if(items && items.length > 0){
                    items.forEach(
                        (item) =>{
                            this.data_cephList.push({
                                id: item.id,
                                apiGroupCode: item.apiGroupCode,
                                apiGroupName: this.apiGroup_nameGet(item.apiGroupCode),
                                storage_type: item.storeType,
                                secretKey: item.storeConfig.secretKey,
                                Buckets: item.storeConfig.bucket,
                                fileName: item.storeConfig.fileName ,
                                default: item.isDefault,
                                lastCreateTime : item.createdDate,
                                lastModifyTime : item.lastModifiedDate
                            });
                        }
                    )
                }
            },
            // api分组名称处理
            apiGroup_nameGet (code) {
                let group = this.$store.state.select['apiGroup'].data
                for(var i in group){
                    if(group[i].value === code){
                        return  group[i].name
                    }
                }
            },
            // 页面查询按钮
            search_Interface (){
                this.show_loading_cephList =true;
                let param = {
                    apiGroupCode: this.data_select_apiGroup,
                    createdStartDate: util.dateFormat_component(this.dateStart),
                    createdEndDate: util.dateFormat_component_end(this.dateEnd),
                    _pageSize : 10,
                    _pageNo: 1
                }
                util.paramFormat(param);
                this.pageRefresh(param);
            },
            // 页面查询按钮
            search_Interface_refresh (val){
                this.show_loading_cephList =true;
                let param = {
                    apiGroup: this.data_select_apiGroup,
                    createdStartDate: util.dateFormat_component(this.dateStart),
                    createdEndDate: util.dateFormat_component_end(this.dateEnd),
                    _pageSize : 10,
                    _pageNo: 1
                }
                if (val) {
                    param._pageNo = val;
                }
                this.pageRefresh(param);
            },
            // 查询重置按钮
            search_reset (){
                this.$refs.select_api.resetSelected();
                // this.data_select_apiGroup = '';
                this.$refs.datepicker.reset();
                this.dateStart = '';
                this.dateEnd = '';
            },
            // 页面重置按钮
            reset_Interface () {
                this.formCustom.id ='';
                this.$refs.select_api_m.resetSelected();
                this.$refs.select_ceph_m.resetSelected();
                // this.formCustom.ceph = '';
                this.formCustom.apiGroup = '';
                this.formCustom.ceph = '';
                this.formCustom.apiGroupName = '';
                this.formCustom.secretKey_create = '';
                this.formCustom.secretKey_modify = '';
                this.formCustom.buckets = '${api.group}';
                this.formCustom.fileName = '${api.biz}-${context.appkey}-${sys.now}-${header.requestId}c';
                this.formCustom.default = '否';
                this.formCheck_reset();
            },
            // 同步更新选择api分组函数
            updateSelect_apiGroup (val) {
                this.data_select_apiGroup = val;
                this.formCustom.apiGroup =val;
            },
            updateSelect_apiGroup_inform (val){
                this.formCustom.apiGroup =val;
            },
            // 同步更新选择后端存储类型
            updateSelect_ceph (val) {
                this.formCustom.ceph =val;
            },
            // api分组修改
            apiGroup_modify (id){
                // this.formCustom.apiGroup = 'default';
                this.formCustom.secretKey_create = 'default';
                this.create_orEdit = false;
                this.modal_create_apiGroup =true;
                api.yop_ceph_detail_single({id:id}).then(
                    (response)=>{
                        if (response.data.status === 'success') {
                        let result = response.data.data.result;
                        //处理逻辑补充
                        this.formCustom.id = result.id;
                        this.formCustom.version = result.version;
                        this.formCustom.ceph = result.storeType;
                        this.formCustom.apiGroupName = this.apiGroup_nameGet(result.apiGroupCode);
                        this.formCustom.apiGroup = result.apiGroupCode;
                        this.formCustom.buckets = result.storeConfig.bucket;
                        this.formCustom.fileName = result.storeConfig.fileName;
                        this.formCustom.default = this.default_format2C(result.isDefault);
                        } else {
                            this.$Modal.error({
                                title: '文件存储详情获取错误',
                                content: response.data.message
                            });
                            this.modal_create_apiGroup =false;
                        }
                    }
                )
            },
            // api分组删除
            apiGroup_delete (id, isDefault) {

                this.$Modal.confirm({
                    title: '删除文件存储配置',
                    content: '<p style=\'color:red\'>删除文件存储配置，会影响此文件配置操作上传或下载！</p><p>确定删除该文件存储配置吗？</p>',
                    'ok-text': '删除',
                    onOk: () => {
                        api.yop_ceph_delete({id: id}).then(
                            (response) => {
                                if (response.status === 'success') {
                                    this.$ypMsg.notice_success(this,'删除成功');
                                    this.search_Interface();
                                } else {
                                    this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                }
                            }
                        );
                    }
                });

            },
            // 新增api分组
            create_apiGroup () {
                this.create_orEdit = true;
                this.reset_Interface();
                this.show_loading_createCeph =true;
                // this.formCustom.apiGroup = this.data_select_apiGroup;
                let that = this
                setTimeout(function () {
                    that.formCustom.apiGroup = that.data_select_apiGroup;
                    that.show_loading_createCeph =false;
                }, 300);

                this.modal_create_apiGroup =true;
            },
            // 新增api分组确定按钮
            ok_create_apiGroup (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        if(this.create_orEdit){
                            let param = {
                                apiGroupCode : this.formCustom.apiGroup,
                                bucket : this.formCustom.buckets,
                                secretKey : this.formCustom.secretKey_create,
                                storeType : this.formCustom.ceph,
                                fileName : this.formCustom.fileName,
                                isDefault : this.default_format(this.formCustom.default)
                            }
                            this.create_request(param);
                        }else{
                            let param = {
                                id: this.formCustom.id,
                                bucket : this.formCustom.buckets,
                                secretKey : this.formCustom.secretKey_modify,
                                storeType : this.formCustom.ceph,
                                fileName : this.formCustom.fileName,
                                isDefault : this.default_format(this.formCustom.default),
                                version : this.formCustom.version
                            }
                            //可能需要对密钥处理剔除
                            api.yop_ceph_update(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        this.$ypMsg.notice_success(this,'修改成功');
                                        this.search_Interface();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    }
                                }
                            )
                            this.modal_create_apiGroup = false;
                        }

                    } else {
                        this.$Message.error('请检查');
                    }
                })

            },
            // 创建请求
            create_request (param) {
                api.yop_ceph_add(param).then(
                    (response) =>{
                        //需要填写
                        if(response.status === 'success'){
                            if(!this.isEmptyObject(response.data)){
                                this.$Modal.confirm({
                                    title: '提示',
                                    content: '该API分组已存在默认配置存储文件<br/>确认替换默认存储配置？',
                                    'ok-text':'确定',
                                    onOk: () =>{
                                        let param = {
                                            apiGroupCode : this.formCustom.apiGroup,
                                            bucket : this.formCustom.buckets,
                                            secretKey : this.formCustom.secretKey_create,
                                            storeType : this.formCustom.ceph,
                                            fileName : this.formCustom.fileName,
                                            isDefault : this.default_format(this.formCustom.default),
                                            force: true
                                        }
                                        this.create_request(param);
                                        this.modal_create_apiGroup = false;
                                    },
                                    onCancel: ()=>{
                                        this.formCustom.default = '否';
                                    }
                                });
                            }else{
                                this.$ypMsg.notice_success(this,'创建成功');
                                this.modal_create_apiGroup = false;
                                this.search_Interface();
                            }
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                    }
                )
            },
            // 判断是否为空对象
            isEmptyObject (obj) {
                debugger;
                for (var key in obj) {
                    return false;
                };
                return true;
            },
    // 默认返回处理
            default_format (value) {
                if(value === '是'){
                    return true;
                }else{
                    return false;
                }
            },
            // 默认反向返回处理
            default_format2C (value) {
                if(value){
                    return '是'
                }else{
                    return '否'
                }
            },
            // 新增api分组取消按钮
            cancel_create_apiGroup () {
                this.modal_create_apiGroup = false;
                this.reset_Interface();
            },
            // 下拉框加载完处理函数
            select_callBack(){
                this.count_select_related--;
                if(this.$store.state.select.apiGroup.data.length > 0){
                    this.data_select_apiGroup = this.$store.state.current_apiGroup;
                }
                if(this.count_select_related === 0){
                    this.search_Interface();
                }
            },
            // 开始日期更新
            update_start_date (val) {
                this.dateStart = val;
            },
            // 结束日期更新
            update_end_date (val) {
                this.dateEnd = val;
            },
            // 表单状态还原
            formCheck_reset(){
                this.$refs.formCustom.resetFields();
            },
            // 编辑时默认配置变更
            defaultChange (val) {
                if(!this.create_orEdit){
                    if(val === '否'){
                        this.$Modal.warning({
                            title: '警告',
                            content: '该配置为默认文件存储配置，如需更新，请先将其他配置设置为默认配置！'
                        });
                        this.formCustom.default = '是';
                    }
                }
            }
        },
        mounted () {
            this.init();
        },
        beforeRouteLeave (to, from, next) {
            this.$store.state.current_apiGroup ='';
            this.$destroy();
            next();
        },
    };
</script>

<style scoped>

</style>
