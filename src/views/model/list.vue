<style lang="less">
.panel-content {
  margin-top: 8px;
  word-break: break-word;
  padding-right: 10px;
}
.reasonType {
    vertical-align: top;
    display: inline-block;
    font-size: 14px;
    margin-top: 5px;
}
</style>
<template>
<Row>
    <Card dis-hover>
        <Row type="flex" align="middle">
            <Col span="20">
                <!-- <Col span="7" >
                    <Col span="7" class="margin-top-10">
                    <span >API URI:</span>
                    </Col>
                    <Col span="17" >
                    <Input id='input_apiM_1' class="margin-top-5" clearable v-model="data_interface_uri" placeholder="API URI" @on-enter="search_Interface"></Input>
                    </Col>
                </Col>-->
                <Col span="7">
                <Col span="7" class="margin-top-10">
                    <span>API分组:</span>
                </Col>
                <Col span="17">
                    <Select
                    ref="select_apiM_1"
                    id="select_apiM_1"
                    class="margin-top-5"
                    v-model="data_select_apiGroup"
                    filterable
                    clearable
                    placeholder="请选择（默认全部）"
                    >
                    <Option
                        v-for="item in data_apiGroup_List"
                        :value="item.value"
                        :key="item.value"
                    >{{ item.label }}</Option>
                    </Select>
                </Col>
                </Col>
                <Col offset="1" span="7">
                <Col span="7" class="margin-top-10">
                    <span>模型名称:</span>
                </Col>
                <Col span="17">
                    <Input
                    id="input_apiM_2"
                    class="margin-top-5"
                    clearable
                    v-model="data_interface_name"
                    placeholder="模型名称"
                    @on-enter="search_Interface"
                    ></Input>
                </Col>
                </Col>
                
                <Col offset="1" span="7">
                <!-- <Col span="7" class="margin-top-10">
                    <span>描述:</span>
                </Col> -->
                <!-- <Col span="17">
                    <Input
                    id="input_apiM_2"
                    class="margin-top-5"
                    clearable
                    v-model="data_interface_description"
                    placeholder="描述"
                    @on-enter="search_Interface"
                    ></Input>
                </Col> -->
                </Col>
                <!-- <Col class="margin-top-5" span="7" offset="1">
                    <Col span="7" class="margin-top-10">
                    <span >状态:</span>
                    </Col>
                    <Col span="17" >
                    <Select id='select_apiM_2' class="margin-top-5" v-model="data_select_status" placeholder="请选择（默认全部）" clearable>
                        <Option v-for="item in data_status_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                    </Col>
                    </Col>
                    <Col class="margin-top-5" offset="1" span="7" >
                        <Col span="7" class="margin-top-10">
                            <span >API类型:</span>
                            </Col>
                            <Col span="17" >
                            <Select id='select_apiM_4' class="margin-top-5" v-model="data_select_apiType"  placeholder="请选择（默认全部）" clearable>
                                <Option v-for="item in data_apiType_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                            </Select>
                            </Col>
                </Col>-->
                <Col class="margin-top-5" offset="1" span="7"></Col>
            </Col>
            <Col span="4">
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button
                    id="btn_apiM_1"
                    type="primary"
                    @click="search_Interface"
                >查询</Button>
                </Col>
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id="btn_apiM_2" type="ghost" @click="reset_Interface">重置</Button>
                </Col>
            </Col>
        </Row>
        <Row>
            <Col span="24">
            <!-- v-url="{url:'/rest/model/create'}" -->

            <Button class="margin-bottom-10 margin-top-10" style="float: left;" type="primary"
            v-url="{url:'/rest/model/create'}"
                    @click="new_param('modal_param_content_model_external',false)">新增模型
            </Button>
            <Button class="margin-right-10  margin-top-10 margin-left-5" style="float: left;" type="success"
                    @click="toChangeRecode">模型变更记录
            </Button>
            </Col>
        </Row>
        <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_1' border ref="selection" :columns="columns_ModalInterfaceList" :data="data_ModalInterfaceList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <!--<Spin fix v-show="show_loading_apiList">-->
                <!--<Icon type="load-c" size=18 class="yop-spin-icon-load"></Icon>-->
                <!--<div>数据加载中...</div>-->
                <!--</Spin>-->
                <!-- <loading :show="show_loading_apiList"></!-->
        </Row>
    
        <Modal v-model="delete_modal_show" width="500">
            <p slot="header" style="color:#000;text-align:left;font-size: 18px;">
                <Icon type="ios-information-circle"></Icon>
                <span>删除模型</span>
            </p>
            <div style="text-align:left;margin-left:20px;font-size: 12px;">
                <p style="color:red">请确保没有指向模型的引用，否则可能会导致文档或sdk生成失败</p>
                <label for="" class="reasonType"><span style="color:red">*</span>原因：</label>
                <Input type="textarea" v-model="cause" style="width:85%;font-size: 12x;" placeholder="请输入原因"></Input>
            </div>
            <div slot="footer">
                <Button type="primary"  @click="sure_Delete_model">确定</Button>
                <Button type="primary"  @click="hide_Delete_model">取消</Button>
            </div>
        </Modal>


        
    </Card>
    <modal_opt_record ref="modal_opt_record"></modal_opt_record>
    <modal_param_content_model_external ref="modal_param_content_model_external" :index="current_index_external" @data_update="data_update_model_external"></modal_param_content_model_external>
    <CopyModel ref="copy_model" @data_update="data_update_model_external" @page_refresh="pageRefresh"/>
</Row>
</template>

<script>
/* eslint-disable no-console, camelcase, eqeqeq, no-redeclare */
import loading from '../my-components/loading/loading';
// import param_content from './modify_models/api-components/param_content';
import modal_param_content_model_external from './modify_models/modal_param_content_model_external';
import modal_opt_record from './modify_models/modal_change_record';
import CopyModel from './modify_models/copyModel';

import util from '../../libs/util'

import api from '../../api/api';

export default {
  name: 'model',
  components: {
    loading,
    // param_content,
    modal_param_content_model_external,
    modal_opt_record,
    CopyModel
  },
  data () {
    return {

      delete_modal_show: false,
      modal_Show: false,
      // modal表格
      columns_ModalInterfaceList: [
        // {
        //     type: 'selection',
        //     width: 60,
        //     align: 'center'
        // },
        // {
        //     title: 'id',
        //     width: 80,
        //     key: 'id',
        //     align: 'center',
        // },
        // {
        //     title: 'version',
        //     width: 80,
        //     key: 'version',
        //     align: 'center',
        // },
        {
          title: 'API分组',
          key: 'apiGroup',
          width: 150,
          align: 'center',
          render: (h, params) => {
            var apiGroupName = ''
            for (var i in this.data_apiGroup_List) {
              if (this.data_apiGroup_List[i].value == params.row.apiGroup) {
                apiGroupName = this.data_apiGroup_List[i].name
                break;
              }
            }
            return h('div', [h('p', apiGroupName), h('p', '(' + params.row.apiGroup + ')')]);
          }

        },
        {
          title: '模型名称',
          key: 'name',
          width: 240,
          align: 'center'
        },

        {
          title: '模型描述',
          key: 'description',
          align: 'center'
        },
        {
          title: '更新日期/创建日期',
          key: 'time',
          align: 'center',
          width: 200,
          render: (h, params) => {
            return h('div', [h('p', params.row.createdDateTime),
              h('p', params.row.lastModifiedDateTime)]);
          }
        },
        {
          title: '操作',
          key: 'option',
          width: 300,

          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  name: 'url',
                  value: {url: '/rest/model/detail'}
                }],
                on: {
                  click: () => {
                    // this.clear_selected_role(params.row.name)
                    // this.clear_selected_role_real(params.row.typeCode);
                    this.detail_model(params.row)
                  }
                }
              }, '详情'),
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  name: 'url',
                  value: {url: '/rest/model/update'}
                }],
                on: {
                  click: () => {
                    // this.clear_selected_role(params.row.name)
                    // this.clear_selected_role_real(params.row.typeCode);
                    this.edit_model(params.row)
                  }
                }
              }, '编辑'),
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  name: 'url',
                  value: {url: '/rest/model/delete'}
                }],
                on: {
                  click: () => {
                    // this.clear_selected_role(params.row.name)
                    // this.clear_selected_role_real(params.row.typeCode);
                    this.delete_model(params.row.id)
                  }
                }
              }, '删除'),
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  name: 'url',
                  value: {url: '/rest/model/change-record'}
                }],
                on: {
                  click: () => {
                    this.toChangeRecode(params.row)
                  }
                }
              }, '变更记录'),
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  name: 'url',
                  value: {url: '/rest/model/change-record'}
                }],
                on: {
                  click: () => {
                    this.copyModel(params.row)
                  }
                }
              }, '复制')
              
            ]);
          },
          align: 'center'
        }
      ],
      // 表格数据
      data_ModalInterfaceList: [],
      // pageTotal
      pageTotal: 0,
      pageSize: 10,
      // pageNo
      pageNo: 1,

      // 模型名称
      data_interface_name: '',
      // 描述
      data_interface_description: '',
      // api分组下拉框数据绑定
      data_select_apiGroup: '',
      // api分组下拉框数据
      data_apiGroup_List: [],
      // 临时变量
      data_test: {
        // "AuthIdCardResultDTO": {
        //     "title": "响应结果",
        //     "type": "object",
        //     "properties": {
        //         "fee": {
        //             "title": "未命名",
        //             "type": "number",
        //             "format": "double",
        //             "x-yop-end-param-name": "fee"
        //         },
        //         "cost": {
        //             "title": "未命名",
        //             "type": "number",
        //             "format": "double",
        //             "x-yop-end-param-name": "cost"
        //         },
        //         "name": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "name"
        //         },
        //         "photo": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "photo"
        //         },
        //         "remark": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "remark"
        //         },
        //         "status": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "status"
        //         },
        //         "address": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "address"
        //         },
        //         "orderId": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "orderId"
        //         },
        //         "authType": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "authType"
        //         },
        //         "encryptMsg": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "encryptMsg"
        //         },
        //         "idCardNumber": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "idCardNumber"
        //         },
        //         "invokeRecords": {
        //             "title": "未命名",
        //             "type": "array",
        //             "items": {
        //                 "type": "string"
        //             },
        //             "x-yop-end-param-name": "invokeRecords"
        //         },
        //         "requestFlowId": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestFlowId"
        //         },
        //         "authInterfaceId": {
        //             "title": "未命名",
        //             "type": "integer",
        //             "format": "int32",
        //             "x-yop-end-param-name": "authInterfaceId"
        //         },
        //         "bottomInterface": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "bottomInterface"
        //         },
        //         "externalOrderId": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "externalOrderId"
        //         },
        //         "channelReturnMsg": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "channelReturnMsg"
        //         },
        //         "authInterfaceType": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "authInterfaceType"
        //         },
        //         "channelReturnCode": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "channelReturnCode"
        //         }
        //     },
        //     "description": "响应结果"
        // },
        // "RequestIdCardAuthDTO": {
        //     "title": "方法签名第0个参数",
        //     "type": "object",
        //     "properties": {
        //         "name": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "name"
        //         },
        //         "authType": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "authType"
        //         },
        //         "requestIP": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestIP"
        //         },
        //         "authMethod": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "authMethod"
        //         },
        //         "encryptMsg": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "encryptMsg"
        //         },
        //         "channelName": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "channelName"
        //         },
        //         "excludePhoto": {
        //             "title": "未命名",
        //             "type": "boolean",
        //             "x-yop-end-param-name": "excludePhoto"
        //         },
        //         "idCardNumber": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "idCardNumber"
        //         },
        //         "requestFlowId": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestFlowId"
        //         },
        //         "requestSystem": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestSystem"
        //         },
        //         "requestSystemId": {
        //             "title": "未命名",
        //             "type": "integer",
        //             "format": "int32",
        //             "x-yop-end-param-name": "requestSystemId"
        //         },
        //         "requestCustomerId": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestCustomerId"
        //         },
        //         "requestIdentification": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestIdentification"
        //         },
        //         "repositoryAvailableDays": {
        //             "title": "未命名",
        //             "type": "integer",
        //             "format": "int32",
        //             "x-yop-end-param-name": "repositoryAvailableDays"
        //         }
        //     },
        //     "description": "方法签名第0个参数，请自行修改arg0等参数的名字"
        // }
      },
      // json表格的数据
      data_model: [],
      // 当前行
      current_index: -1,
      // 外部当前行
      current_index_external: -1,
      // 当前展示的模型
      current_model: [],
      // 当前被编辑模型
      current_modify_model: [],
      current_params: {
        pageNo: 1,
        pageSize: 10
      },
      propertiesDetails: {},
      nowId: '',
      nowVersion: '',
      cause: '',
      pageSize: 10
    };
  },
  methods: {

    //   列表刷新
    pageRefresh (val) {
      this.current_params.pageNo = val;
      var param = {
        name: this.data_interface_name,
        apiGroup: this.data_select_apiGroup,
        description: this.data_interface_description,
        pageNo: val,
        pageSize: 10

      }
      api.yop_modalManagement_modal_list(this.current_params).then(
        (response) => {
          var response = response.data
          if (response.status == 'success') {
            this.tabledataGet(response.data.page.items);
            this.pageNo = response.data.page.pageNo;
            if (response.data.page.items) {
              if (response.data.page.items.length < 10) {
                this.pageTotal = response.data.page.items.length;
              } else {
                this.pageTotal = NaN;
              }
            } else {
              this.pageTotal = NaN;
            }

            // this.show_loading_apiList = false ;
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      )
    },
    tabledataGet (item) {
      this.data_ModalInterfaceList = item
    },
    // 查询列表
    search_Interface () {
      let paramsTemp = {
        name: this.data_interface_name.trim(),
        description: this.data_interface_description,
        // apiType: this.data_select_apiType,
        apiGroup: this.data_select_apiGroup,
        // status: this.data_select_status,
        pageNo: 1,
        pageSize: 10
      };
      if (this.data_interface_name === '') {
        paramsTemp['apiTitle'];
      }

      if (this.data_select_apiType === '') {
        delete paramsTemp['apiType'];
      }
      if (this.data_select_apiGroup === '') {
        delete paramsTemp['apiGroupCode'];
      }
      if (this.data_select_status === '') {
        delete paramsTemp['status'];
      }
      if (this.data_safety_request === '') {
        delete paramsTemp['securityReq'];
      }
      this.current_params = paramsTemp;
      // 查询modal列表
      api.yop_modalManagement_modal_list(paramsTemp).then(response => {
        var response = response.data
        this.tabledataGet(response.data.page.items);
        this.pageNo = response.data.page.pageNo;
        if (response.data.page.items) {
          if (response.data.page.items.length < 10) {
            this.pageTotal = response.data.page.items.length;
          } else {
            this.pageTotal = NaN;
          }
        } else {
          this.pageTotal = NaN;
        }
      });
    },
    // 重置函数
    reset_Interface () {
      this.$refs.select_apiM_1.clearSingleSelect();
      this.data_interface_description = '';
      this.data_interface_name = '';
      // this.data_select_apiType = '';
      this.data_select_apiGroup = '';
      // this.data_select_status = '';
      this.data_safety_request = '';
      this.current_status = {
        pageNo: 1,
        pageSize: 10
      };
    },
    // 页面初始化
    init () {
      this.getModalList();
      // api下拉框列表
      api.yop_dashboard_apis_list().then(
        (response) => {
          this.data_apiGroup_List = [];
          if (this.current_status === 'create') {
            this.api_group_model = '';
          }
          let apigroup = response.data.data.result;
          // this.api_group_model= apigroup[0].groupCode;
          for (var i in apigroup) {
            this.data_apiGroup_List.push(
              {
                label: apigroup[i].apiGroupCode + '(' + apigroup[i].apiGroupName + ')',
                value: apigroup[i].apiGroupCode,
                name: apigroup[i].apiGroupName
              }
            )
          }
        }
      );
      // this.data_model = this.data_rehandler(this.data_test);
      // if(this.data_model && this.data_model.length > 0){
      //     this.current_model = [];
      //     this.current_model.push(this.data_model[0].param)
      // }
    },
    getModalList () {
      var param = {
        name: this.data_interface_name,
        apiGroup: this.data_select_apiGroup,
        description: this.data_interface_description,
        pageNo: this.current_params.pageNo,
        pageSize: this.current_params.pageSize
      }
      api.yop_modalManagement_modal_list(param).then(
        (response) => {
          this.tabledataGet(response.data.data.page.items);
          if (response.data.data.page.items) {
            if (response.data.data.page.items.length < 10) {
              this.pageTotal = response.data.data.page.items.length;
            } else {
              this.pageTotal = NaN;
            }
          } else {
            this.pageTotal = NaN;
          }
        }
      )
    },
    // 本地数据获取
    // setRequestData (data) {
    //     this.data_model =[] ;
    //     this.data_model = this.data_rehandler(data);
    //     if(this.data_model && this.data_model.length > 0){
    //         this.current_model = [];
    //         this.current_model.push(this.data_model[0].param)
    //     }
    // },
    // 设置当前查看model
    setCurrentModel (name) {
      this.current_model = [];
      this.current_model.push(name)
    },
    // model数据显示处理
    data_rehandler (data) {
      let result = []
      for (var i in data) {
        let temp = {
          param: i,
          type: []
        }
        for (var j in data[i]) {
          if (j === 'properties') {
            temp[j] = this.sub_data_rehandler(data[i][j]);
          } else if (util.swagger_properties[j] === 'type') {
            temp['type'][0] = data[i][j];
          } else {
            temp[util.swagger_properties[j]] = data[i][j];
          }
        }
        result.push(temp)
      }
      return result
    },
    // 子数据处理
    sub_data_rehandler (data) {
      let result = []
      for (var i in data) {
        let temp = {
          param: i,
          type: []
        }
        for (var j in data[i]) {
          if (util.swagger_properties[j] === 'type') {
            temp['type'][0] = data[i][j];
            if (data[i][j] === 'object') {
              if (data[i]['additionalProperties']) {
                temp['type'][1] = 'map';
                temp['children'] = this.data_rehandler(data[i]['additionalProperties'])
              }
            } else if (data[i][j] === 'array') {
              if (data[i]['items']) {
                temp['type'][1] = 'array';
                temp['children'] = this.data_rehandler(data[i]['items'])
              }
            }
          } else if (util.swagger_properties[j] === 'format') {
            temp['type'][1] = data[i][j];
          } else {
            temp[util.swagger_properties[j]] = data[i][j];
          }
        }
        result.push(temp);
      }
      return result;
    },
    // 新增model
    new_param (panel, external) {
      this.$refs[panel].current_panel_set('create_model');
      this.$refs[panel].$refs.form_data_top.resetFields();
      this.$refs[panel].$refs.form_data_left.resetFields();
      this.$refs[panel].$refs.form_data_right.resetFields();
      this.$refs[panel].form_data_set(false);
      this.current_index = -1;
      this.$refs[panel].show();
      this.$refs[panel].data_propertiesList = []
      if (external) {
        this.current_modify_model = data;
      }
    },
    // 删除model
    delete_model (index) {
      this.nowId = index
      this.delete_modal_show = true
      this.cause = ''
      // this.data_model.splice(index,1);
    },
    sure_Delete_model () {
      if (!this.cause) {
        this.$Modal.warning({
          title: '警告',
          content: '请输入原因'
        });
      } else {
        this.$Modal.confirm({
          title: '提示',
          content: '确认删除吗？',
          'ok-text': '确认',
          onOk: () => {
            var param = {
              id: this.nowId,
              cause: this.cause
            }
            api.yop_modalManagement_modal_delete(param).then(
              (response) => {
                if (response.status == 'success') {
                  this.$ypMsg.notice_success(this, '删除成功');
                  this.pageRefresh();
                  this.delete_modal_show = false;
                } else {
                  setTimeout(() => {
                    this.$Modal.warning({
                      title: '警告',
                      content: response.message
                    });
                  }, 1000);
                }
              }
            )
          }

        });
      }
    },
    hide_Delete_model () {
      this.delete_modal_show = false;
    },
    // 复制模型
    copyModel(row) {
      this.copyModelData = row
      this.$refs.copy_model.show(row)
    },
    // 修改model
    edit_model (row) {
      console.log(row, '<---bianj2')
      this.nowId = row.id;
      this.nowVersion = row.version;
      var description = row.description;
      var apiGroup = row.apiGroup
      var name = row.name
      api.yop_modalManagement_modal_detail({id: this.nowId}).then(
        (response) => {
          var response = response.data
          if (response.status === 'success') {
            var schema = JSON.parse(response.data.result.schema)
            var bizOrderVariable = response.data.result.bizOrderVariable
            var sensitiveVariables = response.data.result.sensitiveVariables
            this.propertiesDetails = schema.properties;
            var requiredArr = schema.required
            var arr = []
            for (let i in this.propertiesDetails) {
              let o = {};
              o['name'] = i;
              for (var j in this.propertiesDetails[i]) {
                // 每个属性
                o[j] = this.propertiesDetails[i][j]
                if (j == 'type') {
                  o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
                } else if (j == '$ref') {
                  o['type'] = ['object', 'object']
                } else if (j == 'additionalProperties') {
                  o['type'] = ['object', 'map']
                }
              }
              for (var k in o) {
                if (k == 'format') {
                  o['type'][1] = o[k]
                }
              }
              arr.push(o);
            }
            if (requiredArr) {
              for (var i = 0; i < requiredArr.length; i++) {
                var requiredArrI = requiredArr[i]
                for (var j = 0; j < arr.length; j++) {
                  if (requiredArrI == arr[j].name) {
                    arr[j].required = true
                  }
                }
              }
            }

            this.$refs.modal_param_content_model_external.current_panel_set('modify_model');
            var detailData = {
              type: schema.type,
              title: schema.title,
              extensions: schema.extensions,
              name: name,
              description: description,
              apiGroup: apiGroup,
              data_propertiesList: arr
            }
            this.$refs.modal_param_content_model_external.form_data_set(detailData, bizOrderVariable, sensitiveVariables);
            // this.current_index = -1;
            this.$refs.modal_param_content_model_external.show();
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      );
      // this.$refs[panel].$refs.form_data_left.resetFields();
      // this.$refs[panel].$refs.form_data_right.resetFields();
    },
    // 编辑元素
    edit_param (index, data, current_data, panel) {
      this.$refs[panel].$refs.form_data_left.resetFields();
      this.$refs[panel].$refs.form_data_right.resetFields();
      this.$refs[panel].current_panel_set('modify_model');
      this.$refs[panel].current_index_set(index);
      this.$refs[panel].form_data_set(data);

      // form_data.apiGroup
      this.current_modify_model = current_data;
      this.current_index = -1;
      this.$refs[panel].show();
    },
    // 变更记录
    toChangeRecode (row) {
      var apiGroup, name
      if (row) {
        apiGroup = row.apiGroup
        name = row.name
      }
      this.$refs.modal_opt_record.reset_search();
      if (apiGroup) {
        this.$refs.modal_opt_record.set_appId(apiGroup);
      }
      if (name) {
        this.$refs.modal_opt_record.set_modelName(name);
      }

      this.$refs.modal_opt_record.modal_preview();
      this.$refs.modal_opt_record.search();
    },

    detail_model (row) {
      var id = row.id;
      var name = row.name;
      var apiGroup = row.apiGroup;
      var description = row.description;
      api.yop_modalManagement_modal_detail({id: id}).then(
        (response) => {
          var response = response.data
          if (response.status === 'success') {
            var schema = JSON.parse(response.data.result.schema)
            var bizOrderVariable = response.data.result.bizOrderVariable
            var sensitiveVariables = response.data.result.sensitiveVariables
            this.propertiesDetails = schema.properties;
            var arr = []
            for (let i in this.propertiesDetails) {
              let o = {};
              o['name'] = i;
              for (var k in schema.required) {
                var requiredName = schema.required[k]
                if (requiredName === i) {
                  o['required'] = true
                }
              }
              for (var j in this.propertiesDetails[i]) {
                o[j] = this.propertiesDetails[i][j]
                if (j == 'type') {
                  o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
                } else if (j == '$ref') {
                  o['type'] = ['object', 'object']
                } else if (j == 'additionalProperties') {
                  o['type'] = ['object', 'map']
                }
              }
              for (var k in o) {
                if (k == 'format') {
                  o['type'][1] = o[k]
                }
              }
              arr.push(o)
            }
            this.$refs.modal_param_content_model_external.current_panel_set('detail_model');
            var detailData = {
              type: schema.type,
              title: schema.title,
              name: name,
              apiGroup: apiGroup,
              description: description,
              data_propertiesList: arr,
              extensions: schema.extensions,
            }
            this.$refs.modal_param_content_model_external.form_data_set(detailData, bizOrderVariable, sensitiveVariables, 'detail');
            // this.current_index = -1;
            this.$refs.modal_param_content_model_external.show();
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      );
    },
    delete_param_in_panel (index, params) {
      params.splice(index, 1);
    },
    // 更新model的数据
    data_update_model (data, type, index) {
      if (type === 'create_model') {
        // 现在为不包含子项的处理 需要在提交的时候2次数据规范
        this.current_modify_model.push(data)
      } else {
        for (var i in data) {
          this.current_modify_model[index][i] = data[i]
        }
      }
      this.$refs.modal_param_content_model.cancel();
    },
    // 更新model外部的数据
    data_update_model_external (data, type, index) {
      // this.modal_Show = true
      if (type === 'create_model') {
        // 现在为不包含子项的处理 需要在提交的时候2次数据规范
        // this.data_model.push(data)
        api.yop_modalManagement_modal_create(data).then(
          (response) => {
            if (response.status === 'success') {
              this.$ypMsg.notice_success(this, '创建成功');
              this.pageRefresh()
              // this.tabledataGet(response.data.page.items);
              // this.pageNo = response.data.result.pageNo;
              // this.pageTotal = response.data.result.totalPageNum * 10;
            } else {
              this.$Modal.error({
                title: '错误',
                content: response.message
              });
            }
          }
        );
      } else {
        for (var i in data) {
          if (i == 'name' || i == 'apiGroup') {
            delete data[i]
          }
        }
        data.id = this.nowId;
        data.version = this.nowVersion;
        api.yop_modalManagement_modal_update(data).then(
          (response) => {
            if (response.status === 'success') {
              this.$ypMsg.notice_success(this, '修改成功');
              this.pageRefresh();
              // this.tabledataGet(response.data.page.items);
              // this.pageNo = response.data.result.pageNo;
              // this.pageTotal = response.data.result.totalPageNum * 10;
            } else {
              this.$Modal.error({
                title: '错误',
                content: response.message
              });
            }
          }
        );
      }
      this.$refs.modal_param_content_model_external.cancel();
    }

  },
  mounted () {
    if (this.$route.query.data_interface_name) {
      this.data_interface_name = this.$route.query.data_interface_name
    }
    this.init();
  }
};
</script>

<style scoped>
</style>
