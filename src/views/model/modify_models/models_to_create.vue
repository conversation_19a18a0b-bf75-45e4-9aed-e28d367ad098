<style lang="less">
    .width-50-perc{
        width:45%;
    }
    .width-100-perc{
        width:100%;
    }
    .margin-top-0{
        margin-top : 0!important;
    }
    .schemaStyle {
      width: fit-content;
    }
     .chemaStyle {outline: 1px solid #ccc; padding: 5px; margin: 5px; }
// 3     .string { color: green; }        /*字符串的样式*/
// 4     .number { color: darkorange; }    /*数字的样式*/
// 5     .boolean { color: blue; }        /*布尔型数据的样式*/
// 6     .null { color: magenta; }        /*null值的样式*/
// 7     .key { color: red; }            /*key值的样式*/
    /*.margin-right-9{*/
    /*margin-right: 9px!important;*/
    /*}*/
</style>
<template>
    <Row>

        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black" v-show="current_panel ==='create_model'">新增model</span>
            <span style="color:black" v-show="current_panel ==='modify_model'">修改model</span>
            <span style="color:black" v-show="current_panel ==='detail_model'">查看model</span>
        </p>
        <Row>
           <Col span="24">
                <p v-if="current_panel !=='detail_model'">基本信息</p>
                <Form ref="form_data_top"  :model="form_data" :label-width="120" inline >
                    <FormItem label="模型名称：" prop="name" class="width-50-perc">
                        {{form_data.name}}
                    </FormItem>
                    <FormItem label="API分组：" prop="apiGroup" class="width-50-perc">
                        {{form_data.apiGroup}}
                    </FormItem>
                    <FormItem v-if="this.form_data.schema" label="模型描述：" prop="description" class="width-50-perc">
                        {{form_data.description}}
                    </FormItem>
                </Form>
            </Col>
            <Col span="12">

            <Form v-if="this.form_data.schema" ref="form_data_left" :model="form_data" :label-width="120" inline >
                <!-- <FormItem label="类型：" prop="type" class="width-50-perc">
                           <p>{{form_data.type}}</p>
                </FormItem>
                <FormItem label="标题：" v-if="form_data.title" prop="title" class="width-50-perc" >
                           <p>{{form_data.title}}</p>
                </FormItem>
                <FormItem  label="描述：" class="width-50-perc" prop="description">
                           <p>{{form_data.description_content}}</p>
                </FormItem> -->
                <FormItem  label="schema：" class="width-50-perc" prop="description">
                        <pre class="schemaStyle">{{form_data.schema}}</pre>
                </FormItem> 
                <!-- <FormItem v-if="current_panel ==='detail_model'"  class="width-50-perc" label="类型：" prop="type" :rules="{required:true,message:'类型不能为空'}">
                    object
                </FormItem>  -->
                <!-- <FormItem class="width-50-perc" label="是否必填：" prop="required">
                    <i-switch  size="large" v-model="form_data.required">
                        <Icon type="android-done" slot="open"></Icon>
                        <Icon type="android-close" slot="close"></Icon>
                    </i-switch>
                </FormItem> -->

                <!--<FormItem  class="width-100-perc" label="示例值：" prop="sample">-->
                    <!--<Input type="textarea" v-model="form_data.sample"-->
                           <!--style="width:85%"></Input>-->
                <!--</FormItem>-->

                <!-- <FormItem  class="width-100-perc" label="描述：" prop="description">
                    <Input type="textarea" v-model="form_data.description"
                            style="width:85%"></Input>
                </FormItem> -->
            </Form>
            <!--</Card>-->
            </Col>
            <Col span="12">
            <!--<Card>-->
            <Form ref="form_data_right" :model="form_data" :label-width="120" inline>

                <!--<FormItem class="width-50-perc" label="内部参数：" prop="internal" >-->
                    <!--<i-switch size="large" v-model="form_data.internal ">-->
                        <!--<Icon type="android-done" slot="open"></Icon>-->
                        <!--<Icon type="android-close" slot="close"></Icon>-->
                    <!--</i-switch>-->
                <!--</FormItem>-->

                <!-- <FormItem class="width-50-perc" label="敏感字段：" prop="sensitive" >
                    <i-switch  size="large" v-model="form_data.sensitive">
                        <Icon type="android-done" slot="open"></Icon>
                        <Icon type="android-close" slot="close"></Icon>
                    </i-switch>
                </FormItem> -->

            </Form>
            </Col>
            <!--<Col v-if="this.form_data_visual.additionalProperties||this.form_data_visual.items" style="border-bottom: 1px solid #eee;margin-bottom: 20px;" span="24">-->

            <!--</Col>-->
            <!--<Col v-if="this.form_data_visual.additionalProperties" span="24">-->
            <!--<Tag type="dot" color="blue">值：</Tag>-->
            <!--<subcontent :depth="1" ref="subContent_map" :type_format="this.type_data" :type_format_cut="this.type_data_cut"></subcontent>-->
            <!--</Col>-->
            <!--<Col v-if="this.form_data_visual.items" span="24">-->
            <!--<Tag type="dot" color="green">数组元素：</Tag>-->
            <!--<subcontent :depth="1" ref="subContent_array" :type_format="this.type_data" :type_format_cut="this.type_data_cut"></subcontent>-->
            <!--</Col>-->
        </Row>
        <Row>

            <!-- <Table id='table_1' border ref="properties" :columns="columns_propertiesList_detail" :data="data_propertiesList"></Table> -->
            <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                <Page class="margin-top-10" style="float: right" :total="20" :page-size="20" :current="1" show-elevator @on-change=""></Page>
            </Tooltip> -->
            
        </Row>
        <Row style="padding:20px;line-height:40px;">
            <p>操作人：{{operator}}</p>
            <p>操作类型：{{operationType}}</p>
            <p>操作时间：{{operateDate}}</p>
            <p>操作原因：{{cause}}</p>

        </Row>
        <div slot="footer" v-show="current_panel !=='detail_model'">
            <Button type="primary" @click="submit">确定</Button>
            <Button type="ghost" @click="cancel">取消</Button>
        </div>
        <div slot="footer" v-show="current_panel ==='detail_model'">
            <!-- <Button type="primary" @click="submit">确定</Button> -->
            <Button type="ghost" @click="cancel">关闭</Button>
        </div>

        
    <!-- <modal_param_content_model ref="modal_param_content_model" :index="current_index" @data_update="data_update_model"></modal_param_content_model> -->
    </Row>
</template>

<script>
    import textEditorSize from '../../my-components/text-editor/text-editor-size';
    import commonSelect from '../../common-components/select-components/selectCommon';
    import label_icon from '../../common-components/icon-components/star_red'
    // import subcontent from './param_main_content_model'
    // import modal_param_content_model from './modal_param_content_model';
    import api from '../../../api/api'

    export default {
        name: 'modal_param_content_model_external',
        components: {
            textEditorSize,
            commonSelect,
            // subcontent,
            label_icon,
            // modal_param_content_model,
        },
        data () {
            return {
                operator:"",
                operationType:"",
                operateDate:"",
                cause:"",
                apiGroup_List:[],
                tempProperties:{},
                columns_propertiesList:[
                    {
                        title: '名称',
                        key: 'name',
                        width: 100,
                        render: (h, params) => {
                            if(params.row.name){
                                return h('div',params);
                            }else{
                                return h('div','--');
                            }
                        }
                    },
                    {
                        title: '类型',
                        key: 'type',
                        width: 130,
                        align:'center',
                        render: (h, params) => {
                            if(typeof params.row.type == "object"){
                                return h('div',params.row.type[0]);
                            }else{
                                return h('div',params.row.type);
                            }
                        }
                        // render: (h,params) => {
                        //     if(params.row.model){
                        //         let _this = this
                        //         return h('div', [
                        //             h('p',[
                        //                 h('a',
                        //                     {
                        //                         on: {
                        //                             click: function(){
                        //                                 _this.model_click(params.row.model);
                        //                             }
                        //                         },
                        //                     },params.row.model)
                        //             ])
                        //         ]);
                        //     }else{
                        //         return params.row.type[1]? h('div',params.row.type[0]+'/'+params.row.type[1]):h('div',params.row.type[0]);
                        //     }
                        // }
                    },
                    {
                        title: '必填',
                        key: 'must',
                        width: 100,
                        render: (h, params) => {
                            if(params.row.model){
                                return h('div','--');
                            }else{
                                return params.row.required ? h('div','是') : h('div','否');
                            }

                        }
                    },
                    {
                        type:'html',
                        title: '描述',
                        key: 'description',
                        'min-width': 180,
                    },
                    // {
                    //     type:'html',
                    //     title: '示例值',
                    //     key: 'sample',
                    //     // width: 100,
                    //     'min-width': 180,
                    // },
                    // {
                    //     title: '端点参数',
                    //     key: 'param_name',
                    //     width: 120
                    // },
                ],
                columns_propertiesList_detail:[
                    {
                        title: '名称',
                        key: 'name',
                        width: 100,
                        render: (h, params) => {
                            if(params.row.name){
                                return h('div',params.row.name);
                            }else{
                                return h('div','--');
                            }
                        }
                    },
                    {
                        title: '类型',
                        key: 'type',
                        width: 130,
                        align:'center',
                        render: (h, params) => {
                            if(typeof params.row.type == "object"){
                                return h('div',params.row.type[0]);
                            }else{
                                return h('div',params.row.type);
                            }
                        }
                        // render: (h,params) => {
                        //     if(params.row.model){
                        //         let _this = this
                        //         return h('div', [
                        //             h('p',[
                        //                 h('a',
                        //                     {
                        //                         on: {
                        //                             click: function(){
                        //                                 _this.model_click(params.row.model);
                        //                             }
                        //                         },
                        //                     },params.row.model)
                        //             ])
                        //         ]);
                        //     }else{
                        //         return params.row.type[1]? h('div',params.row.type[0]+'/'+params.row.type[1]):h('div',params.row.type[0]);
                        //     }
                        // }
                    },
                    {
                        title: '必填',
                        key: 'must',
                        width: 100,
                        render: (h, params) => {
                            if(params.row.model){
                                return h('div','--');
                            }else{
                                return params.row.required ? h('div','是') : h('div','否');
                            }

                        }
                    },
                    {
                        type:'html',
                        title: '描述',
                        key: 'description',
                        'min-width': 180,
                    },
                    {
                        type:'html',
                        title: '示例值',
                        key: 'sample',
                        // width: 100,
                        'min-width': 180,
                    },
                    // {
                    //     title: '端点参数',
                    //     key: 'param_name',
                    //     width: 120
                    // },
                ],
                data_propertiesList:[],
                submit_modal:[],
                current_modify_model:[],
                current_index : -1,
                current_model : [],
                data_model:[],
                // form_data_left:{
                //     param: '', // 参数
                //     title: '', // 名称
                //     type: ['object'], // 类型
                //     sample: '', // 示例值
                //     description: '', // 描述
                //     required: false, // 是否必填
                // },
                // 表格内容数据绑定
                form_data: {
                    name:"",
                    apiGroup:"",
                    description:"",
                    param: '', // 参数
                    title: '', // 名称
                    type: ['object'], // 类型
                    description_content: '', // 描述
                    required: false, // 是否必填
                    sensitive : false, // 名称
                    schema:""
                },

                // 表格显示绑定
                form_data_visual: {
                    extremum : false,
                    enums : false,
                    length : false,
                    pattern : false,
                    itemSize : false,
                    model : false,
                    additionalProperties: false,
                    items : false,
                    order: false
                },
                current_panel: 'create_request_json',
                // 窗口是否展示
                modal_show : true,
                // 示例值绑定
                data_select_sample : '',
                // 常用正则表达式数据绑定
                data_select_pattern : '',
                // 枚举数据绑定
                enum_data : [{
                    value : '',
                    index : 0,
                    status : 1
                }],
                enum_index : 0,
                // 是否展示更多属性 false 为隐藏 true 为显示
                more_attr : false,

                type_data:[],
                type_data_cut : [],
                sample_data:[
                    {
                        value : 'integer',
                        desc : 'integer',
                        formats :[
                            {
                                value : 'int32',
                                desc : 'int32',
                                defaulted : true,
                            },
                            {
                                value : 'int64',
                                desc : 'int64',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'number',
                        desc : 'number',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                            },
                            {
                                value : 'float',
                                desc : 'float',
                                defaulted : true,
                            },
                            {
                                value : 'double',
                                desc : 'double',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'boolean',
                        desc : 'boolean',
                        constrains : [
                            'enums',
                            'length',
                            'pattern'
                        ],
                        // formats :[
                        //     {
                        //         value : 'boolean',
                        //         desc : 'boolean',
                        //         defaulted : true,
                        //         constrains : [
                        //             'enums',
                        //             'length',
                        //             'pattern'
                        //         ],
                        //     }                            
                        // ]
                    },
                    {
                        value : 'string',
                        desc : 'string',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                                constrains : [
                                    'enums',
                                    'length',
                                    'pattern'
                                ]
                            },
                            {
                                value : 'byte',
                                desc : 'byte',
                                defaulted : true,
                            },
                            {
                                value : 'binary',
                                desc : 'binary',
                                defaulted : true,
                            },
                            {
                                value : 'date',
                                desc : 'date',
                                defaulted : true,
                            },
                            {
                                value : 'date-time',
                                desc : 'date-time',
                                defaulted : true,
                            },
                            {
                                value : 'password',
                                desc : 'password',
                                defaulted : true,
                            },
                            {
                                value : 'email',
                                desc : 'email',
                                defaulted : true,
                            },
                            {
                                value : 'mobile',
                                desc : 'mobile',
                                defaulted : true,
                            },
                            {
                                value : 'idcard',
                                desc : 'idcard',
                                defaulted : true,
                            },
                            {
                                value : 'bankcard',
                                desc : 'bankcard',
                                defaulted : true,
                            },
                            {
                                value : 'cvv',
                                desc : 'cvv',
                                defaulted : true,
                            },
                            {
                                value : 'uuid',
                                desc : 'uuid',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'array',
                        desc : 'array',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                                constrains : [
                                    'items',
                                ]
                            }
                        ]
                    },
                    {
                        value : 'object',
                        desc : 'object',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                            },
                            {
                                value : 'map',
                                desc : 'map',
                                defaulted : true,
                                constrains : [
                                    'additionalProperties',
                                ]
                            }
                        ]
                    },
                ],
                // 当前编辑行数
                current_line_index : 0

            };
        },
        methods: {
            format_submit_param(param,subContent,type){
                var tempProperties = {properties:{}}
                var requiredArr = []
                for(var k = 0 ;k < param.length ; k ++){
                    var paramK = param[k]
                    tempProperties["properties"][paramK["name"]] = {};
                    for(var i in paramK){
                        // if(i == "title"){
                        //     tempProperties.title = paramK[i]
                        // }
                        if(i == "type"){
                            if(paramK[i][1] == "map" && type == "map"){
                                var subContent_fix = {};
                                if(subContent && typeof subContent.type == "object"){
                                    for(var m in subContent){
                                        if(m == "type"){
                                            subContent_fix.format = subContent.type[1] || ""
                                            subContent_fix.type = subContent.type[0]
                                        }else{
                                            subContent_fix[m] = subContent[m]
                                        }
                                    }
                                    paramK["additionalProperties"] = subContent_fix
                                }
                                tempProperties["properties"][paramK["name"]][i] = "object"
                                if(paramK.model){
                                    tempProperties["properties"][paramK["name"]]["additionalProperties"]["$ref"] = `#/components/schemas/${paramK.model}`
                                }else{
                                    tempProperties["properties"][paramK["name"]]["additionalProperties"] = subContent_fix ?subContent_fix: paramK["additionalProperties"]
                                    tempProperties["properties"][paramK["name"]]["additionalProperties"]["format"] = subContent_fix ? subContent_fix["format"] : paramK["additionalProperties"]["format"]
                                    tempProperties["properties"][paramK["name"]]["additionalProperties"]["type"] = subContent_fix ?subContent_fix["type"] : paramK["additionalProperties"]["type"]
                                }
                            }else if (paramK[i][0] == "array" && type == "array"){
                                var subContent_fix = {};
                                if(subContent && typeof subContent.type == "object"){
                                    for(var m in subContent){
                                        if(m == "type"){
                                            subContent_fix.format = subContent.type[1] || ""
                                            subContent_fix.type = subContent.type[0]
                                        }else{
                                            subContent_fix[m] = subContent[m]
                                        }
                                    }
                                    paramK["items"] = subContent_fix
                                }
                                tempProperties["properties"][paramK["name"]][i] = "array"
                                tempProperties["properties"][paramK["name"]]["items"] = subContent_fix ?subContent_fix: paramK["items"]
                                tempProperties["properties"][paramK["name"]]["items"]["format"] = subContent_fix ? subContent_fix["format"] : paramK["items"]["format"]
                                tempProperties["properties"][paramK["name"]]["items"]["type"] = subContent_fix ?subContent_fix["type"] : paramK["items"]["type"]
                            }else if(paramK[i][1] == "object"){
                                tempProperties["properties"][paramK["name"]][i] = "object"
                                tempProperties["properties"][paramK["name"]]["$ref"] = `#/components/schemas/${paramK.model}`;

                            }else{
                                tempProperties["properties"][paramK["name"]][i] = paramK[i][0]
                                if(paramK[i][0] != paramK[i][1]){
                                    tempProperties["properties"][paramK["name"]]["format"] = paramK[i][1]
                                }
                            }
                        }
                        if(i == "required" && paramK[i]){
                            requiredArr.push(paramK["name"]);
                            tempProperties[i] = requiredArr
                        }
                        if(paramK[i] != "" && i != "title" && i != "type" && i != "required" && i != "name" &&i != "model" && i != "_index" && i != "_rowKey" && i != "format" && i != "items" && i != "additionalProperties"){
                            tempProperties["properties"][paramK["name"]][i] = paramK[i]
                        }
                    }
                }
                return tempProperties
            },
         
         
            // 页面初始化
            init () {
                this.data_format(this.sample_data);
            },
            // 表单提交
            submit (){
                if(this.current_panel ==='detail_model'){
                    this.modal_show = false;
                }
                // if(this.current_panel ==='create_model'){
                //     this.form_submit_validate('form_data_top','form_data_left')
                // }else{
                    this.form_submit_validate('form_data_top')
                // }
                // this.form_submit_validate('form_data_top','form_data_left','form_data_right')
            },
            // 继续验证
            next_validate(){
                if(this.form_data_visual.additionalProperties){
                    this.$refs['subContent_map'].submit();
                }else if(this.form_data_visual.items){
                    this.$refs['subContent_array'].submit();
                }else{
                    if(this.current_panel === "create_model"){
                        var param = {
                            name:this.form_data.name,
                            apiGroup:this.form_data.apiGroup,
                            description:this.form_data.description,
                            schema:{
                                title:this.form_data.title,
                                type:"object",
                                properties:this.tempProperties.properties,
                                required:this.tempProperties.required,
                            }

                        }
                      
                    }else{
                         var param = {
                            description:this.form_data.description,
                            schema:{
                                title:this.form_data.title,
                                type:"object",
                                properties:this.tempProperties.properties,
                                required:this.tempProperties.required
                            }

                        }
                    }
                    var stringSchema = JSON.stringify(param.schema)
                    param.schema = stringSchema
                    this.$emit('data_update',param,this.current_panel,this.current_line_index);
                    // 新建或者编辑操作执行
                }
            },
            // 提交函数

            // 表单提交验证
            form_submit_validate (val1,val2) {
                let result1 = 0;
                let result2 = 0;
                let result3 = 0;
                let check = 0;
                    this.$refs[val1].validate((valid) => {
                        if (valid) {
                            this.next_validate();
                        }else {
                            this.form_validate_failed()
                        }
                    })
            },
            // 表单验证失败
            form_validate_failed (){
                    this.$Message.error('请检查');
            },
            // 表单取消
            cancel () {
                this.modal_show = false;
            },
            // 窗口显示
            show () {
                this.modal_show = true;
            },
          
            // 数据处理
            data_format (array){
                this.type_data = [];
                this.type_data_cut = [];
                for (var i in array){
                    let subContent = array[i].formats;
                    let childrenTemp = []
                    for(var j in subContent){
                        if(subContent[j].value === '-'){
                            childrenTemp.push(
                                {
                                    value : array[i].value,
                                    label : array[i].desc,
                                    constrains : subContent[j].constrains ? subContent[j].constrains : [],
                                }
                            );
                        }else{
                            childrenTemp.push(
                                {
                                    value : subContent[j].value,
                                    label : subContent[j].desc,
                                    constrains : subContent[j].constrains ? subContent[j].constrains : [],
                                }
                            );
                        }
                    }
                    this.type_data_cut.push(
                        {
                            value : array[i].value,
                            label : array[i].desc,
                            children: childrenTemp
                        }
                    )
                    this.type_data.push(
                        {
                            value : array[i].value,
                            label : array[i].desc,
                            children: childrenTemp
                        }
                    )
                }
            },
            syntaxHighlight(json) {
                if (typeof json != 'string') {
                    json = JSON.stringify(json, undefined, 4);
                }
                // json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
                return json
                // return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
                //     function(match) {
                //         var cls = 'number';
                //         if (/^"/.test(match)) {
                //             if (/:$/.test(match)) {
                //                 cls = 'key';
                //             } else {
                //                 cls = 'string';
                //             }
                //         } else if (/true|false/.test(match)) {
                //             cls = 'boolean';
                //         } else if (/null/.test(match)) {
                //             cls = 'null';
                //         }
                //         return match;
                //     }
                // );
            },
          
            setResult(val){
                this.operator = val.operator;
                var operationType = ""
                if(val.operationType == "IMPORT"){
                operationType = "导入"
                }
                if(val.operationType == "DELETE"){
                operationType = "删除"
                }
                if(val.operationType == "CREATE"){
                operationType = "创建"
                }
                if(val.operationType == "ROLLBACK"){
                operationType = "回滚"
                }
                if(val.operationType == "UPDATE"){
                operationType = "更新"
                }
                this.operationType = operationType || "";
                this.operateDate = val.operateDate || "";
                this.cause = val.cause || ""
                this.form_data.name = val.modelName
                this.form_data.apiGroup = val.apiGroup


                var val = val.content;
                if(val){
                    var schema = JSON.parse(val.schema);
                    this.form_data.schema = this.syntaxHighlight(schema)
                    this.form_data.type = schema.type
                    this.form_data.title = schema.title
                    this.form_data.description = val.description
                    for (var i in schema.properties){
                        schema.properties[i].name=i
                        this.data_propertiesList.push(schema.properties[i])
                    }
                }else{
                    this.form_data.schema = ""
                }
                
                // this.form_data.description_content = val.description
               
                
                // this.tempProperties = this.format_submit_param(data.data_propertiesList);
            },
        },
        mounted () {
            this.init();
        },
    };
</script>

<style scoped>

</style>
