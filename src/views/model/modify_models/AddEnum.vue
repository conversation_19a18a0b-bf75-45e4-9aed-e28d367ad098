<template>
  <Table 
    :columns="columns"
    :data="data"
    ref="table_enum"
    key="table_enum"
  ></Table>
</template>

<script>
import Sortable from 'sortablejs';
export default {
  props: ['value'],
  data () {
    return {
      columns: [
        {
          title: '排序',
          width: 60,
          key: 'Action',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Icon', {
                class: 'iconDocOrder',
                props: {
                  type: 'arrow-move'
                },
                directives: [
                  {
                    name: 'show',
                    value: this.data.length !== params.row._index + 1
                  }
                ],
                style: {
                  color: '#ad8893',
                  cursor: 'move',
                  marginRight: '5px',
                  fontSize: '18px'
                }
              })
            ]);
          }
        },
        {
          title: '默认',
          key: 'isDefault',
          width: 60,
          align: 'center',
          render: (h, params) => {
            return h('Checkbox',
              {
                props: {
                  value: params.row.value === this.defaultValue
                },
                directives: [
                  {
                    name: 'show',
                    value: this.data.length !== params.row._index + 1
                  }
                ],
                on: {
                  'on-change': (value) => {
                    this.setCheck(params.row, value)
                  }
                }
              }
            )
          }
        },
        {
          title: '枚举值',
          key: 'value',
          align: 'center',
          render: (h, params) => {
            return h('Input',
              {
                props: {
                  value: params.row.value
                },
                on: {
                  'on-blur': () => {
                    this.onInputBlur(params.row)
                  },
                  'on-change': (e) => {
                    params.row.value = e.target.value
                  },
                }
              }
            )
          }
        },
        {
          title: '描述',
          key: 'desc',
          align: 'center',
          render: (h, params) => {
            return h('Input',
              {
                props: {
                  value: params.row.desc
                },
                on: {
                  'on-blur': () => {
                    this.onInputBlur(params.row)
                  },
                  'on-change': (e) => {
                    params.row.desc = e.target.value
                  }
                }
              }
            )
          }
        },
        {
          title: '操作',
          width: 80,
          key: 'option',
          align: 'center',
          render: (h, params) => {
            return h('Button', {
              props: {
                type: 'text',
                size: 'small'
              },
              directives: [
                {
                  name: 'show',
                  value: this.data.length !== params.row._index + 1
                }
              ],
              on: {
                click: () => {
                  this.removeData(params.row._index)
                }
              }
            }, '移除')
          }
        }
      ],
      defaultValue: null,
      data: [
        {
          value: '',
          desc: ''
        }
      ]
    }
  },
  methods: {
    getData () {
      const params = {
        default: this.defaultValue,
        enum: [],
        description: '\n可选项如下:\n'
      }
      this.data.forEach((item, index) => {
        const { value, desc } = item
        if (value) {
          params.enum.push(value)
          if (index < this.data.length - 1) {
            params.description = params.description + `${value}:${desc}\n`
          } else {
            params.description = params.description + `${value}:${desc}`
          }
        }
      })
      return params
    },
    handleDescription (description) {
      if (!description) {
        this.data.push({
          value: '',
          desc: ''
        })
        return
      }
      const descList = description.split('\n')
      descList.forEach(item => {
        if (!item) { return }
        const value = item.split(':')[0]
        const desc = item.split(':')[1]
        this.data.push({
          value,
          desc
        })
      })
      this.data.push({
        value: '',
        desc: ''
      })
    },
    setData (data) {
      this.data = []
      this.defaultValue = data.default
      this.handleDescription(data.description)
      this.$nextTick(() => {
        this.$forceUpdate()
        this.dragSort()
      })
    },
    // 表格拖动排序
    dragSort () {
      const tbody = this.$refs.table_enum.$el.querySelector('tbody')
      const _this = this
      Sortable.create(tbody, {
        onEnd ({ newIndex, oldIndex }) {
          if (newIndex === _this.data.length - 1 || oldIndex === _this.data.length - 1) {
            _this.data = JSON.parse(JSON.stringify(_this.data))
            _this.$forceUpdate()
            return
          }
          const currRow = _this.data.splice(oldIndex, 1)[0]
          _this.data.splice(newIndex, 0, currRow)
          _this.$forceUpdate()
        }
      })
    },
    onInputBlur (row) {
      row.value = row.value.replace(/\s/g, '')
      const { value, desc, _index } = row
      if (desc && value) {
        this.data[_index] = {
          desc,
          value
        }
        if (_index === this.data.length - 1) {
          this.data.push({
            value: '',
            desc: ''
          })
          this.$nextTick(() => {
            this.$forceUpdate()
            this.dragSort()
          })
        }
      }
      if (!desc && !value && _index < (this.data.length - 1)) {
        this.removeData(_index)
      }
    },
    setCheck ({value}, check) {
      if (check) {
        this.defaultValue = value
      } else {
        this.defaultValue = null
      }
    },
    removeData (index) {
      this.data.splice(index, 1)
    }
  }
}
</script>
<style lang="less" scoped>

</style>