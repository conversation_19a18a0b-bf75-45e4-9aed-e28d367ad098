<style lang="less">
    .width-50-perc{
        width:45%;
    }
    .width-100-perc{
        width:100%;
    }
    .margin-top-0{
        margin-top : 0!important;
    }
    /*.margin-right-9{*/
    /*margin-right: 9px!important;*/
    /*}*/
</style>
<template>
    <Row>

    <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="90%">
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black" v-show="current_panel ==='detail_model'">模型详情</span>
        </p>
        <Row>
            <Col span="24">
                <p v-if="current_panel !=='detail_model'">基本信息</p>
                <Form ref="form_data_top"  :model="form_data" :rules="form_data_validate"  :label-width="120" inline >
                    <FormItem v-if="current_panel ==='create_model'"  label="API分组：" prop="apiGroup" class="width-100-perc">
                        <!--<p class="margin-right-9" slot="label"><label_icon></label_icon>参数:</p>-->
                        <Select
                        ref='select_apiM_2'
                        id="select_apiM_1"
                        class=""
                         style="width: 380px;"
                        v-model="form_data.apiGroup"
                        filterable
                        clearable
                        placeholder="请选择（默认全部）"
                        @on-change="getModels"
                        >
                        <Option
                            v-for="item in apiGroup_List"
                            :value="item.value"
                            :key="item.label"
                        >{{ item.label }}</Option>
                        </Select>
                    </FormItem>
                    <FormItem v-if="current_panel ==='create_model'" label="模型名称：" prop="name" class="width-100-perc">
                        <!--<p class="margin-right-9" slot="label"><label_icon></label_icon>参数:</p>-->
                        <Input type="text"  v-model="form_data.name"  style="width: 380px;"
                            ></Input>
                    <!-- <p class="margin-right-9 margin-top-5">model校验规则</p> -->
                    <p class="yop-explain-120" style="margin-bottom:10px;display:inline-block;" v-if="current_panel ==='create_model'">支持大写开头的驼峰</p>
                    </FormItem>

                    <FormItem label="API分组：" v-if="current_panel =='modify_model'"  prop="apiGroup" class="width-100-perc">
                            <p>{{form_data.apiGroup}}</p>
                    </FormItem>
                    <FormItem label="名称：" v-if="current_panel =='modify_model' || current_panel =='modify_model'"  prop="name" class="width-100-perc">
                            <p>{{form_data.name}}</p>
                    </FormItem>
                   
                
                    <FormItem v-if="current_panel !=='detail_model'" label="模型描述：" prop="description" class="width-100-perc" :rules="{required:true,message:'模型描述不能为空',trigger:'blur'}">
                        <!--<p class="margin-right-9" slot="label"><label_icon></label_icon>参数:</p>-->
                        <Input type="textarea" v-model="form_data.description"  style="width: 380px;"
                            ></Input>
                    </FormItem>
                </Form>
            </Col>
            <Col span="12">
            <!--<Card>-->
                

            <Form ref="form_data_left"  :model="form_data" :label-width="120" inline >
                <FormItem label="API分组：" v-if="current_panel =='detail_model'"  prop="apiGroup" class="width-50-perc">
                            <p>{{form_data.apiGroup}}</p>
                    </FormItem>
                 <FormItem label="类型：" v-if="current_panel =='detail_model'" prop="type" class="width-50-perc">
                            <p>{{form_data.type}}</p>
                    </FormItem>
                    <FormItem label="名称：" v-if="current_panel =='detail_model'"  prop="name" class="width-50-perc">
                            <p>{{form_data.name}}</p>
                    </FormItem>
                <FormItem label="描述：" v-if="current_panel =='detail_model'"  prop="description" class="width-50-perc">
                           <p>{{form_data.description}}</p>
                </FormItem>
                <!-- <FormItem  v-if="current_panel ==='detail_model'"  label="名称:" class="width-50-perc" prop="title" :rules="{required:true,message:'名称不能为空',trigger:'blur'}">
                           <p class="panel-content">{{form_data.title}}</p>
                </FormItem>
                <FormItem v-if="current_panel ==='detail_model'"  class="width-50-perc" label="类型：" prop="type" :rules="{required:true,message:'类型不能为空'}">
                    object
                </FormItem>  -->
                <!-- <FormItem class="width-50-perc" label="是否必填：" prop="required">
                    <i-switch  size="large" v-model="form_data.required">
                        <Icon type="android-done" slot="open"></Icon>
                        <Icon type="android-close" slot="close"></Icon>
                    </i-switch>
                </FormItem> -->

                <!--<FormItem  class="width-100-perc" label="示例值：" prop="sample">-->
                    <!--<Input type="textarea" v-model="form_data.sample"-->
                           <!--style="width:85%"></Input>-->
                <!--</FormItem>-->

                <!-- <FormItem  class="width-100-perc" label="描述：" prop="description">
                    <Input type="textarea" v-model="form_data.description"
                            style="width:85%"></Input>
                </FormItem> -->
            </Form>
            <Button v-if="current_panel !=='detail_model'" class="margin-bottom-10" type="primary"
                    @click="new_param('modal_param_content_model',true)">新增属性
            </Button>
            <!--</Card>-->
            </Col>
            <Col span="12" type="flex" align="end">
                <slot v-if="current_panel ==='detail_model'" name="detail-right" :row="form_data"></slot>
            <!--<Card>-->
            <Form ref="form_data_right" :model="form_data" :label-width="120" inline>

                <!--<FormItem class="width-50-perc" label="内部参数：" prop="internal" >-->
                    <!--<i-switch size="large" v-model="form_data.internal ">-->
                        <!--<Icon type="android-done" slot="open"></Icon>-->
                        <!--<Icon type="android-close" slot="close"></Icon>-->
                    <!--</i-switch>-->
                <!--</FormItem>-->

                <!-- <FormItem class="width-50-perc" label="敏感字段：" prop="sensitive" >
                    <i-switch  size="large" v-model="form_data.sensitive">
                        <Icon type="android-done" slot="open"></Icon>
                        <Icon type="android-close" slot="close"></Icon>
                    </i-switch>
                </FormItem> -->

            </Form>
            </Col>
            <!--<Col v-if="this.form_data_visual.additionalProperties||this.form_data_visual.items" style="border-bottom: 1px solid #eee;margin-bottom: 20px;" span="24">-->

            <!--</Col>-->
            <!--<Col v-if="this.form_data_visual.additionalProperties" span="24">-->
            <!--<Tag type="dot" color="blue">值：</Tag>-->
            <!--<subcontent :depth="1" ref="subContent_map" :type_format="this.type_data" :type_format_cut="this.type_data_cut"></subcontent>-->
            <!--</Col>-->
            <!--<Col v-if="this.form_data_visual.items" span="24">-->
            <!--<Tag type="dot" color="green">数组元素：</Tag>-->
            <!--<subcontent :depth="1" ref="subContent_array" :type_format="this.type_data" :type_format_cut="this.type_data_cut"></subcontent>-->
            <!--</Col>-->
            <loading :show="show_loading_basic"></loading>  
        </Row>
        <Row>

            <Table id='modal_edit_table' border ref="properties" :columns="current_panel !=='detail_model'?columns_propertiesList:columns_propertiesList_detail" :data="data_propertiesList"></Table>
            <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                <Page class="margin-top-10" style="float: right" :total="20" :page-size="20" :current="1" show-elevator @on-change=""></Page>
            </Tooltip> -->
            <loading :show="show_loading_basic"></loading>  
            
        </Row>
        <div slot="footer">
            <Button v-show="current_panel !=='detail_model'" type="ghost"  @click="cancel">取消</Button>
            <Button v-show="current_panel ==='detail_model'" type="ghost"  @click="cancel">关闭</Button>
            <Button v-show="current_panel !=='detail_model'" type="primary" @click="submit">保存</Button>
        </div>
    </Modal>
    <model_detail_page ref="model_detail_page" :index="current_index" @data_update="data_update_model"></model_detail_page>
    </Row>
</template>

<script>
 import textEditorSize from '../../my-components/text-editor/text-editor-size';
 import commonSelect from '../../common-components/select-components/selectCommon';
 import label_icon from '../../common-components/icon-components/star_red'
 import subcontent from './param_main_content_model'
 import model_detail_page from './model_detail_page'
 import loading from '../../my-components/loading/loading';
 import api from '../../../api/api'
 import { setTimeout } from 'timers'
 import Sortable from 'sortablejs'

 export default {
   name: 'modal_param_content_model_external',
   components: {
     textEditorSize,
     commonSelect,
     subcontent,
     label_icon,
     loading,
     model_detail_page
   },
   data () {
     const validate_basic_name = (rule, value, callback) => {
       if (!this.isModelname(value)) {
         callback(new Error('支持大写开头的驼峰'));
       } else {
         callback();
       }
     };
     return {
       form_data_validate: {
         apiGroup: [{required: true, message: 'apiGroup不能为空', trigger: 'blur'}],
         name: [{required: true, message: '名称不能为空'}, { validator: validate_basic_name, trigger: 'blur' }],
         description: [{required: true, message: '描述不能为空'}]
       },
       show_loading_basic: true,
       apiGroup_List: [],
       tempProperties: {},
       columns_propertiesList: [
         {
           title: '排序',
           width: 100,
           key: 'Action',
           align: 'center',
           render: (h, params) => {
             return h('div', [
               h('Icon', {
                 class: 'iconDocOrder',
                 props: {
                   type: 'arrow-move'
                 },
                 style: {
                   color: '#ad8893',
                   cursor: 'move',
                   marginRight: '5px',
                   fontSize: '18px'
                 }
               })
             ]);
           }

         },
         {
           title: '名称',
           key: 'name',
           align: 'center',
           render: (h, params) => {
             if (params.row.name) {
               return h('div', params.row.name);
             } else {
               return h('div', '--');
             }
           }
         },
         {
           title: '类型-格式',
           key: 'type',
           align: 'center',
           render: (h, params) => {
             if (typeof params.row.type == 'object') {
               return h('div', [h('p', params.row.type[0]),
                 h('p', params.row.type[1])]);
             } else if (params.row.format) {
               return h('div', [h('p', params.row.type),
                 h('p', params.row.format)]);
             } else {
               return h('div', [h('p', params.row.type),
                 h('p', params.row.type)]);
             }
           }
           // render: (h,params) => {
           //     if(params.row.model){
           //         let _this = this
           //         return h('div', [
           //             h('p',[
           //                 h('a',
           //                     {
           //                         on: {
           //                             click: function(){
           //                                 _this.model_click(params.row.model);
           //                             }
           //                         },
           //                     },params.row.model)
           //             ])
           //         ]);
           //     }else{
           //         return params.row.type[1]? h('div',params.row.type[0]+'/'+params.row.type[1]):h('div',params.row.type[0]);
           //     }
           // }
         },
         {
           title: '必需',
           align: 'center',
           key: 'must',
           render: (h, params) => {
             // if(params.row.model){
             //     return h('div','--');
             // }else{
             return params.row.required ? h('div', '是') : h('div', '否');
             // }
           }
         },
         {
           type: 'html',
           title: '描述',
           align: 'center',
           key: 'description'
         },
         {
           title: '约束',
           align: 'center',
           render: (h, params) => {
             var minimum, maximum, maxLength, minLength, notMin, notMax, maxItems, minItems, pattern
             notMin = params.row.exclusiveMinimum ? '不包含' : '包含'
             notMax = params.row.exclusiveMaximum ? '不包含' : '包含'
             maximum = params.row.maximum ? `最大值：${params.row.maximum}(${notMax})` : ''
             minimum = params.row.minimum ? `最小值：${params.row.minimum}(${notMin})` : ''
             maxLength = params.row.maxLength ? `最大长度：${params.row.maxLength}` : ''
             minLength = params.row.minLength ? `最小长度：${params.row.minLength}` : ''
             maxItems = params.row.maxItems ? `最大数量：${params.row.maxItems}` : ''
             minItems = params.row.minItems ? `最小数量：${params.row.minItems}` : ''
             pattern = params.row.pattern ? `正则：${params.row.pattern}` : ''
             return h('div', [
               h('p', minimum),
               h('p', maximum),
               h('p', maxLength),
               h('p', minLength),
               h('p', maxItems),
               h('p', minItems),
               h('p', pattern)
             ]);
           }
         },
         {
           title: '操作',
           align: 'center',
           render: (h, params) => {
             return h('div', [
               h('Button', {
                 props: {
                   type: 'text',
                   size: 'small'
                 },
                 on: {
                   click: () => {
                     this.edit_param(params.index, params.row, 'json', 'modal_param_content_model')
                   }
                 }
               }, '编辑'),
               h('Button', {
                 props: {
                   type: 'text',
                   size: 'small'
                 },
                 on: {
                   click: () => {
                     this.delete_param_in_panel(params.index, params);
                   }
                 }
               }, '删除')
             ]);
           }

         }
       ],
       columns_propertiesList_detail: [
         {
           title: '名称',
           align: 'center',
           key: 'name',
           render: (h, params) => {
             if (params.row.name) {
               return h('div', params.row.name);
             } else {
               return h('div', '--');
             }
           }
         },
         {
           title: '类型-格式',
           key: 'type',
           align: 'center',
           render: (h, params) => {
             if (typeof params.row.type == 'object') {
               return h('div', [h('p', params.row.type[0]),
                 h('p', params.row.type[1])]);
             } else if (params.row.format) {
               return h('div', [h('p', params.row.type),
                 h('p', params.row.format)]);
             } else {
               return h('div', [h('p', params.row.type),
                 h('p', params.row.type)]);
             }
           }
         },
         {
           align: 'center',
           title: '必需',
           key: 'must',
           render: (h, params) => {
             return params.row.required ? h('div', '是') : h('div', '否');
           }
         },
         {
           title: '描述',
           key: 'description',
           align: 'center'

         },
         {
           title: '约束',
           align: 'center',
           render: (h, params) => {
             var minimum, maximum, maxLength, minLength, notMin, notMax, maxItems, minItems, pattern
             notMin = params.row.exclusiveMinimum ? '不包含' : '包含'
             notMax = params.row.exclusiveMaximum ? '不包含' : '包含'
             minimum = params.row.minimum ? `最小值：${params.row.minimum}(${notMin})` : ''
             maximum = params.row.maximum ? `最大值：${params.row.maximum}(${notMax})` : ''
             maxLength = params.row.maxLength ? `最大长度：${params.row.maxLength}` : ''
             minLength = params.row.minLength ? `最小长度：${params.row.minLength}` : ''
             maxItems = params.row.maxItems ? `最大数量：${params.row.maxItems}` : ''
             minItems = params.row.minItems ? `最小数量：${params.row.minItems}` : ''
             pattern = params.row.pattern ? `正则${params.row.pattern}` : ''
             return h('div', [
               h('p', minimum),
               h('p', maximum),
               h('p', maxLength),
               h('p', minLength),
               h('p', maxItems),
               h('p', minItems),
               h('p', pattern)
             ]);
           }
         },
         {
           title: '操作',
           align: 'center',
           render: (h, params) => {
             return h('div', [
               h('Button', {
                 props: {
                   type: 'text',
                   size: 'small'
                 },
                 on: {
                   click: () => {
                     this.detail_param(params.index, params.row, 'detail_model_show', 'model_detail_page')
                   }
                 }
               }, '详情')
             ]);
           }

         }
       ],
       data_propertiesList: [],
       submit_modal: [],
       current_modify_model: [],
       current_index: -1,
       current_model: [],
       current_modelCode: '',
       data_model: [],
       form_data: {
         name: '',
         apiGroup: '',
         description: '',
         param: '', // 参数
         title: '', // 名称
         type: ['object'], // 类型
         required: false, // 是否必填
         sensitive: false, // 名称
         constrains: ''
       },

       // 表格显示绑定
       form_data_visual: {
         extremum: false,
         enums: false,
         length: false,
         pattern: false,
         itemSize: false,
         model: false,
         additionalProperties: false,
         items: false,
         order: false
       },
       current_panel: 'detail_model',
       // 窗口是否展示
       modal_show: false,
       // 示例值绑定
       data_select_sample: '',
       // 常用正则表达式数据绑定
       data_select_pattern: '',
       // 枚举数据绑定
       enum_data: [{
         value: '',
         index: 0,
         status: 1
       }],
       enum_index: 0,
       // 是否展示更多属性 false 为隐藏 true 为显示
       more_attr: true,

       type_data: [],
       type_data_cut: [],
       sample_data: [
         {
           value: 'integer',
           desc: 'integer',
           formats: [
             {
               value: 'int32',
               desc: 'int32',
               defaulted: true,
               constrains: [
                 'extremum'
               ]
             },
             {
               value: 'int64',
               desc: 'int64',
               defaulted: true,
               constrains: [
                 'extremum'
               ]
             }
           ]
         },
         {
           value: 'number',
           desc: 'number',
           formats: [
             {
               value: '-',
               desc: '-',
               defaulted: true,
               constrains: [
                 'extremum'
               ]
             },
             {
               value: 'float',
               desc: 'float',
               defaulted: true,
               constrains: [
                 'extremum'
               ]
             },
             {
               value: 'double',
               desc: 'double',
               defaulted: true,
               constrains: [
                 'extremum'
               ]
             }
           ]
         },
         {
           value: 'boolean',
           desc: 'boolean',
           formats: [
             {
               value: '-',
               desc: '-',
               defaulted: true,
               constrains: [
 
               ]
             }
           ]
         },
         {
           value: 'string',
           desc: 'string',
           formats: [
             {
               value: '-',
               desc: '-',
               defaulted: true,
               constrains: [
                 'length',
                 'pattern'
               ]
             },
             {
               value: 'byte',
               desc: 'byte',
               defaulted: true
             },
             {
               value: 'binary',
               desc: 'binary',
               defaulted: true
             },
             {
               value: 'date',
               desc: 'date',
               defaulted: true
             },
             {
               value: 'date-time',
               desc: 'date-time',
               defaulted: true
             },
             {
               value: 'password',
               desc: 'password',
               defaulted: true
             },
             {
               value: 'email',
               desc: 'email',
               defaulted: true
             },
             {
               value: 'mobile',
               desc: 'mobile',
               defaulted: true
             },
             {
               value: 'idcard',
               desc: 'idcard',
               defaulted: true
             },
             {
               value: 'bankcard',
               desc: 'bankcard',
               defaulted: true
             },
             {
               value: 'cvv',
               desc: 'cvv',
               defaulted: true
             },
             {
               value: 'uuid',
               desc: 'uuid',
               defaulted: true
             }
           ]
         },
         {
           value: 'array',
           desc: 'array',
           formats: [
             {
               value: '-',
               desc: '-',
               defaulted: true,
               constrains: [
                 'items',
                 'itemSize'
               ]
             }
           ]
         },
         {
           value: 'object',
           desc: 'object',
           formats: [
             {
               value: '-',
               desc: '-',
               defaulted: true
             },
             {
               value: 'map',
               desc: 'map',
               defaulted: true,
               constrains: [
                 'additionalProperties'
               ]
             }
           ]
         }
       ],
       // 当前编辑行数
       current_line_index: 0,
       sensitiveVariables: [],
       bizOrderVariable: '',
       underLine: false

     };
   },
   methods: {
     // 表格拖动排序
     dragSort () {
       const tbody = document.querySelector('#modal_edit_table tbody')
       const _this = this
       Sortable.create(tbody, {
         onEnd ({ newIndex, oldIndex }) {
           const currRow = _this.data_propertiesList.splice(oldIndex, 1)[0]
           _this.data_propertiesList.splice(newIndex, 0, currRow)
           _this.$forceUpdate()
         }
       })
     },
     // 获取model列表
     getModels () {
       this.$refs.modal_param_content_model.search_model_apiGroup = this.form_data.apiGroup
       localStorage.setItem('modelApiGroup', this.form_data.apiGroup)
     },
     delete_param_in_panel (index, params) {
       this.$Modal.confirm({
         title: '提示',
         content: '确定删除？',
         'ok-text': '确认',
         onOk: () => {
           this.data_propertiesList.splice(index, 1);
         }
       });
     },
     // 符合类型的model name
     isModelname (value) {
       // ([a-zA-Z][a-zA-Z\d_]*\.)*[a-zA-Z][a-zA-Z\d_]*
       var reg = /^[A-Z][a-zA-Z0-9]*$/;

       if (reg.test(value)) {
         return true
       } else {
         return false
       }
     },
     quick_sort (arr) {
       var arry1, arry2, mid;

       if (arr.length < 2) return arr;
       else if (arr.length == 2) {
         if (arr[0] > arr[1]) { return arr.reverse(); } else { return arr; }
       } else {
         arry1 = new Array();
         arry2 = new Array();
         mid = arr[0];
         for (var i = 1; i < arr.length; i++) {
           if (arr[i] < mid) { arry1.push(arr[i]); } else { arry2.push(arr[i]); }
         }
       }
       return (this.quick_sort(arry1).concat(mid, this.quick_sort(arry2)));
     },
     format_submit_param (param) {
       var param = JSON.parse(JSON.stringify(param));
       var tempProperties = {properties: {}}
       var requiredArr = []
       this.sensitiveVariables = []
       for (var k = 0; k < param.length; k++) {
         var paramK = param[k]
         tempProperties['properties'][paramK['name']] = {};
         for (var i in paramK) {
           if (paramK['type'][1] == 'map' && i == 'additionalProperties') {
             tempProperties['properties'][paramK['name']]['additionalProperties'] = {}
             for (var o in paramK[i]) {
               if (paramK.additionalProperties.model) {
                 tempProperties['properties'][paramK['name']]['additionalProperties']['$ref'] = `#/components/schemas/${paramK[i].model}`
               } else {
                 if (o == 'type') {
                   if (typeof paramK[i][o] == 'string') {
                     paramK[i]['type'] = [paramK[i]['type'], paramK[i]['format']]
                   }
                   // tempProperties["properties"][paramK["name"]]["additionalProperties"]["format"] = paramK[i]["type"][1]
                   tempProperties['properties'][paramK['name']]['additionalProperties']['type'] = paramK[i]['type'][0]
                 } else if (paramK[i][o] && o != 'constrains') {
                   tempProperties['properties'][paramK['name']]['additionalProperties'][o] = paramK[i][o]
                 }
               }
             }
           }
           if (paramK['type'][1] == 'array' && i == 'items') {
             tempProperties['properties'][paramK['name']]['items'] = {}
             for (var r in paramK.items) {
               if (paramK.items.model) {
                 tempProperties['properties'][paramK['name']]['items']['$ref'] = `#/components/schemas/${paramK[i].model}`
               } else {
                 if (r == 'type') {
                   // TODO
                   if (typeof paramK[i][r] == 'string') {
                     paramK[i]['type'] = [paramK[i]['type'], paramK[i]['format']]
                   }
                   // tempProperties["properties"][paramK["name"]]["items"]["format"] = paramK[i]["type"][1]
                   tempProperties['properties'][paramK['name']]['items']['type'] = paramK[i]['type'][0]
                 } else if (paramK[i][r] && r != 'constrains') {
                   tempProperties['properties'][paramK['name']]['items'][r] = paramK[i][r]
                 }
               }
             }
           }
           if (i == 'type') {
             if (paramK[i][1] == 'object') {
               tempProperties['properties'][paramK['name']][i] = 'object'
               if (paramK.model) {
                 tempProperties['properties'][paramK['name']]['$ref'] = `#/components/schemas/${paramK.model}`;
               }
             } else {
               paramK.$ref = ''
               tempProperties['properties'][paramK['name']][i] = paramK[i][0]
               if (paramK[i][0] != paramK[i][1]) {
                 tempProperties['properties'][paramK['name']]['format'] = paramK[i][1]
               }
             }
           }

           if (i == 'required' && paramK[i]) {
             requiredArr.push(paramK['name']);
             tempProperties[i] = requiredArr
           }
           if (i == 'sensitive' && paramK['sensitive']) {
             this.sensitiveVariables.push(paramK['name'])
           }
           if (i == 'orderNo' && paramK['orderNo']) {
             this.bizOrderVariable = paramK['name']
           }
           if (paramK[i] != '' && i != 'title' && i != 'type' && i != 'required' && i != 'name' && i != 'model' && i != '_index' && i != '_rowKey' && i != 'format' && i != 'items' && i != 'additionalProperties' && i != 'constrains') {
             tempProperties['properties'][paramK['name']][i] = paramK[i]
           }
         }
       }
       console.log('最终返回的tempProperties编辑2', tempProperties)

       if (requiredArr.length > 0) {
         console.log(requiredArr)
         tempProperties['required'] = this.quick_sort(requiredArr)
       }
       console.log(tempProperties, '=====')
       return tempProperties
     },
     detail_format_param (param) {
       var param = JSON.parse(JSON.stringify(param));
     },
     edit_format_submit_param (param) {
       var param = JSON.parse(JSON.stringify(param));
       var tempProperties = {properties: {}}
       var requiredArr = []
       for (var k = 0; k < param.length; k++) {
         var paramK = param[k]
         tempProperties['properties'][paramK['name']] = {};
         for (var i in paramK) {
           if (paramK['type'][1] == 'map' && i == 'additionalProperties') {
             tempProperties['properties'][paramK['name']]['additionalProperties'] = {}
             for (var o in paramK[i]) {
               tempProperties['properties'][paramK['name']]['additionalProperties'][o] = paramK[i][o]
             }
             paramK[i] = tempProperties['properties'][paramK['name']]['additionalProperties']
           }
           if (paramK['type'][1] == 'array' && i == 'items') {
             tempProperties['properties'][paramK['name']]['items'] = {}
             for (var r in paramK.items) {
               tempProperties['properties'][paramK['name']]['items'][o] = paramK[i][o]
             }
             paramK[i] = tempProperties['properties'][paramK['name']]['items']
           }
           if (i == 'type') {
             if (paramK[i][1] == 'object') {
               tempProperties['properties'][paramK['name']][i] = 'object'
               tempProperties['properties'][paramK['name']]['$ref'] = `#/components/schemas/${paramK.model}`;
             } else {
               tempProperties['properties'][paramK['name']][i] = paramK[i][0]
               if (paramK[i][0] != paramK[i][1]) {
                 tempProperties['properties'][paramK['name']]['format'] = paramK[i][1]
               }
             }
           }

           if (i == 'required' && paramK[i]) {
             requiredArr.push(paramK['name']);
             tempProperties[i] = requiredArr
           }
           if (this.sensitiveVariables) {
             for (var i = 0; i < this.sensitiveVariables.length; i++) {
               var sensitiveArrName = this.sensitiveVariables[i]
               for (var j = 0; j < tempProperties['properties'][paramK['name']].length; j++) {
                 if (sensitiveArrName == tempProperties['properties'][paramK['name']][j].name) {
                   tempProperties['properties'][paramK['name']]['sensitive'] = true
                 }
               }
             }
           }
           if (this.bizOrderVariable) {
             var bizOrderVariable = this.bizOrderVariable
             for (var j = 0; j < tempProperties['properties'][paramK['name']].length; j++) {
               if (bizOrderVariable == tempProperties['properties'][paramK['name']][j].name) {
                 tempProperties['properties'][paramK['name']]['orderNo'] = true
               }
             }
           }

           if (paramK[i] != '' && i != 'title' && i != 'type' && i != 'required' && i != 'name' && i != 'model' && i != '_index' && i != '_rowKey' && i != 'format' && i != 'items' && i != 'additionalProperties' && i != 'constrains') {
             tempProperties['properties'][paramK['name']][i] = paramK[i]
           }
         }
         console.log('最终返回的tempProperties编辑', tempProperties)
       }
     },
     // 更新属性列表哦
     data_update_model (data, type, index, subContent) {
       console.log(subContent, '第二部分传值')
       console.log(data, '1更新属性列表哦')
       data.additionalProperties = {}
       data.items = {}
       if (data.constrains) {
         if (data.constrains[0] == 'additionalProperties') {
           data.additionalProperties = subContent
         } else if (data.constrains[0] == 'items') {
           data.items = subContent
         }
       }
       // for(var m = 0 ;m < this.data_propertiesList.length ; m ++){
       //     for(var n in this.data_propertiesList[m]){
       //         if(n == "name"){
       //             if(this.data_propertiesList[m][n] == data.name){
       //                 this.data_propertiesList.splice(m,1);
       //             }
       //         }
       //     }
       // }
       var data = JSON.parse(JSON.stringify(data))
       if (type === 'create_model') {
         this.data_propertiesList.push(data);
         // 现在为不包含子项的处理 需要在提交的时候2次数据规范
       } else {
         // this.data_propertiesList.push(data);

         // 编辑查看
         // if(data.type[1]){
         //     data["format"] = data.type[1]
         // }
         for (var i in data) {
           this.data_propertiesList[index][i] = data[i]
         }
       }
       for (var i = 0; i < this.data_propertiesList.length; i++) {
         var data_propertiesListI = this.data_propertiesList[i]
         for (var j in data_propertiesListI) {
           if (j == 'orderNo') {
             if (data_propertiesListI[j]) {
               this.bizOrderVariable = data_propertiesListI['name']
             } else {
               delete data_propertiesListI['orderNo']
               this.bizOrderVariable = ''
             }
           }
         }
       }
       this.$refs.modal_param_content_model.cancel();
       this.$refs.modal_param_content_model.$refs.form_data_right.resetFields();
       this.$refs.modal_param_content_model.$refs.form_data_left.resetFields();
     },
 
     // 新增参数
     new_param (panel, external) {
       if (!this.form_data.apiGroup) {
         this.$Modal.warning({
           title: '警告',
           content: '请先选择API分组'
         });
       } else {
         this.form_data.$ref = ''
         this.$refs[panel].show(this.data_propertiesList, 'create', false);
         this.form_data.data_propertiesList = [];
         this.$refs[panel].current_panel_set('create_model');
         this.$refs[panel].resetRef();
         var self = this
         setTimeout(function () {
           self.$refs[panel].$refs.form_data_left.resetFields();
           self.$refs[panel].$refs.form_data_right.resetFields();
           self.$refs[panel].$refs.subContent_map.$refs.form_data_left2.resetFields();
           self.$refs[panel].$refs.subContent_map.$refs.form_data_right2.resetFields();
           self.$refs[panel].$refs.subContent_array.$refs.form_data_left2.resetFields();
           self.$refs[panel].$refs.subContent_array.$refs.form_data_right2.resetFields();
           self.$refs[panel].$refs.subContent_map.type_rule_init();
           self.$refs[panel].$refs.subContent_array.type_rule_init();
           self.$refs[panel].type_rule_init();
           self.$refs[panel].form_data.name = '';
         }, 300)
         this.$refs[panel].form_data.type = [];
         var apiGroup = this.form_data.apiGroup || this.current_modelCode
         this.$refs[panel].setApiGroup(apiGroup);
         this.$refs[panel].type_rule_init();
         // this.current_index = -1;
         if (external) {
           // this.current_modify_model = dataTemp;
         }
       }
     },
     detail_param (index, data, current_data, panel) {
       this.$refs[panel].current_panel_set(current_data);
       this.$refs[panel].current_index_set(index);
       this.$refs[panel].form_data_set(JSON.parse(JSON.stringify(data)), this.sensitiveVariables, this.bizOrderVariable);
       this.current_index = -1;
       this.$refs[panel].show();
     },
     edit_param (index, data, current_data, panel) {
       if (!data.orderNo) {
         delete data.orderNo
       }
       // this.$refs[panel].$refs.form_data_left.resetFields();
       // this.$refs[panel].$refs.form_data_right.resetFields();
       this.$refs[panel].current_panel_set('modify_model');
       this.$refs[panel].current_index_set(index);
       var apiGroup = this.current_modelCode || localStorage.getItem('modelApiGroup')
       this.$refs[panel].setApiGroup(apiGroup);
       // if(data.format){
       //     data.type = [data.type,data.format]
       // }else{
       //     data.type = data.type
       // }
       // if(typeof data.type == "object"){
       //     data.type = data.type
       // }else{
       //     data.type = [data.type,data.type == "object" ? "map":data.type]
       // }
       // if(data.format){
       //     data.type[1] = data.format
       // }
       var showOrder = true
       if (data.type[0] !== 'string') { showOrder = false }
       console.log(data, '2datatdatdta')
       this.$refs[panel].form_data_set(JSON.parse(JSON.stringify(data)), this.data_propertiesList, showOrder);
       this.current_modify_model = current_data;
       this.current_index = -1;
       this.$refs[panel].show(this.data_propertiesList, 'modify', showOrder);
     },
     // 当前页面设置
     current_panel_set (val) {
       this.current_panel = val;
     },
     // 页面初始化
     init () {
       this.data_format(this.sample_data);
       this.cascader_format();
       api.yop_dashboard_apis_list().then(
         (response) => {
           this.apiGroup_List = [];
           if (this.current_status === 'create') {
             // this.form_data.apigroup ='';
           }
           let apigroup = response.data.data.result;
           this.form_data.apigroup = apigroup[0].groupCode;
           for (var i in apigroup) {
             this.apiGroup_List.push(
               {
                 label: apigroup[i].apiGroupCode + '(' + apigroup[i].apiGroupName + ')',
                 value: apigroup[i].apiGroupCode,
                 name: apigroup[i].apiGroupName
               }
             )
           }
         }
       );
     },
     // 表单提交
     submit () {
       if (this.current_panel === 'detail_model') {
         this.modal_show = false;
       }
       if (this.data_propertiesList.length <= 0) {
         this.$Modal.warning({
           title: '警告',
           content: '请新增属性'
         });
         return false;
       }
       // if(this.current_panel ==='create_model'){
       //     this.form_submit_validate('form_data_top','form_data_left')
       // }else{
       this.form_submit_validate('form_data_top')
       // }
       // this.form_submit_validate('form_data_top','form_data_left','form_data_right')
     },
     // 继续验证
     next_validate () {
       if (this.form_data_visual.additionalProperties) {
         this.$refs['subContent_map'].submit();
       } else if (this.form_data_visual.items) {
         this.$refs['subContent_array'].submit();
       } else {
         this.tempProperties = this.format_submit_param(this.data_propertiesList);
         if (this.current_panel === 'create_model') {
           var param = {
             name: this.form_data.name,
             apiGroup: this.form_data.apiGroup,
             description: this.form_data.description,
             schema: {
               // title:this.form_data.title,
               type: 'object',
               properties: this.tempProperties.properties,
               required: this.tempProperties.required
             },
             bizOrderVariable: this.bizOrderVariable,
             sensitiveVariables: this.sensitiveVariables

           }
         } else {
           var param = {
             description: this.form_data.description,
             schema: {
               title: this.form_data.title,
               type: 'object',
               properties: this.tempProperties.properties,
               required: this.tempProperties.required
             },
             bizOrderVariable: this.bizOrderVariable,
             sensitiveVariables: this.sensitiveVariables

           }
         }
         if (!param.bizOrderVariable) { delete param.bizOrderVariable }
         var stringSchema = JSON.stringify(param.schema)
         param.schema = stringSchema
         this.$emit('data_update', param, this.current_panel, this.current_line_index);
         // 新建或者编辑操作执行
       }
     },
     // 提交函数

     // 表单提交验证
     form_submit_validate (val1, val2) {
       let result1 = 0;
       let result2 = 0;
       let result3 = 0;
       let check = 0;
       // if(this.current_panel === "create_model"){
       //     this.$refs[val1].validate((valid) => {
       //         if (valid) {
       //             result1 =1;
       //             if(result2 === 1){
       //                 this.next_validate();
       //             }

       //         }else {
       //             this.form_validate_failed()
       //         }
       //     })
       //     this.$refs[val2].validate((valid) => {
       //         if (valid) {
       //             result2 =1;
       //             if(result1 === 1){
       //                 this.next_validate();
       //             }
       //         }else {
       //             this.form_validate_failed()
       //         }
       //     })
       // }else{
       this.$refs[val1].validate((valid) => {
         if (valid) {
           this.next_validate();
         } else {
           this.form_validate_failed()
         }
       })
       // }
 
       // this.$refs[val3].validate((valid) => {
       //     console.log(valid)
       //     if (valid) {
       //         result3 =1;
       //         if(result1 === 1 && result2 === 1){
       //             this.next_validate();
       //         }
       //     }else {
       //         this.form_validate_failed()
       //     }
       // })
     },
     // 表单验证失败
     form_validate_failed () {
       this.$Message.error('请检查');
     },
     // 表单取消
     cancel () {
       this.modal_show = false;
     },
     // 窗口显示
     show () {
       this.modal_show = true;
     },
 
     // 数据处理
     data_format (array) {
       this.type_data = [];
       this.type_data_cut = [];
       for (var i in array) {
         let subContent = array[i].formats;
         let childrenTemp = []
         for (var j in subContent) {
           if (subContent[j].value === '-') {
             childrenTemp.push(
               {
                 value: array[i].value,
                 label: array[i].desc,
                 constrains: subContent[j].constrains ? subContent[j].constrains : []
               }
             );
           } else {
             childrenTemp.push(
               {
                 value: subContent[j].value,
                 label: subContent[j].desc,
                 constrains: subContent[j].constrains ? subContent[j].constrains : []
               }
             );
           }
         }
         this.type_data_cut.push(
           {
             value: array[i].value,
             label: array[i].desc,
             children: childrenTemp
           }
         )
         this.type_data.push(
           {
             value: array[i].value,
             label: array[i].desc,
             children: childrenTemp
           }
         )
       }
     },
     // 级联 参数类型 数据获取及处理
     cascader_format () {

     },
     cleanData () {
       this.$refs.form_data_top.resetFields();
       this.form_data.name = '';
       this.form_data.description = '';
     },
 
     // 设置当前页面数据
     form_data_set (data, bizOrderVariable, sensitiveVariables, type) {
       console.log(11111)
       if (!data) {
         // 新增
         this.tempProperties.properties = {}
         this.tempProperties.required = []
         this.cleanData()
         var _self = this
         setTimeout(() => {
           _self.cleanData()
           _self.$refs.select_apiM_2.clearSingleSelect();
           _self.show_loading_basic = false
         }, 300);
         this.$refs.modal_param_content_model.setUnderLine(false)
       } else {
         this.bizOrderVariable = bizOrderVariable
         this.sensitiveVariables = sensitiveVariables
         // 编辑
         this.show_loading_basic = false
         this.form_data = data;
         this.current_modelCode = data.apiGroup
         localStorage.setItem('modelApiGroup', data.apiGroup)
         this.data_propertiesList = data.data_propertiesList
         for (let i in this.data_propertiesList) {
           var paramK = this.data_propertiesList[i]
           if (paramK.name.indexOf('_') > -1) {
             this.underLine = true
             this.$refs.modal_param_content_model.setUnderLine(this.underLine)
           }
         }
 
         if (type === 'detail') {
           this.detail_format_param(data.data_propertiesList)
         } else {
           this.edit_format_submit_param(data.data_propertiesList);
         }
       }
       this.$nextTick(() => {
         this.dragSort()
       })
     },
     // 设置当前行数
     current_index_set (index) {
       this.current_line_index = index
     }
   },
   mounted () {
     this.init();
   }
 };
</script>

<style scoped>

</style>
