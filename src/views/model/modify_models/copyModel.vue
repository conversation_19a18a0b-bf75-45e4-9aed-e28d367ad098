<style lang="less">
    .width-50-perc{
        width:45%;
    }
    .width-100-perc{
        width:100%;
    }
    .margin-top-0{
        margin-top : 0!important;
    }
</style>
<template>
    <Row>
      <Modal id="modal_copy_1" v-model="modal_show" :closable="false" width="60%">
          <p slot="header" style="color:#2d8cf0;">
              <span style="color:black" >复制模型</span>
          </p>
          <Row>
              <Col span="24">
                  <p>基本信息</p>
                  <Form ref="form_data_copy"  :model="form_data" :rules="form_data_validate"  :label-width="120" inline >
                      <FormItem label="API分组："  prop="apiGroup" class="width-100-perc">
                        <p>{{form_data.apiGroup}}</p>
                      </FormItem>
                      <FormItem label="模型名称：" prop="copyName" class="width-100-perc">
                          <Input type="text"  v-model="form_data.copyName"  style="width: 380px;" ></Input>
                        <p class="yop-explain-120" style="margin-bottom:10px;display:inline-block;">支持大写开头的驼峰</p>
                      </FormItem>
                      <FormItem prop="copyDescription" label="模型描述：" class="width-100-perc" :rules="{required:true,message:'模型描述不能为空',trigger:'blur'}">
                        <Input type="textarea" v-model="form_data.copyDescription"  style="width: 380px;"></Input>
                      </FormItem>
                  </Form>
              </Col>
          </Row>
          <Row>
              <Table id='modal_copy_table1' border ref="properties" :columns="columns_propertiesList" :data="data_propertiesList"></Table>
              <loading :show="show_loading_basic"></loading>
          </Row>
          <div slot="footer">
              <Button type="primary" id="copybtn-save" @click="submit">保存</Button>
          </div>
      </Modal>
    </Row>
</template>

<script>
import loading from '../../my-components/loading/loading';
import api from '~/api/api'
 export default {
   name: 'copy_model',
   components: {
     loading,
   },
   data () {
     const validate_basic_name = (rule, value, callback) => {
       if (!this.isModelname(value)) {
         callback(new Error('支持大写开头的驼峰'));
       } else {
         callback();
       }
     };
     return {
        // 窗口是否展示
        modal_show: false,
        form_data: {
          apiGroup: '',
          copyName: '',
          copyDescription: '',
        },
        form_data_validate: {
          apiGroup: [{required: true, message: 'apiGroup不能为空', trigger: 'blur'}],
          copyName: [{required: true, message: '名称不能为空'}, { validator: validate_basic_name, trigger: 'blur' }],
          copyDescription: [{required: true, message: '描述不能为空'}]
        },
        show_loading_basic: false,
        data_propertiesList: [],
        tempProperties: {},
        propertiesDetails: {},
        sensitiveVariables: [],
        bizOrderVariable: '',
        columns_propertiesList: [
         {
          title: '名称',
          key: 'name',
          align:"center",
          render: (h, params) => {
            if (params.row.name) {
              return h('div', params.row.name);
            } else {
              return h('div', '--');
            }
          }
         },
         {
          title: '类型',
          key: 'type',
          align:"center",
          render: (h, params) => {
            if (typeof params.row.type == 'object') {
              return h('div', [h('p', params.row.type[0]),
                h('p', params.row.type[1])]);
            } else if (params.row.format) {
              return h('div', [h('p', params.row.type),
                h('p', params.row.format)]);
            } else {
              return h('div', [h('p', params.row.type),
                h('p', params.row.type)]);
            }
          }
         },
         {
          title: '必需',
          align:"center",
          key: 'must',
           render: (h, params) => {
             return params.row.required ? h('div', '是') : h('div', '否');
           }
         },
         {
          title: '描述',
          key: 'description',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('div', {
                domProps: {
                  innerHTML: params.row.description
                }
              }, '')
            ])
          }
         },
        {
          title: '约束',
          align: 'center',
          render: (h, params) => {
            var minimum, maximum, maxLength, minLength, notMin, notMax, maxItems, minItems, pattern
            notMin = params.row.exclusiveMinimum ? '不包含' : '包含'
            notMax = params.row.exclusiveMaximum ? '不包含' : '包含'
            maximum = params.row.maximum ? `最大值：${params.row.maximum}(${notMax})` : ''
            minimum = params.row.minimum ? `最小值：${params.row.minimum}(${notMin})` : ''
            maxLength = params.row.maxLength ? `最大长度：${params.row.maxLength}` : ''
            minLength = params.row.minLength ? `最小长度：${params.row.minLength}` : ''
            maxItems = params.row.maxItems ? `最大数量：${params.row.maxItems}` : ''
            minItems = params.row.minItems ? `最小数量：${params.row.minItems}` : ''
            pattern = params.row.pattern ? `正则：${params.row.pattern}` : ''
            return h('div', [
              h('p', minimum),
              h('p', maximum),
              h('p', maxLength),
              h('p', minLength),
              h('p', maxItems),
              h('p', minItems),
              h('p', pattern)
            ]);
          }
        },
        ],
     };
   },
   methods: {
    // 页面初始化
    init (row) {
      // 获取属性相关信息
      api.yop_modalManagement_modal_detail({id: row.id}).then(
        (response) => {
          var response = response.data
          if (response.status === 'success') {
            var schema = JSON.parse(response.data.result.schema)
            this.propertiesDetails = schema.properties;
            var arr = []
            for (let i in this.propertiesDetails) {
              let o = {};
              o['name'] = i;
              for (var k in schema.required) {
                var requiredName = schema.required[k]
                if (requiredName === i) {
                  o['required'] = true
                }
              }
              for (var j in this.propertiesDetails[i]) {
                o[j] = this.propertiesDetails[i][j]
                if (j == 'type') {
                  o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
                } else if (j == '$ref') {
                  o['type'] = ['object', 'object']
                } else if (j == 'additionalProperties') {
                  o['type'] = ['object', 'map']
                }
              }
              for (var k in o) {
                if (k == 'format') {
                  o['type'][1] = o[k]
                }
              }
              arr.push(o)
            }
            // 处理显示数据
            // 显示数据处理
            this.data_propertiesList = arr
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      );
    },
    // 符合类型的model name
     isModelname (value) {
       var reg = /^[A-Z][a-zA-Z0-9]*$/;
       if (reg.test(value)) {
         return true
       } else {
         return false
       }
     },
    // 表单提交
    submit () {
     this.form_submit_validate('form_data_copy')
    },
    // 表单提交验证
    form_submit_validate (val1, val2) {
      this.$refs[val1].validate((valid) => {
        if (valid) {
          this.next_validate();
        } else {
          this.form_validate_failed()
        }
      })
    },
    // 继续验证
    next_validate () {
      this.tempProperties = this.format_submit_param(this.data_propertiesList);
        // 不是object类型时 去掉 testName $ref
        for (const key in this.tempProperties.properties) {
          const element = this.tempProperties.properties[key];
          if(element.type !== 'object') {
            delete element.$ref;
          }
        }
        var param = {
          name: this.form_data.copyName,
          apiGroup: this.form_data.apiGroup,
          description: this.form_data.copyDescription,
          schema: {
            // title:this.form_data.title,
            type: 'object',
            properties: this.tempProperties.properties,
            required: this.tempProperties.required
          },
          bizOrderVariable: this.bizOrderVariable,
          sensitiveVariables: this.sensitiveVariables
        }
        if (!param.bizOrderVariable) { delete param.bizOrderVariable }
        var stringSchema = JSON.stringify(param.schema)
        param.schema = stringSchema
        this.copyModel(param)
    },
    // 表单验证失败
    form_validate_failed () {
      this.$Message.error('请检查');
    },
    // 格式化参数
    format_submit_param (param) {
      var param = JSON.parse(JSON.stringify(param));
      var tempProperties = {properties: {}}
      var requiredArr = []
      this.sensitiveVariables = []
      for (var k = 0; k < param.length; k++) {
        var paramK = param[k]
        tempProperties['properties'][paramK['name']] = {};
        for (var i in paramK) {
          if (paramK['type'][1] == 'map' && i == 'additionalProperties') {
            tempProperties['properties'][paramK['name']]['additionalProperties'] = {}
            for (var o in paramK[i]) {
              if (paramK.additionalProperties.model) {
                tempProperties['properties'][paramK['name']]['additionalProperties']['$ref'] = `#/components/schemas/${paramK[i].model}`
              } else {
                if (o == 'type') {
                  if (typeof paramK[i][o] == 'string') {
                    paramK[i]['type'] = [paramK[i]['type'], paramK[i]['format']]
                  }
                  // tempProperties["properties"][paramK["name"]]["additionalProperties"]["format"] = paramK[i]["type"][1]
                  tempProperties['properties'][paramK['name']]['additionalProperties']['type'] = paramK[i]['type'][0]
                } else if (paramK[i][o] && o != 'constrains') {
                  tempProperties['properties'][paramK['name']]['additionalProperties'][o] = paramK[i][o]
                }
              }
            }
          }
          if (paramK['type'][1] == 'array' && i == 'items') {
            tempProperties['properties'][paramK['name']]['items'] = {}
            for (var r in paramK.items) {
              if (paramK.items.model) {
                tempProperties['properties'][paramK['name']]['items']['$ref'] = `#/components/schemas/${paramK[i].model}`
              } else {
                if (r == 'type') {
                  // TODO
                  if (typeof paramK[i][r] == 'string') {
                    paramK[i]['type'] = [paramK[i]['type'], paramK[i]['format']]
                  }
                  // tempProperties["properties"][paramK["name"]]["items"]["format"] = paramK[i]["type"][1]
                  tempProperties['properties'][paramK['name']]['items']['type'] = paramK[i]['type'][0]
                } else if (paramK[i][r] && r != 'constrains') {
                  tempProperties['properties'][paramK['name']]['items'][r] = paramK[i][r]
                }
              }
            }
          }
          if (i == 'type') {
            if (paramK[i][1] == 'object') {
              tempProperties['properties'][paramK['name']][i] = 'object'
              if (paramK.model) {
                tempProperties['properties'][paramK['name']]['$ref'] = `#/components/schemas/${paramK.model}`;
              }
            } else {
              paramK.$ref = ''
              tempProperties['properties'][paramK['name']][i] = paramK[i][0]
              if (paramK[i][0] != paramK[i][1]) {
                tempProperties['properties'][paramK['name']]['format'] = paramK[i][1]
              }
            }
          }

          if (i == 'required' && paramK[i]) {
            requiredArr.push(paramK['name']);
            tempProperties[i] = requiredArr
          }
          if (i == 'sensitive' && paramK['sensitive']) {
            this.sensitiveVariables.push(paramK['name'])
            tempProperties['properties'][paramK['name']]['sensitive'] = true
          }
          if (i == 'orderNo' && paramK['orderNo']) {
            this.bizOrderVariable = paramK['name']
          }
          if (paramK[i] != '' && i != 'title' && i != 'type' && i != 'required' && i != 'name' && i != 'model' && i != '_index' && i != '_rowKey' && i != 'format' && i != 'items' && i != 'additionalProperties' && i != 'constrains') {
            tempProperties['properties'][paramK['name']][i] = paramK[i]
          }
        }
      }
      console.log(tempProperties, '<--复制模型保存的数据=====')
      return tempProperties
    },
    // 复制模型保存接口
    copyModel(data) {
      api.yop_modalManagement_modal_create(data).then(
        (response) => {
          if (response.status === 'success') {
            this.$ypMsg.notice_success(this, '复制成功');
            this.$emit('page_refresh')
            this.modal_show = false
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      );
    },
    // 表单取消
    cancel () {
      this.modal_show = false;
    },
    // 窗口显示
    show (row) {
      this.modal_show = true;
      this.form_data = row
      this.init(row)
    },
   },
   mounted () {
   }
 };
</script>

<style scoped>

</style>
