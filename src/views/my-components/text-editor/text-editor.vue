<style lang="less">
    @import '../../../styles/loading.less';
</style>

<template>
    <div>
        <Col :borderd="false" dis-hover style="width:567px;">
            <textarea :ref="refs" class='tinymce-textarea' :id="id"></textarea>
        <Spin fix v-show="spinShow">
        <Icon type="load-c" size=18 class="demo-spin-icon-load"></Icon>
        <div>加载组件中...</div>
        </Spin>
        </Col>

    </div>
</template>

<script>
import tinymce1 from 'tinymce';
export default {
    name: 'text-editor',
    props: {
        id : String,
        refs : String
    },
    data () {
        return {
            spinShow: true,
            status:"create"
        };
    },

    methods: {
        init () {
            var that = this
            this.$nextTick(() => {
                let vm = this;
                let height = document.body.offsetHeight - 300;
                // tinymce.init({
                //     selector: '#tinymceEditer',
                //     branding: false,
                //     elementpath: false,
                //     height: '50px',
                //     width: '600px',
                //     language: 'zh_CN.GB2312',
                //     menubar: 'edit insert view format table tools',
                //     plugins: [
                //         'advlist autolink lists link image charmap print preview hr anchor pagebreak imagetools',
                //         'searchreplace visualblocks visualchars code fullpage',
                //         'insertdatetime media nonbreaking save table contextmenu directionality',
                //         'emoticons paste textcolor colorpicker textpattern imagetools codesample'
                //     ],
                //     toolbar1: ' newnote print preview | undo redo | insert | styleselect | forecolor backcolor bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image emoticons media codesample',
                //     autosave_interval: '20s',
                //     image_advtab: true,
                //     table_default_styles: {
                //         width: '100%',
                //         borderCollapse: 'collapse'
                //     },
                //     setup: function (editor) {
                //         editor.on('init', function (e) {
                //             vm.spinShow = false;
                //             // if (localStorage.editorContent) {
                //             //     tinymce.get('tinymceEditer').setContent(localStorage.editorContent);
                //             // }
                //         });
                //         editor.on('keydown', function (e) {
                //             localStorage.editorContent = tinymce.get('tinymceEditer').getContent();
                //         });
                //     }
                // });

                // console.log(this.id);
                tinymce.init({
                    selector: `#${this.id}`,
                    branding: false,
                    elementpath: false,
                    height: '50px',
                    width: '567px',
                    language: 'zh_CN.GB2312',
                    menubar: '',
                    plugins: [
                        'advlist autolink lists link image charmap print preview hr anchor pagebreak imagetools',
                        'searchreplace visualblocks visualchars code fullpage',
                        'insertdatetime media nonbreaking save table contextmenu directionality',
                        'emoticons paste textcolor colorpicker textpattern imagetools codesample'
                    ],
                    toolbar1: 'undo redo | bullist numlist ',
                    autosave_interval: '20s',
                    image_advtab: true,
                    table_default_styles: {
                        width: '100%',
                        // borderCollapse: 'collapse'
                    },
                    setup: function (editor) {
                        editor.on('init', function (e) {
                            vm.spinShow = false;
                            // if (localStorage.editorContent) {
                            //     tinymce.get('tinymceEditer').setContent(localStorage.editorContent);
                            // }
                            if (localStorage.tinymceContent) {
                                tinymce.get(this.id).setContent(localStorage.tinymceContent)
                                localStorage.removeItem('tinymceContent');
                            }
                            if(that.status == "create"){
                                tinymce.get(this.id).setContent("")
                            }
                        });
                        editor.on('keydown', function (e) {
                            localStorage.editorContent = tinymce.get(this.id).getContent();
                        });
                    }
                });
            });
        },
        // 获取当前编辑器内容
        getContent () {
            let content =tinymce.get(this.id).getContent();
            let returnContent = content.replace('<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n','')
            returnContent =returnContent.replace('\n</body>\n</html>','');
            return returnContent;
            // return tinymce.get(this.id).getContent()
        },
        // 设置当前编辑器的内容
        setContent (content,status) {
            this.status = status
            if(content || content===0)
            return tinymce.get(this.id).setContent(content)
            
        },
        // 获取localstorage内容
        setLocalstorage (){
            if (localStorage.tinymceContent) {
                tinymce.get(this.id).setContent('<!DOCTYPE html>\n' +
                    '<html>\n' +
                    '<head>\n' +
                    '</head>\n' +
                    '<body>\n' +
                    '<p><a href="asd">asd</a></p>\n' +
                    '</body>\n' +
                    '</html>');
            }
        },
        // 获取纯文本内容
        getContent_text () {
            var activeEditor = tinymce.get(this.id);
            var editBody = activeEditor.getBody();
            activeEditor.selection.select(editBody);
            var text = activeEditor.selection.getContent( { 'format' : 'text' } );
            return text;
            // return tinymce.get(this.id).getContent({'format' : 'text' });
        },
        // 销毁插件
        destroySelf () {
            tinymce.get(this.id).destroy();
        }


    },
    mounted () {
        this.init();
    },
    watch : {

    },
    destroyed () {
        tinymce.get(this.id).destroy();
    }
};
</script>

<style>

</style>
