<style lang="less">
@import "../../../styles/loading.less";
</style>

<template>
	<div>
		<Col :borderd="false" dis-hover>
			<textarea :ref="refs" class="tinymce-textarea" :id="id"></textarea>
			<Spin fix v-show="spinShow">
				<Icon type="load-c" size="18" class="demo-spin-icon-load"></Icon>
				<div>加载组件中...</div>
			</Spin>
		</Col>
	</div>
</template>

<script>
import 'tinymce';
export default {
  name: 'text-editor-size',
  props: {
    id: String,
    refs: String,
    size: String,
    restrictElements: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      spinShow: true
    };
  },
  mounted () {
    this.init();
  },
  destroyed () {
    if (tinymce.get(this.id)) tinymce.get(this.id).destroy();
  },
  methods: {
    init () {
      this.$nextTick(() => {
        let vm = this;
        const config = {
          selector: `#${this.id}`,
          branding: false,
          elementpath: false,
          height: '50px',
          width: this.size,
          language: 'zh_CN.GB2312',
          indentation: '1em',
          menubar: '',
          plugins: [
            'advlist autolink lists link image charmap print preview hr anchor pagebreak ',
            'searchreplace visualblocks visualchars code fullpage',
            'insertdatetime media nonbreaking save table contextmenu directionality',
            'emoticons paste textcolor colorpicker textpattern  codesample'
          ],
          toolbar1: 'undo redo | bullist bold numlist link outdent indent codesample',
          autosave_interval: '20s',
          image_advtab: true,
          table_default_styles: {
            width: '100%'
            // borderCollapse: 'collapse'
          },
          setup: function (editor) {
            editor.on('init', function (e) {
              vm.spinShow = false;
              if (localStorage.tinymceContent) {
                tinymce.get(this.id).setContent(localStorage.tinymceContent);
                localStorage.removeItem('tinymceContent');
              }
            });
            editor.on('keydown', function (e) {
              localStorage.editorContent = tinymce.get(this.id).getContent();
            });
          }
        }
        if (this.restrictElements) {
        	// config.valid_elements = 'a[href],br,p,ol,ul,li'
        }
        tinymce.init(config);
      });
    },
    // 获取当前编辑器内容
    getContent () {
      let content = tinymce.get(this.id).getContent();
      let returnContent = content.replace('<!DOCTYPE html>\n<html>\n<head>\n</head>\n<body>\n', '');
      returnContent = returnContent.replace('\n</body>\n</html>', '');
      return returnContent;
      // return tinymce.get(this.id).getContent()
    },
    // 设置当前编辑器的内容
    setContent (content) {
      if (content || content === 0) return tinymce.get(this.id).setContent(content);
    },
    // 获取localstorage内容
    setLocalstorage () {
      if (localStorage.tinymceContent) {
        tinymce
          .get(this.id)
          .setContent(
            '<!DOCTYPE html>\n' +
							'<html>\n' +
							'<head>\n' +
							'</head>\n' +
							'<body>\n' +
							'<p><a href="asd">asd</a></p>\n' +
							'</body>\n' +
							'</html>'
          );
      }
    },
    // 获取纯文本内容
    getContent_text () {
      var activeEditor = tinymce.get(this.id);
      var editBody = activeEditor.getBody();
      activeEditor.selection.select(editBody);
      var text = activeEditor.selection.getContent({ format: 'text' });
      return text;
      // return tinymce.get(this.id).getContent({'format' : 'text' });
    },
    // 销毁插件
    destroySelf () {
      tinymce.get(this.id).destroy();
    }
  }
};
</script>

<style></style>
