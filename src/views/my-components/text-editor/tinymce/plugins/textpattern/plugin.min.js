!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; g('1', [], function () { var a = function (b) { var c = b, d = function () { return c; }, e = function (a) { c = a; }, f = function () { return a(d()); }; return {get: d, set: e, clone: f}; }; return a; }), h('6', tinymce.util.Tools.resolve), g('2', ['6'], function (a) { return a('tinymce.PluginManager'); }), g('3', [], function () { var a = function (a) { var b = function (b) { a.set(b); }, c = function () { return a.get(); }; return {setPatterns: b, getPatterns: c}; }; return {get: a}; }), g('4', [], function () { var a = [{start: '*', end: '*', format: 'italic'}, {start: '**', end: '**', format: 'bold'}, {start: '***', end: '***', format: ['bold', 'italic']}, {start: '#', format: 'h1'}, {start: '##', format: 'h2'}, {start: '###', format: 'h3'}, {start: '####', format: 'h4'}, {start: '#####', format: 'h5'}, {start: '######', format: 'h6'}, {start: '1. ', cmd: 'InsertOrderedList'}, {start: '* ', cmd: 'InsertUnorderedList'}, {start: '- ', cmd: 'InsertUnorderedList'}], b = function (b) { return void 0 !== b.textpattern_patterns ? b.textpattern_patterns : a; }; return {getPatterns: b}; }), g('7', ['6'], function (a) { return a('tinymce.util.Delay'); }), g('8', ['6'], function (a) { return a('tinymce.util.VK'); }), h('b', document), g('c', ['6'], function (a) { return a('tinymce.dom.TreeWalker'); }), g('d', ['6'], function (a) { return a('tinymce.util.Tools'); }), g('e', [], function () { var a = function (a) { return a.sort(function (a, b) { return a.start.length > b.start.length ? -1 : a.start.length < b.start.length ? 1 : 0; }); }, b = function (a, b) { for (var c = 0; c < a.length; c++) if (b.indexOf(a[c].start) === 0 && (!a[c].end || b.lastIndexOf(a[c].end) === b.length - a[c].end.length)) return a[c]; }, c = function (a, b, c, d) { var e = b.substr(c - a.end.length - d, a.end.length); return e === a.end; }, d = function (a, b, c) { return a - b - c.end.length - c.start.length > 0; }, e = function (b, e, f, g) { var h, i, j = a(b); for (i = 0; i < j.length; i++) if (h = j[i], void 0 !== h.end && c(h, e, f, g) && d(f, g, h)) return h; }; return {findPattern: b, findEndPattern: e}; }), g('a', ['b', 'c', 'd', 'e'], function (a, b, c, d) { var e = function (a, b, c, d, e) { return a = d > 0 ? a.splitText(d) : a, a.splitText(c - d + b.end.length), a.deleteData(0, b.start.length), a.deleteData(a.data.length - b.end.length, b.end.length), a; }, f = function (b, c, e) { if (c.collapsed !== !1) { var f = c.startContainer, g = f.data, h = e === !0 ? 1 : 0; if (f.nodeType === 3) { var i = d.findEndPattern(b, g, c.startOffset, h); if (void 0 !== i) { var j = g.lastIndexOf(i.end, c.startOffset - h), k = g.lastIndexOf(i.start, j - i.end.length); if (j = g.indexOf(i.end, k + i.start.length), k !== -1) { var l = a.createRange(); l.setStart(f, k), l.setEnd(f, j + i.end.length); var m = d.findPattern(b, l.toString()); if (!(void 0 === i || m !== i || f.data.length <= i.start.length + i.end.length)) return {pattern: i, startOffset: k, endOffset: j}; } } } } }, g = function (a, b, d, f) { var g = c.isArray(d.pattern.format) ? d.pattern.format : [d.pattern.format], h = c.grep(g, function (b) { var c = a.formatter.get(b); return c && c[0].inline; }); if (h.length !== 0) return a.undoManager.transact(function () { b = e(b, d.pattern, d.endOffset, d.startOffset, f), g.forEach(function (c) { a.formatter.apply(c, {}, b); }); }), b; }, h = function (a, b, c) { var d = a.selection.getRng(!0), e = f(b, d, c); if (e) return g(a, d.startContainer, e, c); }, i = function (a, b) { return h(a, b, !0); }, j = function (a, b) { return h(a, b, !1); }, k = function (a, e) { var f, g, h, i, j, k, l, m, n, o, p; if (f = a.selection, g = a.dom, f.isCollapsed() && (l = g.getParent(f.getStart(), 'p'))) { for (n = new b(l, l); j = n.next();) if (j.nodeType === 3) { i = j; break; } if (i) { if (m = d.findPattern(e, i.data), !m) return; if (o = f.getRng(!0), h = o.startContainer, p = o.startOffset, i === h && (p = Math.max(0, p - m.start.length)), c.trim(i.data).length === m.start.length) return; m.format && (k = a.formatter.get(m.format), k && k[0].block && (i.deleteData(0, m.start.length), a.formatter.apply(m.format, {}, i), o.setStart(h, p), o.collapse(!0), f.setRng(o))), m.cmd && a.undoManager.transact(function () { i.deleteData(0, m.start.length), a.execCommand(m.cmd); }); } } }; return {patternFromRng: f, applyInlineFormatSpace: i, applyInlineFormatEnter: j, applyBlockFormat: k}; }), g('9', ['8', 'a'], function (a, b) { function c (a, c) { var d, e; d = b.applyInlineFormatEnter(a, c), d && (e = a.dom.createRng(), e.setStart(d, d.data.length), e.setEnd(d, d.data.length), a.selection.setRng(e)), b.applyBlockFormat(a, c); } function d (a, c) { var d, e, f, g, h; d = b.applyInlineFormatSpace(a, c), d && (h = a.dom, e = d.data.slice(-1), /[\u00a0 ]/.test(e) && (d.deleteData(d.data.length - 1, 1), f = h.doc.createTextNode(e), h.insertAfter(f, d.parentNode), g = h.createRng(), g.setStart(f, 1), g.setEnd(f, 1), a.selection.setRng(g))); } var e = function (a, b, c) { for (var d = 0; d < a.length; d++) if (c(a[d], b)) return !0; }, f = function (b, c) { return e(b, c, function (b, c) { return b === c.keyCode && a.modifierPressed(c) === !1; }); }, g = function (a, b) { return e(a, b, function (a, b) { return a.charCodeAt(0) === b.charCode; }); }; return {handleEnter: c, handleInlineKey: d, checkCharCode: g, checkKeyCode: f}; }), g('5', ['7', '8', '9'], function (a, b, c) { var d = function (d, e) { var f = [',', '.', ';', ':', '!', '?'], g = [32]; d.on('keydown', function (a) { a.keyCode !== 13 || b.modifierPressed(a) || c.handleEnter(d, e.get()); }, !0), d.on('keyup', function (a) { c.checkKeyCode(g, a) && c.handleInlineKey(d, e.get()); }), d.on('keypress', function (b) { c.checkCharCode(f, b) && a.setEditorTimeout(d, function () { c.handleInlineKey(d, e.get()); }); }); }; return {setup: d}; }), g('0', ['1', '2', '3', '4', '5'], function (a, b, c, d, e) { return b.add('textpattern', function (b) { var f = a(d.getPatterns(b.settings)); return e.setup(b, f), c.get(f); }), function () {}; }), d('0')(); }());
