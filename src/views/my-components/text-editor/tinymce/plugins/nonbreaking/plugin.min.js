!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('5', tinymce.util.Tools.resolve), g('1', ['5'], function (a) { return a('tinymce.PluginManager'); }), g('6', [], function () { var a = function (a, b) { for (var c = '', d = 0; d < b; d++)c += a; return c; }, b = function (a) { return !!a.plugins.visualchars && a.plugins.visualchars.isEnabled(); }, c = function (c, d) { var e = b(c) ? '<span class="mce-nbsp">&nbsp;</span>' : '&nbsp;'; c.insertContent(a(e, d)), c.dom.setAttrib(c.dom.select('span.mce-nbsp'), 'data-mce-bogus', '1'); }; return {insertNbsp: c}; }), g('2', ['6'], function (a) { var b = function (b) { b.addCommand('mceNonBreaking', function () { a.insertNbsp(b, 1); }); }; return {register: b}; }), g('7', [], function () { var a = function (a) { var b = a.getParam('nonbreaking_force_tab', 0); return typeof tabs === 'boolean' ? b === !0 ? 3 : 0 : b; }; return {getKeyboardSpaces: a}; }), g('3', ['7', '6'], function (a, b) { var c = function (c) { var d = a.getKeyboardSpaces(c); d > 0 && c.on('keydown', function (a) { if (a.keyCode === 9) { if (a.shiftKey) return; a.preventDefault(), b.insertNbsp(c, d); } }); }; return {setup: c}; }), g('4', [], function () { var a = function (a) { a.addButton('nonbreaking', {title: 'Nonbreaking space', cmd: 'mceNonBreaking'}), a.addMenuItem('nonbreaking', {text: 'Nonbreaking space', cmd: 'mceNonBreaking', context: 'insert'}); }; return {register: a}; }), g('0', ['1', '2', '3', '4'], function (a, b, c, d) { return a.add('nonbreaking', function (a) { b.register(a), d.register(a), c.setup(a); }), function () {}; }), d('0')(); }());
