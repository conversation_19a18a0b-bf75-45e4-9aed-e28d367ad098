!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('4', tinymce.util.Tools.resolve), g('1', ['4'], function (a) { return a('tinymce.PluginManager'); }), g('2', [], function () { var a = function (a) { a.addCommand('InsertHorizontalRule', function () { a.execCommand('mceInsertContent', !1, '<hr />'); }); }; return {register: a}; }), g('3', [], function () { var a = function (a) { a.addButton('hr', {icon: 'hr', tooltip: 'Horizontal line', cmd: 'InsertHorizontalRule'}), a.addMenuItem('hr', {icon: 'hr', text: 'Horizontal line', cmd: 'InsertHorizontalRule', context: 'insert'}); }; return {register: a}; }), g('0', ['1', '2', '3'], function (a, b, c) { return a.add('hr', function (a) { b.register(a), c.register(a); }), function () {}; }), d('0')(); }());
