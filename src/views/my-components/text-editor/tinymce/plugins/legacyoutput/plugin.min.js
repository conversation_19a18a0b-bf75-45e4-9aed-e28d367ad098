!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('4', tinymce.util.Tools.resolve), g('1', ['4'], function (a) { return a('tinymce.PluginManager'); }), g('5', ['4'], function (a) { return a('tinymce.util.Tools'); }), g('2', ['5'], function (a) { var b = function (b) { var c = 'p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table,img', d = a.explode(b.settings.font_size_style_values), e = b.schema; b.formatter.register({alignleft: {selector: c, attributes: {align: 'left'}}, aligncenter: {selector: c, attributes: {align: 'center'}}, alignright: {selector: c, attributes: {align: 'right'}}, alignjustify: {selector: c, attributes: {align: 'justify'}}, bold: [{inline: 'b', remove: 'all'}, {inline: 'strong', remove: 'all'}, {inline: 'span', styles: {fontWeight: 'bold'}}], italic: [{inline: 'i', remove: 'all'}, {inline: 'em', remove: 'all'}, {inline: 'span', styles: {fontStyle: 'italic'}}], underline: [{inline: 'u', remove: 'all'}, {inline: 'span', styles: {textDecoration: 'underline'}, exact: !0}], strikethrough: [{inline: 'strike', remove: 'all'}, {inline: 'span', styles: {textDecoration: 'line-through'}, exact: !0}], fontname: {inline: 'font', attributes: {face: '%value'}}, fontsize: {inline: 'font', attributes: {size: function (b) { return a.inArray(d, b.value) + 1; }}}, forecolor: {inline: 'font', attributes: {color: '%value'}}, hilitecolor: {inline: 'font', styles: {backgroundColor: '%value'}}}), a.each('b,i,u,strike'.split(','), function (a) { e.addValidElements(a + '[*]'); }), e.getElementRule('font') || e.addValidElements('font[face|size|color|style]'), a.each(c.split(','), function (a) { var b = e.getElementRule(a); b && (b.attributes.align || (b.attributes.align = {}, b.attributesOrder.push('align'))); }); }, c = function (a) { a.settings.inline_styles = !1, a.on('init', function () { b(a); }); }; return {setup: c}; }), g('3', [], function () { var a = function (a) { a.addButton('fontsizeselect', function () { var b = [], c = '8pt=1 10pt=2 12pt=3 14pt=4 18pt=5 24pt=6 36pt=7', d = a.settings.fontsizeFormats || c; return a.$.each(d.split(' '), function (a, c) { var d = c, e = c, f = c.split('='); f.length > 1 && (d = f[0], e = f[1]), b.push({text: d, value: e}); }), {type: 'listbox', text: 'Font Sizes', tooltip: 'Font Sizes', values: b, fixedWidth: !0, onPostRender: function () { var b = this; a.on('NodeChange', function () { var c; c = a.dom.getParent(a.selection.getNode(), 'font'), c ? b.value(c.size) : b.value(''); }); }, onclick: function (b) { b.control.settings.value && a.execCommand('FontSize', !1, b.control.settings.value); }}; }), a.addButton('fontselect', function () { function b (a) { a = a.replace(/;$/, '').split(';'); for (var b = a.length; b--;)a[b] = a[b].split('='); return a; } var c = 'Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats', d = [], e = b(a.settings.font_formats || c); return a.$.each(e, function (a, b) { d.push({text: {raw: b[0]}, value: b[1], textStyle: b[1].indexOf('dings') === -1 ? 'font-family:' + b[1] : ''}); }), {type: 'listbox', text: 'Font Family', tooltip: 'Font Family', values: d, fixedWidth: !0, onPostRender: function () { var b = this; a.on('NodeChange', function () { var c; c = a.dom.getParent(a.selection.getNode(), 'font'), c ? b.value(c.face) : b.value(''); }); }, onselect: function (b) { b.control.settings.value && a.execCommand('FontName', !1, b.control.settings.value); }}; }); }; return {register: a}; }), g('0', ['1', '2', '3'], function (a, b, c) { return a.add('legacyoutput', function (a) { b.setup(a), c.register(a); }), function () {}; }), d('0')(); }());
