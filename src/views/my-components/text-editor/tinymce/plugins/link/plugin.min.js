!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('6', tinymce.util.Tools.resolve), g('1', ['6'], function (a) { return a('tinymce.PluginManager'); }), g('7', ['6'], function (a) { return a('tinymce.util.VK'); }), g('8', [], function () { var a = function (a) { return typeof a.link_assume_external_targets === 'boolean' && a.link_assume_external_targets; }, b = function (a) { return typeof a.link_context_toolbar === 'boolean' && a.link_context_toolbar; }, c = function (a) { return a.link_list; }, d = function (a) { return typeof a.default_link_target === 'string'; }, e = function (a) { return a.default_link_target; }, f = function (a) { return a.target_list; }, g = function (a, b) { a.settings.target_list = b; }, h = function (a) { return f(a) !== !1; }, i = function (a) { return a.rel_list; }, j = function (a) { return void 0 !== i(a); }, k = function (a) { return a.link_class_list; }, l = function (a) { return void 0 !== k(a); }, m = function (a) { return a.link_title !== !1; }, n = function (a) { return typeof a.allow_unsafe_link_target === 'boolean' && a.allow_unsafe_link_target; }; return {assumeExternalTargets: a, hasContextToolbar: b, getLinkList: c, hasDefaultLinkTarget: d, getDefaultLinkTarget: e, getTargetList: f, setTargetList: g, shouldShowTargetList: h, getRelList: i, hasRelList: j, getLinkClassList: k, hasLinkClassList: l, shouldShowLinkTitle: m, allowUnsafeLinkTarget: n}; }), h('c', document), h('d', window), g('e', ['6'], function (a) { return a('tinymce.dom.DOMUtils'); }), g('f', ['6'], function (a) { return a('tinymce.Env'); }), g('9', ['c', 'd', 'e', 'f'], function (a, b, c, d) { var e = function (b, c) { a.body.appendChild(b), b.dispatchEvent(c), a.body.removeChild(b); }, f = function (f) { if (!d.ie || d.ie > 10) { var g = a.createElement('a'); g.target = '_blank', g.href = f, g.rel = 'noreferrer noopener'; var h = a.createEvent('MouseEvents'); h.initMouseEvent('click', !0, !0, b, 0, 0, 0, 0, 0, !1, !1, !1, !1, 0, null), e(g, h); } else { var i = b.open('', '_blank'); if (i) { i.opener = null; var j = i.document; j.open(), j.write('<meta http-equiv="refresh" content="0; url=' + c.DOM.encode(f) + '">'), j.close(); } } }; return {open: f}; }), h('g', RegExp), g('h', ['6'], function (a) { return a('tinymce.util.Tools'); }), g('a', ['g', 'h', '8'], function (a, b, c) { var d = function (a, c) { var d = ['noopener'], e = a ? a.split(/\s+/) : [], f = function (a) { return b.trim(a.sort().join(' ')); }, g = function (a) { return a = h(a), a.length ? a.concat(d) : d; }, h = function (a) { return a.filter(function (a) { return b.inArray(d, a) === -1; }); }; return e = c ? g(e) : h(e), e.length ? f(e) : null; }, e = function (a) { return a.replace(/\uFEFF/g, ''); }, f = function (a, b) { return b = b || a.selection.getStart(), k(b) ? a.dom.select('a[href]', b)[0] : a.dom.getParent(b, 'a[href]'); }, g = function (a, b) { var c = b ? b.innerText || b.textContent : a.getContent({format: 'text'}); return e(c); }, h = function (a) { return a && a.nodeName === 'A' && a.href; }, i = function (a) { return b.grep(a, h).length > 0; }, j = function (a) { return !(/</.test(a) && (!/^<a [^>]+>[^<]+<\/a>$/.test(a) || a.indexOf('href=') === -1)); }, k = function (a) { return a && a.nodeName === 'FIGURE' && /\bimage\b/i.test(a.className); }, l = function (a, b) { return function (e) { a.undoManager.transact(function () { var g = a.selection.getNode(), h = f(a, g), i = {href: e.href, target: e.target ? e.target : null, rel: e.rel ? e.rel : null, 'class': e['class'] ? e['class'] : null, title: e.title ? e.title : null}; c.hasRelList(a.settings) || c.allowUnsafeLinkTarget(a.settings) !== !1 || (i.rel = d(i.rel, i.target === '_blank')), e.href === b.href && (b.attach(), b = {}), h ? (a.focus(), e.hasOwnProperty('text') && ('innerText' in h ? h.innerText = e.text : h.textContent = e.text), a.dom.setAttribs(h, i), a.selection.select(h), a.undoManager.add()) : k(g) ? o(a, g, i) : e.hasOwnProperty('text') ? a.insertContent(a.dom.createHTML('a', i, a.dom.encode(e.text))) : a.execCommand('mceInsertLink', !1, i); }); }; }, m = function (a) { return function () { a.undoManager.transact(function () { var b = a.selection.getNode(); k(b) ? n(a, b) : a.execCommand('unlink'); }); }; }, n = function (a, b) { var c, d; d = a.dom.select('img', b)[0], d && (c = a.dom.getParents(d, 'a[href]', b)[0], c && (c.parentNode.insertBefore(d, c), a.dom.remove(c))); }, o = function (a, b, c) { var d, e; e = a.dom.select('img', b)[0], e && (d = a.dom.create('a', c), e.parentNode.insertBefore(d, e), d.appendChild(e)); }; return {link: l, unlink: m, isLink: h, hasLinks: i, isOnlyTextSelected: j, getAnchorElement: f, getAnchorText: g, toggleTargetRules: d}; }), g('i', ['6'], function (a) { return a('tinymce.util.Delay'); }), g('j', ['6'], function (a) { return a('tinymce.util.XHR'); }), g('b', ['i', 'h', 'j', '8', 'a'], function (a, b, c, d, e) { var f = {}, g = function (a, b) { var e = d.getLinkList(a.settings); typeof e === 'string' ? c.send({url: e, success: function (c) { b(a, JSON.parse(c)); }}) : typeof e === 'function' ? e(function (c) { b(a, c); }) : b(a, e); }, h = function (a, c, d) { var e = function (a, d) { return d = d || [], b.each(a, function (a) { var b = {text: a.text || a.title}; a.menu ? b.menu = e(a.menu) : (b.value = a.value, c && c(b)), d.push(b); }), d; }; return e(a, d || []); }, i = function (b, c, d) { var e = b.selection.getRng(); a.setEditorTimeout(b, function () { b.windowManager.confirm(c, function (a) { b.selection.setRng(e), d(a); }); }); }, j = function (a, c) { var g, j, k, l, m, n, o, p, q, r, s, t = {}, u = a.selection, v = a.dom, w = function (a) { var b = k.find('#text'); (!b.value() || a.lastControl && b.value() === a.lastControl.text()) && b.value(a.control.text()), k.find('#href').value(a.control.value()); }, x = function (c) { var d = []; if (b.each(a.dom.select('a:not([href])'), function (a) { var b = a.name || a.id; b && d.push({text: b, value: '#' + b, selected: c.indexOf('#' + b) !== -1}); }), d.length) return d.unshift({text: 'None', value: ''}), {name: 'anchor', type: 'listbox', label: 'Anchors', values: d, onselect: w}; }, y = function () { j || !l || t.text || this.parent().parent().find('#text')[0].value(this.value()); }, z = function (c) { var d = c.meta || {}; n && n.value(a.convertURL(this.value(), 'href')), b.each(c.meta, function (a, b) { var c = k.find('#' + b); b === 'text' ? j.length === 0 && (c.value(a), t.text = a) : c.value(a); }), d.attach && (f = {href: this.value(), attach: d.attach}), d.text || y.call(this); }, A = function (a) { a.meta = k.toJSON(); }; l = e.isOnlyTextSelected(u.getContent()), g = e.getAnchorElement(a), t.text = j = e.getAnchorText(a.selection, g), t.href = g ? v.getAttrib(g, 'href') : '', g ? t.target = v.getAttrib(g, 'target') : d.hasDefaultLinkTarget(a.settings) && (t.target = d.getDefaultLinkTarget(a.settings)), (s = v.getAttrib(g, 'rel')) && (t.rel = s), (s = v.getAttrib(g, 'class')) && (t['class'] = s), (s = v.getAttrib(g, 'title')) && (t.title = s), l && (m = {name: 'text', type: 'textbox', size: 40, label: 'Text to display', onchange: function () { t.text = this.value(); }}), c && (n = {type: 'listbox', label: 'Link list', values: h(c, function (b) { b.value = a.convertURL(b.value || b.url, 'href'); }, [{text: 'None', value: ''}]), onselect: w, value: a.convertURL(t.href, 'href'), onPostRender: function () { n = this; }}), d.shouldShowTargetList(a.settings) && (void 0 === d.getTargetList(a.settings) && d.setTargetList(a, [{text: 'None', value: ''}, {text: 'New window', value: '_blank'}]), p = {name: 'target', type: 'listbox', label: 'Target', values: h(d.getTargetList(a.settings))}), d.hasRelList(a.settings) && (o = {name: 'rel', type: 'listbox', label: 'Rel', values: h(d.getRelList(a.settings), function (b) { d.allowUnsafeLinkTarget(a.settings) === !1 && (b.value = e.toggleTargetRules(b.value, t.target === '_blank')); })}), d.hasLinkClassList(a.settings) && (q = {name: 'class', type: 'listbox', label: 'Class', values: h(d.getLinkClassList(a.settings), function (b) { b.value && (b.textStyle = function () { return a.formatter.getCssText({inline: 'a', classes: [b.value]}); }); })}), d.shouldShowLinkTitle(a.settings) && (r = {name: 'title', type: 'textbox', label: 'Title', value: t.title}), k = a.windowManager.open({title: 'Insert link', data: t, body: [{name: 'href', type: 'filepicker', filetype: 'file', size: 40, autofocus: !0, label: 'Url', onchange: z, onkeyup: y, onbeforecall: A}, m, r, x(t.href), n, o, p, q], onSubmit: function (c) { var g = d.assumeExternalTargets(a.settings), h = e.link(a, f), k = e.unlink(a), m = b.extend({}, t, c.data), n = m.href; return n ? (l && m.text !== j || delete m.text, n.indexOf('@') > 0 && n.indexOf('//') === -1 && n.indexOf('mailto:') === -1 ? void i(a, 'The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?', function (a) { a && (m.href = 'mailto:' + n), h(m); }) : g === !0 && !/^\w+:/i.test(n) || g === !1 && /^\s*www[\.|\d\.]/i.test(n) ? void i(a, 'The URL you entered seems to be an external link. Do you want to add the required http:// prefix?', function (a) { a && (m.href = 'http://' + n), h(m); }) : void h(m)) : void k(); }}); }, k = function (a) { g(a, j); }; return {open: k}; }), g('3', ['7', '8', '9', 'a', 'b'], function (a, b, c, d, e) { var f = function (a, b) { return a.dom.getParent(b, 'a[href]'); }, g = function (a) { return f(a, a.selection.getStart()); }, h = function (a) { var b = a.getAttribute('data-mce-href'); return b || a.getAttribute('href'); }, i = function (a) { var b = a.plugins.contextmenu; return !!b && b.isContextMenuVisible(); }, j = function (a) { return a.altKey === !0 && a.shiftKey === !1 && a.ctrlKey === !1 && a.metaKey === !1; }, k = function (a, b) { if (b) { var d = h(b); if (/^#/.test(d)) { var e = a.$(d); e.length && a.selection.scrollIntoView(e[0], !0); } else c.open(b.href); } }, l = function (a) { return function () { e.open(a); }; }, m = function (a) { return function () { k(a, g(a)); }; }, n = function (a) { return function (c) { var e, f, g; return !!(b.hasContextToolbar(a.settings) && !i(a) && d.isLink(c) && (e = a.selection, f = e.getRng(), g = f.startContainer, g.nodeType === 3 && e.isCollapsed() && f.startOffset > 0 && f.startOffset < g.data.length)); }; }, o = function (b) { b.on('click', function (c) { var d = f(b, c.target); d && a.metaKeyPressed(c) && (c.preventDefault(), k(b, d)); }), b.on('keydown', function (a) { var c = g(b); c && a.keyCode === 13 && j(a) && (a.preventDefault(), k(b, c)); }); }, p = function (a) { return function () { var b = this; a.on('nodechange', function (c) { b.active(!a.readonly && !!d.getAnchorElement(a, c.element)); }); }; }, q = function (a) { return function () { var b = this, c = function (a) { d.hasLinks(a.parents) ? b.show() : b.hide(); }; d.hasLinks(a.dom.getParents(a.selection.getStart())) || b.hide(), a.on('nodechange', c), b.on('remove', function () { a.off('nodechange', c); }); }; }; return {openDialog: l, gotoSelectedLink: m, leftClickedOnAHref: n, setupGotoLinks: o, toggleActiveState: p, toggleViewLinkState: q}; }), g('2', ['3'], function (a) { var b = function (b) { b.addCommand('mceLink', a.openDialog(b)); }; return {register: b}; }), g('4', ['3'], function (a) { var b = function (b) { b.addShortcut('Meta+K', '', a.openDialog(b)); }; return {setup: b}; }), g('5', ['3', 'a'], function (a, b) { var c = function (c) { c.addButton('link', {icon: 'link', tooltip: 'Insert/edit link', shortcut: 'Meta+K', onclick: a.openDialog(c), onpostrender: a.toggleActiveState(c)}), c.addButton('unlink', {icon: 'unlink', tooltip: 'Remove link', onclick: b.unlink(c), onpostrender: a.toggleActiveState(c)}), c.addContextToolbar && c.addButton('openlink', {icon: 'newtab', tooltip: 'Open link', onclick: a.gotoSelectedLink(c)}); }, d = function (b) { b.addMenuItem('openlink', {text: 'Open link', icon: 'newtab', onclick: a.gotoSelectedLink(b), onPostRender: a.toggleViewLinkState(b), prependToContext: !0}), b.addMenuItem('link', {icon: 'link', text: 'Link', shortcut: 'Meta+K', onclick: a.openDialog(b), stateSelector: 'a[href]', context: 'insert', prependToContext: !0}); }, e = function (b) { b.addContextToolbar && b.addContextToolbar(a.leftClickedOnAHref(b), 'openlink | link unlink'); }; return {setupButtons: c, setupMenuItems: d, setupContextToolbars: e}; }), g('0', ['1', '2', '3', '4', '5'], function (a, b, c, d, e) { return a.add('link', function (a) { e.setupButtons(a), e.setupMenuItems(a), e.setupContextToolbars(a), c.setupGotoLinks(a), b.register(a), d.setup(a); }), function () {}; }), d('0')(); }());
