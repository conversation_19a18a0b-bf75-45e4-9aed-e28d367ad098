!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('6', tinymce.util.Tools.resolve), g('1', ['6'], function (a) { return a('tinymce.PluginManager'); }), g('g', ['6'], function (a) { return a('tinymce.dom.RangeUtils'); }), g('h', ['6'], function (a) { return a('tinymce.dom.TreeWalker'); }), g('b', ['6'], function (a) { return a('tinymce.util.VK'); }), g('i', ['6'], function (a) { return a('tinymce.dom.BookmarkManager'); }), g('d', ['6'], function (a) { return a('tinymce.util.Tools'); }), g('j', ['6'], function (a) { return a('tinymce.dom.DOMUtils'); }), g('e', [], function () { var a = function (a) { return a && a.nodeType === 3; }, b = function (a) { return a && /^(OL|UL|DL)$/.test(a.nodeName); }, c = function (a) { return a && /^(LI|DT|DD)$/.test(a.nodeName); }, d = function (a) { return a && a.nodeName === 'BR'; }, e = function (a) { return a.parentNode.firstChild === a; }, f = function (a) { return a.parentNode.lastChild === a; }, g = function (a, b) { return b && !!a.schema.getTextBlockElements()[b.nodeName]; }, h = function (a, b) { return a && a.nodeName in b; }, i = function (a, b) { return !!d(b) && !(!a.isBlock(b.nextSibling) || d(b.previousSibling)); }, j = function (a, b, c) { var d = a.isEmpty(b); return !(c && a.select('span[data-mce-type=bookmark]', b).length > 0) && d; }, k = function (a, b) { return a.isChildOf(b, a.getRoot()); }; return {isTextNode: a, isListNode: b, isListItemNode: c, isBr: d, isFirstChild: e, isLastChild: f, isTextBlock: g, isBlock: h, isBogusBr: i, isEmpty: j, isChildOfBody: k}; }), g('p', ['g', 'e'], function (a, b) { var c = function (c, d) { var e = a.getNode(c, d); if (b.isListItemNode(c) && b.isTextNode(e)) { var f = d >= c.childNodes.length ? e.data.length : 0; return {container: e, offset: f}; } return {container: c, offset: d}; }, d = function (a) { var b = a.cloneRange(), d = c(a.startContainer, a.startOffset); b.setStart(d.container, d.offset); var e = c(a.endContainer, a.endOffset); return b.setEnd(e.container, e.offset), b; }; return {getNormalizedEndPoint: c, normalizeRange: d}; }), g('k', ['j', 'e', 'p'], function (a, b, c) { var d = a.DOM, e = function (a) { var b = {}, c = function (c) { var e, f, g; f = a[c ? 'startContainer' : 'endContainer'], g = a[c ? 'startOffset' : 'endOffset'], f.nodeType === 1 && (e = d.create('span', {'data-mce-type': 'bookmark'}), f.hasChildNodes() ? (g = Math.min(g, f.childNodes.length - 1), c ? f.insertBefore(e, f.childNodes[g]) : d.insertAfter(e, f.childNodes[g])) : f.appendChild(e), f = e, g = 0), b[c ? 'startContainer' : 'endContainer'] = f, b[c ? 'startOffset' : 'endOffset'] = g; }; return c(!0), a.collapsed || c(), b; }, f = function (a) { function b (b) { var c, e, f, g = function (a) { for (var b = a.parentNode.firstChild, c = 0; b;) { if (b === a) return c; b.nodeType === 1 && b.getAttribute('data-mce-type') === 'bookmark' || c++, b = b.nextSibling; } return -1; }; c = f = a[b ? 'startContainer' : 'endContainer'], e = a[b ? 'startOffset' : 'endOffset'], c && (c.nodeType === 1 && (e = g(c), c = c.parentNode, d.remove(f)), a[b ? 'startContainer' : 'endContainer'] = c, a[b ? 'startOffset' : 'endOffset'] = e); }b(!0), b(); var e = d.createRng(); return e.setStart(a.startContainer, a.startOffset), a.endContainer && e.setEnd(a.endContainer, a.endOffset), c.normalizeRange(e); }; return {createBookmark: e, resolveBookmark: f}; }), g('l', ['j', 'd', 'e'], function (a, b, c) { var d = a.DOM, e = function (a, b) { var e, f = b.parentNode; f.nodeName === 'LI' && f.firstChild === b && (e = f.previousSibling, e && e.nodeName === 'LI' ? (e.appendChild(b), c.isEmpty(a, f) && d.remove(f)) : d.setStyle(f, 'listStyleType', 'none')), c.isListNode(f) && (e = f.previousSibling, e && e.nodeName === 'LI' && e.appendChild(b)); }, f = function (a, c) { b.each(b.grep(a.select('ol,ul', c)), function (b) { e(a, b); }); }; return {normalizeList: e, normalizeLists: f}; }), g('m', ['6'], function (a) { return a('tinymce.dom.DomQuery'); }), g('f', ['m', 'd', 'e'], function (a, b, c) { var d = function (a) { return a.dom.getParent(a.selection.getStart(!0), 'OL,UL,DL'); }, e = function (a) { var e = d(a); return b.grep(a.selection.getSelectedBlocks(), function (a) { return c.isListNode(a) && e !== a; }); }, f = function (c, d) { var e = b.map(d, function (a) { var b = c.dom.getParent(a, 'li,dd,dt', c.getBody()); return b || a; }); return a.unique(e); }, g = function (a) { var d = a.selection.getSelectedBlocks(); return b.grep(f(a, d), function (a) { return c.isListItemNode(a); }); }; return {getParentList: d, getSelectedSubLists: e, getSelectedListItems: g}; }), g('q', ['6'], function (a) { return a('tinymce.Env'); }), g('o', ['j', 'q', 'e'], function (a, b, c) { var d = a.DOM, e = function (a, e, f) { var g, h, i, j = d.createFragment(), k = a.schema.getBlockElements(); if (a.settings.forced_root_block && (f = f || a.settings.forced_root_block), f && (h = d.create(f), h.tagName === a.settings.forced_root_block && d.setAttribs(h, a.settings.forced_root_block_attrs), c.isBlock(e.firstChild, k) || j.appendChild(h)), e) for (;g = e.firstChild;) { var l = g.nodeName; i || l === 'SPAN' && g.getAttribute('data-mce-type') === 'bookmark' || (i = !0), c.isBlock(g, k) ? (j.appendChild(g), h = null) : f ? (h || (h = d.create(f), j.appendChild(h)), h.appendChild(g)) : j.appendChild(g); } return a.settings.forced_root_block ? i || b.ie && !(b.ie > 10) || h.appendChild(d.create('br', {'data-mce-bogus': '1'})) : j.appendChild(d.create('br')), j; }; return {createNewTextBlock: e}; }), g('n', ['j', 'e', 'o', 'd'], function (a, b, c, d) { var e = a.DOM, f = function (a, f, g, h) { var i, j, k, l, m = function (a) { d.each(k, function (b) { a.parentNode.insertBefore(b, g.parentNode); }), e.remove(a); }; for (k = e.select('span[data-mce-type="bookmark"]', f), h = h || c.createNewTextBlock(a, g), i = e.createRng(), i.setStartAfter(g), i.setEndAfter(f), j = i.extractContents(), l = j.firstChild; l; l = l.firstChild) if (l.nodeName === 'LI' && a.dom.isEmpty(l)) { e.remove(l); break; }a.dom.isEmpty(j) || e.insertAfter(j, f), e.insertAfter(h, f), b.isEmpty(a.dom, g.parentNode) && m(g.parentNode), e.remove(g), b.isEmpty(a.dom, f) && e.remove(f); }; return {splitList: f}; }), g('9', ['j', 'k', 'e', 'l', 'f', 'n', 'o'], function (a, b, c, d, e, f, g) { var h = a.DOM, i = function (a, b) { c.isEmpty(a, b) && h.remove(b); }, j = function (a, b) { var e, j = b.parentNode, k = j.parentNode; return j === a.getBody() || (b.nodeName === 'DD' ? (h.rename(b, 'DT'), !0) : c.isFirstChild(b) && c.isLastChild(b) ? (k.nodeName === 'LI' ? (h.insertAfter(b, k), i(a.dom, k), h.remove(j)) : c.isListNode(k) ? h.remove(j, !0) : (k.insertBefore(g.createNewTextBlock(a, b), j), h.remove(j)), !0) : c.isFirstChild(b) ? (k.nodeName === 'LI' ? (h.insertAfter(b, k), b.appendChild(j), i(a.dom, k)) : c.isListNode(k) ? k.insertBefore(b, j) : (k.insertBefore(g.createNewTextBlock(a, b), j), h.remove(b)), !0) : c.isLastChild(b) ? (k.nodeName === 'LI' ? h.insertAfter(b, k) : c.isListNode(k) ? h.insertAfter(b, j) : (h.insertAfter(g.createNewTextBlock(a, b), j), h.remove(b)), !0) : (k.nodeName === 'LI' ? (j = k, e = g.createNewTextBlock(a, b, 'LI')) : e = c.isListNode(k) ? g.createNewTextBlock(a, b, 'LI') : g.createNewTextBlock(a, b), f.splitList(a, j, b, e), d.normalizeLists(a.dom, j.parentNode), !0)); }, k = function (a) { var c = e.getSelectedListItems(a); if (c.length) { var d, f, g = b.createBookmark(a.selection.getRng(!0)), h = a.getBody(); for (d = c.length; d--;) for (var i = c[d].parentNode; i && i !== h;) { for (f = c.length; f--;) if (c[f] === i) { c.splice(d, 1); break; }i = i.parentNode; } for (d = 0; d < c.length && (j(a, c[d]) || d !== 0); d++);return a.selection.setRng(b.resolveBookmark(g)), a.nodeChanged(), !0; } }; return {outdent: j, outdentSelection: k}; }), g('a', ['i', 'd', '9', 'k', 'e', 'l', 'f', 'n'], function (a, b, c, d, e, f, g, h) { var i = function (a, b, c) { var d = c['list-style-type'] ? c['list-style-type'] : null; a.setStyle(b, 'list-style-type', d); }, j = function (a, c) { b.each(c, function (b, c) { a.setAttribute(c, b); }); }, k = function (a, c, d) { j(c, d['list-attributes']), b.each(a.select('li', c), function (a) { j(a, d['list-item-attributes']); }); }, l = function (a, b, c) { i(a, b, c), k(a, b, c); }, m = function (a, b, c) { var d, f, g = a.getBody(); for (d = b[c ? 'startContainer' : 'endContainer'], f = b[c ? 'startOffset' : 'endOffset'], d.nodeType === 1 && (d = d.childNodes[Math.min(f, d.childNodes.length - 1)] || d); d.parentNode !== g;) { if (e.isTextBlock(a, d)) return d; if (/^(TD|TH)$/.test(d.parentNode.nodeName)) return d; d = d.parentNode; } return d; }, n = function (c, d) { for (var f, g = [], h = c.getBody(), i = c.dom, j = m(c, d, !0), k = m(c, d, !1), l = [], n = j; n && (l.push(n), n !== k); n = n.nextSibling);return b.each(l, function (b) { if (e.isTextBlock(c, b)) return g.push(b), void (f = null); if (i.isBlock(b) || e.isBr(b)) return e.isBr(b) && i.remove(b), void (f = null); var d = b.nextSibling; return a.isBookmarkNode(b) && (e.isTextBlock(c, d) || !d && b.parentNode === h) ? void (f = null) : (f || (f = i.create('p'), b.parentNode.insertBefore(f, b), g.push(f)), void f.appendChild(b)); }), g; }, o = function (a, c, f) { var g, h = a.selection.getRng(!0), i = 'LI', j = a.dom; f = f || {}, j.getContentEditable(a.selection.getNode()) !== 'false' && (c = c.toUpperCase(), c === 'DL' && (i = 'DT'), g = d.createBookmark(h), b.each(n(a, h), function (b) { var d, g, h = function (a) { var b = j.getStyle(a, 'list-style-type'), c = f ? f['list-style-type'] : ''; return c = c === null ? '' : c, b === c; }; g = b.previousSibling, g && e.isListNode(g) && g.nodeName === c && h(g) ? (d = g, b = j.rename(b, i), g.appendChild(b)) : (d = j.create(c), b.parentNode.insertBefore(d, b), d.appendChild(b), b = j.rename(b, i)), l(j, d, f), u(a.dom, d); }), a.selection.setRng(d.resolveBookmark(g))); }, p = function (a) { var i = d.createBookmark(a.selection.getRng(!0)), j = a.getBody(), k = g.getSelectedListItems(a), l = b.grep(k, function (b) { return a.dom.isEmpty(b); }); k = b.grep(k, function (b) { return !a.dom.isEmpty(b); }), b.each(l, function (b) { if (e.isEmpty(a.dom, b)) return void c.outdent(a, b); }), b.each(k, function (b) { var c, d; if (b.parentNode !== a.getBody()) { for (c = b; c && c !== j; c = c.parentNode)e.isListNode(c) && (d = c); h.splitList(a, d, b), f.normalizeLists(a.dom, d.parentNode); } }), a.selection.setRng(d.resolveBookmark(i)); }, q = function (a, b) { return a && b && e.isListNode(a) && a.nodeName === b.nodeName; }, r = function (a, b, c) { var d = a.getStyle(b, 'list-style-type', !0), e = a.getStyle(c, 'list-style-type', !0); return d === e; }, s = function (a, b) { return a.className === b.className; }, t = function (a, b, c) { return q(b, c) && r(a, b, c) && s(b, c); }, u = function (a, b) { var c, d; if (c = b.nextSibling, t(a, b, c)) { for (;d = c.firstChild;)b.appendChild(d); a.remove(c); } if (c = b.previousSibling, t(a, b, c)) { for (;d = c.lastChild;)b.insertBefore(d, b.firstChild); a.remove(c); } }, v = function (a, b, c, d) { if (b.nodeName !== c) { var e = a.rename(b, c); l(a, e, d); } else l(a, b, d); }, w = function (a, c, e, f, g) { if (c.nodeName !== f || x(g)) { var h = d.createBookmark(a.selection.getRng(!0)); b.each([c].concat(e), function (b) { v(a.dom, b, f, g); }), a.selection.setRng(d.resolveBookmark(h)); } else p(a, f); }, x = function (a) { return 'list-style-type' in a; }, y = function (a, b, c, e) { if (b !== a.getBody()) if (b) if (b.nodeName !== c || x(e)) { var f = d.createBookmark(a.selection.getRng(!0)); l(a.dom, b, e), u(a.dom, a.dom.rename(b, c)), a.selection.setRng(d.resolveBookmark(f)); } else p(a, c); else o(a, c, e); }, z = function (a, b, c) { var d = g.getParentList(a), e = g.getSelectedSubLists(a); c = c || {}, d && e.length > 0 ? w(a, d, e, b, c) : y(a, d, b, c); }; return {toggleList: z, removeList: p, mergeWithAdjacentLists: u}; }), g('7', ['g', 'h', 'b', 'a', 'k', 'e', 'l', 'p', 'f'], function (a, b, c, d, e, f, g, h, i) { var j = function (c, d, e) { var g, h, i = d.startContainer, j = d.startOffset; if (i.nodeType === 3 && (e ? j < i.data.length : j > 0)) return i; for (g = c.schema.getNonEmptyElements(), i.nodeType === 1 && (i = a.getNode(i, j)), h = new b(i, c.getBody()), e && f.isBogusBr(c.dom, i) && h.next(); i = h[e ? 'next' : 'prev2']();) { if (i.nodeName === 'LI' && !i.hasChildNodes()) return i; if (g[i.nodeName]) return i; if (i.nodeType === 3 && i.data.length > 0) return i; } }, k = function (a, b) { var c = b.childNodes; return c.length === 1 && !f.isListNode(c[0]) && a.isBlock(c[0]); }, l = function (a, b) { k(a, b) && a.remove(b.firstChild, !0); }, m = function (a, b, c) { var d, e; if (e = k(a, c) ? c.firstChild : c, l(a, b), !f.isEmpty(a, b, !0)) for (;d = b.firstChild;)e.appendChild(d); }, n = function (a, b, c) { var d, e, g = b.parentNode; f.isChildOfBody(a, b) && f.isChildOfBody(a, c) && (f.isListNode(c.lastChild) && (e = c.lastChild), g === c.lastChild && f.isBr(g.previousSibling) && a.remove(g.previousSibling), d = c.lastChild, d && f.isBr(d) && b.hasChildNodes() && a.remove(d), f.isEmpty(a, c, !0) && a.$(c).empty(), m(a, b, c), e && c.appendChild(e), a.remove(b), f.isEmpty(a, g) && g !== a.getRoot() && a.remove(g)); }, o = function (a, b, c) { a.dom.$(c).empty(), n(a.dom, b, c), a.selection.setCursorLocation(c); }, p = function (a, b, c, d) { var f = a.dom; if (f.isEmpty(d))o(a, c, d); else { var g = e.createBookmark(b); n(f, c, d), a.selection.setRng(e.resolveBookmark(g)); } }, q = function (a, b, c, d) { var f = e.createBookmark(b); n(a.dom, c, d), a.selection.setRng(e.resolveBookmark(f)); }, r = function (a, b) { var c, e, g, i = a.dom, k = a.selection, l = i.getParent(k.getStart(), 'LI'); if (l) { if (c = l.parentNode, c === a.getBody() && f.isEmpty(i, c)) return !0; if (e = h.normalizeRange(k.getRng(!0)), g = i.getParent(j(a, e, b), 'LI'), g && g !== l) return b ? p(a, e, g, l) : q(a, e, l, g), !0; if (!g && !b && d.removeList(a, c.nodeName)) return !0; } return !1; }, s = function (a, b) { var c = a.getParent(b.parentNode, a.isBlock); a.remove(b), c && a.isEmpty(c) && a.remove(c); }, t = function (a, b) { var c = a.dom, e = c.getParent(a.selection.getStart(), c.isBlock); if (e && c.isEmpty(e)) { var f = h.normalizeRange(a.selection.getRng(!0)), g = c.getParent(j(a, f, b), 'LI'); if (g) return a.undoManager.transact(function () { s(c, e), d.mergeWithAdjacentLists(c, g.parentNode), a.selection.select(g, !0), a.selection.collapse(b); }), !0; } return !1; }, u = function (a, b) { return r(a, b) || t(a, b); }, v = function (a) { var b = a.dom.getParent(a.selection.getStart(), 'LI,DT,DD'); return !!(b || i.getSelectedListItems(a).length > 0) && (a.undoManager.transact(function () { a.execCommand('Delete'), g.normalizeLists(a.dom, a.getBody()); }), !0); }, w = function (a, b) { return a.selection.isCollapsed() ? u(a, b) : v(a); }, x = function (a) { a.on('keydown', function (b) { b.keyCode === c.BACKSPACE ? w(a, !1) && b.preventDefault() : b.keyCode === c.DELETE && w(a, !0) && b.preventDefault(); }); }; return {setup: x, backspaceDelete: w}; }), g('2', ['7'], function (a) { var b = function (b) { return {backspaceDelete: function (c) { a.backspaceDelete(b, c); }}; }; return {get: b}; }), g('8', ['j', 'k', 'e', 'f'], function (a, b, c, d) { var e = a.DOM, f = function (a, b) { var d; if (c.isListNode(a)) { for (;d = a.firstChild;)b.appendChild(d); e.remove(a); } }, g = function (a) { var b, d, g; return a.nodeName === 'DT' ? (e.rename(a, 'DD'), !0) : (b = a.previousSibling, b && c.isListNode(b) ? (b.appendChild(a), !0) : b && b.nodeName === 'LI' && c.isListNode(b.lastChild) ? (b.lastChild.appendChild(a), f(a.lastChild, b.lastChild), !0) : (b = a.nextSibling, b && c.isListNode(b) ? (b.insertBefore(a, b.firstChild), !0) : (b = a.previousSibling, !(!b || b.nodeName !== 'LI') && (d = e.create(a.parentNode.nodeName), g = e.getStyle(a.parentNode, 'listStyleType'), g && e.setStyle(d, 'listStyleType', g), b.appendChild(d), d.appendChild(a), f(a.lastChild, d), !0)))); }, h = function (a) { var c = d.getSelectedListItems(a); if (c.length) { for (var e = b.createBookmark(a.selection.getRng(!0)), f = 0; f < c.length && (g(c[f]) || f !== 0); f++);return a.selection.setRng(b.resolveBookmark(e)), a.nodeChanged(), !0; } }; return {indentSelection: h}; }), g('3', ['8', '9', 'a'], function (a, b, c) { var d = function (a, b) { return function () { var c = a.dom.getParent(a.selection.getStart(), 'UL,OL,DL'); return c && c.nodeName === b; }; }, e = function (e) { e.on('BeforeExecCommand', function (c) { var d, f = c.command.toLowerCase(); if (f === 'indent' ? a.indentSelection(e) && (d = !0) : f === 'outdent' && b.outdentSelection(e) && (d = !0), d) return e.fire('ExecCommand', {command: c.command}), c.preventDefault(), !0; }), e.addCommand('InsertUnorderedList', function (a, b) { c.toggleList(e, 'UL', b); }), e.addCommand('InsertOrderedList', function (a, b) { c.toggleList(e, 'OL', b); }), e.addCommand('InsertDefinitionList', function (a, b) { c.toggleList(e, 'DL', b); }), e.addQueryStateHandler('InsertUnorderedList', d(e, 'UL')), e.addQueryStateHandler('InsertOrderedList', d(e, 'OL')), e.addQueryStateHandler('InsertDefinitionList', d(e, 'DL')); }; return {register: e}; }), g('c', [], function () { var a = function (a) { return a.getParam('lists_indent_on_tab', !0); }; return {shouldIndentOnTab: a}; }), g('4', ['b', '8', '9', 'c', '7'], function (a, b, c, d, e) { var f = function (d) { d.on('keydown', function (e) { e.keyCode !== a.TAB || a.metaKeyPressed(e) || d.dom.getParent(d.selection.getStart(), 'LI,DT,DD') && (e.preventDefault(), e.shiftKey ? c.outdentSelection(d) : b.indentSelection(d)); }); }, g = function (a) { d.shouldIndentOnTab(a) && f(a), e.setup(a); }; return {setup: g}; }), g('5', ['d', 'e', 'f'], function (a, b, c) { var d = function (c, d) { return function (e) { var f = e.control; c.on('NodeChange', function (c) { var e = a.grep(c.parents, b.isListNode); f.active(e.length > 0 && e[0].nodeName === d); }); }; }, e = function (a) { return function (d) { var e = d.control; a.on('nodechange', function () { var d = c.getSelectedListItems(a), f = d.length > 0 && b.isFirstChild(d[0]); e.disabled(f); }); }; }, f = function (b) { var c = function (b, c) { var d = b.settings.plugins ? b.settings.plugins : ''; return a.inArray(d.split(/[ ,]/), c) !== -1; }; c(b, 'advlist') || (b.addButton('numlist', {title: 'Numbered list', cmd: 'InsertOrderedList', onPostRender: d(b, 'OL')}), b.addButton('bullist', {title: 'Bullet list', cmd: 'InsertUnorderedList', onPostRender: d(b, 'UL')})), b.addButton('indent', {icon: 'indent', title: 'Increase indent', cmd: 'Indent', onPostRender: e(b)}); }; return {register: f}; }), g('0', ['1', '2', '3', '4', '5'], function (a, b, c, d, e) { return a.add('lists', function (a) { return d.setup(a), e.register(a), c.register(a), b.get(a); }), function () {}; }), d('0')(); }());
