!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; g('1', [], function () { var a = function (b) { var c = b, d = function () { return c; }, e = function (a) { c = a; }, f = function () { return a(d()); }; return {get: d, set: e, clone: f}; }; return a; }), h('9', tinymce.util.Tools.resolve), g('2', ['9'], function (a) { return a('tinymce.PluginManager'); }), h('a', window), g('3', ['a', '2'], function (a, b) { var c = function (c) { return !(!/(^|[ ,])tinymcespellchecker([, ]|$)/.test(c.settings.plugins) || !b.get('tinymcespellchecker')) && (typeof a.console !== 'undefined' && a.console.log && a.console.log('Spell Checker Pro is incompatible with Spell Checker plugin! Remove \'spellchecker\' from the \'plugins\' option.'), !0); }; return {hasProPlugin: c}; }), g('6', [], function () { var a = function (a) { var b = 'English=en,Danish=da,Dutch=nl,Finnish=fi,French=fr_FR,German=de,Italian=it,Polish=pl,Portuguese=pt_BR,Spanish=es,Swedish=sv'; return a.getParam('spellchecker_languages', b); }, b = function (a) { var b = a.getParam('language', 'en'); return a.getParam('spellchecker_language', b); }, c = function (a) { return a.getParam('spellchecker_rpc_url'); }, d = function (a) { return a.getParam('spellchecker_callback'); }, e = function (a) { var b = new RegExp('[^\\s!"#$%&()*+,-./:;<=>?@[\\]^_{|}`\xa7\xa9\xab\xae\xb1\xb6\xb7\xb8\xbb\xbc\xbd\xbe\xbf\xd7\xf7\xa4\u201d\u201c\u201e\xa0\u2002\u2003\u2009]+', 'g'); return a.getParam('spellchecker_wordchar_pattern', b); }; return {getLanguages: a, getLanguage: b, getRpcUrl: c, getSpellcheckerCallback: d, getSpellcheckerWordcharPattern: e}; }), g('c', ['9'], function (a) { return a('tinymce.util.Tools'); }), g('g', ['9'], function (a) { return a('tinymce.util.URI'); }), g('h', ['9'], function (a) { return a('tinymce.util.XHR'); }), g('i', [], function () { var a = function (a) { return a.fire('SpellcheckStart'); }, b = function (a) { return a.fire('SpellcheckEnd'); }; return {fireSpellcheckStart: a, fireSpellcheckEnd: b}; }), g('j', [], function () { function a (a) { return a && a.nodeType === 1 && a.contentEditable === 'false'; } return function (b, c) { function d (a, b) { if (!a[0]) throw 'findAndReplaceDOMText cannot handle zero-length matches'; return {start: a.index, end: a.index + a[0].length, text: a[0], data: b}; } function e (b) { var c; if (b.nodeType === 3) return b.data; if (z[b.nodeName] && !y[b.nodeName]) return ''; if (a(b)) return '\n'; if (c = '', (y[b.nodeName] || A[b.nodeName]) && (c += '\n'), b = b.firstChild) do c += e(b); while (b = b.nextSibling);return c; } function f (b, c, d) { var e, f, g, h, i, j = [], k = 0, l = b, m = 0; c = c.slice(0), c.sort(function (a, b) { return a.start - b.start; }), i = c.shift(); a:for (;;) { if ((y[l.nodeName] || A[l.nodeName] || a(l)) && k++, l.nodeType === 3 && (!f && l.length + k >= i.end ? (f = l, h = i.end - k) : e && j.push(l), !e && l.length + k > i.start && (e = l, g = i.start - k), k += l.length), e && f) { if (l = d({startNode: e, startNodeIndex: g, endNode: f, endNodeIndex: h, innerNodes: j, match: i.text, matchIndex: m}), k -= f.length - h, e = null, f = null, j = [], i = c.shift(), m++, !i) break; } else if (z[l.nodeName] && !y[l.nodeName] || !l.firstChild) { if (l.nextSibling) { l = l.nextSibling; continue; } } else if (!a(l)) { l = l.firstChild; continue; } for (;;) { if (l.nextSibling) { l = l.nextSibling; break; } if (l.parentNode === b) break a; l = l.parentNode; } } } function g (a) { function b (b, c) { var d = B[c]; d.stencil || (d.stencil = a(d)); var e = d.stencil.cloneNode(!1); return e.setAttribute('data-mce-index', c), b && e.appendChild(C.doc.createTextNode(b)), e; } return function (a) { var c, d, e, f = a.startNode, g = a.endNode, h = a.matchIndex, i = C.doc; if (f === g) { var j = f; e = j.parentNode, a.startNodeIndex > 0 && (c = i.createTextNode(j.data.substring(0, a.startNodeIndex)), e.insertBefore(c, j)); var k = b(a.match, h); return e.insertBefore(k, j), a.endNodeIndex < j.length && (d = i.createTextNode(j.data.substring(a.endNodeIndex)), e.insertBefore(d, j)), j.parentNode.removeChild(j), k; }c = i.createTextNode(f.data.substring(0, a.startNodeIndex)), d = i.createTextNode(g.data.substring(a.endNodeIndex)); for (var l = b(f.data.substring(a.startNodeIndex), h), m = [], n = 0, o = a.innerNodes.length; n < o; ++n) { var p = a.innerNodes[n], q = b(p.data, h); p.parentNode.replaceChild(q, p), m.push(q); } var r = b(g.data.substring(0, a.endNodeIndex), h); return e = f.parentNode, e.insertBefore(c, f), e.insertBefore(l, f), e.removeChild(f), e = g.parentNode, e.insertBefore(r, g), e.insertBefore(d, g), e.removeChild(g), r; }; } function h (a) { var b = a.parentNode; b.insertBefore(a.firstChild, a), a.parentNode.removeChild(a); } function i (a) { return a.className.indexOf('mce-spellchecker-word') !== -1; } function j (a) { var c = b.getElementsByTagName('*'), d = []; a = typeof a === 'number' ? '' + a : null; for (var e = 0; e < c.length; e++) { var f = c[e], g = f.getAttribute('data-mce-index'); g !== null && g.length && i(f) && (g !== a && a !== null || d.push(f)); } return d; } function k (a) { for (var b = B.length; b--;) if (B[b] === a) return b; return -1; } function l (a) { var b = []; return m(function (c, d) { a(c, d) && b.push(c); }), B = b, this; } function m (a) { for (var b = 0, c = B.length; b < c && a(B[b], b) !== !1; b++);return this; } function n (a) { return B.length && f(b, B, g(a)), this; } function o (a, b) { if (x && a.global) for (;w = a.exec(x);)B.push(d(w, b)); return this; } function p (a) { var b, c = j(a ? k(a) : null); for (b = c.length; b--;)h(c[b]); return this; } function q (a) { return B[a.getAttribute('data-mce-index')]; } function r (a) { return j(k(a))[0]; } function s (a, b, c) { return B.push({start: a, end: a + b, text: x.substr(a, b), data: c}), this; } function t (a) { var b = j(k(a)), d = c.dom.createRng(); return d.setStartBefore(b[0]), d.setEndAfter(b[b.length - 1]), d; } function u (a, b) { var d = t(a); return d.deleteContents(), b.length > 0 && d.insertNode(c.dom.doc.createTextNode(b)), d; } function v () { return B.splice(0, B.length), p(), this; } var w, x, y, z, A, B = [], C = c.dom; return y = c.schema.getBlockElements(), z = c.schema.getWhiteSpaceElements(), A = c.schema.getShortEndedElements(), x = e(b), {text: x, matches: B, each: m, filter: l, reset: v, matchFromElement: q, elementFromMatch: r, find: o, add: s, wrap: n, unwrap: p, replace: u, rangeFromMatch: t, indexOf: k}; }; }), g('b', ['c', 'g', 'h', 'i', '6', 'j'], function (a, b, c, d, e, f) { var g = function (a, b) { if (!b.get()) { var c = new f(a.getBody(), a); b.set(c); } return b.get(); }, h = function (a) { for (var b in a) return !1; return !0; }, i = function (d, f, g) { return function (h, i, j, k) { var l = {method: h, lang: g.get()}, m = ''; l[h === 'addToDictionary' ? 'word' : 'text'] = i, a.each(l, function (a, b) { m && (m += '&'), m += b + '=' + encodeURIComponent(a); }), c.send({url: new b(f).toAbsolute(e.getRpcUrl(d)), type: 'post', content_type: 'application/x-www-form-urlencoded', data: m, success: function (a) { if (a = JSON.parse(a))a.error ? k(a.error) : j(a); else { var b = d.translate('Server response wasn\'t proper JSON.'); k(b); } }, error: function () { var a = d.translate('The spelling service was not found: (') + e.getRpcUrl(d) + d.translate(')'); k(a); }}); }; }, j = function (a, b, c, d, f, g, h) { var j = e.getSpellcheckerCallback(a), k = j || i(a, b, c); k.call(a.plugins.spellchecker, d, f, g, h); }, k = function (a, b, c, d, e, f) { if (!o(a, c, d)) { var h = function (b) { a.notificationManager.open({text: b, type: 'error'}), a.setProgressState(!1), o(a, c, d); }, i = function (b) { r(a, c, d, e, b); }; a.setProgressState(!0), j(a, b, f, 'spellcheck', g(a, d).text, i, h), a.focus(); } }, l = function (a, b, c) { a.dom.select('span.mce-spellchecker-word').length || o(a, b, c); }, m = function (a, b, c, d, e, f) { a.setProgressState(!0), j(a, b, 'addToDictionary', e, function () { a.setProgressState(!1), a.dom.remove(f, !0), l(a, c, d); }, function (b) { a.notificationManager.open({text: b, type: 'error'}), a.setProgressState(!1); }); }, n = function (b, c, d, e, f, g) { b.selection.collapse(), g ? a.each(b.dom.select('span.mce-spellchecker-word'), function (a) { a.getAttribute('data-mce-word') === e && b.dom.remove(a, !0); }) : b.dom.remove(f, !0), l(b, c, d); }, o = function (a, b, c) { if (g(a, c).reset(), c.set(null), b.get()) return b.set(!1), d.fireSpellcheckEnd(a), !0; }, p = function (a) { var b = a.getAttribute('data-mce-index'); return typeof b === 'number' ? '' + b : b; }, q = function (b, c) { var d, e = []; if (d = a.toArray(b.getBody().getElementsByTagName('span')), d.length) for (var f = 0; f < d.length; f++) { var g = p(d[f]); g !== null && g.length && g === c.toString() && e.push(d[f]); } return e; }, r = function (a, b, c, f, i) { var j, k; if (i.words ? (k = !!i.dictionary, j = i.words) : j = i, a.setProgressState(!1), h(j)) { var l = a.translate('No misspellings found.'); return a.notificationManager.open({text: l, type: 'info'}), void b.set(!1); }f.set({suggestions: j, hasDictionarySupport: k}), g(a, c).find(e.getSpellcheckerWordcharPattern(a)).filter(function (a) { return !!j[a.text]; }).wrap(function (b) { return a.dom.create('span', {'class': 'mce-spellchecker-word', 'data-mce-bogus': 1, 'data-mce-word': b.text}); }), b.set(!0), d.fireSpellcheckStart(a); }; return {spellcheck: k, checkIfFinished: l, addToDictionary: m, ignoreWord: n, findSpansByIndex: q, getElmIndex: p, markErrors: r}; }), g('4', ['6', 'b'], function (a, b) { var c = function (c, d, e, f, g) { var h = function () { return a.getLanguage(c); }, i = function () { return a.getSpellcheckerWordcharPattern(c); }, j = function (a) { b.markErrors(c, d, f, e, a); }, k = function () { return f.get(); }; return {getTextMatcher: k, getWordCharPattern: i, markErrors: j, getLanguage: h}; }; return {get: c}; }), g('5', ['b'], function (a) { var b = function (b, c, d, e, f, g) { b.addCommand('mceSpellCheck', function () { a.spellcheck(b, c, d, e, f, g); }); }; return {register: b}; }), g('7', ['c', '6', 'b'], function (a, b, c) { var d = function (b, c) { var d = []; return a.each(c, function (a) { d.push({selectable: !0, text: a.name, data: a.value}); }), d; }, e = function (a) { return function (c) { var d = b.getLanguage(a); c.control.items().each(function (a) { a.active(a.settings.data === d); }); }; }, f = function (c) { return a.map(b.getLanguages(c).split(','), function (a) { return a = a.split('='), {name: a[0], value: a[1]}; }); }, g = function (a, b, g, h, i, j) { var k = d('Language', f(a)), l = function () { c.spellcheck(a, b, g, h, j, i); }, m = {tooltip: 'Spellcheck', onclick: l, onPostRender: function (b) { var c = b.control; a.on('SpellcheckStart SpellcheckEnd', function () { c.active(g.get()); }); }}; k.length > 1 && (m.type = 'splitbutton', m.menu = k, m.onshow = e(a), m.onselect = function (a) { i.set(a.control.settings.data); }), a.addButton('spellchecker', m), a.addMenuItem('spellchecker', {text: 'Spellcheck', context: 'tools', onclick: l, selectable: !0, onPostRender: function () { var b = this; b.active(g.get()), a.on('SpellcheckStart SpellcheckEnd', function () { b.active(g.get()); }); }}); }; return {register: g}; }), h('d', document), g('e', ['9'], function (a) { return a('tinymce.dom.DOMUtils'); }), g('f', ['9'], function (a) { return a('tinymce.ui.Factory'); }), g('8', ['d', 'e', 'f', 'c', '6', 'b'], function (a, b, c, d, e, f) { var g, h = function (e, h, i, j, k, l, m) { var n = [], o = i.get().suggestions[l]; d.each(o, function (a) { n.push({text: a, onclick: function () { e.insertContent(e.dom.encode(a)), e.dom.remove(m), f.checkIfFinished(e, j, k); }}); }), n.push({text: '-'}); var p = i.get().hasDictionarySupport; p && n.push({text: 'Add to Dictionary', onclick: function () { f.addToDictionary(e, h, j, k, l, m); }}), n.push.apply(n, [{text: 'Ignore', onclick: function () { f.ignoreWord(e, j, k, l, m); }}, {text: 'Ignore all', onclick: function () { f.ignoreWord(e, j, k, l, m, !0); }}]), g = c.create('menu', {items: n, context: 'contextmenu', onautohide: function (a) { a.target.className.indexOf('spellchecker') !== -1 && a.preventDefault(); }, onhide: function () { g.remove(), g = null; }}), g.renderTo(a.body); var q = b.DOM.getPos(e.getContentAreaContainer()), r = e.dom.getPos(m[0]), s = e.dom.getRoot(); s.nodeName === 'BODY' ? (r.x -= s.ownerDocument.documentElement.scrollLeft || s.scrollLeft, r.y -= s.ownerDocument.documentElement.scrollTop || s.scrollTop) : (r.x -= s.scrollLeft, r.y -= s.scrollTop), q.x += r.x, q.y += r.y, g.moveTo(q.x, q.y + m[0].offsetHeight); }, i = function (a, b, c, d, e) { a.on('click', function (g) { var i = g.target; if (i.className === 'mce-spellchecker-word') { g.preventDefault(); var j = f.findSpansByIndex(a, f.getElmIndex(i)); if (j.length > 0) { var k = a.dom.createRng(); k.setStartBefore(j[0]), k.setEndAfter(j[j.length - 1]), a.selection.setRng(k), h(a, b, c, d, e, i.getAttribute('data-mce-word'), j); } } }); }; return {setup: i}; }), g('0', ['1', '2', '3', '4', '5', '6', '7', '8'], function (a, b, c, d, e, f, g, h) { return b.add('spellchecker', function (b, i) { var j = a(!1), k = a(f.getLanguage(b)), l = a(null), m = a({}); return c.hasProPlugin(b) === !1 && (g.register(b, i, j, l, k, m), h.setup(b, i, m, j, l), e.register(b, i, j, l, m, k)), d.get(b, j, m, l, i); }), function () {}; }), d('0')(); }());
