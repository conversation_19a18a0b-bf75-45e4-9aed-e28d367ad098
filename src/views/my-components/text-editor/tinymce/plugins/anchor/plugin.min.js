!(function () { var a = {}, b = function (b) { for (var c = a[b], e = c.deps, f = c.defn, g = e.length, h = new Array(g), i = 0; i < g; ++i)h[i] = d(e[i]); var j = f.apply(null, h); if (void 0 === j) throw 'module [' + b + '] returned undefined'; c.instance = j; }, c = function (b, c, d) { if (typeof b !== 'string') throw 'module id must be a string'; if (void 0 === c) throw 'no dependencies for ' + b; if (void 0 === d) throw 'no definition function for ' + b; a[b] = {deps: c, defn: d, instance: void 0}; }, d = function (c) { var d = a[c]; if (void 0 === d) throw 'module [' + c + '] was undefined'; return void 0 === d.instance && b(c), d.instance; }, e = function (a, b) { for (var c = a.length, e = new Array(c), f = 0; f < c; ++f)e[f] = d(a[f]); b.apply(null, e); }, f = {}; f.bolt = {module: {api: {define: c, require: e, demand: d}}}; var g = c, h = function (a, b) { g(a, [], function () { return b; }); }; h('5', tinymce.util.Tools.resolve), g('1', ['5'], function (a) { return a('tinymce.PluginManager'); }), g('7', [], function () { var a = function (a) { return /^[A-Za-z][A-Za-z0-9\-:._]*$/.test(a); }, b = function (a) { var b = a.selection.getNode(), c = b.tagName === 'A' && a.dom.getAttrib(b, 'href') === ''; return c ? b.id || b.name : ''; }, c = function (a, b) { var c = a.selection.getNode(), d = c.tagName === 'A' && a.dom.getAttrib(c, 'href') === ''; d ? (c.removeAttribute('name'), c.id = b) : (a.focus(), a.selection.collapse(!0), a.execCommand('mceInsertContent', !1, a.dom.createHTML('a', {id: b}))); }; return {isValidId: a, getId: b, insert: c}; }), g('6', ['7'], function (a) { var b = function (b, c) { return a.isValidId(c) ? (a.insert(b, c), !1) : (b.windowManager.alert('Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.'), !0); }, c = function (c) { var d = a.getId(c); c.windowManager.open({title: 'Anchor', body: {type: 'textbox', name: 'id', size: 40, label: 'Id', value: d}, onsubmit: function (a) { var d = a.data.id; b(c, d) && a.preventDefault(); }}); }; return {open: c}; }), g('2', ['6'], function (a) { var b = function (b) { b.addCommand('mceAnchor', function () { a.open(b); }); }; return {register: b}; }), g('3', [], function () { var a = function (a) { return !a.attr('href') && (a.attr('id') || a.attr('name')) && !a.firstChild; }, b = function (b) { return function (c) { for (var d = 0; d < c.length; d++)a(c[d]) && c[d].attr('contenteditable', b); }; }, c = function (a) { a.on('PreInit', function () { a.parser.addNodeFilter('a', b('false')), a.serializer.addNodeFilter('a', b(null)); }); }; return {setup: c}; }), g('4', [], function () { var a = function (a) { a.addButton('anchor', {icon: 'anchor', tooltip: 'Anchor', cmd: 'mceAnchor', stateSelector: 'a:not([href])'}), a.addMenuItem('anchor', {icon: 'anchor', text: 'Anchor', context: 'insert', cmd: 'mceAnchor'}); }; return {register: a}; }), g('0', ['1', '2', '3', '4'], function (a, b, c, d) { return a.add('anchor', function (a) { c.setup(a), b.register(a), d.register(a); }), function () {}; }), d('0')(); }());
