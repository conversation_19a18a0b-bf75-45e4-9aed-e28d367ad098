
<style lang="less" scoped>
    .role-list{
        max-height: 200px;
        overflow-y: scroll;
    }
    .role-list::-webkit-scrollbar{
        background: #fff;
        width: 6px;
    }
    .role-list::-webkit-scrollbar-thumb{
        background: #777;
        border-radius: 3px;
    }
</style>
<template>
    <div>
        <!-- 角色列表 -->
        <Modal
            width="320"
            v-model="showFlag"
            title="用户列表">
            <div class="role-list">
                <p v-for="(item, index) in roleList" :key="index">{{item}} </p>
            </div>
            <div slot="footer">
                <Button @click="showFlag = false" >关闭</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import api from "../../../api/api";
export default {
    data(){
        return{
            showFlag : false,
            roleList: []
        }
    },
    methods: {
        getRoleList(role){
            let params = {
                roleCode: role
            }
            api.yop_acl_role_commons_users(params).then(
                (response) => {
                    let status = response.data.status;
                    if(status === 'success'){
                        this.roleList = response.data.data.result ? response.data.data.result : []
                    }else{
                        this.$ypMsg.notice_error(this,'角色列表获取失败',response.data.message,response.solution);
                    }
                    
                }
            )
        }
    },
}
</script>