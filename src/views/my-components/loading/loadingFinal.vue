<template>
    <Spin fix v-show="show">
        <Icon type="load-c" size=18 class="yop-spin-icon-load"></Icon>
        <div>{{content}}</div>
    </Spin>
</template>

<script>
    export default {
        name: 'loading',
        props: {
            show: false,
            content: String
        }
    }
</script>

<style lang="less">
    .yop-spin-icon-load{
        animation: ani-demo-spin 1s linear infinite;
    }
</style>
