<template>
    <Tooltip placement="top">
        <Icon type="ios-information"></Icon>
        <div slot="content">
            <p>TP指标(TP{{this.tpNum}})：</p>
            <p>指在一个时间段内（如5分钟），统计该方法</p>
            <p>每次调用所消耗的时间，并将这些时间按从</p>
            <p>小到大的顺序进行排序，取第{{this.tpNum}}%的那个值</p>
            <p>作为TP{{this.tpNum}}值；配置此监控指标对应的报警阀</p>
            <p>值后，需要保证在这个时间段内该方法所有</p>
            <p>调用的消耗时间至少有{{this.tpNum}}%的值要小于此阀</p>
            <p>值，否则系统将会报警。</p>
        </div>
    </Tooltip>
</template>

<script>
    export default {
        name: 'tooltipTp',
        props: {
            tpNum: Number
        }
    }
</script>


