<style lang="less">
    @import '../../styles/common.less';
    @import '../API_Management_Open/api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="24">
                <Col span="8" >
                <Col  span="5">
                <Col class="margin-top-10">请求时间：</Col>
                </Col>
                <Col span="8">
                <DatePicker v-show="data_select_type === 'DAILY'" class="margin-top-5" :options="optionsStart" :clearable="false" v-model="requestDateStart" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择起始日期" ></DatePicker>
                <DatePicker v-show="data_select_type !== 'DAILY'" class="margin-top-5" :options="optionsStartMonth" :clearable="false" v-model="requestMonthStart" format="yyyy/MM" type="month" placement="bottom-end" placeholder="选择起始月份" ></DatePicker>
                </Col>
                <!--<Col span="5">-->
                <!--<TimePicker class="margin-top-5"v-model="requestTimeStart" format="HH:mm:ss" placeholder="选择起始时间"></TimePicker>-->
                <!--</Col>-->
                <Col class="margin-top-10" span="1">—</Col>
                <Col span="8">
                <DatePicker  v-show="data_select_type === 'DAILY'" class="margin-top-5" :options="optionsStart" :clearable="false" v-model="requestDateEnd" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择结束日期" ></DatePicker>
                <DatePicker  v-show="data_select_type !== 'DAILY'" class="margin-top-5" :options="optionsStartMonth" :clearable="false" v-model="requestMonthEnd" format="yyyy/MM" type="month" placement="bottom-end" placeholder="选择结束月份" ></DatePicker>
                </Col>

                </Col>
                <Col span="8" >
                <Col  span="5">
                <Col class="margin-top-10" >API分组：&nbsp;</Col>
                </Col>
                <Col span="17">
                <Select id='select_apiM_4' class="margin-top-5" v-model="data_select_apiGroup" filterable placeholder="请选择（默认全部）" clearable>
                    <Option v-for="item in data_apiGroup_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                <!--<Col span="5">-->
                <!--<TimePicker class="margin-top-5" v-model="requestTimeEnd" format="HH:mm:ss" placeholder="选择结束时间"></TimePicker>-->
                <!--</Col>-->
                </Col>
                <Col span="8" >
                <Col  span="5">
                <Col class="margin-top-10" >APIURI：&nbsp;</Col>
                </Col>
                <Col span="17">
                <Input id='input_apiM_2'  class="margin-top-5" v-model="data_interface_uri" placeholder="APIuri" @on-enter="search_Interface"></Input>
                </Col>
                </Col>

                <Col span="8" class="margin-top-5">
                <Col  span="5">
                <Col class="margin-top-10">SP编码：</Col>
                </Col>
                <Col span="17">
                <Select id='select_apiM_2' class="margin-top-5" v-model="data_select_spCode" filterable placeholder="请选择（默认全部）" clearable>
                    <Option v-for="item in data_spCode_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>
                <Col span="8" class="margin-top-5">
                <Col  span="5">
                <Col class="margin-top-10" >统计类型：&nbsp;</Col>
                </Col>
                <Col span="12">
                <!--<Select id='select_apiM_1' class="margin-top-5" v-model="data_select_type"  placeholder="请选择（默认全部）" clearable>-->
                    <!--<Option v-for="item in data_type_List" :value="item.value" :key="item.value">{{ item.label }}</Option>-->
                <!--</Select>-->
                <RadioGroup class="margin-top-10" v-model="data_select_type">
                    <Radio label="DAILY">按日统计</Radio>
                    <Radio label="MONTHLY">按自然月统计</Radio>
                </RadioGroup>
                </Col>
                </Col>


                <Col class="margin-top-10" offset="1" span="5" style="text-align: center">
                <Button class="margin-right-20" id='btn_apiM_1' type="primary" @click="search_Interface">查询</Button>
                <Button  id='btn_apiM_2' type="primary" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_1' border ref="selection" :columns="columns_ApiInterfaceList" :data="data_ApiInterfaceList" @on-selection-change="handleselection"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_apiList"></loading>
            </Row>
        </Card>
    </div>
</template>

<script>
    import api from '../../api/api'
    import Vue from 'vue';
    import qs from 'qs';
    import loading from '../my-components/loading/loading';
    import util from '../../libs/util';
    import tooltipTp from '../my-components/toolTip/toolTip-tp'
    export default {
        name: 'invoke_statistic_latency',
        components:{
            loading,
            tooltipTp
        },
        data () {
            return {
                /**
                 * 主界面部分数据
                 */
                // 请求开始月份额外选项
                optionsStartMonth :{
                    disabledDate (date) {
                        return date && date.valueOf() > util.date_lastMonth().dateDate.getTime();
                    }
                },
                // 请求开始日期额外选项
                optionsStart :{
                    disabledDate (date) {
                        return date && date.valueOf() > Date.now() - 86400000;
                    }
                },
                // api列表加载动画数据绑定
                show_loading_apiList : true,
                // switch点击行数
                switch_index: 0,
                // 应用标识
                data_app_key : '',
                // 请求开始日期
                requestDateStart: '',
                // 请求开始时间
                requestTimeStart: '',
                // 请求月份
                requestMonthStart: '',
                // 请求结束日期
                requestDateEnd : '',
                // 请求结束月份
                requestMonthEnd : '',
                // 请求结束时间
                requestTimeEnd : '',
                // APIuri数据绑定
                data_interface_uri : '',
                // spcode下拉框数据绑定
                data_select_spCode: '',
                // spcode下拉框选项数据
                data_spCode_List: [],
                // api分组下拉框数据绑定
                data_select_apiGroup: '',
                // api分组下拉框数据
                data_apiGroup_List: [],
                // 统计方式类型选择数据绑定
                data_select_type: 'DAILY',
                // 统计方式类型
                data_type_List:[
                    {
                        value: 'MONTHLY',
                        label: '按自然月统计'
                    },
                    {
                        value: 'DAILY',
                        label: '按日统计'
                    }
                ],
                // apiAPI列表列属性
                columns_ApiInterfaceList: [
                    {
                        title: '统计日期',
                        'min-width': 100,
                        key: 'date'
                    },
                    {
                        title: 'SP编码',
                        'min-width': 100,
                        key: 'spCode',
                        type:'html'
                    },
                    {
                        title: 'API分组',
                        'min-width': 100,
                        key: 'apiGroup',
                        type: 'html'
                    },
                    {
                        title: 'APIURI',
                        'min-width': 100,
                        key: 'apiUri'
                    },
                    {
                        title: '调用次数',
                        'min-width': 100,
                        key: 'invokeTimes'
                    },
                    {
                        key: 'lantency',
                        width : 140,
                        renderHeader:(h, params) => {
                            return h('div', [
                                h('div','最大总体耗时'),
                                h('div','最大后端耗时(ms)'),
                                h('div','最大平台耗时')
                            ]);
                        },
                        type: 'html'
                    },
                    {
                        key: 'tp99',
                        'min-width' : 100,
                        renderHeader:(h, params) => {
                            return h('div', [
                                h('div',[
                                    h('span','总体TP99 '),
                                    h(tooltipTp,{
                                        props:{
                                            tpNum : 99
                                        }
                                    })]),

                                h('div','后端TP99(ms)'),
                                h('div','平台TP99')
                            ]);
                        },
                        type: 'html'
                    },
                    {
                        key: 'tp95',
                        'min-width' : 100,
                        renderHeader:(h, params) => {
                            return h('div', [
                                h('div',[
                                    h('span','总体TP95 '),
                                    h(tooltipTp,{
                                        props:{
                                            tpNum : 95
                                        }
                                    })]),

                                h('div','后端TP95(ms)'),
                                h('div','平台TP95')
                            ]);
                        },
                        type: 'html'
                    },
                    {
                        key: 'tp90',
                        'min-width' : 100,
                        renderHeader:(h, params) => {
                            return h('div', [
                                h('div',[
                                    h('span','总体TP90 '),
                                    h(tooltipTp,{
                                        props:{
                                            tpNum : 90
                                        }
                                    })]),

                                h('div','总体TP90(ms)'),
                                h('div','总体TP90')
                            ]);
                        },
                        type: 'html'
                    }
                    ],
                // 表格数据
                data_ApiInterfaceList: [],
                // 分页单页个数
                pageNo : 1,
                // 分页总数
                pageTotal: 0,
                // 当前查询参数
                current_params : {
                    pageNo : 1,
                    pageSize : 10
                }
            };
        },
        methods: {
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
            // 界面初始化函数
            init () {
                this.requestDateStart = util.date_oneDay().before;
                this.requestDateEnd =util.date_oneDay().before;
                this.requestMonthStart = util.date_lastMonth().dateString;
                this.requestMonthEnd =util.date_lastMonth().dateString;
                this.show_loading_apiList = true;
                this.show_loading_apiList = false;
                // spcode列表
                api.yop_invokeSta_spCode().then(
                    (response) => {
                        console.log(response);
                        let resultTemp = response.data.data.result
                        this.data_spCode_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].spCode,
                                label: resultTemp[i].spCode+'/'+resultTemp[i].spName
                            })
                        }
                        this.data_spCode_List = dataTemp;
                    }
                )

                // api分组列表
                api.yop_dashboard_interfaceMenuList().then(
                    (response) => {
                        let resultTemp = response.data.data['api-group-query-list']
                        this.data_apiGroup_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].groupCode,
                                label: resultTemp[i].groupCode+'/'+resultTemp[i].groupTitle
                            })
                        }
                        this.data_apiGroup_List = dataTemp;
                    }
                )
            },
            // 页面刷新
            pageRefresh (val) {
                this.show_loading_apiList = true ;
                this.current_params.pageNo = val;
                api.yop_invokeSta_cost(this.current_params).then(
                    (response) => {
                        if(response.data.status === 'success'){
                            this.tabledataGet(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                            this.pageTotal = response.data.data.page.totalPageNum * 10;
                            this.show_loading_apiList = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_apiList = false;
                        }
                        // console.log(response);
                        // this.tabledataGet(response.data.data.page.items);
                        // this.pageNo = response.data.data.page.pageNo;
                        // this.pageTotal = response.data.data.page.totalPageNum * 10;
                        // this.show_loading_apiList = false;
                    }
                )
                // console.log(a);
            },
            // 表格赋值
            tabledataGet (items) {
                this.data_ApiInterfaceList = [];

                let dataTemp = [];

                for (var i in items){
                    let apiTemp = items[i].apiGroupCode+'<br/>'+items[i].apiGroupName;
                    let spTemp = items[i].spCode+'<br/>'+items[i].spName;
                    let lantencyTemp = items[i].maxTotalLatency+'<br/>'+items[i].maxBackendLatency+'<br/>'+items[i].maxPlatformLatency;
                    let tp99Temp = items[i].totalLatencyTp99+'<br/>'+items[i].backendLatencyTp99+'<br/>'+items[i].platformLatencyTp99;
                    let tp95Temp = items[i].totalLatencyTp95+'<br/>'+items[i].backendLatencyTp95+'<br/>'+items[i].platformLatencyTp95;
                    let tp90Temp = items[i].totalLatencyTp90+'<br/>'+items[i].backendLatencyTp90+'<br/>'+items[i].platformLatencyTp90;
                    dataTemp.push({
                        spCode: spTemp,
                        apiGroup: apiTemp,
                        apiUri:items[i].apiUri,
                        invokeTimes: items[i].invokeTimes,
                        lantency:lantencyTemp,
                        tp99: tp99Temp,
                        tp95: tp95Temp,
                        tp90: tp90Temp,
                        date:items[i].statisticDate
                    });
                }
                this.data_ApiInterfaceList =dataTemp;
            },
            // 安全需求数组处理
            SecurityGenerate (title,array) {
                let securityTemp = title +'<br/>'
                for(var i in array){
                    if(i === array.length){
                        securityTemp = securityTemp +array[i]
                    }else{
                        securityTemp = securityTemp +array[i]+'<br/>'
                    }
                }
                return securityTemp;
            },
            // 空处理函数
            ParamHandle (Param){
                if(Param || (Param === 0)){
                    return Param;
                }else{
                    return '';
                }
            },
            // 表格内容改动时
            handleselection(value){
                this.multiSelectedData =value;
            },
            // 查询apiAPI函数
            search_Interface () {

                    if(this.data_select_type === 'DAILY'){
                        let datebefore = this.requestDateStart;
                        let dateafter = this.requestDateEnd;
                        if(dateafter.getTime() < datebefore.getTime()){
                            this.$Modal.error({
                                title: '错误',
                                content: '结束日期不能小于开始日期'
                            });
                        }else if(this.date_space() > 93){
                            this.$Modal.error({
                                title: '错误',
                                content: '结束日期和开始日期间隔不能超过3个月'
                            });
                        }else{
                            this.search_real();
                        }
                    }else{
                        let monthbefore = this.requestMonthStart;
                        let monthafter = this.requestMonthEnd;
                        console.log(monthbefore);
                        console.log(monthafter);
                        if(monthafter.getTime() < monthbefore.getTime()){
                            this.$Modal.error({
                                title: '错误',
                                content: '结束日期不能小于开始日期'
                            });
                        }else if(this.month_space() > 93){
                            this.$Modal.error({
                                title: '错误',
                                content: '结束日期和开始日期间隔不能超过3个月'
                            });
                        }else{
                            this.search_real();
                        }
                    }


            },


            // 查询执行函数
            search_real () {
                this.show_loading_apiList = true ;
                let dateStartTemp = '';
                let dateEndTemp = '';
                if(this.data_select_type === 'DAILY'){
                    dateStartTemp = this.requestDateStart;
                    dateEndTemp = this.requestDateEnd;
                }else{
                    dateStartTemp = this.requestMonthStart;
                    dateEndTemp = this.requestMonthEnd;
                }
                let paramsTemp = {
                    apiUri: this.data_interface_uri,
                    spCode: this.data_select_spCode,
                    apiGroup : this.data_select_apiGroup,
                    statisticType : this.data_select_type,
                    statisticStartDate: util.dateFormat(dateStartTemp,this.data_select_type),
                    statisticEndDate: util.dateFormat(dateEndTemp,this.data_select_type),
                    pageNo : 1,
                    pageSize: 10
                }
                util.paramFormat(paramsTemp);
                this.current_params = paramsTemp;
                api.yop_invokeSta_cost(paramsTemp).then(
                    (response) => {
                        console.log(response);
                        if(response.data.status === 'success'){
                            this.tabledataGet(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                            this.pageTotal = response.data.data.page.totalPageNum * 10;
                            this.show_loading_apiList = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_apiList = false;
                        }
                    }
                );
            },
            // 重置函数
            reset_Interface () {
                this.data_select_spCode = '';
                this.data_select_apiGroup = '';
                this.data_select_type = 'DAILY';
                this.requestDateStart = util.date_oneDay().before;
                this.requestDateEnd =util.date_oneDay().before;
                this.requestMonthStart = util.date_lastMonth().dateString;
                this.requestMonthEnd =util.date_lastMonth().dateString;
                this.data_interface_uri = '';
                this.current_status ={
                    pageNo : 1,
                    pageSize: 10
                };
            },
            // 一周前日期生成
            date_oneWeek () {
                let now = new Date();
                let date = new Date(now.getTime() - 7 * 24 * 3600 * 1000);
                return util.dateFormat(date);
            },
            // 详情按钮确认
            ok_detail () {
                this.modal_Show_detail = false;
            },
            // 日期区间
            date_space () {
                let dateAfter = this.requestDateEnd;
                let dateBefore = this.requestDateStart;
                let days = Math.ceil((dateAfter.getTime() - dateBefore.getTime()+1000)/(24 * 3600 * 1000));
                console.log(days);
                return days;
            },
            // 日期区间
            month_space () {
                let dateAfter = this.requestMonthEnd;
                let dateBefore = this.requestMonthStart;
                let days = Math.ceil((dateAfter.getTime() - dateBefore.getTime()+1000)/(24 * 3600 * 1000));
                console.log(days+31);
                return days+ 31;
            },





        },
        mounted () {
            // 挂载时调用页面初始化函数
            this.init();
            localStorage.removeItem('apiInfo');
        },
        created (){
            // localStorage.removeItem('apiInfo');
        },

    };
</script>

<style scoped>

</style>
