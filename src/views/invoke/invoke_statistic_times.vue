<style lang="less">
    @import '../../styles/common.less';
    @import '../API_Management_Open/api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
    .page-wrapper-2 {
      .ivu-card {
        min-height: 500px;
      }
    }
</style>
<template>
    <div style="background: #eee;padding:8px" class="page-wrapper-2">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">
                <Col span="7" >
                <Col  span="7">
                <Col class="margin-top-10" >API分组：&nbsp;</Col>
                </Col>
                <Col span="15">
                <Select ref="select_apiM_4" id='select_apiM_4' class="margin-top-5" v-model="data_select_apiGroup" filterable placeholder="请选择（默认全部）" clearable>
                    <Option v-for="item in data_apiGroup_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>
                <Col span="9" >
                <Col  span="6">
                <Col class="margin-top-10">请求时间：&nbsp;</Col>
                </Col>
                <Col span="8">
                <DatePicker v-show="data_select_type === 'DAILY'" class="margin-top-5" :options="optionsStart" :clearable="false" v-model="requestDateStart" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择起始日期" ></DatePicker>
                <DatePicker v-show="data_select_type !== 'DAILY'" class="margin-top-5" :options="optionsStart" :clearable="false" v-model="requestMonthStart" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择起始日期" ></DatePicker>
                </Col>
                <!--<Col span="5">-->
                <!--<TimePicker class="margin-top-5"v-model="requestTimeStart" format="HH点mm分ss秒" placeholder="选择起始时间"></TimePicker>-->
                <!--</Col>-->
                <Col class="margin-top-10" span="1">—</Col>
                <Col span="8">
                <DatePicker  v-show="data_select_type === 'DAILY'" class="margin-top-5" :options="optionsStart" :clearable="false" v-model="requestDateEnd" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择结束日期" ></DatePicker>
                <DatePicker  v-show="data_select_type !== 'DAILY'" class="margin-top-5" :options="optionsStart" :clearable="false" v-model="requestMonthEnd" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择结束日期" ></DatePicker>
                </Col>
                <!--<Col span="5">-->
                <!--<TimePicker class="margin-top-5" v-model="requestTimeEnd" format="HH点mm分ss秒" placeholder="选择结束时间"></TimePicker>-->
                <!--</Col>-->
                </Col>
                <Col span="7">
                <Col offset="1" span="6">
                <Col class="margin-top-10">SP编码：</Col>
                </Col>
                <Col span="15">
                <Select ref="select_apiM_2" id='select_apiM_2' class="margin-top-5" v-model="data_select_spCode" filterable placeholder="请选择（默认全部）" clearable>
                    <Option v-for="item in data_spCode_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>
                </Col>
                <Col span="4">
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button  id='btn_apiM_1' type="primary" v-url="{url:'/rest/invoke-stat/api-group/list'}" @click="search_Interface">查询</Button>
                </Col>
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button  id='btn_apiM_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>


                <Col span="8" class="margin-top-5" >
                <Col  span="6">
                <Col class="margin-top-10" >统计类型：&nbsp;</Col>
                </Col>
                <Col span="16">
                <!--<Select id='select_apiM_1' class="margin-top-5" v-model="data_select_type"  placeholder="请选择（默认全部）" clearable>-->
                    <!--<Option v-for="item in data_type_List" :value="item.value" :key="item.value">{{ item.label }}</Option>-->
                <!--</Select>-->
                <RadioGroup class="margin-top-10" v-model="data_select_type">
                    <Radio label="DAILY">按日统计</Radio>
                    <Radio label="MONTHLY">按自然月统计</Radio>
                </RadioGroup>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_1' border ref="selection" :columns="columns_ApiInterfaceList" :data="data_ApiInterfaceList" @on-selection-change="handleselection"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_apiList"></loading>
            </Row>
        </Card>
    </div>
</template>

<script>
    import api from '../../api/api'
    import Vue from 'vue';
    import qs from 'qs';
    import loading from '../my-components/loading/loading';
    import util from '../../libs/util'
    export default {
        name: 'invoke_statistic_times',
        components:{
            loading
        },
        data () {
            return {
                /**
                 * 主界面部分数据
                 */
                // 请求开始日期额外选项
                optionsStart :{
                    disabledDate (date) {
                        return date && date.valueOf() > Date.now() - 86400000;
                    }
                },
                // api列表加载动画数据绑定
                show_loading_apiList : true,
                // switch点击行数
                switch_index: 0,
                // 应用标识
                data_app_key : '',
                // 请求开始日期
                requestDateStart: '',
                // 请求开始时间
                requestTimeStart: '',
                // 请求月份
                requestMonthStart: '',
                // 请求结束日期
                requestDateEnd : '',
                // 请求结束月份
                requestMonthEnd : '',
                // 请求结束时间
                requestTimeEnd : '',
                // spcode下拉框数据绑定
                data_select_spCode: '',
                // spcode下拉框选项数据
                data_spCode_List: [],
                // api分组下拉框数据绑定
                data_select_apiGroup: '',
                // api分组下拉框数据
                data_apiGroup_List: [],
                // 统计方式类型选择数据绑定
                data_select_type: 'DAILY',
                // 统计方式类型
                data_type_List:[
                    {
                        value: 'MONTHLY',
                        label: '按自然月统计'
                    },
                    {
                        value: 'DAILY',
                        label: '按日统计'
                    }
                ],
                // apiAPI列表列属性
                columns_ApiInterfaceList: [

                    {
                        title: '统计日期',
                        key: 'date',
                        'min-width' : 200,
                        align: 'center'
                    },
                    {
                        title: 'SP',
                        'min-width': 200,
                        key: 'spCode',
                        type: 'html',
                        align: 'center'
                    },
                    {
                        title: 'API分组',
                        'min-width': 200,
                        key: 'apiGroup',
                        type: 'html',
                        align: 'center'
                    },
                    {
                        title: '调用次数',
                        'min-width': 200,
                        key: 'times',
                        align: 'center',
                        sortable: true
                    }],
                // 表格数据
                data_ApiInterfaceList: [],
                // 分页单页个数
                pageNo : 1,
                // 分页总数
                pageTotal: 0,
                // 当前查询参数
                current_params : {
                    pageNo : 1,
                    pageSize : 10
                }
            };
        },
        methods: {
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
            // 界面初始化函数
            init () {
                this.requestDateStart = util.date_oneDay().before;
                this.requestDateEnd =util.date_oneDay().before;
                this.requestMonthStart = util.date_oneMonth().before;
                this.requestMonthEnd =util.date_oneMonth().after;
                this.show_loading_apiList = true;
                this.show_loading_apiList = false;
                // spcode列表
                api.yop_invokeSta_spCode().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.data_spCode_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].spCode,
                                label: resultTemp[i].spCode+'('+resultTemp[i].spName+')'
                            })
                        }
                        this.data_spCode_List = dataTemp;
                    }
                )

                // api分组列表
                api.yop_dashboard_apis_list().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.data_apiGroup_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].apiGroupCode,
                                label: resultTemp[i].apiGroupCode+'('+resultTemp[i].apiGroupName+')'
                            })
                        }
                        this.data_apiGroup_List = dataTemp;
                    }
                )
            },
            // 页面刷新
            pageRefresh (val) {
                this.show_loading_apiList = true ;
                this.current_params.pageNo = val;
                api.yop_invokeSta_times(this.current_params).then(
                    (response) => {
                        if(response.data.status === 'success'){
                            this.tabledataGet(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                            this.pageTotal = response.data.data.page.totalPageNum * 10;
                            this.show_loading_apiList = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_apiList = false;
                        }
                    }
                )
                // console.log(a);
            },
            // 表格赋值
            tabledataGet (items) {
                this.data_ApiInterfaceList = [];
                let dataTemp = [];
                for (var i in items){
                    let apiTemp = items[i].apiGroupName+'<br/>'+'('+items[i].apiGroupCode+')'
                    let spTemp = items[i].spName+'<br/>'+'('+items[i].spCode+')'
                    dataTemp.push({
                        spCode: spTemp,
                        apiGroup: apiTemp,
                        times:items[i].invokeTimes,
                        date: items[i].statisticDate

                    });
                }
                this.data_ApiInterfaceList =dataTemp;
            },
            // 安全需求数组处理
            SecurityGenerate (title,array) {
                let securityTemp = title +'<br/>'
                for(var i in array){
                    if(i === array.length){
                        securityTemp = securityTemp +array[i]
                    }else{
                        securityTemp = securityTemp +array[i]+'<br/>'
                    }
                }
                return securityTemp;
            },
            // 空处理函数
            ParamHandle (Param){
                if(Param || (Param === 0)){
                    return Param;
                }else{
                    return '';
                }
            },
            // 表格内容改动时
            handleselection(value){
                this.multiSelectedData =value;
            },
            // 查询apiAPI函数
            search_Interface () {
                let datebefore = this.requestDateStart;
                let dateafter = this.requestDateEnd;
                if(dateafter.getTime() < datebefore.getTime()){
                    this.$Modal.error({
                        title: '错误',
                        content: '结束日期不能小于开始日期'
                    });
                }else if(this.date_space() > 93){
                    this.$Modal.error({
                        title: '错误',
                        content: '结束日期和开始日期间隔不能超过3个月'
                    });
                }else{
                    this.search_real();
                }
            },
            date_real (date) {
                let dateBefore = util.dateFormat_sep(date);
                let str = dateBefore.toString();
                str = str.replace("/-/g", "/");
                let odateBefore = new Date(str);
                return odateBefore;
            },
            // 查询执行函数
            search_real () {
                this.show_loading_apiList = true;
                let dateStartTemp = '';
                let dateEndTemp = '';
                if(this.data_select_type === 'DAILY'){
                    dateStartTemp = this.requestDateStart;
                    dateEndTemp = this.requestDateEnd;
                }else{
                    dateStartTemp = this.requestMonthStart;
                    dateEndTemp = this.requestMonthEnd;
                }
                let paramsTemp = {
                    spCode: this.data_select_spCode,
                    apiGroup : this.data_select_apiGroup,
                    statisticType : this.data_select_type,
                    statisticStartDate: util.dateFormat(dateStartTemp,'DAILY'),
                    statisticEndDate: util.dateFormat(dateEndTemp,'DAILY'),
                    pageNo : 1,
                    pageSize: 10
                }
                util.paramFormat(paramsTemp);
                this.current_params = paramsTemp;
                api.yop_invokeSta_times(paramsTemp).then(
                    (response) => {
                        if(response.data.status === 'success'){
                            this.tabledataGet(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                            this.pageTotal = response.data.data.page.totalPageNum * 10;
                            this.show_loading_apiList = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_apiList = false;
                        }
                    }
                );
            },
            // 重置函数
            reset_Interface () {
                // this.data_select_spCode = '';
                this.$refs.select_apiM_4.clearSingleSelect();
                this.$refs.select_apiM_2.clearSingleSelect();
                // this.data_select_apiGroup = '';
                this.data_select_type = 'DAILY';
                this.requestDateStart = util.date_oneDay().before;
                this.requestDateEnd =util.date_oneDay().before;
                this.requestMonthStart = util.date_oneMonth().before;
                this.requestMonthEnd =util.date_oneMonth().after;
                this.current_status ={
                    pageNo : 1,
                    pageSize: 10
                };
            },
            // 详情按钮确认
            ok_detail () {
                this.modal_Show_detail = false;
            },
            // 日期区间
            date_space () {
                let dateAfter = this.requestDateEnd;
                let dateBefore = this.requestDateStart;
                let days = Math.ceil((dateAfter.getTime() - dateBefore.getTime()+1000)/(24 * 3600 * 1000));
                return days;
            }



        },
        mounted () {
            // 挂载时调用页面初始化函数
            this.init();
            localStorage.removeItem('apiInfo');
        },
        created (){
            // localStorage.removeItem('apiInfo');
        },

    };
</script>

<style scoped>

</style>
