<style lang="less">
    @import '../../styles/common.less';
    @import '../API_Management_Open/api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">

                <Col span="7">
                <Col span="8" class="margin-top-10">
                <span>服务提供方:</span>
                </Col>
                <Col span="16">
                <common-select ref="select_sp" @on-update="updateSelect_sp"
                               type="combo"
                               keyWord="result"
                               code="spCode"
                               title="spName"
                               group="serviceSupplier"
                               :default="this.data_select_sp"
                               @on-loaded="select_callBack"
                               holder="请选择（默认全部）"
                               :uri="this.$store.state.select.serviceSupplier.uri"></common-select>
                </Col>
                </Col>

                <Col offset="1" span="7">
                <Col span="8" class="margin-top-10">
                <span>后端应用编码:</span>
                </Col>
                <Col span="16">
                <Input id='input_backend_1' class="margin-top-5" v-model="data_backend_code" placeholder="后端应用编码"
                       @on-enter="search_interface"></Input>
                </Col>
                </Col>

                <Col offset="1" span="7">
                <Col span="8" class="margin-top-10">
                <span>后端应用名称:</span>
                </Col>
                <Col span="16">
                <Input id='input_backend_2' class="margin-top-5" v-model="data_backend_name" placeholder="后端应用名称"
                       @on-enter="search_interface"></Input>
                </Col>
                </Col>

                <Col class="margin-top-5" span="7">
                <Col span="8" class="margin-top-10">
                <span>部署环境:</span>
                </Col>
                <Col span="16">
                <common-select ref="select_env" @on-update="updateSelect_env"
                               type="normal"
                               keyWord="result"
                               holder="请选择（默认全部）"
                               code="code"
                               title="name"
                               group="ba_env"
                               @on-loaded="select_callBack"
                               :default="this.data_select_env"
                               :uri="this.$store.state.select.ba_env.uri"></common-select>
                </Col>
                </Col>

                <Col class="margin-top-5" offset="1" span="7">
                <Col span="8" class="margin-top-10">
                <span>类加载方式:</span>
                </Col>
                <Col span="16">
                <common-select ref="select_classLoader" @on-update="updateSelect_classLoader"
                               type="normal"
                               keyWord="result"
                               holder="请选择（默认全部）"
                               code="code"
                               title="name"
                               group="ba_classLoader"
                               @on-loaded="select_callBack"
                               :default="this.data_select_classLoader"
                               :uri="this.$store.state.select.ba_classLoader.uri"></common-select>
                </Col>
                </Col>

                <Col class="margin-top-5" offset="1" span="7">
                <Col span="8" class="margin-top-10">
                <span>后端服务方式:</span>
                </Col>
                <Col span="16">
                <common-select ref="select_service" @on-update="updateSelect_service"
                               type="normal"
                               keyWord="result"
                               holder="请选择（默认全部）"
                               code="code"
                               title="name"
                               group="ba_service"
                               @on-loaded="select_callBack"
                               :default="this.data_select_service"
                               :uri="this.$store.state.select.ba_service.uri"></common-select>
                </Col>
                </Col>
                </Col>

                <Col span="4">
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_apiM_1' type="primary"  v-url="{url:'/rest/backend-app/list'}"  @click="search_interface">查询</Button>
                </Col>
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_apiM_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Button class="margin-right-10"  v-url="{url:'/rest/backend-app/create'}" type="primary" @click="backendApp_add">新增后端应用</Button>
                <Button class="margin-right-10"  v-url="{url:'/rest/backend-app/batch-init'}" :loading="init_loading"type="ghost" @click="batch_init">批量初始化</Button>
                </Col>
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_1' border :columns="columns_backendAppList" :data="data_backendAppList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10"
                          :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_bakcendAppList"></loading>
            </Row>
            <Modal id="modal_request_1" v-model="modal_create_backend" width="600" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" v-show="create_orEdit">新增后端应用</span>
                    <span style="color:black" v-show="!create_orEdit">编辑后端应用</span>
                </p>
                <div>
                    <Form ref="form_backend" :model="form_backend" :rules="rule_backend" :label-width="120">
                        <FormItem label="服务提供方：" prop="sp" v-show="create_orEdit">
                            <common-select ref="select_sp_m" @on-update="updateSelect_sp_modal"
                                           type="combo"
                                           keyWord="result"
                                           holder="请选择服务提供方编码"
                                           code="spCode"
                                           title="spName"
                                           group="serviceSupplier"
                                           @on-loaded="select_callBack"
                                           :default="this.form_backend.sp"
                                           size="small"
                                           :uri="this.$store.state.select.serviceSupplier.uri"
                                           style="width:80%"></common-select>
                        </FormItem>
                        <FormItem label="服务提供方：" v-show="!create_orEdit">
                            {{form_backend.spName}}({{form_backend.sp}})
                        </FormItem>
                        <FormItem label="后端应用标识：" prop="backendAppCode" v-show="create_orEdit">
                            <Input type="text" size="small" v-model="form_backend.backendAppCode" style="width:80%"></Input>
                        </FormItem>
                        <p class="yop-explain-120" v-show="create_orEdit">最长可输入64个字符，支持特殊字符</p>
                        <FormItem label="后端应用标识：" v-show="!create_orEdit">
                            {{form_backend.backendAppCode}}
                        </FormItem>
                        <FormItem label="后端应用名称：" prop="backendAppName">
                            <Input type="text" size="small" v-model="form_backend.backendAppName" style="width:80%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">最长可输入64个字符，支持特殊字符</p>
                        <FormItem label="部署环境：" prop="envCode">
                            <common-select ref="select_env_m" @on-update="updateSelect_env_modal"
                                           type="normal"
                                           keyWord="result"
                                           holder="请选择部署环境"
                                           code="code"
                                           title="name"
                                           group="ba_env"
                                           @on-loaded="select_callBack"
                                           :default="this.form_backend.envCode"
                                           size="small"
                                           :uri="this.$store.state.select.ba_env.uri"
                                           style="width:80%"></common-select>
                        </FormItem>
                        <FormItem label="类加载方式：" prop="classLoaderCode">
                            <common-select ref="select_classLoader_m" @on-update="updateSelect_classLoader_modal"
                                           type="normal"
                                           keyWord="result"
                                           holder="请选择类加载方式"
                                           code="code"
                                           title="name"
                                           group="ba_classLoader"
                                           @on-loaded="select_callBack"
                                           :default="this.form_backend.classLoaderCode"
                                           size="small"
                                           :uri="this.$store.state.select.ba_classLoader.uri"
                                           style="width:80%"></common-select>
                        </FormItem>
                        <FormItem label="后端服务方式：" prop="serviceMethodCode">
                            <common-select ref="select_serviceMethod_m" @on-update="updateSelect_serviceMethod_modal"
                                           type="normal"
                                           keyWord="result"
                                           holder="请选择后端服务方式"
                                           code="code"
                                           title="name"
                                           group="ba_service"
                                           @on-loaded="select_callBack"
                                           :default="this.form_backend.serviceMethodCode"
                                           size="small"
                                           :uri="this.$store.state.select.ba_service.uri"
                                           style="width:80%"></common-select>
                        </FormItem>
                        <FormItem label="描述：" prop="description">
                            <Input type="textarea" size="small" v-model="form_backend.description"
                                   style="width:80%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">最长可输入100个字符</p>
                    </Form>
                </div>
                <div slot="footer">
                    <Button id="modal_request_btn_2" type="primary" @click="ok_create_backend('form_backend')">确定</Button>
                    <Button id="modal_request_btn_1" type="ghost"  @click="cancel_create_backend">取消</Button>
                </div>
                <loading :show="show_loading_createBackend"></loading>
            </Modal>
            <Modal id="modal_request_2" v-model="modal_classLoader" :closable="false" width="700">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black">重建ClassLoader</span>
                </p>
                <div >
                    <p style="color:red; margin-left: 120px;">重建ClassLoader操作， 仅在您的应用有facade更新上线之后。</p>
                    <p style=" margin-left: 120px;margin-bottom: 20px;">确认重建ClassLoader？</p>
                    <Form ref="form_classLoader" :model="form_classLoader"  :rules="rule_classLoader" :label-width="120">
                        <FormItem label="操作类型：" prop="opt_type">
                            <!--<Select ref="select_opt_type" size="small" class="margin-top-5" v-model="form_classLoader.opt_type"  placeholder="请选择环境类型" style="width: 90%">-->
                                <!--<Option v-for="item in data_opt_type_List" :value="item.value" :key="item.value">{{ item.label }}</Option>-->
                            <!--</Select>-->
                            <common-select ref="select_opt_type_m" @on-update="updateSelect_opt_type_modal"
                                           type="normal"
                                           keyWord="result"
                                           holder="请选择操作类型"
                                           code="code"
                                           title="name"
                                           group="opt_type"
                                           @on-loaded="select_callBack"
                                           :default="this.form_classLoader.opt_type"
                                           size="small"
                                           :uri="this.$store.state.select.opt_type.uri"
                                           style="width:90%"></common-select>
                        </FormItem>
                        <FormItem label="原因：" prop="classLoader">
                            <Input type="textarea" size="small" v-model="form_classLoader.classLoader"
                                   style="width:90%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">最长可输入100个字符</p>
                    </Form>
                </div>
                <div slot="footer">
                    <Button type="primary" @click="classLoader_submit('form_classLoader')">确认</Button>
                    <Button type="ghost" @click="classLoader_cancel">取消</Button>
                </div>
                <loading :show="show_loading_rebuild"></loading>
            </Modal>
        </Card>
    </div>
</template>

<script>
    import loading from '../my-components/loading/loading'
    import commonSelect from '../common-components/select-components/selectCommon'
    import util from '../../libs/util'
    import api from '../../api/api'
    export default {
        name: 'backend-app',
        components:{
            loading,
            commonSelect
        },
        data(){
            const validate_notNull = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('输入内容不能为空'));
                }else{
                    callback();
                }
            };
            // classLoader输入验证
            const validate_classLoader = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('原因不能为空'));
                }else if(util.getLength(value) > 100){
                    callback(new Error('长度不能大于100'));
                }else{
                    callback();
                }
            };
            // 后端应用标识输入验证
            const validate_ba_code = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('后端应用标识不能为空'));
                }else if(util.getLength(value) > 64){
                    callback(new Error('长度不能大于64'));
                }else{
                    callback();
                }
            };
            // 后端应用名称输入验证
            const validate_ba_name = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('后端应用名称不能为空'));
                }else if(util.getLength(value) > 64){
                    callback(new Error('长度不能大于64'));
                }else{
                    callback();
                }
            };
            // 描述输入验证
            const validate_ba_description = (rule, value, callback) => {
                if(util.getLength(value) > 100){
                    callback(new Error('长度不能大于100'));
                }else{
                    callback();
                }
            };
            return {
                data_opt_type_List : [
                    {
                        value : 'UPGRADE_NC',
                        label : '仅升级内测环境',
                        name : '仅升级内测环境'
                    },
                    {
                        value : 'SYNC_NC_TO_PRO',
                        label : '同步内测到生产',
                        name : '同步内测到生产'
                    },
                    {
                        value : 'UPDATE_ALL',
                        label : '同步所有环境',
                        name : '同步所有环境'
                    },
                    {
                        value : 'UPGRADE_ALL',
                        label : '升级所有环境(审慎!)',
                        name : '升级所有环境(审慎!)'
                    }
                ],
                show_loading_rebuild : false,
                // 初始化按钮loading
                init_loading :false,
                // 列表loading
                show_loading_bakcendAppList :false,
                // 新增还是编辑界面
                create_orEdit : true,
                // 新增界面弹窗显示
                modal_create_backend : false,
                // 服务提供方数据绑定
                data_select_sp : '',
                // 下拉框个数
                count_select_related : 4,
                // 后端应用编码数据绑定
                data_backend_code : '',
                // 后端应用名称数据绑定
                data_backend_name : '',
                // 部署环境数据绑定
                data_select_env : '',
                // 后端服务方式数据绑定
                data_select_service : '',
                // 类加载数据绑定
                data_select_classLoader : '',
                // 新增后端应用loading
                show_loading_createBackend : false,
                // 后端应用列表表头
                columns_backendAppList : [
                    {
                        title: '服务提供方',
                        width: 150,
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.spName),
                                h('p', '(' + params.row.spCode + ')')
                            ]);
                        },
                        align: 'center'
                    },
                    {
                        title: '后端应用',
                        width: 200,
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.backendAppName),
                                h('p', '(' + params.row.backendAppCode + ')')
                            ]);
                        },
                        align: 'center'
                    },
                    {
                        title: '部署环境',
                        key: 'deployEnv',
                        width: 120,
                        align: 'center'
                    },
                    {
                        title: '类加载方式',
                        key: 'classLoader',
                        width: 120,
                        align: 'center'
                    },
                    {
                        title: '后端服务方式',
                        key: 'bService',
                        width: 120,
                        align: 'center'
                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ]);
                        },
                        width: 160,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [h('p', params.row.createTime),
                                h('p', params.row.lastModifyTime)]);
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'operations',
                        'min-width': 200,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/backend-app/update'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.backend_modify(params.row.backendAppCode);
                                        }
                                    }
                                }, '编辑'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/backend-app/delete'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.backend_delete(params.row.backendAppCode);
                                        }
                                    }
                                }, '删除'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    style: {
                                        color : 'red'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/backend-app/class-loader/rebuild'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.rebuild_classLoader(params.row.backendAppCode);
                                        }
                                    }
                                }, '重建ClassLoader'),

                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/backend-app/export-apis'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.$Modal.confirm({
                                                title: '导出API',
                                                content: "<p>确定导出API？</p>",
                                                onOk: () =>{
                                                    this.exportApi(params.row);
                                                }
                                            });
                                        }
                                    }
                                }, '导出api')
                            ]);
                        }

                    }
                ],
                // 后端应用数据
                data_backendAppList : [
                    // {
                    //     spCode : 'spCode',
                    //     spName: '服务提供方编码',
                    //     backendAppCode : 'bc-pay-hessian',
                    //     backendAppName : '系统自动生成',
                    //     deployEnv : 'docker',
                    //     deployEnvCode : 'docker',
                    //     classLoader : '本地',
                    //     classLoaderCode : 'local',
                    //     bService : 'RMI',
                    //     bServiceCode : 'RMI',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-03 18:40:09'
                    // }
                ],
                // 总数
                pageTotal : 10,
                // 当前页码
                pageNo : 1,
                // classLoader表单数据
                form_classLoader : {
                    classLoader : '',
                    opt_type : ''
                },
                // classLoader表单数据规则
                rule_classLoader : {
                    classLoader: [
                        {required: true, message: '内容不能为空', trigger: 'blur'},
                        { validator: validate_classLoader, trigger: 'blur' }
                    ],
                    opt_type:[
                        {required: true, message: '请选择操作类型'}
                    ],
                },
                // classloader弹窗
                modal_classLoader : false,
                // classloader原因
                reason_classLoader : '',
                form_backend : {
                    sp: '',
                    spName : '',
                    backendAppCode : '',
                    backendAppName : '',
                    env : '',
                    envCode : '',
                    classLoader : '',
                    classLoaderCode : '',
                    serviceMethod : '',
                    serviceMethodCode : '',
                    description : '',
                    version : ''
                },
                rule_backend : {
                    sp: [
                        {required: true, message: '服务提供方不能为空'},
                    ],
                    backendAppCode : [
                        {required: true, message: '后端应用标识不能为空', trigger: 'blur'},
                        { validator: validate_ba_code, trigger: 'blur' }
                    ],
                    backendAppName : [
                        {required: true, message: '后端应用名称不能为空', trigger: 'blur'},
                        { validator: validate_ba_name, trigger: 'blur' }
                    ],
                    envCode : [
                        {required: true, message: '部署环境不能为空'},
                    ],
                    classLoaderCode : [
                        {required: true, message: '累加载方式不能为空'}
                    ],
                    serviceMethodCode : [
                        {required: true, message: '后端服务方式不能为空'},
                    ],
                    description : [
                        { validator: validate_ba_description, trigger: 'blur' }
                    ]
                },
                // 当前后端应用标识
                currentCode : ''
            }
        },
        methods:{
            // 页面初始化
            init(){

            },
            // 新增后端应用确定
            ok_create_backend (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        if(this.create_orEdit){
                            let  param= {
                                spCode : this.form_backend.sp,
                                backendCode : this.form_backend.backendAppCode.trim(),
                                backendName: this.form_backend.backendAppName.trim(),
                                deployMode : this.form_backend.envCode,
                                classLoadMode : this.form_backend.classLoaderCode,
                                rpcMode: this.form_backend.serviceMethodCode,
                                description: this.form_backend.description
                            }
                            util.paramFormat(param);
                            api.yop_backend_app_create(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        this.$ypMsg.notice_success(this,'创建成功');
                                            this.modal_create_backend = false;
                                            this.search_interface();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        this.modal_create_backend = false;
                                    }
                                }
                            )
                        }else{
                            let param = {
                                version : this.form_backend.version,
                                backendCode: this.form_backend.backendAppCode.trim(),
                                backendName: this.form_backend.backendAppName.trim(),
                                deployMode : this.form_backend.envCode,
                                classLoadMode : this.form_backend.classLoaderCode,
                                rpcMode: this.form_backend.serviceMethodCode,
                                description: this.form_backend.description
                            }
                            util.paramFormat(param);
                            //可能需要对密钥处理剔除
                            api.yop_backend_app_update(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        this.$ypMsg.notice_success(this,'修改成功');
                                            this.modal_create_backend = false;
                                            this.search_interface();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        this.modal_create_backend = false;
                                    }
                                }
                            )
                        }
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 新增后端应用取消
            cancel_create_backend () {
                this.modal_create_backend = false;
                this.formData_reset();
            },
            // 服务提供方数据更新
            updateSelect_sp (val) {
                this.data_select_sp = val;
            },
            // 下拉框加载完处理函数
            select_callBack () {
                this.count_select_related--;
                if (this.count_select_related === 0) {
                    this.search_interface();
                }
            },
            // 后端应用查询函数
            search_interface () {
                this.show_loading_bakcendAppList = true;
                let params = {
                    spCode: this.data_select_sp,
                    backendCode: this.data_backend_code.trim(),
                    backendName: this.data_backend_name.trim(),
                    deployMode: this.data_select_env,
                    classLoadMode: this.data_select_classLoader,
                    rpcMode:this.data_select_service,
                    _pageNo: 1,
                    _pageSize: 10
                };
                util.paramFormat(params);
                api.yop_backend_app_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            this.pageTotal = response.data.data.result.totalPageNum * 10;
                            this.show_loading_bakcendAppList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_bakcendAppList = false;
                        }
                    }
                );
            },
            // 表格数据处理函数
            tableDataFormat (items) {
                this.data_backendAppList = [];
                for (var i in items) {
                    this.data_backendAppList.push({
                        spCode : util.empty_handler(items[i].spCode),
                        spName: util.empty_handler(this.data_name_handler(items[i].spCode, 'serviceSupplier')),
                        backendAppCode : util.empty_handler(items[i].backendCode),
                        backendAppName : util.empty_handler(items[i].backendName),
                        deployEnv : util.empty_handler(this.data_name_handler(items[i].deployMode, 'ba_env')),
                        deployEnvCode : util.empty_handler(items[i].deployMode),
                        classLoader : util.empty_handler(this.data_name_handler(items[i].classLoadMode, 'ba_classLoader')),
                        classLoaderCode : util.empty_handler(items[i].classLoadMode),
                        bService : util.empty_handler(this.data_name_handler(items[i].rpcMode, 'ba_service')),
                        bServiceCode : util.empty_handler(items[i].rpcMode),
                        createTime : util.empty_handler(items[i].createdDate),
                        lastModifyTime : util.empty_handler(items[i].lastModifiedDate),
                    });
                }
            },
            // 返回相应编码的名字
            data_name_handler (code, name) {
                if (code) {
                    let group = this.$store.state.select[name].data;
                    for (var i in group) {
                        if (group[i].value === code) {
                            return group[i].name;
                        }
                    }
                } else {
                    return '';
                }
            },
            // 后端应用重置函数
            reset_Interface () {
                this.$refs.select_sp.resetSelected();
                this.$refs.select_env.resetSelected();
                this.$refs.select_classLoader.resetSelected();
                this.$refs.select_service.resetSelected();
                this.data_select_sp = '';
                this.data_select_env = '';
                this.data_select_classLoader = '';
                this.data_select_service = '';
                this.data_backend_code = '';
                this.data_backend_name = '';
            },
            // 后端部署环境数据更新
            updateSelect_env (val) {
                this.data_select_env = val;
            },
            // 后端服务方式数据更新
            updateSelect_service (val) {
                this.data_select_service = val;
            },
            // 累加载数据更新
            updateSelect_classLoader (val) {
                this.data_select_classLoader = val;
            },
            updateSelect_sp_modal (val) {
                this.form_backend.sp= val;
            },
            updateSelect_classLoader_modal (val) {
                this.form_backend.classLoaderCode = val;
            },
            updateSelect_env_modal (val) {
                this.form_backend.envCode= val;
            },
            updateSelect_serviceMethod_modal (val) {
                this.form_backend.serviceMethodCode= val;
            },
            updateSelect_opt_type_modal (val) {
                this.form_classLoader.opt_type= val;
            },
            // 后端应用新增函数
            backendApp_add () {
                this.create_orEdit = true;
                this.formData_reset();
                this.modal_create_backend = true;
            },
            // 批量初始化
            batch_init () {
                this.init_loading =true;
                api.yop_backend_app_batch_init().then(
                    (response) => {
                        if (response.status === 'success') {
                            if(response.data){
                                let result = response.data.result
                                if(result && result.length > 0 ){
                                    let content = '';
                                    result.forEach(
                                        item =>{
                                            content = content + '<br/>'+item
                                        }
                                    )
                                    this.$ypMsg.notice_success(this,'后端应用批量初始化成功：'+content);
                                }else{

                                    this.$ypMsg.notice_success(this,'后端应用批量初始化成功');
                                }
                            }else{
                                this.$ypMsg.notice_success(this,'后端应用批量初始化成功');
                            }
                            this.init_loading =false;
                            this.search_interface();
                        } else {
                            this.$ypMsg.notice_error(this,'错误：后端应用匹配初始化失败，请重试',response.message,response.solution);
                            this.init_loading =false;
                        }
                    }
                );
            },
            // 页面刷新
            pageRefresh (val) {
                this.show_loading_bakcendAppList = true;
                let params = {
                    spCode: this.data_select_sp,
                    backendCode: this.data_backend_code.trim(),
                    backendName: this.data_backend_name.trim(),
                    deployMode: this.data_select_env,
                    classLoadMode: this.data_select_classLoader,
                    rpcMode:this.data_select_service,
                    _pageNo: 1,
                    _pageSize: 10
                };
                if (val) {
                    params._pageNo = val;
                }
                util.paramFormat(params);
                api.yop_backend_app_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            this.pageTotal = response.data.data.result.totalPageNum * 10;
                            this.show_loading_bakcendAppList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_bakcendAppList = false;
                        }
                    }
                );
            },
            // 编辑按钮
            backend_modify (code) {
                this.create_orEdit = false;
                this.formData_reset();
                this.modal_create_backend = true;
                this.show_loading_createBackend = true;
                api.yop_backend_app_detail({backendCode:code}).then(
                    (response)=>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.detail_format(result)
                            this.show_loading_createBackend = false;
                        }else{
                            this.$ypMsg.notice_error(this,'后端应用详细信息获取失败,请刷新重试',response.data.message,response.data.solution);
                            this.modal_create_backend= false;
                            this.show_loading_createBackend = false;
                        }
                    }
                )
            },
            // 详细信息处理函数
            detail_format (result) {
                this.form_backend.sp = util.empty_handler(result.spCode);
                this.form_backend.spName = util.empty_handler(this.data_name_handler(result.spCode, 'serviceSupplier'));
                this.form_backend.backendAppCode = util.empty_handler(result.backendCode);
                this.form_backend.backendAppName = util.empty_handler(result.backendName);
                this.form_backend.env = util.empty_handler(this.data_name_handler(result.deployMode, 'ba_env')),
                this.form_backend.envCode = util.empty_handler(result.deployMode);
                this.form_backend.classLoader = util.empty_handler(this.data_name_handler(result.classLoadMode, 'ba_classLoader')),
                this.form_backend.classLoaderCode = util.empty_handler(result.classLoadMode);
                this.form_backend.serviceMethod = util.empty_handler(this.data_name_handler(result.rpcMode, 'ba_service'));
                this.form_backend.serviceMethodCode = util.empty_handler(result.rpcMode);
                this.form_backend.description = this.empty_check(result.description);
                this.form_backend.version = result.version;
            },
            // 空校验
            empty_check (val) {
              if(val){
                  return val;
              }else{
                  return ''
              }
            },
            // 删除按钮
            backend_delete (code) {
                this.$Modal.confirm({
                    title: '删除后端应用',
                    content: '确定删除该后端应用吗？',
                    'ok-text': '删除',
                    onOk: () => {
                        api.yop_backend_app_delete({backendCode: code}).then(
                            (response) => {
                                if (response.status === 'success') {
                                    this.$ypMsg.notice_success(this,'删除成功');
                                    this.search_interface();
                                } else {
                                    this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                }
                            }
                        );
                    }
                });
            },
            // 重建classloader
            rebuild_classLoader (code,disabled) {
                this.currentCode = code;
                this.classloader_opt_type_init();
                this.modal_classLoader = true;
            },
            // 重建classloader提交按钮
            classLoader_submit (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        this.show_loading_rebuild =true;
                        let param = {
                            backendCode : this.currentCode,
                            rebuildType : this.form_classLoader.opt_type,
                            reason : this.form_classLoader.classLoader
                        }
                        api.yop_backend_app_rebuild(param).then(
                            (response) =>{
                                //需要填写
                                if(response){
                                    if(response.status === 'success'){
                                        this.$ypMsg.notice_success(this,'所有服务器将在5分钟内完成重新加载过程，请耐心等待');
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    }
                                }else{
                                    this.$ypMsg.notice_warning(this,'当前应用正在重新加载中，请勿重复触发');
                                }
                                this.show_loading_rebuild =false;
                                this.modal_classLoader = false;

                            }
                        )
                        this.form_classLoader.classLoader = '';
                        this.$refs.form_classLoader.resetFields();
                        this.classloader_opt_type_init();

                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },

            // 重建classloader取消按钮
            classLoader_cancel () {
                this.form_classLoader.classLoader = '';
                this.$refs.form_classLoader.resetFields();
                this.classloader_opt_type_init();
                this.modal_classLoader = false;
            },

            // 导出API
            exportApi(row){
                let param = {
                    backendCode : row.backendAppCode,
                };
                api.yop_backend_app_exportapi(param,'json').then(
                    (response) => {
                        if(response.data.status != "error"){
                            this.$ypMsg.notice_success(this,'导出成功');
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                    }
                );
            },
            // 操作类型初始化
            classloader_opt_type_init(){
                if(this.$store.state.select.opt_type.data && this.$store.state.select.opt_type.data.length>0){
                    this.$refs.select_opt_type_m.resetSelected();
                    this.$refs.select_opt_type_m.default_select_set(this.$store.state.select.opt_type.data[0].value);
                }else{
                    this.$refs.select_opt_type_m.resetSelected();
                    this.form_classLoader.opt_type = '';
                }
            },
            // 列表数据重置
            formData_reset () {
                this.$refs.select_sp_m.resetSelected();
                this.$refs.select_env_m.resetSelected();
                this.$refs.select_classLoader_m.resetSelected();
                this.$refs.select_serviceMethod_m.resetSelected();
                this.form_backend.sp = '';
                this.form_backend.envCode = '';
                this.form_backend.classLoaderCode = '';
                this.form_backend.serviceMethodCode = '';
                this.form_backend.backendAppCode = '';
                this.form_backend.backendAppName = '';
                this.form_backend.description = '';
                this.formBackend_reset();

            },
            // 表单状态还原
            formBackend_reset(){
                this.$refs.form_backend.resetFields();
            },
        },
        mounted () {
            this.init();
        },
        // beforeRouteLeave (to, from, next) {
        //     this.$store.state.current_apiGroup ='';
        //     this.$destroy();
        //     next();
        // },
    };
</script>

<style scoped>

</style>
