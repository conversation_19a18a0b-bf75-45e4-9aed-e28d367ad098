<style scoped>
    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;
    }

    .el-checkbox__inner {
        width: 12px !important;
        height: 12px !important;
    }

    .custom-tree-node {
        font-size: 12px;
    }

    .yop-explain-120 {
        font-size: 12px;
        color: grey;
        padding-left: 120px;
        line-height: 30px;
    }
    .red{
        color:red
    }
    .green{
        color:green
    }
    .icon-preview-edit {
    width: 35px;
    height: 35px;
    font-size: 35px;
    }
    .yp-margin-left-15{
        margin-left: 15px;
        vertical-align: text-bottom;
    }
    .text-center{
        text-align: center;
    }
    .over-hidden{
        overflow: hidden;
    }
    .over-scroll-none{
        -ms-overflow-style: none; 
    }
    .over-scroll-none::-webkit-scrollbar { width: 0 !important }
    /*@import '../API_Management_Open/api_Mangement_Open.less';*/
    @import '../../../styles/common.less';
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false" dis-hover>
            <Row>
                <Col span="24" class="margin-bottom-10">
                <Button id="btn_resList_6" type="primary" class="margin-right-20" v-url="{url:'/rest/acl/resource/create'}" @click="add_top_resource()">新增顶级资源</Button>
                <Button id="btn_resList_7" type="primary" class="margin-right-20" v-url="{url:'/rest/acl/resource/delete'}" @click="batch_delete()">批量删除</Button>
                <Button id="btn_resList_8" type="primary" class="margin-right-20" v-url="{url:'/rest/acl/resource/enable'}" @click="batch_enable()">批量启用</Button>
                <Button id="btn_resList_9" type="primary" class="margin-right-20" v-url="{url:'/rest/acl/resource/disable'}" @click="batch_disable()">批量禁用</Button>
                </Col>
                <Col span="10">

                <Card id="card_resList_1" dis-hover>
                    <p slot="title">
                        资源管理
                    </p>
                    <Input size="small" class="margin-bottom-10" placeholder="资源名称搜索" v-model="filterText"></Input>
                    <el-tree
                            draggable
                            ref="tree"
                            :data="data_tree"
                            show-checkbox
                            node-key="id"
                            :default-expand-all="false"
                            :filter-node-method="filterNode"
                            :expand-on-click-node="false"
                            :allow-drop="allowDrop"
                            @node-drop="handleDrop"
                            @node-click="nodeClick"
                            :highlight-current="true"
                    >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
        <span><Button v-show="node.data.enableStatus" style="color: green" type="text" size="small" :icon="node.icon"></Button>
        <Button v-show="!node.data.enableStatus" style="color: red" type="text" size="small" :icon="node.icon"></Button>{{node.label}}</span>
        <span>
          <Button
                  :id="'btn_resList_1_'+data.id"
                  type="text"
                  size="small"
                  shape="circle"
                  @click="tree_Create(data,$event)"
                  v-url="{url:'/rest/acl/resource/create'}"
                  v-show="data.depth < 3 && data.type !== 'BUTTON'">
            新增
          </Button>
          <Button
                  :id="'btn_resList_2_'+data.id"
                  type="text"
                  size="small"
                  shape="circle"
                  v-url="{url:'/rest/acl/resource/delete'}"
                  @click="tree_Delete(node, data,$event)">
            删除
          </Button>
            <Button
                    :id="'btn_resList_3_'+data.id"
                    type="text"
                    size="small"
                    shape="circle"
                    v-show="data.option === '启用'"
                    v-url="{url:'/rest/acl/resource/enable'}"
                    @click="tree_Change(node, data,$event)">
            <!--启用-->
                {{data.option}}
          </Button>
            <Button
                    :id="'btn_resList_4_'+data.id"
                    type="text"
                    size="small"
                    shape="circle"
                    v-show="data.option === '禁用'"
                    v-url="{url:'/rest/acl/resource/disable'}"
                    @click="tree_Change(node, data,$event)">
            <!--启用-->
                {{data.option}}
          </Button>
            <Button
                    :id="'btn_resList_5_'+data.id"
                    type="text"
                    size="small"
                    shape="circle"
                    v-url="{url:'/rest/acl/resource/edit'}"
                    @click="tree_Edit(node, data,$event)">
            编辑
          </Button>
          <Button 
            :id="'btn_resList_5_'+data.id"
            type="text"
            size="small"
            shape="circle"
            v-show="data.audit"
            v-url="{url:'/rest/acl/audit/detail'}"
            @click="treeCheck(node, data,$event)">
            审核设置
          </Button>
        </span>
      </span>

                    </el-tree>
                </Card>
                <loading :show="show_loading_tree"></loading>
                </Col>
                <Col  span="14" v-show="detail_resource">

                <Card id="card_resList_2" class="margin-left-16" dis-hover>
                    <p v-show="!top_resource" slot="title">
                        查看资源配置
                    </p>
                    <p v-show="top_resource" slot="title">
                        查看顶级资源配置
                    </p>
                    <a slot="extra" @click.prevent="detail_close">
                        <Icon type="close"></Icon>
                    </a>
                    <Form ref="form_detail" :model="form_detail" :label-width="120">
                        <FormItem label="上一级资源：" v-show="!top_resource">
                            {{form_detail.PResource}}
                        </FormItem>
                        <FormItem label="资源名称：">
                            {{form_detail.name}}
                        </FormItem>
                        <FormItem label="资源类型：">
                            {{form_detail.type}}
                        </FormItem>
                        <FormItem label="资源图标：" v-show="form_detail.type === '菜单'">
                            <span v-show="!form_detail.icon" style="vertical-align: text-bottom;">暂无</span>
                            <Icon :type="form_detail.icon" v-show="form_detail.icon" class="icon-preview-edit"></Icon>
                        </FormItem>
                        <FormItem label="资源URL：" v-show="!top_resource">
                            {{form_detail.url}}
                        </FormItem>
                        <FormItem label="资源权限：" v-show="!top_resource">
                            {{form_detail.permission}}
                        </FormItem>
                        <FormItem label="权限规则：" v-show="!top_resource">
                            {{form_detail.per_rule}}
                        </FormItem>
                        <FormItem label="状态：">
                            {{form_detail.status}}
                        </FormItem>
                        <FormItem label="描述：">
                            {{form_detail.description}}
                        </FormItem>
                    </Form>
                    <div style="text-align: center">
                        <Button type="ghost" @click="detail_close">关闭</Button>
                    </div>
                </Card>
                <loading :show="show_loading_preview"></loading>
                </Col>

                <Col span="14" v-show="add_resource">

                <Card id="card_resList_3" class="margin-left-16" dis-hover>
                    <p v-show="!top_resource && create_orEdit" slot="title">
                        新增资源配置
                    </p>
                    <p v-show="top_resource && create_orEdit" slot="title">
                        新增顶级资源配置
                    </p>
                    <p v-show="!create_orEdit" slot="title">
                        编辑资源配置
                    </p>
                    <a slot="extra" @click.prevent="add_close">
                        <Icon type="close"></Icon>
                    </a>
                    <Form ref="form_detail" :model="form_detail" :rules="rule_detail" :label-width="120">
                        <FormItem label="上一级资源：" v-show="!top_resource">
                            {{form_detail.PResource}}
                        </FormItem>
                        <FormItem label="资源名称：" prop="name">
                            <Input id="input_resList_1" type="text" size="small" v-model="form_detail.name"
                                   style="width:85%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">支持数字、大小写字母，汉字，最大支持输入64个字符</p>
                        <FormItem label="资源类型：" prop="type" v-show="create_orEdit && !top_resource">
                            <!--<FormItem label="资源类型：" prop="type" >-->
                            <RadioGroup v-model="form_detail.type">
                                <Radio id="rbtn_resList_1" label="菜单"></Radio>
                                <Radio id="rbtn_resList_2" label="按钮"></Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="资源类型：" v-show="!(create_orEdit && !top_resource)">
                            {{form_detail.type}}
                        </FormItem>
                        <FormItem label="资源图标：" v-show="form_detail.type === '菜单'">
                            <span v-show="!form_detail.icon" style="vertical-align: text-bottom;">暂无</span>
                            <Icon :type="form_detail.icon" v-show="form_detail.icon" class="icon-preview-edit"></Icon><span class="yp-margin-left-15"><Button type="primary" size="small" icon="edit" @click="icon_select"></Button></span>
                        </FormItem>
                        <FormItem label="资源URL：" v-show="!top_resource" prop="url">
                            <!--<FormItem label="资源URL："  prop="url">-->
                            <Input id="input_resList_2" type="text" size="small" v-model="form_detail.url" style="width:85%" @on-blur="per_rule_refresh"></Input>
                        </FormItem>
                        <p  v-show="!top_resource" class="yop-explain-120">以/(正斜杠)开头，支持输入小写字母、数字、特殊字符，最大输入64个字符</p>
                        <FormItem label="资源权限：" v-show="!top_resource" prop="permission">
                            <!--<FormItem label="资源权限："  prop="permission">-->
                            <RadioGroup v-model="form_detail.permission">
                                <Radio id="rbtn_resList_3" label="全部可见"></Radio>
                                <Radio id="rbtn_resList_4" label="YOP可见"></Radio>
                                <Radio id="rbtn_resList_5" label="SP可见"></Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="权限规则：" v-show="!top_resource" prop="permId">
                            <Select id="select_resList_1" ref="per_rule" size="small"  v-model="form_detail.permId" filterable clearable placeholder="选择权限规则" style="width:85%">
                                <Option v-for="item in data_per_rule_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="状态：" prop="status" v-show="create_orEdit">
                            <!--<FormItem label="状态：" prop="status">-->
                            <RadioGroup v-model="form_detail.status">
                                <Radio id="rbtn_resList_6" label="启用"></Radio>
                                <Radio id="rbtn_resList_7" label="禁用"></Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="描述：" prop="description">
                            <Input id="input_resList_3" type="textarea" size="small" v-model="form_detail.description"
                                   style="width:85%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">最大支持输入100字</p>

                    </Form>
                    <div style="text-align: center">
                        <Button id="btn_resList_10" type="primary" class="margin-right-10" @click="add_submit('form_detail')">确定</Button>
                        <Button id="btn_resList_11" type="ghost" @click="add_close">关闭</Button>
                    </div>
                </Card>
                <loading :show="show_loading_edit"></loading>
                </Col>
                <!--<Button @click="nodeChange">sdad</Button>-->
                <Col span="14" v-if="applyCheck">
                    <!-- 新增审核 -->
                    <Card id="card_resList_4" class="over-hidden margin-left-16">
                        <p slot="title">审核设置</p>
                        <a slot="extra" @click="applyCheck = false">
                            <Icon type="close"></Icon>
                        </a>
                        <!-- 一级之上添加审核 -->
                        <Button v-if="!willAddFlag" @click="addCheck('',0)" size="small" shape="circle" type="text">
                            <i class="iconfont ">&#xe6f1;</i>
                        </Button>
                        <!-- 头部展示部分 -->
                        <Row v-if="ifShowCheckBlocks">
                            <div v-for="(items, index) in justShowCheck" :key="index">
                                <Row>
                                    <Col span="4">
                                        <h3> {{index+1}} 级审核</h3>
                                    </Col>
                                    <Col span="12">
                                        <span v-for="(item, index) in items.roles" :key="'check-'+index">
                                            {{item}}
                                        </span>
                                        <span v-for="(item, index) in items.operators" :key="'operators-'+index">
                                            {{item}}
                                        </span>
                                    </Col>
                                    <Col span="6">
                                        <!-- 编辑 -->
                                        <Button @click="editChecked(items,index)" size="small" shape="circle" type="text">
                                            <i class="iconfont">&#xe6c7;</i>
                                        </Button>
                                        <!-- 删除 -->
                                        <Button @click="deleteChecked(items,index)" size="small" shape="circle" type="text">
                                            <i class="iconfont">&#xe6a6;</i>
                                        </Button>
                                        <!-- 添加审核 -->
                                        <Button @click="addCheck(items,index)" size="small" shape="circle" type="text">
                                            <i class="iconfont ">&#xe6f1;</i>
                                        </Button>
                                    </Col>
                                </Row>
                            </div>
                            
                        </Row>
                        <!-- 底部添加部分 -->
                        <Card v-if="willAddFlag">
                            <a slot="extra" @click="closeCurrentCheck">
                                <Icon type="close"></Icon>
                            </a>
                            <h2 class="margin-bottom-10">{{addEditFlag ? currentAddIndex+2 : editCheckTitle}}级审核</h2>
                            <Col span="12">
                                <h3 class="text-center margin-bottom-10">角色</h3>
                                <Form ref="form_batch_auth" :model="rolePersonForm" :label-width="90">
                                    <FormItem label="审核角色：">
                                        <!-- :filter-method="myFilter" 自定义过滤器 -->
                                        <el-select
                                                style="width:90%"
                                                multiple
                                                v-model="rolePersonForm.checkedRoles"
                                                filterable
                                                
                                                clearable
                                                size="mini"
                                                default-first-option
                                                placeholder="角色名">
                                            <el-option
                                                v-for="item in checkRoleList"
                                                :key="item.id"
                                                :label="item.label"
                                                :value="item.value">
                                            </el-option>
                                        </el-select>
                                        <span><Button id="modal_acl_role_btn_4" type="primary" size="small" @click="addRolePersonel('role')">添加</Button></span>
                                    </FormItem>
                                    <FormItem label="确定角色：">
                                        <Card dis-hover  style="height:200px; width: 90%">
                                            <Scroll class="over-scroll-none" height="170">
                                                <Tag class="over-scroll-none" v-for="item in rolePersonForm.roleList" :key="item" :name="item" closable @on-close="deleteRole('role',item)">{{item}}</Tag>
                                            </Scroll>
                                        </Card>
                                    </FormItem>
                                </Form>
                            </Col>
                            <Col span="12">
                                <h3 class="text-center margin-bottom-10">人员</h3>
                                <Form ref="form_batch_auth" :model="rolePersonForm" :label-width="90">
                                    <FormItem label="审核人员：">
                                        <Input v-model="rolePersonForm.operators" placeholder="审核人员"></Input>
                                        <!-- <el-select
                                                style="width:90%"
                                                v-model="rolePersonForm.operators"
                                                filterable
                                                clearable
                                                multiple
                                                size="mini"
                                                allow-create
                                                default-first-option
                                                placeholder="审核人员">
                                            <el-option
                                                v-for="item in checkPersonList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value">
                                            </el-option>
                                        </el-select> -->
                                        <span><Button id="modal_acl_role_btn_4" type="primary" size="small" @click="addRolePersonel('personal')">添加</Button></span>
                                    </FormItem>
                                    <FormItem label="确定人员：">
                                        <Card dis-hover  style="height:200px; width: 90%">
                                            <Scroll height="170">
                                                <Tag v-for="item in rolePersonForm.personalList" :key="item" :name="item" closable @on-close="deleteRole('personal',item)">{{item}}</Tag>
                                            </Scroll>
                                        </Card>
                                    </FormItem>
                                </Form>
                            </Col>
                            <div class="text-center">
                                <Button :disabled="disableSave" @click="saveCurrentCheck" type="primary">保存</Button>
                            </div>
                        </Card>
                        <div v-if="!willAddFlag" class="text-center margin-top-10">
                            <Button @click="commitCheck" type="primary"> 提交</Button>
                            <Button @click="cancelCheck" type="primary">撤销</Button>
                        </div>
                        
                    </Card>
                    <loading :show="applyCheckLoading"></loading>
                </Col>
            </Row>
            <Modal id="modal_resList_1" v-model="modal_enable" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black">启用资源配置</span>
                </p>
                <div v-show="single_orBatch">
                    <p>禁用原因：</p>
                    <p >{{disable_reason_single}}</p>
                    <br/>
                    <p>请确定该资源问题已经修复后，再启用！</p>
                </div>
                <div v-show="!single_orBatch">
                    <p>禁用原因：</p>
                    <p v-for="item in disable_reason_batch"><Tag color="#C1CDCD">{{item.name}}</Tag>  {{item.reason}}</p>
                    <!--<p v-for="item in disable_reason_batch"><span style="font-weight: b  old">{{item.name}}</span> : {{item.reason}}</p>-->
                    <br/>
                    <p>请确定以上资源问题已经修复，再启用相关资源！</p>
                </div>
                <div slot="footer">
                    <Button id="btn_resList_12" type="primary" @click="enable_submit">确认</Button>
                    <Button id="btn_resList_13" type="ghost" @click="enable_cancel">取消</Button>
                </div>
                <loading :show="show_loading_enable"></loading>
            </Modal>
            <Modal id="modal_resList_2" v-model="modal_disable" :closable="false" width="700">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black">禁用资源配置</span>
                </p>
                <div >
                    <Form ref="form_disable" :label-width="120">
                        <FormItem label="禁用原因：">
                            <Select id="select_resList_2" size="small" style="width:90%" v-model="data_select_disabledReason"  placeholder="请选择接口类型">
                                <Option v-for="item in data_disabledReason_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem v-show="data_select_disabledReason === '其他'" label="其他原因：">
                            <Input id="input_resList_4" type="textarea" size="small" v-model="reason_other"
                                   style="width:90%"></Input>
                        </FormItem>
                    </Form>
                </div>
                <div slot="footer">
                    <Button id="btn_resList_14" type="primary" @click="disable_submit">确认</Button>
                    <Button id="btn_resList_15" type="ghost" @click="disable_cancel">取消</Button>
                </div>
            </Modal>
            <iconSelector ref="iconSelector" @icon_update="icon_update"></iconSelector>
        </Card>
    </div>
</template>

<script>
    import commonSelect from '../../common-components/select-components/selectCommon';
    import loading from '../../my-components/loading/loading'
    import api from '../../../api/api'
    import util from '../../../libs/util'
    import iconSelector from '../../common-components/icon-components/icon-seletor'
    import qs from 'qs';
    export default {
        name: 'resource',
        watch: {
            filterText (val) {
                this.$refs.tree.filter(val);
            },
            justShowCheck(val){
                if(val.length == 0){
                    this.currentAddIndex = -1;
                    this.willAddFlag = true;
                }
            }
        },
        components:{
            commonSelect,
            loading,
            iconSelector
        },
        data () {
            // 资源名称验证
            const validate_resourceName = (rule, value, callback) => {
                if(util.formatCheck3(value)){
                    callback(new Error('格式不正确'));
                }else if(util.getLength(value) > 64){
                    callback(new Error('长度不能大于64'));
                }else{
                    callback();
                }
            };
            // 资源url验证
            const validate_resourceUrl = (rule, value, callback) => {
                if(util.formatCheck4(value)){
                    callback(new Error('格式只支持以/开头，支持输入小写字母、数字、字符(/.-)'));
                }else if(util.getLength(value) > 64){
                    callback(new Error('长度不能大于64'));
                }else{
                    callback();
                }
            };
            // 资源描述验证
            const validate_resourceDes = (rule, value, callback) => {
                if(value){
                    if(util.getLength(value) > 100){
                        callback(new Error('长度不能大于100'));
                    }else{
                        callback();
                    }
                }else{
                    callback();
                }
            };
            return {
                // 权限规则数据绑定
                data_per_rule_List : [],
                // 当前是创建和编辑 创建
                create_orEdit : true,
                //其他原因数据绑定
                reason_other : '',
                // 当前禁用资源原因
                data_select_disabledReason : '',
                // 资源下拉框列表
                data_disabledReason_List : [
                    {
                        label: '该资源正在进行升级，暂停使用给您带来不便望请谅解！',
                        value: '该资源正在进行升级，暂停使用给您带来不便望请谅解！'
                    },
                    {
                        label: '该资源已迁移到旧版运营后台。暂停使用。给您带来不变望请谅解！',
                        value: '该资源已迁移到旧版运营后台。暂停使用。给您带来不变望请谅解！'
                    },
                    {
                        label: '其他',
                        value: '其他'
                    }
                ],
                // 单个启用还是批量 单个：true 批量：false
                single_orBatch : true,
                // 单个禁用原因
                disable_reason_single : '该资源正在进行升级，暂停使用。给您带来不便望请谅解！',
                // 批量禁用原因
                disable_reason_batch : [{
                    name: '资源一',
                    reason: '该资源正在进行升级，暂停使用。给您带来不便望请谅解！'
                }, {
                    name: '资源二',
                    reason: '该资源已迁移到旧版运营后台。暂停使用。给您带来不便望请谅解！'
                }],
                // 启用资源窗口配置
                modal_enable: false,
                // 禁用资源窗口配置
                modal_disable : false,
                // 加载树loading
                show_loading_tree : false,
                // 启用资源配置loading
                show_loading_enable : false,
                // 编辑资源loading
                show_loading_edit : false,
                // 申请审核loading
                applyCheckLoading: false,
                // 查看资源loading
                show_loading_preview : false,
                // 查看页面显示
                detail_resource : false,
                // 新增页面显示
                add_resource : false,
                // 编辑还是添加
                addEditFlag: true,
                // 是否禁止添加
                disableSave: true,
                // 申请审核展示
                applyCheck: false,
                // 审核标题
                checkTitle: 1,
                // 编辑几级审核标题
                editCheckTitle: 1,
                // 是否进行添加 角色人员开关
                willAddFlag: true,
                // 展示已经添加过的审核 
                ifShowCheckBlocks: false,
                /* 审核 */
                // 当前添加的 级别
                currentAddIndex: 1,
                // 筛选文字
                filterText : '',
                code_md5 : '',
                data_tree:[], 
                defaultProps: {
                    children: 'children',
                    label: 'label'
                },
                // 当前选择的权限规则
                data_select_permission_rule : '',
                // 是否为顶级资源
                top_resource: false,
                form_detail: {
                    id: '',
                    pid: '',
                    PResource: '',
                    name: '',
                    type: '菜单',
                    url: '',
                    permission: 'YOP可见',
                    permId: '',
                    per_rule: '',
                    status: '启用',
                    description: '',
                    icon : ''
                },
                // 人员角色表单
                rolePersonForm: {
                    // 当前节点id
                    resourceId: '',
                    role: '',
                    confirmRole: '',
                    personal: '',
                    // 待添加人员
                    operators: '',
                    // 角色列表
                    roleList: [],
                    // 人员列表
                    personalList: [],
                    // 已选择角色列表
                    checkedRoles: [],
                    // 已选择人员列表
                    checkedOperators: []
                },
                // 审核角色列表
                checkRoleList: [],
                // 搜索时 用
                checkRoleListCopy: [],
                // 审核人员列表暂无
                checkPersonList:[],
                // 添加多级审核列表
                applyCheckList: [],
                // 只是展示列表的内容
                justShowCheck: [],
                // 表单验证规则数据绑定
                rule_detail: {
                    name: [
                        {required: true, message: '资源名称不能为空', trigger: 'blur'},
                        { validator: validate_resourceName, trigger: 'blur' }
                    ],
                    type: [
                        {required: true, message: '资源类型不能为空'},
                    ],
                    url: [
                        {required: true, message: '资源url不能为空', trigger: 'blur'},
                        { validator: validate_resourceUrl, trigger: 'blur' }
                    ],
                    permission: [
                        {required: true},
                    ],
                    permId: [
                        {required: true, message: '权限规则不能为空'},
                    ],
                    status: [
                        {required: true, message: '资源状态不能为空'},
                    ],
                    description : [
                        {validator: validate_resourceDes, trigger : 'blur'}
                    ]
                },
                // 当前节点数据
                current_nodeData : Object,
                // 当前节点数据
                current_dataTemp : Object,
                // 选中节点状态合集
                current_dataStatus : []
            };
        },
        methods: {
            // 树的添加
            tree_Create (data, e) {
                this.create_orEdit =true;
                this.top_resource = false;
                this.add_resource = true;
                this.detail_resource = false;
                this.applyCheck = false;
                this.add_resource_data_init(data.label,data.id);
                e.stopPropagation();
                this.current_dataTemp = data;
                // this.append(data);
            },
            // 树的删除
            tree_Delete (node, data, e) {
                e.stopPropagation();
                this.$Modal.confirm({
                    title: '删除资源配置',
                    content: "<p style='color:red'>如果删除父级菜单，则关联子级菜单一并删除。</p><p>确定删除资源配置？</p>",
                    'ok-text':'确认',
                    onOk: () =>{
                        // 删除操作
                        let param = {
                            ids : [data.id]
                        }
                        this.delete_node_handler(node, data,param);
                    }
                });
                // this.remove(node, data);
            },
            // 删除执行函数
            delete_node_handler (node,data,param) {
                api.yop_acl_resource_delete(param).then(
                    (response) =>{
                        console.log(response);
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'资源删除成功');
                            this.remove(node, data);
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                    }
                )
            },
            // 树的启用禁用
            tree_Change (node, data, e) {
                e.stopPropagation();
                if (data.option === '启用') {
                    this.disable_reasonList_get([data.id]);
                    this.modal_enable = true;
                    this.single_orBatch = true;
                    this.enable_status(data);
                    // data.status = '禁用';
                } else {
                    this.data_select_disabledReason = '';
                    this.modal_disable = true;
                    this.single_orBatch = true;
                    // data.status = '启用';
                    this.enable_status(data);

                }
            },
            // 获取禁用原因
            disable_reasonList_get (ids) {
                let param = {
                    ids : ids
                }
                let transfer = {
                    params: param,
                    paramsSerializer: function(params) {
                        return qs.stringify(params, {arrayFormat: 'repeat'})
                    }
                }
                api.yop_acl_resource_disableReason_search(transfer).then(
                    (response) =>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result
                            this.disable_reason_single ='';
                            this.disable_reason_batch = [];
                            if(result.length === 1){
                                this.disable_reason_single =result[0].disableReason
                            }else if(result.length > 1) {
                                result.forEach(
                                    item =>{
                                        this.disable_reason_batch.push({
                                            name : item.name,
                                            reason : item.disableReason
                                        })
                                    }
                                )
                            }
                        }else{
                            this.$ypMsg.notice_error(this,'禁用原因获取失败',response.data.message,response.data.solution);
                            this.modal_enable= false;
                        }
                    }
                )
            },
            // 启用禁用状态数据获取
            enable_status (val) {
                this.current_nodeData = val;
            },
            // 自定义element下拉框搜索
            myFilter(val){
                // 节流
                let runFlag = true;
                if(!runFlag){
                    return false 
                }
                // 工作时间，执行函数并且在间隔期内把状态位设为无效
                runFlag = false
                setTimeout(() => {
                    console.log(val)
                    // 真正筛选部分 start
                    this.value = val;
                    if (val) { //val存在
                        this.checkRoleList = this.checkRoleListCopy.filter((item) => {
                            if (!!~item.label.indexOf(val) ) {
                                return true
                            }
                        })
                    } else { //val为空时，还原数组
                        this.checkRoleList = this.checkRoleListCopy;
                    }
                    // 真正筛选部分 end
                    runFlag = true;
                }, 1000)

                
            },
            // 申请审核
            treeCheck(node, data, e){
                e.stopPropagation();
                // 设置当前显示高亮
                this.$nextTick(() => {
                    this.$refs.tree.setCurrentKey(node); 
                });
                this.rolePersonForm.resourceId = data.id;
                this.detail_resource =false;
                this.create_orEdit = false;
                this.add_resource = false;
                this.top_resource = false;

                // 展示右边编辑审核弹框
                this.applyCheck = true;
                // 不显示添加编辑审核
                this.willAddFlag = false;
                // 获取审核表单详情
                let detailParams = {
                    resourceId: data.id
                }
                api.yop_acl_audit_detail(detailParams).then(
                    (response) => {
                        let status = response.data.status
                        if(status === 'success'){
                            this.ifShowCheckBlocks = true;
                            this.justShowCheck = [];
                            this.justShowCheck = response.data.data.result.auditStage ? response.data.data.result.auditStage : []
                            // 清洗下数据
                            for (let index = 0; index < this.justShowCheck.length; index++) {
                                const element = this.justShowCheck[index];
                                if(element.operators == null){
                                    element.operators = []
                                }
                            }
                            
                            // 没有已经编辑过得展示 添加一级审核
                            if(this.justShowCheck.length == 0){
                                this.ifShowCheckBlocks = false;
                                // 显示添加编辑审核
                                this.willAddFlag = true;
                            }

                        }else{
                            this.ifShowCheckBlocks = false;
                            this.$ypMsg.notice_error(this,'审核列表信息获取失败,请刷新重试',response.data.errMsg,response.data.solution);
                        }
                        
                    }
                );
            },
            // 获取用户列表 角色列表
            getRoleList(){
                // 获取用户列表 角色列表
                let params = {}
                api.yop_acl_role_user_common_list(params).then(
                    (response) => {
                        let status = response.data.status
                        if(status === 'success'){
                            let resRoleList = response.data.data.result ? response.data.data.result : [];
                            for (let index = 0; index < resRoleList.length; index++) {
                                const element = resRoleList[index];
                                this.checkRoleList.push({
                                    label: element.code,
                                    value: element.code
                                })
                                this.checkRoleListCopy.push({
                                    label: element.code,
                                    value: element.code
                                })
                            }
                        }else{
                            this.$ypMsg.notice_error(this,'用户列表信息获取失败,请刷新重试',response.data.message,response.data.solution);
                        }
                        
                    }
                );
            },
            // 点击提交 创建/更新审核
            commitCheck(){
                if(this.justShowCheck.length == 0){
                    this.$Message.warning('请填写内容再提交');
                    return false;
                }
                let params = {
                    resourceId: this.rolePersonForm.resourceId,
                    auditStage: this.justShowCheck
                }
                api.yop_acl_audit_update(params).then(
                    (response) => {
                        let status = response.status
                        if(status === 'success'){
                            this.$ypMsg.notice_success(this,'提交成功');
                            
                            // 清空上次选项
                            this.rolePersonForm.personalList = [];
                            this.rolePersonForm.roleList = [];
                            this.rolePersonForm.checkedRoles = [];
                            this.rolePersonForm.checkedOperators = []
                        }else{
                            this.$ypMsg.notice_error(this,'创建审核失败',response.message,response.solution);
                        }
                        
                    }
                )
            },
            // 撤销审核
            cancelCheck(){
                this.$Modal.confirm({
                    title: '确认撤销',
                    content: '<p>确认撤销以上所有审核设置？</p>',
                    onOk: () => {
                        this.confirmCancelCheck()
                    },
                    onCancel: () => {
                        this.$Message.info('取消成功');
                    }
                });
                
            },
            // 确认撤销审核
            confirmCancelCheck(){
                let params = {
                    resourceId: this.rolePersonForm.resourceId
                }
                api.yop_acl_audit_record_delete(params).then(
                    (response) => {
                        let status = response.status
                        if(status === 'success'){
                            this.$ypMsg.notice_success(this,'撤销成功');
                            this.applyCheck = false;
                            this.justShowCheck = []
                            // 清空所有选择
                            this.rolePersonForm.operators = ''
                            this.rolePersonForm.personalList = [];
                            this.rolePersonForm.roleList = [];
                            this.rolePersonForm.checkedRoles = [];
                            this.rolePersonForm.checkedOperators = []
                        }else{
                            this.$ypMsg.notice_error(this,'撤销失败',response.data.errMsg);
                        }
                        
                    }
                )
            },
            // 角色名单删除
            deleteRole (type,name) {
                if(type === 'role'){
                    let index = this.rolePersonForm.roleList.indexOf(name);
                    this.rolePersonForm.roleList.splice(index, 1);
                }else{
                    let index = this.rolePersonForm.personalList.indexOf(name);
                    this.rolePersonForm.personalList.splice(index, 1);
                }
                
            },
            // 审核 添加按钮 角色/人员
            addRolePersonel(type){
                if(type === 'role'){
                    // this.rolePersonForm.roleList.push(this.rolePersonForm.role)
                    this.rolePersonForm.roleList = this.rolePersonForm.checkedRoles
                    this.disableSave = false;
                }else{
                    // 人员名字校验
                    let params = {
                        name: this.rolePersonForm.operators
                    }
                    api.yop_acl_check_protal_user(params).then(
                        (response) => {
                            let status = response.data.status
                            if(status === 'success'){
                                if(response.data.data.result.status){
                                    this.disableSave = false;
                                    this.rolePersonForm.personalList.push(this.rolePersonForm.operators)
                                    this.rolePersonForm.operators = ''
                                }else{
                                    this.$ypMsg.notice_error(this,'添加人员失败',response.data.data.result.remarks);
                                    this.rolePersonForm.operators = ''
                                }
                            }else{
                                this.$ypMsg.notice_error(this,'添加人员失败',response.data.message,response.data.solution);
                            }
                            
                        }
                    )
                }

            },
            // 关闭当前审核
            closeCurrentCheck(){
                if(this.justShowCheck.length == 0){
                    this.$Message.warning('当前审核为空，不能关闭');
                    return false;
                }
                this.willAddFlag = false;
                // 清空上次选项
                this.rolePersonForm.operators = ''
                this.rolePersonForm.personalList = [];
                this.rolePersonForm.roleList = [];
                this.rolePersonForm.checkedRoles = [];
                this.rolePersonForm.checkedOperators = []
            },
            addCheck(items,index){
                // 要添加的级别
                if(!items){
                    this.currentAddIndex = -1;
                }else{
                    this.currentAddIndex = index;
                }
                if(this.justShowCheck.length >= 5){
                    this.$Message.warning('暂时只支持5级审核');
                    return false;
                }
                // 清空所有选择
                this.rolePersonForm.operators = ''
                this.rolePersonForm.personalList = [];
                this.rolePersonForm.roleList = [];
                this.rolePersonForm.checkedRoles = [];
                this.rolePersonForm.checkedOperators = []
                this.willAddFlag = true;
                // 每次添加都展示当前标题
                this.addEditFlag = true;
            },
            // 保存当前审核
            saveCurrentCheck(){
                // 检查这个层级审核是否为空 
                if(this.rolePersonForm.personalList.length == 0 && this.rolePersonForm.roleList.length == 0){
                    this.$Message.warning('需要填写审核角色或人员才能添加下级审核');
                    return false;
                }else{
                    this.willAddFlag = false;

                    // 编辑 or 添加
                    if(this.addEditFlag){
                        // 在对应级别下添加 下一级处理
                        this.justShowCheck.splice(this.currentAddIndex+1, 0, {
                            stage: this.currentAddIndex+2,
                            roles: this.rolePersonForm.roleList.map(String),
                            operators: this.rolePersonForm.personalList
                        });
                        // 对整个数组重排
                        this.justShowCheck.forEach((element,index) => {
                            element.stage = index + 1
                        });
                        
                    }else{
                        let newArr =JSON.parse(JSON.stringify(this.justShowCheck));
                        this.justShowCheck = newArr;
                        // 对应 某级审核
                        this.justShowCheck[this.editCheckTitle-1].stage = this.editCheckTitle
                        this.justShowCheck[this.editCheckTitle-1].roles = this.rolePersonForm.roleList
                        this.justShowCheck[this.editCheckTitle-1].operators = this.rolePersonForm.personalList
                        this.addEditFlag = true;
                    }
                    // 几级标题
                    this.checkTitle = this.justShowCheck.length+1;
                    // 展示已经编辑过列表
                    this.ifShowCheckBlocks = true;
                    
                    // 清空上次选项
                    this.rolePersonForm.operators = ''
                    this.rolePersonForm.personalList = [];
                    this.rolePersonForm.roleList = [];
                    this.rolePersonForm.checkedRoles = [];
                    this.rolePersonForm.checkedOperators = []
                }
            },
            // 删除已经编辑过审核
            deleteChecked(items,index){
                this.$Modal.confirm({
                    title: '确认删除',
                    content: '<p>确定删除此级审核吗？</p>',
                    onOk: () => {
                        this.justShowCheck.splice(index, 1);
                        this.checkTitle = this.justShowCheck.length+1;
                    },
                    onCancel: () => {
                        this.$Message.info('取消成功');
                    }
                });
                
            },
            // 编辑 编辑过的审核
            editChecked(items,index){
                this.willAddFlag = true;
                // 禁止添加
                this.addEditFlag = false;
                this.editCheckTitle = index+1;
                this.rolePersonForm.checkedRoles = items.roles
                this.rolePersonForm.checkedOperators = items.operators;
                
                this.rolePersonForm.roleList = items.roles
                this.rolePersonForm.personalList = items.operators;
            },
            // 树的编辑
            tree_Edit (node, data, e) {
                e.stopPropagation();
                this.$refs.form_detail.resetFields();
                if (data.top) {
                    this.top_resource = true;
                } else {
                    this.top_resource = false;
                }
                this.detail_resource =false;
                this.create_orEdit = false;
                this.applyCheck = false;
                this.add_resource = true;
                this.current_dataTemp = data;
                let param = {
                    id : data.id
                }
                this.show_loading_edit = true;
                api.yop_acl_resource_detail(param).then(
                    (response) => {
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.detail_info_handler(result,data.top);
                            if(result.url){
                                this.per_rule_list_get(result.url,true);
                            }
                        }else{
                            this.$ypMsg.notice_error(this,'资源详细信息获取失败,请刷新重试',response.data.message,response.data.solution);
                            this.add_resource= false;
                        }
                        this.show_loading_edit = false;
                    }
                )
            },
            // 树的添加执行函数
            append (data,id,top) {
                let newChild_notTop ={}
                let optionTmp = '禁用';
                let enableStatusTmp = true;
                let colorTmp = 'green';
                if(top){
                    if(this.form_detail.status === '启用'){
                        optionTmp = '禁用';
                        colorTmp = 'green';
                        enableStatusTmp = true;
                    }else{
                        optionTmp = '启用';
                        colorTmp = 'red';
                        enableStatusTmp = false;
                    }
                    let newChild_top ={
                        depth: 1,
                        id: id,
                        label: this.form_detail.name,
                        top: top,
                        color: colorTmp,
                        icon: 'android-menu',
                        type: 'MENU',
                        status: this.form_detail.status,
                        option: optionTmp,
                        enableStatus : enableStatusTmp,
                    }
                    this.data_tree.push(newChild_top);
                }else{
                    let iconTmp = 'android-menu'
                    let tempTmp = 'MENU'
                    // let
                    if(this.form_detail.status === '启用'){
                        optionTmp = '禁用';
                        colorTmp = 'green';
                        enableStatusTmp = true;
                    }else{
                        optionTmp = '启用';
                        colorTmp = 'red';
                        enableStatusTmp = false;
                    }
                    if(this.form_detail.type === '菜单'){
                        iconTmp = 'android-menu'
                        tempTmp = 'MENU'
                    }else{
                        iconTmp = 'android-radio-button-on'
                        tempTmp = 'BUTTON'
                    }
                    newChild_notTop = {
                        depth: data.depth+1,
                        id: id,
                        label: this.form_detail.name,
                        top: top,
                        color: colorTmp,
                        icon: iconTmp,
                        type: tempTmp,
                        status: this.form_detail.status,
                        option: optionTmp,
                        enableStatus : enableStatusTmp,
                    };
                    if (!data.children) {
                        this.$set(data, 'children', []);
                    }
                    data.children.push(newChild_notTop);
                }


            },
            // 资源树的删除执行函数
            remove (node, data) {
                const parent = node.parent;
                const children = parent.data.children || parent.data;
                const index = children.findIndex(d => d.id === data.id);
                children.splice(index, 1);
            },
            nodeChange () {
                console.log(this.$refs.tree.getHalfCheckedNodes());
            },
            filterNode (value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            },
            allowDrop (draggingNode, dropNode, type) {
                console.log(draggingNode,1)
                console.log(dropNode,2)
                console.log(type)
                if(draggingNode.data.type === 'BUTTON') {
                    if(draggingNode.parent.data.id === dropNode.parent.data.id && type !== 'inner'){
                        return true;
                    }
                    // 跨级拖动
                    if(draggingNode.parent.data.id !== dropNode.parent.data.id){
                        return true;
                    }
                }else if(draggingNode.data.type === 'MENU'){
                    if(dropNode.data.depth === draggingNode.data.depth && type !== 'inner'){
                        return true;
                    }else if(type === 'inner' && dropNode.data.depth+1 === draggingNode.data.depth ){
                        return true;
                    }
                }
            },
            // allowDrop (draggingNode, dropNode, type) {
            //     console.log(draggingNode,1)
            //     console.log(dropNode,2)
            //     console.log(type)
            //     if(draggingNode.data.type === 'BUTTON') {
            //         if(draggingNode.parent.data.id === dropNode.parent.data.id && type !== 'inner'){
            //             return true;
            //         }
            //     }else if(draggingNode.data.type === 'MENU'){
            //         if(dropNode.data.depth === draggingNode.data.depth && type !== 'inner'){
            //             return true;
            //         }else if(type === 'inner' && dropNode.data.depth+1 === draggingNode.data.depth ){
            //             return true;
            //         }
            //     }
            // },
            // 拖拽提示
            drag_notice () {
                // this.$Modal.warning({
                //     title: '警告',
                //     content: '不允许拖动到此菜单中'
                // });
                this.$Message.warning('不允许拖动到此菜单中');
            },
            nodeClick (data) {
                if (data.top) {
                    this.top_resource = true;
                } else {
                    this.top_resource = false;
                }
                let param = {
                    id : data.id
                }
                this.show_loading_preview = true;
                api.yop_acl_resource_detail(param).then(
                    (response) => {
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.detail_info_handler(result);

                        }else{
                            this.$ypMsg.notice_error(this,'资源详细信息获取失败,请刷新重试',response.data.message,response.data.solution);
                            this.add_resource= false;
                        }
                        this.show_loading_preview = false;
                    }
                )
                this.add_resource = false;
                this.applyCheck = false;
                this.detail_resource = true;
            },
            // 详细信息数据处理
            detail_info_handler (result,top) {
                if(!top){
                    this.form_detail.id = result.id;
                    this.form_detail.PResource = result.pName;
                    this.form_detail.name = result.name;
                    this.form_detail.icon = util.empty_handler2(result.icon);
                    if(result.type === 'MENU'){
                        this.form_detail.type = '菜单';
                    }else{
                        this.form_detail.type = '按钮';
                    }
                    if(result.visibleType === 'ALL'){
                        this.form_detail.permission = '全部可见';
                    }else if(result.visibleType === 'YOP'){
                        this.form_detail.permission = 'YOP可见';
                    }else{
                        this.form_detail.permission = 'SP可见';
                    }
                    this.form_detail.url = result.url;
                    this.form_detail.description = result.description;
                    if(result.status === 'ENABLE'){
                        this.form_detail.status = '启用'
                    }else{
                        this.form_detail.status = '禁用'
                    }
                    if(result.filterChain){
                        this.form_detail.per_rule = result.filterChain.url+'('+result.filterChain.name+')';
                    }
                }else{
                    this.data_add_top_init();
                    this.form_detail.id = result.id;
                    this.form_detail.name = result.name;
                    this.form_detail.icon = util.empty_handler2(result.icon);
                    if(result.status){
                        if(result.status === 'ENABLE'){
                            this.form_detail.status = '启用'
                        }else{
                            this.form_detail.status = '禁用'
                        }
                    }
                    if(result.type){
                        if(result.type === 'MENU'){
                            this.form_detail.type = '菜单';
                        }else{
                            this.form_detail.type = '按钮';
                        }
                    }
                    if(result.visibleType){
                        if(result.visibleType === 'ALL'){
                            this.form_detail.permission = '全部可见';
                        }else if(result.visibleType === 'YOP'){
                            this.form_detail.permission = 'YOP可见';
                        }else{
                            this.form_detail.permission = 'SP可见';
                        }
                    }
                    if(result.description){
                        this.form_detail.description = result.description;
                    }
                }


            },
            // 关闭详情
            detail_close () {
                this.detail_resource = false;
            },
            // 添加提交
            add_submit (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        let param = {}
                        if(!this.top_resource && this.create_orEdit){//新增非顶级资源
                            param = this.param_notTop_handler();
                            this.resource_create_handler(param,false);
                        }else if(this.top_resource && this.create_orEdit){
                            param = this.param_top_handler();
                            this.resource_create_handler(param,true);
                        }else if(!this.top_resource && !this.create_orEdit){
                            param = this.param_notTop_edit_handler();
                            this.resource_edit_handler(param);
                        }else if(this.top_resource && !this.create_orEdit){
                            param = this.param_top_edit_handler();
                            this.resource_edit_handler(param);
                        }
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 添加请求处理
            resource_create_handler (param,top) {
                api.yop_acl_resource_create(param).then(
                    (response) =>{
                        console.log(response);
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'资源添加成功');
                            this.add_resource = false;
                            let id = response.data.result
                            this.append(this.current_dataTemp,id,top);
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            this.add_resource = false;
                        }
                    }
                )
            },
            // 修改请求处理
            resource_edit_handler (param) {
                api.yop_acl_resource_edit(param).then(
                    (response) =>{
                        console.log(response);
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'资源修改成功');
                            this.add_resource = false;
                            let nameTemp = param.name;
                            if(util.getLength(param.name) > 10){
                                nameTemp = param.name.substring(0,10)+'...'
                            }else{
                                nameTemp = param.name
                            }
                            this.current_dataTemp.label = nameTemp
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            this.add_resource = false;
                        }
                    }
                )
            },
            //非顶级资源编辑参数处理
            param_notTop_edit_handler () {
                let visiableTypeTemp = 'YOP'
                if(this.form_detail.permission === 'YOP可见'){
                    visiableTypeTemp = 'YOP'
                }else if(this.form_detail.permission === '全部可见'){
                    visiableTypeTemp = 'ALL'
                }else{
                    visiableTypeTemp = 'SP'
                }
                let param = {
                    id : this.form_detail.id,
                    name : this.form_detail.name,
                    url : this.form_detail.url,
                    icon : this.form_detail.icon,
                    permId : this.form_detail.permId,
                    description : this.form_detail.description,
                    visibleType : visiableTypeTemp
                }
                return param;
            },
            //顶级资源编辑参数处理
            param_top_edit_handler () {
                let param = {
                    id : this.form_detail.id,
                    name : this.form_detail.name,
                    icon : this.form_detail.icon,
                    description : this.form_detail.description
                }
                return param;
            },
            // 非顶级资源参数处理
            param_notTop_handler () {
                let typeTemp = 'MENU';
                let statusTemp = 'ENABLE';
                let visiableTypeTemp = 'YOP'
                if(this.form_detail.type === '菜单'){
                    typeTemp = 'MENU';
                }else{
                    typeTemp = 'BUTTON';
                }
                if(this.form_detail.status === '启用') {
                    statusTemp = 'ENABLE'
                }else{
                    statusTemp = 'DISABLE'
                }
                if(this.form_detail.permission === 'YOP可见'){
                    visiableTypeTemp = 'YOP'
                }else if(this.form_detail.permission === '全部可见'){
                    visiableTypeTemp = 'ALL'
                }else{
                    visiableTypeTemp = 'SP'
                }
                let param = {
                    pId : this.form_detail.id,
                    name : this.form_detail.name,
                    type : typeTemp,
                    icon : this.form_detail.icon,
                    url : this.form_detail.url,
                    permId : this.form_detail.permId,
                    description : this.form_detail.description,
                    status : statusTemp,
                    visibleType : visiableTypeTemp
                }
                return param;
            },
            // 顶级资源参数处理
            param_top_handler () {
                let statusTemp = 'ENABLE';
                if(this.form_detail.status === '启用') {
                    statusTemp = 'ENABLE'
                }else{
                    statusTemp = 'DISABLE'
                }
                let param = {
                    name : this.form_detail.name,
                    type : 'MENU',
                    icon : this.form_detail.icon,
                    description : this.form_detail.description,
                    status : statusTemp,

                }
                return param;
            },
            // 顶级资源添加数据初始化
            data_add_top_init(){
                this.$refs.form_detail.resetFields();
                this.form_detail.name = '';
                this.form_detail.url = '/123';
                this.form_detail.description = '';
                this.form_detail.permission = 'YOP可见';
                this.form_detail.type === '菜单';
                this.form_detail.status === '启用';
                this.form_detail.icon = '';
                // this.$refs.per_rule.clearSingleSelect();
                this.form_detail.permId = 0;
            },
            // 添加提交
            add_close () {
                this.add_resource = false;
            },
            // 错误数组处理
            error_array_handler (array) {
                let result = ''
                for(var i in array){
                    if(i === array.length-1){
                        result =result + array[i]
                    }else{
                        result =result + array[i]+','
                    }
                }
                return result;
            },
            // 启用提交
            enable_submit () {
                let param = {}
                if(this.single_orBatch){
                    param = {
                        ids :[this.current_nodeData.id],
                    }
                }else{
                    param = {
                        ids :this.get_allCheckedNodes().ids,
                    }
                }
                api.yop_acl_resource_enable(param).then(
                    (response) =>{
                        console.log(response);
                        if (response.status === 'success') {
                            if(response.data.result && response.data.result.length > 0){
                                let errorMessage = this.error_array_handler(response.data.result);
                                this.$ypMsg.notice_warning(this,errorMessage+'已启用，未启用成功','成功');
                            }else{
                                this.$ypMsg.notice_success(this,'启用成功');

                            }
                            if(this.single_orBatch){
                                this.current_nodeData.option = '禁用';
                                this.current_nodeData.status = '启用';
                                this.current_nodeData.enableStatus = true;
                            }else{
                                this.get_allCheckedNodes().nodes.forEach(
                                    item =>{
                                        item.option ='禁用';
                                        item.status = '启用';
                                        item.enableStatus = true;
                                    }
                                )
                                this.resetChecked();
                            }

                        } else {
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                        this.modal_enable =false;
                    }
                );
            },
            // 启用取消
            enable_cancel () {
                this.modal_enable =false;
            },
            // 禁用提交
            disable_submit () {
                let reason = ''
                if(this.data_select_disabledReason === '其他'){
                    reason = this.reason_other
                }else{
                    reason = this.data_select_disabledReason
                }
                if(reason.trim()){
                    let param = {}
                    if(this.single_orBatch){
                        param = {
                            ids :[this.current_nodeData.id],
                            disableReason : reason
                        }
                    }else{
                        param = {
                            ids :this.get_allCheckedNodes().ids,
                            disableReason : reason
                        }
                    }
                    api.yop_acl_resource_disable(param).then(
                        (response) =>{
                            if (response.status === 'success') {
                                if(response.data.result && response.data.result.length > 0){
                                    let errorMessage = this.error_array_handler(response.data.result);
                                    this.$ypMsg.notice_warning(this,errorMessage+'已禁用，未禁用成功','成功');
                                }else{
                                    this.$ypMsg.notice_success(this,'禁用成功');
                                }
                                if(this.single_orBatch){
                                    this.current_nodeData.option = '启用';
                                    this.current_nodeData.status = '禁用';
                                    this.current_nodeData.enableStatus = false;
                                }else{
                                    this.get_allCheckedNodes().nodes.forEach(
                                        item =>{
                                            item.option ='启用';
                                            item.status = '禁用';
                                            item.enableStatus = false;
                                        }
                                    )
                                    this.resetChecked();
                                }

                            } else {
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                            this.modal_disable =false;
                        }
                    )
                }else{
                    this.$Message.error('禁用原因不能为空');
                }

            },
            // 禁用取消
            disable_cancel () {
                this.modal_disable =false;
            },
            // 新增顶级资源
            add_top_resource () {
                this.data_add_top_init();
                this.create_orEdit =true;
                this.add_resource = true;
                this.detail_resource = false;
                this.top_resource = true;
            },
            // 批量删除
            batch_delete () {
                if(this.get_allCheckedNodes().ids.length > 0){
                    this.$Modal.confirm({
                        title: '删除资源配置',
                        content: "<p style='color:red'>如果删除父级菜单，则关联子级菜单一并删除。</p><p>确定删除资源配置？</p>",
                        'ok-text':'确认',
                        onOk: () =>{
                            // 删除操作
                            let ids = this.get_allCheckedNodes().ids;
                            let nodes = this.get_allCheckedNodes().nodes;
                            let param = {
                                ids : ids
                            }
                            // console.log(nodes)
                            this.delete_node_handler_batch(nodes,param);

                        }
                    });
                }else{
                    this.$Modal.warning(
                        {
                            title: '警告',
                            content: '至少选择一个资源'
                        }
                    )
                }

            },
            // 批量删除执行函数
            delete_node_handler_batch (nodes,param) {
                // nodes.forEach(
                //     item =>{
                //         this.remove(item, item.data);
                //     }
                // )
                api.yop_acl_resource_delete(param).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'资源删除成功');
                            this.information_tree_get();
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                    }
                )
            },
            // 批量启用
            batch_enable () {
                if(this.get_allCheckedNodes().ids.length > 0){
                    if(this.allNodes_status(this.get_allCheckedNodes().nodes,true)){
                        this.$Modal.warning(
                            {
                                title: '警告',
                                content: '所选资源全部已经全部启用'
                            }
                        )
                    }else{
                        this.modal_enable = true;
                        this.single_orBatch = false;
                        // 请求接口获取禁用原因
                        this.disable_reasonList_get(this.get_allCheckedNodes().ids);
                    }

                }else{
                    this.$Modal.warning(
                        {
                            title: '警告',
                            content: '至少选择一个资源'
                        }
                    )
                }
            },
            // 批量禁用
            batch_disable () {
                if(this.get_allCheckedNodes().ids.length > 0){
                    if(this.allNodes_status(this.get_allCheckedNodes().nodes,false)){
                        this.$Modal.warning(
                            {
                                title: '警告',
                                content: '所选资源全部已经全部禁用'
                            }
                        )
                    }else{
                        this.single_orBatch =false;
                        this.data_select_disabledReason = '';
                        this.modal_disable = true;
                    }

                }else{
                    this.$Modal.warning(
                        {
                            title: '警告',
                            content: '至少选择一个资源'
                        }
                    )
                }

            },
            // 初始化
            init () {
                this.information_tree_get();
                this.disable_reason_get();
            },
            updateSelect_permission_rule (val) {
                this.form_detail.per_rule = val;
            },
            // 获取禁用原因列表
            disable_reason_get () {
                api.yop_acl_resource_disableReason_list().then(
                    (response) =>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.data_disabledReason_List = [];
                            if(result){
                                result.forEach(
                                    item =>{
                                        this.data_disabledReason_List.push({
                                            value : item,
                                            label : item
                                        })
                                    }
                                )
                            }
                            this.data_disabledReason_List.push({
                                value : '其他',
                                label : '其他'
                            })
                        }else{
                            this.$ypMsg.notice_error(this,'禁用原因列表获取失败',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            // 获取所有选择节点
            get_allCheckedNodes () {
                let ids = []
                let nodes = this.$refs.tree.getCheckedNodes();
                if(!!nodes){
                    nodes.forEach(
                        item =>{
                            ids.push(item.id)
                        }
                    )
                }
                return {
                    ids: ids,
                    nodes: nodes
                };
            },
            // 树的结构信息获取
            information_tree_get () {
                this.show_loading_tree = true;
                api.yop_acl_resource_list().then(
                    (response) =>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.data_tree = [];
                            this.data_tree = this.information_tree_handler(result,0);
                        }else{
                            this.$ypMsg.notice_error(this,'资源数据获取失败,请刷新重试',response.data.message,response.data.solution);
                        }
                        this.show_loading_tree =false;
                    }
                )
            },
            // 树的结构数据处理
            information_tree_handler (data,index) {
                let topTemp =true;
                if(index === 0){
                    topTemp = true;

                }else{
                    topTemp =false;
                }
                let returnData = [];
                index ++;
                if(data){
                    data.forEach(
                        (item) =>{
                            let enableStatusTemp = true
                            let statusTemp = '启用'
                            let optionTemp = '禁用'
                            let colorTemp = 'green'
                            let iconTemp = 'android-radio-button-on'
                            let nameTemp = ''
                            if(item.status === 'ENABLE'){
                                statusTemp = '启用'
                                optionTemp = '禁用'
                                colorTemp = 'green'
                                enableStatusTemp = true
                            }else{
                                statusTemp = '禁用'
                                optionTemp = '启用'
                                colorTemp = 'red'
                                enableStatusTemp = false
                            }
                            if(item.type === 'BUTTON'){
                                iconTemp = 'android-radio-button-on'
                            }else{
                                iconTemp = 'android-menu'
                            }
                            if(util.getLength(item.name) > 10){
                                nameTemp = item.name.substring(0,10)+'...'
                            }else{
                                nameTemp = item.name
                            }
                            if(!!item.children){
                                returnData.push({
                                    id: item.id,
                                    // pid : item.pid,
                                    top : topTemp,
                                    label : nameTemp,
                                    depth: item.depth,
                                    status : statusTemp,
                                    option : optionTemp,
                                    color : colorTemp,
                                    icon : iconTemp,
                                    type : item.type,
                                    audit: item.audit,
                                    enableStatus : enableStatusTemp,
                                    children : this.information_tree_handler(item.children,index)
                                })
                            }else{
                                returnData.push({
                                    id: item.id,
                                    // pid : item.pid,
                                    top : topTemp,
                                    label : nameTemp,
                                    depth: item.depth,
                                    status : statusTemp,
                                    option : optionTemp,
                                    color : colorTemp,
                                    icon : iconTemp,
                                    type : item.type,
                                    audit: item.audit,
                                    enableStatus : enableStatusTemp,
                                    children : []
                                })
                            }
                        }
                    )
                }
                return returnData;
            },
            //  拖拽操作
            nodeDrop (before,after,inner,event) {
                let pid = after.id;
            },
            // 权限规则列表获取函数
            per_rule_list_get (url,set) {
                if(!this.top_resource){
                    this.form_detail.permId = '';
                    let param = {
                        url : url
                    }
                    api.yop_acl_per_rule_list(param).then(
                        (response) =>{
                            let status =response.data.status
                            if(status === 'success'){
                                let result = response.data.data.result;
                                this.data_per_rule_List = [];
                                if(result){
                                    let aclList = result.aclFilter;
                                    if(!!aclList){
                                        aclList.forEach(
                                            item =>{
                                                this.data_per_rule_List.push({
                                                    value : item.id,
                                                    label : item.url+'('+item.name+')'
                                                })
                                            }
                                        )
                                    }
                                    this.$refs.per_rule.clearSingleSelect();
                                    // this.form_detail.permId = ''
                                    if (result.defaultId && set) {
                                        this.form_detail.permId = result.defaultId;
                                    }
                                }
                            }else{
                                this.$ypMsg.notice_error(this,'权限过滤器列表获取失败,请重试',response.message,response.solution);
                            }
                            this.show_loading_edit = false;
                        }
                    )
                }
            },
            // 根据输入刷新权限规则列表
            per_rule_refresh (){
                if(!this.top_resource){
                    if(this.form_detail.url && !util.formatCheck4(this.form_detail.url) && util.getLength(this.form_detail.url) <= 64){
                        this.per_rule_list_get(this.form_detail.url,false);
                    }
                }
            },
            // 新增资源数据初始化
            add_resource_data_init(name,id){
                this.form_detail.name = '';
                this.form_detail.icon = '';
                this.form_detail.url = '';
                this.form_detail.description = '';
                this.form_detail.permission = 'YOP可见';
                this.$refs.per_rule.clearSingleSelect();
                this.form_detail.permId = '';
                this.$refs.form_detail.resetFields();
                this.form_detail.PResource = name;
                this.form_detail.id = id;
            },
            // 判断是否所有选中的节点状态数组
            allNodes_status (nodes,status){
                if(!!nodes){
                    for(var i in nodes){
                        if(nodes[i].enableStatus !== status){
                            return false;
                        }
                    }
                    return true;
                }else{
                    return false;
                }

            },
            // 拖拽后台执行函数
            handleDrop(draggingNode, dropNode, dropType, ev) {
                console.log(draggingNode);
                console.log(dropNode);
                console.log(dropType);
                if(draggingNode.data.type = 'MENU'){
                    let appendToIdTemp = ''
                    if(dropType === 'inner'){
                        appendToIdTemp = this.appendToId_get(draggingNode.data.id,dropNode.data.children)
                    }else{
                        // 最顶级判断
                        console.log(draggingNode.data.depth);
                        if(draggingNode.data.depth === 1){
                            appendToIdTemp = this.appendToId_get(draggingNode.data.id,dropNode.parent.data)
                        }else{
                            appendToIdTemp = this.appendToId_get(draggingNode.data.id,dropNode.parent.data.children)
                        }
                    }
                    let targetPidTemp = this.targetPid_get(draggingNode, dropNode, dropType);
                    let param ={
                        id : draggingNode.data.id,
                        targetPid: targetPidTemp,
                        appendToId: appendToIdTemp
                    }
                    if(appendToIdTemp === ''){
                        delete param.appendToId;
                    }
                    this.drop_handler(param);
                    console.log(param);
                }else if(draggingNode.data.type = 'BUTTON'){
                    let appendToIdTemp = this.appendToId_get(draggingNode.data.id,draggingNode.parent.data.children)
                    let param ={
                        id : draggingNode.data.id,
                        targetPid: draggingNode.parent.data.id,
                        appendToId: appendToIdTemp
                    }
                    if(appendToIdTemp === ''){
                        delete param.appendToId;
                    }
                    console.log(param);
                    this.drop_handler(param);
                }

            },
            // 拖拽执行函数
            drop_handler (param) {
                api.yop_acl_resource_drag(param).then(
                    (response) =>{
                        console.log(response);
                        if(response.status === 'success'){

                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            this.information_tree_get();
                        }
                    }
                )
            },
            // 获取appendToId
            appendToId_get (id,parent){
                if(parent){
                    for(var i in parent){
                        if(parent[i].id === id){
                            if(i > 0){
                                return parent[i-1].id
                            }else{
                                return ''
                            }
                        }
                    }
                    return '';
                }
            },
            // 获取pid
            targetPid_get (draggingNode, dropNode, dropType){
                if(dropType === 'inner'){
                    return dropNode.data.id;
                }else{
                    if(dropNode.parent.data.id){
                        return dropNode.parent.data.id;
                    }else{
                        return 0;
                    }
                }
            },
            // 清空已选节点
            resetChecked() {
                this.$refs.tree.setCheckedKeys([]);
            },
            // 图标选取
            icon_select () {
                if(this.form_detail.icon && this.form_detail.icon !== ''){
                    this.$refs.iconSelector.icon_set(this.form_detail.icon);
                }
                this.$refs.iconSelector.modal_preview();
            },
            // 图标数据更新
            icon_update (val) {
                this.form_detail.icon = val;
                this.$refs.iconSelector.modal_reset();
            }
        },
        created() {
            this.getRoleList()
        },
        mounted () {
            if(this.justShowCheck.length == 0){
                this.currentAddIndex = -1;
            }
            this.init();
        }
    };
</script>


