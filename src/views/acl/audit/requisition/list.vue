<style lang="less" scoped>
 @import '../../../../styles/common.less';
    .page-container{
        background: #eee;
        padding:8px;
        .ivu-card{
            .ivu-card-body{
                .ivu-tabs{
                    min-height: 400px;
                }
            }
        }
    }
    .order-con{
        line-height: 40px;
    }
    .tip-txt{
        margin-bottom: 12px;
        font-size: 12px;
        color: #495060;
        position: relative;
        top: -10px;
    }
    .tip-txt i{
        color: #f90;
        font-size: 36px;
        margin-right: 10px;
        position: relative;
        top: 6px;
    }
    .text-right{
        text-align: right;
    }
    .text-center{
        text-align: center;
    }
    .checking-title{
        border-bottom: 1px solid #e9eaec;
        margin: 40px 0 15px 0;
    }
    .check-item{
        font-size: 14px;
        & > p{
            margin-left: 20px;
        }
    }
    .advise{
        margin-left: 180px;
        position: relative;
        top: -110px;
    }
    .order-detail{
        & > span{
            margin-right: 20px;
        }
    }
    .ivu-modal-content{
        .ivu-modal-footer{
            border: none!important;
        }
    } 
    .code-con{
        min-height: 50px;
        max-height: 360px;
        width: 1014px;
        padding: 10px 0 10px 20px;
        margin-top: 22px;
        background-color: #282c34;
        white-space:normal;
        color: #abb2bf;
        overflow: scroll;
        -ms-overflow-style: none; 
    }
    .code-con::-webkit-scrollbar { width: 0 !important }
    .cancel-cause{
        margin-left: 41px;
        margin-top: 10px;
        width: 66%;
    }
    .close-more-data{
        position: absolute;
        right: -11px;
        top: -12px;
    }
    .json-con{
        max-height: 300px;
        overflow-y: scroll;
    }
    .hide{
        display: none!important;
    }
    .jv-container.boxed{
        border: none!important;
    }
</style>
<template>
    <div class="page-container">
        <Card :bordered="false" dis-hover>
            
            <Row class="order-con">
                <Row class="margin-left-10" >
                    审核状态：
                    <Radio-group @on-change="checkoutStatus"  v-model="orderStatus">
                        <Radio label="">全部</Radio>
                        <Radio v-for='(item,index) in checkStatusArr' :label='item.code' :key='index'><span>{{item.name}}</span></Radio>
                    </Radio-group>
                </Row>
                <!-- 申请单号 -->
                <Col class="margin-left-10" span="6" >
                    <Col span="5">申请单号:</Col> 
                    <Col span="19">
                        <Input clearable v-model="orderNumber" placeholder="申请单号"></Input>
                    </Col>
                </Col> 
                <Col >
                    <Col class="margin-left-10" span="1">申请人:</Col> 
                    <Col span="3">
                        <Input clearable v-model="orderPerson" placeholder="申请人"></Input>
                    </Col>
                </Col> 
                <!-- 时间 -->
                <Col span="10">
                    <Col class="margin-left-10" span="3">
                        <Col >申请时间:</Col>
                    </Col>
                    <Col span="18">
                        <DatePicker 
                            type="datetime"
                            ref="start"
                            @on-change="update_start_date"
                            format="yyyy-MM-dd HH:mm:ss"
                            placeholder="选择开始时间" 
                            style="width: 156px">
                        </DatePicker>
                        至
                        <DatePicker 
                            type="datetime"
                            ref="end"
                            @on-change="update_end_date"
                            format="yyyy-MM-dd HH:mm:ss" 
                            placeholder="选择结束时间" 
                            style="width: 156px">
                        </DatePicker>
                    </Col>
                </Col>
                <!-- 搜索 -->
                <Col span="3" >
                    <Button @click="searchList" type="primary">查询</Button>
                    <Button @click="resetList">重置</Button>
                </Col>
                
            </Row>
            <!-- 审核单列表 -->
            <Row class="margin-top-10">
                <template>
                    <Table border :columns="checkListTitle"  :data="checkListData">
                    </Table>
                </template>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page
                    class="margin-top-10"
                    :page-size="10"
                    style="float: right"
                    :total="pageTotal"
                    :current="pageNo"
                    show-elevator
                    @on-change="pageRefresh"
                    ></Page>
                </Tooltip>
            </Row>
           
            <!-- modal 取消审核 弹框 -->
            <Modal v-model="cancelCheckFlag" width="560">
                <p slot="header" >
                    <span>撤销审核</span>
                </p>
                <div>
                    <p class="tip-txt"><i class="ivu-icon ivu-icon-help-circled"></i>确认要撤销审核 ？
                        <p style="padding-left: 41px;">功能为 {{cancelFun}}，申请单号：{{currentOrderNum}} </p> 
                    </p>
                    <Input class="cancel-cause" placeholder="请输入撤销原因" v-model="cancelCheckCause"/>
                </div>
                <div class="text-right" slot="footer">
                    <Button id="btn_resList_19" type="text"  @click="cancelReasonModal('recover')">取消</Button>
                    <Button id="btn_resList_18" type="primary"  @click="confirmCancelCheck">确认</Button>
                </div>
            </Modal>
            <!-- 进行审核弹框 -->
            <Modal  :closable="false" :mask-closable="false" v-model="checkingModelFlag" width="1080">
                <p slot="header" >
                    <span>审核</span>
                </p>
                <Col class="check-item">
                <p class="order-detail">
                    审核单号: &nbsp;&nbsp;<span>{{checkingForm.orderNum}}</span>
                    申请时间: &nbsp;&nbsp;<span>{{checkingForm.applyTime}}</span>
                    申请人: &nbsp;&nbsp;<span>{{checkingForm.operator}}</span>
                    申请状态: &nbsp;&nbsp;<span>{{checkingForm.status}}</span>
                </p>
                </Col>
                <Row>
                    <Col class="check-item">
                        <h3 class="checking-title">审核内容</h3>
                        <p class="margin-bottom-10">申请变更功能：{{checkingForm.applyChange}}</p>
                        <p>操作原因：{{checkingForm.cause}}</p>
                        <Button @click="searchMoreData"  class="margin-top-20 margin-left-20" type="primary">查看变更数据</Button>
                    </Col>
                    <!-- 查看更多数据 -->
                    <Card v-if="ifShowMoreDataFlag" width="900"  class="margin-top-20" id="card_resList_1" >
                        <a slot="extra" class="close-more-data" @click="ifShowMoreDataFlag = false">
                            <Icon type="close"></Icon>
                        </a>
                        <div class="json-con">
                            <!-- <pre class="code-con">{{this.moreData}}</pre> -->
                            <json-viewer
                            :value="moreData"
                            :expand-depth=8
                            boxed
                            sort
                            ></json-viewer>
                        </div>
                    </Card>
                </Row>

                <Row>
                    <Col class="check-item">
                        <h3 class="checking-title">下级审核</h3>
                        <div>审核角色: 
                            <span v-if="nextRoles.length != 0">
                                <Button type="primary" @click="showRoleList(item)" v-for="(item, index) in nextRoles" :key="index">
                                    <span>{{item}}</span> &nbsp;
                                </Button>
                            </span>
                            <span v-else>
                                无
                            </span>
                        </div>
                        <div>审核人员: 
                            <span v-if="nextOperators.length != 0">
                                <Button type="text" v-for="(item, index) in nextOperators" :key="index">
                                    <span>{{item}}</span> &nbsp;
                                </Button>
                            </span>
                            <span v-else>
                                无
                            </span>
                        </div>
                    </Col>
                </Row>

                <Row>
                    <Col class="check-item">
                        <h3 class="checking-title">审核记录</h3>
                        <Table :columns="recordTitle" :data="recordData"></Table>
                    </Col>
                </Row>
                <div slot="footer">
                    <Button @click="closeChecking" >关闭</Button>
                </div>
            </Modal>
        </Card>
        <nextRoles ref="next_roles"/>
    </div>
</template>
<script>
import JsonViewer from 'vue-json-viewer'
import loading from '../../../my-components/loading/loading'
import nextRoles from '../../../my-components/next-roles/nextRoles'
import api from '../../../../api/api'
export default {
    components:{
        JsonViewer,
        nextRoles
    },
    data(){
        return {
            // 申请编号
            orderNumber: '',
            // 查询时间
            dateStart: '',
            // 申请人
            orderPerson: '',
            // 审核状态
            orderStatus: '',
            dateEnd: '',
            // 页码
            pageNo: 1,
            // 每页条数
            pageSize: 10,
            // 每页条数
            pageTotal: 0,
            // 审核申请单 列表数据
            checkListData: [
                // {
                // "code": "string",
                // "resourceName": "string",
                // "originator": "string",
                // "operator": "string",
                // "status": "string",
                // "arrivedDate": "2019-12-11T02:49:51.970Z"
                // }
            ],
            // 下级审核人
            nextRoles: [],
            // 下级审核角色
            nextOperators: [],
            // 申请记录列表数据
            recordData: [],
            // 取消审核弹框开关
            cancelCheckFlag: false,
            // 进行审核开关
            checkingModelFlag: false,
            // 取消审核原因
            cancelCheckCause: '',
            // 取消审核功能
            cancelFun: '',
            // 当前审核单号
            currentOrderNum: '',
            // 是否显示审核按钮
            ifShowCheckBtn: true,
            // 是否显示更多内容开关
            ifShowMoreDataFlag: false,
            moreData: '',
            // table列表审核状态
            tableCheckStatus: '',
            // 进行审核相关数据
            checkingForm: {
                orderNum: '',
                applyTime: '',
                operator: '',
                status: '',
                applyChange: '',
                cause: ''
            },
            //审核状态枚举
            checkStatusArr: [],
            //审核操作枚举
            checkUpDateArr: [],
            chechStatusEng: '',
            // 审核记录列表标题
            recordTitle: [
                {
                    title: '序号',
                    key: 'step'
                },
                {
                    title: '审核人',
                    key: 'operator'
                },
                {
                    title: '审核结果',
                    render: (h, params) => {
                        let statusTxt = '';
                            if(params.row.operate == "REVOKE"){
                                statusTxt = "撤回"
                            }
                            if(params.row.operate == "ACCEPT"){
                                statusTxt = "通过"
                            }
                            if(params.row.operate == "CANCEL"){
                                statusTxt = "取消"
                            }
                            if(params.row.operate == "HANDLE"){
                                statusTxt = "办理"
                            }
                            if(params.row.operate == "REFUSE"){
                                statusTxt = "不通过"
                            }
                        return h("div", [
                            h("p",statusTxt),
                            h("p", params.row.createdDate)
                        ]);
                    }
                },
                
                {
                    title: '审核意见',
                    key: 'cause',
                    render: (h, params) => {
                        return h("div", [
                            h("p",params.row.cause ? params.row.cause : '无'),
                        ]);
                    }
                    
                },
            ],
            // 审核单列表标题
            checkListTitle: [
                {
                    title: '申请单号',
                    key: 'code',
                    width: 250,
                },
                {
                    title: '功能',
                    key: 'resourceName'
                },
                {
                    title: '申请人',
                    key: 'originator',
                    width: 140,
                },
                {
                    title: '当前审核人',
                    key: 'operator',
                    width: 140,
                },
                {
                    title: '审核状态',
                    key: 'status',
                    width: 100,
                    render: (h,params) => {
                            var orderStatus = "";
                            if(params.row.status == "REVOKED"){
                                orderStatus = "已撤回"
                            }
                            if(params.row.status == "ACCEPTED"){
                                orderStatus = "已通过"
                            }
                            if(params.row.status == "AUDITING"){
                                orderStatus = "审核中"
                            }
                            if(params.row.status == "UNAUDITED"){
                                orderStatus = "待审核"
                            }
                            if(params.row.status == "REFUSED"){
                                orderStatus = "未通过"
                            }
                        return h("div", orderStatus);
                    }
                },
                {
                    title: '申请时间',
                    key: 'createdDate',
                    width: 180,
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 180,
                    align: 'center',
                    render: (h, params) => {
                        let ifShow = false;
                        if(params.row.status == "AUDITING" || params.row.status == "UNAUDITED"){
                            ifShow = true
                        }
                        return h('div', [
                            h('Button', {
                                props: {
                                    type: 'primary',
                                    size: 'small'
                                },
                                directives: [{
                                    name: 'url',
                                    value: {url: '/rest/acl/audit/requisition/detail'}
                                }],
                                style: {
                                    marginRight: '5px'
                                },
                                on: {
                                    click: () => {
                                        this.goCheck(params)
                                    }
                                }
                            }, '详情'),
                            h('Button', {
                                props: {
                                    type: 'error',
                                    size: 'small',
                                },
                                style: {
                                    display: `${ifShow ? 'inline-block' : 'none'}`
                                },
                                directives: [{
                                    name: 'url',
                                    value: {url: '/rest/acl/audit/requisition/revoke'}
                                }],
                                on: {
                                    click: () => {
                                        this.cancleCheck(params)
                                    }
                                }
                            }, '撤回审核'),
                        ]);
                    }
                }
            ],
        }
    },
    created() {
        
    },
    mounted() {
        this.searchList()
        // 获取枚举状态
        this.getCheckOrderStatus()
    },
    methods: {
        // 开始日期更新
        update_start_date(val) {
            this.dateStart = val;
        },
        // 结束日期更新
        update_end_date(val) {
            this.dateEnd = val;
        },
        // 清空查询列表
        resetList(){
            this.pageNo = 1;
            this.orderNumber = '';
            this.orderPerson = "";
            this.orderStatus = "";
            this.$refs.start.handleClear();
            this.$refs.end.handleClear();
        },
        // 状态变化后 页数改为1
        checkoutStatus(){
            this.pageNo = 1;
            this.searchList()
        },
        // 查询审批单列表
        searchList(){
            let params = {};
            let code = this.orderNumber ? this.orderNumber : null,
                originator = this.orderPerson ? this.orderPerson : null,
                status = this.orderStatus ? this.orderStatus : null;
                params = {
                    code,
                    originator,
                    status,
                    createdStartDate: this.dateStart,
                    createdEndDate: this.dateEnd,
                    _pageSize: this.pageSize,
                    _pageNo: 1
                };
            api.yop_acl_audit_requisition_list(params).then(
                (response) => {
                    let status = response.data.status;
                    if(status === 'success'){
                        this.checkListData = response.data.data.page.items
                        this.pageNo = response.data.data.page.pageNo
                            if(response.data.data.page.items){
                                if(response.data.data.page.items.length < 10){
                                    this.pageTotal=response.data.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                    }else{
                        this.$ypMsg.notice_error(this,'审核单列表获取失败',response.data.message,response.data.solution);
                    }
                    
                }
            )
        },
        pageRefresh(page){
            let params = {};
            let code = this.orderNumber ? this.orderNumber : null,
                originator = this.orderPerson ? this.orderPerson : null,
                status = this.orderStatus ? this.orderStatus : null;
                params = {
                    code,
                    originator,
                    status,
                    createdStartDate: this.dateStart,
                    createdEndDate: this.dateEnd,
                    _pageSize: this.pageSize,
                    _pageNo: page
                };
            api.yop_acl_audit_requisition_list(params).then(
                (response) => {
                    let status = response.data.status;
                    if(status === 'success'){
                        this.checkListData = response.data.data.page.items
                        this.pageNo = response.data.data.page.pageNo
                            if(response.data.data.page.items){
                                if(response.data.data.page.items.length < 10){
                                    this.pageTotal=response.data.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                    }else{
                        this.$ypMsg.notice_error(this,'审核单列表获取失败',response.data.message,response.data.solution);
                    }
                    
                }
            )
        },
        // 关闭审核详情弹框
        closeChecking() {
            this.checkingModelFlag = false
        },
        // 查看更多数据
        searchMoreData(){
            let params = {
                code: this.currentOrderNum
            }
            api.yop_acl_audit_record_content(params).then(
                (response) => {
                    let status = response.data.status;
                    if(status === 'success'){
                        this.ifShowMoreDataFlag = true;
                        this.moreData = response.data.data.result ? JSON.parse(response.data.data.result) : '无'
                    }else{
                        this.$ypMsg.notice_error(this,'数据获取失败',response.data.message,response.data.solution);
                    }
                    
                }
            )
        },
        // 点击取消审核
        cancleCheck(param){
            this.cancelCheckFlag = true
            this.currentOrderNum =  param.row.code
            this.cancelFun = param.row.resourceName
        },
        // 确认取消审核
        confirmCancelCheck(){
            if(!this.cancelCheckCause){
                this.$Message.warning('请输入撤销原因');
                return false;
            }
            let params = {
                cause: this.cancelCheckCause,
                code: this.currentOrderNum
            }
            api.yop_acl_audit_requisition_revoke(params).then(
                (response) => {
                    let status = response.status;
                    if(status === 'success'){
                        this.$ypMsg.notice_success(this,'撤销成功');
                        this.cancelCheckFlag = false
                        this.cancelCheckCause = '';
                        // 更新列表
                        this.searchList()
                    }else{
                        this.cancelCheckFlag = false
                        this.$ypMsg.notice_error(this,'撤销失败',response.message,response.solution);
                    }
                    
                }
            )

        },
        // 审核单详情
        goCheck(param) {
            this.ifShowMoreDataFlag = false; //将审核查看变更数据开关关闭
            this.currentOrderNum =  param.row.code
            let params = {
                code: param.row.code
            }
            api.yop_acl_audit_record_detail(params).then(
                (response) => {
                    let status = response.data.status,
                        resData = response.data;
                    if(status === 'success'){
                        this.checkingModelFlag = true;
                        this.checkingForm.orderNum = resData.data.result.code ? resData.data.result.code : ''
                        this.checkingForm.applyTime = resData.data.result.createdDate ? resData.data.result.createdDate : ''
                        this.checkingForm.cause = resData.data.result.cause ? resData.data.result.cause : '无'
                        this.checkingForm.status = resData.data.result.status ? resData.data.result.status : ''
                        this.checkingForm.operator = resData.data.result.originator ? resData.data.result.originator  : '无'
                        this.checkingForm.applyChange = resData.data.result.resourceName ? resData.data.result.resourceName  : '无'
                        // 下级审核人
                        this.nextRoles = resData.data.result.nextRoles ? resData.data.result.nextRoles : []
                        this.nextOperators = resData.data.result.nextOperators ? resData.data.result.nextOperators : []
                        // 审核记录
                        this.recordData = resData.data.result.auditRecordStage ? resData.data.result.auditRecordStage : []
                        // 处理状态
                        this.checkStatusArr.forEach(element => {
                            if(element.code  == this.checkingForm.status ){
                                this.checkingForm.status = element.name
                            }
                        });
                    }else{
                        this.$ypMsg.notice_error(this,'审核详情获取失败',resData.message,resData.solution);
                    }
                    
                }
            )
        },
        // 请求角色列表
        showRoleList(role){
            this.$refs.next_roles.showFlag = true
            this.$refs.next_roles.getRoleList(role)
        },
        //审核状态 枚举值获取
        getCheckOrderStatus() {
            let params = {}
            // 审核状态枚举
            api.yop_ac_audit_record_commons_status(params).then(
                (response) => {
                    let status = response.data.status;
                    if(status === 'success'){
                        this.checkStatusArr = response.data.data.result ? response.data.data.result : []
                    }else{
                        this.$ypMsg.notice_error(this,'审核枚举状态获取失败',response.data.message,response.solution);
                    }
                    
                }
            )
            // 审核操作枚举
            api.yop_ac_audit_record_commons_operate(params).then(
                (response) => {
                    let status = response.data.status;
                    if(status === 'success'){
                        this.checkUpDateArr = response.data.data.result ? response.data.data.result : []
                    }else{
                        this.$ypMsg.notice_error(this,'审核操作状态获取失败',response.data.message,response.solution);
                    }
                    
                }
            )
        },
        cancelReasonModal(){
            this.cancelCheckFlag = false;
        }
    },
}
</script>