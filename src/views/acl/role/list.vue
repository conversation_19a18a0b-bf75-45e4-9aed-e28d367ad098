<style lang="less">
    @import '../../../styles/common.less';
    @import '../../API_Management_Open/api_Mangement_Open.less';

    .demo-badge-alone {
        background: #5cb85c !important;
    }
    .round {
        width: 16px;
        height: 16px;
        display: inline-block;
        font-size: 20px;
        line-height: 16px;
        text-align: center;
        color: #f00;
        text-decoration: none
    }
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">
                <Col span="7">
                <Col span="7" class="margin-top-10">
                <span>角色类型:</span>
                </Col>
                <Col span="17">
                <common-select id="select_acl_role_1" ref="select_roleType" @on-update="updateSelect_roleType"
                               type="normal"
                               holder="选择角色（默认全部）"
                               keyWord="result"
                               code="code"
                               title="name"
                               group="role_type"
                               @on-loaded="select_callBack"
                               :default="this.data_select_roleType"
                               :uri="this.$store.state.select.role_type.uri"></common-select>
                </Col>
                </Col>
                <Col offset="1" span="7">
                <Col span="7" class="margin-top-10">
                <span>服务提供方:</span>
                </Col>
                <Col span="17">
                <common-select id="select_acl_role_2"  ref="select_sp" @on-update="updateSelect_sp"
                               type="combo"
                               keyWord="result"
                               holder="请选择（默认全部）"
                               code="spCode"
                               title="spName"
                               group="serviceSupplier"
                               @on-loaded="select_callBack"
                               :default="this.data_select_sp"
                               :uri="this.$store.state.select.serviceSupplier.uri"></common-select>
                </Col>
                </Col>
                <Col offset="1" span="7">
                <Col span="7" class="margin-top-10">
                <span>角色编码:</span>
                </Col>
                <Col span="17">
                <Input id='input_acl_role_1' class="margin-top-5" v-model="data_role_code" placeholder="角色编码"
                       @on-enter="search_role"></Input>
                </Col>
                </Col>
                <Col class="margin-top-5" span="7">
                <Col span="7" class="margin-top-10">
                <span>角色名称:</span>
                </Col>
                <Col span="17">
                <Input id='input_acl_role_2' class="margin-top-5" v-model="data_role_name" placeholder="角色名称"
                       @on-enter="search_role"></Input>
                </Col>
                </Col>
                <Col class="margin-top-5" offset="1" span="16">
                <Col span="7" class="margin-top-10">
                </Col>
                <Col span="17">
                </Col>
                </Col>
                </Col>
                <Col span="4">
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_acl_role_1' type="primary" v-url="{url:'/rest/acl/role/list'}" @click="search_role">查询</Button>
                </Col>
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_acl_role_2' type="ghost" @click="reset_role">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Button id='btn_acl_role_3' class="margin-right-10" v-url="{url:'/rest/acl/role/create'}" type="primary" @click="role_create">新增角色</Button>
                </Col>
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_acl_role_1' border ref="selection" :columns="columns_roleList" :data="data_roleList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10"
                          :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <!--<Spin fix v-show="show_loading_apiList">-->
                <!--<Icon type="load-c" size=18 class="yop-spin-icon-load"></Icon>-->
                <!--<div>数据加载中...</div>-->
                <!--</Spin>-->
                <loading :show="show_loading_roleList"></loading>
            </Row>
            <Modal id="modal_acl_role_1" v-model="modal_create_role" width="800" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" v-show="currentStatus === 'create'">新增角色</span>
                    <span style="color:black" v-show="currentStatus === 'edit'">编辑角色</span>
                    <span style="color:black" v-show="currentStatus === 'preview'">查看</span>
                </p>
                <div>
                    <Form ref="form_role" :model="form_role" :rules="rule_role" :label-width="120">
                        <FormItem label="角色类型：" v-show="currentStatus === 'create' && currentRoleType === 'PLATFORM'" prop="roleType">
                            <!--<FormItem label="角色类型：" prop="roleType">-->
                            <common-select id="modal_acl_role_select_1" ref="select_roleType_m" @on-update="updateSelect_roleType_model"
                                           type="normal"
                                           holder="选择角色类型"
                                           keyWord="result"
                                           code="code"
                                           size="small"
                                           title="name"
                                           group="role_type"
                                           @on-loaded="select_callBack"
                                           :default="this.form_role.roleType"
                                           :uri="this.$store.state.select.role_type.uri"
                                           style="width:90%"></common-select>
                        </FormItem>
                        <FormItem label="角色类型：" v-show="currentStatus !== 'create' && currentRoleType === 'PLATFORM'">
                           {{form_role.roleTypeName}}
                        </FormItem>
                        <FormItem label="服务提供方：" v-show="currentStatus === 'create' && form_role.roleType === 'SP_BASED'" prop="spCode">
                        <!--<FormItem label="服务提供方："  prop="spCode">-->
                            <common-select id="modal_acl_role_select_2" ref="select_sp_m" @on-update="updateSelect_sp_model"
                                           type="combo"
                                           keyWord="result"
                                           holder="请选择服务提供方"
                                           code="spCode"
                                           title="spName"
                                           size="small"
                                           group="serviceSupplier"
                                           @on-loaded="select_callBack"
                                           :default="this.form_role.spCode"
                                           :uri="this.$store.state.select.serviceSupplier.uri"
                                           style="width:90%"></common-select>
                        </FormItem>
                        <FormItem label="服务提供方：" v-show="currentStatus !== 'create' && form_role.roleType === 'SP_BASED'">
                            {{form_role.spName}}({{form_role.spCode}})
                        </FormItem>
                        <FormItem label="角色编码：" v-show="currentStatus === 'create'" prop="roleCode">
                        <!--<FormItem label="角色编码：" prop="roleCode">-->
                            <Input id="modal_acl_role_input_1" type="text" size="small" v-model="form_role.roleCode" style="width:90%"><span slot="prepend">{{form_role.preview}}</span></Input>
                        </FormItem>
                        <FormItem label="角色编码：" v-show="currentStatus !== 'create'">
                            {{form_role.preview}}{{form_role.roleCode}}
                            <!--{{form_role.roleCode}}-->
                        </FormItem>
                        <p class="yop-explain-120" v-show="currentStatus === 'create'">支持小写字母、下划线，最长可输入32个字符</p>
                        <FormItem label="角色名称：" v-show="currentStatus !== 'preview'" prop="roleName">
                            <Input id="modal_acl_role_input_2" type="text" size="small" v-model="form_role.roleName" style="width:90%"></Input>
                        </FormItem>
                        <FormItem label="角色名称：" v-show="currentStatus === 'preview'">
                            {{form_role.roleName}}
                        </FormItem>
                        <p class="yop-explain-120" v-show="currentStatus !== 'preview'">支持大小写字母、汉字，最长可输入64个字符</p>
                        <FormItem label="资源配置：" prop="resource_config">
                            <Card dis-hover  style="height:300px;width: 90%">
                                <Scroll height="270">
                                    <Col v-for="(item, index) in form_role.resource_config" :key="index">
                                        <Col style="margin-left: 0;">
                                            <Checkbox v-model="item.checked" :disabled="currentStatus === 'preview'"  @on-change="handleCheckAll_parent(item)" >{{item.name}}</Checkbox>
                                        </Col>
                                        <Col v-for="(subItem, sINdex) in item.children" :key="sINdex">
                                            <Col style="margin-left: 15px;">
                                                <Checkbox v-model="subItem.checked" :disabled="currentStatus === 'preview'" @on-change="handleCheckAll(subItem)" >{{subItem.name}}</Checkbox>
                                            </Col>
                                            <Col style="margin-left: 30px;">
                                                <CheckboxGroup  v-model="subItem.checkedChildren" @on-change="checkAllGroupChange(subItem)">
                                                    <Checkbox v-for="(subSubItem, ssIndex) in subItem.children" :key="ssIndex" :label="subSubItem.id" :disabled="currentStatus === 'preview'">{{subSubItem.name}}</Checkbox>
                                                </CheckboxGroup>
                                            </Col>
                                        </Col>
                                    </Col>
                                </Scroll>
                            </Card>
                        </FormItem>
                    </Form>
                </div>
                <div slot="footer">
                    <Button v-show="currentStatus !== 'preview'" id="modal_acl_role_btn_1" type="primary" @click="ok_create_role('form_role')">确定
                    </Button>
                    <Button v-show="currentStatus !== 'preview'" id="modal_acl_role_btn_2" type="ghost" @click="cancel_create_role">取消</Button>
                    <Button v-show="currentStatus === 'preview'" id="modal_acl_role_btn_3" type="ghost" @click="cancel_create_role">关闭</Button>
                </div>
                <loading :show="show_loading_create_role"></loading>
            </Modal>
            <Modal id="modal_request_2" v-model="modal_batch_auth" width="800" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" >批量授权用户</span>
                </p>
                <div>
                    <Form ref="form_batch_auth" :model="form_role" :rules="rule_batch_auth" :label-width="120">
                        <FormItem v-show="currentRoleType === 'PLATFORM'" label="角色类型：">
                            {{form_role.roleTypeName}}
                        </FormItem>
                        <FormItem v-show="form_role === 'SP_BASED'" label="服务提供方：">
                            {{form_role.spName}}({{form_role.spCode}})
                        </FormItem>
                        <FormItem label="角色名称：">
                            {{form_role.roleName}}
                        </FormItem>
                        <FormItem label="岗位：" >
                            <common-select id="modal_acl_role_select_3" ref="position" @on-update="updateSelect_post_model"
                                           type="normal"
                                           keyWord="result"
                                           holder="选择用户岗位"
                                           code="code"
                                           title="name"
                                           size="small"
                                           group="position"
                                           @on-loaded="select_callBack"
                                           :default="this.data_select_post_model"
                                           :uri="this.$store.state.select.position.uri"
                                           style="width:90%"></common-select>
                        </FormItem>
                        <FormItem label="输入用户名：">
                            <el-select
                                    style="width:90%"
                                    v-model="form_role.inputUserCode"
                                    filterable
                                    clearable
                                    size="mini"
                                    allow-create
                                    clearable
                                    default-first-option
                                    placeholder="用户名">
                                <el-option
                                        v-for="item in data_userNameList_modal"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                            <!--<Input type="text" size="small" v-model="form_role.roleCode" style="width:90%"></Input>-->
                            <span><Button id="modal_acl_role_btn_4" type="primary" size="small" @click="addUser()">添加</Button></span>
                        </FormItem>
                        <FormItem label="确定用户名：" prop="inputUserCode">
                            <Card dis-hover  style="height:300px;width: 90%">
                                <Scroll height="270">
                                    <Tag v-for="item in auth_user_list" :key="item" :name="item" closable @on-close="handleClose2">{{item}}</Tag>
                                </Scroll>
                            </Card>
                        </FormItem>

                    </Form>
                </div>
                <div slot="footer">
                    <Button id="modal_acl_role_btn_5" type="primary" @click="ok_create_batch_auth('form_batch_auth')">确定
                    </Button>
                    <Button id="modal_acl_role_btn_6" type="ghost" @click="cancel_create_batch_auth">取消</Button>
                </div>

            </Modal>
            <Modal id="modal_acl_role_3" v-model="modal_user_list" width="1000" :closable="false" title="用户列表">
                <!--<p slot="header" style="color:#2d8cf0;">-->
                    <!--<span style="color:black" >用户列表</span>-->
                <!--</p>-->
                <div>
                    <Row type="flex" align="middle">
                        <Col span="19">
                        <Col span="10">
                        <Col span="7" class="margin-top-10">
                        <span>用户名:</span>
                        </Col>
                        <Col span="17">
                        <Input id="modal_acl_role_input_3" class="margin-top-5" v-model="data_user_code" placeholder="用户名" @on-enter="search_user"></Input>
                        </Col>
                        </Col>
                        <Col offset="1" span="10">
                        <Col span="7" class="margin-top-10">
                        <span>用户状态:</span>
                        </Col>
                        <Col span="17">
                        <common-select id="modal_acl_role_select_4" ref="select_user_status" @on-update="updateSelect_user_status"
                                       holder="选择状态类型（默认全部）"
                                       group="user_status"
                                       @on-loaded="select_callBack"
                                       :default="this.data_select_user_status"
                                       :uri="this.$store.state.select.user_status.uri"></common-select>
                        </Col>
                        </Col>
                        </Col>
                        <Col span="5">
                        <Col class="margin-top-10" span="11" style="text-align:center">
                        <Button id="modal_acl_role_btn_7" type="primary" v-url="{url:'/rest/acl/role/users'}" @click="search_user">查询</Button>
                        </Col>
                        <Col class="margin-top-10" span="11" style="text-align:center">
                        <Button id="modal_acl_role_btn_8" type="ghost" @click="reset_user">重置</Button>
                        </Col>
                        </Col>
                    </Row>
                    <Row class="margin-top-10">
                        <Col span="24">
                        <Table  border :columns="columns_roleList_user" :data="data_roleList_user"></Table>
                        <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                            <Page class="margin-top-10" style="float: right" :total="pageTotal_user" :page-size="10"
                                  :current="pageNo_user" show-elevator @on-change="pageRefresh_user"></Page>
                        </Tooltip>
                        </Col>
                        <!--<Spin fix v-show="show_loading_apiList">-->
                        <!--<Icon type="load-c" size=18 class="yop-spin-icon-load"></Icon>-->
                        <!--<div>数据加载中...</div>-->
                        <!--</Spin>-->
                        <loading :show="show_loading_userList"></loading>
                    </Row>
                </div>
                <div slot="footer">
                    <Button id="modal_acl_role_btn_9"  type="ghost" @click="cancel_create_user_list">关闭</Button>
                </div>
            </Modal>
        </Card>
    </div>
</template>

<script>
    import api from '../../../api/api';
    import commonSelect from '../../common-components/select-components/selectCommon';
    import loading from '../../my-components/loading/loading';
    import textEditor from '../../my-components/text-editor/text-editor-size';
    import util from '../../../libs/util';

    export default {
        name: 'role-manage',
        components: {
            loading,
            commonSelect,
            textEditor
        },
        mounted () {
            this.current_login_user_check();
        },
        data () {
            const validate_notNull = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('输入内容不能为空'));
                }else{
                    callback();
                }
            };
            // 角色编码校验规则
            const validate_role_code = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('角色编码不能为空'));
                }else if(util.formatCheck6(value)){
                    callback(new Error('格式不正确'));
                }else if(util.getLength(value) > 32){
                    callback(new Error('长度不能大于100'));
                }else{
                    // 添加重复校验接口
                    if(this.currentStatus === 'create'){
                        api.yop_acl_role_check({
                            roleCode : (this.form_role.preview +  value).trim()
                        }).then(
                            (response) =>{
                                if(response.data.status === 'success'){
                                    let result = response.data.data.result
                                    if(result){
                                        callback(new Error('该编码已存在'));
                                    }else{
                                        callback();
                                    }
                                }else{
                                    callback(new Error('验重失败'))
                                }
                            }
                        )
                    }else{
                        callback();
                    }
                }
            };
            // 角色名称校验规则
            const validate_role_name = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('角色名称不能为空'));
                }else if(util.formatCheck5(value)){
                    callback(new Error('格式不正确'));
                }else if(util.getLength(value) > 64){
                    callback(new Error('长度不能大于64'));
                }else{
                    callback();
                }
            };
            // 资源配置验证校验规则
            const validate_resource_config = (rule, value, callback) => {
                this.form_role.checkedRcs =[];
                this.checked_resources_handler(this.form_role.resource_config);
                if (!(this.form_role.checkedRcs && this.form_role.checkedRcs.length > 0)){
                    return callback(new Error('请选择资源配置'));
                }else{
                    callback();
                }
            };
            // 批量授权用户校验规则
            const validate_batch_auth = (rule, value, callback) => {
                if(!(this.auth_user_list && this.auth_user_list.length > 0)){
                    callback(new Error('确定用户名不能为空'));
                }else{
                    callback();
                }
            };
            return {
                data_user_code : '',
                // 当前角色类型
                currentRoleType : 'PLATFORM',
                //
                show_loading_userList : false,
                // 新增、编辑、查看页面loading
                show_loading_create_role : false,
                // 新建还是编辑数据绑定 true为新建 false为编辑
                currentStatus : 'create',
                // 下拉框个数
                count_select_related : 2,
                // 批量授权用户弹窗数据绑定
                modal_batch_auth : false,
                // 用户列表弹窗数据绑定
                modal_user_list : false,
                // 角色类型数据绑定
                data_select_roleType : '',
                // 服务提供方数据绑定
                data_select_sp : '',
                // 弹窗服务提供方数据绑定
                data_select_sp_model : '',
                // 角色编码数据绑定
                data_role_code : '',
                // 角色名称数据绑定
                data_role_name : '',
                // 角色列表表头数据绑定
                columns_roleList : [
                    {
                        title: '角色类型',
                        key: 'roleTypeName',
                        width: 150,
                        align: 'center'
                    },
                    {
                        title: '角色名称',
                        width: 180,
                        render: (h, params) => {
                                return h('div', [
                                    h('p', params.row.roleName),
                                    h('p', '(' + params.row.roleCode + ')')
                                ]);
                        },
                        align: 'center'
                    },
                    {
                        title: '服务提供方',
                        width: 180,
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.spName),
                                h('p', '(' + params.row.spCode + ')')
                            ]);
                        },
                        align: 'center'
                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ]);
                        },
                        width: 180,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [h('p', params.row.createTime),
                                h('p', params.row.lastModifyTime)]);
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'operations',
                        // width:  320,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/role/auth/add'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.role_batchAuth(params.row.roleCode);
                                        }
                                    }
                                }, '批量授权用户'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/role/update'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.role_modify(params.row.roleCode);
                                        }
                                    }
                                }, '编辑'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/role/delete'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.role_delete(params.row.roleCode);
                                        }
                                    }
                                }, '删除'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/role/detail'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.role_preview(params.row.roleCode);
                                        }
                                    }
                                }, '查看'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/role/users'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.role_userList(params.row.roleCode);
                                        }
                                    }
                                }, '用户列表')
                            ]);
                        }

                    }
                ],
                // 角色列表数据内容绑定
                data_roleList : [
                    // {
                    //     id : '1',
                    //     roleTypeName : 'SP角色模板',
                    //     roleName : 'SP管理员',
                    //     roleTypeCode : 'SPCODE',
                    //     roleCode : 'admin',
                    //     spName : '出款组',
                    //     spCode : 'chukuan',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-01 10:30:00'
                    // }
                ],
                // 职位数据绑定
                data_select_post_model : '',
                // 新增编辑弹窗数据绑定
                modal_create_role : false,
                // 列表loading数据绑定
                show_loading_roleList : false,
                // 总页数
                pageTotal : 10,
                // 当前页数
                pageNo : 1,
                // 表单数据绑定
                form_role : {
                    roleType : '',
                    roleTypeName : 'SP角色',
                    roleCode : '',
                    spCode: 'chukuan',
                    spName: '出款组',
                    roleName : '',
                    preview : 'PLATEFORM:',
                    resource_config : [
                        // {
                        //     id: 1,
                        // name : '服务分组管理',
                        // checked : false,
                        // children : [
                        //     {
                        //         name: '服务分组管理',
                        //         checked : false,
                        //         checkedChildren :[2],
                        //         children : [{
                        //             id : 2,
                        //             name :'新增',
                        //         }]
                        //     }
                        //     ]
                        // }
                    ],
                    inputUserCode : '',
                    checkedRcs : []
                },
                // 表单规则数据绑定
                rule_role : {
                    roleType: [
                        {required: true, message: '角色类型不能为空'},
                    ],
                    roleCode : [
                        {required: true, message: '角色编码不能为空',trigger: 'blur'},
                        { validator: validate_role_code, trigger: 'blur' }
                    ],
                    spCode : [
                        {required: true, message: '服务提供方不能为空'},
                    ],
                    roleName : [
                        {required: true, message: '角色名称不能为空'},
                        { validator: validate_role_name, trigger: 'blur' }
                    ],
                    resource_config : [
                        {required: true, message: '资源配置不能为空'},
                        { validator: validate_resource_config}

                    ]
                },
                // 表单规则数据绑定
                rule_batch_auth : {
                    inputUserCode: [
                        {required: true, message: '确定用户不能为空'},
                        { validator: validate_batch_auth, trigger: 'blur' }
                    ]
                },
                // 表格内选择数据绑定
                data_select_roleType_model : '',
                // 授权用户列表
                auth_user_list : [],
                // 用户状态选择数据绑定
                data_select_user_status : '',
                // 用户查询总数目
                pageTotal_user: 10,
                // 用户查询当前页码
                pageNo_user : 1,
                // 用户列表表头
                columns_roleList_user : [
                    {
                        title: '用户名',
                        width: 180,
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.userName),
                                h('p', '('+ params.row.userCode + ')')
                            ]);
                        },
                        align: 'center'
                    },
                    {
                        title: '岗位',
                        key: 'userPostName',
                        width: 150,
                        align: 'center'
                    },
                    {
                        title: '用户状态',
                        width: 100,
                        render: (h,params) => {
                            // if(params.row.status === 'SUCCESS'){
                            //     return h ('div','处理成功');
                            // }else{
                            //     return h ('div','处理失败');
                            // }
                            let color = 'red';
                            let status = '冻结';
                            if(params.row.status === 'NORMAL'){
                                color ='green';
                                status = '活动';
                            }
                            return h('div',[
                                h('Tag',{
                                    style:{
                                        align :'center'
                                    },
                                    props:{
                                        color: color
                                    }
                                },status)
                            ])

                        },
                        align: 'center'
                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ]);
                        },
                        width: 180,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [h('p', params.row.createTime),
                                h('p', params.row.lastModifyTime)]);
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'operations',
                        // width:  320,
                        render: (h, params) => {
                            let status = '冻结'
                            if(params.row.status === '活动'){
                                status = '冻结'
                            }else{
                                status = '解冻'
                            }
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/role/auth/delete'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.user_unassign_role(params.row.userCode);
                                        }
                                    }
                                }, '取消授权')
                            ]);
                        }
                    }
                ],
                // 用户列表数据
                data_roleList_user : [
                    // {
                    //     id : '1',
                    //     userName : '徐倩-3',
                    //     userCode : 'xuqian-3',
                    //     userPostCode : 'SPCODE',
                    //     userPostName : '产品经理',
                    //     status : '活动',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id : '2',
                    //     userName : '纪柏涛',
                    //     userCode : 'baitao.ji',
                    //     userPostCode : 'SPCODE',
                    //     userPostName : '开发负责人',
                    //     status : '冻结',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id : '3',
                    //     userName : '乐乐',
                    //     userCode : 'lele',
                    //     userPostCode : 'SPCODE',
                    //     userPostName : '开发工程师',
                    //     status : '活动',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id : '4',
                    //     userName : '王丹',
                    //     userCode : 'dan.wang-2',
                    //     userPostCode : 'SPCODE',
                    //     userPostName : '开发工程师',
                    //     status : '活动',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-01 10:30:00'
                    // }
                ],
                // 当前角色
                currentRoleCode : '',
                // 授权用户角色下拉框选中数据
                data_userName_modal : '',
                // 授权用户角色下拉框列表数据
                data_userNameList_modal : []
            }
        },
        methods :{
            // 角色类型数据更新
            updateSelect_roleType (val){
                this.data_select_roleType = val;
            },
            // 用户名单删除
            handleClose2 (event, name) {
                const index = this.auth_user_list.indexOf(name);
                this.auth_user_list.splice(index, 1);
            },
            // 弹窗角色类型数据更新
            updateSelect_roleType_model (val) {
                this.form_role.roleType = val;
                if(val === 'PLATFORM'){
                    this.form_role.preview = 'PLATFORM:'
                    this.form_role.spCode = '123';
                }else if(val === 'TEMPLET'){
                    this.form_role.preview = ''
                    this.form_role.spCode = '123';
                }else{
                    this.form_role.preview = ''
                }
                if(val){
                    this.resource_config_get(val);
                }
            },
            // 服务提供方数据更新
            updateSelect_sp (val) {
                this.data_select_sp = val;
            },
            // 弹窗服务提供方数据更新
            updateSelect_sp_model (val) {
                this.form_role.spCode = val;
                if(val && val !== 'bukeneng'){
                    this.form_role.preview = val+':'
                }else{
                    this.form_role.preview = ''
                }
            },
            // 职位数据更新
            updateSelect_post_model (val) {
                this.data_select_post_model = val;
                if(!!val){
                    api.yop_acl_user_by_position({position:val}).then(
                        (response) => {
                            if (response.data.status === 'success') {
                                let result= response.data.data.result;
                                this.data_userNameList_modal = [];
                                if(!!result){
                                    result.forEach(
                                        item =>{
                                            this.data_userNameList_modal.push({
                                                value : item,
                                                code: item
                                            })
                                        }
                                    )
                                }

                            } else {
                                this.$Modal.error({
                                    title: '错误',
                                    content: response.data.message
                                });
                            }
                        }
                    );
                }

            },
            // 查询角色函数
            search_role () {
                this.show_loading_roleList = true;
                let params = {
                    spCode: this.data_select_sp,
                    type: this.data_select_roleType,
                    roleCode: this.data_role_code.trim(),
                    roleName: this.data_role_name.trim(),
                    _pageNo: 1,
                    _pageSize: 10
                };
                util.paramFormat(params);
                api.yop_acl_role_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            // this.pageTotal = response.data.data.result.totalPageNum * 10;
                            if(response.data.data.result.items){
                                if(response.data.data.result.items.length < 10){
                                    this.pageTotal=response.data.data.result.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                            this.show_loading_roleList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_roleList = false;
                        }
                    }
                );
            },
            // 重置角色函数
            reset_role () {
                this.$refs.select_roleType.resetSelected();
                this.$refs.select_sp.resetSelected();
                this.data_select_roleType = '';
                this.data_select_sp = '' ;
                this.data_role_code = '';
                this.data_role_name = '';
            },
            // 添加角色函数
            role_create () {
                this.auto_set_roleType();
                this.formData_reset();
                this.currentStatus = 'create';
                this.resource_config_get('SP_BASED');
                if(localStorage.userType === 'PLATFORM'){
                    this.form_role.spCode = '123' ;
                }
                this.modal_create_role = true;
            },
            // 页面刷新
            pageRefresh (val) {
                this.show_loading_roleList = true;
                let params = {
                    spCode: this.data_select_sp,
                    type: this.data_select_roleType,
                    roleCode: this.data_role_code.trim(),
                    roleName: this.data_role_name.trim(),
                    _pageNo: val,
                    _pageSize: 10
                };
                util.paramFormat(params);
                api.yop_acl_role_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            // this.pageTotal = response.data.data.result.totalPageNum * 10;
                            if(response.data.data.result.items){
                                if(response.data.data.result.items.length < 10){
                                    this.pageTotal=response.data.data.result.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                            this.show_loading_roleList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_roleList = false;
                        }
                    }
                );
            },
            // 表格数据处理函数
            tableDataFormat (items) {
                this.data_roleList = [];
                for (var i in items) {
                    this.data_roleList.push({
                        id : items[i].id,
                        roleTypeName: util.empty_handler(this.data_name_handler(items[i].type, 'role_type')),
                        roleName : util.empty_handler(items[i].name),
                        // roleTypeCode : util.empty_handler(items[i].code),
                        roleCode : util.empty_handler(items[i].code),
                        spName : util.empty_handler(items[i].spName),
                        spCode : util.empty_handler(items[i].spCode),
                        createTime : util.empty_handler(items[i].createdTime),
                        lastModifyTime : util.empty_handler(items[i].lastModifiedTime),
                    });
                }
            },
            // 返回相应编码的名字
            data_name_handler (code, name) {
                if (code) {
                    let group = this.$store.state.select[name].data;
                    for (var i in group) {
                        if (group[i].value === code) {
                            return group[i].name;
                        }
                    }
                } else {
                    return '';
                }
            },
            // 确定创建角色按钮
            ok_create_role (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        this.form_role.checkedRcs = [];
                        this.checked_resources_handler(this.form_role.resource_config);
                        if(this.currentStatus === 'create'){
                            let  param= {
                                type : this.form_role.roleType,
                                code : (this.form_role.preview +this.form_role.roleCode).trim(),
                                name: this.form_role.roleName.trim(),
                                spCode : this.form_role.spCode,
                                resources : this.form_role.checkedRcs,
                            }
                            if(this.form_role.roleType !== 'SP_BASED'){
                                delete param['spCode'];
                            }
                            util.paramFormat(param);
                            api.yop_acl_role_create(param).then(
                                (response) =>{
                                    if(response.status === 'success'){
                                        this.$ypMsg.notice_success(this,'创建成功');
                                        this.modal_create_role = false;
                                        this.search_role();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        this.modal_create_role = false;
                                    }
                                }
                            )
                        }else{
                            let param = {
                                code : (this.form_role.preview +this.form_role.roleCode).trim(),
                                name: this.form_role.roleName.trim(),
                                resources : this.form_role.checkedRcs,
                                version : this.form_role.version,
                            }
                            util.paramFormat(param);
                            //可能需要对密钥处理剔除
                            api.yop_acl_role_update(param).then(
                                (response) =>{
                                    if(response.status === 'success'){
                                        this.$ypMsg.notice_success(this,'修改成功');
                                        this.modal_create_role = false;
                                        this.search_role();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        this.modal_create_role = false;
                                    }
                                }
                            )
                        }
                    } else {
                        this.$Message.error('请检查');
                    }
                })
                // this.modal_create_role = false;
            },
            // 已经选择的资源集合id获取
            checked_resources_handler (resources){
                if(resources && resources.length > 0){
                    resources.forEach(
                        item =>{
                            if(item.checked){
                                this.form_role.checkedRcs.push(item.id)
                            }
                            if(item.children && item.children.length > 0){
                                this.checked_resources_handler2(item.children);
                            }
                        }
                    )
                }
            },
            // 已经选择的资源集合id获取2
            checked_resources_handler2 (resources){
                if(resources && resources.length > 0){
                    resources.forEach(
                        item =>{
                            if(item.checked){
                                this.form_role.checkedRcs.push(item.id)
                            }
                            if(item.checkedChildren && item.checkedChildren.length > 0){
                                item.checkedChildren.forEach(
                                    subItem=>{
                                        // this.form_role.checkedRcs.push(this.name_to_id(subItem,item.children));
                                        this.form_role.checkedRcs.push(subItem);
                                    }
                                )
                            }
                        }
                    )
                }
            },
            // 根据name获取id
            name_to_id (name,data){
                for(var i in data){
                    if(data[i].name === name){
                        return data[i].id
                    }
                }
                return ''
            },
            // 取消创建角色按钮
            cancel_create_role () {
                this.modal_create_role = false;
                this.formData_reset();
            },
            // 下拉框加载完处理函数
            select_callBack () {
                this.count_select_related--;
                if (this.count_select_related === 0) {
                    this.search_role();
                }
            },
            // 批量授权用户
            role_batchAuth (code) {
                this.batch_auth_init();
                this.role_detail_information_handler(code);
                this.modal_batch_auth =true;
            },
            // 角色编辑
            role_modify (code) {
                this.currentStatus = 'edit';
                this.formData_reset();
                this.auto_set_roleType();
                this.role_detail_information_handler(code);
                this.modal_create_role = true;
            },
            // 角色删除
            role_delete (code) {
                this.$Modal.confirm({
                    title: '删除角色',
                    content: '确定删除该角色？',
                    'ok-text': '确认',
                    onOk: () => {
                        api.yop_acl_role_delete({roleCode: code}).then(
                            (response) => {
                                if (response.status === 'success') {
                                    this.$ypMsg.notice_success(this,'删除成功');
                                    this.search_role();
                                } else {
                                    this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                }
                            }
                        );
                    }
                });
            },
            // 查看角色
            role_preview (code) {
                this.currentStatus = 'preview';
                this.modal_create_role = true;
                this.show_loading_create_role = true;
                this.role_detail_information_handler(code);
            },
            // 详细信息detail查询
            role_detail_information_handler (code){
                api.yop_acl_role_detail({roleCode:code}).then(
                    (response)=>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.detail_format(result);
                            this.show_loading_create_role = false;
                        }else{
                            this.$ypMsg.notice_error(this,'角色详细信息获取失败,请刷新重试',response.data.message,response.data.solution);
                            this.modal_create_role= false;
                            this.show_loading_create_role = false;
                        }
                    }
                )
            },
            // 列表数据重置
            formData_reset () {
                this.$refs.select_roleType_m.resetSelected();
                this.$refs.select_sp_m.resetSelected();
                this.form_role.roleType = '';
                this.form_role.roleTypeName = '';
                this.form_role.spCode = '';
                this.form_role.spName = '';
                this.form_role.roleCode = '';
                this.form_role.preview = '';
                this.form_role.roleName = '';
                this.form_role.resource_config = [];
                this.formRole_reset();
            },
            // 表单状态还原
            formRole_reset(){
                this.$refs.form_role.resetFields();
            },
            // 详细信息处理函数
             detail_format (result) {
                let roleCode = '';
                let preview = '';
                let sp = util.empty_handler(result.code).split(':');
                if(sp.length === 1){
                    roleCode = sp[0]
                }else if(sp.length ===2){
                    preview = sp[0]+':'
                    roleCode = sp[1]
                }
                this.form_role.roleTypeName = util.empty_handler(this.data_name_handler(result.type, 'role_type'));
                this.form_role.roleType = util.empty_handler(result.type);
                this.form_role.roleCode = roleCode;
                this.form_role.spCode = util.empty_handler(result.spCode);
                this.form_role.preview = preview;
                this.form_role.spName = util.empty_handler(result.spName);
                this.form_role.roleName = util.empty_handler(result.name);
                // this.form_role.resource_config.length = 0;
                this.form_role.resource_config = this.resource_data_handler(result.resources);
                this.form_role.version = result.version;

            },
            // 资源管理数据处理1
            resource_data_handler (resources) {
                if(!!resources){
                    let returnData = []
                    resources.forEach(
                        item =>{
                            if(item.children && item.children.length > 0){
                                returnData.push(
                                    {
                                        id:item.id,
                                        name : item.name,
                                        checked : this.checked_result_handler(item.access),
                                        children : this.resource_data_handler2(item.children)
                                    }
                                )
                            }else{
                                returnData.push(
                                    {
                                        id:item.id,
                                        name : item.name,
                                        checked : this.checked_result_handler(item.access),
                                        children : []
                                    }
                                )
                            }
                        }
                    )
                    return returnData;
                }else{
                    return []
                }
            },
            // 资源管理数据处理2
            resource_data_handler2 (resources) {
                if(!!resources){
                    let returnData = []
                    resources.forEach(
                        item =>{
                            if(item.children && item.children.length > 0){
                                returnData.push(
                                    {
                                        id:item.id,
                                        name : item.name,
                                        checked : this.checked_result_handler(item.access),
                                        checkedChildren : this.checked_children_handler(item.children),
                                        children : this.resource_data_handler3(item.children)
                                    }
                                )
                            }else{
                                returnData.push(
                                    {
                                        id:item.id,
                                        name : item.name,
                                        checked : this.checked_result_handler(item.access),
                                        checkedChildren : [],
                                        children : []
                                    }
                                )
                            }
                        }
                    )
                    return returnData;
                }else{
                    return []
                }
            },
            // 选中结果返回
            checked_result_handler (result) {
                if(result){
                    return true;
                }else{
                    return false;
                }
            },
            // 子项全选勾中
            checked_all_children (children) {
                let returnData = []
                if(!!children){
                    children.forEach(
                        item =>{
                            returnData.push(
                                item.id
                            );
                        }
                    )
                    return returnData
                }else{
                    return []
                }
            },
            // 子项中勾选集合获取
            checked_children_handler (children) {
                let returnData = []
                if(!!children){
                    children.forEach(
                        item =>{
                            if(item.access){
                                returnData.push(
                                    item.id
                                )
                            }
                        }
                    )
                    return returnData
                }else{
                    return []
                }
            },
            // 资源管理数据处理3
            resource_data_handler3 (resources) {
                if(!!resources){
                    let returnData = []
                    resources.forEach(
                        item =>{
                                returnData.push(
                                    {
                                        id:item.id,
                                        name : item.name,
                                        checked : this.checked_result_handler(item.access),
                                    }
                                )
                        }
                    )
                    return returnData;
                }else{
                    return []
                }
            },
            // 用户列表
            role_userList (code) {
                this.currentRoleCode = code;
                this.search_user();
                this.modal_user_list = true;
            },
            // 确定批量添加用户
            ok_create_batch_auth (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                            let  param= {
                                code : this.form_role.preview+this.form_role.roleCode,
                                optCodes : this.auth_user_list
                            }
                            util.paramFormat(param);
                            api.yop_acl_role_auth_add(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        this.$ypMsg.notice_success(this,'批量用户授权成功');
                                        this.modal_batch_auth = false;
                                        this.search_interface();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        this.modal_batch_auth = false;
                                    }
                                }
                            )
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 取消批量添加用户
            cancel_create_batch_auth () {
                this.modal_batch_auth =false;
            },
            // 添加用户按钮数据绑定
            addUser () {
                if(!(!!this.form_role.inputUserCode)){
                    this.$Message.warning({
                        content: '用户名不能为空',
                        duration: 5
                    });
                }else{
                    api.yop_acl_user_check_native_user({name: this.form_role.inputUserCode}).then(
                        (response) => {
                            if (response.data.status === 'success') {
                                if(response.data.data.result){
                                    if(this.auth_user_list.indexOf(this.form_role.inputUserCode) === -1){
                                        this.auth_user_list.push(this.form_role.inputUserCode);
                                    }else{
                                        this.$Message.warning({
                                            content: '该员工用户名已存在列表中，不要重复添加',
                                            duration: 5
                                        });
                                    }
                                }else{
                                    this.$Modal.error({
                                        title: '用户不存在',
                                        content: '该用户不存在系统中'
                                    });
                                }
                            } else {
                                this.$Modal.error({
                                    title: '校验错误',
                                    content: response.data.message
                                });
                            }
                        }
                    );
                }
                // this.auth_user_list.push(this.form_role.roleCode);
            },
            // 用户列表结果查询
            search_user () {
                this.show_loading_userList = true;
                let params = {
                    roleCode :this.currentRoleCode,
                    name: this.data_user_code.trim(),
                    status: this.data_select_user_status,
                    _pageNo: 1,
                    _pageSize: 10
                };
                util.paramFormat(params);
                api.yop_acl_role_users(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat_user(response.data.data.result.items);
                            this.pageNo_user = response.data.data.result.pageNo;
                            // this.pageTotal_user = response.data.data.result.totalPageNum * 10;
                            if(response.data.data.result.items){
                                if(response.data.data.result.items.length < 10){
                                    this.pageTotal_user=response.data.data.result.items.length;
                                }else{
                                    this.pageTotal_user=NaN;
                                }
                            }else{
                                this.pageTotal_user=NaN;
                            }
                            this.show_loading_userList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_userList = false;
                        }
                    }
                );
            },
            // 表格数据处理函数
            tableDataFormat_user (items) {
                this.data_roleList_user = [];
                for (var i in items) {
                    this.data_roleList_user.push({
                        userName: util.empty_handler(items[i].optName),
                        userCode : util.empty_handler(items[i].optCode),
                        // roleTypeCode : util.empty_handler(items[i].code),
                        userPostCode : util.empty_handler(items[i].position),
                        userPostName : util.empty_handler(this.data_name_handler(items[i].position, 'position')),
                        status : util.empty_handler(items[i].status),
                        createTime : util.empty_handler(items[i].createdDate),
                        lastModifyTime : util.empty_handler(items[i].lastModifiedDate),
                    });
                }
            },
            // 用户状态下拉框加载完成函数
            updateSelect_user_status (val) {
                this.data_select_user_status =val
            },
            // 用户列表重置
            reset_user () {
                this.data_user_code = '';
                this.$refs.select_user_status.resetSelected();
            },
            // 分页刷新页面查询函数
            pageRefresh_user (val) {
                this.show_loading_userList = true;
                let params = {
                    roleCode :this.currentRoleCode,
                    name: this.data_user_code.trim(),
                    status: this.data_select_user_status,
                    _pageNo: 1,
                    _pageSize: val
                };
                util.paramFormat(params);
                api.yop_acl_role_users(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat_user(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            this.pageTotal = response.data.data.result.totalPageNum * 10;
                            this.show_loading_userList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_userList = false;
                        }
                    }
                );
            },
            // 窗口关闭
            cancel_create_user_list () {
                this.modal_user_list = false;
            },
            // 取消授权
            user_unassign_role (code) {
                let  param= {
                    code : this.currentRoleCode,
                    optCodes : [code]
                }
                util.paramFormat(param);
                api.yop_acl_role_auth_delete(param).then(
                    (response) =>{
                        //需要填写
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'用户取消授权成功');
                            this.search_user();
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                    }
                )
            },
            // 确认当前登录用户
            current_login_user_check () {
                if(localStorage.userType === 'PLATFORM'){
                    this.currentRoleType = 'PLATFORM'
                }else{
                    this.currentRoleType = 'SP_BASED'
                }
            },
            // 表格内角色类型根据当前用户自动设置
            auto_set_roleType () {
                if(this.currentRoleType = 'PLATFORM'){
                    this.form_role.roleType = '';
                }else{
                    this.form_role.roleType = 'SP_BASED';
                }
            },
            // 点击判断
            handleCheckAll_parent(val){
                // val.checked = !val.checked;
                if(val.checked){
                     this.checked_children(val.children,true);
                }else{
                     this.checked_children(val.children,false);
                }
            },
            // 点击判断
            handleCheckAll(val){
                // val.checked = !val.checked;
                // console.log(val);
                if(val.checked){
                    val.checkedChildren = this.checked_all_children(val.children);
                }else{
                    val.checkedChildren = [];
                }
                this.parent_node_status();
            },
            // 子项点击影响
            checkAllGroupChange (data) {
                if(data.children){
                    if(data.children.length === data.checkedChildren.length){
                        data.checked = true;
                    }else{
                        data.checked = false;
                    }
                }else{
                    data.checked = false;
                }
                this.parent_node_status();
            },
            // 将所有子节点统一设定
            checked_children (children,check) {
                if(!!children){
                    children.forEach(
                        item =>{
                            item.checked = check;
                            this.handleCheckAll(item);
                            // this.handleCheckAll_parent(item);
                        }
                    )
                }
            },
            // 根节点判断状态置换
            parent_node_status () {
                if(!!this.form_role.resource_config){
                    this.form_role.resource_config.forEach(
                        item =>{
                            if(!!item.children){
                                if(this.parent_child_check(item.children)){
                                    item.checked = true;
                                }else{
                                    item.checked = false;
                                }
                            }
                        }
                    )
                }
            },
            // 跟节点子项判断
            parent_child_check (children) {
                let result = true;
                if(!!children){
                    for(var i in children){
                        if(!children[i].checked){
                            result = false;
                            break;
                        }
                    }
                }
                return result;
            },
            // 资源配置信息获取
            resource_config_get (code) {
                api.yop_acl_resource_by_role_type({roleType:code}).then(
                    (response)=>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.form_role.resource_config = this.resource_data_handler(result);
                        }else{
                            this.$ypMsg.notice_error(this,'资源配置信息获取失败,请刷新重试',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            // 批量授权用户初始化
            batch_auth_init () {
                this.$refs.position.resetSelected();
                this.form_role.inputUserCode = '';
                this.auth_user_list = [];
            }
        }
    };
</script>

<style scoped>

</style>
