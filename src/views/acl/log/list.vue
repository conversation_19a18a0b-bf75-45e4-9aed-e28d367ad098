<style lang="less">
@import "../../../styles/common.less";
@import "../../API_Management_Open/api_Mangement_Open.less";
.demo-badge-alone {
  background: #5cb85c !important;
}
.round {
  width: 16px;
  height: 16px;
  display: inline-block;
  font-size: 20px;
  line-height: 16px;
  text-align: center;
  color: #f00;
  text-decoration: none;
}
// .ivu-select-single .ivu-select-selection {
//   height:inherit;
// }
.ivu-table-border td, .ivu-table-border th {
  position:relative;
}
.overflow {
  width:90%;
  max-height:200px;
  overflow-y:scroll;
  margin:0 auto;
  border: 1px solid #eee;
    padding:10px;
}
.detailTitle {
  height:34px;
  line-height:34px;
}
</style>
<template>
  <div style="background: #eee;padding:8px">
    <Card :bordered="false">
      <Row type="flex" align="middle">
          <Col span="24">
            <Col span="12">
                <Col  span="3"  style="max-width:70px;">
                      <Col class="margin-top-10">请求时间:</Col>
                </Col>
                <Col span="21">
                  <Col span="5">
                  <DatePicker class="margin-top-5" v-model="requestDateStart" :options="optionsStart" :clearable="false" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择起始日期" ></DatePicker>
                  </Col>
                  <Col span="5">
                  <TimePicker class="margin-top-5" v-model="requestTimeStart" :clearable="false" format="HH:mm:ss" placeholder="选择起始时间"></TimePicker>
                  </Col>
                  <Col class="margin-top-10" span="1"> &nbsp;—</Col>
                  <Col span="5">
                  <DatePicker class="margin-top-5" v-model="requestDateEnd" :clearable="false" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择结束日期" ></DatePicker>
                  </Col>
                  <Col span="5">
                  <TimePicker class="margin-top-5" v-model="requestTimeEnd" :clearable="false" format="HH:mm:ss" placeholder="选择结束时间"></TimePicker>
                  </Col>
                </Col>
            </Col>
            <Col span="5"   >
              <Col span="6" class="margin-top-10"  style="max-width:57px;">
                <span>功能:</span>
              </Col>
              <Col span="18">
                <Cascader class="margin-top-5" ref="select_acl_log_top_resource" style="max-width:300px;" id='select_apiM_1' change-on-select clearable placeholder="请选择（默认全部）" :data="data_acl_log_top_resource" v-model="data_select_acl_log_top_resource"></Cascader>
              </Col>
            </Col>
            <Col span="5" offset="1">
              <Col span="6" class="margin-top-10" style="max-width:70px;">
                <span>操作人:</span>
              </Col>
              <Col span="18">
                <Input
                style="max-width:300px;"
                  id="input_app_1"
                  class="margin-top-5"
                  v-model="data_log_operator"
                  placeholder="操作人"
                  @on-enter="search"
                  clearable
                ></Input>
              </Col>
            </Col>
          </Col>
            
          </Col>
        <Col span="23" class="margin-top-20"  style="text-align:right;margin-bottom:20px;">
            <Button id="btn_app_1" type="primary" v-url="{url:'/rest/acl/log/list'}"  @click="search">查询</Button>
            <Button id="btn_app_2" type="ghost" @click="reset_search">重置</Button>
        </Col>
      </Row>
      <Row class="margin-top-10">
        <Col span="24">
          <Table id="table_1" border :columns="columns_acl_log" :data="data_acl_log"></Table>
          <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
            <Page
              class="margin-top-10"
              style="float: right"
              :total="pageTotal"
              :page-size="10"
              :current="pageNo"
              show-elevator
              @on-change="page_refresh"
            ></Page>
          </Tooltip>
        </Col>
        <loading :show="show_loading_acl_log"></loading>
      </Row>
    </Card>
    <Modal v-model="modal_Show_detail" width="700px" >
            <p slot="header">
                <span  class="margin-right-10">详情</span>
            </p>
            <div >
              <p class="detailTitle">请求内容:</p>
              <div  class="overflow">
                <pre style="white-space:pre-wrap;word-wrap:break-word">{{this.data_invoke_detail_part1}}</pre>
              </div>
            </div>
            <div v-show="this.data_invoke_detail_part2">
              <p class="detailTitle">异常报错:</p>
              <div class="overflow">
                <p  style="white-space:pre-wrap;word-wrap:break-word">{{this.data_invoke_detail_part2}}</p>
              </div>
            </div>
            <div slot="footer">
                <Button id='modal_invokeL_btn_1' type="primary" @click="ok_detail">关闭</Button>
            </div>
            <loading :show="show_loading_detail"></loading>
        </Modal>
  </div>
</template>
<script>
import loading from "../../my-components/loading/loading";
import commonSelect from "../../common-components/select-components/selectCommon";
import util from "../../../libs/util";
import api from "../../../api/api";
export default {
  name: "app-list",
  components: {
    loading,
    commonSelect,
  },
  data() {
    return {
      /**
       * 应用列表部分变量
       */
      // 应用列表loading
      show_loading_acl_log: false,
      // 下拉框-应用类型数据绑定
      data_select_acl_log_top_resource: [],
      left:"",
      right:"",
      // 搜索时间框-创建时间开始时间数据绑定
      dateStart: "",
      // 搜索时间框-创建时间结束时间数据绑定
      dateEnd: "",
      // 下拉框加载统计
      count_select_related: 2,
      // 应用列表表头配置
      columns_acl_log: [
          {
          title: "操作人",
          key: "status",
          align: "center",
          render: (h, params) => {
            let color = "red";
            if (params.row.status === "SUCCESS") {
              color = "green";
            } else if (params.row.status === "FORBIDDEN" || params.row.status === "UNAUTHORIZED") {
              color = "orange";
            }else if (params.row.status === "UNKNOWN") {
              color = "red";
            } else {
              // NOT_FOUND  
              color = "grey";
            }
            return h("div", [
              h(
                "Tag",
                {
                  style: {
                    align: "center",
                    width: "3px",
                    position:"absolute",
                    left:0,
                    top:0,
                    height:"100%",
                    padding:0,
                    margin:0
                  },
                  props: {
                    color: color
                  }
                },
                ""
              ),
              h("p", params.row.operator),
              h("p", params.row.requestIp)
            ]);
          }
        },
        {
          title: "功能",
          key: "resourceName",
          align: "center"
        },
        {
          title: "操作时间",
          key: "operDate",
          align: "center"
        },
        {
          title: "耗时（ms）",
          key: "latency",
          align: "center",
        },
        {
          title: "操作",
          align: "center",
          key: "operations",
          render: (h, params) => {
            return h("div", [
              h(
                "Button",
                {
                  props: {
                    type: "text",
                    size: "small"
                  },
                  directives: [
                    {
                      name: "url",
                      value: { url: "/rest/acl/log/detail" }
                    }
                  ],
                  on: {
                    click: () => {
                      this.invoke_detail(params.row.id,params.row.operDate);
                    }
                  }
                },
                "详情"
              )
            ]);
          }
        }
      ],
      // 应用列表数据
      data_acl_log: [],
      // 总数
      pageTotal: 10,
      // 当前页码
      pageNo: 1,
      // 请求开始日期
      requestDateStart: '',
      // 请求开始日期额外选项
      optionsStart :{
          disabledDate (date) {
              return date && date.valueOf() > Date.now();
          }
      },
      // 请求开始时间
      requestTimeStart: '00:00:00',
      // 请求结束日期
      requestDateEnd : '',
      // 请求结束时间
      requestTimeEnd : '23:59:59',
      modal_Show_detail: false,
      data_log_operator:"",
      data_acl_log_top_resource:[],
              
      // 详细信息表格数据第一部分
      data_invoke_detail_part1 : "",
      // 详细信息表格数据第二部分
      data_invoke_detail_part2 : "",
      show_loading_detail : false,


    };
  },
  methods: {
    // 初始化页面
    init () {
      this.requestDateStart = util.date_oneDay().after;
      console.log(this.requestDateStart)
      this.requestDateEnd =util.date_oneDay().after;
      this.search()
      this.get_acl_log_top_resource()
    },
    // 数据检索
    search() {
      let datebefore = this.date_real(this.requestDateStart,this.requestTimeStart);
      let dateafter = this.date_real(this.requestDateEnd,this.requestTimeEnd);
      console.log(typeof datebefore,dateafter.getMonth())
      if(datebefore.getMonth() != dateafter.getMonth()){
          this.$Modal.error({
              title: '错误',
              content: '日期不能跨月'
          });
      }else
      if(dateafter.getTime() < datebefore.getTime()){
          this.$Modal.error({
              title: '错误',
              content: '结束日期不能小于开始日期'
          });
      }
      // else if(this.date_space() > 1){
      //     this.$Modal.error({
      //         title: '错误',
      //         content: '结束日期和开始日期间隔不能超过1天'
      //     });
      // }
      // else if(this.date_space() > 1){
      //     this.$Modal.error({
      //         title: '错误',
      //         content: '结束日期和开始日期间隔不能超过1天'
      //     });
      // }
      else{
            let params = this.search_pre_option();
            this.list_handler(params);
      }
     
    },
    get_acl_log_top_resource(){
        // api分组列表
        api.yop_dashboard_acl_log_resource().then(
            (response) => {
                let resultTemp = response.data.data.result.children
                this.data_acl_log_top_resource = this.resetList(resultTemp);
            });
        
    },
    resetList(list) {
      if(!list || list.length < 1) return []
      const arr = []
      for(var i in list){
          arr.push({
              value: list[i].id,
              label: list[i].resourceName,
              name: list[i].resourceName,
              children: this.resetList(list[i].children)
          })
      }
      return arr
    },
    // 检索条件初始化
    reset_search() {
      this.data_log_operator = "";
      this.data_select_acl_log_top_resource = [];
      this.left = "";
      this.right = "";
      this.requestDateStart = util.date_oneDay().after;
      this.requestTimeStart = '00:00:00';
      this.requestDateEnd = util.date_oneDay().after;
      this.requestTimeEnd = '23:59:59';
    },
    // 一周前日期生成
    date_oneWeek () {
        let now = new Date();
        let date = new Date(now.getTime() - 7 * 24 * 3600 * 1000);
        return util.dateFormat(date);
    },
    // 日期区间
    date_space () {
        let dateAfter = this.date_real(this.requestDateEnd,this.requestTimeEnd);
        let dateBefore = this.date_real(this.requestDateStart,this.requestTimeStart);
        let days = Math.ceil((dateAfter.getTime() - dateBefore.getTime()+1000)/(24 * 3600 * 1000));
        return days;
    },
    // 请求日期开始具体时间返回
    date_real (date,time) {
        let dateBefore = util.dateFormat_sep(date,time);
        let str = dateBefore.toString();
        str = str.replace(/-/g, "/");
        let odateBefore = new Date(str);
        return odateBefore;
    },
    date_real_before (){
        let dateBefore = util.dateFormat_sep(this.requestDateStart,this.requestTimeStart);
        console.log(dateBefore);
        let str = dateBefore.toString();
        str = str.replace("/-/g", "/");
        let odateBefore = new Date(str);
        return odateBefore;
    },
    // 请求日期结束具体时间返回
    date_real_after () {
        let timesAfter =new Date(this.requestTimeEnd);
        let dateAfter = new Date(this.requestDateEnd);
        dateAfter.setHours(timesAfter.getHours(),timesAfter.getMinutes(),timesAfter.getSeconds());
        return dateAfter;
    },
// 表格内容改动时
    handleselection(value){
        this.multiSelectedData =value;
    },
    // 请求参数生成前序操作
    search_pre_option() {
      var spot = ''
      var length = this.data_select_acl_log_top_resource.length
      if( length > 0) {
        spot = this.data_select_acl_log_top_resource[length - 1]
      }

      this.show_loading_acl_log = true;
      let params = {
        operator: this.data_log_operator.trim(),
        operStartDate: util.dateFormat_sep(this.requestDateStart,this.requestTimeStart),
        operEndDate: util.dateFormat_sep(this.requestDateEnd,this.requestTimeEnd),
        _pageNo: 1,
        _pageSize: 10,
        id: spot,
      };
      util.paramFormat(params);
      return params;
    },
    // 列表刷新
    page_refresh(val) {
      let params = this.search_pre_option();
      if (val) {
        params._pageNo = val;
      }
      this.list_handler(params);
    },
    // 列表请求执行函数
    list_handler(params) {
      api.yop_acl_log_list(params).then(response => {
        if (response.data.status === "success") {
          this.tableDataFormat(response.data.data.page.items);
          this.pageNo = response.data.data.page.pageNo;
            if(response.data.data.page.items){
                if(response.data.data.page.items.length < 10){
                    this.pageTotal=response.data.data.page.items.length;
                }else{
                    this.pageTotal=NaN;
                }
            }else{
                this.pageTotal=NaN;
            }
        } else {
          this.$ypMsg.notice_error(
            this,
            "错误",
            response.data.message,
            response.data.solution
          );
        }
        this.show_loading_acl_log = false;
      });
    },
    // 表格数据处理函数
    tableDataFormat(items) {
      this.data_acl_log = [];
      for (var i in items) {
        this.data_acl_log.push({
          operDate: util.empty_handler(items[i].operDate),
          resourceName: util.empty_handler(items[i].resourceName),
          operator: util.empty_handler(items[i].operator),
          requestIp: util.empty_handler(items[i].requestIp),
          status: util.empty_handler(items[i].status),
          latency: items[i].latency,
          id: util.empty_handler(items[i].id),
        });
      }
    },
    // 下拉框加载完处理函数
    select_callBack() {
      this.count_select_related--;
      if (this.count_select_related === 0) {
        this.search();
      }
    },
    // json格式化
    syntaxHighlight(json) {
        if (typeof json != 'string') {
            json = JSON.stringify(json, undefined, 4);
        }
        return json
    },
    isJSON(str) {
      if (typeof str == 'string') {
          try {
              var obj=JSON.parse(str);
              if(typeof obj == 'object' && obj ){
                  return true;
              }else{
                  return false;
              }

          } catch(e) {
              console.log('error：'+str+'!!!'+e);
              return false;
          }
      }
      console.log('It is not a string!')
    },
    // 详情
    invoke_detail(id,time) {
      var that = this
      let params  = {
            id : id,
            operDate : time
        };
        api.yop_acl_log_detail(params).then(
            (response) =>{
                var result = response.data.data.result;
                this.modal_Show_detail = true;
                this.show_loading_detail = true;
                if (response.data.status === 'success') {
                    if(result.content){
                      if(that.isJSON(result.content)){
                        this.data_invoke_detail_part1 = this.syntaxHighlight(JSON.parse(result.content));
                      }else{
                        this.data_invoke_detail_part1 = result.content;
                      }
                    }else{
                      this.data_invoke_detail_part1 = "无请求内容"
                    }
                    if(result.detail){
                      this.data_invoke_detail_part2 = result.detail;
                    }else{
                      this.data_invoke_detail_part2 = "";
                    }
                    this.show_loading_detail = false;
                } else {
                    this.$Modal.error({
                        title: '查看详情获取失败',
                        content: response.data.message
                    });
                    this.show_loading_detail = false;
                    this.modal_Show_detail = false;
                }

            }
        );
    },
    // 详情按钮确认
    ok_detail () {
        this.modal_Show_detail = false;
    },
  },
  mounted() {
        this.init();
  },
}
</script>

<style scoped>
</style>
