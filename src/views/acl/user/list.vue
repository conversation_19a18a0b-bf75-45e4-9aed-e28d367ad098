<style lang="less">
    @import '../../../styles/common.less';
    @import '../../API_Management_Open/api_Mangement_Open.less';

    .demo-badge-alone {
        background: #5cb85c !important;
    }

    .round {
        width: 16px;
        height: 16px;
        display: inline-block;
        font-size: 20px;
        line-heigth: 16px;
        text-align: center;
        color: #f00;
        text-decoration: none
    }
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">
                <Col span="7">
                <Col span="7" class="margin-top-10">
                <span>岗位:</span>
                </Col>
                <Col span="17">
                <common-select id="select_acl_user_1" ref="select_post"  @on-update="updateSelect_Post"
                               type="normal"
                               holder="选择用户岗位（默认全部）"
                               keyWord="result"
                               code="code"
                               title="name"
                               group="position"
                               @on-loaded="select_callBack"
                               :default="this.data_select_Post"
                               :uri="this.$store.state.select.position.uri"></common-select>
                </Col>
                </Col>

                <Col offset="1" span="7">
                <Col span="7" class="margin-top-10">
                <span>用户名:</span>
                </Col>
                <Col span="17">
                <Input id="input_acl_user_1" class="margin-top-5" v-model="data_user_code" placeholder="用户名"
                       @on-enter="search_user"></Input>
                </Col>
                </Col>

                <Col offset="1" span="7">
                <Col span="7" class="margin-top-10">
                <span>用户状态:</span>
                </Col>
                <Col span="17">
                <Select id="select_acl_user_2" ref="select_user_status" class="margin-top-5" v-model="data_select_userStatus" filterable clearable placeholder="选择状态类型（默认全部）">
                    <Option v-for="item in data_userStatus_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>


                </Col>
                <Col span="4">
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_acl_user_1' v-url="{url:'/rest/acl/user/list'}" type="primary"  @click="search_user">查询</Button>
                </Col>
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_acl_user_2' type="ghost" @click="reset_user">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Button id='btn_acl_user_3' class="margin-right-10" v-url="{url:'/rest/acl/user/create'}"  type="primary" @click="user_create">新增用户并授权</Button>
                </Col>
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_acl_user_1' border ref="selection" :columns="columns_userList" :data="data_userList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10"
                          :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <!--<Spin fix v-show="show_loading_apiList">-->
                <!--<Icon type="load-c" size=18 class="yop-spin-icon-load"></Icon>-->
                <!--<div>数据加载中...</div>-->
                <!--</Spin>-->
                <loading :show="show_loading_userList"></loading>
            </Row>
            <Modal id="modal_acl_user_1" v-model="modal_edit_user" width="800" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" >编辑用户</span>
                </p>
                <div>
                    <Form ref="form_user_edit" :model="form_user" :rules="rule_user_edit" :label-width="120">
                        <FormItem label="用户名：" >
                            {{form_user.userName}}({{form_user.userCode}})
                        </FormItem>
                        <FormItem label="邮箱：">
                            {{form_user.mailbox}}
                        </FormItem>
                        <FormItem label="岗位：" prop="post">
                            <common-select id="modal_acl_user_select_1" ref="position_m" @on-update="updateSelect_post_model"
                                           type="normal"
                                           keyWord="result"
                                           holder="选择用户岗位"
                                           code="code"
                                           title="name"
                                           size="small"
                                           group="position"
                                           @on-loaded="select_callBack"
                                           :default="this.form_user.post"
                                           :uri="this.$store.state.select.position.uri"
                                           style="width:90%"></common-select>
                        </FormItem>
                    </Form>
                </div>
                <div slot="footer">
                    <Button id="modal_acl_user_btn_1" type="primary" :loading="edit_user_auth_loading" @click="ok_edit_user_auth('form_user_edit')">确定
                    </Button>
                    <Button id="modal_acl_user_btn_2" type="ghost" @click="cancel_edit_user_auth">取消</Button>
                </div>
            </Modal>
            <Modal id="modal_acl_user_2" v-model="modal_new_auth" width="800" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" v-show="currentStatus">新增用户并授权</span>
                    <span style="color:black" v-show="!currentStatus">分配角色</span>
                </p>
                <div>
                    <Form ref="form_user" :model="form_user" :rules="rule_user" :label-width="120">
                        <FormItem label="用户类型：" v-show="currentStatus && currentUserType === 'PLATFORM'" prop="userType">
                            <Select id="modal_acl_user_select_2" ref="sp_user_type_m" v-model="form_user.userType" filterable clearable placeholder="请选择用户类型" size="small" style="width:90%" @on-change="changeType_modal">
                                <Option v-for="item in data_userType_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="用户类型：" v-show="!currentStatus && currentUserType === 'PLATFORM'">
                            {{form_user.userTypeName}}
                        </FormItem>
                        <FormItem label="服务提供方：" v-show="!currentStatus">
                            <common-select-mult-param  id="modal_acl_user_select_3" ref="sp_edit_m" @on-update="updateSelect_sp_model_edit"
                                                type="combo"
                                                keyWord="result"
                                                holder="请选择服务提供方"
                                                code="spCode"
                                                title="spName"
                                                size="small"
                                                group="sp_unassinged"
                                                @on-loaded="select_callBack"
                                                :default="this.form_user.spCode_edit"
                                                :uri="this.$store.state.select.sp_unassinged.uri"
                                                style="width:90%"></common-select-mult-param>
                        </FormItem>
                        <FormItem label="服务提供方：" v-show="currentStatus">
                            <common-select-mult-param id="modal_acl_user_select_4" ref="sp_create_m" @on-update="updateSelect_sp_model"
                                           type="combo"
                                           keyWord="result"
                                           holder="请选择服务提供方"
                                           code="spCode"
                                           title="spName"
                                           size="small"
                                           group="sp_has_right"
                                           @on-loaded="select_callBack"
                                           :default="this.form_user.spCode"
                                           :uri="this.$store.state.select.sp_has_right.uri"
                                           style="width:90%"></common-select-mult-param>
                        </FormItem>
                        <FormItem label="输入用户名：" v-show="currentStatus">
                            <Input id="modal_acl_user_input_1" type="text" size="small" v-model="form_user.inputUserName" style="width:90%"></Input><span class="margin-left-5">
                            <Button id="modal_acl_user_btn_3" type="primary" size="small" @click="addUser()">添加</Button></span>
                        </FormItem>
                        <FormItem label="确定用户名：" v-show="currentStatus">
                            <Card dis-hover  style="height:180px;width: 90%">
                                <Scroll height="162">
                                    <Tag v-for="item in auth_user_list" :key="item" :name="item" closable @on-close="handleClose2">{{item}}</Tag>
                                </Scroll>
                            </Card>
                        </FormItem>
                        <FormItem label="用户名：" v-show="!currentStatus">
                            {{form_user.userName}}({{form_user.userCode}})
                        </FormItem>
                        <FormItem label="分配角色：">
                            <Card dis-hover  style="height:300px;width: 90%">
                                <Scroll height="270">
                                    <Table id="modal_acl_user_table_1" border  :columns="columns_roleList" :data="form_user.roleInfo"></Table>
                                </Scroll>
                            </Card>
                        </FormItem>
                    </Form>
                </div>
                <div slot="footer">
                    <Button id="modal_acl_user_btn_4" type="primary" @click="ok_create_user_auth('form_user')">确定
                    </Button>
                    <Button id="modal_acl_user_btn_5" type="ghost" @click="cancel_create_user_auth">取消</Button>
                </div>
            </Modal>
            <Modal id="modal_acl_user_3" v-model="modal_edit_userType" width="600" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" >修改用户类型</span>
                </p>
                <div class="yop-margin-bottom-10" style="font-size: 14px;font-weight: bold">
                    用户类型修改后，则自动清除原用户分配角色资源，确定修改该用户类型？
                </div>
                <div>
                    <Form ref="form_user_type_modify" :model="form_user" :rules="rule_user_type_modify" :label-width="100">
                        <FormItem label="用户类型：" prop="userType">
                            <Select id="modal_acl_user_select_5" v-model="form_user.userType" filterable clearable placeholder="请选择用户类型" size="small" style="width:90%">
                                <Option v-for="item in data_userType_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                            </Select>
                        </FormItem>
                    </Form>
                </div>
                <div slot="footer">
                    <Button id="modal_acl_user_btn_6" type="primary" @click="ok_edit_userType('form_user_type_modify')">确定
                    </Button>
                    <Button id="modal_acl_user_btn_7" type="ghost" @click="cancel_edit_userType">取消</Button>
                </div>
            </Modal>
            <Modal id="modal_user_4" v-model="modal_preview" width="800" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" >查看</span>
                </p>
                <div>
                    <Form ref="form_user_preview" :model="form_user"  :label-width="120">

                        <FormItem label="用户类型：">
                            {{form_user.userTypeName}}
                        </FormItem>
                        <FormItem label="服务提供方：" v-show="form_user.spCode === 'SP'">
                            {{form_user.spName}}({{form_user.spCode}})
                        </FormItem>
                        <FormItem label="用户名：" >
                            {{form_user.userName}}
                        </FormItem>
                        <FormItem label="邮箱：" >
                            {{form_user.mailbox}}
                        </FormItem>
                        <FormItem label="岗位：" >
                            {{form_user.post}}
                        </FormItem>
                        <FormItem label="分配角色：">
                            <Card dis-hover  style="height:300px;width: 90%">
                                <Scroll height="270">
                                    <Table id='table_3' border  :columns="columns_roleList_preview" :data="form_user.roleCheckedInfo"></Table>
                                </Scroll>
                            </Card>
                        </FormItem>
                    </Form>
                </div>
                <div slot="footer">
                    <Button id="modal_acl_user_btn_8" type="ghost" @click="cancel_preivew">返回</Button>
                </div>
            </Modal>
        </Card>
    </div>
</template>

<script>
    import api from '../../../api/api';
    import commonSelect from '../../common-components/select-components/selectCommon';
    import commonSelectMult from '../../common-components/select-components/selectCommon_multiple';
    import commonSelectMultParam from '../../common-components/select-components/selectCommon_params';
    import loading from '../../my-components/loading/loading';
    import textEditor from '../../my-components/text-editor/text-editor-size';
    import util from '../../../libs/util';
    import qs from 'qs';

    export default {
        name: 'user-manage',
        components: {
            loading,
            commonSelect,
            textEditor,
            commonSelectMult,
            commonSelectMultParam
        },
        mounted () {
          this.init();
        },
        data () {
            //确定用户名校验规则

            return {
                // 编辑用户按钮loading
                edit_user_auth_loading : false,
                // 当前用户类型
                currentUserType : 'SP_BASED',
                // 窗口内是否显示服务提供方下拉
                show_sp_modal : false,
                // 账户状态数据绑定
                data_select_userStatus : '',
                // 用户状态下拉数组绑定
                data_userStatus_List : [
                    {
                        value : 'NORMAL',
                        label : '活动'
                    },
                    {
                        value : 'FROZEN',
                        label : '冻结'
                    }
                ],
                // 新建还是编辑数据绑定 true为新建 false为编辑
                currentStatus : true,
                // 下拉框个数
                count_select_related : 2,
                // 岗位数据绑定
                data_select_Post : '',
                // 用户编码数据绑定
                data_user_code : '',
                // 用户名称数据绑定
                data_user_name : '',
                // 用户列表表头数据绑定
                columns_userList : [
                    {
                        title: '用户名',
                        width: 180,
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.userName),
                                h('p', '('+ params.row.userCode + ')')
                            ]);
                        },
                        align: 'center'
                    },
                    {
                        title: '岗位',
                        key: 'userPostName',
                        width: 150,
                        align: 'center'
                    },
                    {
                        title: '用户状态',
                        width: 180,
                        render: (h,params) => {
                            // if(params.row.status === 'SUCCESS'){
                            //     return h ('div','处理成功');
                            // }else{
                            //     return h ('div','处理失败');
                            // }
                            let color = 'red';
                            let status = '冻结';
                            if(params.row.status === 'NORMAL'){
                                color ='green';
                                status = '活动';
                            }
                            return h('div',[
                                h('Tag',{
                                    style:{
                                        align :'center'
                                    },
                                    props:{
                                        color: color
                                    }
                                },status)
                            ])

                        },
                        align: 'center'
                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ]);
                        },
                        width: 180,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [h('p', params.row.createTime),
                                h('p', params.row.lastModifyTime)]);
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'operations',
                        // width:  320,
                        render: (h, params) => {
                            let status = '冻结'
                            if(params.row.status === 'NORMAL'){
                                status = '冻结'
                            }else{
                                status = '解冻'
                            }
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/user/auth'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.user_assign_role(params.row.userCode,params.row.type);
                                        }
                                    }
                                }, '分配角色'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/user/edit'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.user_modify(params.row.userCode);
                                        }
                                    }
                                }, '编辑'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/user/frozen'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.user_option(params.row.userCode,status);
                                        }
                                    }
                                }, status),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/user/detail'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.user_preview(params.row.userCode);
                                        }
                                    }
                                }, '查看'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/acl/user/edit-type'}
                                    },{
                                        name: 'show',
                                        value: this.currentUserType === 'PLATFORM'
                                    }],
                                    on: {
                                        click: () => {
                                            this.user_type(params.row.userCode);
                                        }
                                    }
                                }, '用户类型')
                            ]);
                        }

                    }
                ],
                // 用户列表数据内容绑定
                data_userList : [
                ],
                // 列表loading数据绑定
                show_loading_userList : false,
                // 总页数
                pageTotal : 10,
                // 当前页数
                pageNo : 1,
                // 表单数据绑定
                form_user : {
                    userType : '',
                    userTypeName : '',
                    userCode : '',
                    userName : '',
                    spCode: [],
                    spName: '',
                    mailbox : '',
                    post : '',
                    roleInfo : [
                    ],
                    roleCheckedInfo : [],
                    inputUserName : '',
                    spCode_edit : []
                },
                // 分配角色表头
                columns_roleList : [
                    {
                        title: '角色身份',
                        width: 120,
                        key: 'type',
                        align: 'center'
                    },
                    {
                        title: '角色名称',
                        key: 'name',
                        'min-width': 400,
                        render: (h, params) => {
                            //let self =this
                            return h('div', params.row.name.map((item) =>{
                                return h('Checkbox',{
                                    // domProps : {
                                    //     checked : item.checked
                                    // },
                                    props :{
                                        value : item.checked,
                                        disabled : params.row.disabled
                                    },
                                    on : {
                                        'on-change': (event) => {
                                            item.checked = event
                                            this.role_checked_bind(item.code,event);
                                        }
                                    }
                                },item.name)
                            }));
                        },
                        align: 'center'
                    },{
                        title: '操作',
                        key: 'option',
                        width: 100,
                        render: (h, params) => {
                            return h('div',[
                                h('Button', {
                                    props: {
                                        type: 'primary',
                                        size: 'small',
                                        disabled : params.row.disabled
                                    },
                                    on: {
                                        click: () => {
                                            // this.user_unassign_role(params.row.userCode);
                                            this.clear_selected_role(params.row.name)
                                            this.clear_selected_role_real(params.row.typeCode);
                                        }
                                    }
                                }, '清空')
                            ]);
                        },
                        align: 'center'
                    },
                ],
                columns_roleList_preview : [
                    {
                        title: '角色身份',
                        width: 120,
                        key: 'type',
                        align: 'center'
                    },
                    {
                        title: '角色名称',
                        key: 'name',
                        'min-width': 400,
                        render: (h, params) => {
                            return h('div', params.row.name.map((item) =>{
                                return h('span',{
                                    style:{
                                        'margin-right': '5px'
                                    }
                                },item.name)
                            }));
                        },
                        align: 'center'
                    },
                ],
                // 新增/分配角色表单规则数据绑定
                rule_user : {
                    userType : [
                        {required: true, message: '用户类型不能为空', trigger: 'blur'},
                    ]
                },
                // 编辑表单规则数据绑定
                rule_user_edit : {
                    post : [
                        {required: true, message: '岗位不能为空', trigger: 'blur'},
                    ]
                },
                // 修改用户类型表单规则数据绑定
                rule_user_type_modify : {
                    userType : [
                        {required: true, message: '用户类型不能为空', trigger: 'blur'},
                    ]
                },
                // 新增用户并授权弹窗显示绑定
                modal_new_auth : false,
                // 编辑用户弹窗显示绑定
                modal_edit_user : false,
                // 编辑用户类型弹窗显示绑定
                modal_edit_userType : false,
                // 查看用户弹窗显示绑定
                modal_preview : false,
                // 用户类型数据绑定
                data_select_userType : '',
                // 授权用户列表
                auth_user_list : [],
                // 用户类型下拉框数据绑定
                data_userType_List : [
                    {
                        value : 'SP_BASED',
                        label : 'SP用户'
                    },
                    {
                        value : 'PLATFORM',
                        label : '平台用户'
                    }
                ],
                // 弹窗服务提供方数据绑定
                data_select_sp_model : '',
                // 当前登录的用户拥有的角色
                userRoles_temp : []

            }
        },
        methods :{
            // 清空选择的角色
            clear_selected_role (data) {
                if(!!data){
                    data.forEach(
                        item =>{
                            item.checked =false;
                        }
                    )
                }
            },
            clear_selected_role_real (code) {
                for(var i in this.form_user.roleInfo){
                    if(this.form_user.roleInfo[i].typeCode === code){
                        this.clear_selected_role(this.form_user.roleInfo[i].name);
                        break;
                    }
                }
            },
            init () {
                this.currentUserType = localStorage.userType;
              this.form_user.roleCheckedInfo = this.roleChecked_handler(this.form_user.roleInfo);
            },
            // 用户类型数据更新
            updateSelect_Post (val){
                this.data_select_Post = val;
            },
            // 用户名单删除
            handleClose2 (event, name) {
                let index = this.auth_user_list.indexOf(name);
                this.auth_user_list.splice(index, 1);
            },
            // 查询用户函数
            search_user () {
                this.show_loading_userList = true;
                let params = {
                    position: this.data_select_Post,
                    name: this.data_user_code.trim(),
                    status: this.data_select_userStatus,
                    _pageNo: 1,
                    _pageSize: 10
                };
                util.paramFormat(params);
                api.yop_acl_user_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat_user(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            this.pageTotal = response.data.data.result.totalPageNum * 10;
                            this.show_loading_userList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_userList = false;
                        }
                    }
                );
            },
            // 表格数据处理函数
            tableDataFormat_user (items) {
                this.data_userList = [];
                for (var i in items) {
                    this.data_userList.push({
                        userName: util.empty_handler(items[i].optName),
                        userCode : util.empty_handler(items[i].optCode),
                        // roleTypeCode : util.empty_handler(items[i].code),
                        userPostCode : util.empty_handler(items[i].position),
                        userPostName : util.empty_handler(this.data_name_handler(items[i].position, 'position')),
                        status : util.empty_handler(items[i].status),
                        createTime : util.empty_handler(items[i].createdDate),
                        type : util.empty_handler(items[i].type),
                        lastModifyTime : util.empty_handler(items[i].lastModifiedDate),
                    });
                }
            },
            // 返回相应编码的名字
            data_name_handler (code, name) {
                if (code) {
                    let group = this.$store.state.select[name].data;
                    for (var i in group) {
                        if (group[i].value === code) {
                            return group[i].name;
                        }
                    }
                } else {
                    return '';
                }
            },
            // 重置用户函数
            reset_user () {
                this.$refs.select_post.resetSelected();
                this.$refs.select_user_status.clearSingleSelect();
                this.data_select_Post = '';
                this.data_user_code = '';
                this.data_select_userStatus = '';
            },
            // 添加用户函数
            user_create () {
                // this.form_user.roleInfo = [];
                this.reset_create_user_auth();
                this.currentStatus = true;
                if(localStorage.userType === 'SP_BASED'){
                    this.form_user.userType = 'SP_BASED';
                    this.$refs.sp_create_m.updateList({optType:'SP_BASED'});
                }else{
                    this.$refs.sp_create_m.updateList({optType:'PLATFORM'});
                }

                this.modal_new_auth = true;
            },
            // 页面刷新
            pageRefresh (val) {
                this.show_loading_userList = true;
                let params = {
                    position: this.data_select_Post,
                    name: this.data_user_code.trim(),
                    status: this.data_select_userStatus,
                    _pageNo: val,
                    _pageSize: 10
                };
                util.paramFormat(params);
                api.yop_acl_user_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat_user(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            this.pageTotal = response.data.data.result.totalPageNum * 10;
                            this.show_loading_userList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_userList = false;
                        }
                    }
                );
            },
            // 添加用户按钮数据绑定
            addUser () {
                api.yop_acl_user_check_boss_user({name: this.form_user.inputUserName}).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            if(response.data.data.result.status){
                                if(this.auth_user_list.indexOf(this.form_user.inputUserName) === -1){
                                    this.auth_user_list.push(this.form_user.inputUserName);
                                }else{
                                    this.$Message.warning({
                                        content: '该员工用户名已存在不要重复添加',
                                        duration: 5
                                    });
                                }
                            }else{
                                this.$Modal.error({
                                    title: '警告',
                                    content: response.data.data.result.remarks
                                });
                            }
                        } else {
                            this.$Modal.error({
                                title: '校验错误',
                                content: response.data.message
                            });
                        }
                    }
                );
            },
            // 确定创建用户按钮
            ok_create_user (val) {
                this.modal_create_user = false;
            },
            // 取消创建用户按钮
            cancel_create_user () {
                this.modal_create_user = false;
            },
            // 下拉框加载完处理函数
            select_callBack () {
                this.count_select_related--;
                if (this.count_select_related === 0) {
                    this.search_user();
                }
            },
            // 查询接口
            search_interface () {

            },
            // 用户分配角色
            user_assign_role (code,type) {
                this.currentStatus = false;
                this.reset_create_user_auth();
                this.user_detail_information_handler(code,this.modal_preview);
                this.form_user.roleInfo = [];
                let userType = 'SP_BASED'
                if(localStorage.userType === 'PLATFORM')
                {
                    userType = type;
                }else{
                    userType = 'SP_BASED'
                }
                this.$refs.sp_edit_m.updateList({optCode:code,optType:userType});
                // api.yop_acl_role_by_optcode.then(
                //     (response) =>{
                //         if(response.data.status === 'success'){
                //             let result = response.data.data.result;
                //             this.form_user.roleInfo = this.roleInfo_handler(result);
                //             this.userRoles_temp = this.roleInfo_handler(result);
                //         }else{
                //             this.$Modal.error({
                //                 title: '错误',
                //                 content: response.data.message
                //             });
                //         }
                //     }
                // )

                this.modal_new_auth = true;
            },
            // 现有无法修改的角色的初始化
            roles_data_init (data,init) {
                if(init){
                    this.form_user.roleInfo = [];
                }
                if(!!data){
                    data.forEach(
                        item =>{
                            this.form_user.roleInfo.push(item);
                        }
                    )
                }
                console.log(this.form_user.roleInfo)
            },
            // 用户编辑
            user_modify (code) {
                this.reset_create_user_auth();
                this.user_detail_information_handler(code,this.modal_preview);
                this.modal_edit_user = true;
            },
            // 用户冻结/解冻
            user_option (code,status) {
                if(status === '冻结'){
                    api.yop_acl_user_frozen({optCode:code}).then(
                        (response) =>{
                            //需要填写
                            if(response.status === 'success'){
                                this.$ypMsg.notice_success(this,'冻结成功');
                                this.search_user();
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }else{
                    api.yop_acl_user_unfreeze({optCode:code}).then(
                        (response) =>{
                            //需要填写
                            if(response.status === 'success'){
                                this.$ypMsg.notice_success(this,'解冻成功');
                                this.search_user();
                            }else{
                                this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            }
                        }
                    )
                }
            },
            // 用户查看
            user_preview (code) {
                this.modal_preview = true;
                this.user_detail_information_handler(code,this.modal_preview);
            },
            // 详细信息detail查询
            user_detail_information_handler(code,loading){
                api.yop_acl_user_detail({optCode:code}).then(
                    (response)=>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.detail_format(result)
                        }else{
                            this.$ypMsg.notice_error(this,'用户详细信息获取失败,请刷新重试',response.data.message,response.data.solution);
                            loading= false;
                        }
                    }
                )
            },
            // 详细信息处理函数
            detail_format (result) {
                this.form_user.userTypeName = util.empty_handler(this.data_name_handler(result.type, 'user_type'));
                this.form_user.userType = util.empty_handler(result.type);
                this.form_user.userName = util.empty_handler(result.optName);
                this.form_user.userCode = util.empty_handler(result.optCode);
                this.form_user.spCode = this.sp_selected_handler(result.perms).spCodes;
                this.form_user.spName = this.sp_selected_handler(result.perms).spName;
                this.form_user.mailbox = util.empty_handler(result.email);
                this.form_user.post = util.empty_handler(result.position);
                this.form_user.roleInfo = this.roleInfo_handler(result.perms);
                this.form_user.version = result.version;
                this.userRoles_temp = this.form_user.roleInfo;
                this.init();
            },
            // 返回相应编码的名字
            data_name_handler (code, name) {
                if (code) {
                    let group = this.$store.state.select[name].data;
                    for (var i in group) {
                        if (group[i].value === code) {
                            return group[i].name;
                        }
                    }
                } else {
                    return '';
                }
            },
            // 分配角色信息处理函数
            roleInfo_handler (data) {
                let returnData = [];
                if(!!data){
                     data.forEach(
                         item =>{
                             let disabledTemp = false
                             if(item.modify === undefined){
                                 disabledTemp = false
                             }else if(item.modify){
                                 disabledTemp = false
                             }else{
                                 disabledTemp = true
                             }
                             returnData.push({
                                 type : item.spName,
                                 typeCode : item.spCode,
                                 disabled : disabledTemp,
                                 name : this.roles_detail_handler(item.roles)
                             })
                         }
                     )
                }
                return returnData;
            },
            roles_detail_handler (data) {
                let returnData = [];
                if(!!data){
                    data.forEach(
                        item => {
                            let checkedTemp = false
                            if(item.access){
                                checkedTemp = true;
                            }else{
                                checkedTemp = false;
                            }
                            returnData.push({
                                    name : item.name,
                                    code : item.code,
                                    checked : checkedTemp
                                }
                            )
                        }
                    )
                }
                return returnData;
            },
            // 选取sp返回
            sp_selected_handler (data) {
                let returnData ={
                    spCodes:[],
                    spName : ''
                }
              if(!!data){
                  for(var i in data){
                      returnData.spCodes.push(data[i].spCode);
                      if(i !== data.length){
                          returnData.spName += data[i].spName +'、'
                      }else{
                          returnData.spName += data[i].spName
                      }
                  }
              }
              return returnData
            },
            // 用户类型
            user_type (code) {
                this.user_detail_information_handler(code,this.modal_preview);
                this.modal_edit_userType =true;
            },
            // 新增用户并授权确定
            ok_create_user_auth (val) {
                // this.modal_new_auth = false;
                    this.$refs[val].validate((valid) => {
                        if (valid) {
                            let roleCodesTemp = this.assigned_user_set_handler()
                            if(this.currentStatus){
                                let  param= {
                                    type : this.form_user.userType,
                                    optCodes : this.auth_user_list,
                                    roleCodes: roleCodesTemp
                                }
                                util.paramFormat(param);
                                api.yop_acl_user_create(param).then(
                                    (response) =>{
                                        //需要填写
                                        if(response.status === 'success'){
                                            this.$ypMsg.notice_success(this,'创建成功');
                                            this.modal_new_auth = false;
                                            this.search_user();
                                        }else{
                                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                            this.modal_new_auth = false;
                                        }
                                    }
                                )
                            }else{
                                console.log(roleCodesTemp);
                                let param = {
                                    optCode : this.form_user.userCode,
                                    roleCodes : roleCodesTemp
                                }
                                console.log(param);
                                // util.paramFormat(param);
                                //可能需要对密钥处理剔除
                                api.yop_acl_user_auth(param).then(
                                    (response) =>{
                                        //需要填写
                                        if(response.status === 'success'){
                                            this.$ypMsg.notice_success(this,'修改成功');
                                            this.modal_new_auth = false;
                                            this.search_user();
                                        }else{
                                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                            this.modal_new_auth = false;
                                        }
                                    }
                                )
                            }
                        } else {
                            this.$Message.error('请检查');
                        }
                    })
            },
            // 获取已经分配的角色集合
            assigned_user_set_handler () {
                console.log(this.form_user.roleInfo);
                let returnData = []
                if(!!this.form_user.roleInfo){
                    this.form_user.roleInfo.forEach(
                        item =>{
                            if(!!item.name){
                                item.name.forEach(
                                    subItem =>{
                                        if(subItem.checked){
                                            returnData.push(subItem.code)
                                        }
                                    }
                                )
                            }
                        }
                    )
                }
                // console.log(returnData);
                return returnData;
            },
            // 新增用户并授权取消
            cancel_create_user_auth () {
                this.modal_new_auth = false;
                this.reset_create_user_auth();
            },
            // 新增用户表单重置
            reset_create_user_auth (){
                if(this.$refs.sp_user_type_m){
                    this.$refs.sp_user_type_m.clearSingleSelect();
                }
                this.edit_user_auth_loading = false;
                this.form_user.userType = '';
                this.$refs.sp_create_m.resetSelected();
                this.$refs.sp_edit_m.resetSelected();
                this.form_user.spCode = [];
                this.form_user.spCode_edit = [];
                this.form_user.inputUserName = '';
                this.auth_user_list = [];
                this.form_user.roleInfo = [];
            },
            // 编辑用户并授权确定
            ok_edit_user_auth (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        this.edit_user_auth_loading = true;
                        let  param= {
                            optCode : this.form_user.userCode,
                            position : this.form_user.post
                        }
                        util.paramFormat(param);
                        api.yop_acl_user_edit(param).then(
                            (response) =>{
                                //需要填写
                                if(response.status === 'success'){
                                    this.$ypMsg.notice_success(this,'修改职位成功');
                                    this.edit_user_auth_loading = false;
                                    this.modal_edit_user = false;
                                    this.search_user();
                                }else{
                                    this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    this.modal_edit_user = false;
                                }
                                this.$refs.position_m.resetSelected();
                            }
                        )
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 编辑用户并授权取消
            cancel_edit_user_auth () {
                this.$refs.position_m.resetSelected();
                this.modal_edit_user = false;
            },
            // 编辑用户类型
            ok_edit_userType (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        let  param= {
                            optCode : this.form_user.userCode,
                            type : this.form_user.userType
                        }
                        util.paramFormat(param);
                        api.yop_acl_user_edit_type(param).then(
                            (response) =>{
                                if(response.status === 'success'){
                                    this.$ypMsg.notice_success(this,'修改用户类型成功');
                                    this.modal_edit_userType = false;
                                }else{
                                    this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    this.modal_edit_userType = false;
                                }
                            }
                        )
                    } else {
                        this.$Message.error('请检查');
                    }
                })
                this.modal_edit_userType = false;
            },
            // 编辑用户类型
            cancel_edit_userType () {
                this.modal_edit_userType = false;
            },
            // 查看返回
            cancel_preivew () {
                this.modal_preview = false;
            },
            // 弹窗服务提供方数据更新
            updateSelect_sp_model (val) {
                    if(this.currentStatus){
                        this.form_user.spCode = val;
                        if(this.form_user.spCode && this.form_user.spCode.length > 0){
                            let param = {spCodes : this.form_user.spCode}
                            let transfer = {
                                params: param,
                                paramsSerializer: function(params) {
                                    return qs.stringify(params, {arrayFormat: 'repeat'})
                                }
                            }
                            api.yop_acl_role_by_spcodes(transfer).then(
                                (response) => {
                                    if (response.data.status === 'success') {
                                        // this.roles_data_init(this.userRoles_temp,true);
                                        this.form_user.roleInfo = [];
                                        this.roles_data_init(this.roleInfo_handler(response.data.data.result),false);
                                    } else {
                                        this.$Modal.error({
                                            title: '错误',
                                            content: response.data.message
                                        });
                                    }
                                }
                            )
                        }else{
                            this.form_user.roleInfo = [];
                        }
                    }

            },
            updateSelect_sp_model_edit (val) {
                if(!this.currentStatus) {
                    this.form_user.spCode_edit = val;
                    if (this.form_user.spCode_edit && this.form_user.spCode_edit.length > 0) {
                        let param = {spCodes: this.form_user.spCode_edit}
                        let transfer = {
                            params: param,
                            paramsSerializer: function (params) {
                                return qs.stringify(params, {arrayFormat: 'repeat'})
                            }
                        }
                        api.yop_acl_role_by_spcodes(transfer).then(
                            (response) => {
                                if (response.data.status === 'success') {
                                    this.roles_data_init(this.userRoles_temp, true);
                                    this.roles_data_init(this.roleInfo_handler(response.data.data.result), false);
                                } else {
                                    this.$Modal.error({
                                        title: '错误',
                                        content: response.data.message
                                    });
                                }
                            }
                        )
                    } else {
                        this.form_user.roleInfo = this.userRoles_temp;
                    }
                }

            },
            // 弹窗服务提供方数据更新
            updateSelect_post_model (val) {
                this.form_user.post = val;
            },
            // 弹窗内用户类型改变
            changeType_modal (val) {
                // if(val === 'SP'){
                //     this.show_sp_modal = true;
                // }else {
                //     this.show_sp_modal = false;
                // }
                if(val){
                    this.$refs.sp_create_m.updateList({optType : val});
                }
            },
            // 获取已选择的角色
            roleChecked_handler (array) {
                let result = []
                if(!! array){
                    array.forEach(
                        item =>{
                            result.push({
                                type: item.type,
                                name: this.roleChecked_handler_single(item.name)
                            })
                        }
                    )
                }
                return result;
            },
            // 获取已选择的角色single
            roleChecked_handler_single (array) {
                let checkedRoles = []
                if(!! array){
                    array.forEach(
                        item =>{
                            if(item.checked){
                                checkedRoles.push(
                                    {
                                        name : item.name,
                                        code : item.code
                                    }
                                )
                            }
                        }
                    )
                }
                return checkedRoles;
            },
            // 角色选取重新绑定数据
            role_checked_bind (code,result){
                if(!!this.form_user.roleInfo){
                    for(var i in this.form_user.roleInfo){
                        if(!!this.form_user.roleInfo[i].name){
                            for(var j in this.form_user.roleInfo[i].name){
                                if(this.form_user.roleInfo[i].name[j].code === code){
                                    this.form_user.roleInfo[i].name[j].checked = result;
                                    break;
                                }
                            }
                        }
                    }
                }
            },

        }
    };
</script>

<style scoped>

</style>
