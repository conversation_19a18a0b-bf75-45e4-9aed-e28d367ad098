<style lang="less">
    @import '../../styles/common.less';
    @import 'api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-height:16px;text-align:center;color:#f00;text-decoration:none}
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">
                <Col span="7" >
                <Col span="7" class="margin-top-10">
                <span >API URI:</span>
                </Col>
                <Col span="17" >
                <Input id='input_apiM_1' class="margin-top-5" clearable v-model="data_interface_uri" placeholder="API URI" @on-enter="search_Interface"></Input>
                </Col>
                </Col>
                <Col offset="1" span="7" >
                <Col span="7" class="margin-top-10">
                <span >API名称:</span>
                </Col>
                <Col span="17" >
                <Input id='input_apiM_2' class="margin-top-5" clearable v-model="data_interface_name" placeholder="API名称" @on-enter="search_Interface"></Input>
                </Col>
                </Col>
                <Col offset="1" span="7" >
                <Col span="7" class="margin-top-10">
                <span >API分组:</span>
                </Col>
                <Col span="17" >
                <Select ref="select_apiM_1" id='select_apiM_1' class="margin-top-5" v-model="data_select_apiGroup" filterable clearable placeholder="请选择（默认全部）">
                    <Option v-for="item in data_apiGroup_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>
                <Col class="margin-top-5" span="7" >
                <Col span="7" class="margin-top-10">
                <span >状态:</span>
                </Col>
                <Col span="17" >
                <Select id='select_apiM_2' class="margin-top-5" v-model="data_select_status" placeholder="请选择（默认全部）" clearable>
                    <Option v-for="item in data_status_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>
                <Col class="margin-top-5" offset="1" span="7" >
                    <Col span="7" class="margin-top-10">
                        <span >API类型:</span>
                        </Col>
                        <Col span="17" >
                        <Select id='select_apiM_4' class="margin-top-5" v-model="data_select_apiType"  placeholder="请选择（默认全部）" clearable>
                            <Option v-for="item in data_apiType_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                        </Select>
                        </Col>
                </Col>
                <Col class="margin-top-5" offset="1" span="7" >
                
                </Col>
                </Col>
                <Col span="4">
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button id='btn_apiM_1' v-url="{url:'/rest/api/list'}" type="primary" @click="search_Interface">查询</Button>
                </Col>
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button id='btn_apiM_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
            </Row>
            <!-- <Row class="margin-top-20">
                <Col span="24">
                <Button id='btn_apiM_3' class="margin-right-10" type="primary" v-url="{url:'/rest/api/config/create'}" @click="webApi_Register">注册 API</Button>
                <Upload class="margin-right-50" style="display: inline-block;"
                        :action="importIP"
                        :name="name_upload"
                        :headers="header_upload"
                        :show-upload-list="false"
                        :on-format-error="handleFormatError"
                        :before-upload="handleBeforeUpload"
                        :on-progress="handleProgress"
                        :on-success="handleSuccess"
                        :on-error="handleError"
                >
                    <Button id='btn_apiM_4' v-url="{url:'/rest/api/import'}" type="ghost">导入API</Button>
                </Upload> -->
                <!--<input id="fileinput" @change="uploading($event)" type="file" accept="image/*">-->
                <!-- <Button id='btn_apiM_5' class="margin-left-10" type="ghost" v-url="{url:'/rest/api/export'}" @click="interface_Export">导出API</Button>
                </Col>
            </Row> -->
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_1' border ref="selection" :columns="columns_ApiInterfaceList" :data="data_ApiInterfaceList" @on-selection-change="handleselection"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <!--<Spin fix v-show="show_loading_apiList">-->
                <!--<Icon type="load-c" size=18 class="yop-spin-icon-load"></Icon>-->
                <!--<div>数据加载中...</div>-->
                <!--</Spin>-->
                <loading :show="show_loading_apiList"></loading>
            </Row>
        </Card>
        <Modal v-model="modal_Show_register" width="600" >
            <p slot="header">
                <span  class="margin-right-10">注册 API</span> <span style="color:gray;font-size: 12px;">端点服务</span>
            </p>
            <div style="padding-left:30px;">
                <p>* &nbsp;是否幂等：
                    <RadioGroup id="modal_rbtn_1" v-model="idempotent">
                        <Radio label="否"></Radio>
                        <Radio label="是"></Radio>
                    </RadioGroup>
                </p>
                <p style="font-size: 12px;color: grey; padding-left: 70px;line-height:30px;">幂等API支持回归测试</p>
                <!--<p style="line-height:40px;">* &nbsp;适配类型：-->
                    <!--<Select id='modal_apiM_select_1' size="small" v-model="data_select_type" style="width:190px" placeholder="请选择" @on-change="selected_type">-->
                        <!--<Option v-for="item in data_type_List" :value="item.value" :key="item.value">{{ item.label }}</Option>-->
                    <!--</Select>-->
                <!--</p>-->
                <p style="line-height:40px;">* &nbsp;API分组：
                    <common-select id="modal_ag_select_1" ref="select_ag_m" @on-update="updateSelect_apiGroup"
                                   type="combo"
                                   holder="请选择"
                                   keyWord="result"
                                   code="apiGroupCode"
                                   title="apiGroupName"
                                   group="apiGroup"
                                   @on-loaded="select_callBack"
                                   :default="this.data_apiGroup"
                                   size="small"
                                   :uri="this.$store.state.select.apiGroup.uri"
                                   style="width:80%"></common-select>
                </p>
                <p style="line-height:40px;">* &nbsp;API类型：
                    <common-select id="modal_at_select_1" ref="select_at_m" @on-update="updateSelect_apiType"
                                   type="sub"
                                   holder="请选择"
                                   keyWord="result"
                                   code="value"
                                   title="desc"
                                   group="api_type"
                                   subCode="contentTypeMapping"
                                   @on-loaded="select_callBack"
                                   :default="this.data_apiType"
                                   size="small"
                                   :uri="this.$store.state.select.api_type.uri"
                                   style="width:80%"></common-select>
                </p>
                <p style="line-height:40px;">* &nbsp;端点协议：
                    <Select id='modal_apiM_select_2' size="small" v-model="data_select_EndpointProtocol" style="width:190px" placeholder="请选择">
                        <Option v-for="item in data_EndpointProtocol_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </p>
                <p style="line-height:40px;" v-show="show_pointUrl">&nbsp;&nbsp;&nbsp;端点url：&nbsp;&nbsp;&nbsp;
                    <Input id='modal_apiM_input_1' size="small" style="width: 380px;" v-model="endpoint_Url" placeholder="请输入" @on-focus="onFocus_url" @on-blur="onblur_url"></Input>
                </p>
                <p style="font-size: 12px;color: grey; padding-left: 75px; line-height: 30px;" v-show="show_pointUrl">
                    可指定访问某台服务器，如：10.10.10.10:8080 //com.yeepay.3g.auth.aaa</p>
                <p style="line-height:40px;">* &nbsp;端点类名：&nbsp;<Input  id='input_apiM_4' size="small" style="width: 380px;" v-model="endpoint_Name" placeholder="请输入"  @on-focus="onFocus" @on-blur="keydown"></Input>
                    <Checkbox id='modal_apiM_chk_1' v-model="pointUrl_user_defined" @on-change="check_url"></Checkbox>自定义
                </p>
                <p style="font-size: 12px;color: grey; padding-left: 75px; line-height: 30px;">如：com.yeepay.g3.facade.auth2.facade.Auth2Facade</p>
                <p style="line-height:40px;">* &nbsp;端点方法：
                    <Select id='modal_apiM_select_3' size="small" v-model="data_select_EndpointMethod" style="width:380px" placeholder="根据端点类名字自动加载">
                        <Option v-for="item in data_EndpointMethod_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                </p>
            </div>
            <div slot="footer">
                <Button id='modal_apiM_btn_1' type="ghost"  @click="cancel_Newapp">取消</Button>
                <Button id='modal_apiM_btn_2' type="primary" @click="ok_Newapp">下一步</Button>
            </div>
        </Modal>
        <modal-security ref="modal_security"></modal-security>
        <AddErrorCodeDia ref="addErrorCodeDia" />
        <ApiOptions ref="apiOptions" @refreshList="search_Interface" />
        <ApiScenarioExampleModal ref="apiScenarioExampleModal" @refreshList="search_Interface" />
        <!-- 回调弹框 api所在分组下可用的spi列表，已关联的spi列表 -->
        <Modal v-model="callBackFlag" width="735" :closable="false" >
            <p slot="header">
                <span  class="margin-right-10">SPI关联API</span> 
            </p>
            <div>
                <Col class="margin-bottom-10">
                    <p>新增：
                        <Select id="select_basic_1" ref="selectSpi" @on-change="changeSpi" v-model="supportSpi" size="small" class="yop-length-450" filterable clearable >
                            <Option v-for="item in spi_group_list" :value="item.name" :key="item.name">{{ item.title }}</Option>
                        </Select>
                        
                    </p>
                    </Col>
                    <Col class="margin-bottom-30">
                    <Table id='table_spi_list' border :columns="columns_spiList" :data="data_spiList"></Table>
                    <loading :show="show_loading_errCommon"></loading>
                </Col>
            </div>
            <div slot="footer">
                <Button class="margin-left-20" type="primary" v-url="{url:'/rest/api/old/callback/add'}"  @click="saveAddSpi">保存</Button>
                <Button id='modal_apiM_btn_11' type="ghost"  @click="closeCallback">关闭</Button>
            </div>
        </Modal>    
    </div>
</template>

<script>
    import commonSelect from '../common-components/select-components/selectCommon';
    import api from'../../api/api'
    import Vue from 'vue';
    import qs from 'qs';
    import loading from '../my-components/loading/loading';
    import modalSecurity from './modals/modal_api_security'
    import AddErrorCodeDia from './modals/addErrorCodeDia.vue';
    import ApiOptions from './modals/apiOptions.vue';
    import ApiScenarioExampleModal from './modals/apiScenarioExampleModal.vue';
    // import
    export default {
        name: 'api_Management_Open',
        components:{
            loading,
            commonSelect,
            modalSecurity,
            AddErrorCodeDia,
            ApiOptions,
            ApiScenarioExampleModal
        },
        data () {
            return {
                // api分组数据绑定
                data_apiGroup: '',
                // api类型数据绑定
                data_apiType: '',
                // 倒入功能ip动态变量
                importIP : localStorage.remoteIP+'/rest/api/import',
                /**
                 * 注册弹窗数据
                 */
                name_upload:'apiFile',
                // 端点url数据绑定
                endpoint_Url : '',
                // 显示端点url部分
                show_pointUrl : false,
                // 端点类名数据绑定
                endpoint_Name: '',
                // 是否幂等数据绑定
                idempotent: '否',
                // 端点url自定义
                pointUrl_user_defined: false,
                // 适配类型下拉框数据绑定
                data_select_type: 'TRANSFORM',
                // 适配类型下拉框数据
                data_type_List: [
                    {
                        value: 'TRANSFORM',
                        label: '转换'
                    },
                    {
                        value: 'LOCAL',
                        label: '本地'
                    },
                    {
                        value: 'PASSTHROUGH',
                        label: '透传'
                    }
                ],
                // 端点协议下拉框数据绑定
                data_select_EndpointProtocol: 'HESSIAN',
                // 端点协议下拉框数据
                data_EndpointProtocol_List: [
                    {
                        value: 'HESSIAN',
                        label: 'hessian'
                    }
                ],
                // 端点方法下拉框数据绑定
                data_select_EndpointMethod: '',
                // 端点方法下拉框数据
                data_EndpointMethod_List: [],
                // 当前端点名称内容
                current_content : '',
                // 当前端点url内容
                current_content_url : '',
                // url校验结果
                pass_url : true,
                /**
                 * 主界面部分数据
                 */
                // 上传header绑定
                header_upload: {
                    Authorization: 'Bearer ' + localStorage.accesstoken
                },
                // api列表加载动画数据绑定
                show_loading_apiList : true,
                // switch点击行数
                switch_index: 0,
                // 注册web api对话框显示
                modal_Show_register: false,
                // APIuri数据绑定
                data_interface_uri : '',
                // API名称
                data_interface_name : '',
                // 状态下拉框数据绑定
                data_select_status: '',
                // 状态下拉框选项数据
                data_status_List: [],
                // api分组下拉框数据绑定
                data_select_apiGroup: '',
                // api分组下拉框数据
                data_apiGroup_List: [],
                // 当前api 相关信息
                currentApiInfo: {},
                // 回调弹框 开关
                callBackFlag: false,
                // spi 列表loading
                show_loading_errCommon: false,
                // 新增 支持spi
                supportSpi:'',
                // 支持spi 列表
                spi_group_list: [],
                // 展示spi列表
                data_spiList: [],
                // 实际后台数据有的spi
                true_spiList: [],
                // 支持spi 列表table 
                columns_spiList : [
                    {
                        title: '名称',
                        key: 'name',
                        align: 'center',
                        width: 275
                    },
                    {
                        title: '标题',
                        align: 'center',
                        key: 'title',
                        width: 275
                    },
                    {
                        title: '操作',
                        align: 'center',
                        width: 150,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'error',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.$Modal.confirm({
                                                title: '确认移除',
                                                content: '<p>确定要移除吗？</p>',
                                                onOk: () => {
                                                    // 是否需要发布
                                                    this.deleteSpi(params.row);
                                                },
                                                onCancel: () => {
                                                    // this.$Message.info('取消成功');
                                                }
                                            });
                                            
                                        }
                                    }
                                }, '移除')
                            ]);
                        }
                    }
                ],
                // 安全需求下拉框数据绑定
                data_safety_request: '',
                // 安全需求下拉框数据
                data_safetyRequest_List:[],
                // api类型选择数据绑定
                data_select_apiType: '',
                // api类型
                data_apiType_List:[],
                // apiAPI列表列属性
                columns_ApiInterfaceList: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    {
                        title: 'API名称',
                        width: 200,
                        key: 'interface_Name'
                    },
                    {
                        title: 'API URI',
                        width: 250,
                        key: 'interface_URI'
                    },
                    {
                        title: 'API类型',
                        key: 'APItypeDesc',
                        width : 100,
                        align: 'center',
                    },
                    {
                        title: 'API分组',
                        key: 'apiGroup',
                        width : 110,
                        type:'html',
                        align: 'center'
                        // render: (h,params) => {
                        //     return h ('div',params.row.APIgroupTitle+'('+params.row.APIgroupCode+')')
                        // }
                    },
                    // {
                    //     title: '安全需求',
                    //     key: 'interface_Tag',
                    //     type: 'html',
                    //     width: 167,
                    //     // render: (h,params) => {
                    //     //     let securityTemp = ''
                    //     //     for(var i in params.row.groupSecurity){
                    //     //         if(i === params.row.groupSecurity.length){
                    //     //             securityTemp = securityTemp +params.row.groupSecurity[i]
                    //     //         }else{
                    //     //             securityTemp = securityTemp +params.row.groupSecurity[i]+'<br/>'
                    //     //         }
                    //     //     }
                    //     //     console.log(securityTemp)
                    //     //     return securityTemp;
                    //     // }
                    // },
                    {
                        title: '状态',
                        // key: 'statusDesc',
                        align: 'center',
                        render: (h, params) => {
                            var color = 'red';
                            if (params.row.statusDesc == '活动中') {
                                color = 'green';
                            }
                            if (params.row.statusDesc == '已下线') {
                                color = 'grey';
                            }
                            return h('div',[
                                h('Tag',{
                                    style:{
                                        align: 'center'
                                    },
                                    props:{
                                        color: color
                                    }
                                },params.row.statusDesc)
                            ]);
                        }
                    },
                    {
                        title: '创建时间/最后更新时间',
                        key: 'Date',
                        type: 'html',
                        // render: (h,params) => {
                        //     return h('div',[('p',params.row.createTime),
                        //         ('p',params.row.lastModifyTime)])
                        // }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        render: (h,params) =>{
                            const list = [
                              h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api/security-req'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.api_security(params.row.interface_URI,params.row.APIgroupCode);
                                        }
                                    }
                                }, '安全需求'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    on: {
                                      click: () => {
                                        this.$refs.apiOptions.showModal(
                                            {
                                              apiId: params.row.apiIdV2, 
                                              apiGroupCode: params.row.APIgroupCode, 
                                              apiUri: params.row.interface_URI, 
                                            }
                                        );
                                      }
                                    }
                                }, 'API选项'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    on: {
                                      click: () => {
                                        this.$refs.apiScenarioExampleModal.showModal(
                                            {
                                              apiId: params.row.apiIdV2, 
                                              apiGroupCode: params.row.APIgroupCode, 
                                            }
                                        );
                                      }
                                    }
                                }, 'API场景示例'),
                                // 回调
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api/old/callbacks'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.currentApiInfo = params.row
                                            this.showCallBack(params.row)
                                            this.getSpiSimpleList(params)
                                        }
                                    }
                                }, '回调'),
                            ]
                            const { statusDesc } = params.row
                            list.push(h('Button', {
                                        domProps:{
                                            id: "api_mangent_edit_btn21"
                                        },
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        on: {
                                            click: () => {
                                                this.relevance_error_code(params.row)
                                            }
                                        }
                                    }, '关联错误码'))
                            return h('div',list)
                        }
                    }
                    // {
                    //     title: '操作',
                    //     key: 'operations',
                    //     width: 160,
                    //     render: (h, params) => {
                    //         if(params.row.operations !== undefined){
                    //             let buttonStatus = ''
                    //             if(params.row.operations === false){
                    //                 buttonStatus = '启用';
                    //                 return h('div', [
                    //                     h('Button', {
                    //                         props: {
                    //                             type: 'text',
                    //                             size: 'small',
                    //                         },
                    //                         directives: [{
                    //                             name: 'url',
                    //                             value: {url: '/rest/api/config/edit'}
                    //                         }],
                    //                         on: {
                    //                             click: () => {
                    //                                 this.interface_modify(params.row.APIid);
                    //                             }
                    //                         }
                    //                     }, '修改'),
                    //                     h('Button', {
                    //                         props: {
                    //                             type: 'text',
                    //                             size: 'small'
                    //                         },
                    //                         directives: [{
                    //                             name: 'url',
                    //                             value: {url: '/rest/api/config/edit-for-doc'}
                    //                         }],
                    //                         on: {
                    //                             click: () => {
                    //                                 this.interface_des(params.row.APIid);
                    //                             }
                    //                         }
                    //                     }, '描述'),
                    //                     // h('Button', {
                    //                     //     props: {
                    //                     //         type: 'text',
                    //                     //         size: 'small'
                    //                     //     },
                    //                     //     on: {
                    //                     //         click: () => {
                    //                     //             this.interface_submit(params.row.APIid);
                    //                     //         }
                    //                     //     }
                    //                     // }, '提交审核'),
                    //                     // h('Button', {
                    //                     //     props: {
                    //                     //         type: 'text',
                    //                     //         size: 'small'
                    //                     //     },
                    //                     //     on: {
                    //                     //         click: () => {
                    //                     //             this.interface_doc(params.row.APIid);
                    //                     //         }
                    //                     //     }
                    //                     // }, '预览'),

                    //                     h('Button', {
                    //                         props: {
                    //                             type: 'text',
                    //                             size: 'small'
                    //                         },
                    //                         directives: [{
                    //                             name: 'url',
                    //                             value: {url: '/rest/api/active'}
                    //                         }],
                    //                         on: {
                    //                             click: () => {
                    //                                 console.log(params.index);
                    //                                 this.interface_use(params.index,params.row.operations,params.row.APIid);
                    //                             }
                    //                         }
                    //                     }, buttonStatus),
                    //                     h('Button', {
                    //                         props: {
                    //                             type: 'text',
                    //                             size: 'small'
                    //                         },
                    //                         directives: [{
                    //                             name: 'url',
                    //                             value: {url: '/rest/api/delete'}
                    //                         }],
                    //                         on: {
                    //                             click: () => {
                    //                                 console.log(params.index);
                    //                                 this.interface_delete(params.index,params.row.operations,params.row.APIid);
                    //                             }
                    //                         }
                    //                     }, '删除')
                    //                     // h('Button', {
                    //                     //     props: {
                    //                     //         type: 'text',
                    //                     //         size: 'small'
                    //                     //     },
                    //                     //     on: {
                    //                     //         click: () => {
                    //                     //             this.interface_version(params.row.APIid);
                    //                     //         }
                    //                     //     }
                    //                     // }, '版本记录')
                    //                 ]);
                    //             }else {
                    //                 buttonStatus = '禁用';
                    //                 return h('div', [
                    //                     h('Button', {
                    //                         props: {
                    //                             type: 'text',
                    //                             size: 'small'
                    //                         },
                    //                         directives: [{
                    //                             name: 'url',
                    //                             value: {url: '/rest/api/config/edit'}
                    //                         }],
                    //                         on: {
                    //                             click: () => {
                    //                                 this.interface_modify(params.row.APIid);
                    //                             }
                    //                         }
                    //                     }, '修改'),
                    //                     h('Button', {
                    //                         props: {
                    //                             type: 'text',
                    //                             size: 'small'
                    //                         },
                    //                         directives: [{
                    //                             name: 'url',
                    //                             value: {url: '/rest/api/config/edit-for-doc'}
                    //                         }],
                    //                         on: {
                    //                             click: () => {
                    //                                 this.interface_des(params.row.APIid);
                    //                             }
                    //                         }
                    //                     }, '描述'),
                    //                     // h('Button', {
                    //                     //     props: {
                    //                     //         type: 'text',
                    //                     //         size: 'small'
                    //                     //     },
                    //                     //     on: {
                    //                     //         click: () => {
                    //                     //             this.interface_submit(params.row.APIid);
                    //                     //         }
                    //                     //     }
                    //                     // }, '提交审核'),
                    //                     // h('Button', {
                    //                     //     props: {
                    //                     //         type: 'text',
                    //                     //         size: 'small'
                    //                     //     },
                    //                     //     on: {
                    //                     //         click: () => {
                    //                     //             this.interface_doc(params.row.APIid);
                    //                     //         }
                    //                     //     }
                    //                     // }, '预览'),

                    //                     h('Button', {
                    //                         props: {
                    //                             type: 'text',
                    //                             size: 'small'
                    //                         },
                    //                         directives: [{
                    //                             name: 'url',
                    //                             value: {url: '/rest/api/forbid'}
                    //                         }],
                    //                         on: {
                    //                             click: () => {
                    //                                 console.log(params.index);
                    //                                 this.interface_use(params.index,params.row.operations,params.row.APIid);
                    //                             }
                    //                         }
                    //                     }, buttonStatus)
                    //                     // h('Button', {
                    //                     //     props: {
                    //                     //         type: 'text',
                    //                     //         size: 'small'
                    //                     //     },
                    //                     //     on: {
                    //                     //         click: () => {
                    //                     //             this.interface_version(params.row.APIid);
                    //                     //         }
                    //                     //     }
                    //                     // }, '版本记录')
                    //                 ]);
                    //             }

                    //         }else{
                    //             return h('div', [
                    //                 h('Button', {
                    //                     props: {
                    //                         type: 'text',
                    //                         size: 'small'
                    //                     },
                    //                     directives: [{
                    //                         name: 'url',
                    //                         value: {url: '/rest/api/config/edit'}
                    //                     }],
                    //                     on: {
                    //                         click: () => {
                    //                             this.interface_modify(params.row.APIid);
                    //                         }
                    //                     }
                    //                 }, '修改'),
                    //                 h('Button', {
                    //                     props: {
                    //                         type: 'text',
                    //                         size: 'small'
                    //                     },
                    //                     directives: [{
                    //                         name: 'url',
                    //                         value: {url: '/rest/api/config/edit-for-doc'}
                    //                     }],
                    //                     on: {
                    //                         click: () => {
                    //                             this.interface_des(params.row.APIid);
                    //                         }
                    //                     }
                    //                 }, '描述'),
                    //                 // h('Button', {
                    //                 //     props: {
                    //                 //         type: 'text',
                    //                 //         size: 'small'
                    //                 //     },
                    //                 //     on: {
                    //                 //         click: () => {
                    //                 //             this.interface_submit(params.row.APIid);
                    //                 //         }
                    //                 //     }
                    //                 // }, '提交审核'),
                    //                 // h('Button', {
                    //                 //     props: {
                    //                 //         type: 'text',
                    //                 //         size: 'small'
                    //                 //     },
                    //                 //     on: {
                    //                 //         click: () => {
                    //                 //             this.interface_doc(params.row.APIid);
                    //                 //         }
                    //                 //     }
                    //                 // }, '预览'),
                    //                 // h('Button', {
                    //                 //     props: {
                    //                 //         type: 'text',
                    //                 //         size: 'small'
                    //                 //     },
                    //                 //     on: {
                    //                 //         click: () => {
                    //                 //             this.interface_version(params.row.APIid);
                    //                 //         }
                    //                 //     }
                    //                 // }, '版本记录')
                    //             ]);
                    //         }

                    //     }
                    // }
                ],
                // 表格数据
                data_ApiInterfaceList: []
                ,
                // 表格选中数据
                multiSelectedData: [],
                // 分页单页个数
                pageNo : 1,
                // 分页总数
                pageTotal: 0,
                // 当前查询参数
                current_params : {
                    pageNo : 1,
                    pageSize : 20
                }
            };
        },
        methods: {
            // 关联错误码
            relevance_error_code (params) {
                this.$refs.addErrorCodeDia.show_model(
                    {
                        APIid: params.apiIdV2, 
                        APIgroupCode: params.APIgroupCode, 
                    }
                );
            },
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
            // 修改按钮调用方法
            interface_modify (val) {
                localStorage.apiInfo = 'modify';
                localStorage.apiId = val;
                this.$router.replace({
                    name: 'api_basics'
                });

            },
            // 描述按钮调用方法
            interface_des (val) {
                localStorage.apiInfo='description';
                localStorage.apiId = val;
                this.$router.replace({
                    name: 'api_basics'
                });
            },
            // 文档预览按钮调用方法
            interface_doc (val) {
                console.log(val);
                // alert('预览');
            },
            // 提交审核按钮调用方法
            interface_submit (val) {
                console.log(val);
                // alert('提交审核');
            },
            // 删除按钮调用方法
            interface_delete (index,operations,id) {
                this.$Modal.confirm({
                    title: '提示',
                    content: '删除API将导致API无法重新启用，您还要继续吗？',
                    'ok-text':'删除',
                    onOk: () =>{
                        // this.data_ApiInterfaceList[index].operations = !operations;
                        this.delete_ok(index,operations,id);
                    }
                    // onCancel: this.forbidden_cancel(index,operations)
                });
            },
            // 启用按钮调用方法
            interface_use (index,operations,id) {
                if(operations){
                    this.$Modal.confirm({
                        title: '提示',
                        content: '禁用API将导致API无法调用，您还要继续吗？',
                        'ok-text':'禁用',
                        onOk: () =>{
                            // this.data_ApiInterfaceList[index].operations = !operations;
                            this.forbidden_ok(index,operations,id);
                        }
                        // onCancel: this.forbidden_cancel(index,operations)
                    });
                }else{
                    this.$Modal.confirm({
                        title: '提示',
                        'ok-text':'启用',
                        content: '您将启用API，请确认是否维护好API信息，您还要继续吗',
                        onOk: () =>{
                            this.enabled_ok(index,operations,id);
                            // this.data_ApiInterfaceList[index].operations = !operations;
                        },
                        // onCancel: this.forbidden_cancel(index,operations)
                    });
                }
                // this.data_ApiInterfaceList[index].operations = !operations;
                // alert('启用');
            },
            // 获取新增 spi列表
            getSpiSimpleList(param){
                
                let params = {
                   apiGroup: param.row.APIgroupCode
                }
                api.yop_apiManagement_simple_list(params).then(
                    (response) =>{
                        if(response.data.status === 'success'){
                            this.spi_group_list = response.data.data.result
                        }else{
                            this.$ypMsg.notice_error(this,'新增列表失败',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            
            // 点击回调 请求相关spi api相关列表
            showCallBack(param){
                this.show_loading_errCommon = true;
                let params = {
                   apiUri: param.interface_URI
                }
                api.yop_apiManagement_old_callbacks(params).then(
                    (response) =>{
                        this.callBackFlag = true;
                        this.show_loading_errCommon = false;
                        if(response.data.status === 'success'){
                            this.data_spiList = []
                            if(response.data.data.result){
                                let resultArr = response.data.data.result;
                                resultArr.forEach(item => {
                                    this.data_spiList.push(item)
                                });
                            }
                            this.true_spiList = response.data.data.result ? response.data.data.result : []
                        }else{
                            this.$ypMsg.notice_error(this,'已关联spi获取列表失败',response.data.message,response.data.solution);
                        }
                    }
                )
            },
            // 保存新增spi
            saveAddSpi(){
                if(this.data_spiList.length == 0){
                    this.$Message.warning('请先添加')
                    return false;
                }
                let currentCallback = [];
                if(this.true_spiList.length == 0){
                    this.data_spiList.forEach(element => {
                        currentCallback.push(element.name)
                    });
                }else{
                    function getArrDifference(arr1, arr2) {
                        return arr1.concat(arr2).filter(function(v, i, arr) {
                            return arr.indexOf(v) === arr.lastIndexOf(v);
                        });
                    }
                    let newAdd =  getArrDifference(this.true_spiList,this.data_spiList);
                    newAdd.forEach(element => {
                        currentCallback.push(element.name)
                    });

                }
                let params = {
                   apiUri: this.currentApiInfo.interface_URI,
                   callbacks: currentCallback
                }
                api.yop_apiManagement_callback_add(params).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'添加成功');
                            this.$refs.selectSpi.clearSingleSelect()
                            // 更新下列表
                            this.showCallBack(this.currentApiInfo)
                        }else{
                            this.$ypMsg.notice_error(this,'保存失败',response.message,response.solution);
                        }
                    }
                )
            },
            // 移除spi
            deleteSpi(row){
                let currentCallback = [];
                currentCallback.push(row.name)
                let params = {
                   apiUri: this.currentApiInfo.interface_URI,
                   callbacks: currentCallback
                }
                if(this.true_spiList.length == 0){
                    this.data_spiList.forEach((element,index) => {
                        if(element.name == row.name){
                            this.data_spiList.splice(index, 1); 
                            this.$ypMsg.notice_success(this,'移除成功');
                        }
                    });
                }else{
                    var flags = false;
                    for (let index = 0; index < this.true_spiList.length; index++) {
                        const element = this.true_spiList[index];
                        if(row.name == element.name){
                            flags  = true
                        }
                    }
                    if(flags){
                        api.yop_apiManagement_callback_delete(params).then(
                            (response) =>{
                                if(response.status === 'success'){
                                    this.$ypMsg.notice_success(this,'移除成功');
                                    // 更新下列表
                                    this.showCallBack(this.currentApiInfo)
                                }else{
                                    this.$ypMsg.notice_error(this,'保存失败',response.message,response.solution);
                                }
                            }
                        )
                    }else{
                        this.data_spiList.forEach((element,index) => {
                            if(element.name == row.name){
                                this.data_spiList.splice(index, 1); 
                                this.$ypMsg.notice_success(this,'移除成功');
                            }
                        });
                    }
                }
                
                
            },
            // 新增如果列表里有 则弹框
            changeSpi(val){
                if(val && val != 'undefined'){
                    if(this.data_spiList.length == 0){
                        this.spi_group_list.forEach(item => {
                            if(item.name === val){
                                this.data_spiList.push(item) 
                            }
                        });
                    }else{
                        var flag = true;
                        this.data_spiList.forEach(element => {
                            if(element.name == val){
                                this.$Message.warning('此spi已添加')
                                flag = false
                                return flag
                            }
                        });
                        if(flag){
                            this.spi_group_list.forEach(item => {
                                if(item.name === val){
                                    this.data_spiList.push(item) 
                                }
                            });
                        }
                    }
                    this.$refs.selectSpi.clearSingleSelect()
                }
            },
            // 关闭回调
            closeCallback(){
                this.callBackFlag = false;
                this.$refs.selectSpi.clearSingleSelect()
            },
            // 启用提示框ok
            enabled_ok (index,operations,id) {
                api.yop_apiManagement_apiActive({
                    apiId : id
                }).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'所选API启用成功','启用成功');
                            this.data_ApiInterfaceList[index].statusDesc = '活动中'
                            this.data_ApiInterfaceList[index].operations = !operations;
                        }else{
                            this.$ypMsg.notice_error(this,'启用失败',response.message,response.solution);
                        }
                    }
                )
                // this.data_ApiInterfaceList[index].operations = !operations;
            },
            // 禁用提示框ok
            forbidden_ok (index,operations,id) {
                api.yop_apiManagement_apiForbid({
                    apiId : id
                }).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'所选API禁用成功','禁用成功');
                            this.data_ApiInterfaceList[index].statusDesc = '已禁用';
                            this.data_ApiInterfaceList[index].operations = !operations;
                        }else{
                            this.$ypMsg.notice_error(this,'禁用失败',response.message,response.solution);
                        }
                    }
                )
                // this.data_ApiInterfaceList[index].operations = !operations;

            },
            // 禁用提示框ok
            delete_ok (index,operations,id) {
                api.yop_apiManagement_apiDelete({
                    apiId : id
                }).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'所选API删除成功','删除成功');
                        }else{
                            this.$ypMsg.notice_error(this,'删除失败',response.message,response.solution);
                        }
                        this.pageRefresh();
                    }
                )
                // this.data_ApiInterfaceList[index].operations = !operations;

            },
            // 版本记录按钮调用方法
            interface_version () {
                // console.log(val);
                alert('版本记录');
            },
            // 界面初始化函数
            init () {
                this.show_loading_apiList = true;
                api.yop_dashboard_checkAccesstoken();
                api.yop_dashboard_apilist(localStorage.accesstoken).then(
                    function (response) {

                    }
                )
                api.yop_apiManagement_apiList().then(
                    (response) => {
                        this.tabledataGet(response.data.data.page.items);
                    }
                )
                // 初始化页面表格数据
                api.yop_apiManagement_apiList_page({
                    pageNo : 1,
                    pageSize : 20
                }).then(
                    (response) => {
                        if(response.data.status === 'success'){
                            this.tabledataGet(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                                this.pageTotal=NaN;
                            this.show_loading_apiList = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                        }
                    }
                );
                // api状态列表
                api.yop_apiManagement_apiCommonStatusList().then(
                    (response)=>{
                        let resultTemp = response.data.data.statusList
                        this.data_status_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].value,
                                label: resultTemp[i].desc
                            })
                        }
                        this.data_status_List = dataTemp;
                    }
                );
                // 安全需求列表
                api.yop_apiManagement_securityReqList().then(
                    (response)=>{
                        let resultTemp = response.data.data.result
                        this.data_safetyRequest_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].name,
                                label: resultTemp[i].name
                            })
                        }
                        this.data_safetyRequest_List = dataTemp;
                    }
                );
                // api类型列表
                api.yop_apiManagement_apiCommonTypeList().then(
                    (response)=>{
                        let resultTemp = response.data.data.result
                        this.data_apiType_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].value,
                                label: resultTemp[i].desc
                            })
                        }
                        this.data_apiType_List = dataTemp;
                    }
                );
                // api分组列表
                api.yop_dashboard_apis_list().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.data_apiGroup_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].apiGroupCode,
                                label: resultTemp[i].apiGroupCode+'('+resultTemp[i].apiGroupName+')',
                                name: resultTemp[i].apiGroupName
                            })
                        }
                        this.data_apiGroup_List = dataTemp;
                    }
                )
            },
            // 页面刷新
            pageRefresh (val) {
                this.show_loading_apiList = true ;
                this.current_params.pageNo = val;
                api.yop_apiManagement_apiList_page(this.current_params).then(
                    (response) => {
                        this.tabledataGet(response.data.data.page.items);
                        this.pageNo = response.data.data.page.pageNo;
                                this.pageTotal=NaN;
                        this.show_loading_apiList = false ;
                    }
                )
            },
            // 表格赋值
            tabledataGet (items) {
                this.data_ApiInterfaceList = [];
                let dataTemp = [];
                for (var i in items){
                    let DateTemp = items[i].createdDate + '<br/>' + items[i].lastModifiedDate
                    // let securityTemp = []
                    // if(items[i].apiSecurities){
                    //     securityTemp = this.SecurityGenerate('自定义',items[i].apiSecurities);
                    // }else{
                    //     securityTemp = this.SecurityGenerate('继承',items[i].apiGroupSecurities);
                    // }
                    let operationsTemp = false;
                    if(items[i].status === 'ACTIVE'){
                        operationsTemp = true;
                    }
                    if(items[i].status === 'FORBID'){
                        operationsTemp = false
                    }
                    dataTemp.push({
                        APIid : items[i].apiId,
                        apiIdV2 : items[i].apiIdV2,
                        interface_Name: items[i].apiTitle,
                        interface_URI: items[i].apiUri,
                        APItype: items[i].apiType,
                        APItypeDesc: items[i].apiTypeDesc,
                        APIgroupTitle: items[i].apiGroupTitle,
                        // interface_Tag: securityTemp,
                        Date: DateTemp,
                        apiGroup: this.apiGroup_title_handler(items[i].apiGroupTitle,items[i].apiGroupCode)+'<br/>('+items[i].apiGroupCode+')',
                        APIgroupCode:items[i].apiGroupCode,
                        status: items[i].status,
                        statusDesc: items[i].statusDesc,
                        apiSecurity: items[i].apiSecurity,
                        groupSecurity: items[i].groupSecurity,
                        createTime: items[i].createdDate,
                        lastModifyTime: items[i].lastModifiedDate,
                        operations : operationsTemp
                    });
                    if(items[i].status === 'OFFLINE'){
                        delete dataTemp[i]['operations'];
                    }
                }
                this.data_ApiInterfaceList = dataTemp;
            },
            // api分组标题处理
            apiGroup_title_handler (title,code) {
                if(title && title !== ''){
                    return title;
                }else{
                    return this.apiGroup_title_transfer(code);
                }
            },
            // api分组名称转义
            apiGroup_title_transfer (code) {
                console.log(code);
                for(var i in this.data_apiGroup_List){
                    if(this.data_apiGroup_List[i].value === code){
                        // debugger;
                        return  this.data_apiGroup_List[i].name;
                    }
                }
                return '';
            },
            // 安全需求数组处理
            SecurityGenerate (title,array) {
                let securityTemp = title +'<br/>'
                for(var i in array){
                    if(i === array.length){
                        securityTemp = securityTemp +array[i]
                    }else{
                        securityTemp = securityTemp +array[i]+'<br/>'
                    }
                }
                return securityTemp;
            },
            // 空处理函数
            ParamHandle (Param){
                if(Param || (Param === 0)){
                    return Param;
                }else{
                    return '';
                }
            },
            // 表格内容改动时
            handleselection(value){
                this.multiSelectedData =value;
            },
            // 查询apiAPI函数
            search_Interface () {
                let paramsTemp = {
                    apiTitle: this.data_interface_name,
                    apiUri: this.data_interface_uri,
                    apiType: this.data_select_apiType,
                    apiGroupCode : this.data_select_apiGroup,
                    status: this.data_select_status,
                    securityReq: this.data_safety_request,
                    pageNo : 1,
                    pageSize: 20
                }
                if(this.data_interface_name === ''){
                    paramsTemp['apiTitle'];
                }
                if(this.data_interface_uri === ''){
                    delete paramsTemp['apiUri'];
                }
                if(this.data_select_apiType === ''){
                    delete paramsTemp['apiType'];
                }
                if(this.data_select_apiGroup === ''){
                    delete paramsTemp['apiGroupCode'];
                }
                if(this.data_select_status === ''){
                    delete paramsTemp['status'];
                }
                if(this.data_safety_request === ''){
                    delete paramsTemp['securityReq'];
                }
                this.current_params = paramsTemp;
                api.yop_apiManagement_apiList_page(paramsTemp).then(
                    (response) => {
                        this.tabledataGet(response.data.data.page.items);
                        this.pageNo = response.data.data.page.pageNo;
                                this.pageTotal=NaN;

                    }
                );
            },
            // 重置函数
            reset_Interface () {
                this.$refs.select_apiM_1.clearSingleSelect();
                this.data_interface_name = '';
                this.data_interface_uri = '';
                this.data_select_apiType = '';
                this.data_select_apiGroup = '';
                this.data_select_status = '';
                this.data_safety_request = '';
                this.current_status ={
                    pageNo : 1,
                    pageSize: 20
                };
            },
            // 注册web api函数
            webApi_Register () {
                // this.modal_Show_register = true;
                // alert('注册web api');
                localStorage.apiInfo='create';
                // localStorage.apiId =
                // this.$router.push({
                //     name: 'api_basics'
                // });
                this.modal_Show_register =true;

            },
            // 导入API函数
            interface_Import () {
                // alert('导入接口');
                if (this.multiSelectedData == null || this.multiSelectedData.length == 0){
                    //还有其他函数判定
                    alert("请选择数据后再点击");
                }else {
                    var temp =[];
                    this.multiSelectedData.forEach( m => {
                        temp.push(m.APIid);
                    });
                    //*（需要删除）
                    // console.log("一键回归"+temp);
                    // alert("一键回归"+temp);
                }
            },
            // 导出API函数
            interface_Export () {
                if (this.multiSelectedData == null || this.multiSelectedData.length == 0){
                    //还有其他函数判定
                    this.$ypMsg.notice_warning(this,'请选择需要导出的数据后再点击');
                }else {
                    var temp =[];
                    this.multiSelectedData.forEach( m => {
                        temp.push(m.APIid);
                    });
                    let paramsTmp = {
                        id : temp
                    };
                    var transfer = {
                        params: paramsTmp,
                        paramsSerializer: function(params) {
                            return qs.stringify(params, {arrayFormat: 'repeat'})
                        },
                        responseType: 'blob'
                    }
                    api.yop_apiManagement_apiExport(transfer).then(
                        (response) => {
                            console.log(response)
                            this.exec_download(response.data,'exportData.json');
                        }
                    )
                }
            },
            // 下载执行函数
            exec_download (data,) {
                // let blob = data;
                // let link =document.createElement('a');
                // link.style.display = 'none';
                // link.href = window.URL.createObjectURL(blob);
                // link.download =name;
                // link.click();
                if (!data) {
                    return
                }
                let url = window.URL.createObjectURL(new Blob([data]));
                let link = document.createElement('a');
                link.style.display = 'none';
                link.href = url;
                link.setAttribute('download','导出数据.json');
                document.body.appendChild(link);
                link.click()
            },
            /**
             * 新建弹窗方法
             */
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
            // 点击选择端点类型触发函数
            selected_type (value) {
                if(value === 'LOCAL'){
                    this.data_EndpointProtocol_List = [
                        {
                            value: 'SPRING',
                            label: 'spring'
                        }];
                    this.data_select_EndpointProtocol = 'SPRING';
                }else if (value === 'PASSTHROUGH'){
                    this.data_EndpointProtocol_List = [
                        {
                            value: 'HTTP',
                            label: 'http'
                        }];
                    this.data_select_EndpointProtocol = 'HTTP';
                }else if (value === 'TRANSFORM'){
                    this.data_EndpointProtocol_List = [
                        {
                            value: 'HESSIAN',
                            label: 'hessian'
                        }];
                    this.data_select_EndpointProtocol = 'HESSIAN';
                }
            },
            // onblur调用参数
            keydown () {
                if(this.current_content === this.endpoint_Name){

                }else{
                    if(this.pointUrl_user_defined){
                        this.pass_url = this.IsUrl(this.endpoint_Url)
                        if(this.pass_url){
                            this.run_request_point();
                        }else{
                            this.$ypMsg.notice_warning(this,'url不符合规范请修改');
                        }
                    }else{
                        // 请求端点方法
                        this.run_request_point();
                    }

                }
                this.IsLocal(this.endpoint_Name);
            },
            // 执行请求端点方法
            run_request_point () {
                if(this.endpoint_Name && this.endpoint_Name !== ''){
                    // var params = new URLSearchParams();
                    // params.append('className', this.endpoint_Name);
                    var params ;
                }
                if(this.pointUrl_user_defined && this.endpoint_Url !== ''){
                    // params.append('endServiceUrl', this.endpoint_Url);
                    params = {
                        params:{
                            'className': this.endpoint_Name,
                            'endServiceUrl' : this.endpoint_Url
                        }
                    }
                }else{
                    params= {
                        params:{
                            'className': this.endpoint_Name,
                        }
                    }

                    api.yop_apiManagement_loader_methodQuery(params).then(
                        (response) =>{
                            this.data_EndpointMethod_List = [];
                            this.data_select_EndpointMethod = ''
                            let MethodArray = response.data.data.methodList;
                            this.data_select_EndpointMethod = MethodArray[0];
                            for(var i in MethodArray){
                                this.data_EndpointMethod_List.push(
                                    {
                                        label : MethodArray[i],
                                        value : MethodArray[i]
                                    }
                                );
                            }
                        }
                    )
                }
            },
            // onfocus 调用方法
            onFocus () {
                this.current_content = this.endpoint_Name;
            },
            // 端点url onblur调用参数
            onblur_url () {
                if(this.current_content_url === this.endpoint_Url){

                }else{
                    if(this.endpoint_Url === '' && this.pointUrl_user_defined ===false){
                        this.run_request_point();
                    }else{
                        this.pass_url = this.IsUrl(this.endpoint_Url)
                        if(this.pass_url){
                            // 请求端点方法
                            this.run_request_point();
                        }else{
                            this.$ypMsg.notice_warning(this,'url不符合规范请修改');
                        }
                    }
                }

            },
            // 端点url onfocus 调用方法
            onFocus_url () {
                this.current_content_url = this.endpoint_Url;
            },

            // 注册Web API取消按钮
            cancel_Newapp () {
                this.modal_Show_register = false;
            },
            // 实际注册功能
            ok_func (){
                let param = {
                    methodName : this.data_select_EndpointMethod,
                    className : this.endpoint_Name,
                    apiType : this.data_apiType,
                    apiGroup : this.data_apiGroup
                }
                // api 生成函数
                api.yop_apiManagement_config_autoGenerate_uri(param).then(
                    response =>{
                        let status = response.data.status;
                        if (status === 'success') {
                            let result = response.data.data.apiUri;
                            // api uri数据绑定
                            this.$store.state.api_uri = result;
                            // api 分组数据绑定
                            this.$store.state.api_group = this.data_apiGroup;
                            // api 类型数据绑定
                            this.$store.state.api_type = this.data_apiType;
                            // 端点url数据绑定
                            this.$store.state.endpoint_Url=this.endpoint_Url  ;
                            // 显示端点url部分
                            this.$store.state.show_pointUrl = this.show_pointUrl;
                            // 端点类名数据绑定
                            this.$store.state.endpoint_Name = this.endpoint_Name;
                            // 是否幂等数据绑定
                            this.$store.state.idempotent = this.idempotent;
                            // 端点url自定义
                            this.$store.state.pointUrl_user_defined = this.pointUrl_user_defined;
                            // 适配类型下拉框数据绑定
                            this.$store.state.data_select_type = this.data_select_type;
                            // 适配类型下拉框数据
                            this.$store.state.data_type_List = this.data_type_List;
                            // 端点协议下拉框数据绑定
                            this.$store.state.data_select_EndpointProtocol = this.data_select_EndpointProtocol;
                            // 端点协议下拉框数据
                            this.$store.state.data_EndpointProtocol_List = this.data_EndpointProtocol_List;
                            // 端点方法下拉框数据绑定
                            this.$store.state.data_select_EndpointMethod = this.data_select_EndpointMethod;
                            // 端点方法下拉框数据
                            this.$store.state.data_EndpointMethod_List = this.data_EndpointMethod_List;
                            this.$router.push({
                                name: 'api_basics'
                            });
                        } else {
                            this.$Notice.error({
                                title: 'api uri生成失败',
                                desc: response.data.message,
                                duration: 10
                            });
                        }
                        this.modal_Show_register = false;
                    }
                )
            },
            // 注册Web API确定按钮
            ok_Newapp () {
                if(this.endpoint_Name === '' || this.data_select_EndpointMethod === ''){
                    this.$Modal.warning({
                        title: '警告',
                        content: '请检查必填项是否填写'
                    });
                }else{
                    if(!this.pointUrl_user_defined){
                        this.ok_func();
                    }else{
                        if(this.pass_url){
                            this.ok_func();
                        }else{
                            this.$ypMsg.notice_warning(this,'url不符合规范请修改');
                        }
                    }


                }
            },
            // 本地校验
            IsLocal (string){
                if(string.trim().substring(0,32)  === 'com.yeepay.g3.yop.center.combapi'){
                    if(this.data_select_type !== 'LOCAL'){
                        this.$Modal.warning({
                            title: '警告',
                            content: '您配置的端点方法适配类型为本地，系统将帮您的适配类型转化为本地！',
                            onOk: () =>{
                                this.data_select_type ='LOCAL';
                            }
                        });
                    }

                }
            },
            // url校验
            IsUrl (str_url) {
                let strRegex = '^((https|http|ftp|rtsp|mms)?://)'
                    + '?(([0-9a-z_!~*\'().&=+$%-]+: )?[0-9a-z_!~*\'().&=+$%-]+@)?' //ftp的user@
                    + '(([0-9]{1,3}.){3}[0-9]{1,3}' // IP形式的URL- **************
                    + '|' // 允许IP和DOMAIN（域名）
                    + '([0-9a-z_!~*\'()-]+.)*' // 域名- www.
                    + '([0-9a-z][0-9a-z-]{0,61})?[0-9a-z].' // 二级域名
                    + '[a-z]{2,6})' // first level domain- .com or .museum
                    + '(:[0-9]{1,4})?' // 端口- :80
                    + '((/?)|' // a slash isn't required if there is no file name
                    + '(/[0-9a-z_!~*\'().;?:@&=+$,%#-]+)+/?)$';
                let re=new RegExp(strRegex);
                if (re.test(str_url)) {
                    return true;
                } else {
                    return false;
                }
            },
            // 上传文件
            uploading(event){

            },
            //文件上传状态绑定函数
            handleFormatError (file) {
                this.$ypMsg.notice_warning(this,'文件 ' + file.name + ' 格式不正确，请选择图片文件。','文件格式不正确');
            },
            handleBeforeUpload (file) {
                this.$ypMsg.notice_warning(this,'文件 ' + file.name + ' 准备上传。','文件准备上传')
            },
            handleProgress (event, file) {
                this.$ypMsg.notice_info(this,'文件 ' + file.name + ' 正在上传。','文件正在上传')
            },
            handleSuccess (event, file) {
                let total = event.data.result.total
                let success = event.data.result.success
                let failure = event.data.result.failure
                this.$ypMsg.notice_success(this,'全部API数量：' + total + ' <br/>'+'成功API数量：' + success + ' <br/>'+'失败API数量：' + failure,'文件上传完成结果');
            },
            handleError (event, file,fileList) {
                this.$ypMsg.notice_error_simple(this,'文件上传失败','文件 ' + fileList.name + ' 上传失败。');
            },
            // 下拉框加载完处理函数
            select_callBack () {

            },
            // api分组下拉框数据更新
            updateSelect_apiGroup (val) {
                this.data_apiGroup = val;
            },
            // api类型下拉框数据更新
            updateSelect_apiType (val) {
                this.data_apiType = val;

            },
            // api安全需求设置
            api_security (uri,apiGroup){
                this.$refs.modal_security.modal_show(uri,apiGroup);
            } 
        },
        mounted () {
            // 挂载时调用页面初始化函数
            this.init();
            localStorage.removeItem('apiInfo');
        },
        created (){
            // localStorage.removeItem('apiInfo');
        },

    };
</script>

<style scoped>

</style>
