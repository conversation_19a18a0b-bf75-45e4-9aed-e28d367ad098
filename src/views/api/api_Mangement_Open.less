// p标签中label和后续组件在一行限定高度
.yop-api-height-oneline {
  line-height:40px;
}
//内容居中对齐
.yop-text-align-center {
  padding-left: 25%;
}
//  45长度
.yop-length-45{
  width : 45px;
}
// 100长度
.yop-length-100{
  width: 100px;
}
// 130长度
.yop-length-130{
  width: 130px;
}
// 200长度
.yop-length-200 {
  width : 200px;
}
// 200长度
.yop-length-300 {
  width : 300px;
}
// 400长度
.yop-length-450 {
  width : 450px;
}
// 注释样式
.yop-explain-85 {
  font-size: 12px;
  color: grey;
  padding-left: 85px;
  line-height:30px;
}
// 注释样式
.yop-explain {
  font-size: 12px;
  line-height:30px;
}
// 注释样式100
.yop-explain-100 {
  font-size: 12px;
  color: grey;
  padding-left: 100px;
  line-height:30px;
}
// 注释样式120
.yop-explain-120 {
  font-size: 12px;
  color: grey;
  padding-left: 120px;
  line-height:30px;
}
// 注释样式130
.yop-explain-130 {
  font-size: 12px;
  color: grey;
  padding-left: 130px;
  line-height:30px;
}
// 红色标题
.yop-title-red {
  font-size : 12px;
  color : red;
}
// 蓝色span
.yop-span-blue {
  color : #2d8cf0;
}
// 左外边距80
.yop-margin-left-80 {
  margin-left: 80px;
}
// 左外边距30
.yop-margin-left-30 {
  margin-left: 30px;
}
// 小标题样式
.yop-modal-subtitle{
  color : black;
  font-size: 13px;
  font-weight: 500;
  text-indent: 0px;
}
// 缩进10
.yop-text-indent-10{
  text-indent: 10px;
}
// 边框
.yop-border {
  border-bottom: 1px solid;
  margin-bottom: 10px;
  height: 40px;
}
// 边框
.yop-border-grey {
  border-bottom: 1px solid #bbbec4;
  margin-bottom: 10px;
  height: 40px;
  //text-align: center;
}
// 下拉收起过渡动画
.yop-trans-enter-active, .yop-trans-leave-active {
  transition: all .5s ease-out;
}
.yop-trans-enter {
  transform: translateY(-40px);
  opacity: 0;
}
.yop-trans-leave-active {
  transform: translateY(-40px);
  opacity: 0;
}
