<style lang="less">
@import "../../../styles/common.less";
@import "../../../styles/loading.less";
@import "../api_Mangement_Open.less";
@import "../../regression-test/regression.less";
/*html,body{*/
/*width: 100%;*/
/*height: 100%;*/
/*overflow: scroll;*/
/*}*/
.tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
  border-radius: 0;
  background: #fff;
}

.tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
  border-top: 1px solid #3399ff;
}

.tabs-style2
> .ivu-tabs.ivu-tabs-card
> .ivu-tabs-bar
.ivu-tabs-tab-active:before {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  background: #3399ff;
  position: absolute;
  top: 0;
  left: 0;
}
</style>
<template>
  <div style="background: #eee;padding:8px">
    <Card :bordered="false" dis-hover>
      <Row>
        <Row class="margin-bottom-10">
          <Col>
            <Button id="basic_btn1" type="ghost" @click="returnList">
              <Icon type="chevron-left"></Icon>&nbsp;接口列表
            </Button>
            <span style="float:right">
              <!-- <Button type="primary" @click="getContent">文档预览</Button> -->
              <Button
                v-show="this.current_status !== 'description'"
                id="basic_btn2"
                class="margin-left-10"
                type="primary"
                @click="save"
              >保存</Button>
            </span>
          </Col>
        </Row>
        <Card dis-hover>
          <template v-if="current_status === 'create'">
            <Tabs v-model="tabNow" :key="0">
              <TabPane :disabled="current_status !== 'create'" id="tabQuickAnalysis" :label="label0" name="quickAnalysis">
                <QuickAnalysis
                  ref="quickAnalysis"
                  :current_status="current_status"
                  @model_jump="model_jump"
                  @passQuickAnalysis="passQuickAnalysis"
                />
              </TabPane>
              <TabPane id="tabBasic" :label="label1" name="basic">
                <transition name="yop-trans">
                  <Row v-show="basicShow">
                    <Form ref="form_data" :label-width="150" :model="form_data" inline>
                      <Row>
                        <FormItem
                          :rules="{required:true,message:'API分组不能为空'}"
                          class="width-100-perc"
                          label="API分组："
                          prop="data_select_apiGroup"
                        >
                          <div></div>

                          <Select
                            v-show="this.current_status == 'create'"
                            id="selectAPIGroupBasic"
                            ref="select_ag_m"
                            v-model="form_data.data_select_apiGroup"
                            :disabled="api_group_disabled"
                            class
                            filterable
                            placeholder="请选择（默认全部）"
                            size="small"
                            style="width: 380px;"
                            @on-change="updateSelect_apiGroup_modal"
                          >
                            <Option
                              v-for="item in apiGroup_List"
                              :key="item.value"
                              :value="item.value"
                            >{{ item.label }}
                            </Option>
                          </Select>
                          <Select
                            v-show="this.current_status == 'modify'"
                            id="selectAPIGroupBasic"
                            ref="select_ag_m"
                            v-model="form_data.data_select_apiGroup"
                            :disabled="api_group_disabled"
                            class
                            placeholder="请选择（默认全部）"
                            size="small"
                            style="width: 380px;"
                            @on-change="updateSelect_apiGroup_modal"
                          >
                            <Option
                              v-for="item in apiGroup_List"
                              :key="item.value"
                              :value="item.value"
                            >{{ item.label }}
                            </Option>
                          </Select>

                          <p
                            v-show="this.current_status === 'description'"
                          >{{ form_data.data_select_apiGroup }}</p>
                        </FormItem>
                      </Row>
                      <Row>
                        <FormItem
                          v-show="this.current_status !== 'description'"
                          :rules="{required:true,validator:this.validate_api_name,trigger:'blur'}"
                          class="width-100-perc"
                          label="API名称："
                          prop="API_name"
                        >
                          <Input
                            id="inputAPINameBasic"
                            v-model="form_data.API_name"
                            placeholder="请输入"
                            size="small"
                            style="width: 380px;"
                          ></Input>
                        </FormItem>
                        <FormItem
                          v-show="this.current_status === 'description'"
                          :rules="{required:true,validator:this.validate_api_name,trigger:'blur'}"
                          class="width-100-perc"
                          label="API名称："
                          prop="API_name"
                        >
                          <p>{{ form_data.API_name }}</p>
                        </FormItem>
                      </Row>
                      <Row>
                        <FormItem
                          :rules="{required:true,message:'API标题不能为空'}"
                          class="width-100-perc"
                          label="API标题："
                          prop="API_title"
                        >
                          <Input
                            v-show="this.current_status !== 'description'"
                            id="inputAPITitleBasic"
                            v-model="form_data.API_title"
                            placeholder="请输入"
                            size="small"
                            style="width: 380px;"
                          ></Input>
                          <p v-show="this.current_status === 'description'">{{ form_data.API_title }}</p>
                        </FormItem>
                      </Row>
                      <Row>
                        <FormItem
                          :rules="{required:true,message:'接口类型不能为空'}"
                          class="width-100-perc"
                          label="接口类型："
                          prop="data_select_apiType"
                        >
                          <Select
                            v-show="this.current_status !== 'description'"
                            id="selectInterfaceType"
                            v-model="form_data.data_select_apiType"
                            :disabled="api_group_disabled"
                            class="yop-length-450"
                            placeholder="请选择接口类型"
                            size="small"
                            style="width: 380px;"
                            @on-change="get_interface_type"
                          >
                            <Option
                              v-for="item in data_apiType_List"
                              :key="item.value"
                              :value="item.value"
                            >{{ item.label }}
                            </Option>
                          </Select>
                          <p
                            v-show="this.current_status === 'description'"
                          >{{ data_select_apiType_text }}</p>
                        </FormItem>
                      </Row>
                      <Row>
                        <FormItem class="width-50-perc" label="接口描述：" @contentTrans="getContent">
                          <text-editor
                            :ke1="1"
                            v-show="this.current_status !== 'description' && hiddenTextEdit"
                            id="textareaDes"
                            ref="te"
                            :disabled="text_editor_disabled"
                            style="width: 567px;display: inline-flex"
                          ></text-editor>
                          <p
                            v-show="this.current_status === 'description'"
                            v-html="form_data.description"
                          ></p>
                        </FormItem>
                      </Row>
                    </Form>
                  </Row>
                </transition>
                <loading :show="show_loading_basic"></loading>
              </TabPane>
              <!-- <TabPane id="tabSecurity" :label="label2" name="security_require">
                              <safety_request ref="safetyRequest"  @passSecurityRequire="passSecurityRequire"></safety_request>
              </TabPane>-->
              <TabPane id="tabRequest" :label="label3" name="request_params">
                <param_request
                  ref="param_request"
                  :createdByExitedInterface="analysis.createdByExitedInterface"
                  :reqModelSchema="reqModelSchema"
                  @getServiceType="getServiceType"
                  @model_jump="model_jump"
                  @passHTTPCode="passHTTPCode"
                  @passParamRequest="passParamRequest"
                  @passQueryWay="passQueryWay"
                  @setServiceList="setServiceList"
                  @setUnderLine="setUnderLine"
                  @updateApiOptionParamList="updateApiOptionParamList"
                ></param_request>
              </TabPane>
              <TabPane id="tabResponse" :label="label4" name="response_params">
                <param_response
                  ref="param_response"
                  :createdByExitedInterface="analysis.createdByExitedInterface"
                  :resModelSchema="resModelSchema"
                  @model_jump="model_jump"
                  :httpType="form_data.data_select_apiType"
                  @passParamResponse="passParamResponse"
                ></param_response>
              </TabPane>
              <TabPane id="apiScenarioExample" :label="label8" name="apiScenarioExample">
                <ApiScenarioExample
                  ref="apiScenarioExample"
                  :status="current_status"
                  :apiGroup="form_data.data_select_apiGroup"
                  :apiId="current_apiId"
                />
              </TabPane>
              <!-- <TabPane id="tabService" :label="label5" name="point_service">
                              <point_service ref="pointService" @backendNameListFunc="backendNameListFunc" @passParamService="passParamService"></point_service>
              </TabPane>-->
              <TabPane :disabled="current_status !== 'create'" id="tabService" :label="label5" name="model_list">
                <model-list v-if="tabNow === 'model_list'" :response-list="responseModelList" :request-list="requestModelList" :resModelSchema="resModelSchema" :reqModelSchema="reqModelSchema" :api-group="form_data.data_select_apiGroup" @data-model-update="updateModelData" />
              </TabPane>
              <TabPane id="tabCallback" :label="label6" name="callback">
                <callback ref="callback" @passParamCallback="passParamCallback"></callback>
              </TabPane>
              <TabPane id="tabCallback" :label="label7" name="apiOption">
                <ApiOptionsForApi
                  ref="apiOptionsForApi"
                  :status="current_status"
                  :optionsRule="form_data.optionsRule"
                  :apiGroup="form_data.data_select_apiGroup"
                  :apiId="current_apiId"
                  :paramList="apiOptionParamList"
                  :resParamList="resParamList"
                  :callbackList="callbackList"
                />
              </TabPane>
            </Tabs>
          </template>
          <template v-else>
            <Tabs v-model="tabNow" :key="1">
              <TabPane id="tabBasic" :label="label1" name="basic">
                <transition name="yop-trans">
                  <Row v-show="basicShow">
                    <Form ref="form_data" :label-width="150" :model="form_data" inline>
                      <Row>
                        <FormItem
                          :rules="{required:true,message:'API分组不能为空'}"
                          class="width-100-perc"
                          label="API分组："
                          prop="data_select_apiGroup"
                        >
                          <div></div>

                          <Select
                            v-show="this.current_status == 'create'"
                            id="selectAPIGroupBasic"
                            ref="select_ag_m"
                            v-model="form_data.data_select_apiGroup"
                            :disabled="api_group_disabled"
                            class
                            filterable
                            placeholder="请选择（默认全部）"
                            size="small"
                            style="width: 380px;"
                            @on-change="updateSelect_apiGroup_modal"
                          >
                            <Option
                              v-for="item in apiGroup_List"
                              :key="item.value"
                              :value="item.value"
                            >{{ item.label }}
                            </Option>
                          </Select>
                          <Select
                            v-show="this.current_status == 'modify'"
                            id="selectAPIGroupBasic"
                            ref="select_ag_m"
                            v-model="form_data.data_select_apiGroup"
                            :disabled="api_group_disabled"
                            class
                            placeholder="请选择（默认全部）"
                            size="small"
                            style="width: 380px;"
                            @on-change="updateSelect_apiGroup_modal"
                          >
                            <Option
                              v-for="item in apiGroup_List"
                              :key="item.value"
                              :value="item.value"
                            >{{ item.label }}
                            </Option>
                          </Select>

                          <p
                            v-show="this.current_status === 'description'"
                          >{{ form_data.data_select_apiGroup }}</p>
                        </FormItem>
                      </Row>
                      <Row>
                        <FormItem
                          v-show="this.current_status !== 'description'"
                          :rules="{required:true,validator:this.validate_api_name,trigger:'blur'}"
                          class="width-100-perc"
                          label="API名称："
                          prop="API_name"
                        >
                          <Input
                            id="inputAPINameBasic"
                            v-model="form_data.API_name"
                            placeholder="请输入"
                            disabled
                            size="small"
                            style="width: 380px;"
                          ></Input>
                        </FormItem>
                        <FormItem
                          v-show="this.current_status === 'description'"
                          :rules="{required:true,validator:this.validate_api_name,trigger:'blur'}"
                          class="width-100-perc"
                          label="API名称："
                          prop="API_name"
                        >
                          <p>{{ form_data.API_name }}</p>
                        </FormItem>
                      </Row>
                      <Row>
                        <FormItem
                          :rules="{required:true,message:'API标题不能为空'}"
                          class="width-100-perc"
                          label="API标题："
                          prop="API_title"
                        >
                          <Input
                            v-show="this.current_status !== 'description'"
                            id="inputAPITitleBasic"
                            v-model="form_data.API_title"
                            placeholder="请输入"
                            size="small"
                            style="width: 380px;"
                          ></Input>
                          <p v-show="this.current_status === 'description'">{{ form_data.API_title }}</p>
                        </FormItem>
                      </Row>
                      <Row>
                        <FormItem
                          :rules="{required:true,message:'接口类型不能为空'}"
                          class="width-100-perc"
                          label="接口类型："
                          prop="data_select_apiType"
                        >
                          <Select
                            v-show="this.current_status !== 'description'"
                            id="selectInterfaceType"
                            v-model="form_data.data_select_apiType"
                            :disabled="api_group_disabled"
                            class="yop-length-450"
                            placeholder="请选择接口类型"
                            size="small"
                            style="width: 380px;"
                            @on-change="get_interface_type"
                          >
                            <Option
                              v-for="item in data_apiType_List"
                              :key="item.value"
                              :value="item.value"
                            >{{ item.label }}
                            </Option>
                          </Select>
                          <p
                            v-show="this.current_status === 'description'"
                          >{{ data_select_apiType_text }}</p>
                        </FormItem>
                      </Row>
                      <Row>
                        <FormItem class="width-50-perc" label="接口描述：" @contentTrans="getContent">
                          <text-editor
                            :ke1="2"
                            v-show="this.current_status !== 'description' && hiddenTextEdit"
                            id="textareaDes1"
                            ref="te1"
                            :disabled="text_editor_disabled"
                            style="width: 567px;display: inline-flex"
                          ></text-editor>
                          <p
                            v-show="this.current_status === 'description'"
                            v-html="form_data.description"
                          ></p>
                        </FormItem>
                      </Row>
                    </Form>
                  </Row>
                </transition>
                <loading :show="show_loading_basic"></loading>
              </TabPane>
              <!-- <TabPane id="tabSecurity" :label="label2" name="security_require">
                              <safety_request ref="safetyRequest"  @passSecurityRequire="passSecurityRequire"></safety_request>
              </TabPane>-->
              <TabPane id="tabRequest" :label="label3" name="request_params">
                <param_request
                  ref="param_request"
                  :createdByExitedInterface="analysis.createdByExitedInterface"
                  :reqModelSchema="reqModelSchema"
                  @getServiceType="getServiceType"
                  @model_jump="model_jump"
                  @passHTTPCode="passHTTPCode"
                  @passParamRequest="passParamRequest"
                  @passQueryWay="passQueryWay"
                  @setServiceList="setServiceList"
                  @setUnderLine="setUnderLine"
                  @updateApiOptionParamList="updateApiOptionParamList"
                ></param_request>
              </TabPane>
              <TabPane id="tabResponse" :label="label4" name="response_params">
                <param_response
                  ref="param_response"
                  :createdByExitedInterface="analysis.createdByExitedInterface"
                  :resModelSchema="resModelSchema"
                  @model_jump="model_jump"
                  @passParamResponse="passParamResponse"
                ></param_response>
              </TabPane>
              <TabPane id="apiScenarioExample" :label="label8" name="apiScenarioExample">
                <ApiScenarioExample
                  ref="apiScenarioExample"
                  :status="current_status"
                  :optionsRule="form_data.optionsRule"
                  :apiGroup="form_data.data_select_apiGroup"
                  :apiId="current_apiId"
                  :paramList="apiOptionParamList"
                />
              </TabPane>
              <!-- <TabPane id="tabService" :label="label5" name="point_service">
                              <point_service ref="pointService" @backendNameListFunc="backendNameListFunc" @passParamService="passParamService"></point_service>
              </TabPane>-->
              <TabPane id="tabCallback" :label="label6" name="callback">
                <callback ref="callback" @passParamCallback="passParamCallback"></callback>
              </TabPane>
              <!-- <TabPane id="tab_basic_4" label="端点服务" name="point_service" :disabled="disable_point_service">
                              <point_service ref="pointService"></point_service>
                          </TabPane>
                          <TabPane id="tab_basic_3" label="业务错误码" ref="error_code" name="error_code">
                              <business_error-code ref="errorCode"></business_error-code>
                          </TabPane>
                          <TabPane id="tab_basic_6" label="示例代码" ref="sample_code" name="sample_code">
                              <sample_code ref="sampleCode"></sample_code>
              </TabPane>-->
              <TabPane id="tabCallback" :label="label7" name="apiOption">
                <ApiOptionsForApi
                  ref="apiOptionsForApi"
                  :status="current_status"
                  :optionsRule="form_data.optionsRule"
                  :apiGroup="form_data.data_select_apiGroup"
                  :apiId="current_apiId"
                  :paramList="apiOptionParamList"
                  :resParamList="resParamList"
                  :callbackList="callbackList"
                />
              </TabPane>
            </Tabs>
          </template>
          <loading :show="show_loading"></loading>
        </Card>
      </Row>
    </Card>
  </div>
</template>

<script>
import textEditor from '../../my-components/text-editor/text-editor';
import point_service from '../modify_models/point_service';
import callback from '../modify_models/callback'

import api from '../../../api/api.js';
import loading from '../../my-components/loading/loading'
import sample_code from '../modify_models/sample_code'
import param_request from '../modify_models/param-request'
import param_response from '../modify_models/param-response'
import param_refs from '../modify_models/param-ref';
import QuickAnalysis from '../modify_models/quickAnalysis';
import ModelList from '../modify_models/model_list'
import ApiOptionsForApi from '../modals/apiOptionsForApi.vue'
import ApiScenarioExample from '../modals/apiScenarioExample.vue'

export default {
  name: 'api_basics',
  components: {
    textEditor,
    point_service,
    loading,
    sample_code,
    QuickAnalysis,
    param_request,
    param_response,
    param_refs,
    callback,
    ModelList,
    ApiOptionsForApi,
    ApiScenarioExample
  },
  data () {
    return {
      validate_api_name: '',
      analysis: {
        createdByExitedInterface: false,
        appName: '',
        className: '',
        method: ''
      },
      /**
       * 基础界面变量
       */
      //    api名称
      form_data: {
        data_select_apiGroup: '',
        data_select_apiType: 'COMMON',
        API_title: '',
        API_name: '',
        classic: '',
        endClass: '',
        method: '',
      },
      data_select_apiType_text: '',
      data_select_apiGroup: '',
      API_name: '',
      API_title: '',
      hiddenTextEdit: true,
      // api 请求方式数据绑定
      data_select_apiMethod: '',
      // 当前参数tab页
      current_param_tab: 'request_params',
      // api contentType数据绑定
      data_select_apiContent: [],
      // api contentType下拉框总体数据
      data_apiContent_list: [],
      // 基础信息加载数据绑定
      show_loading: false,
      show_loading_basic: false,
      // 显示收起内容
      basicShow: true,
      // 展开收起按钮文字内容绑定
      button_Content: '收起',
      // api分组数据绑定
      api_group_model: '',
      // api分组disabled
      api_group_disabled: false,
      text_editor_disabled: false,
      // api分组数据列表
      api_group_list: [],
      // api类型选择数据绑定
      data_select_apiType: '',
      abc: 'quickAnalysis',
      // api类型
      data_apiType_List: [],
      // 当前tab页绑定的参数
      tabNow: 'quickAnalysis',
      // 端点服务禁用绑定
      disable_point_service: false,
      // 4个子组件的类型格式绑定
      cascaderType: [],
      // originalID统计
      originalIDCount: 0,
      // 接口uri数据绑定
      interface_uri: '',
      // 接口名称数据绑定
      interface_name: '',
      // 当前端点apiUri内容
      current_content_apiUri: '',
      // 基本类型
      cascader_type: [],
      // 当前apiId
      current_apiId: '',
      // 创建还是修改
      current_status: '',
      //
      check_result: true,
      /**
       * 新建弹窗变量
       */
      // 端点url数据绑定
      endpoint_Url: '',
      // 显示端点url部分
      show_pointUrl: false,
      // 端点类名数据绑定
      endpoint_Name: '',
      // 是否幂等数据绑定
      idempotent: '否',
      // 端点url自定义
      pointUrl_user_defined: false,
      // 适配类型下拉框数据绑定
      data_select_type: 'TRANSFORM',
      // 适配类型下拉框数据
      data_type_List: [
        //    {
        //        value: 'TRANSFORM',
        //        label: '转换'
        //    },
        {
          value: 'MAPPING',
          label: '映射'
        },
        {
          value: 'PASSTHROUGH',
          label: '透传'
        }
      ],
      // 端点协议下拉框数据绑定
      data_select_EndpointProtocol: 'HESSIAN',
      // 端点协议下拉框数据
      data_EndpointProtocol_List: [
        {
          value: 'HESSIAN',
          label: 'hessian'
        }
      ],
      // 端点方法下拉框数据绑定
      data_select_EndpointMethod: '',
      // 端点方法下拉框数据
      data_EndpointMethod_List: [],
      // 当前端点名称内容
      current_content: '',
      // 当前端点url内容
      current_content_url: '',
      // url校验结果
      pass_url: true,

      safetyData: '',
      requestData: '',
      request: '',
      response: '',
      endpoint: '',
      methodService: 'GET',
      paramPosition: '', // 参数位置
      callbackList: [],

      label0: '',
      label1: '',
      label2: '',
      label3: '',
      label4: '',
      label5: '',
      label6: '',
      label7: '',
      label8: '',
      leaveName: '',

      backendNameListData: [],

      bizOrderVariable: null,
      sensitiveVariables: [],
      apiId_now: '',
      version_now: '',
      apiGroup_List: '',
      interface_value: '',

      sourceRef: null,
      allResponseModelList: [],
      allRequestModellList: [],
      requestModelList: [],
      responseModelList: [],
      reqModelSchema: '',
      resModelSchema: '',
      apiOptionParamList: [],
      resParamList: []
    }
  },
  methods: {
    updateApiOptionParamList(list) {
      this.apiOptionParamList = list
    },
    // 初始化函数
    init () {
      this.validate_api_name = (rule, value, callback) => {
        var reg = /^[a-zA-Z]([a-zA-Z_0-9]*[a-zA-Z0-9])*$/
        if (!reg.test(value)) {
          callback(new Error('只允许大小写字母、数字和下划线，必须以大小写字母开头和字母数字结尾'));
        } else {
          callback();
        }
      }
      this.show_loading_basic = true
      var self = this

      api.yop_dashboard_apis_list().then(
        (response) => {
          this.apiGroup_List = [];
          if (this.current_status === 'create') {
            // this.form_data.apigroup ='';
          }
          let apigroup = response.data.data.result;
          this.form_data.apigroup = apigroup[0].groupCode;
          for (var i in apigroup) {
            this.apiGroup_List.push(
              {
                label: apigroup[i].apiGroupCode + '(' + apigroup[i].apiGroupName + ')',
                value: apigroup[i].apiGroupCode,
                name: apigroup[i].apiGroupName
              }
            )
          }
        }
      );

      // api类型列表
      api.yop_apiManagement_apiCommonTypeList().then(
        (response) => {
          let resultTemp = response.data.data.result
          this.data_apiType_List = [];
          let dataTemp = [];
          for (var i in resultTemp) {
            dataTemp.push({
              value: resultTemp[i].value,
              label: resultTemp[i].desc
            })
          }
          // this.data_apiType_List = dataTemp;
          this.data_apiType_List = [{
            value: 'COMMON',
            label: '普通'
          }, {
            value: 'FILE_UPLOAD',
            label: '文件上传'
          }, {
            value: 'FILE_DOWNLOAD',
            label: '文件下载'
          }]
        }
      );
      this.$nextTick(function () {
        self.$refs.param_request.set_current_status(self.current_status)
        self.$refs.param_response.set_current_status(self.current_status)
        self.$refs.callback.set_current_status(self.current_status)
        console.log(self.current_status, '<===父传子current_status6666666')
        this.show_loading_basic = false
      });

      if (this.current_status === 'modify') {
        this.api_group_disabled = true;
        this.getInfo();
      }
      if (this.current_status === 'description') {
        this.disable_point_service = true;
        this.api_group_disabled = true;
        this.text_editor_disabled = true
        this.getInfo();
      }
      if (this.current_status === 'create') {
        this.$nextTick(function () {
          self.$refs.te && self.$refs.te.setContent('', 'create');
          self.$refs.te1 && self.$refs.te1.setContent('', 'create');
        });
      }
      // 默认接口类型 普通触发对应函数处理
      this.$nextTick(() => {
        if(this.current_status === 'create') {
          this.get_interface_type('COMMON');
        }
      })
    },

    async tabClick (tab) {
      this.leaveName = tab;
      var _tabTip = false
      if (tab === 'security_require' || tab === 'callback') {
        if (this.form_data.data_select_apiGroup) { _tabTip = true } else {
          _tabTip = false
          this.$Message.error('请选择apigroup');
        }
      }
      if (tab === 'quickAnalysis' || tab === 'apiOption') {
        if (tab === 'apiOption') {
          this.$refs.callback.saveCheck()
          this.resParamList = await this.$refs.param_response.responseParams()
        }
        _tabTip = true
      }
      if (tab === 'basic') {
        if ((String(localStorage.apiInfo) === 'create' || String(localStorage.apiInfo) === 'create_model')) {
          this.$refs.quickAnalysis.saveCheck().then(result => {
            _tabTip = result;
            if (_tabTip) {
              this.tabNow = tab
            }
          })
        } else {
          this.tabNow = tab
        }
      }
      if (tab === 'request_params') {
        this.saveCheck().then(result => {
          _tabTip = result
          if (_tabTip) {
            this.tabNow = tab
            this.$refs.param_request.reset()
          }
        })
      }
      if (tab === 'response_params') {
        // this.saveCheck().then(result => {_tabTip = result})
        this.$refs.param_request.saveCheck().then(result => {
          _tabTip = result;
          // 路径重复校验 若果重复 不可切换tab响应参数
          let checkPathFlag = this.$refs.param_request.returnCheckPath()
          if (_tabTip && !checkPathFlag) {
            this.tabNow = tab
            this.$refs.param_response.reset()
          }
        })
      }
      if (tab === 'point_service') {
        // this.saveCheck().then(result => {_tabTip = result})
        this.$refs.param_response.saveCheck().then(result => {
          _tabTip = result;
          if (_tabTip) {
            this.tabNow = tab
          }
        })
      }
      if (tab === 'model_list') {
        // this.saveCheck().then(result => {_tabTip = result})
        this.$refs.param_response.saveCheck().then(result => {
          _tabTip = result;
          if (_tabTip) {
            this.tabNow = tab
          }
        })
      }
      if (tab === 'apiScenarioExample') {
        // this.saveCheck().then(result => {_tabTip = result})
          _tabTip = true;
          if (_tabTip) {
            this.tabNow = tab
          }
      }
      return _tabTip;
    },
    // 触发改变服务类型的函数
    getServiceType (value) {
    },
    backendNameListFunc (backendNameList) {
      this.backendNameListData = backendNameList
      this.$refs.param_request.setBackendNameListData(backendNameList);
    },
    getLabel (label, name) {
      var that = this;
      var _tabTip = false
      return h => {
        return h(
          'div',
          {
            style: {
              padding: '8px 16px'
            },
            on: {
              click: e => {
                var _tabTip = that.tabClick(name); // 判断条件是否满足
                if (_tabTip) {
                  this.tabNow = name
                } else {
                  e.stopPropagation(); // 不满足条件则阻止事件冒泡 本质是不让触发tab的on-click事件
                }
              }
            },
            domProps: { // 添加标签,使用自己引入的iconfont图标
              innerHTML: '<p>' + label + '</p>'
            }
          }
        );
      };
    },

    // change接口类型
    get_interface_type (value) {
      this.interface_value = value
      this.$refs.param_request.initSelect();
      this.$refs.param_request.setInterfaceType(value)
      this.$refs.param_response.initSelect();
    },
    //
    // 同步更新选择api分组函数
    updateSelect_apiGroup_modal (val, ifCreate) {
      localStorage.APIgroupCode = val
      if (!ifCreate) {
        // this.$refs.safetyRequest.modal_show("",val);
      }
      this.form_data.data_select_apiGroup = val;
      this.$refs.param_request.getModels(val, '')
      this.$refs.param_response.getModels(val, '')
      this.$refs.callback.getSpisList(val)
    },
    // model跳转
    model_jump (val) {
      this.current_param_tab = 'refs_params'
      this.$refs.param_ref.setCurrentModel(val)
    },
    // 获取信息函数
    getInfo () {
      // 其他的模块信息
      api.yop_api_manage_detail({
        apiId: this.current_apiId
      }).then(
        (response) => {
          var current_status = this.current_status

          var response = response.data.data.result
          this.apiId_now = response.apiId
          this.version_now = response.version

          var basic = response.basic
          var request = response.request
          var bizOrderVariable = response.bizOrderVariable
          var sensitiveVariables = response.sensitiveVariables

          var responsePass = response.response
          var callbacks = response.callbacks
          var exampleScenes = response.exampleScenes
          // 基本信息的设置
          this.basicInfoHandle(basic, true);
          this.$refs.param_request.setRequestData(request, bizOrderVariable, sensitiveVariables);
          this.$refs.quickAnalysis && this.$refs.quickAnalysis.setData(response.basic);
          this.$refs.param_response.setRequestData(responsePass);
          this.$refs.callback.setRequestData(callbacks);
          this.$refs.apiScenarioExample.setValue(exampleScenes || [])
          this.$nextTick(() => {
            this.tabNow = 'quickAnalysis'
            setTimeout(() => {
              this.tabClick('basic')
            }, 100)
          })
        }
      )
    },
    // 获取富文本数据
    getContent (msg) {
      // console.log(this.interface_name);
      // console.log(util.getLength(this.interface_name));
      // console.log(this.$refs.te.getContent(),'<===heml');
      // console.log(this.$refs.te.getContent_text(),'<====text')
      // console.log(util.getLength(this.$refs.te.getContent_text()));
    },
    // 获取基本类型
    getCascaderType (val) {
      this.cascader_type = val;
    },
    saveFormat_request (requestData) {
      // 修改
      if (localStorage.apiInfo == 'modify') {
        requestData.paramForm.prependApiGroup = requestData.paramForm.prependApiGroup.substr(0, requestData.paramForm.prependApiGroup.length - 1);
        console.log('requestData.paramForm.prependApiGroup')
        console.log(localStorage.apiInfo)
        console.log(requestData.paramForm.prependApiGroup)
      }

      console.log('----requestData.paramForm----')
      console.log(requestData.paramForm)
      let _prependApiGroup = requestData.paramForm.prependApiGroup.trim()
      // if (_prependApiGroup[_prependApiGroup.length - 1] !== '/') {
      //   // eslint-disable-next-line no-unused-expressions
      //   _prependApiGroup + '/'
      // }
      // 再加校验 如果不带斜线加上 '/'
      if (_prependApiGroup.substr(_prependApiGroup.length-1, 1) !== '/') {
        _prependApiGroup = `${_prependApiGroup}/`
      }
      var contents = ''
      var paramPosition = requestData.paramPosition
      if (paramPosition === 'query') {
        var request = {
          path: requestData.paramForm.prependPath.trim() + requestData.paramForm.requestVersion.trim() + _prependApiGroup + requestData.paramForm.requestUrl.trim(),
          httpMethod: requestData.paramForm.httpMethod,
          parameters: requestData.schema
        }
      } else {
        var contentType = requestData.paramForm.contentType
        if (contentType === 'application/json') {
          if (this.analysis.createdByExitedInterface) {
            const _data_propertiesList = this.reqModelSchema
            contents = {
              'application/json': {
                schema: `{"$ref":"#/components/schemas/${_data_propertiesList[contentType].modelRef}"}`,
                examples: requestData.examples
              }
            }
          } else {
            contents = {
              'application/json': {
                schema: `{"$ref":"#/components/schemas/${requestData.paramForm.model}"}`,
                examples: requestData.examples
              }
            }
          }
        }
        if (contentType === 'application/x-www-form-urlencoded') {
          contents = {
            'application/x-www-form-urlencoded': {
              schema: JSON.stringify(requestData.schema),
              examples: requestData.examples
            }
          }
        }
        if (contentType === 'multipart/form-data') {
          contents = {
            'multipart/form-data': {
              schema: JSON.stringify(requestData.schema),
              examples: requestData.examples
            }
          }
        }
        if (contentType === 'application/octet-stream') {
          contents = {
            'application/octet-stream': {
              schema: '{"type": "string","format": "binary"}'
            }
          }
        }
        var request = {
          httpMethod: requestData.paramForm.httpMethod,
          path: requestData.paramForm.prependPath.trim() + requestData.paramForm.requestVersion.trim() + _prependApiGroup + requestData.paramForm.requestUrl.trim(),
          requestBody: {
            description: requestData.paramForm.description,
            contents: contents// TODO:老数据支持多种类型 看看怎么展示
          }
        }
      }
      return {
        ...request,
        encrypt: requestData.paramForm.encrypt
      }
    },
    // 表格数据安全需求格式化递归处理空处理
    saveFormat_safetyRequest (safetyData) {
      var security = {
        securities: safetyData.paramForm,
        inheritFlag: safetyData.paramTag
      }
      console.log('安全信息验证通过', security)
      return security
    },
    // 获取请求参数模块的参数
    passQuickAnalysis (val) {
      const { allResponseModelList, allRequestModelList, requestModelList, responseModelList, reqModelSchema, resModelSchema, ...data } = val
      console.log(requestModelList)
      console.log(responseModelList)
      this.analysis = data
      this.allResponseModelList = allResponseModelList || []
      this.allRequestModellList = allRequestModelList || []
      this.responseModelList = responseModelList || []
      this.requestModelList = requestModelList || []
      this.reqModelSchema = reqModelSchema
      this.resModelSchema = resModelSchema
    },
    // 获取请求参数模块的参数
    passParamRequest (val) {
      this.sensitiveVariables = val.sensitiveVariables
      this.bizOrderVariable = val.bizOrderVariable
      console.log('val.paramFormval.paramFormval.paramFormval.paramFormval.paramForm')
      console.log(val.paramForm)
      this.requestData = this.saveFormat_request(val)
      this.request = val
      if (val.paramForm.contentType === 'application/json') {
        this.requestModelList = this.requestModelList.length ? this.requestModelList : this.allRequestModellList
      } else {
        this.requestModelList = []
      }
    },
    passParamService (val) {
      var data_ConstantListTemp = []
      if (val.data_ConstantList) {
        for (var j = 0; j < val.data_ConstantList.length; j++) {
          var data_ConstantListJ = val.data_ConstantList[j]
          var obj = {}
          for (var i in data_ConstantListJ) {
            if (i === '_value' || i === 'value') {
              obj['value'] = data_ConstantListJ[i]
            }
            if (i === 'backend_name' || i === 'backendName') {
              obj['backendName'] = 'x-yop-const-' + data_ConstantListJ[i]
            }
            if (i === 'location') {
              obj['location'] = data_ConstantListJ[i]
            }
            if (i === '_description' || i === 'description') {
              obj['description'] = data_ConstantListJ[i]
            }
          }
          data_ConstantListTemp.push(obj);
        }
      }
      console.log(data_ConstantListTemp, 'data_ConstantListTemp');

      var data_SystemListTemp = []
      if (val.data_SystemList) {
        for (var j = 0; j < val.data_SystemList.length; j++) {
          var data_SystemListJ = val.data_SystemList[j]
          var obj = {}
          for (var i in data_SystemListJ) {
            if (i === 'sysParam' || i === 'systemName') {
              obj['systemName'] = data_SystemListJ[i]
            }
            if (i === 'backend_name' || i === 'backendName') {
              obj['backendName'] = data_SystemListJ[i]
            }
            if (i === 'location') {
              obj['location'] = data_SystemListJ[i]
            }
          }
          data_SystemListTemp.push(obj);
        }
      }
      this.endpoint = {
        type: val.paramForm.service_type,
        serviceName: val.paramForm.service,
        path: val.paramForm.query_Url,
        method: this.methodService,
        connectionTimeout: val.paramForm.link_timeout * 1,
        readTimeout: val.paramForm.read_timeout * 1,
        constantParameters: data_ConstantListTemp,
        systemParameters: data_SystemListTemp
      }
      if (data_ConstantListTemp.length == 0) {
        delete this.endpoint.constantParameters
      }
      if (data_SystemListTemp.length == 0) {
        delete this.endpoint.systemParameters
      }
    },
    passQueryWay (val) {
      this.methodService = val
    },
    passHTTPCode (val) {
      this.$refs.param_response.setHTTPCode(val);
    },
    setServiceList (mapping_list_temp, initData) {
    },
    setUnderLine (underLine) {
    },
    passSecurityRequire (val) {
      this.safetyData = this.saveFormat_safetyRequest(val)
    },
    passParamResponse (val) {
      if (val.contentType === 'application/json') {
        this.responseModelList = this.responseModelList.length ? this.responseModelList : this.allResponseModelList
      } else {
        this.responseModelList = []
      }

      this.response = val
    },
    passParamCallback (val) {
      this.callbackList = val.paramList
    },
    changeReqModelSchema (val) {
      this.reqModelSchema = val
    },
    updateModelData ({ requestList, responseList }) {
      this.requestModelList = requestList
      this.responseModelList = responseList
    },
    saveCheck () {
      return this.$refs['form_data'].validate((valid) => {
        if (valid) {
          return true
        } else {
          this.$Message.error('请检查基本信息是否填写完全');
          return false
        }
      });
    },
    // 保存按钮调用方法
    async save () {
      this.show_loading = true;
      var basicInfo = false
      this.resParamList = await this.$refs.param_response.responseParams()
      this.saveCheck().then(result => {
        basicInfo = result
      })

      var param_request = false
      this.$refs.param_request.saveCheck().then(result => {
        param_request = result
      })
      var param_response = false
      this.$refs.param_response.saveCheck().then(result => {
        param_response = result
      })
      var callback = this.$refs.callback.saveCheck()
      var apiScenarioExample = false
      this.$refs.apiScenarioExample.saveCheck().then(result => {
        apiScenarioExample = result
      })
      var self = this
      setTimeout(function () {
        // 基本信息的验证
        self.show_loading = false;
        if (basicInfo && param_request &&
          param_response &&
          callback && apiScenarioExample) {
          let paramsTemp = {
            ...self.analysis,
            basic: {
              name: self.form_data.API_name,
              apiType: self.form_data.data_select_apiType, // 接口类型
              apiGroup: self.form_data.data_select_apiGroup, // api分组
              title: self.form_data.API_title, // 接口名称
              classic: self.form_data.classic, // api分类
              description: (self.$refs.te && self.$refs.te.getContent_text()) || (self.$refs.te1 && self.$refs.te1.getContent_text()), // 接口描述
              optionsRule: self.$refs.apiOptionsForApi.getOptionsRule()
            },
            // security: self.safetyData,
            request: self.requestData,
            response: self.response,
            callbacks: self.callbackList,
            bizOrderVariable: self.bizOrderVariable,
            sensitiveVariables: self.sensitiveVariables,
            definitions: self.definitions,
            exampleScenes: self.$refs.apiScenarioExample.getExampleScenes(),
          }
          let paramsTempM = {
            ...self.analysis,
            apiId: self.apiId_now,
            version: self.version_now,
            basic: {
              name: self.form_data.API_name,
              apiType: self.form_data.data_select_apiType, // 接口类型
              apiGroup: self.form_data.data_select_apiGroup, // api分组
              title: self.form_data.API_title, // 接口名称
              classic: self.form_data.classic, // api分类
              description: (self.$refs.te && self.$refs.te.getContent_text()) || (self.$refs.te1 && self.$refs.te1.getContent_text()), // 接口描述
              optionsRule: self.$refs.apiOptionsForApi.getOptionsRule()
            },
            // security: self.safetyData,
            request: self.requestData,
            response: self.response,
            callbacks: self.callbackList,
            bizOrderVariable: self.bizOrderVariable,
            sensitiveVariables: self.sensitiveVariables,
            definitions: self.definitions,
            exampleScenes: self.$refs.apiScenarioExample.getExampleScenes(),
          }
          if (self.current_status === 'create') {
            if (!paramsTemp.bizOrderVariable || !paramsTemp.bizOrderVariable.variableName) {
              delete paramsTemp['bizOrderVariable']
            }
            let content = 'api创建成功'
            if (self.sourceRef) {
              paramsTemp['sourceRef'] = self.sourceRef
              content = 'api升级成功'
            }
            // 如果是data-scope创建方式，添加integrationId
            if (self.analysis.createType === 'data-scope' && self.analysis.integrationId) {
              paramsTemp['integrationId'] = self.analysis.integrationId
            }
            if (self.analysis.createdByExitedInterface) {
              let _responseModel = {}
              let _requestModel = {}
              const requestModel = self.reqModelSchema['application/json']
              const responseModel = self.resModelSchema
              const findRequestModel = self.requestModelList.find(item => item.modelRef === requestModel.modelRef)
              const findResponseModel = self.responseModelList.find(item => item.modelRef === responseModel.modelRef)
              if (self.response && self.response.contentType && self.response.contentType === 'application/json') {
                _responseModel = {
                  ...responseModel,
                  schema: (findResponseModel && findResponseModel.schema) || (findRequestModel && findRequestModel.schema) || responseModel.schema,
                  nestModel: self.responseModelList.map(item => {
                    const { modelType, ..._data } = item
                    return _data
                  })
                }
              }
              if (self.request && self.request.paramForm && self.request.paramForm.contentType && self.request.paramForm.contentType === 'application/json') {
                _requestModel = {
                  ...requestModel,
                  schema: (findRequestModel && findRequestModel.schema) || (findResponseModel && findResponseModel.schema) || requestModel.schema,
                  nestModel: self.requestModelList.map(item => {
                    const { modelType, ..._data } = item
                    return _data
                  })
                }
              }
              paramsTemp = {
                ...paramsTemp,
                response: {
                  ...paramsTemp.response,
                  responseModel: _responseModel
                },
                request: {
                  ...paramsTemp.request,
                  requestModel: _requestModel
                }
              }
            }
            api.yop_api_manage_create(paramsTemp).then(
              (response) => {
                if (response.status === 'success') {
                  self.$Modal.success({
                    title: '成功',
                    content
                  });
                  self.returnList();
                }
                if (response.status === 'error') {
                  self.$Modal.error({
                    title: '错误',
                    content: response.message
                  });
                }
              }
            );
          }
          if (self.current_status === 'modify') {
            if (!paramsTempM.bizOrderVariable || !paramsTempM.bizOrderVariable.variableName) {
              delete paramsTempM['bizOrderVariable']
            }
            api.yop_api_manage_update(paramsTempM).then(
              (response) => {
                if (response.status === 'success') {
                  self.$Modal.success({
                    title: '成功',
                    content: 'api修改成功'
                  });
                  self.returnList();
                }
                if (response.status === 'error') {
                  self.$Modal.error({
                    title: '错误',
                    content: response.message
                  });
                }
              }
            );
          }
          if (self.current_status === 'description') {
            if (self.saveFormat_safetyRequest(self.safetyData).length > 0) {
              delete paramsTempM['securities'];
            }
            if (!paramsTempM.bizOrderVariable || !paramsTempM.bizOrderVariable.variableName) {
              delete paramsTempM['bizOrderVariable']
            }
            api.yop_api_manage_update(paramsTempM).then(
              (response) => {
                if (response.status === 'success') {
                  self.$Modal.success({
                    title: '成功',
                    content: 'api修改成功'
                  });
                  self.returnList();
                }
                if (response.status === 'error') {
                  self.$Modal.error({
                    title: '错误',
                    content: response.message
                  });
                }
              }
            );
          }
        } else {
          self.$Message.error('请检查信息是否填写完全');
        }
      }, 1000)
    },
    // 判断数组是否包含某值
    IsInArray (arr, val) {
      var testStr = ',' + arr.join(',') + ',';
      return testStr.indexOf(',' + val + ',') != -1;
    },
    // 基础信息信息处理
    basicInfoHandle(basic, ifCreate) {
      // 手动change参数基本信息的接口类型改变参数信息的HTTP请求方法
      this.get_interface_type(basic.apiType);
      this.form_data.API_name = basic.name
      this.form_data.classic = basic.classic
      this.form_data.data_select_apiGroup = basic.apiGroup
      this.updateSelect_apiGroup_modal(basic.apiGroup, ifCreate)// apigroup改变调用其他模块接口
      this.form_data.API_title = basic.title;
      this.form_data.data_select_apiType = basic.apiType;
      this.form_data.optionsRule = basic.optionsRule;
      if (basic.apiType === 'COMMON') { this.data_select_apiType_text = '普通' }
      if (basic.apiType === 'FILE_UPLOAD') { this.data_select_apiType_text = '文件上传' }
      if (basic.apiType === 'FILE_DOWNLOAD') { this.data_select_apiType_text = '文件下载' }
      this.show_loading_basic = false;
      if (basic.description) {
        localStorage.tinymceContent = basic.description;
        this.form_data.description = basic.description;
        this.$nextTick(function () {
          this.$refs.te && this.$refs.te.setContent(basic.description);
          this.$refs.te1 && this.$refs.te1.setContent(basic.description);
          localStorage.description_api = basic.description
        });
      }
      // this.$refs.te.setLocalstorage();
    },
    // 安全需求继承分组遍历全部数组分组处理函数
    format_safeRequest (items, array) {
      let dataTemp = [];
      for (var p in items) {
        for (var i in array) {
          if (array[i].name === items[p].name) {
            if (array[i].custom) {
              dataTemp.push({
                name: items[p].name,
                custom: this.selfDefine_format(items[p].custom, array[i].custom),
                desc: array[i].desc
              })
            } else {
              dataTemp.push({
                name: items[p].name,
                desc: array[i].desc
              })
            }
          }
        }
      }
      return dataTemp;
    },
    // 安全需求自定义分组处理函数
    selfDefine_format (customGet, customTotal) {
      let dataReturn = [];
      for (var i in customTotal) {
        if (customGet) {
          if (this.IsInArray(customGet, customTotal[i])) {
            dataReturn.push({
              name: customTotal[i],
              check: true
            })
          } else {
            dataReturn.push({
              name: customTotal[i],
              check: false
            })
          }
        } else {
          dataReturn.push({
            name: customTotal[i],
            check: false
          })
        }
      }
      return dataReturn;
    },
    // 安全需求api遍历全部数组分组处理函数
    format_safeRequest_spec (items, array) {
      let dataTemp = [];
      for (var i in array) {
        if (items) {
          let nameTemp = []
          for (var j in items) {
            nameTemp.push(items[j].name);
          }
          if (this.IsInArray(nameTemp, array[i].name)) {
            for (var p in items) {
              if (array[i].name === items[p].name) {
                if (array[i].custom) {
                  dataTemp.push({
                    name: items[p].name,
                    custom: this.selfDefine_format(items[p].custom, array[i].custom),
                    desc: array[i].desc,
                    check: true
                  })
                } else {
                  dataTemp.push({
                    name: items[p].name,
                    desc: array[i].desc,
                    check: true
                  })
                }
              }
            }
          } else {
            if (array[i].custom) {
              dataTemp.push({
                name: array[i].name,
                custom: this.selfDefine_format(false, array[i].custom),
                desc: array[i].desc,
                check: false
              })
            } else {
              dataTemp.push({
                name: array[i].name,
                desc: array[i].desc,
                check: false
              })
            }
          }
        } else {
          if (array[i].custom) {
            dataTemp.push({
              name: array[i].name,
              custom: this.selfDefine_format(false, array[i].custom),
              desc: array[i].desc,
              check: false
            })
          } else {
            dataTemp.push({
              name: array[i].name,
              desc: array[i].desc,
              check: false
            })
          }
        }
      }
      return dataTemp;
    },

    // apiuri 校验函数
    apiUriCheck () {
      let params = {
        groupCode: this.api_group_model,
        apiUri: this.interface_uri
      }
      api.yop_apiManagement_apiConfig_apiUriValidate(params).then(
        (response) => {
          if (response.data.status !== 'success') {
            this.$ypMsg.notice_warning(this, response.data.message, 'apiUri校验不通过请修改');
            this.check_result = false;
          } else {
            this.check_result = true;
          }
        }
      )
    },

    // 返回api列表
    returnList () {
      // this.$refs.te.destroySelf();
      // var el =document.querySelectorAll('textarea');
      // for(var i =0;i<el.length;i++){
      //     el[i].removeAttribute('id');
      // }
      // this.$refs.request.removeTextEditorId();
      this.$refs.te && this.$refs.te.setContent(localStorage.description_api);
      this.$refs.te1 && this.$refs.te1.setContent(localStorage.description_api);
      this.show_loading_basic = true
      this.hiddenTextEdit = false
      var that = this
      setTimeout(function () {
        that.$destroy();
        that.$store.commit('removeTag', 'api_basics');
        that.$store.commit('closePage', 'api_basics');
        // that.$router.replace({
        //   name: '/api/manage/list'
        // });
        that.$router.push({
          name: '/api/manage/list',
          params: {
            name: that.$route.params.name,
            path: that.$route.params.path,
            apiType: that.$route.params.apiType,
            method: that.$route.params.method,
            routeStatus: that.$route.params.routeStatus,
            apiGroup: that.$route.params.apiGroup,
            status: that.$route.params.status,
            _pageNo: that.$route.params._pageNo,
          }
        });
      }, 100)
    },

    /**
     * 新建弹窗方法
     */
    // 点击自定义checkbox
    check_url (data) {
      this.show_pointUrl = data;
    },
    // 本地校验
    IsLocal (string) {
      if (string.trim().substring(0, 32) === 'com.yeepay.g3.yop.center.combapi') {
        if (this.data_select_type !== 'LOCAL') {
          this.$Modal.warning({
            title: '警告',
            content: '您配置的端点方法适配类型为本地，系统将帮您的适配类型转化为本地！',
            onOk: () => {
              this.data_select_type = 'LOCAL';
            }
          });
        }
      }
    },
    // url校验
    IsUrl (str_url) {
      let strRegex = '^((https|http|ftp|rtsp|mms)?://)' +
        '?(([0-9a-z_!~*\'().&=+$%-]+: )?[0-9a-z_!~*\'().&=+$%-]+@)?' + // ftp的user@
        '(([0-9]{1,3}.){3}[0-9]{1,3}' + // IP形式的URL- **************
        '|' + // 允许IP和DOMAIN（域名）
        '([0-9a-z_!~*\'()-]+.)*' + // 域名- www.
        '([0-9a-z][0-9a-z-]{0,61})?[0-9a-z].' + // 二级域名
        '[a-z]{2,6})' + // first level domain- .com or .museum
        '(:[0-9]{1,4})?' + // 端口- :80
        '((/?)|' + // a slash isn't required if there is no file name
        '(/[0-9a-z_!~*\'().;?:@&=+$,%#-]+)+/?)$';
      let re = new RegExp(strRegex);
      if (re.test(str_url)) {
        return true;
      } else {
        return false;
      }
    }

  },
  created () {
    this.label0 = this.getLabel('<span style=\'color:red;font-size:18px;vertical-align:top;\'>*</span>快速解析', 'quickAnalysis');
    this.label1 = this.getLabel('<span style=\'color:red;font-size:18px;vertical-align:top;\'>*</span>基本信息', 'basic');
    this.label2 = this.getLabel('安全需求', 'security_require');
    this.label3 = this.getLabel('<span style=\'color:red;font-size:18px;vertical-align:top;\'>*</span>请求参数', 'request_params');
    this.label4 = this.getLabel('<span style=\'color:red;font-size:18px;vertical-align:top;\'>*</span>响应参数', 'response_params');
    this.label5 = this.getLabel('模型列表', 'model_list');
    // this.label5 = this.getLabel("<span style='color:red;font-size:18px;vertical-align:top;'>*</span>后端服务", "point_service");
    this.label6 = this.getLabel('回调', 'callback');
    this.label7 = this.getLabel('API选项', 'apiOption');
    this.label8 = this.getLabel('API场景示例', 'apiScenarioExample');

    // if(location.href.indexOf("api_basics?apiId=") != -1){
    //     var apiId=location.href.substring(location.href.indexOf("api_basics?apiId=")+17,location.href.length);
    //     this.current_apiId = apiId;
    //     this.current_status = 'modify';
    // }else if(location.href.indexOf("home?apiId=") != -1){
    //     var apiId=location.href.substring(location.href.indexOf("home?apiId=")+11,location.href.length);
    //     this.current_apiId = apiId;
    //     this.current_status = 'modify';
    // }else{
    //     this.current_status = 'create';
    // }
  },
  mounted () {
    // 创建或升级, 有sourceRef，就是升级
    const {sourceRef} = this.$route.query;
    console.log(sourceRef);
    if (sourceRef !== 'null') {
      this.sourceRef = sourceRef
    }
    this.analysis.createdByExitedInterface = false

    console.log(localStorage.apiInfo)
    if (localStorage.apiInfo) {
      if (String(localStorage.apiInfo) === 'create' || String(localStorage.apiInfo) === 'create_model') {
        this.current_status = 'create';
        this.analysis.createdByExitedInterface = true
      } else if (String(localStorage.apiInfo) === 'modify') {
        // var apiId=location.href.substring(location.href.indexOf("api_basics?apiId=")+17,location.href.length);
        this.current_apiId = localStorage.apiId;
        this.current_status = 'modify';
      } else if (String(localStorage.apiInfo) === 'description') {
        this.current_apiId = localStorage.apiId;
        this.current_status = 'description';
      } else {
        this.current_status = 'create';
      }
    } else {
      this.current_status = 'create';
    }
    console.log(this.current_status)
    this.init();
  },
  beforeRouteLeave (to, from, next) {
    this.$destroy();
    next();
  },
  beforeDestroy () {
    // console.log(this.$refs.te);
    // this.$refs.te.destroySelf();
  },
  destroyed () {
    // this.$store.commit('closePage', 'api_basics');
  }
};
</script>

<style lang="less">
/*.ivu-collapse {*/
/*background-color : #FFFFFF;*/
/*}*/
</style>
