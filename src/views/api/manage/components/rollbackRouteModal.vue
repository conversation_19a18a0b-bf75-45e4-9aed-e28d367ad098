<template>
  <Modal v-model="show" width="500" :closable="false">
    <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
      <Icon type="ios-information-circle"></Icon>
      <span>路由回滚</span>
    </p>
    <div style="text-align:left;font-size: 12px;">
      <div
        style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;"
      >
        <i class="ivu-icon ivu-icon-help-circled"></i>
      </div>
      <div style="display:inline-block;margin:0 0 0 56px;width:75%">
        <p style="color:red"></p>
        <p>您正在将生产环境中的路由回滚至当前版本，回滚后将直接生效，您确定要继续回滚吗？</p>
        <label for class="reasonType">
          <span style="color:red;font-size: 12x;">*</span>回滚原因：
        </label>
        <Input
          type="textarea"
          v-model="params.cause"
          style="width:85%;font-size: 12x;margin-top:8px;"
          placeholder
          placeholder-style="font-size:12px;"
          :maxlength="60"
        ></Input>
      </div>
    </div>
    <div slot="footer" style="border:0;">
      <Button id="api_mange_btn3" type="ghost" @click="cancel">取消</Button>
      <Button id="api_mange_btn4" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import Api from '~/api/newApiManage/route'
export default {
  data () {
    return {
      show: false,
      showLoading: false,
      params: {
        deployId: '',
        cause: ''
      }
    }
  },
  methods: {
    showModal (id) {
      this.params.deployId = id
      this.params.cause = ''
      this.show = true
      this.showLoading = false
    },
    cancel () {
      this.show = false
    },
    confirm () {
      this.showLoading = true
      if (!this.params.cause) {
        this.$Message.error('请输入回滚原因')
        return false
      }
      Api.rollbackRoute(this.params)
        .then(res => {
          const { status, message, solutions } = res
          if (status === 'success') {
            this.cancel()
            this.$parent.getList()
          } else {
            this.$ypMsg.notice_error(
              this,
              '回滚错误',
              message,
              solutions
            )
          }
        })
        .finally(() => {
          this.showLoading = false
        })
    }
  }
}
</script>

<style>
</style>