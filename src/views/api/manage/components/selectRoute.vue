<template>
  <div>
		<div style="" v-show="routeList && routeList.length > 1">
			<Checkbox
					:indeterminate="indeterminate"
					:value="checkAll"
					@click.prevent.native="handleCheckAll">全选</Checkbox>
		</div>
		<CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
			<div v-for="(item,index) in routeList" :key="index.toString()+'_0'" >
				<Checkbox 
					:label="item.path+' '+item.httpMethod" 
				>
					{{item.httpMethod}}{{item.path}}
				</Checkbox>
			</div>
		</CheckboxGroup>
  </div>
</template>

<script>
export default {
  props: ['routeList'],
  data () {
    return {
      indeterminate: true,
      checkAll: false,
      checkAllGroup: []
    }
  },
  methods: {
    handleCheckAll () {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;

      if (this.checkAll) {
        this.checkAllGroup = this.routeList.map(item => item.path + ' ' + item.httpMethod);
        this.updateData()
      } else {
        this.checkAllGroup = [];
        this.updateData()
      }
    },
    checkAllGroupChange (data) {
      if (data.length === this.routeList.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
      this.updateData()
    },
    updateData () {
      this.$emit('input', this.checkAllGroup.map(item => {
        const list = item.split(' ')
        return {
          path: list[0],
          httpMethod: list[1]
        }
      }))
    }
  }
}
</script>

<style>

</style>