<template>
	<div class="serveArguments">
		<template>
			<el-table
				@cell-mouse-enter="hoverChange"
				key="edit-serveArguments"
				:data="serviceResponse"
				border
        row-key="id" 
				:disabled="true"
				:cell-style="{textAlign: 'center',fontSize: '12px' }"
				:header-cell-style="{textAlign: 'center', padding: '5px', fontSize: '12px'}"
        :tree-props="{children: 'sub'}"
			>
				<el-table-column prop="date" label="后端参数名称">
					<template slot-scope="scope">
						<div class="param-name">
							<el-tooltip class="item"
              v-if="!noTip"
							placement="top-start"
							title="标题"
							trigger="hover"
							:content="scope.row.tip ? scope.row.tip : '无'">
								<span class="icon" :class="scope.row.type"><Icon type="information-circled"></Icon></span>
							</el-tooltip>
							<el-tooltip class="item" effect="dark" :content="scope.row.tip ? scope.row.tip : '无'" placement="top">
								<el-input :disabled="disabledFlag" class="param-name-input" size="small" v-model="scope.row.value" clearable placeholder="后端参数名称" />
							</el-tooltip>
						</div>
					</template>
				</el-table-column>
				<el-table-column prop="in" label="后端参数位置" width="320">
					<template slot-scope="scope">
						<el-select 
							style="width: 100%" 
							v-model="scope.row.in" size="small"
							@change="(value) => { onInSelectChange(value, scope.row)}">
							<el-option v-for="item in valueList" 
								:value="item" 
								:key="item" 
								>{{ item }}</el-option>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="name" label="API参数名称" width="300">
					<template slot-scope="scope">
						<el-select
              :disabled="scope.row.type === 'delete' || scope.row.disabledFlag || disabledFlag"
							style="width: 100%"
							v-model="scope.row.name"
							size="small"
							@change="onSelectChange(value, scope.row)"
						>
							<div v-if="scope.row.level === 1 || !scope.row.level">
                <el-option v-for="item in apiSelectData" :value="item.value" :key="item.key">{{ item.value }}</el-option>
              </div>
              <div v-else>
                <el-option v-for="item in scope.row.subSelect" :value="item.value" :key="item.name">{{ item.value }}</el-option>
              </div>
						</el-select>
					</template>
				</el-table-column>
        <el-table-column prop="name" label="操作" width="80">
					<template slot-scope="scope">
						<Button v-if="scope.row.type === 'delete'" type="primary" @click="deleteOne(scope.row, scope.$index)" size="small">移除</Button>
					</template>
				</el-table-column>
			</el-table>
		</template>
	</div>
</template>
<script>
export default {
  props: ['value', 'isDisplay', 'type', 'serviceResponse', 'disabledFlag', 'noTip'],
  data () {
    return {
      apiSelectData: [],
      apiSubSelectData: [],
    }
  },
  mounted() {
  },
  computed: {
    valueList () {
      return this.$store.state.apiRoute.valueListMap['resSelect']
    },
    parametersList () {
      return this.$store.state.apiRoute.parametersList
    },
    // 列表key 设置
    onlykey(row,data) {
      // this.serviceResponse.forEach((element, i) => {
        
      // });
      // console.log(row,'随即将数')
      // console.log(data,'data11')
      return 
    }
  },
  methods: {
    setServiceResponse(data) {
      this.serviceResponse = data
      this.dealRes()
    },
    // 参数列表移入展示相关提示
    // row, column, cell, event
    hoverChange(row, column, cell, event) {
      // this.$Message.success('发布成功');

    },
    // 处理相应数据
    dealRes() {
      this.serviceResponse.forEach((el, k) => {
        el.id =` ${k}${el.name}${ Math.floor(Math.random() * 10) + 1}`
        if(el.sub) {
          el.childrenArr = []
        }
      })

      this.serviceResponse.forEach(element => {
        // 处理有子项的数据 如果有则子项数据使用 subSelect: subArr,
        if(element.sub) {
          element.sub.forEach((j,k) => {
            j.id = `${k}sub${j.name}${ Math.floor(Math.random() * 10) + 1}`
            element.childrenArr.push({name: j.name, value: j.name})
            j.subSelect = element.childrenArr
          })
        }
      });
      this.$nextTick(() => {
        console.log(this.serviceResponse, '</--列表数据')
        this.apiSelectData = this.serviceResponse.filter(item => {
          return item.type !== 'delete'
        })
        this.apiSelectData.forEach((item) => {
          item.key = `${item.name}${ Math.floor(Math.random() * 10) + 1}`
        })
      })
    },
    onSelectChange (value, obj) {
      if (!obj.name) {
        obj.name = value
      }
      const item = this.parametersList.find(item => item.name === value)
      if(item) {
        if (item.array && obj.in === 'RPC_CONTEXT') {
          obj.in = 'PARAM'
        }
        obj.canEditArray = item.array
        obj.array = item.array
      }
    },
    onInSelectChange (value, obj) {
      obj.in = value
      if (obj.in === 'RPC_CONTEXT') {
        obj.index = ''
        obj.array = false
      } else {
        obj.index = 0
      }
    },
    // 可以删除 标移除的项
    deleteOne (row) {
      this.$Modal.confirm({
        title: '确认删除',
        content: '您确定要删除此参数？',
        onOk: () => {
          this.serviceResponse = this.serviceResponse.filter((item) => {
            return item.name !== row.name
          });
          this.$emit('refreshResData', this.serviceResponse)
        }
      });
    },
    addOne () {
      this.value.push({
        name: '',
        in: this.type === 'DUBBO' ? 'PARAM' : 'BODY',
        value: '',
        required: false,
        array: false,
        index: 0
      })
      this.$emit('input', this.value)
    }
  }
}
</script>
<style lang="less" scoped>
	.serveArguments {
		display: flex;
		align-items: flex-end;
		margin-right: 20px;
		.add-icon {
			margin-left: 10px;
			cursor: pointer;
		}
	}
</style>
<style lang="less">
	.serveArguments {
		.param-name {
			// .test2 {
			// 	color: #E6A23C;
			// }
			.delete {
				color: #ff3300;
			}
			.repeat {
				color: #E6A23C;
			}
			.add {
				color: #3399ff;
			}
			.icon {
				padding: 0 10px 0 0;
				cursor: pointer;
				font-size: 18px;
			}
			display: flex;
			align-items: center;
      .param-name-input {
        width: 80%;
        position: relative;
        left: 10%;
      }
		}
		// table 列表hover效果
		.el-table--enable-row-hover .el-table__body tr:has(.add):hover>td {
			background-color: #F3FAFF;
		}
		.el-table--enable-row-hover .el-table__body tr:has(.delete):hover>td {
			background-color: #FFF3F3;
		}
		.el-table--enable-row-hover .el-table__body tr:has(.repeat):hover>td {
			background-color: #FFFEF3;
		}
    // 嵌套 箭头
    .el-table__row--level-0 {
      > td .cell {
        position: relative;
        .el-table__expand-icon {
          position: absolute;
          left: 10%;
          top: 7px;
        }
      }
    }
    // 子项
    .el-table__row--level-1 {
      .el-tooltip {
        position: relative;
        left: 10px;
      }
      .param-name {
        position: relative;
        left: 9%;
      }
      .el-table__placeholder, .el-table__indent{
        display: none;
      }
    }
	}
</style>