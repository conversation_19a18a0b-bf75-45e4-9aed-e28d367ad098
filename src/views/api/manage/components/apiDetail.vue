<template>
	<Modal v-model="show" width="1000" :closable="false">
		<p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
			<Icon type="ios-information-circle"></Icon>
			<span>api详情</span>
		</p>
		<div>
			<p>基本信息</p>
			<Row>
				<Col>
					<Form ref="form_data_basics" :model="form_data_basics" :label-width="120" inline>
						<FormItem class="width-100-perc" label="API名称：" prop="name">
							<p>{{form_data_basics.name}}</p>
						</FormItem>
						<Row>
							<FormItem class="width-100-perc" label="API标题：" prop="title">
								<p>{{form_data_basics.title}}</p>
							</FormItem>
						</Row>

						<Row>
							<FormItem label="接口类型：" prop="apiType">
								<p>{{form_data_basics.apiType == "COMMON"?"普通":form_data_basics.apiType == "FILE_UPLOAD"?"文件上传":"文件下载"}}</p>
							</FormItem>
						</Row>
						<Row>
							<FormItem class="width-100-perc" label="API分组：" prop="apiGroup">
								<p>{{form_data_basics.apiGroup}}</p>
							</FormItem>
						</Row>
						<FormItem class="width-100-perc" label="状态" prop="IDEMPOTENT">
							<p>{{form_data_basics.status == 'DISABLED' ?"已禁用": "已启用"}}</p>
						</FormItem>
						<Row>
							<FormItem class="width-100-perc" label="描述：" prop="description">
								<p v-html="form_data_basics.description"></p>
							</FormItem>
						</Row>
					</Form>
				</Col>
			</Row>
			<p>请求参数</p>
			<Form ref="form_data_request" :model="form_data_request" :label-width="120" inline>
				<Col span="24">
					<Row>
						<FormItem label="请求路径：" class="width-50-perc" prop="requestUrl">
							<p>{{form_data_request.path}}</p>
						</FormItem>
					</Row>
					<Row>
						<FormItem label="http方法：" class="width-50-perc" prop="httpMethod">
							<p>{{form_data_request.httpMethod}}</p>
						</FormItem>
					</Row>
					<Row v-show="form_data_request.requestBody">
						<FormItem label="入参请求模式：" class="width-100-perc" prop="description">
							<p>{{form_data_request.parameterHandlingType === "MAPPING"?"映射":"透传"}}</p>
						</FormItem>
						<FormItem
							v-show="form_data_request.requestBody?form_data_request.requestBody.contents.schema:false"
							label="content："
							class="width-50-perc"
							prop="description"
						>
							<p>{{form_data_request.requestBody?form_data_request.requestBody.contents.schema:""}}</p>
						</FormItem>
						<FormItem label="描述：" class="width-100-perc" prop="description">
							<p>{{form_data_request.requestBody?form_data_request.requestBody.description:""}}</p>
						</FormItem>
					</Row>
					<Row v-show="form_data_request.parameters">
						<FormItem label="入参请求模式：" class="width-100-perc" prop="description">
							<p>{{form_data_request.parameterHandlingType === "MAPPING"?"映射":"透传"}}</p>
						</FormItem>
					</Row>
				</Col>
			</Form>
			<p>响应参数</p>
			<Form ref="form_data_response" :model="form_data_response" :label-width="120" inline>
				<Col span="24">
					<Row>
						<FormItem label="返回HttpCode：" class="width-50-perc" prop="httpCode">
							<p>{{form_data_response.httpCode}}</p>
						</FormItem>
					</Row>
					<Row>
						<FormItem label="返回contentType：" class="width-100-perc" prop="contentType">
							<p>{{form_data_response.contentType}}</p>
						</FormItem>
					</Row>
				</Col>
			</Form>
			<p>回调</p>
			<Form ref="form_data_callbacks" :label-width="120" inline>
				<Row>
					<Col span="24">
						<FormItem label="回调：" class="width-50-perc" prop="type">
							<p v-for="(item, index) in form_data_callbacks" :key="index">{{item}}</p>
						</FormItem>
					</Col>
				</Row>
			</Form>
		</div>
		<loading :show="showLoading"></loading>
		<div slot="footer" style="border:0;">
			<Button id="api_mange_btn3" type="ghost" @click="cancel">关闭</Button>
		</div>
	</Modal>
</template>

<script>
import Api from '~/api/newApiManage/route';
import loading from '~/views/my-components/loading/loading';
import ApiRouteForm from '../api_route_form_display';
export default {
    components: {
        loading,
        ApiRouteForm
    },
    data () {
        return {
            routeList: [],
            showLoading: false,
            show: false,
            recordId: '',
            value1: '',
            form_data_basics: {},
            form_data_request: {},
            form_data_callbacks: [],
            form_data_response: {},
        };
    },
    computed: {
    },
    methods: {
        getList () {
            this.showLoading = true;
            Api.apiDeployDetail({
                id: this.recordId
            })
                .then(res => {
                    const { status, message, solutions, data } = res.data;
                    if (status === 'success') {
                        const result = data.page;
                        this.form_data_basics = result.basic
                        this.form_data_request = result.request
                        this.form_data_callbacks = result.callbacks
                        this.form_data_response = result.response
						// 改结构后
                        // this.form_data_basics = result.basic
                    } else {
                        this.$ypMsg.notice_error(
                            this,
                            '列表获取错误',
                            message,
                            solutions
                        );
                    }
                })
                .finally(() => {
                    this.showLoading = false;
                });
        },
        showModal (id) {
            this.show = true;
            this.recordId = id;
            this.getList();
        },
        cancel () {
            this.show = false;
        },
        confirm () {

        }
    }
};
</script>

<style lang="less" scoped>
	.routeDetail-wrap {
		background-color: #fff;
		/deep/ .ivu-collapse-header .ivu-icon-arrow-right-b {
			display: none;
		}
		/deep/ .ivu-collapse-item-active .ivu-icon-chevron-up {
			transform: rotate(180deg);
		}
		.tip {
			width: 450px;
		}
		.header-api {
			display: flex;
			align-items: center;
      justify-content: space-between;
			p {
				width: 15%;
				margin: 0 20px;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
		}
	}
</style>