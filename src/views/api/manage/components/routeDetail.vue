<template>
  <Modal v-model="show" width="1000" :closable="false">
    <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
      <Icon type="ios-information-circle"></Icon>
      <span>路由详情</span>
    </p>
    <Row class="margin-top-10 routeDetail-wrap">
      <Col>
        <Collapse v-model="value1">
          <Panel :name="`${item.basic.id}`" v-for="(item, index) in routeList" :key="index">
            <Row>
              <Col span="18">
                <div class="header-api">
                  <Icon type="chevron-up"></Icon>
                  <p>{{item.basic.type}}</p>
                  <p class="apigroup">{{item.basic.serviceName}}</p>
                  <p>{{item.status === 'ENABLED' ? '已启用' : '已禁用'}}</p>
                </div>
              </Col>
            </Row>
            <p slot="content">
              <ApiRouteForm :id="item.basic.id" />
            </p>
          </Panel>
        </Collapse>
        <loading :show="showLoading"></loading>
      </Col>
    </Row>
    <div slot="footer" style="border:0;">
      <Button id="api_mange_btn3" type="ghost" @click="cancel">关闭</Button>
    </div>
  </Modal>
</template>

<script>
import Api from '~/api/newApiManage/route';
import loading from '~/views/my-components/loading/loading';
import ApiRouteForm from '../api_route_form_display';
export default {
  components: {
    loading,
    ApiRouteForm
  },
  data () {
    return {
      routeList: [],
      showLoading: false,
      show: false,
      recordId: '',
      value1: ''
    };
  },
  computed: {
  },
  methods: {
    getList () {
      this.showLoading = true;
      Api.routeDeployDetail({
        recordId: this.recordId
      })
        .then(res => {
          const { status, message, solutions, data } = res.data;
          if (status === 'success') {
            this.routeList = data.result;
          } else {
            this.$ypMsg.notice_error(
              this,
              '列表获取错误',
              message,
              solutions
            );
          }
        })
        .finally(() => {
          this.showLoading = false;
        });
    },
    showModal (id) {
      this.show = true;
      this.recordId = id;
      this.getList();
    },
    cancel () {
      this.show = false;
    },
    confirm () {

    }
  }
};
</script>

<style lang="less" scoped>
.routeDetail-wrap {
  background-color: #fff;
  /deep/ .ivu-collapse-header .ivu-icon-arrow-right-b {
    display: none;
  }
  /deep/ .ivu-collapse-item-active .ivu-icon-chevron-up {
    transform: rotate(180deg);
  }
  .tip {
    width: 450px;
  }
  .header-api {
    display: flex;
    align-items: center;
    justify-content: space-between;
    p {
      width: 15%;
      margin: 0 20px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
</style>