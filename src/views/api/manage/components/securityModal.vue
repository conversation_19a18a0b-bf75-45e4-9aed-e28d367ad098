<template>
    <Modal id="modal_ag_1" v-model="modal_api_security" width="1300" :closable="false" :mask-closable="false">
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">设置API安全需求</span>
        </p>
        <Col style="text-align: center">
        <ButtonGroup>
            <Button id="btn_security_3" :type="default_type" @click="default_Setting">继承自API分组</Button>
            <Button id="btn_security_4" :type="user_defined_type" @click="user_defined_Setting">自定义安全需求</Button>
        </ButtonGroup>
        <div class="margin-top-20" v-show="default_orNot">
            <Row class="margin-top-10">
                <Col class="yop-border-grey" span="6">安全定义</Col>
                <Col class="yop-border-grey" span="11">定制需求</Col>
                <Col class="yop-border-grey" span="7">补充说明</Col>
                <Row v-for="(items, index) in formCustom.apiGroupSecurity" :key="index" style="text-align: start; margin-bottom: 20px;">
                    <Col span="6">
                    <Col>
                    <Checkbox disabled :id="'modal_sg_chk_'+items.name" v-model="items.check">{{items.name}}</Checkbox>
                    </Col>
                    </Col>
                    <Col span="11">
                    <Col v-show="items.name === 'YOP-RSA2048-SHA256'" span="6">接口加密:</Col>
                    <Col v-show="items.name === 'YOP-RSA2048-SHA256'" span="18">
                    <Col>
                    <Checkbox disabled v-model="items.extensions.needEncrypt.defaultValue">
                        {{items.extensions.needEncrypt.title}}</Checkbox>
                    </Col>
                    <Col v-show="items.extensions.needEncrypt.defaultValue">
                    {{items.extensions.forceEncryptAppDate.title}}: <DatePicker disabled clearable ref="start"
                        class="margin-top-5" v-model="items.extensions.forceEncryptAppDate.defaultValue"
                        format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择强制加密起始时间"></DatePicker>
                    </Col>
                    <Col v-show="items.extensions.needEncrypt.defaultValue" style="color:red">
                    {{items.extensions.forceEncryptAppDate.remark}}</Col>
                    </Col>
                    <Col v-show="items.name === 'YOP-RSA2048-SHA256'" span="6">{{items.extensions.authority.title}}:
                    </Col>
                    <Col v-show="items.name === 'YOP-RSA2048-SHA256'" span="18">
                    <Select disabled style="width:150px;" ref="auth_select" size="small"
                        v-model="items.extensions.authority.defaultValue" filterable multiple placeholder="不限"
                        clearable>
                        <Option v-for="item in items.extensions.authority.optionValues" :value="item.name"
                            :key="item.name">{{ item.title }}</Option>
                    </Select>
                    </Col>
                    <Col v-show="items.name === 'YOP-OAUTH2'" span="6">访问权限:</Col>
                    <Col v-show="items.name === 'YOP-OAUTH2'" span="18">
                    <Col v-for="(subitems, subIndex) in items.scopes" :key="subIndex">
                    <Checkbox disabled :id="'modal_sg_chk_sub'+subitems.name" v-model="subitems.check">{{subitems.name}}
                    </Checkbox>
                    </Col>
                    </Col>
                    </Col>
                    <Col span="7" :offset="items.name === 'YOP-RSA2048-SHA256'|| items.name === 'YOP-OAUTH2'?0:11">
                    {{items.desc}}
                    </Col>
                </Row>
            </Row>
        </div>
        <div class="margin-top-20" v-show="!default_orNot">
            <Row class="margin-top-10">
                <Col class="yop-border-grey" span="6">安全定义</Col>
                <Col class="yop-border-grey" span="11">定制需求</Col>
                <Col class="yop-border-grey" span="7">补充说明</Col>
                <Row v-for="(items, index) in formCustom.apiSecurity" :key="index" style="text-align: start; margin-bottom: 20px;">
                    <Col span="6">
                    <Col>
                    <Checkbox :id="'modal_sg_chk_'+items.name" v-model="items.check">{{items.name}}</Checkbox>
                    </Col>
                    </Col>
                    <Col span="11">
                    <Col v-show="items.name === 'YOP-RSA2048-SHA256'" span="6">接口加密:</Col>
                    <Col v-show="items.name === 'YOP-RSA2048-SHA256'" span="18">
                    <Col>
                    <Checkbox v-model="items.extensions.needEncrypt.defaultValue">{{items.extensions.needEncrypt.title}}
                    </Checkbox>
                    </Col>
                    <Col v-show="items.extensions.needEncrypt.defaultValue">
                    {{items.extensions.forceEncryptAppDate.title}}: <DatePicker clearable ref="start"
                        class="margin-top-5" v-model="items.extensions.forceEncryptAppDate.defaultValue"
                        format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择强制加密起始时间"></DatePicker>
                    </Col>
                    <Col v-show="items.extensions.needEncrypt.defaultValue" style="color:red">
                    {{items.extensions.forceEncryptAppDate.remark}}</Col>
                    </Col>
                    <Col v-show="items.name === 'YOP-RSA2048-SHA256'" span="6">{{items.extensions.authority.title}}:
                    </Col>
                    <Col v-show="items.name === 'YOP-RSA2048-SHA256'" span="18">
                    <Select style="width:150px;" ref="auth_select" size="small"
                        v-model="items.extensions.authority.defaultValue" filterable multiple placeholder="不限"
                        clearable>
                        <Option v-for="item in items.extensions.authority.optionValues" :value="item.name"
                            :key="item.name">{{ item.title }}</Option>
                    </Select>
                    </Col>
                    <Col v-show="items.name === 'YOP-OAUTH2'" span="6">访问权限:</Col>
                    <Col v-show="items.name === 'YOP-OAUTH2'" span="18">
                    <Col v-for="(subitems, indexs) in items.scopes" :key="indexs">
                    <Checkbox :id="'modal_sg_chk_sub'+subitems.name" v-model="subitems.check">{{subitems.name}}
                    </Checkbox>
                    </Col>
                    </Col>
                    </Col>
                    <Col span="7" :offset="items.name === 'YOP-RSA2048-SHA256'|| items.name === 'YOP-OAUTH2'?0:11">
                    {{items.desc}}
                    </Col>
                </Row>
            </Row>
        </div>
        </div>
        </Col>
        <div>
        </div>
        <div slot="footer">
            <Button id="modal_sg_btn_2" type="primary" @click="submit_api_security()">确定</Button>
            <Button id="modal_sg_btn_1" type="ghost" @click="cancel_api_security()">取消</Button>
        </div>
        <loading :show="show_loading_api_security"></loading>
    </Modal>
</template>

<script>
    import loading from '../../../my-components/loading/loading';
    import commonSelect from '../../../common-components/select-components/selectCommon';
    import apiManage from '~/api/newApiManage/apiManage';
    import api from '../../../../api/api';
    import util from '../../../../libs/util';
    import datePicker from '../../../common-components/date-components/date-picker-set';
    export default {
        name: 'securityModal',
        components: {
            commonSelect,
            loading,
            datePicker
        },
        data () {
            return {
                // 页面loading
                show_loading_api_security: false,
                // 页面显示绑定
                modal_api_security: false,
                // 安全需求数据
                formCustom: {
                    apiId: '',
                    apiSecurity: [],
                    apiGroupSecurity: [],
                    securityReqVersion: '0',
                    apiGroupCode: ''
                },
                // 显示默认还是自定义配置
                default_orNot: true,
                // 默认选项风格数据绑定
                default_type: 'primary',
                // 用户自定义风格数据绑定
                user_defined_type: 'ghost'
            };
        },
        methods: {
            // 默认文件存储配置设置
            default_Setting () {
                this.default_type = 'primary';
                this.user_defined_type = 'ghost';
                this.default_orNot = true;
            },
            // 自定义文件存储设置
            user_defined_Setting () {
                this.default_type = 'ghost';
                this.user_defined_type = 'primary';
                this.default_orNot = false;
            },
            // api安全需求提交
            submit_api_security () {
                let param = {
                    apiId: this.formCustom.apiId,
                    apiGroupCode: this.formCustom.apiGroupCode,
                    securityReqVersion: this.formCustom.securityReqVersion,
                    securities: this.security_return()
                };
                apiManage.updateApiSecurityReq(param).then(
                    (response) => {
                        if (response.status === 'success') {
                            this.$ypMsg.notice_success(this, 'API安全需求设置成功');
                            this.modal_api_security = false;
                        } else {
                            this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
                        }
                    }
                );
            },
            // api安全需求取消
            cancel_api_security () {
                this.modal_api_security = false;
            },
            // 安全需求反向处理
            security_return () {
                let security_final = [];
                if (this.formCustom.apiSecurity) {
                    this.formCustom.apiSecurity.forEach(
                        (item) => {
                            let scopesTemp = [];
                            if (item.check) {
                                if (item.scopes) {
                                    item.scopes.forEach(
                                        (subitem) => {
                                            if (subitem.check) {
                                                scopesTemp.push(subitem.name);
                                            }
                                        }
                                    );
                                }
                                if (scopesTemp.length > 0) {
                                    security_final.push({
                                        name: item.name,
                                        scopes: scopesTemp
                                    });
                                } else if (item.extensions && item.name === 'YOP-RSA2048-SHA256') {
                                    security_final.push({
                                        name: item.name,
                                        extensions: {
                                            needEncrypt: item.extensions.needEncrypt.defaultValue,
                                            authority: item.extensions.authority.defaultValue,
                                            forceEncryptAppDate: this.date_handler(item.extensions.forceEncryptAppDate.defaultValue)
                                        }
                                    });
                                } else {
                                    security_final.push({
                                        name: item.name
                                    });
                                }
                            }
                        }
                    );
                }
                return security_final;
            },
            date_handler (val) {
                if (val) {
                    return util.dateFormat(val, 'DAILY');
                } else {
                    return '';
                }
            },
            // 界面显示
            modal_show (apiId, apiGroupCode) {
                this.modal_api_security = true;
                this.show_loading_api_security = true;
                this.formCustom.apiId = apiId;
                this.formCustom.apiGroupCode = apiGroupCode;
                api.yop_apiGroup_security().then(
                    (response) => {
                        this.formCustom.apiSecurity = [];
                        this.formCustom.apiGroupSecurity = [];
                        this.formCustom.apiSecurity = this.basic_security_handler(response.data.data.result);
                        this.formCustom.apiGroupSecurity = this.basic_security_handler(response.data.data.result);
                        this.api_security_detail();
                    }
                );
            },
            api_security_detail () {
                let params = {
                    apiId: this.formCustom.apiId,
                    apiGroupCode: this.formCustom.apiGroupCode
                };
                apiManage.queryApiSecurityReq(params).then(
                    (response) => {
                        let status = response.data.status;
                        if (status === 'success') {
                            let data = response.data.data.result;
                            if (data.apiSecurity) {
                                this.formCustom.securityReqVersion = data.apiSecurity.securityReqVersion;
                            }
                            if (data.apiGroupSecurity) {
                                this.detail_security_handler(this.formCustom.apiGroupSecurity, data.apiGroupSecurity.securities);
                            }
                            if (data.apiSecurity) {
                                this.detail_security_handler(this.formCustom.apiSecurity, data.apiSecurity.securities);
                            }
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.modal_api_security = false;
                        }
                        this.show_loading_api_security = false;
                    }
                );
            },
            // 安全需求返回数据处理函数
            detail_security_handler (original, security) {
                for (var i in original) {
                    for (var j in security) {
                        if (original[i].name === security[j].name) {
                            original[i].check = true;
                            if (security[j].scopes && security[j].scopes.length > 0) {
                                this.special_security_handler(original[i].scopes, security[j].scopes);
                            }
                            if (security[j].extensions) {
                                this.special_security_extensions_handler(original[i].extensions, security[j].extensions);
                            }
                        }
                    }
                }
            },
            // 安全需求特殊处理
            special_security_handler (all, custom) {
                for (var i in all) {
                    for (var j in custom) {
                        if (all[i].name === custom[j]) {
                            all[i].check = true;
                        }
                    }
                }
            },
            // extensions特殊处理
            special_security_extensions_handler (extensions, result) {
                extensions.needEncrypt.defaultValue = result.needEncrypt;
                extensions.authority.defaultValue = result.authority;
                extensions.forceEncryptAppDate.defaultValue = result.forceEncryptAppDate;
            },
            // 安全需求清空
            security_clear (data) {
                if (data && data.length > 0) {
                    for (var i in data) {
                        data[i].check = false;
                        if (data[i].scopes && data[i].scopes.length > 0) {
                            for (var j in data[i].scopes) {
                                data[i].scopes[j].check = false;
                            }
                        }
                        if (data[i].extensions) {
                            data[i].extensions.authority.defaultValue = [];
                            data[i].extensions.needEncrypt.defaultValue = false;
                            data[i].extensions.forceEncryptAppDate.defaultValue = '';
                        }
                    }
                }
            },
            // 安全需求的获取处理
            basic_security_handler (security) {
                let securityTemp = [];
                for (var i in security) {
                    securityTemp.push({
                        name: security[i].name,
                        extensions: this.extensions_handler(security[i].extensions),
                        scopes: this.security_subitem_handler(security[i].scopes),
                        desc: security[i].desc,
                        check: false
                    });
                }
                return securityTemp;
                this.cache_handler(securityTemp);
            },
            cache_handler (val) {
                this.$store.state.security_cache = val;
            },
            // 扩展项处理
            extensions_handler (extentions) {
                let extentions_temp = {
                    authority: {
                        title: '',
                        optionValues: [],
                        defaultValue: []
                    },
                    needEncrypt: {
                        title: '',
                        defaultValue: false
                    },
                    forceEncryptAppDate: {
                        title: '',
                        defaultValue: '',
                        remark: ''
                    }
                };
                if (extentions) {
                    if (extentions.authority) {
                        extentions_temp.authority.title = extentions.authority.title;
                        extentions_temp.authority.optionValues = extentions.authority.optionValues;
                        extentions_temp.authority.defaultValue = extentions.authority.defaultValue;
                    }
                    if (extentions.needEncrypt) {
                        extentions_temp.needEncrypt.title = extentions.needEncrypt.title;
                        extentions_temp.needEncrypt.defaultValue = extentions.needEncrypt.defaultValue;
                    }
                    if (extentions.forceEncryptAppDate) {
                        extentions_temp.forceEncryptAppDate.title = extentions.forceEncryptAppDate.title;
                        extentions_temp.forceEncryptAppDate.remark = extentions.forceEncryptAppDate.remark;
                    }
                }
                return extentions_temp;
            },
            // 安全需求子项处理函数
            security_subitem_handler (items) {
                let data_custom = [];
                if (items && items.length > 0) {
                    for (var i in items) {
                        data_custom.push({
                            name: items[i],
                            check: false
                        });
                    }
                }
                return data_custom;
            },
            // 表格数据初始化
            reset_data () {
                this.formCustom.securityReqVersion = '0';
                this.security_clear(this.formCustom.apiSecurity);
                this.security_clear(this.formCustom.apiGroupSecurity);
            }

        }
    };
</script>

<style scoped>

</style>