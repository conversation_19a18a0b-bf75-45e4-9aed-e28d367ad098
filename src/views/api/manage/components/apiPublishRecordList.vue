<template>
  <div>
    <!-- 发布记录 -->
    <Modal v-model="show" :transfer="false" width="1200">
      <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
        <Icon type="ios-information-circle"></Icon>
        <span>发布记录</span>
      </p>
      <Card :bordered="false">
        <Row type="flex" align="middle">
          <Col span="24">
            <Col span="6">
              <Col span="6" class="margin-top-10">
                <span>API分组:</span>
              </Col>
              <Col span="17">
                <Select
                  id="selectRecordAPIGroup"
                  ref="clearRecordApiGroup"
                  class="margin-top-5"
                  v-model="params.apiGroup"
                  filterable
                  clearable
                  placeholder="请选择（默认全部）"
                >
                  <Option
                    v-for="item in options"
                    :value="item.value"
                    :key="item.value"
                  >{{ item.label }}</Option>
                </Select>
              </Col>
            </Col>
            <Col span="6">
              <Col span="6" class="margin-top-10">
                <span>请求路径:</span>
              </Col>
              <Col span="17">
                <Input
                  id="inputRecordAPIURI"
                  class="margin-top-5"
                  clearable
                  v-model="params.path"
                  placeholder="请求路径"
                ></Input>
              </Col>
            </Col>

            <Col span="6" offset>
              <Col span="6" class="margin-top-10">
                <span>请求方法:</span>
              </Col>
              <Col span="17">
                <Select
                  class="margin-top-5"
                  ref="clearRecordMethod"
                  v-model="params.method"
                  filterable
                  clearable
                  placeholder="请选择"
                >
                  <Option value="GET">GET</Option>
                  <Option value="POST">POST</Option>
                </Select>
              </Col>
            </Col>
          </Col>
        </Row>
        <Row>
          <Col span="24">
            <!--  -->
            <Col class="margin-top-5" span="6">
              <Col span="6" class="margin-top-10">
                <span>操作类型:</span>
              </Col>
              <Col span="17">
                <Select
                  id="selectAPIType"
                  class="margin-top-5"
                  ref="clearRecordType"
                  v-model="params.opType"
                  placeholder="请选择（默认全部）"
                  clearable
                >
                  <Option
                    v-for="item in apiOpTypes"
                    :value="item.value"
                    :key="item.value"
                  >{{ item.label }}</Option>
                </Select>
              </Col>
            </Col>
            <!-- 时间 -->
            <Col class="margin-top-5" span="10">
              <Col span="4" class="margin-top-10">
                <span>操作时间:</span>
              </Col>
              <Col span="18" class="margin-top-5">
                <DatePicker
                  type="datetime"
                  ref="start"
                  :value="params.createdStartDate"
                  @on-change="update_start_date"
                  format="yyyy-MM-dd"
                  placeholder="选择开始时间"
                  style="width: 166px"
                ></DatePicker>至
                <DatePicker
                  type="datetime"
                  ref="end"
                  :value="params.createdStartDate"
                  @on-change="update_end_date"
                  format="yyyy-MM-dd"
                  placeholder="选择结束时间"
                  style="width: 166px"
                ></DatePicker>
              </Col>
            </Col>

            <Col span="2">
              <Col class="margin-top-10" span="11" style="text-align:center">
                <Button
                  id="btnSearchList"
                  v-url="{url:'/rest/api/manage/deploy-record/list'}"
                  type="primary"
                  @click="getList"
                >查询</Button>
              </Col>
            </Col>

            <Col span="2">
              <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id="btnReset" type="ghost" @click="reset">重置</Button>
              </Col>
            </Col>
          </Col>
        </Row>

        <Row></Row>
        <Row class="margin-top-10">
          <Col span="24">
            <Table id="table_1" border ref="selection" :columns="columns" :data="list"></Table>
            <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
            <Page
              class="margin-top-10"
              style="float: right"
              :total="recordPageTotal"
              :page-size="10"
              :current="recordPageNo"
              show-elevator
              @on-change="pageRecordRefresh"
            ></Page>
            </Tooltip>-->
          </Col>
          <loading :show="showLoading"></loading>
        </Row>
      </Card>
      <div slot="footer" style="border:0;"></div>
    </Modal>
    <Modal v-model="checkoutApiFlag">
      <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
        <Icon type="ios-information-circle"></Icon>
        <span>API回滚</span>
      </p>
      <div style="text-align:left;font-size: 12px;">
        <div
          style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;"
        >
          <i class="ivu-icon ivu-icon-help-circled"></i>
        </div>
        <div style="display:inline-block;margin:0 0 0 56px;width:75%">
          <p style="color:red"></p>
          <p>
            您将回滚API:
            <Button id="api_mange_btn16" size="small" type="primary">{{this.currentApi.method}}</Button>
            {{this.currentApi.path}}至当前版本，回滚后API文档将发生变更，您确定要回滚吗？
          </p>
          <label for class="reasonType">
            <span style="color:red;font-size: 12x;">*</span>原因：
          </label>
          <Input
            type="textarea"
            v-model="cause"
            style="width:85%;font-size: 12x;margin-top:8px;"
            placeholder
            placeholder-style="font-size:12px;"
          ></Input>
        </div>
      </div>
      <div slot="footer" style="border:0;">
        <Button id="api_mange_btn18" type="primary" @click="rollbackRoute">回滚</Button>
        <Button id="api_mange_btn17" type="ghost" @click="checkoutApiFlag = false">取消</Button>
      </div>
    </Modal>
    <ApiDetail ref="apiDetail" />
  </div>
</template>

<script>
import apiManage from '~/api/newApiManage/apiManage';
import Api from '~/api/api';
import loading from '~/views/my-components/loading/loading';
import ApiDetail from './apiDetail'
export default {
  props: ['options'],
  components: {
    loading,
    ApiDetail
  },
  data () {
    return {
      currentApi: {},
      apiOpTypes: [
        {
          value: 'ROLLBACK',
          label: '回滚'
        },
        {
          value: 'PUBLISH',
          label: '发布'
        }
      ],
      columns: [
        {
          title: 'API分组',
          key: 'apiGroup',
          type: 'html',
          width: 90,
          align: 'center',
          render (h, params) {
            return h('div', [
              h('p', params.row.apiGroup),
              h('p', params.row.apiGroupName)
            ]);
          }
        },
        {
          title: '方法/请求路径',
          align: 'center',
          render: (h, params) => {
            var color = 'blue';
            return h('div', [
              h('Tag', {
                style: {
                  align: 'center'
                },
                props: {
                  color: color
                }
              }, params.row.method),
              h('p', params.row.path)]);
          }
        },
        {
          title: '操作类型',
          key: 'opType',
          align: 'center',
          render: (h, params) => {
            const item = this.apiOpTypes.find(item => item.value === params.row.opType);
            const text = item ? item.label : ''
            return h('span', text);
          }
        },
        {
          title: '操作原因',
          align: 'center',
          key: 'cause'
        },
        {
          title: '操作人',
          align: 'center',
          key: 'operator'
        },
        {
          title: '操作时间',
          align: 'center',
          key: 'createdDate'
        },
        {
          title: '操作',
          align: 'center',
          key: 'address',
          render: (h, params) => {
            return h('div',
              [
                h('Button',
                  {
                    props: {
                      type: 'primary'
                    },
                    style: {
                      marginRight: '10px'
                    },
                    on: {
                      click: () => {
                        this.$refs.apiDetail.showModal(params.row.id);
                      }
                    }
                  },
                  '查看'),
                h('Button', {
                  on: {
                    click: () => {
                      this.currentApi = params.row;
                      this.cause = '';
                      this.checkoutApiFlag = true;
                    }
                  }
                }, '回滚')
              ]);
          }
        }
      ],
      list: [],
      show: false,
      cause: '',
      checkoutApiFlag: false,
      showLoading: false,
      params: {
        apiGroup: null,
        path: null,
        method: null,
        createdStartDate: null,
        createdEndDate: null,
        opType: null
      }
    };
  },
  methods: {
    // 开始日期更新
    update_start_date (val) {
      this.params.createdStartDate = val;
    },
    // 结束日期更新
    update_end_date (val) {
      this.params.createdEndDate = val;
    },
    reset () {
      this.params = {
        apiGroup: null,
        path: null,
        method: null,
        createdStartDate: null,
        createdEndDate: null,
        opType: null
      };
      this.getList();
    },
    showModal (data) {
      this.show = true;
      this.params.method = data.httpMethod;
      this.params.path = data.interface_URI;
      this.getList();
    },
    getList () {
      this.showLoading = true;
      Api.yop_apiManagement_deploy_record_list(this.params)
        .then(res => {
          const { status, message, solutions, data } = res.data;
          if (status === 'success') {
            this.list = data.page.items;
          } else {
            this.$ypMsg.notice_error(
              this,
              '列表获取错误',
              message,
              solutions
            );
          }
        })
        .finally(() => {
          this.showLoading = false;
        });
    },
    rollbackRoute () {
      if (!this.cause) {
        this.$Message.error('请输入原因');
        return false;
      }
      this.showLoading = true;
      apiManage.rollback({
        cause: this.cause,
        id: this.currentApi.id
      })
        .then(res => {
          const { status, message, solutions } = res;
          if (status === 'success') {
            this.getList();
          } else {
            this.$ypMsg.notice_error(
              this,
              '回滚错误',
              message,
              solutions
            );
          }
        })
        .finally(() => {
          this.checkoutApiFlag = false;
          this.showLoading = false;
        });
    },
    cancel () {
      this.show = false;
    },
    confirm () {

    }
  }
};
</script>

<style>
</style>