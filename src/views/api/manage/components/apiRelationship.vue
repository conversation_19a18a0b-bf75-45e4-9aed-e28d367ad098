<style lang="less" scoped>
    .relation-api-box {
        .tips-con {
            display: flex;
            justify-content: space-between;
        }
        .add-eidt-box {
            text-align: center;
            .ivu-form-inline .ivu-form-item {
                width: 90%;
                display: block;
            }
            .text-left {
                text-align: left;
            }
            .tip-text {
                > span {
                    color: #1aba00;
                    cursor: pointer;
                }
            }
        }
        .detail-box {
            width: 100%;
            > ul {
                margin: 0 auto;
                li {
                    width: 100%;
                    height: 40px;
                    line-height: 40px;
                    list-style: none;
                    display: flex;
                    justify-content: flex-start;
                    > h4 {
                        margin-right: 10px;
                        text-indent: 40px;
                    }
                }
            }
        }
    }
</style>
<template>
  <div class="relation-api-box">
      <Modal v-model="show" :transfer="false" width="900">
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">API关系</span>
        </p>
        <h4 class="tips-con">
            <div>
                <Alert class="margin-bottom-10 tip">
                    <Icon type="ios-information" size="14" color="#2d8cf0" class="margin-right-10"></Icon>当前API信息: {{currentApiInfo.interface_Title}} ({{currentApiInfo.name}})
                </Alert>
            </div>
            <div>
                <Button type="primary" @click="add" id="relation_btn_1">新增</Button>
            </div>
        </h4>
        <Row class="margin-top-10">
          <Col span="24">
            <Table id="table_1" border ref="selection" :columns="columns" :data="list"></Table>
          </Col>
          <loading :show="showLoading"></loading>
        </Row>
        <div class="text-right" slot="footer">
        </div>
      </Modal>
      <Modal v-model="reModelFlag" :transfer="false" width="600">
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">{{editAddDetailFlag === 'add' ? '新增' : (editAddDetailFlag === 'edit' ? '编辑' : '详情')}}</span>
        </p>
        <div v-if="editDetailFlag === 'addEdit'" class="add-eidt-box">
            <Form ref="formApiRelation" :model="formApiRelation" :rules="ruleValidate" :key="formKey" :label-width="120" inline>
                <Form-item label="关系类型:" prop="relationType">
                    <Select id="selectRelation1" class="text-left" disabled v-model="formApiRelation.relationType" placeholder="请选择关系类型">
                        <Option value="ORDER_QUERY">查单关系</Option>
                    </Select>
                </Form-item>
                <Form-item label="API分组:" prop="apiGroup">
                    <Select
                        id="selectRecordAPIGroup"
                        ref="clearRecordApiGroup"
                        class="margin-top-5 text-left"
                        v-model="formApiRelation.apiGroup"
                        filterable
                        clearable
                        :disabled="editAddDetailFlag === 'edit'"
                        placeholder="请选择API分组"
                        @on-change="getRelation"
                        >
                        <Option
                            v-for="(item,index) in options"
                            :value="item.value"
                            :key="index"
                        >{{ item.label }}</Option>
                    </Select>
                </Form-item>
                <Form-item id="selectRelation3" label="关联API:" prop="relateApi">
                    <Input v-if="editAddDetailFlag === 'edit'" disabled v-model="formApiRelation.apiTitle"></Input>
                    <Select v-else class="margin-top-5 text-left" 
                    v-model="formApiRelation.relateApi" 
                    @on-change="chooseApi"
                    filterable
                    placeholder="请选择关联API">
                        <Option
                            v-for="(item, k) in relationApis"
                            :value="item.apiUri"
                            :disabled="item.apiTitle === currentApiInfo.interface_Title "
                            :key="k"
                        >{{ item.apiTitle }}</Option>
                    </Select>
                </Form-item>
                <Form-item label="结果通知:" prop="resultNotification">
                    <Select 
                    multiple 
                    filterable
                    id="selectRelation4" class="margin-top-5 text-left" v-model="formApiRelation.resultNotification" placeholder="请选择结果通知">
                        <Option
                            v-for="item in relationNoticeArr"
                            :value="item.code"
                            :key="item.code"
                        >{{ item.name }}</Option>
                    </Select>
                </Form-item>
                <div class="tip-text">如果没有找到您需要的结果通知，请先在 <span @click="goPage">结果通知管理</span> 处关联API</div>
                <!-- <div class="tip-text">如果没有找到您需要的结果通知，请先在 <a target="blank" href="/#/spi/list">结果通知管理</a> 处关联API</div> -->
            </Form>
        </div>
        <div v-else class="detail-box">
            <ul>
                <li><h4>关系类型: </h4> 查单接口关联关系</li>
                <!-- <li><h4>API分组: </h4> {{this.formApiRelation.relateApiConfig}}</li> -->
                <li><h4>关联API: </h4> {{this.formApiRelation.apiTitle}}</li>
                <li>
                  <h4>结果通知: </h4>
                  <span v-for="(item, index) in showResultArr" :key="index">
                    {{item.name}}
                  </span>
                 </li>
            </ul>
        </div>
        <div class="text-right" slot="footer">
            <Button id="btn_resList_19" type="ghost"  v-if="editDetailFlag === 'addEdit'" @click="cancel()">取消</Button>
            <Button id="btn_resList_18" type="primary" v-if="editDetailFlag === 'addEdit'"  @click="confirmSave">保存</Button>
            <Button id="btn_resList_18" type="primary"  v-else  @click="cancel()">确定</Button>
        </div>
      </Modal>
  </div>
</template>
<script>
import Api from '~/api/api';
import loading from '~/views/my-components/loading/loading';

export default {
    props: ['options'],
    components: {
        loading
    },
    data() {
        return {
            // 渲染form
            formKey: 1,
            // 最外层api相关信息
            currentApiInfo: {
              interface_Title: '',
              APIid: ''
            },
            // api关系列表 api信息
            relationApiInfo: null,
            show: false,
            showLoading: false,
            // 详情 新增编辑开关
            editDetailFlag: 'addEdit', // addEdit detail
            // 详情展示 结果通知数组
            showResultArr: [],
            // 关联api下拉数据
            relationApis: [],
            // 关系类型下拉
            // relationTypes: [],
            // 结果通知下拉数据
            relationNoticeArr: [],
            // 编辑新增详情开关
            editAddDetailFlag: 'add',  // edit detail
            reModelFlag: false, //新增编辑 详情弹框开关
            // 列表数据
            list: [
                // {
                //     name: '京东到家',
                //     type: '关联关系',
                //     id: '666'
                // }
            ],
            // 编辑新增表单数据
            formApiRelation: {
                relationType: 'ORDER_QUERY', //关系类型
                apiGroup: '', // API分组
                relateApi: '', // 关联API
                relateApiConfig: null, // 关联API信息
                resultNotification: [], //结果通知
                apiTitle: ''
            },
            // 表头
            columns : [
                {
                    title: '已关联API',
                    key: 'apiTitle',
                    align: 'center',
                    render: (h, params) => {
                        return h('div', params.row.relateApi.apiTitle)
                    },
                },
                {
                    title: '关系类型',
                    align: 'center',
                    key: 'relateType',
                    width: 300
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 200,
                    render: (h, params) => {
                        return h('div', [
                            h('Button', {
                                props: {
                                    type: 'text',
                                    size: 'small'
                                },
                                on: {
                                    click: () => {
                                        this.relationApiInfo = params.row
                                        this.reModelFlag = true
                                        this.editAddDetailFlag = 'detail'
                                        this.editDetailFlag = 'detail'
                                        this.getDetail(params.row)
                                    }
                                }
                            }, '详情'),
                            h('Button', {
                                props: {
                                    type: 'text',
                                    size: 'small'
                                },
                                on: {
                                    click: () => {
                                        this.relationApiInfo = params.row
                                        this.reModelFlag = true
                                        this.editAddDetailFlag = 'edit'
                                        this.editDetailFlag = 'addEdit'
                                        // 将api分组 取消必填
                                        this.ruleValidate.apiGroup[0].required = false
                                        this.getDetail(params.row)
                                    }
                                }
                            }, '编辑'),
                            h('Button', {
                                props: {
                                    type: 'text',
                                    size: 'small'
                                },
                                on: {
                                    click: () => {
                                        this.$Modal.confirm({
                                            title: '确认移除',
                                            content: '<p>确定要删除吗？</p>',
                                            onOk: () => {
                                                this.delete(params.row);
                                            },
                                            onCancel: () => {
                                            }
                                        });
                                        
                                    }
                                }
                            }, '删除')
                        ]);
                    }
                }
            ],
            // 编辑新增表单校验规则
            ruleValidate: {
                name: [
                    { required: true, message: '关系类型不能为空', trigger: 'change' }
                ],
                relationType: [
                    { required: true, message: '关系类型不能为空', trigger: 'blur' }
                ],
                apiGroup: [
                    { required: true, message: 'API分组不能为空', trigger: 'change' }
                ],
                relateApi: [
                    { required: true, message: '关联API不能为空', trigger: 'change' }
                ],
                resultNotification: [
                    { type: 'array', required: true, message: '结果通知不能为空', trigger: 'change' }
                ],
            }
        }
    },
    mounted() {
        
    },
    methods: {
        // 打开api关系弹框
        showModal (row) {
            this.currentApiInfo = row
            this.show = true;
            // 获取已关联api列表
            this.getList()
            this.getNoticeList()
        },
        // 新增
        add() {
            this.editAddDetailFlag = 'add'
            this.editDetailFlag = 'addEdit'
            this.cancel('add')
            this.reModelFlag = true
            this.ruleValidate.apiGroup[0].required = true
            this.$refs['formApiRelation'].resetFields();
        },
        // 跳转结果通知管理
        goPage() {
          const {href} = this.$router.resolve({
            name: "/spi/list",
          });
          window.open(href, '_blank')
        },
        // 获取关联api列表
        getRelation() {
            this.formApiRelation.relateApi = ''
            if(!this.formApiRelation.apiGroup) {
                return
            }
            let params = {
                apiGroup: this.formApiRelation.apiGroup,
                apiId: this.currentApiInfo.APIid
            };
            Api.yop_api_relation_apis(params)
            .then(res => {
                const { status, message, data } = res.data;
                if (status === 'success') {
                    this.relationApis = data.result;
                } else {
                    this.$Message.error(message);
                }
            })
            .finally(() => {
                this.showLoading = false;
            });
        },
        // 选中关联api
        chooseApi() {
            if(this.formApiRelation.relateApi){
                this.relationApis.map((item) => {
                    if(this.formApiRelation.relateApi === item.apiUri) {
                        this.formApiRelation.relateApiConfig = item
                    }
                })
            } else {
                this.formApiRelation.relateApiConfig = null
            }
        },
        // 获取关联api列表
        getList () {
            this.showLoading = true;
            let params = {
                apiId: this.currentApiInfo.APIid
            }
            Api.yop_api_relation_list(params)
            .then(res => {
                const { status, message, data } = res.data;
                if (status === 'success') {
                    this.list = data.result;
                } else {
                    this.$Message.error(message);
                }
            })
            .finally(() => {
                this.showLoading = false;
            });
        },
        // 获取详情
        getDetail(row) {
            let params = {
                id: row.id
            };
            Api.yop_api_relation_detail(params)
            .then(res => {
                const { status, message, data } = res.data;
                if (status === 'success') {
                    this.formApiRelation.relationType = data.result.relateType
                    // 回填信息
                    this.formApiRelation.relateApi = data.result.relateApi.apiUri
                    this.formApiRelation.apiTitle = data.result.relateApi.apiTitle
                    this.formApiRelation.relateApiConfig = data.result.relateApi
                    this.formApiRelation.version = data.result.version
                    this.formApiRelation.resultNotification = data.result.relateConfig.spis
                    // 结果通知的数据处理 展示返回的
                    this.showResultArr = []
                    this.relationNoticeArr.forEach((item)=> {
                      data.result.relateConfig.spis.forEach((el) => {
                        if(item.code === el) {
                          this.showResultArr.push(item)
                        }
                      })
                    })
                } else {
                    this.$Message.error(message);
                }
            })
        },
        // 获取结果通知
        getNoticeList() {
            let params = {
                apiId: this.currentApiInfo.APIid
            };
            Api.yop_api_relation_spis(params)
            .then(res => {
                const { status, message, data } = res.data;
                if (status === 'success') {
                    this.relationNoticeArr = data.result;
                } else {
                    this.$Message.error(message);
                }
            })
        },
        // 取消
        cancel(type) {
            if(!type) {
                this.reModelFlag = false;
            } else {
                this.reModelFlag = true;
            }
            this.formKey += 1
            this.formApiRelation.apiGroup = ''
            this.formApiRelation.relateApi = ''
            this.formApiRelation.relateApiConfig = null
            this.formApiRelation.version = ''
            this.formApiRelation.resultNotification = []
            if(this.editAddDetailFlag == 'edit') {
              this.$refs['formApiRelation'].resetFields();
            }
        },
        // 保存
        confirmSave() {
            this.$refs.formApiRelation.validate((valid) => {
                if (valid) {
                    let params = {
                        "apiId": this.currentApiInfo.APIid,
                        "id": this.editAddDetailFlag === 'add' ? '' : this.relationApiInfo.id,
                        "relateApi": {
                            "apiId": this.formApiRelation.relateApiConfig.apiId,
                            "apiName": this.formApiRelation.relateApiConfig.apiName,
                            "apiTitle": this.formApiRelation.relateApiConfig.apiTitle,
                            "apiUri": this.formApiRelation.relateApiConfig.apiUri,
                            "httpMethod": this.formApiRelation.relateApiConfig.httpMethod
                        },
                        "relateConfig": {
                            "spis": this.formApiRelation.resultNotification
                        },
                        "relateType": this.formApiRelation.relationType,
                        "version": this.formApiRelation.version
                    };
                    if(this.editAddDetailFlag === 'add') {
                        this.create(params)
                    }
                    if(this.editAddDetailFlag === 'edit') {
                        this.edit(params)
                    }
                }
            })
            
        },
        create(paramsData) {
            Api.yop_api_relation_create(paramsData)
            .then(res => {
                const { status, message, data, code } = res;
                if (status === 'success') {
                    this.$Message.success('新增成功')
                    this.getList()
                    this.cancel()
                } else {
                  this.$Message.error(message ? message : "请选择相同产品下的API进行关联");
                }
            })
        },
        edit(paramsData) {
            Api.yop_api_relation_edit(paramsData)
            .then(res => {
                const { status, message, data } = res;
                if (status === 'success') {
                    this.getList()
                    this.$Message.success('保存成功')
                    this.reModelFlag = false
                } else {
                  this.$Message.error(message ? message : "请选择相同产品下的API进行关联");
                }
            })
        },
        delete(row) {
            let params = {
                id: row.id
            };
            Api.yop_api_relation_delete(params)
            .then(res => {
                const { status, message, data } = res;
                if (status === 'success') {
                    this.getList()
                } else {
                    this.$Message.error(message);
                }
            })
        },

    },
}
</script>