<template>
  <div>
    <Modal v-model="show" width="1000" :closable="false">
      <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
        <Icon type="ios-information-circle"></Icon>
        <span>发布记录</span>
      </p>
      <Row>
        <Col span="7">
          <Col span="6" class="margin-top-10">操作类型:</Col>
          <Col span="18">
            <Select v-model="params.opType">
              <Option
                v-for="(item, index) in fileOptions"
                :key="index"
                :value="item.value"
              >{{item.label}}</Option>
            </Select>
          </Col>
        </Col>
        <Col span="7" offset="1">
          <Col span="6" class="margin-top-10">操作时间:</Col>
          <Col span="18">
            <DatePicker
              type="daterange"
              v-model="time"
              split-panels
              format="yyyy-MM-dd"
              placeholder="操作时间"
              style="width: 200px"
            ></DatePicker>
          </Col>
        </Col>
        <Col span="7" offset="1">
          <Col span="6" class="margin-top-10">操作人:</Col>
          <Col span="18">
            <Input v-model="params.operator" />
          </Col>
        </Col>
      </Row>
      <Row class="margin-top-10" type="flex" justify="end">
        <Col span="4">
          <Button type="primary" @click="getList">查询</Button>
          <Button type="ghost" @click="reset">重置</Button>
        </Col>
      </Row>
      <Row class="margin-top-10">
        <Col>
          <Table :columns="columns" :data="list"></Table>
          <loading :show="showLoading"></loading>
        </Col>
      </Row>
      <div slot="footer" style="border:0;">
        <Button id="api_mange_btn3" type="ghost" @click="cancel">取消</Button>
      </div>
    </Modal>
    <RouteDetail ref="routeDetail" />
    <RollbackRouteModal ref="rollbackRouteModal" />
  </div>
</template>

<script>
import Api from '~/api/newApiManage/route'
import loading from '~/views/my-components/loading/loading'
import RouteDetail from './routeDetail'
import RollbackRouteModal from './rollbackRouteModal'
export default {
  components: {
    loading,
    RollbackRouteModal,
    RouteDetail
  },
  data () {
    const start = new Date()
    const end = new Date()
    start.setTime(end.getTime() - 3600 * 1000 * 24 * 30)
    return {
      fileOptions: [
        {
          value: 'ROLLBACK',
          label: '回滚'
        },
        {
          value: 'PUBLISH',
          label: '发布'
        }
      ],
      columns: [
        {
          title: '操作类型',
          key: 'opType'
        },
        {
          title: '操作原因',
          key: 'cause'
        },
        {
          title: '操作人',
          key: 'operator'
        },
        {
          title: '操作时间',
          key: 'createdDate'
        },
        {
          title: '操作',
          key: 'address',
          render: (h, params) => {
            return h('div',

              [
                h('Button',
                  {
                    props: {
                      type: 'primary'
                    },
                    style: {
                      marginRight: '10px'
                    },
                    on: {
                      click: () => {
                        this.$refs.routeDetail.showModal(params.row.id)
                      }
                    }
                  },
                  '查看'),
                h('Button', {
                  on: {
                    click: () => {
                      this.rollbackRoute(params.row.id)
                    }
                  }
                }, '回滚')
              ])
          }
        }
      ],
      start,
      end,
      list: [],
      show: false,
      showLoading: false,
      params: {
        apiId: this.$store.state.apiRoute.apiId,
        opType: null,
        createdStartDate: start,
        createdEndDate: end,
        _pageNo: 1,
        _pageSize: 10,
        operator: null
      }
    }
  },
  computed: {
    time: {
      get () {
        return [this.params.createdStartDate, this.params.createdEndDate]
      },
      set (value) {
        this.params.createdStartDate = value[0]
        this.params.createdEndDate = value[1]
      }
    }
  },
  methods: {
    reset () {
      this.params = {
        apiId: this.$store.state.apiRoute.apiId,
        opType: null,
        createdStartDate: this.start,
        createdEndDate: this.end,
        _pageNo: 1,
        _pageSize: 10,
        operator: null
      }
      this.getList()
    },
    showModal () {
      this.show = true
      this.$nextTick(() => {
        this.getList()
      })
    },
    dateFormat (date, end = ' 00:00:00') {
      if (date !== '') {
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        if (month < 10) month = '0' + month
        let day = date.getDate()
        if (day < 10) day = '0' + day
        const time = year + '-' + month + '-' + day + end
        return time
      } else {
        return ''
      }
    },
    getList () {
      this.showLoading = true
      Api.routeDeployList({
        ...this.params,
        createdStartDate: this.dateFormat(this.params.createdStartDate),
        createdEndDate: this.dateFormat(this.params.createdEndDate, ' 23:59:59')
      })
        .then(res => {
          const { status, message, solutions, data } = res.data
          if (status === 'success') {
            this.list = data.page.items
          } else {
            this.$ypMsg.notice_error(
              this,
              '列表获取错误',
              message,
              solutions
            )
          }
        })
        .finally(() => {
          this.showLoading = false
        })
    },
    rollbackRoute (id) {
      this.$refs.rollbackRouteModal.showModal(id)
    },
    cancel () {
      this.show = false
    }
  }
}
</script>

<style>
</style>