<template>
  <div class="custom-switch">
    <i-switch v-model="value" :disabled="disabled" @on-change="onChange" >
      <span slot="open">是</span>
      <span slot="close">否</span>
    </i-switch>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    onChange(val) {
      this.$emit('input', val)
    }
  }
}
</script>
<style lang="less" scoped>
	.custom-switch {
		/deep/ .ivu-switch {
      border-color: #ed3f14;
      background-color: #ed3f14;
    }
    /deep/ .ivu-switch-checked {
      border-color: #2d8cf0;
      background-color: #2d8cf0;
    }
    /deep/ .ivu-switch-disabled {
      cursor: not-allowed;
      background: #f3f3f3;
      border-color: #f3f3f3;
    }
	}
</style>
