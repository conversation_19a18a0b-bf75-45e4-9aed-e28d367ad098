<template>
	<div class="constantArguments">
		<template v-if="!isDisplay">
			<el-table
				key="eidt-constantArguments"
				:data="constantParameters"
				border
				:cell-style="{textAlign: 'center',fontSize: '12px' }"
				:header-cell-style="{textAlign: 'center', padding: '5px', fontSize: '12px'}"
			>
				<el-table-column prop="name" label="后端参数名称">
					<template slot-scope="scope">
						<template v-if="scope.row.in === 'HEADER'">
							<el-input
								size="small" 
								key="hasprepend"
								v-model="scope.row.name"
								placeholder="后端参数名称"
								clearable
							>
								<span slot="prepend">x-yop-const-</span>
							</el-input>
						</template>
						<template v-else>
							<el-input
								size="small" 
								key="noprepend"
								placeholder="后端参数名称"
								v-model="scope.row.name"
								clearable
							/>
						</template>
					</template>
				</el-table-column>
				<el-table-column prop="in" label="后端参数位置" width="120">
					<template slot-scope="scope">
						<el-select
							style="width: 100%"
							v-model="scope.row.in"
							size="small"
							@change="(value) => { onSelectChange(value, scope.row)}"
						>
							<el-option v-for="item in valueList" :value="item" :key="item">{{ item }}</el-option>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="index" label="后端参数索引" v-if="type === 'DUBBO'" width="120">
					<template slot-scope="scope">
						<el-input
							size="small" 
							oninput="this.value=this.value.replace(/\D/,'')"
							v-model="scope.row.index"
							:clearable="scope.row.in !== 'RPC_CONTEXT'"
							:disabled="scope.row.in === 'RPC_CONTEXT'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="value" label="参数值" width="220">
					<template slot-scope="scope">
						<el-input size="small"  v-model="scope.row.value" clearable />
					</template>
				</el-table-column>
				<el-table-column prop="internal" label="内部参数" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.internal" />
					</template>
				</el-table-column>
				<el-table-column prop="in" label="是否数组" v-if="type === 'DUBBO'" width="80">
					<template slot-scope="scope">
						<CustomSwitch
							v-model="scope.row.array"
							:clearable="scope.row.in !== 'RPC_CONTEXT'"
							:disabled="scope.row.in === 'RPC_CONTEXT'"
						/>
					</template>
				</el-table-column>
				<el-table-column prop="name" label="操作" width="80">
					<template slot-scope="scope">
						<Button type="primary" @click="deleteOne(scope.$index)" size="small">移除</Button>
					</template>
				</el-table-column>
			</el-table>
			<div class="add-icon" >
				<Icon type="plus-circled" :size="20" @click="addOne"></Icon>
			</div>
		</template>
		<template v-else>
			<el-table
				key="display-constantArguments"
				:data="constantParameters"
				border
				:cell-style="{textAlign: 'center',fontSize: '12px' }"
				:header-cell-style="{textAlign: 'center', padding: '5px', fontSize: '12px'}"
			>
				<el-table-column prop="name" label="后端参数名称">
					<template slot-scope="scope">
						<template v-if="scope.row.in === 'HEADER'">
							x-yop-const{{scope.row.name}}
						</template>
						<template v-else>
							{{scope.row.name}}
						</template>
					</template>
				</el-table-column>
				<el-table-column prop="in" label="后端参数位置" width="120">
				</el-table-column>
				<el-table-column prop="index" label="后端参数索引" key="display-index" v-if="type === 'DUBBO'" width="120">
				</el-table-column>
				<el-table-column prop="value" label="参数值" width="220">
				</el-table-column>
				<el-table-column prop="internal" label="内部参数" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.internal" disabled />
					</template>
				</el-table-column>
				<el-table-column prop="array" label="是否数组" key="display-array" v-if="type === 'DUBBO'" width="80">
					<template slot-scope="scope">
						<CustomSwitch
							v-model="scope.row.array"
							:disabled="true"
						/>
					</template>
				</el-table-column>
			</el-table>
		</template>
	</div>
</template>
<script>
import CustomSwitch from './CustomSwitch.vue'
export default {
  components: {
    CustomSwitch
  },
  props: ['value', 'isDisplay', 'type'],
  data () {
    return {
    }
  },
  computed: {
    apiMsg () {
      return this.$store.state.apiRoute.apiMsg
    },
    valueList () {
      return this.$store.state.apiRoute.valueListMap[this.type]
    },
    sysParamsList () {
      return this.$store.state.apiRoute.sysParamsList
    },
    constantParameters () {
      return this.value
    }
  },
  methods: {
    onSelectChange (value, obj) {
      if (value === 'RPC_CONTEXT') {
        obj.index = ''
      }
    },
    deleteOne (index) {
      this.value.splice(index, 1)
      this.$emit('input', this.value)
    },
    addOne () {
      this.value.push({
        name: '',
        in: this.type === 'DUBBO' ? 'PARAM' : 'BODY',
        value: '',
        array: false,
        internal: true,
        index: '0'
      })
      this.$emit('input', this.value)
    }
  }
}
</script>
<style lang="less" scoped>
	.constantArguments {
		display: flex;
		align-items: flex-end;
		margin-right: 20px;
		.add-icon {
			margin-left: 10px;
			cursor: pointer;
		}
	}
</style>