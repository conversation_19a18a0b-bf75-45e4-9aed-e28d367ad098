<template>
	<div class="serveArguments">
		<template v-if="!isDisplay">
			<el-table
				key="edit-serveArguments"
				:data="serviceParameters"
				border
				:cell-style="{textAlign: 'center',fontSize: '12px' }"
				:header-cell-style="{textAlign: 'center', padding: '5px', fontSize: '12px'}"
			>
				<el-table-column prop="date" label="后端参数名称">
					<template slot-scope="scope">
						<el-input size="small" v-model="scope.row.name" placeholder="后端参数名称" disabled />
					</template>
				</el-table-column>
				<el-table-column prop="in" label="后端参数位置" width="120">
					<template slot-scope="scope">
						<el-select style="width: 100%" v-model="scope.row.in" size="small" disabled>
							<el-option v-for="item in valueList" :value="item" :key="item">{{ item }}</el-option>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column v-if="type === 'DUBBO'" prop="name" label="后端参数索引" width="120">
					<template slot-scope="scope">
						<el-input size="small" oninput="this.value=this.value.replace(/\D/,'')" v-model="scope.row.index" disabled  />
					</template>
				</el-table-column>
				<el-table-column prop="name" label="API参数名称" width="220">
					<template slot-scope="scope">
            <!-- :disabled="scope.row.disabled" -->
						<el-select
							style="width: 100%"
							disabled
							v-model="scope.row.value"
							size="small"
							@change="(value) => { onSelectChange(value, scope.row)}"
						>
							<el-option
								v-for="item in parametersListSpecial"
								:value="item.name"
								:key="item.name"
							>{{ item.name }}</el-option>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="required" label="是否必填" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.required" disabled />
					</template>
				</el-table-column>
				<el-table-column prop="value" label="是否数组" v-if="type === 'DUBBO'" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.array" disabled />
					</template>
				</el-table-column>
				<el-table-column prop="name" label="操作" width="80">
					<template slot-scope="scope">
						<Button type="primary" size="small" :disabled="scope.row.disabled" @click="deleteOne(scope.$index)">移除</Button>
					</template>
				</el-table-column>
			</el-table>
			<!-- <div class="add-icon" >
				<Icon type="plus-circled" :size="20" @click="addOne"></Icon>
			</div> -->
		</template>
		<template v-else>
			<el-table
				key="display-serveArguments"
				:data="serviceParameters"
				border
				:cell-style="{textAlign: 'center',fontSize: '12px' }"
				:header-cell-style="{textAlign: 'center', padding: '5px', fontSize: '12px'}"
			>
				<el-table-column prop="name" label="后端参数名称">
				</el-table-column>
				<el-table-column prop="in" label="后端参数位置" width="120">
				</el-table-column>
				<el-table-column v-if="type === 'DUBBO'" key="display-index" prop="index" label="后端参数索引" width="120">
				</el-table-column>
				<el-table-column prop="value" label="API参数名称" width="220">
				</el-table-column>
				<el-table-column prop="required" label="是否必填" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.required" disabled />
					</template>
				</el-table-column>
				<el-table-column prop="array" label="是否数组" key="display-array" v-if="type === 'DUBBO'" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.array" disabled />
					</template>
				</el-table-column>
			</el-table>
		</template>
	</div>
</template>
<script>
import CustomSwitch from './CustomSwitch.vue'
export default {
  components: {
    CustomSwitch
  },
  props: ['value', 'isDisplay', 'type'],
  data () {
    return {
    }
  },
  computed: {
    serviceParameters () {
      return this.value
    },
    valueList () {
      return this.$store.state.apiRoute.valueListMap[this.type]
    },
    apiMsg () {
      return this.$store.state.apiRoute.apiMsg
    },
    parametersListSpecial () {
      return this.$store.state.apiRoute.parametersListSpecial
    }
  },
  methods: {
    onSelectChange (value, obj) {
      if (!obj.name) {
        obj.name = value
      }
    },
    deleteOne (index) {
      this.value.splice(index, 1)
      this.$emit('input', this.value)
    },
    addOne () {
      this.value.push({
        name: '',
        in: 'PARAM',
        value: '',
        required: false,
        array: true,
        index: 0
      })
      this.$emit('input', this.value)
    }
  }
}
</script>
<style lang="less" scoped>
	.serveArguments {
		display: flex;
		align-items: flex-end;
		margin-right: 20px;
		.add-icon {
			margin-left: 10px;
			cursor: pointer;
		}
	}
</style>