<template>
	<div class="serveArguments">
		<template v-if="!isDisplay">
			<el-table
				@cell-mouse-enter="hoverChange"
				key="edit-serveArguments"
				:data="serviceParameters"
				border
				:cell-style="{textAlign: 'center',fontSize: '12px' }"
				:header-cell-style="{textAlign: 'center', padding: '5px', fontSize: '12px'}"
			>
				<el-table-column prop="date" label="后端参数名称">
					<template slot-scope="scope">
						<div class="param-name">
							<el-tooltip class="item"
							placement="top-start"
							title="标题"
							trigger="hover"
							:content="scope.row.tip ? scope.row.tip : '无'">
								<span class="icon" :class="scope.row.type"><Icon type="information-circled"></Icon></span>
							</el-tooltip>
							<el-tooltip class="item" effect="dark" :content="scope.row.tip ? scope.row.tip : '无'" placement="top">
								<el-input size="small" v-model="scope.row.name" clearable placeholder="后端参数名称" />
							</el-tooltip>
						</div>
					</template>
				</el-table-column>
				<el-table-column prop="in" label="后端参数位置" width="120">
					<template slot-scope="scope">
						<el-select 
							style="width: 100%" 
							v-model="scope.row.in" size="small"
							@change="(value) => { onInSelectChange(value, scope.row)}"
							:disabled="scope.row.canEditArray && type === 'DUBBO'">
							<el-option v-for="item in valueList" 
								:value="item" 
								:key="item" 
								>{{ item }}</el-option>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column v-if="type === 'DUBBO'" prop="name" label="后端参数索引" width="120">
					<template slot-scope="scope">
						<el-input :disabled="scope.row.in === 'RPC_CONTEXT'" size="small" oninput="this.value=this.value.replace(/\D/,'')" v-model="scope.row.index" clearable />
					</template>
				</el-table-column>
				<el-table-column prop="name" label="API参数名称" width="220">
					<template slot-scope="scope">
						<el-select
              allow-create
              filterable
							style="width: 100%"
							v-model="scope.row.value"
              :disabled="scope.row.type === 'delete'"
							size="small"
							@change="(value) => { onSelectChange(value, scope.row)}"
						>
							<el-option v-for="item in parametersList" :value="item.name" :key="item.name">{{ item.name }}</el-option>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="required" label="是否必填" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.required" />
					</template>
				</el-table-column>
				 
				<el-table-column prop="value" label="是否数组" v-if="type === 'DUBBO'" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.array" :disabled="scope.row.canEditArray || scope.row.in === 'RPC_CONTEXT'" />
					</template>
				</el-table-column>
				<el-table-column prop="name" label="操作" width="80">
					<template slot-scope="scope">
						<Button v-if="scope.row.canDelete || scope.row.type === 'delete'" type="primary" @click="deleteOne(scope.$index)" size="small">移除</Button>
					</template>
				</el-table-column>
			</el-table>
			<div class="add-icon" >
				<Icon type="plus-circled" :size="20" @click="addOne"></Icon>
			</div>
		</template>
		<template v-else>
			<el-table
				key="display-serveArguments"
				:data="serviceParameters"
				border
				:cell-style="{textAlign: 'center',fontSize: '12px' }"
				:header-cell-style="{textAlign: 'center', padding: '5px', fontSize: '12px'}"
			>
				<el-table-column prop="name" label="后端参数名称">
				</el-table-column>
				<el-table-column prop="in" label="后端参数位置" width="120">
				</el-table-column>
				<el-table-column v-if="type === 'DUBBO'" key="display-index" prop="index" label="后端参数索引" width="120">
				</el-table-column>
				<el-table-column prop="value" label="API参数名称" width="220">
				</el-table-column>
				<el-table-column prop="required" label="是否必填" width="80">
					<template slot-scope="scope">
            <CustomSwitch v-model="scope.row.required" disabled />
					</template>
				</el-table-column>
				<el-table-column prop="array" label="是否数组" key="display-array" v-if="type === 'DUBBO'" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.array" disabled />
					</template>
				</el-table-column>
			</el-table>
		</template>
	</div>
</template>
<script>
import CustomSwitch from './CustomSwitch.vue'
export default {
  components: {
    CustomSwitch
  },
  props: ['value', 'isDisplay', 'type'],
  data () {
    return {
    }
  },
  computed: {
    serviceParameters () {
      return this.value.map(item => {
        if (!item.value) return item
        const parameter = this.parametersList.find(parameter => parameter.name === item.value)
        if (!parameter) return item
        item.canEditArray = parameter.array
        return item
      })
    },
    valueList () {
      return this.$store.state.apiRoute.valueListMap[this.type]
    },
    apiMsg () {
      return this.$store.state.apiRoute.apiMsg
    },
    parametersList () {
      return this.$store.state.apiRoute.parametersList
    }
  },
  methods: {
	// 参数列表移入展示相关提示
	// row, column, cell, event
	hoverChange(row, column, cell, event) {
		// this.$Message.success('发布成功');
	},
    onSelectChange (value, obj) {
      if (!obj.name) {
        obj.name = value
      }
      const item = this.parametersList.find(item => item.name === value)
      if (item.array && obj.in === 'RPC_CONTEXT') {
        obj.in = 'PARAM'
      }
      obj.canEditArray = item.array
      obj.array = item.array
    },
    onInSelectChange (value, obj) {
      obj.in = value
      if (obj.in === 'RPC_CONTEXT') {
        obj.index = ''
        obj.array = false
      } else {
        obj.index = 0
      }
    },
    deleteOne (index) {
      this.value.splice(index, 1)
      this.$emit('cancelRepeat')
      this.$emit('input', this.value)
    },
    addOne () {
      this.value.push({
        name: '',
        in: this.type === 'DUBBO' ? 'PARAM' : 'BODY',
        value: '',
        required: false,
        array: false,
        index: 0,
        canDelete: true
      })
      this.$emit('input', this.value)
    }
  }
}
</script>
<style lang="less" scoped>
	.serveArguments {
		display: flex;
		align-items: flex-end;
		margin-right: 20px;
		.add-icon {
			margin-left: 10px;
			cursor: pointer;
		}
	}
</style>
<style lang="less">
	.serveArguments {
		.param-name {
			// .test2 {
			// 	color: #E6A23C;
			// }
			.delete {
				color: #ff3300;
			}
			.repeat {
				color: #E6A23C;
			}
			.add {
				color: #3399ff;
			}
			.icon {
				padding: 0 10px 0 0;
				cursor: pointer;
				font-size: 18px;
			}
			display: flex;
			align-items: center;
		}
		// table 列表hover效果
		.el-table--enable-row-hover .el-table__body tr:has(.add):hover>td {
			background-color: #F3FAFF;
		}
		.el-table--enable-row-hover .el-table__body tr:has(.delete):hover>td {
			background-color: #FFF3F3;
		}
		.el-table--enable-row-hover .el-table__body tr:has(.repeat):hover>td {
			background-color: #FFFEF3;
		}
	}
</style>