<template>
<div>
	<Modal v-model="show" width="500" class="api_show">
		<p slot="header" style="color:#2d8cf0;">
			<span style="color:black">导入路由</span>
		</p>
		<div>
			<Form ref="importApi" :model="formData"  :label-width="120">
				<FormItem label="API分组:" class="width-100-perc" prop="apiGroup" :rules="{required:true,message:'api分组不能为空'}">
					<Select
						style="width:200px"
						ref="select_apiM_1"
						id="select_apiM_1"
						class="margin-top-5"
						v-model="formData.apiGroup"
						filterable
						clearable
						placeholder="请选择（默认全部）"
						>
						<Option
								v-for="item in data_apiGroup_List"
								:value="item.value"
								:key="item.value"
						>{{ item.label }}</Option>
						</Select>
				</FormItem>
				<FormItem label="格式:" class="width-100-perc" prop="dataFormat" :rules="{required:true,message:'类型不能为空'}">
					<Select ref="modal_apiM_select_5" style="display: inline-block;width:200px;" id='modal_apiM_select_5' v-model="formData.dataFormat" placeholder="类型">
						<Option v-for="item in data_spi_type" :value="item.value" :key="item.value">{{ item.value }}</Option>
					</Select>
				</FormItem>
				<FormItem label="上传文件:" prop="data" :rules="{required:true,message:'文件不能为空'}">
						<Upload class="margin-right-50" style="display: inline-block;"
							v-model="formData.data"
							ref = "upload"
							:action="importIP"
							:name="name_upload"
							:headers="header_upload"
							:show-upload-list="true"
							:on-format-error="handleFormatError"
							:before-upload="handleBeforeUpload"
							:on-progress="handleProgress"
							:on-success="handleSuccess"
							:on-error="handleError"
						>
						<Button id='btn_apiM_442' type="ghost">点击上传文件</Button><span style="margin-left:10px;">{{this.formData.data.name}}</span>
						</Upload>
				</FormItem>
			</Form>
		</div>
		<div slot="footer">
			<Button id="modal_request_btn_2" type="primary" @click="analysis">分析
			</Button>
			<Button id="modal_request_btn_1" type="ghost" @click="closeModal">取消</Button>
		</div>
		<Loading :show="analysisLoading" />
	</Modal>
	<Modal v-model="analysisShow" width="500">
		<p slot="header" style="color:#000;text-align:left;font-size: 18px;">
				<Icon type="ios-information-circle"></Icon>
				<span>导入路由结果</span> 
		</p>	
		<Collapse v-model="value" accordion >
			<Panel  name="page1">
					待创建路由({{routeToCreateList?routeToCreateList.length:0}}）
					<div slot="content">
						<SelectRoute v-model="selectData.routeToCreate" :routeList="routeToCreateList" />
					</div>
			</Panel>
			<Panel  name="page2">
					待覆盖路由({{routeToOverrideList?routeToOverrideList.length:0}}）
					<div slot="content">
						<SelectRoute v-model="selectData.routeToOverride" :routeList="routeToOverrideList" />
					</div>
			</Panel>
		</Collapse>
		<div slot="footer">
				<Button id="api_mange_btn1" type="primary"  @click="sureImport">确认导入</Button>
				<Button id="api_mange_btn2" type="ghost"  @click="hideImport">取消</Button>
		</div>
		<Loading :show="loading" />
	</Modal>
</div>
</template>

<script>
import Loading from '../../../my-components/loading/loading';
import SelectRoute from './selectRoute.vue'
import api from '../../../../api/api';
export default {
  props: [
    'data_apiGroup_List',
    'data_spi_type',
    'importIP',
    'name_upload',
    'header_upload'
  ],
  components: {
    Loading,
    SelectRoute
  },
  data () {
    return {
      show: false,
      loading: false,
      analysisShow: false,
      analysisLoading: false,
      value: '',
      formData: {
        apiGroup: '',
        data: '',
        dataFormat: ''
      },
      selectData: {
        requestId: '',
        routeToOverride: [],
        routeToCreate: []
      },
      routeToCreateList: [],
      routeToOverrideList: []
    }
  },
  methods: {
    // 文件上传状态绑定函数
    handleFormatError (file) {
      this.$ypMsg.notice_warning(this, '文件 ' + file.name + ' 格式不正确，请选择图片文件。', '文件格式不正确');
    },
    handleBeforeUpload (file) {
      this.formData.data = file
      this.$ypMsg.notice_warning(this, '文件 ' + file.name + ' 准备上传。', '文件准备上传')
      return false
    },
    handleProgress (event, file) {
      this.$ypMsg.notice_info(this, '文件 ' + file.name + ' 正在上传。', '文件正在上传')
    },
    handleSuccess (event, file) {
      let total = event.data.result.total
      let success = event.data.result.success
      let failure = event.data.result.failure
      this.$ypMsg.notice_success(this, '全部API数量：' + total + ' <br/>' + '成功API数量：' + success + ' <br/>' + '失败API数量：' + failure, '文件上传完成结果');
    },
    handleError (event, file, fileList) {
      this.$ypMsg.notice_error_simple(this, '文件上传失败', '文件 ' + fileList.name + ' 上传失败。');
    },
    analysis () {
      this.analysisLoading = true
      const formData = new FormData();
      formData.append('dataFormat', this.formData.dataFormat)
      formData.append('data', this.formData.data)
      formData.append('apiGroup', this.formData.apiGroup)
      api.yop_apiManagement_route_import_analysis(formData)
        .then(
          (response) => {
            if (response.status === 'success') { // todo
              this.$ypMsg.notice_info(this, '文件上传成功');
              this.selectData.requestId = response.data.result.requestId;
              this.routeToCreateList = response.data.result.routeToCreate;
              this.routeToOverrideList = response.data.result.routeToOverride;
              this.show = false
              this.analysisShow = true;
            } else {
              var detail = response.data.detail
              if (detail) {
                var html = '';
                for (var k = 0; k < detail.length; k++) {
                  html += `<p>项目：${detail[k].item} ;值：${detail[k].value} ;原因：${detail[k].reason}</p>`
                }
                this.$Modal.error({
                  title: '失败',
                  content: html
                });
              } else {
                this.$Notice.error({
                  title: '失败',
                  desc: response.message,
                  duration: 10
                });
              }
            }
          }
        )
        .finally(() => {
          this.analysisLoading = false
        })
    },
    showModal () {
      this.show = true
      this.value = ''
      this.formData = {
        apiGroup: '',
        data: '',
        dataFormat: ''
      }
      this.selectData = {
        requestId: '',
        routeToOverride: [],
        routeToCreate: []
      }
      this.routeToCreateList = []
      this.routeToOverrideList = []
    },
    closeModal () {
      this.show = false
    },
    sureImport () {
      if (this.selectData.routeToOverride.length === 0 && this.selectData.routeToCreate.length === 0) {
        this.$ypMsg.notice_warning(this, '没有导入内容，请重新确认');
        this.sure_import_api_loading = false
        return false
      }
      this.loading = true
      api.yop_apiManagement_route_import(this.selectData).then(
        (response) => {
          if (response.status === 'success') {
            var total = response.data.result.total
            var success = response.data.result.success || '0'
            var failed = response.data.result.failed || '0'
            var importFailed = response.data.result.failedDetails;
            let successHtml = ''
            let failedHtml = '';
            successHtml = `<p>导入数量：${total} </p><p>成功数量：${success} </p><p>失败数量：${failed} </p>`
            successHtml = '<p>导入结果</p>' + successHtml
            if (importFailed) {
              for (var l = 0; l < importFailed.length; l++) {
                failedHtml += `<p>原因：${importFailed[l].reason} </p><p>路径：${importFailed[l].key.path} </p><p>HTTP方法：${importFailed[l].key.httpMethod}</p>`
              }
              failedHtml = '<p>导入失败</p' + failedHtml
            }
            if (!importFailed) {
              this.$Modal.success({
                title: '详情',
                content: successHtml
              });
            }
            if (importFailed) {
              this.$Modal.warning({
                title: '详情',
                content: successHtml + failedHtml
              });
            }
            this.analysisShow = false
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      )
        .finally(() => {
          this.loading = false
        })
    },
    hideImport () {
      this.analysisShow = false
    }
  }
}
</script>

<style>

</style>