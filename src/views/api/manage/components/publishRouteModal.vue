<template>
  <Modal v-model="show" width="500" :closable="false">
    <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
      <Icon type="ios-information-circle"></Icon>
      <span>发布路由</span>
    </p>
    <div style="text-align:left;font-size: 12px;">
      <div
        style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;"
      >
        <i class="ivu-icon ivu-icon-help-circled"></i>
      </div>
      <div style="display:inline-block;margin:0 0 0 56px;width:75%">
        <p style="color:red"></p>
        <p>您将要发布当前API下的所有路由到{{this.env === 'dev' ? 'QA' : '生产'}}环境，发布之后立即生效，您确定继续发布吗？</p>
        <label for class="reasonType">
          <span style="color:red;font-size: 12x;">*</span>操作原因：
        </label>
        <Input
          type="textarea"
          v-model="params.cause"
          style="width:85%;font-size: 12x;margin-top:8px;"
          placeholder
          placeholder-style="font-size:12px;"
          :maxlength="60"
        ></Input>
        <p style="color:rgb(255, 153, 0);margin-top:8px;">
          <Icon type="information-circled" color="rgb(255, 153, 0)"></Icon>该操作将导致{{this.env === 'dev' ? 'QA' : '生产'}}环境下路由被覆盖，请仔细确认！
        </p>
      </div>
    </div>
    <div slot="footer" style="border:0;">
      <Button id="api_mange_btn3" type="ghost" @click="cancel">取消</Button>
      <Button id="api_mange_btn4" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import Api from '~/api/newApiManage/route'
export default {
  data () {
    return {
      show: false,
      showLoading: false,
      env: 'dev', // dev pro 区分生产 dev环境
      params: {
        apiId: this.$store.state.apiRoute.apiId,
        cause: ''
      }
    }
  },
  mounted() {
    if(location.origin === 'https://boss3g.yeepay.com') {
      this.env = 'pro'
    } else {
      this.env = 'dev'
    }
  },
  methods: {
    showModal () {
      this.show = true
      this.params.cause = ''
      this.showLoading = false
    },
    cancel () {
      this.show = false
    },
    confirm () {
      this.showLoading = true
      Api.deployRoute(this.params)
        .then(res => {
          const { status, message, solutions } = res
          if (status === 'success') {
            this.cancel()
          } else {
            this.$ypMsg.notice_error(
              this,
              '发布错误',
              message,
              solutions
            )
          }
        })
        .finally(() => {
          this.showLoading = false
        })
    }
  }
}
</script>

<style>
</style>