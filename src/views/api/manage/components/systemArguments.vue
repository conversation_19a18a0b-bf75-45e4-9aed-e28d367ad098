<template>
	<div class="systemArguments">
		<template v-if="!isDisplay">
			<el-table
				key="edit-systemArguments"
				:data="systemParameters"
				border
				:cell-style="{textAlign: 'center',fontSize: '12px' }"
				:header-cell-style="{textAlign: 'center', padding: '5px', fontSize: '12px'}"
			>
				<el-table-column prop="name" label="后端参数名称">
					<template slot-scope="scope">
						<template v-if="scope.row.in === 'HEADER'">
							<el-input
								size="small" 
								key="hasprepend"
								v-model="scope.row.name"
								placeholder="后端参数名称"
								clearable
							>
								<span slot="prepend">x-yop-</span>
							</el-input>
						</template>
						<template v-else>
							<el-input
								size="small" 
								key="noprepend"
								placeholder="后端参数名称"
								v-model="scope.row.name"
								clearable
							/>
						</template>
					</template>
				</el-table-column>
				<el-table-column prop="in" label="后端参数位置" width="120">
					<template slot-scope="scope">
						<el-select style="width: 100%" v-model="scope.row.in" size="small" @change="(value) => { onInChange(value, scope.row)}">
							<el-option v-for="item in valueList" :value="item" :key="item">{{ item }}</el-option>
						</el-select>
					</template>
				</el-table-column>
				<el-table-column prop="index" label="后端参数索引" v-if="type === 'DUBBO'" width="120" key="edit-index">
					<template slot-scope="scope">
						<el-input size="small" oninput="this.value=this.value.replace(/\D/,'')" v-model="scope.row.index" clearable :disabled="scope.row.in === 'RPC_CONTEXT'" />
					</template>
				</el-table-column>
				<el-table-column prop="value" label="参数值"  width="220">
					<template slot-scope="scope">
						<el-autocomplete
							size="small"
							style="width: 100%"
							v-model="scope.row.value"
							:fetch-suggestions="getSysParamsList"
						>
							<template slot-scope="{ item }">
									<div>{{item.name}}</div>
									<div>{{item.code}}</div>
							</template>
						</el-autocomplete>
					</template>
				</el-table-column>
				<el-table-column prop="value" label="内部参数" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.internal" :disabled="scope.row.internal" />
					</template>
				</el-table-column>
				<el-table-column prop="value" label="是否数组" v-if="type === 'DUBBO'" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.array" :disabled="scope.row.in === 'RPC_CONTEXT'" />
					</template>
				</el-table-column>
				<el-table-column prop="name" label="操作" width="80">
					<template slot-scope="scope">
						<Button type="primary" @click="deleteOne(scope.$index)" size="small">移除</Button>
					</template>
				</el-table-column>
			</el-table>
			<div class="add-icon" >
				<Icon type="plus-circled" :size="20" @click="addOne"></Icon>
			</div>
		</template>
		<template v-else>
			<el-table
				key="display-systemArguments"
				:data="systemParameters"
				border
				:cell-style="{textAlign: 'center',fontSize: '12px' }"
				:header-cell-style="{textAlign: 'center', padding: '5px', fontSize: '12px'}"
			>
				<el-table-column prop="name" label="后端参数名称">
				</el-table-column>
				<el-table-column prop="in" label="后端参数位置" width="120">
				</el-table-column>
				<el-table-column 
          prop="index" 
          label="后端参数索引" 
          v-if="type === 'DUBBO'" 
          width="120" 
          key="display-index">
				</el-table-column>
				<el-table-column prop="value" label="参数值" width="220">
					<template slot-scope="scope">
						{{ sysParamsList.find(item => item.code === scope.row.value) ? 
							sysParamsList.find(item => item.code === scope.row.value).code
							: scope.row.value
						 }}
					</template>
				</el-table-column>
				<el-table-column prop="internal" label="内部参数" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.internal" disabled />
					</template>
				</el-table-column>
				<el-table-column prop="array" label="是否数组" key="display-array" v-if="type === 'DUBBO'" width="80">
					<template slot-scope="scope">
						<CustomSwitch v-model="scope.row.array" disabled />
					</template>
				</el-table-column>
			</el-table>
		</template>
	</div>
</template>
<script>
import { Autocomplete } from 'element-ui'
import CustomSwitch from './CustomSwitch.vue'
export default {
  props: ['value', 'isDisplay', 'type'],
  components: {
    ElAutocomplete: Autocomplete,
    CustomSwitch
  },
  data () {
    return {
    }
  },
  computed: {
    valueList () {
      return this.$store.state.apiRoute.valueListMap[this.type]
    },
    sysParamsList () {
      return this.$store.state.apiRoute.sysParamsList
    },
    systemParameters () {
      return this.value
    }
  },
  methods: {
    getSysParamsList (queryString, cb) {
      cb(this.sysParamsList.map(item => ({ value: item.code, ...item })))
    },
    onInChange (value, obj) {
      if (value === 'RPC_CONTEXT') {
        obj.index = ''
        obj.array = false
      }
    },
    deleteOne (index) {
      this.value.splice(index, 1)
      this.$emit('input', this.value)
    },
    addOne () {
      this.value.push({
        name: '',
        in: this.type === 'DUBBO' ? 'PARAM' : 'BODY',
        value: '',
        array: false,
        internal: true,
        index: '0'
      })
      this.$emit('input', this.value)
    }
  }
}
</script>
<style lang="less" scoped>
	.systemArguments {
		display: flex;
		align-items: flex-end;
		margin-right: 20px;
		.add-icon {
			margin-left: 10px;
			cursor: pointer;
		}
	}
</style>