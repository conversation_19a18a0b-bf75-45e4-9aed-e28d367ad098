<template>
  <Modal v-model="show" width="500" class="api_show">
    <p slot="header" style="color:#2d8cf0;">
        <span style="color:black">导出路由</span>
    </p>
    <div>
    <Form ref="formData" :model="formData"  :label-width="120">
      <FormItem label="API分组:" prop="apiGroup" :rules="{required:true,message:'api分组不能为空'}">
        <p>{{formData.apiGroup}}</p>
      </FormItem>
      <FormItem label="是否草稿:" prop="draft">
        <Checkbox
          v-model="formData.draft"
        />
      </FormItem>
      <FormItem label="类型:" class="width-50-perc" prop="dataFormat" :rules="{required:true,message:'类型不能为空'}">
        <Select ref="apiM_select_1" id='apiM_select_1' style="width:200px;" v-model="formData.dataFormat" placeholder="类型">
          <Option v-for="item in data_spi_type" :value="item.value" :key="item.value">{{ item.value }}</Option>
        </Select>
      </FormItem>
    </Form>
    </div>
    <div slot="footer">
      <Button id="modal_request_btn_2" type="primary" @click="confirm">确定
      </Button>
      <Button id="modal_request_btn_1" type="ghost" @click="closeModal">取消</Button>
    </div>
</Modal>
</template>

<script>
import api from '../../../../api/api';
export default {
  props: ['data_spi_type'],
  data () {
    return {
      show: false,
      formData: {
        apiGroup: '',
        apis: [],
        draft: false,
        dataFormat: ''
      },
    }
  },
  methods: {
    showModal (data) {
      this.$refs.formData.resetFields()
      this.formData.apiGroup = data.apiGroup
      this.formData.apis = data.multiSelectedAPIId
      this.formData.dataFormat = ''
      this.formData.draft = false
      this.show = true
    },
    closeModal () {
      this.show = false
    },
    confirm () {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          let type = ''
          if (this.formData.dataFormat === 'JSON') {
            type = 'json'
          } else if (this.formData.dataFormat === 'YAML') {
            type = 'yaml'
          }
          api.yop_apiManagement_route_export(this.formData, type)
          this.closeModal();
        } else {
          this.$Message.error('请检查');
        }
      })
    }
  }
}
</script>

<style>

</style>