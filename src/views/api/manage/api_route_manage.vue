<style lang="less">
	@import '../../../styles/common.less';
	@import '../../../styles/loading.less';
	@import '../api_Mangement_Open.less';
	@import '../../regression-test/regression.less';
</style>
<template>
	<div class="api-route-manage-wrap">
		<Row class="margin-bottom-10">
			<Col>
				<Button id="basic_btn1" type="ghost" @click="returnList">
					<Icon type="chevron-left"></Icon>&nbsp;接口列表
				</Button>
			</Col>
		</Row>
		<Card :bordered="true" dis-hover>
			<div slot="title">
				{{apiRouteParams.name}}
				<Tag checkable color="blue">{{apiRouteParams.httpMethod}}</Tag>
				{{apiRouteParams.interface_URI}}
			</div>
			<div class="margin-bottom-10">
				<Button id="basic_btn2" type="primary" @click="addRoute">新增</Button>
				<Button id="basic_btn3" type="ghost" @click="publishRoute">发布</Button>
				<Button id="basic_btn4" type="ghost" @click="publishRecord">发布记录</Button>
			</div>
			<Alert class="margin-bottom-10 tip">
				<Icon type="ios-information" size="14" color="#2d8cf0" class="margin-right-10"></Icon>路由按照表中排序处理API，您可以选择升序或者降序排序。
			</Alert>
			<Collapse v-model="value1">
				<Panel :name="`${item.id}`" v-for="(item, index) in routeList" :key="item.id">
					<Row @click.native="catRoute(item.id)">
						<Col span="18">
							<div class="header-api">
								<Icon type="chevron-up"></Icon>
								<p class="apigroup">{{item.name}}</p>
								<p>{{item.type}}</p>
								<p class="apigroup">{{item.serviceName}}</p>
								<p>{{item.status === 'ENABLED' ? '已启用' : '已禁用'}}</p>
							</div>
						</Col>
						<Col span="6" v-if="item.id.toString().indexOf('-') === -1">
							<Col span="6">
								<template v-if="item.status !== 'ENABLED'">
									<Button id="basic_btn2" size="small" type="ghost" @click.stop="useRoute(item.id)">启用</Button>
								</template>
								<template v-else>
									<Button id="basic_btn2" size="small" type="ghost" @click.stop="forbidRoute(item.id)">禁用</Button>
								</template>
							</Col>
							<Col span="6">
								<Button id="basic_btn3" size="small" type="ghost" @click.stop="editRoute(item.id)">修改</Button>
							</Col>
							<Col span="6">
								<Button id="basic_btn4" size="small" type="ghost" @click.stop="deleteRoute(item.id)">删除</Button>
							</Col>
							<Col span="6">
								<Button id="basic_btn4" size="small" type="ghost" @click.stop="cloneOne(item, index)">复制</Button>
							</Col>
						</Col>
					</Row>
					<p slot="content">
						<ApiRouteFormDisplay 
              v-if="formType[item.id] === null" 
              :id="item.id" />
						<ApiRouteForm
							v-if="formType[item.id] !== null && formType[item.id]" 
							:id="item.id"
							@cancel="(type) => cancel(type, index)"
							@confirm="getList"
						/>
					</p>
				</Panel>
			</Collapse>
			<loading :show="showLoading"></loading>
		</Card>
		<PublishRecordList ref="publishRecord" />
		<PublishRouteModal ref="publishRouteModal" />
	</div>
</template>

<script>
import Api from '~/api/newApiManage/route'
import loading from '~/views/my-components/loading/loading'
import Sortable from 'sortablejs'
import ApiRouteForm from './api_route_form'
import ApiRouteFormDisplay from './api_route_form_display'
import PublishRecordList from './components/publishRecordList'
import PublishRouteModal from './components/publishRouteModal'
export default {
  name: 'api_route_manage',
  components: {
    ApiRouteForm,
    ApiRouteFormDisplay,
    PublishRouteModal,
    loading,
    PublishRecordList
  },
  data () {
    return {
      value1: [],
      showLoading: false,
      apiRouteParams: {},
      routeList: []
    }
  },
  watch: {
    value1 (newValue, oldValue) {
      if (newValue && newValue.length !== 0) {
        this.Sortable.option('disabled', true)
      } else {
        this.Sortable.option('disabled', false)
      }
    }
  },
  computed: {
    apiId () {
      return this.$store.state.apiRoute.apiId
    },
    formType () {
      return this.$store.state.apiRoute.formType
    }
  },
  created () {
    try {
      this.apiRouteParams = JSON.parse(window.localStorage.getItem('apiRouteParams'))
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error)
    }
    const { APIid, httpMethod, contentType, APItype } = this.apiRouteParams
    this.$store.commit('apiRoute/setApiId', APIid)
    this.$store.commit('apiRoute/setApiMsg', { method: httpMethod, contentType, APItype })
    // 获取参数名称
    this.$store.dispatch('apiRoute/getParametersList')
    // 获取出参参数设置
    this.$store.dispatch('apiRoute/getResponseList')
    // 获取系统参数
    this.$store.dispatch('apiRoute/getSysParams')
    // 获取服务名称
    this.$store.dispatch('apiRoute/getServiceNameList')
  },
  mounted () {
    this.dragSort()
    this.getList()
  },
  methods: {
    dragSort () {
      const wrap = document.querySelector('.ivu-collapse')
      const that = this
      this.Sortable = Sortable.create(wrap, {
        onEnd ({ newIndex, oldIndex }) {
          [that.routeList[newIndex], that.routeList[oldIndex]] = [that.routeList[oldIndex], that.routeList[newIndex]]
          that.arrange()
        }
      })
    },
    arrange () {
      this.showLoading = true
      Api.arrange({
        apiId: this.apiId,
        routeIds: this.routeList.map(item => item.id)
      })
        .then(res => {
          const { status, message, solutions } = res
          if (status === 'success') {
            this.getList()
          } else {
            this.$ypMsg.notice_error(
              this,
              '排序错误',
              message,
              solutions
            )
          }
        })
        .finally(() => {
          this.showLoading = false
        })
    },
    getList () {
      this.showLoading = true
      this.value1 = null
      Api.routeList({apiId: this.apiId})
        .then(res => {
          const { status, message, solutions, data } = res.data
          if (status === 'success') {
            this.routeList = data.result
          } else {
            this.$ypMsg.notice_error(
              this,
              '列表获取错误',
              message,
              solutions
            )
          }
        })
        .finally(() => {
          this.showLoading = false
        })
    },
    catRoute (id) {
      if (id.toString().indexOf('clone') !== -1) return
      if (id.toString().indexOf('add') !== -1) return
      this.$store.commit('apiRoute/changeFormType', { [id]: null })
    },
    useRoute (id) {
      this.showLoading = true
      Api.enable({id})
        .then(res => {
          const { status, message, solutions } = res
          if (status === 'success') {
            this.getList()
          } else {
            this.$ypMsg.notice_error(
              this,
              '启用错误',
              message,
              solutions
            )
          }
        })
        .finally(() => {
          this.showLoading = false
        })
    },
    forbidRoute (id) {
      this.$Modal.confirm({
        title: '禁用路由',
        content: '当前路由已被API关联，您确定要禁用该路由?',
        onOk: () => {
          this.showLoading = true
          Api.disable({id})
            .then(res => {
              const { status, message, solutions } = res
              if (status === 'success') {
                this.getList()
              } else {
                this.$ypMsg.notice_error(
                  this,
                  '禁用错误',
                  message,
                  solutions
                )
              }
            })
            .finally(() => {
              this.showLoading = false
            })
        }
      })
    },
    deleteRoute (id, index) {
      this.$Modal.confirm({
        title: '删除路由',
        content: '当前路由已被API关联，您确定要删除此路由？',
        onOk: () => {
          this.showLoading = true
          Api.deleteRoute({id})
            .then(res => {
              const { status, message, solutions } = res
              if (status === 'success') {
                this.getList()
              } else {
                this.$ypMsg.notice_error(
                  this,
                  '删除错误',
                  message,
                  solutions
                )
              }
            })
            .finally(() => {
              this.showLoading = false
            })
        }
      })
    },
    cloneOne (item, index) {
      const id = `clone-${item.id}`
      this.$store.commit('apiRoute/changeFormType', { [id]: 'clone' })
      this.routeList.splice(index + 1, 0, {
        id,
        type: null,
        serviceName: null,
        status: 'DISABLED'
      })
      this.$nextTick(() => {
        this.value1 = id
      })
    },
    addRoute () {
      const id = `add-${this.routeList.length + 1}`
      this.$store.commit('apiRoute/changeFormType', { [id]: 'add' })
      this.routeList.push({
        id,
        type: null,
        serviceName: null,
        status: 'DISABLED'
      })
      this.$nextTick(() => {
        this.value1 = id
      })
    },
    editRoute (id) {
      this.value1 = `${id}`
      this.$store.commit('apiRoute/changeFormType', { [id]: 'edit' })
    },
    publishRoute () {
      this.$refs.publishRouteModal.showModal()
    },
    publishRecord () {
      this.$refs.publishRecord.showModal()
    },
    returnList () {
      // this.$router.replace({ name: '/api/manage/list' })
      // this.$router.back()
      this.$router.push({
        name: '/api/manage/list',
        params: {
          name: this.$route.params.name,
          path: this.$route.params.path,
          apiType: this.$route.params.apiType,
          method: this.$route.params.method,
          routeStatus: this.$route.params.routeStatus,
          apiGroup: this.$route.params.apiGroup,
          status: this.$route.params.status,
          _pageNo: this.$route.params._pageNo,
        }
      });
    },
    cancel (type, index) {
      if (type === 'add' || type === 'clone') {
        this.value1 = ''
        this.routeList.splice(index, 1)
      }
      if (type === 'edit') {
        this.value1 = ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
	.api-route-manage-wrap {
		height: 100%;
		background-color: #fff;
		padding: 8px;
		/deep/ .ivu-collapse-header .ivu-icon-arrow-right-b {
			display: none;
		}
		/deep/ .ivu-collapse-item-active .ivu-icon-chevron-up {
			transform: rotate(180deg);
		}
		.tip {
			width: 450px;
		}
		.header-api {
			display: flex;
			align-items: center;
      justify-content: space-between;
			p {
				width: 15%;
				margin: 0 20px;
			}
		}
		.apigroup {
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}
	}
</style>
