<template>
  <div class="api_route_form">
    <Form label-position="left" :label-width="135">
      <div class="content">
        <FormItem label="路由名称：" prop="type">{{form.basic.name}}</FormItem>
        <FormItem label="后端类型：" prop="type">{{form.basic.type}}</FormItem>
        <FormItem label="后端服务：" prop="serviceName">{{form.basic.serviceName}}</FormItem>
        <template v-if="form.basic.type === 'HTTP'">
          <FormItem
            label="path："
            prop="properties.path"
            key="properties.path"
          >{{form.basic.properties.path}}</FormItem>
          <FormItem
            label="HTTP请求方法："
            prop="properties.httpMethod"
            key="properties.httpMethod"
          >{{form.basic.properties.httpMethod}}</FormItem>
          <FormItem
            label="ContentType："
            prop="properties.contentType"
            key="properties.contentType"
          >{{form.basic.properties.contentType}}</FormItem>
        </template>
        <template v-else>
          <FormItem
            label="端点类名："
            prop="properties.endClass"
            key="properties.endClass"
          >{{form.basic.properties.endClass}}</FormItem>
          <FormItem
            label="端点方法："
            prop="properties.method"
            key="properties.method"
          >
            <template v-if="form.basic.properties.method">
              <div  v-for="(text, index) in form.basic.properties.method.split(',')" :key="index">
                  {{ text }}
              </div>
            </template>
          </FormItem>
        </template>
        <FormItem label="连接超时：">{{form.basic.properties.connectionTimeout}}ms</FormItem>
        <FormItem label="读取超时：">{{form.basic.properties.readTimeout}}ms</FormItem>
        <FormItem label="谓词：">{{form.basic.predicate}}</FormItem>
        <FormItem label="策略">{{form.basic.filters}}</FormItem>
        <FormItem label="是否幂等：">{{form.basic.properties.idempotent ? '是' : '否'}}</FormItem>
        <h3>入参设置</h3>
        <FormItem
          label="入参请求模式："
          prop="properties.parameterHandlingType"
        >{{form.basic.properties.parameterHandlingType === 'PASSTHROUGH' ? '透传' : '映射'}}</FormItem>
      </div>
      <FormItem
        label="后端服务入参映射："
        prop="serviceParameters"
        key="required"
        v-if="form.basic.properties.parameterHandlingType === 'MAPPING'"
      >
        <ServeArguments v-model="form.request.serviceParameters" :isDisplay="true" :type="form.basic.type" />
      </FormItem>
       <FormItem label="后端服务参数映射：" prop="serviceParameters" v-if="isSpecial">
          <ServeArguments2 v-model="form.request.serviceParameters" :isDisplay="true"  :type="form.basic.type" />
      </FormItem>
      <FormItem label="常量参数：">
        <ConstantArguments v-model="form.request.constantParameters" :isDisplay="true" :type="form.basic.type" />
      </FormItem>
      <FormItem label="系统参数：">
        <SystemArguments v-model="form.request.systemParameters" :isDisplay="true" :type="form.basic.type" />
      </FormItem>
      <h3>出参设置</h3>
      <FormItem label="出参请求模式：" prop="properties.responseHandlingType">
        {{form.basic.properties.responseHandlingType === 'MAPPING' ? '映射' : '透传'}}
      </FormItem>
      <FormItem v-show="form.basic.properties.responseHandlingType !== 'PASSTHROUGH'"  label="后端服务出参映射：" prop="serviceParameters">
        <p>{{form.basic.properties.responseHandlingType === 'MAPPING' ? '映射' : '透传'}}</p>
				<ServeArgumentsRes ref="serveArgumentsRes" :disabledFlag="true" v-model="form.response.serviceResponse" :serviceResponse="form.response.serviceResponse"  :noTip="true" :type="form.basic.type" />
			</FormItem>
    </Form>
  </div>
</template>

<script>
import Api from '~/api/newApiManage/route'
import { Autocomplete } from 'element-ui'
import ServeArguments from './components/serveArguments'
import ServeArguments2 from './components/serveArguments2'
import ConstantArguments from './components/constantArguments'
import SystemArguments from './components/systemArguments'
import ServeArgumentsRes from './components/serveArgumentsRes'
export default {
  props: ['id'],
  components: {
    ServeArguments,
    ServeArguments2,
    ConstantArguments,
    ElAutocomplete: Autocomplete,
    SystemArguments,
    ServeArgumentsRes
  },
  data () {
    return {
      form: {
        basic: {
          name: '',
          apiId: this.$store.state.apiRoute.apiId,
          type: 'HTTP',
          serviceName: '',
          predicate: '',
          filters: '',
          properties: {
            path: '',
            httpMethod: 'GET',
            contentType: 'application/json',
            endClass: '',
            method: '',
            readTimeout: '',
            connectionTimeout: '',
            idempotent: true,
            parameterHandlingType: 'MAPPING',
            responseHandlingType: ''
          }
        },
        request: {
          constantParameters: [],
          systemParameters: [],
          serviceParameters: [],
        },
        response: {
          serviceResponse: []
        },
      },
      resetForm: {
        basic: {
          name: '',
          apiId: this.$store.state.apiRoute.apiId,
          type: 'HTTP',
          serviceName: '',
          predicate: '',
          filters: '',
          properties: {
            path: '',
            httpMethod: 'GET',
            contentType: 'application/json',
            endClass: '',
            method: '',
            readTimeout: '',
            connectionTimeout: '',
            idempotent: true,
            parameterHandlingType: 'MAPPING',
            responseHandlingType: ''
          }
        },
        request: {
          constantParameters: [],
          systemParameters: [],
          serviceParameters: [],
        },
        response: {
          serviceResponse: []
        },
      }
    }
  },
  computed: {
    isSpecial () {
      return this.form.basic.type === 'DUBBO' && this.form.basic.properties.parameterHandlingType === 'PASSTHROUGH' && this.apiMsg.contentType === 'application/x-www-form-urlencoded'
    },
    apiMsg () {
      return this.$store.state.apiRoute.apiMsg
    },
    parametersList () {
      return this.$store.state.apiRoute.parametersList
    },
    responseList () {
      return this.$store.state.apiRoute.responseList
    },
  },
  mounted () {
    this.getRouteDetail()
  },
  methods: {
    getRouteDetail () {
      this.showLoading = true
      let id = this.id
      Api.routeDetail({id})
        .then(res => {
          const { status, message, solutions, data } = res.data
          if (status === 'success') {
            const properties = Object.assign({}, JSON.parse(JSON.stringify(this.resetForm.basic.properties)), data.result.basic.properties)
            let serviceParameters = []
            let constantParameters = []
            if (data.result.request.serviceParameters) {
              serviceParameters = data.result.request.serviceParameters.map(item => {
                if (item.in === 'RPC_CONTEXT') {
                  item.index = ''
                }
                return item
              })
            }
            if (data.result.request.constantParameters) {
              constantParameters = data.result.request.constantParameters.map(item => {
                if (item.in === 'HEADER') {
                  item.name = item.name.replace(/x-yop-const-/, '')
                }
                return item
              })
            }
            this.resetForm.request.serviceParameters = serviceParameters
            this.resetForm.request.constantParameters = constantParameters
            this.form.request.systemParameters = data.result.request.systemParameters
            this.form = Object.assign({}, JSON.parse(JSON.stringify(this.resetForm)), data.result, { properties, serviceParameters, constantParameters })
            this.form.response = data.result.response
            this.form.basic = data.result.basic
            this.$nextTick(() => {
              this.$refs.serveArgumentsRes.setServiceResponse(this.form.response.serviceResponse)
            })
            // this.compare()
          } else {
            this.$ypMsg.notice_error(
              this,
              '获取详情错误',
              message,
              solutions
            )
          }
        })
        .finally(() => {
          this.showLoading = false
        })
    },
    // 对比api 和路由详情 入参映射
    compare() {
      console.log(this.form.response.serviceResponse, '<--2响应列表')
      /**
       * 入参映射相关处理
       */
      //  取删除的
      let deleteArr = [...this.form.request.serviceParameters].filter(x => [...this.parametersList].every(y => y.name !== x.name))
      // 取新增的
      let addArr = [...this.parametersList].filter(x => [...this.form.request.serviceParameters].every(y => y.name !== x.name))
      // 取交集重复的 
      let reqRepeat = [];
      let reqRepeatArr = [];
      this.form.request.serviceParameters.forEach((el,j) => {
        el.type = ''
        // 筛选入参重复项
        if(reqRepeat.indexOf(el['name']) == -1){
          reqRepeat.push(el['name']);
        } else {
          reqRepeatArr.push(el)
        }
        // this.parametersList.forEach((item,i) => {
        //   // 路由详情列表都有，标重复 不过这种情况一般就是 点击保存才会触发。其他不会触发
        //   if(el.name === item.name) {
        //     el.type = 'repeat'
        //     el.tip = '请注意该参数（d）有多个映射，确认是否正确'
        //   }
        //   // 新增情况 api里面有， 路由里面没有 要展示
        // })
        //路由详情有，api列表没有 标删除
        deleteArr.forEach((delItem) => {
          if(delItem.name === el.name) {
            delItem.value = delItem.name
            delItem.in = this.inType
            delItem.canEditArray = delItem.canEditArray
            el.type = 'delete'
            el.tip = '该参数（string）已被删除，与API不一致，保存后该参数会被删除'
          }
        })
      })
      // 新增情况 api里面有， 路由里面没有
      addArr.forEach((addItem) => {
        addItem.value = addItem.name
        addItem.in = this.inType
        addItem.canEditArray = addItem.canEditArray
        addItem.type = 'add'
        addItem.tip = '请注意该参数（i）为新增参数，请确认映射状态'
      })
      
      // 入参重复项
      console.log(reqRepeatArr, '<--reqRepeatArr')
      reqRepeatArr.forEach((eles) => {
        this.form.request.serviceParameters.forEach((el) => {
          if(eles.name === el.name) {
            el.type = 'repeat'
            el.tip = '请注意该参数（d）有多个映射，确认是否正确'
          }
        })
      })
      
      /**
       * 出参相关处理
       */
      this.resCompare()
      // 子项sub 相关重复 删除 增加对应处理 end
      // 获取新增情况的
      this.$nextTick(() => {
        this.form.request.serviceParameters.push.apply(this.form.request.serviceParameters, addArr)
        console.log(this.form.request.serviceParameters, '<---this.form.request.serviceParameters')
        // this.form.response.serviceResponse.push.apply(this.form.response.serviceResponse, resAddArr)
        // this.form.response.serviceResponse.forEach((dom,k) => {
        //   dom.id = k + dom.name
        // })
      })
      // console.log(this.form, '<--处理后')
    },
    // 出参映射处理
    resCompare() {
      /**
       * 出参相关处理
       */
      console.log(this.responseList, '<--this.responseList')
      //  取删除的
      // 进行处理 responseList 进行数据转化
      let compareResList = [];
      // 找到主键
      let resPrimaryName = JSON.parse(this.responseList.models[this.responseList.primaryName]);
      // 主键properties
      let properties = resPrimaryName ? resPrimaryName.properties : null;
      // 遍历主键properties
      for (let key in properties) {
        // console.log(key, properties[key], '<列表值')
        // 没有嵌套类型
        if(properties[key].type !== 'object') {
          compareResList.push({
            name: key,
            value: key,
            in: "BODY",
            subFlag: false,
            level: 1
          })
        } else {
          // 嵌套类型需要再进行sub处理
          // let objKey = key.charAt(0).toUpperCase() + key.slice(1)
          // 取$ref 切割后最后一个 
          let objKey = properties[key].$ref.split('/').pop()
          let subPropertie = JSON.parse(this.responseList.models[objKey]).properties;
          let subArr = [];
          for (const subKey in subPropertie) {
            subArr.push({
              name: subKey,
              in: 'BODY',
              category: 'OUTPUT',
              value: subKey,
              id: subKey,
              subFlag: true,
              level: 2
            })
          }
          // // 子项 遍历 处理新增删除重复
          // // 新增项 -------start----  找到对应路由详情 响应中dto中的 sub  
          let curSub = this.form.response.serviceResponse.find(item => item.name === objKey)
          let subResAddArr = []
          let subResDeleteArr = []
          if(curSub) {
            subResAddArr = [...subArr].filter(x => [...curSub.sub].every(y => y.name !== x.name));
            subResDeleteArr = [...curSub.sub].filter(x => [...subArr].every(y => y.name !== x.name))

            
            subResAddArr.forEach((addItem) => {
              addItem.type = 'add'
              addItem.tip = '请注意该参数（i）为新增参数，请确认映射状态'
            })
            if(curSub) {
              curSub.sub.push.apply(curSub.sub, subResAddArr)
            }
            // 子项新增的 -------end----

            // 子项删除的 -------start----
            curSub.sub.forEach(del => {
              subResDeleteArr.forEach((delItem) => {
                if(delItem.name === del.name) {
                  del.type = 'delete'
                  del.tip = '该参数（string）已被删除，与API不一致，保存后该参数会被删除'
                }
              })
            })
            // 子项删除的 -------end----
          } else {
            // 如果 响应response.serviceResponse 列表为空 则都为新增
            if(this.form.response.serviceResponse.length == 0) {
              this.$nextTick(() => {
                compareResList.map((item) => {
                  item.type = 'add'
                  item.tip = '请注意该参数（i）为新增参数，请确认映射状态'
                })
              })
            }
          }
          compareResList.push({
            name: objKey,
            value: key,
            in: "BODY",
            sub: subArr,
            level: 1
          })
          
        }
      }
      let resDeleteArr = [...this.form.response.serviceResponse].filter(x => [...compareResList].every(y => y.name !== x.name))
      // 取新增的
      let resAddArr = [...compareResList].filter(x => [...this.form.response.serviceResponse].every(y => y.name !== x.name))
      // 标重复的
      let resrepeat = [];
      let resRepeatArr = [];
      this.form.response.serviceResponse.forEach((el,j) => {
        el.type = ''
        //路由详情有，api列表没有 标删除
        resDeleteArr.forEach((delItem) => {
          if(delItem.name === el.name) {
            el.type = 'delete'
            el.tip = '该参数（string）已被删除，与API不一致，保存后该参数会被删除'
            if(el.sub) {
              el.sub.forEach((disableItem) => {
                disableItem.disabledFlag = true
              })
            }
          }
        })
        // 筛选响应重复项
        if(resrepeat.indexOf(el['name']) == -1){
          resrepeat.push(el['name']);
        } else {
          resRepeatArr.push(el)
        }
        // 新增情况 api里面有， 路由里面没有
        resAddArr.forEach((addItem) => {
          addItem.type = 'add'
          addItem.tip = '请注意该参数（i）为新增参数，请确认映射状态'
        })
      })
      // 响应参数重复筛选
      resRepeatArr.forEach((eles) => {
        this.form.response.serviceResponse.forEach((el) => {
          if(eles.name === el.name) {
            el.type = 'repeat'
            el.tip = '请注意该参数（d）有多个映射，确认是否正确'
          }
        })
      })

      // 获取新增情况的
      this.$nextTick(() => {
        this.form.response.serviceResponse.push.apply(this.form.response.serviceResponse, resAddArr)
        this.$refs.serveArgumentsRes.setServiceResponse(this.form.response.serviceResponse)
      })
    },
  }
}
</script>
<style lang="less" scoped>
.api_route_form {
  max-height: 500px;
  overflow-y: auto;
  .content {
    width: 800px;
  }
}
</style>