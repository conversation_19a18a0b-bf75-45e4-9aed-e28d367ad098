<template>
  <Modal v-model="show" width="500" :closable="false">
    <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
      <Icon type="ios-information-circle"></Icon>
      <span>发布文档</span>
    </p>
    <div style="text-align:left;font-size: 12px;">
      <div
        style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;"
      >
        <i class="ivu-icon ivu-icon-help-circled"></i>
      </div>
      <div style="display:inline-block;margin:0 0 0 56px;width:75%">
        <p style="color:red"></p>
        <p>文档发布后，将触发API文档更新，您确定要继续发布文档吗？</p>
        <label for class="reasonType">
          <span style="color:red;font-size: 12x;">*</span>操作原因：
        </label>
        <Input
          type="textarea"
          v-model="cause"
          style="width:85%;font-size: 12x;margin-top:8px;"
          placeholder
          placeholder-style="font-size:12px;"
          :maxlength="60"
        ></Input>
        <p style="color:#888;font-size:12px;margin-top:10px;">最长可输入60字符</p>
      </div>
    </div>
    <div slot="footer" style="border:0;">
      <Button id="api_mange_btn3" type="ghost" @click="cancel">取消</Button>
      <Button id="api_mange_btn4" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import Api from '~/api/newApiManage/apiManage';
export default {
  data () {
    return {
      show: false,
      apiId: '',
      cause: ''
    };
  },
  methods: {
    showModal (apiId) {
      this.show = true;
      this.apiId = apiId;
      this.cause = ''
    },
    cancel () {
      this.show = false;
    },
    confirm () {
      if (!this.cause) {
        this.$Message.error('请输入发布原因');
        return false;
      }
      this.showLoading = true;
      Api.apiDeloyToDoc({
        apiId: this.apiId,
        cause: this.cause
      })
        .then(res => {
          const { status, message, solutions } = res;
          if (status === 'success') {
            this.$ypMsg.notice_success(this, '发布成功');
            this.show = false;
            this.$emit('confirm');
          } else {
            this.$ypMsg.notice_error(
              this,
              '发布错误',
              message,
              solutions
            );
          }
        })
        .finally(() => {
          this.showLoading = false;
        });
    }
  }
};
</script>

<style>
</style>