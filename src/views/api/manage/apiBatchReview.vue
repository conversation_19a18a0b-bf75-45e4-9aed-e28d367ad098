<style lang="less">
    @import '../../../styles/common.less';
    .float-right {
        float: right;
    }
    .red {
        color: #ed3f14;
    }
    .alert-tip {
        width: 46%;
        margin-top: 10px;
    }
    // 新建产品编码 列表
    .addpro-box {
        > h4 {
            display: flex;
            justify-content: space-around;
            background-color: #f8f8f9;
            padding: 10px;
            > span {
                width: 100%;
                text-align: left;
                padding-left: 20px;
            }
        }
        .addpro-table {
            display: flex;
            justify-content: space-around;
            > p {
                width: 18%;
                position: relative;
                > .ivu-input-wrapper, .ivu-select {
                    width: 94%;
                }
                > .tip {
                    display: block;
                }
                
            }
            > .action {
                .ivu-input-wrapper {
                    width: 72%;
                }
                .del-btn {
                    position: absolute;
                    right: 0;
                    top: -10px;
                }
            }
            .hidden {
                visibility: hidden;
            }
        }
    }
</style>
<template>
    <div>
        <Modal
        v-model="visable" width="96%" 
        @on-cancel="cancel"
        title="批量审核"
        >
            <br>
            <Row>
                <Col >
                    <div class="addpro-box">
                        <h4>
                            <span><span class="red">*</span> 产品选择</span>
                            <span>产品类型</span>
                            <span><span class="red">*</span> 产品名称</span>
                            <span><span class="red">*</span> 产品编码</span>
                            <span>产品服务提供方</span>
                            <span>服务描述</span>
                        </h4>
                            <div class="addpro-table margin-top-10" v-for="(item,index) in productData" :key="index">
                                <p>
                                    <RadioGroup class="margin-top-10" v-model="item.isNew" @on-change="changeProduct(item,index)">
                                        <Radio id="rbtn_review_1" label="false">选择已有产品</Radio>
                                        <Radio id="rbtn_review_2" label="true" >新建产品</Radio>
                                    </RadioGroup>
                                </p>
                                <p>
                                    <Select v-model="item.productType" @on-change="onProductTypeChange(item, index)">
                                        <Option v-for="item in productTypeOption" :value="item.code" :key="item.value">{{ `${item.code}(${item.name})` }}</Option>
                                    </Select>
                                </p>
                                <p v-if="item.isNew === 'false'" :key="item.productType">
                                    <Select filterable :on-select="chooseProduct(item,index)"  v-model="item.productCode">
                                        <Option v-for="ele in productList.filter(p => p.productType === item.productType)" :value="ele.productCode" :disabled="ele.disabled" :key="ele.code">{{ `${ele.productName} (${ele.productCode})` }}</Option>
                                    </Select>
                                    <span v-if="!item.productCode && commitFlag" class="red tip">请填写产品名称</span>
                                </p>
                                <p v-if="item.isNew === 'true'">
                                    <Input placeholder="数字、中英文不超过64字符" v-model="item.productName" :disabled="item.isNew === 'true' ? false : true"></Input>
                                    <span v-if="!item.productName && commitFlag" class="red tip">请填写产品名称</span>
                                </p>

                                <p>
                                    <Input placeholder="数字、英文、下划线，不超过64字符" v-model="item.productCode" :disabled="item.isNew === 'true' ? false : true"></Input>
                                    <span v-if="!item.productCode && commitFlag && item.isNew === 'true'" class="red tip">请填写产品编码</span>
                                    <!-- <span>支持数字、大小写字母、下划线，最长支持输入64个字符。</span> -->
                                    <!-- <span v-if="item.productCode && item.fillTypeFlag" class="red tip">格式不正确</span> -->
                                </p>
                                <p>
                                    <Select :disabled="item.isNew === 'true' ? false : true" v-model="item.spCode" >
                                        <Option v-for="item in productServeOption" :value="item.spCode" :key="item.spCode">{{ `${item.spCode}(${item.spName})`}}</Option>
                                    </Select>
                                </p>
                                <p class="action">
                                    <Input v-model="item.desc" :disabled="item.isNew === 'true' ? false : true"></Input>
                                    <span class="del-btn">
                                        <Button v-if="index > 0" type="primary" @click="delProduct(item,index)" class="float-right margin-top-10">删除</Button>
                                    </span>
                                </p>
                            </div>
                    </div>
                    <Button type="primary" @click="addProduct" class="float-right margin-top-10">添加</Button>
                </Col>
            </Row>
            <br>
            <Row>
                <Col span="24">
                    <Table v-if="tableShow" :key="tableKey" id='table_api_review2' border ref="review_table_2" :columns="columns_ApiInterfaceList" :data="reviewTableDatas" ></Table>
                </Col>
            </Row>
            <div slot="footer">
                <Button type="primary" @click="commitCheck" >提交审核</Button>
            </div>
        </Modal>
    </div>
</template>
<script>
import api from '~/api/api'
import util from '~/libs/util'
export default {
    name: 'apiBatchReview',
    components: {
    },
    props: ['batchReviewData'],
    data() {
        let that = this
        return {
            visable: true,
            tableShow: true,
            isCommitFlag: false, //已有产品时提交开关
            tableKey: 1,
            // 产品码类型
            productType: '1',
            productArr: [],
            productList: [],
            // 请求后台产品编码
            reqArr: [],
            reviewTableDatas: [],
            commitFlag: false,  //提交开关
            // 校验必填
            rules: {
                name: [{ required: true, message: '请填写产品名称', trigger: 'blur'}]
            },
            // 产品类型下拉
            productTypeOption: [],
            // 服务提供方
            productServeOption:[],
            productData: [
                {
                    "isNew": 'false',
                    "desc": "",
                    "productCode": "",
                    "productName": "",
                    "productType": "",
                    "spCode": "",
                },
            ],
            // api 表头
            columns_ApiInterfaceList: [
                {
                    title: 'API',
                    key: 'name',
                    width: 110,
                    render (h, params) {
                        return h('div',
                        [
                            h('div', params.row.name),
                            h('div', params.row.interface_Title)
                        ]);
                    },
                    align: 'center'
                },
                {
                    title: 'API分组',
                    key: 'apiGroup',
                    type: 'html',
                    align: 'center'
                },
                {
                    title: '方法/请求路径',
                    minWidth: 160,
                    render: (h, params) => {
                        var color = 'blue';
                        return h('div', [
                        h('Tag', {
                            style: {
                            align: 'center'
                            },
                            props: {
                            color: color
                            }
                        }, params.row.httpMethod),
                        h('span', params.row.interface_URI)]);
                    }
                },
                {
                    title: '是否维护结果通知',
                    key: 'relateCallback',
                    type: 'text',
                    align: 'center',
                    render (h, params) {
                        return h('div', params.row.relateCallback  === 'yes' ? '已维护' : '未维护' );
                    },
                },
                {
                    title: '是否维护错误码',
                    key: 'groupRelateErrorCode',
                    render (h, params) {
                        return h('div',
                        [
                            h('div', params.row.relateErrorCode === 'yes' ? '已维护' : '未维护'),
                            h('div', params.row.groupRelateErrorCode === 'yes' ? '已维护' : '未维护')
                        ]);
                    },
                    align: 'center'
                },
            ],
            
        }
    },
    mounted() {
        this.getProductsCode()
        this.getProductsType()
        this.reviewTableDatas = this.batchReviewData
        this.reviewTableDatas.map((item) => {
            this.getErrcodeList(item)
            this.getApiManageDetail(item)
            this.getApiGroupErrList(item)
        })
        // 获取产品列表
        this.getProducts()
        let _that = this
        // 处理获取完信息后的 列表数据
        setTimeout(() => {
            _that.initTableDatas()
            _that.tableKey += _that.tableKey
        }, 1000);
    },
    methods: {
        getProducts() {
            api.yop_product_all().then(
                (response) => {
                    if (response.data.status === 'success') {
                        this.productList = response.data.data.result
                    }
                }
            )
        },
        // 获取错误码列表
        getErrcodeList(row) {
            let param = {
                apiId: row.APIid,
                pageNo: '1',
                total: '0',
            };
            api.yop_errorCode_has_relevance_api(param).then(
                (response) => {
                    if (response.data.status === 'success') {
                        if(response.data.data.page.items.length > 0) {
                            this.forEachTable(row.APIid, 'relateErrorCode')
                        }
                    }
                }
            )
        },
        // 获取api详情 
        getApiManageDetail(row) {
            let param = {
                apiId: row.APIid,
            };
            api.yop_api_manage_detail(param).then(
                (response) => {
                    if (response.data.status === 'success') {
                        if(response.data.data.result.callbacks.length > 0) {
                            this.forEachTable(row.APIid, 'relateCallback')
                        }
                    }
                }
            )
        },
        // 获取api分组错误码
        getApiGroupErrList(row) {
            let params = {
                apiGroupCode: row.APIgroupCode,
                _pageNo: 1,
                _pageSize: 10
            };
            api.yop_errorCode_list(params).then(
                (response) => {
                    if (response.data.status === 'success') {
                        if(response.data.data.result.items.length > 0) {
                            this.forEachTable(row.APIid, 'groupRelateErrorCode')
                        }
                    } else {
                    }
                }
            );
        },
        // 循环处理 当前列表
        forEachTable(apiId,type) {
            this.reviewTableDatas.forEach(element => {
                if(element.APIid === apiId) {
                    element[type] = 'yes'
                }
            });
        },
        // 获取产品服务编码
        getProductsCode() {
            api.yop_invokeSta_spCode().then(
                (response) => {
                    if (response.data.status === 'success') {
                        this.productServeOption = response.data.data.result
                    }
                }
            )
        },
        // 获取产品类型
        getProductsType() {
            api.yop_apiManagement_productTypeList().then(
                (response) => {
                    if (response.data.status === 'success') {
                        this.productTypeOption = response.data.data.result
                    }
                }
            )
        },
        // 切换产品编码类型 置空
        changeProduct(item,index){
            if(item.isNew === 'true') {
                this.productData[index].desc = ''
                this.productData[index].productCode = ''
                this.productData[index].productName = ''
                this.productData[index].productType = ''
                this.productData[index].spCode = ''
            } else {
                this.productData[index].desc = ''
            }
            this.productData[index].isNew = item.isNew
      },
      onProductTypeChange(item, index) {
        this.productData[index].productCode = ''
      },
        // 选择已有产品
      chooseProduct(item, index) {
            this.productList.forEach((element,i) => {
                if(element.productCode === item.productCode) {
                    this.productData[index].isNew = item.isNew
                    this.productData[index].productCode = element.productCode
                    this.productData[index].productName = element.productName
                    // this.productData[index].productType = element.productType
                    this.productData[index].spCode = element.spCode
                }
                element.disabled = false
            });
            // 将已选择过得产品名称 不可选
            this.productList.forEach((ele,i) => {
                this.productData.forEach((itms,j) => {
                    if(itms.productCode == ele.productCode) {
                        ele.disabled = true
                    }
                })
            });
        },
        cancel() {
            this.$emit('openBatchReview','close')
        },
        initTableDatas() {
            this.$nextTick(() => {
                this.tableShow = true
            })
        },
        // 提交审核
        commitCheck() {
            // 重置请求
            let tableFillFlag = false;
            this.commitFlag = true
            this.productData.forEach((item) => {
                if(!item.productName || !item.productCode) {
                    tableFillFlag = true
                } else {
                    if (util.format_check_common(item.productCode, /^[0-9a-zA-Z_-]+?$/)) {
                        tableFillFlag = true
                        this.$Message.warning('产品编码格式不正确，请检查!');
                    }
                    if (util.format_check_common(item.productName, /^[0-9a-zA-Z—\u4e00-\u9fa5]+?$/)) {
                        tableFillFlag = true
                        this.$Message.warning('产品名称格式不正确，请检查!');
                    }
                    if(util.getLength(item.productCode) > 64) {
                        tableFillFlag = true
                        this.$Message.warning('产品编码长度不能大于64!')
                    }
                    if(util.getLength(item.productName) > 64) {
                        tableFillFlag = true
                        this.$Message.warning('产品名称长度不能大于64!')
                    }
                }
            })
            if(!tableFillFlag) {
                this.confirmSub()
            }
        },
        // 请求批量审核
        confirmSub() {
            let apisArr = [];
            this.reviewTableDatas.forEach((item) => {
                apisArr.push({
                    apiDescription: item.APItypeDesc,
                    apiId: item.APIid,
                    apiName: item.name,
                    apiTitle: item.interface_Title,
                    apiUri: item.interface_URI,
                    httpMethod: item.httpMethod,
                    relateCallback: item.relateCallback === 'yes' ? true : false,
                    relateErrorCode: item.relateErrorCode === 'yes' ? true : false,
                    groupRelateErrorCode: item.groupRelateErrorCode === 'yes' ? true : false,
                })
            });
            let newProducts = JSON.parse(JSON.stringify(this.productData));
            newProducts.forEach((item) => {
                item.newProduct = item.isNew === 'true' ? true : false
                delete item.isNew
            })
            let params = {
                "apis": apisArr,
                // "newProducts": [],
                "products": newProducts,
            };
            api.yop_apiManagement_batchReview(params).then(
                (response) => {
                    if (response.code === '000000') {
                        this.$ypMsg.notice_success(this,'审核信息已提交至流程魔方，请耐心等待审批');
                        this.cancel()
                    } else {
                        this.$Message.error(response.message);
                    }
                }
            )
        },
        // 添加产品编码
        addProduct() {
            let row = {
                "isNew": 'true',
                "desc": "",
                "productCode": "",
                "productName": "",
                "productType": "",
                "spCode": "",
            };
            this.productData.push(row)
        },
        // 删除空白的按钮
        delProduct(item,index) {
            if(this.productData.length > 1) {
                this.productData = this.productData.filter((item,j) => j !== index);
            }
            this.productList.forEach((element,i) => {
                if(element.productCode === item.productCode) {
                    element.disabled = false
                }
            });
        }
    }
}
</script>