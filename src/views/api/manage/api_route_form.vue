<template>
	<div class="api_route_form">
		<Form :model="form.basic" ref="form" label-position="left" :label-width="135" :rules="comFromRules">
			<div class="content">
        <FormItem label="路由名称：" prop="name">
          <Input v-model="form.basic.name"/>
				</FormItem>
				<FormItem label="后端类型：" prop="type">
					<Select v-model="form.basic.type" @on-change="onTypeChange" :disabled="formType[this.id] === 'edit'">
						<Option value="HTTP">http</Option>
						<Option value="DUBBO">dubbo</Option>
					</Select>
				</FormItem>
				<FormItem label="后端服务：" prop="serviceName">
					<Select v-model="form.basic.serviceName" @on-change="changeAppName">
						<Option v-for="item in serviceNameList" :value="item.name" :key="item.name">{{item.name}}</Option>
					</Select>
				</FormItem>
				<template v-if="form.basic.type === 'HTTP'">
					<FormItem label="path：" prop="properties.path" key="properties.path">
						<Input v-model="form.basic.properties.path" />
					</FormItem>
					<FormItem label="HTTP请求方法：" prop="properties.httpMethod" key="properties.httpMethod">
						<Select v-model="form.basic.properties.httpMethod" @on-change="onHttpMethodChange">
							<Option
								value="GET"
								:disabled="apiMsg.contentType === 'application/json' && form.basic.type === 'HTTP'"
							>GET</Option>
							<Option value="POST">POST</Option>
						</Select>
					</FormItem>
					<FormItem label="ContentType：" prop="properties.contentType" key="properties.contentType">
						<Select v-model="form.basic.properties.contentType" @on-change="onContentTypeChange">
							<Option
								:disabled="form.basic.properties.httpMethod === 'GET' && form.basic.type === 'HTTP'"
								value="application/json"
							>application/json</Option>
							<Option
								value="application/x-www-form-urlencoded"
								:disabled="apiMsg.contentType === 'application/json' && form.basic.type === 'HTTP'"
							>application/x-www-form-urlencoded</Option>
						</Select>
					</FormItem>
				</template>
				<template v-else>
          <Form-item label="接口环境：" required>
            <Radio-group v-model="env">
              <Radio v-for="(item, index) in envList" :label="item.name" :key="index">{{item.title}}</Radio>
            </Radio-group>
          </Form-item>
					<FormItem label="端点类名：" prop="properties.endClass" key="properties.endClass">
            <el-autocomplete
              key="properties.endClass"
              size="small"
              style="width: 100%"
              type="select"
              v-model="form.basic.properties.endClass"
              :fetch-suggestions="getFacadeList"
              clearable
              placeholder="请选择端点类名"
              @select="changeClassName"
            >
            <i
              class="el-icon-caret-bottom"
              slot="suffix">
            </i>
              <template slot-scope="{ item }">
                <div v-for="(text, index) in item.value.split(',')" :key="index">
                  {{ text }}
                </div>
              </template>
            </el-autocomplete>
					</FormItem>
					<FormItem label="端点方法：" prop="properties.method" key="properties.method">
						<el-autocomplete
							key="properties.method"
              :rows="4"
              size="small"
              style="width: 100%"
              type="select"
              clearable
							v-model="form.basic.properties.method"
							:fetch-suggestions="getFacadeDesc"
							placeholder="请选择端点方法"
						>
            <i
              class="el-icon-caret-bottom"
              slot="suffix">
            </i>
              <template slot-scope="{ item }">
                <div v-for="(text, index) in item.value.split(',')" :key="index">
                  {{ text }}
                </div>
              </template>
            </el-autocomplete>
            <!-- <Select v-model="form.basic.properties.method"  >
              <Option v-for="item in facadeDescList" :value="item" :key="item">{{item}}</Option>
            </Select> -->
					</FormItem>
				</template>
				<FormItem label="连接超时：" key="connectionTimeout">
					<Input v-model.number="form.basic.properties.connectionTimeout" style="width: 100px">
						<span slot="append">ms</span>
					</Input>
				</FormItem>
				<FormItem label="读取超时：" key="readTimeout">
					<Input v-model.number="form.basic.properties.readTimeout" style="width: 100px">
						<span slot="append">ms</span>
					</Input>
				</FormItem>
				<FormItem label="谓词：">
					<Input v-model="form.basic.predicate" type="textarea" />
				</FormItem>
				<FormItem label="策略">
					<Input v-model="form.basic.filters" type="textarea" />
				</FormItem>
				<FormItem label="是否幂等：">
					<RadioGroup :value="form.basic.properties.idempotent ? '是' : '否'" @on-change="onRadioChange">
						<Radio label="是">是</Radio>
						<Radio label="否">否</Radio>
					</RadioGroup>
				</FormItem>
        <h3>入参设置</h3>
				<FormItem label="入参请求模式：" prop="properties.parameterHandlingType">
					<Select v-model="form.basic.properties.parameterHandlingType" @on-change="onSelectChange">
						<Option value="PASSTHROUGH">透传</Option>
						<Option value="MAPPING" :disabled="apiMsg.contentType === 'application/json'">映射</Option>
					</Select>
				</FormItem>
			</div>
      <FormItem
          label="后端服务入参映射："
          prop="serviceParameters"
          key="required"
          v-if="form.basic.properties.parameterHandlingType === 'MAPPING'"
      >
          <ServeArguments v-model="form.request.serviceParameters" @cancelRepeat="cancelRepeat" :type="form.basic.type" />
      </FormItem>
      <!-- <FormItem key="noRequired" label="后端服务入参映射：" prop="serviceParameters" v-if="isSpecial">
          <ServeArguments2
            v-model="form.request.serviceParameters"
            :type="form.basic.type"
          />
      </FormItem> -->
			<FormItem label="常量参数：" prop="constantParameters">
				<ConstantArguments v-model="form.request.constantParameters" :type="form.basic.type" />
			</FormItem>
			<FormItem label="系统参数：" prop="systemParameters">
				<SystemArguments v-model="form.request.systemParameters" :type="form.basic.type" />
			</FormItem>
      <h3>出参设置</h3>
      <FormItem label="出参请求模式：" prop="properties.responseHandlingType">
        <Select style="width: 665px;" v-model="form.basic.properties.responseHandlingType" @on-change="onChangeResType">
          <Option value="PASSTHROUGH">透传</Option>
          <Option value="MAPPING">映射</Option>
        </Select>
      </FormItem>
      <FormItem v-show="form.basic.properties.responseHandlingType === 'MAPPING'" label="后端服务出参映射：" prop="serviceParameters">
      <!-- <FormItem v-if="resTableDisabled" label="后端服务出参映射：" prop="systemParameters"> -->
				<ServeArgumentsRes ref="serveArgumentsRes" v-model="form.response.serviceResponse" :serviceResponse="form.response.serviceResponse" @refreshResData="refreshResData" :type="form.basic.type" />
			</FormItem>
			<FormItem label v-if="isShowBtn">
				<Button type="primary" @click="save">保存</Button>
				<Button type="ghost" @click="cancel">取消</Button>
			</FormItem>
		</Form>
		<loading :show="showLoading"></loading>
	</div>
</template>

<script>
import Api from '~/api/newApiManage/route'
import loading from '~/views/my-components/loading/loading'
import { Autocomplete } from 'element-ui'
import ServeArguments from './components/serveArguments'
import ServeArguments2 from './components/serveArguments2'
import ServeArgumentsRes from './components/serveArgumentsRes'
import ConstantArguments from './components/constantArguments'
import SystemArguments from './components/systemArguments'
import util from '~/libs/util';
export default {
  props: ['id'],
  components: {
    ServeArguments,
    ServeArguments2,
    ServeArgumentsRes,
    ConstantArguments,
    ElAutocomplete: Autocomplete,
    loading,
    SystemArguments
  },
  data () {
    // 路由名称验证
    const validate_routeName = (rule, value, callback) => {
      if (util.format_check_common(value, /^[0-9a-zA-Z\u4e00-\u9fa5]+?$/)) {
        callback(new Error('格式不正确'));
      } else if (util.getLength(value) > 64) {
        callback(new Error('长度不能大于64'));
      } else {
        callback();
      }
    };
    function isEmpty (str) {
      if (str === 0 || str === undefined) return false
      return !str
    }
    const checkParameters = (rule, value, callback) => {
      const arr = value
      const checkMap = {}
      let flag = true
      let flag2 = true
      let key = ''
      let msg = ''
      let msg2 = ''
      // HTTP的 后端参数名称+后端参数位置加唯一校验（同时这两个都不能为空）
      // DUBBO的
      // PARAM: 后端参数位置+后端参数索引+后端参数名称加唯一校验（后端参数名称可以为空，其他不能为空）
      // RPC_CONTEXT: 后端参数位置+后端参数名称加唯一校验(后端参数名称不能为空)
      if(arr) {
        for (let i = 0; i < arr.length; i++) {
          if (this.form.basic.type === 'HTTP' || arr[i].in === 'RPC_CONTEXT') {
            if (isEmpty(arr[i].name) || isEmpty(arr[i].value)) {
              flag2 = false
              msg2 = '后端参数名称、参数值不能为空'
              break
            }
            key = `${arr[i].name}${arr[i].in}`
            msg = '参数重复：后端参数名称+后端参数位置'
          } else {
            if (isEmpty(arr[i].index) || isEmpty(arr[i].value)) {
              flag2 = false
              msg2 = '后端参数索引、参数值不能为空'
              break
            }
            key = `${arr[i].name}${arr[i].in}${arr[i].index}`
            msg = '参数重复：后端参数名称+后端参数位置+后端参数索引'
          }
          if (checkMap[key]) {
            flag = false
            break
          } else {
            checkMap[key] = true
          }
        }
      }
      if (!flag2) {
        return callback(new Error(msg2))
      }
      if (!flag) {
        return callback(new Error(msg))
      }
      return callback()
    }
    return {
      showLoading: false,
      isShowBtn: false,
      endClassList: [],
      methodClassList: [],
      // 端点类名列表
      facadeList: [],
      facadeDescList: [], //端点方法
      resTableDisabled: true,
      showSystemParametersFlag: false, //出参设置 系统参数显示开关
      form: {
        basic: {
          apiId: this.$store.state.apiRoute.apiId,
          filters: '',
          serviceName: '',
          predicate: '',
          type: 'DUBBO',
          properties: {
            path: '',
            httpMethod: 'GET',
            contentType: 'application/x-www-form-urlencoded',
            endClass: '',
            method: '',
            readTimeout: '',
            connectionTimeout: '',
            idempotent: true,
            parameterHandlingType: 'PASSTHROUGH', //入参模式 映射    透传 PASSTHROUGH
            responseHandlingType: "PASSTHROUGH" // 响应出参 模式     映射 MAPPING    透传
          }
        },
        request: {
          constantParameters: [],
          systemParameters: [],
          serviceParameters: [],
        },
        response: {
          serviceResponse: []
        },
      },
      resetForm: {
        basic: {
          name: '',
          apiId: this.$store.state.apiRoute.apiId,
          filters: '',
          serviceName: '',
          predicate: '',
          type: 'DUBBO',
          properties: {
            path: '',
            httpMethod: 'GET',
            contentType: 'application/x-www-form-urlencoded',
            endClass: '',
            method: '',
            readTimeout: '',
            connectionTimeout: '',
            idempotent: true,
            parameterHandlingType: 'PASSTHROUGH'
          }
        },
        request: {
          constantParameters: [],
          systemParameters: [],
          serviceParameters: [],
        },
        response: {
          serviceResponse: [
            //  {
            //   "category": "CONSTANT",
            //   "in": "BODY",
            //   "name": "string",
            //   "sub": [
            //     null
            //   ],
            //   "value": "string"
            // }
          ]
        },
        // type: 'DUBBO',
        // serviceName: '',
        // predicate: '',
        // filters: '',
        // constantParameters: [],
        // systemParameters: [],
        // serviceParameters: [],
        // properties: {
        //   path: '',
        //   httpMethod: 'GET',
        //   contentType: 'application/x-www-form-urlencoded',
        //   endClass: '',
        //   method: '',
        //   readTimeout: '',
        //   connectionTimeout: '',
        //   idempotent: true,
        //   parameterHandlingType: 'PASSTHROUGH'
        // }
      },
      ruleValidate: {
        name: [
          {required: true, message: '请输入路由名称', trigger: 'blur'}
        ],
        type: [
          { required: true, message: '请选择后端类型', trigger: 'change' }
        ],
        serviceName: [
          { required: true, message: '请输入后端服务', trigger: 'change' }
        ],
        'properties.path': [
          { required: true, message: '请输入path', trigger: 'change' }
        ],
        'properties.httpMethod': [
          { required: true, message: '请选择httpMethod', trigger: 'change' }
        ],
        'properties.contentType': [
          { required: true, message: '请选择contentType', trigger: 'change' }
        ],
        'properties.endClass': [
          { required: true, message: '请选择端点类名', trigger: 'change' }
        ],
        'properties.method': [
          { required: true, message: '请选择端点方法', trigger: 'change' }
        ],
        'properties.parameterHandlingType': [
          { required: true, message: '请选择入参请求模式', trigger: 'change' }
        ],
        'properties.responseHandlingType': [
          { required: true, message: '请选择入参请求模式', trigger: 'change' }
        ],
        constantParameters: [
          { required: false, validator: checkParameters, trigger: 'change' }
        ],
        systemParameters: [
          { required: false, validator: checkParameters, trigger: 'change' }
        ]
      },
      // serviceParametersRequired: [
      //   { required: true, type: 'array', message: '请添加后端服务入参映射1', trigger: 'change' },
      //   { validator: checkParameters, trigger: 'change' }
      // ],
      // serviceParameters: [
      //   { required: false, type: 'array', message: '请添加后端服务入参映射2', trigger: 'change' },
      //   { validator: checkParameters, trigger: 'change' }
      // ],
      env: '',
      envList: []
    }
  },
  computed: {
    comFromRules () {
      if (this.isSpecial) {
        return {
          ...this.ruleValidate,
          serviceParameters: this.serviceParameters
        }
      }
      return {
        ...this.ruleValidate,
        serviceParameters: this.serviceParametersRequired
      }
    },
    isSpecial () {
      return this.form.basic.type === 'DUBBO' && this.form.basic.properties.parameterHandlingType === 'PASSTHROUGH' && this.apiMsg.contentType === 'application/x-www-form-urlencoded'
    },
    inType () {
      if (this.form.basic.type === 'DUBBO') {
        return 'PARAM'
      } else {
        if (this.form.basic.properties.httpMethod === 'GET') {
          return 'QUERY'
        } else {
          return 'BODY'
        }
      }
    },
    formType () {
      return this.$store.state.apiRoute.formType
    },
    apiMsg () {
      return this.$store.state.apiRoute.apiMsg
    },
    serviceNameList () {
      return this.$store.state.apiRoute.serviceNameList.filter(item => item.type === this.form.basic.type)
    },
    parametersList () {
      return this.$store.state.apiRoute.parametersList
    },
    responseList () {
      return this.$store.state.apiRoute.responseList
    },
    parametersListSpecial () {
      return this.$store.state.apiRoute.parametersListSpecial
    }
  },
  watch: {
    formType: {
      handler (newProps, oldProps) {
        const val = newProps[this.id]
        const valOld = oldProps[this.id]
        if (val !== valOld) {
          this.isShowBtn = false
          // 编辑
          if (val === 'edit') {
            this.isShowBtn = true
          }
          // 复制
          if (val === 'clone') {
            this.isShowBtn = true
          }
          this.getRouteDetail()
        }
      },
      deep: true
    }
  },
  mounted () {
    this.getEnvList()
    // 新增
    if (this.formType[this.id] === 'add') {
      this.isShowBtn = true
      this.form.basic.properties.idempotent = this.apiMsg.method === 'GET'
      if (this.isSpecial) {
        this.form.request.serviceParameters = this.parametersListSpecial.map(item => {
          return Object.assign({}, {
            name: '',
            in: 'PARAM',
            location: this.apiMsg.method === 'GET' ? 'QUERY' : 'BODY',
            value: item.name,
            required: false,
            disabled: true,
            array: true,
            index: 0
          }, item)
        })
        return
      }
      this.form.request.serviceParameters = this.parametersList.map(item => {
        return Object.assign({}, {
          name: '',
          in: 'PARAM',
          location: this.apiMsg.method === 'GET' ? 'QUERY' : 'BODY',
          value: item.name,
          required: false,
          canEditArray: item.array,
          array: false,
          index: 0
        }, item)
      })
      return
    }
    // 编辑
    if (this.formType[this.id] === 'edit') {
      this.isShowBtn = true
    }
    // 复制
    if (this.formType[this.id] === 'clone') {
      this.isShowBtn = true
    }
    this.getRouteDetail()
  },
  methods: {
    onHttpMethodChange (value) {
      if (value === 'GET') {
        this.form.basic.properties.idempotent = true
      } else {
        this.form.basic.properties.idempotent = false
      }
      if (value === 'GET' && this.form.basic.properties.contentType !== 'application/x-www-form-urlencoded') {
        this.form.basic.properties.contentType = 'application/x-www-form-urlencoded'
      }
    },
    onContentTypeChange (value) {
      if (value === 'application/json' && this.form.basic.properties.parameterHandlingType !== 'PASSTHROUGH') {
        this.form.basic.properties.parameterHandlingType = 'PASSTHROUGH'
        this.$refs.form.validateField('properties.parameterHandlingType')
      }
    },
    // 出参选择模式
    onChangeResType(value) {
      // this.resCompare()
      // PASSTHROUGH 透传
      if(value === 'PASSTHROUGH') {
        // 出参表格禁止填写
        // this.resTableDisabled = false
      } else {
        // this.getRouteDetail()
        // this.compare()
        // 展示出参表格
        // this.resTableDisabled = true
      }
    },
    onSelectChange (value) {
      // 映射和透传切换时，初始化后端服务参数
      if (this.isSpecial && this.formType[this.id] === 'add') {
        this.form.request.serviceParameters = this.parametersListSpecial.map(item => {
          return Object.assign({}, {
            name: '',
            in: 'PARAM',
            location: this.apiMsg.method === 'GET' ? 'QUERY' : 'BODY',
            value: item.name,
            required: false,
            disabled: true,
            array: true,
            index: 0
          }, item)
        })
        return
      }
      if (value === 'MAPPING' && this.formType[this.id] === 'add') {
        this.form.request.serviceParameters = this.parametersList.map(item => {
          return Object.assign({}, {
            name: '',
            location: this.apiMsg.method === 'GET' ? 'QUERY' : 'BODY',
            in: this.inType,
            value: item.name,
            required: false,
            canEditArray: item.array,
            index: 0
          }, item)
        })
      }
      this.form.request.serviceParameters.forEach((item, i) => {
        if (item.canDelete) {
          this.form.request.serviceParameters.splice(i,1)
        }
      })
    },
    onTypeChange () {
      this.form.basic.serviceName = ''
      this.form.basic.properties.httpMethod = ''
      this.form.basic.properties.contentType = ''
      this.form.basic.properties.parameterHandlingType = ''
      this.resetParams(this.form.request.constantParameters)
      this.resetParams(this.form.request.systemParameters)
      this.resetParams(this.form.request.serviceParameters)
    },
    resetParams (list) {
      list.forEach(item => {
        item.in = this.inType
      })
    },
    // 查询后端类
    getEndclassList (queryString, cb) {
      this.form.basic.properties.method = ''
      Api.getEndclassList({
        serviceName: this.form.basic.serviceName
      })
        .then(res => {
          const { status, data } = res.data
          if (status === 'success') {
            const result = data.result || []
            cb(result.map(item => ({ value: item })))
          }
        })
    },
    // 查询端点类对应的方法
    getListByEndclass (queryString, cb) {
      this.form.basic.properties.endClass = this.form.basic.properties.endClass.trim()
      Api.getListByEndclass({
        endClass: this.form.basic.properties.endClass,
        serviceName: this.form.basic.serviceName
      })
        .then(res => {
          const { status, data } = res.data
          if (status === 'success') {
            const result = data.result || []
            cb(result.map(item => ({ value: item })))
          }
        })
    },
    getFacadeDesc (queryString, cb) {
      if (!this.form.basic.serviceName) return
      this.form.basic.properties.endClass = this.form.basic.properties.endClass.trim()
      Api.getFacadeDesc({
        appName: this.form.basic.serviceName,
        facadeName: this.form.basic.properties.endClass,
        env: this.env
      })
        .then(res => {
          if (res.data.code === '200') {
            Api.getFacadeDescData({ swagger: res.data.data })
              .then(res1 => {
                if (res1.status === 'success') {
                  this.params = res1.data.result
                  console.log('methods => ' + res1.data.result.methods)
                  const list = Object.keys(res1.data.result.methods).filter(item => item.indexOf(queryString) !== -1)
                  cb(list.map(item => ({ value: item })))
                  this.facadeDescList = res1.data.result.methods
                } else {
                  cb()
                  this.$Modal.error({
                    title: '错误',
                    content: res1.message
                  });
                }
              })
              .catch(() => {
                cb()
              })
          } else {
            cb()
            this.$Modal.error({
              title: '错误',
              content: res.data.message
            });
          }
        })
        .catch(() => {
          cb()
        })
    },
    onRadioChange (value) {
      this.form.basic.properties.idempotent = value === '是'
    },
    getRouteDetail () {
      this.showLoading = true
      let id = this.id
      if (this.formType[this.id] === 'clone') {
        id = this.id.split('-')[1]
      }
      Api.routeDetail({id})
        .then(res => {
          const { status, message, solutions, data } = res.data
          if (status === 'success') {
            // const properties = Object.assign({}, JSON.parse(JSON.stringify(this.resetForm.basic.properties)), data.result.properties)
            let serviceParameters = []
            let constantParameters = []
            if(data.result.request) {
              if (data.result.request.serviceParameters) {
                serviceParameters = data.result.request.serviceParameters.map(item => {
                  if (item.in === 'RPC_CONTEXT') {
                    item.index = ''
                  }
                  return item
                })
              }
              if (data.result.request.constantParameters) {
                constantParameters = data.result.request.constantParameters.map(item => {
                  if (item.in === 'HEADER') {
                    item.name = item.name.replace(/x-yop-const-/, '')
                  }
                  return item
                })
              }
            }

            this.resetForm.request.serviceParameters = serviceParameters
            this.resetForm.request.constantParameters = constantParameters
            // this.form = Object.assign({}, JSON.parse(JSON.stringify(this.resetForm)), data.result, { properties, serviceParameters, constantParameters })
            this.form = Object.assign({}, JSON.parse(JSON.stringify(this.resetForm)), data.result)
            this.form.response = data.result.response
            this.form.basic.properties.responseHandlingType = this.form.basic.properties.responseHandlingType ? this.form.basic.properties.responseHandlingType : 'PASSTHROUGH'
            console.log(this.form, '<---forme2')
            // 对比 api 和路由详情
            this.compare()
          } else {
            this.$ypMsg.notice_error(
              this,
              '获取详情错误',
              message,
              solutions
            )
          }
        })
        .finally(() => {
          this.showLoading = false
          // 获取类名列表
          // this.getFacadeList()
        })
    },
    // 移除某一项后取消重复
    cancelRepeat() {
      let repeatArr = []
      var arrId = []
      this.form.request.serviceParameters.forEach((item, index) => {
        if(arrId.indexOf(item['name']) == -1){
          arrId.push(item['name']);
        } else {
          repeatArr.push(item)
        }
      });
      if(repeatArr.length > 0) {
        repeatArr.forEach((item) => {
          this.form.request.serviceParameters.forEach((el) => {
            if(item.name === el.name) {
              el.type = 'repeat'
              el.tip = '请注意该参数（d）有多个映射，确认是否正确'
            } else {
              if(el.type === 'repeat') {
                el.type = ''
                el.tip = ''
              }
            }
          })
        })
      } else {
        this.form.request.serviceParameters.forEach((el) => {
          if(el.type === 'repeat') {
            el.type = ''
            el.tip = ''
          }
        })
      }
    },
    // 出参 移除 标删除的 将删了的数据传回来
    refreshResData(data) {
      this.form.response.serviceResponse = JSON.parse(JSON.stringify(data))
    },
    save () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // 校验重复项 进行对比
          var arrId = [];
          var resArrId = [];
          let repeatArr = [];
          let resRepeatArr = [];
          let postForm = JSON.parse(JSON.stringify(this.form))
          if(this.form.request.serviceParameters) {
              this.form.request.serviceParameters.forEach((item, index) => {
                item.repeatFlag = `${item.name}${item.in}${item.index}`
                if(arrId.indexOf(item['repeatFlag']) == -1){
                  arrId.push(item['repeatFlag']);
                } else {
                  repeatArr.push(item)
                }
              
            });
            // 去掉删除项 临时去掉
            // postForm.request.serviceParameters = postForm.request.serviceParameters.filter(item => item.type !== "delete");
            // console.log(postForm.request.serviceParameters, '<--裁剪后')
            // 入参重复标志
            let repeatFlag = false
            repeatArr.forEach((item) => {
              item.repeatFlag = `${item.name}${item.in}${item.index}`
              this.form.request.serviceParameters.forEach((el) => {
                if(item.repeatFlag === el.repeatFlag) {
                  el.type = 'repeat'
                  el.tip = '请注意该参数（d）有多个映射，确认是否正确'
                  repeatFlag = true
                  return
                } else {
                  repeatFlag = false
                }
              })
            })
            this.form.request.serviceParameters.push({})
            this.form.request.serviceParameters = this.form.request.serviceParameters.slice(0,-1)
            if(repeatFlag) {
              return
            }
          }
          // 处理删除的 不传给后端
          // 删除响应参数的
          if(this.form.response.serviceResponse.length > 0 ) {
            this.form.response.serviceResponse.forEach((del,index) => {
              // 筛选重复项
              if(resArrId.indexOf(del['name']) == -1){
                resArrId.push(del['name']);
              } else {
                resRepeatArr.push(del)
              }
              // 去掉删除项 注释了不去掉
              // if(del.type === 'delete') {
              //   postForm.response.serviceResponse.splice(index, 1)
              // }
              
              // if(del.sub) {
              //   del.sub.forEach((subDel, j) => {
              //     if(subDel.type === 'delete') {
              //       del.sub.splice(j, 1)
              //     }
              //   });
              // }
            })
          }
          // 出参重复 拦截
          let resRepeatFlag = false
          resRepeatArr.forEach((row) => {
            this.form.response.serviceResponse.forEach((el) => {
              if(row.name === el.name) {
                el.type = 'repeat'
                el.tip = '请注意该参数（d）有多个映射，确认是否正确'
                resRepeatFlag = true
                return false
              } else {
                resRepeatFlag = false
              }
            })
          })
          this.form.response.serviceResponse.push({})
          this.form.response.serviceResponse = this.form.response.serviceResponse.slice(0,-1)
          // 编辑
          
          if(resRepeatFlag) {
            return
          }
          this.showLoading = true
          if (this.formType[this.id] === 'clone') {
            postForm.basic.id = null
          }
          if (postForm.basic.id) {
            Api.editRoute(postForm)
              .then(res => {
                const { status, message, solutions } = res
                if (status === 'success') {
                  this.$emit('confirm')
                } else {
                  this.$ypMsg.notice_error(
                    this,
                    '编辑错误',
                    message,
                    solutions
                  )
                }
              })
              .finally(() => {
                this.showLoading = false
              })
            return
          }
          Api.create(postForm)
            .then(res => {
              const { status, message, solutions } = res
              if (status === 'success') {
                this.$emit('confirm')
              } else {
                this.$ypMsg.notice_error(
                  this,
                  '新增错误',
                  message,
                  solutions
                )
              }
            })
            .finally(() => {
              this.showLoading = false
            })
        }
      })
    },
    cancel () {
      this.$emit('cancel', this.formType[this.id])
    },
    changeAppName () {
      this.form.basic.properties.endClass = ''
      this.form.basic.properties.method = ''
    },
    changeClassName () {
      this.form.basic.properties.method = ''
      console.log(this.form.basic.properties.endClass)
    },
    getEnvList () {
      Api.getManageEnvList()
        .then(res => {
          this.envList = (res.data && res.data.data && res.data.data.result) || []
          this.env = (this.envList && this.envList[0] && this.envList[0].name) || ''
        })
    },
    getFacadeList (queryString, cb) {
      if (!this.form.basic.serviceName) return
      Api.getFacadeList({
        appName: this.form.basic.serviceName,
        env: this.env
      })
        .then(res => {
          if (res.data.code === '200') {
            this.facadeList = res.data.data.facadeList
            const list = res.data.data.facadeList.filter(item => item.indexOf(queryString) !== -1)
            cb(list.map(item => ({ value: item })))
          } else {
            cb()
            this.$Modal.error({
              title: '错误',
              content: res.data.message
            });
          }
        })
    },
    // 对比api 和路由详情 入参映射
    compare() {
      // console.log(this.parametersList, '<--this.parametersList 22api的')
      // console.log(this.form.request.serviceParameters, '<--this.form.request.serviceParameters')
      // console.log(this.form.request.serviceParameters, '<--form.request.serviceParameters 路由详情')
      
      /**
       * 入参映射相关处理
       */
      //  取删除的
      let deleteArr = [...this.form.request.serviceParameters].filter(x => [...this.parametersList].every(y => y.name !== x.value))
      console.log(deleteArr, '<--删除arr')
      // 取新增的
      let addArr = [...this.parametersList].filter(x => [...this.form.request.serviceParameters].every(y => y.value !== x.name))
      // 取交集重复的 
      let reqRepeat = [];
      let reqRepeatArr = [];
      this.form.request.serviceParameters.forEach((el,j) => {
        el.type = ''
        el.repeatFlag = `${el.name}${el.in}${el.index}`
        // 筛选入参重复项
        if(reqRepeat.indexOf(el['repeatFlag']) == -1){
          reqRepeat.push(el['repeatFlag']);
        } else {
          reqRepeatArr.push(el)
        }
        // this.parametersList.forEach((item,i) => {
        //   // 路由详情列表都有，标重复 不过这种情况一般就是 点击保存才会触发。其他不会触发
        //   if(el.name === item.name) {
        //     el.type = 'repeat'
        //     el.tip = '请注意该参数（d）有多个映射，确认是否正确'
        //   }
        //   // 新增情况 api里面有， 路由里面没有 要展示
        // })
        //路由详情有，api列表没有 标删除
        deleteArr.forEach((delItem) => {
          if(delItem.name === el.name) {
            // delItem.value = delItem.name
            delItem.in = this.inType
            delItem.canEditArray = delItem.canEditArray
            el.type = 'delete'
            el.tip = '该参数（string）在API中已被删除'
          }
        })
        // 新增的取值 路由里面的
        addArr.forEach((add) => {
          if(add.name === el.name) {
            add = el
          }
        })
      })
      // 新增情况 api里面有， 路由里面没有
      addArr.forEach((addItem) => {
        addItem.value = addItem.name
        addItem.in = this.inType
        addItem.canEditArray = addItem.canEditArray
        addItem.type = 'add'
        addItem.tip = '请注意该参数（i）为新增参数，请确认映射状态'
      })
      
      // 入参重复项
      reqRepeatArr.forEach((eles) => {
        eles.repeatFlag = `${eles.name}${eles.in}${eles.index}`
        this.form.request.serviceParameters.forEach((el) => {
          if(eles.repeatFlag === el.repeatFlag) {
            el.type = 'repeat'
            el.tip = '请注意该参数（d）有多个映射，确认是否正确'
          }
        })
      })
      
      /**
       * 出参相关处理
       */
      this.resCompare()
      // 子项sub 相关重复 删除 增加对应处理 end
      // 获取新增情况的
      this.$nextTick(() => {
        this.form.request.serviceParameters.push.apply(this.form.request.serviceParameters, JSON.parse(JSON.stringify(addArr)))
        this.form.request.serviceParameters = this.form.request.serviceParameters.filter(item => !!item.value && !!item.name)
        console.log(this.form.request.serviceParameters, '<---this.form.request.serviceParameters')
        // this.form.response.serviceResponse.push.apply(this.form.response.serviceResponse, resAddArr)
        // this.form.response.serviceResponse.forEach((dom,k) => {
        //   dom.id = k + dom.name
        // })
      })
      // console.log(this.form, '<--处理后')
    },
    // 出参映射处理
    resCompare() {
      /**
       * 出参相关处理
       */
      console.log(this.responseList, '<--this.responseList')
      //  取删除的
      // 进行处理 responseList 进行数据转化
      let compareResList = [];
      // 找到主键
      let resPrimaryName = JSON.parse(this.responseList.models[this.responseList.primaryName]);
      // 主键properties
      let properties = resPrimaryName ? resPrimaryName.properties : null;
      // 遍历主键properties
      for (let key in properties) {
        // console.log(key, properties[key], '<列表值')
        // 没有嵌套类型
        if(properties[key].type !== 'object') {
          compareResList.push({
            name: key,
            value: key,
            in: "BODY",
            subFlag: false,
            level: 1
          })
        } else {
          // 嵌套类型需要再进行sub处理
          // console.log(key, '<--1key')
          // let objKey = key.charAt(0).toUpperCase() + key.slice(1)
          // 取$ref 切割后最后一个 
          let objKey = properties[key].$ref.split('/').pop()
          let subPropertie = JSON.parse(this.responseList.models[objKey]).properties;
          let subArr = [];
          for (const subKey in subPropertie) {
            subArr.push({
              name: subKey,
              in: 'BODY',
              category: 'OUTPUT',
              value: subKey,
              id: subKey,
              subFlag: true,
              level: 2
            })
          }
          // // 子项 遍历 处理新增删除重复
          // // 新增项 -------start----  找到对应路由详情 响应中dto中的 sub  
          let curSub = this.form.response.serviceResponse.find(item => item.name === objKey)
          let subResAddArr = []
          let subResDeleteArr = []
          if(curSub) {
            subResAddArr = [...subArr].filter(x => [...curSub.sub].every(y => y.name !== x.name));
            subResDeleteArr = [...curSub.sub].filter(x => [...subArr].every(y => y.name !== x.name))

            
            subResAddArr.forEach((addItem) => {
              addItem.type = 'add'
              addItem.tip = '请注意该参数（i）为新增参数，请确认映射状态'
            })
            if(curSub) {
              curSub.sub.push.apply(curSub.sub, subResAddArr)
            }
            // 子项新增的 -------end----

            // 子项删除的 -------start----
            curSub.sub.forEach(del => {
              subResDeleteArr.forEach((delItem) => {
                if(delItem.name === del.name) {
                  del.type = 'delete'
                  del.tip = '该参数（string）在API中已被删除'
                }
              })
            })
            // 子项删除的 -------end----
          } else {
            // 如果 响应response.serviceResponse 列表为空 则都为新增
            if(this.form.response.serviceResponse.length == 0) {
              this.$nextTick(() => {
                compareResList.map((item) => {
                  item.type = 'add'
                  item.tip = '请注意该参数（i）为新增参数，请确认映射状态'
                })
              })
            }
          }
          // // 最后将值push进去
          // subArr.push.apply(subArr, subResAddArr)
          // console.log(subArr, '<--最后的subarr')
          compareResList.push({
            name: key,
            value: key,
            in: "BODY",
            sub: subArr,
            level: 1
          })
          
        }
      }
      console.log(this.form.response.serviceResponse, '<---22响应列表')
      console.log(compareResList, '<---1compareResList')
      let resDeleteArr = [...this.form.response.serviceResponse].filter(x => [...compareResList].every(y => y.name !== x.name))
      // 取新增的
      let resAddArr = [...compareResList].filter(x => [...this.form.response.serviceResponse].every(y => y.name !== x.name))
      // 标重复的
      let resrepeat = [];
      let resRepeatArr = [];
      this.form.response.serviceResponse.forEach((el,j) => {
        if(el.sub) {
          el.sub.forEach((subEl) => {
            subEl.level = 2
          })
        }
        el.type = ''
        //路由详情有，api列表没有 标删除
        resDeleteArr.forEach((delItem) => {
          if(delItem.name === el.name) {
            el.type = 'delete'
            el.tip = '该参数（string）已被删除，与API不一致，保存后该参数会被删除'
            if(el.sub) {
              el.sub.forEach((disableItem) => {
                disableItem.disabledFlag = true
              })
            }
          }
        })
        // 筛选响应重复项
        if(resrepeat.indexOf(el['name']) == -1){
          resrepeat.push(el['name']);
        } else {
          resRepeatArr.push(el)
        }
        // 新增情况 api里面有， 路由里面没有
        resAddArr.forEach((addItem) => {
          addItem.type = 'add'
          addItem.tip = '请注意该参数（i）为新增参数，请确认映射状态'
        })
      // 子项sub 相关重复 删除 增加对应处理 start
      })
      // 响应参数重复筛选
      resRepeatArr.forEach((eles) => {
        this.form.response.serviceResponse.forEach((el) => {
          if(eles.name === el.name) {
            el.type = 'repeat'
            el.tip = '请注意该参数（d）有多个映射，确认是否正确'
          }
        })
      })

      // 获取新增情况的
      this.$nextTick(() => {
        this.form.response.serviceResponse.push.apply(this.form.response.serviceResponse, resAddArr)
        this.$refs.serveArgumentsRes.setServiceResponse(this.form.response.serviceResponse)
      })
    },
  }
}
</script>
<style lang="less" scoped>
.api_route_form {
    max-height: 500px;
    overflow-y: auto;
    .content {
        width: 800px;
    }
}
</style>
