<style lang="less">
    @import '../../../styles/common.less';
    @import '../api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .reasonType {
        vertical-align: top;
        display: inline-block;
        font-size: 12px;
        margin-top: 5px;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-height:16px;text-align:center;color:#f00;text-decoration:none}
    .ivu-modal-confirm-body-icon.ivu-modal-confirm-body-icon-warning + div {
        overflow-wrap:break-word;
    }
    .api_show .ivu-modal-footer{
        border-top:1px solid #e9eaec!important;
    }
    .noArrow .ivu-icon-arrow-right-b{
        visibility:hidden!important;
    }
    .padding-none{
        padding: 0;
    }
    .up-grade{
        display: block;
        width: 11px;
        position: relative;
        left: 103px;
    }
    .hide{
        display: none;
    }
    .description{
        cursor: pointer;
        padding: 2px 7px;
    }
    
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="24">
                
                <Col span="6">
                    <Col span="6" class="margin-top-10">
                        <span >API分组:</span>
                    </Col>
                    <Col span="17" >
                    <Select id='selectRecordAPIGroup' ref="clearRecordApiGroup" class="margin-top-5" v-model="recordSearch.apiGroup" filterable clearable placeholder="请选择（默认全部）">
                        <Option v-for="item in data_apiGroup_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                    </Col>
                </Col>
                <!--  -->
                <Col span="6" >
                    <Col span="6" class="margin-top-10">
                        <span >发布环境:</span>
                    </Col>
                    <Col span="17" >
                    <Select id='selectRecordAPIEnv' ref='clearRecordEnv' class="margin-top-5" v-model="recordSearch.env" filterable clearable placeholder="请选择（默认全部）">
                        <Option v-for="item in apiEnvList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                    </Col>
                </Col>
                <!--  -->
                <Col span="6">
                    <Col span="6" class="margin-top-10">
                        <span >请求路径:</span>
                    </Col>
                    <Col span="17" >
                        <Input id='inputRecordAPIURI' class="margin-top-5" clearable v-model="recordSearch.requestPath" placeholder="请求路径" @on-enter="search_Interface"></Input>
                    </Col>
                </Col>

                <Col  span="6" offset="" >
                    <Col span="6" class="margin-top-10">
                        <span >请求方法:</span>
                    </Col>
                    <Col span="17" >
                        <Select  class="margin-top-5" ref="clearRecordMethod" v-model="recordSearch.requestMethod" filterable clearable placeholder="请选择">
                            <Option v-for="item in requestMethodList" :value="item.value" :key="item.value">{{ item.label }}</Option>
                        </Select>
                    </Col>
                </Col>

                
            </Col>
            </Row>
            <Row>
            <Col span="24">
                <!--  -->
                <Col class="margin-top-5" span="6" >
                    <Col span="6" class="margin-top-10">
                        <span >操作类型:</span>
                    </Col>
                    <Col span="17" >
                        <Select id='selectAPIType' class="margin-top-5" ref="clearRecordType"  v-model="recordSearch.opType"  placeholder="请选择（默认全部）" clearable>
                            <Option v-for="item in apiOpTypes" :value="item.value" :key="item.value">{{ item.label }}</Option>
                        </Select>
                    </Col>
                </Col>
                <!-- 时间 -->
                <Col class="margin-top-5" span="10">
                    <Col span="4" class="margin-top-10">
                        <span>操作时间:</span>
                    </Col>
                    <Col span="18" class="margin-top-5">
                        <DatePicker 
                            type="datetime"
                            ref="start"
                            @on-change="update_start_date"
                            format="yyyy-MM-dd"
                            placeholder="选择开始时间" 
                            style="width: 166px">
                        </DatePicker>
                        至
                        <DatePicker 
                            type="datetime"
                            ref="end"
                            @on-change="update_end_date"
                            format="yyyy-MM-dd" 
                            placeholder="选择结束时间" 
                            style="width: 166px">
                        </DatePicker>
                    </Col>
                </Col>

                <Col  span="2" >
                    <Col class="margin-top-10"  span="11" style="text-align:center">
                        <Button id='btnSearchList' v-url="{url:'/rest/api/manage/deploy-record/list'}" type="primary" @click="searchApiPublishRecord">查询</Button>
                    </Col>
                </Col>

                <Col span="2">
                    <Col class="margin-top-10"  span="11" style="text-align:center">
                        <Button id='btnReset' type="ghost" @click="resetRecordSearch">重置</Button>
                    </Col>
                </Col>

                </Col>
            </Row>
            
        
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_1' border ref="selection" :columns="apiPublishRecordList" :data="apiPublishRecordTable" ></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="recordPageTotal" :page-size="20" :current="recordPageNo" show-elevator @on-change="pageRecordRefresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_apiList"></loading>
            </Row>
        </Card>
        <modal_opt_record ref="modal_opt_record"></modal_opt_record>
    <!-- api发布管理modal ==== -->
    <!-- 下线弹框 -->
    <Modal v-model="downApiFlag" title="">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
              <Icon type="ios-information-circle"></Icon>
              <span>下线API</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;">
                <i class="ivu-icon ivu-icon-help-circled"></i>
              </div>
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                <p style="color:red"></p>
                <p >您将要下线一下API： <Button id="api_publish_btn1" size="small" type="primary">{{this.currentApi.httpMethod}}</Button>  {{this.currentApi.interface_URI}}</p>
                <p>下线环境 : {{this.apiEnv}}</p>
                <label for="" class="reasonType"><span style="color:red;font-size: 12x;">*</span>下线原因：</label>
                <Input type="textarea" v-model="offline_api_cause" style="width:85%;font-size: 12x;margin-top:8px;" placeholder="" placeholder-style="font-size:12px;"></Input>
                <!-- <p style="color:#888;font-size:12px;margin-top:10px;margin-left:50px">最长可输入60字符</p> -->
                <p>该操作将导致生产环境该API不可用,请仔细确认</p>
              </div>
          </div>
          <div slot="footer" style="border:0;">
              <Button id="api_publish_btn2" type="ghost"  @click="cancelApiModel">取消</Button>
              <Button id="api_publish_btn3" type="primary"  @click="downApiConfirm">下线</Button>
          </div>
    </Modal>
    <!-- 灰度升级弹框 -->
    <Modal v-model="devToProFlag" title="升级">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
              <Icon type="ios-information-circle"></Icon>
              <span>升级</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                <p style="color:red"></p>
                <p >确定将 API: <Button id="api_publish_btn4" size="small" type="primary">{{this.currentApi.httpMethod}}</Button>  {{this.currentApi.interface_URI}}</p>
                <p>从灰度环境升级到生产环境吗？</p>
                <p>该操作将导致生产环境的该API被覆盖，灰度环境的该API被删除，请仔细确认</p>
              </div>
          </div>
          <div slot="footer" style="border:0;">
              <Button id="api_publish_btn5" type="ghost"  @click="cancelApiModel">取消</Button>
              <Button id="api_publish_btn6" type="primary"  @click="upGrade">升级</Button>
          </div>
    </Modal>
    <!-- 发布api 弹框 -->
    <Modal v-model="publishFlag"  title="发布api">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
              <Icon @click="cancelApiModel" type="ios-information-circle"></Icon>
              <span>发布API</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;">
                <i class="ivu-icon ivu-icon-help-circled"></i>
              </div>
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                <p style="color:red"></p>
                <p >您将要发布以下API: <Button id="api_publish_btn7" size="small" type="primary">{{this.currentApi.httpMethod}}</Button>  {{this.currentApi.interface_URI}}</p>
                <p>请选择发布的环境： </p>
                <p>
                    <Radio-group v-model="publishApiEnv" type="button">
                        <Radio value='CANARY'><span>灰度</span></Radio>
                        <Radio value='PRODUCTION'><span>生产</span></Radio>
                    </Radio-group>
                </p>
                
                <label for="" class="reasonType"><span style="color:red;font-size: 12x;">*</span>发布原因：</label>
                <Input type="textarea" v-model="publish_api_cause" style="width:85%;font-size: 12x;margin-top:8px;" placeholder="" placeholder-style="font-size:12px;"></Input>
                <!-- <p style="color:#888;font-size:12px;margin-top:10px;margin-left:50px">最长可输入60字符</p> -->
                <p>该操作将导致灰度环境下该api被覆盖，请仔细确认</p>
              </div>
          </div>
          <div slot="footer" style="border:0;">
              <Button id="api_publish_btn8" type="ghost"  @click="cancelApiModel">取消</Button>
              <Button id="api_publish_btn9" type="primary"  @click="confirmPublish">发布</Button>
          </div>
    </Modal>
    <!-- 复制弹框 -->
    <Modal v-model="copyFlag" title="升级">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
              <Icon type="ios-information-circle"></Icon>
              <span>复制</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                <p style="color:red"></p>
                <p >您正将版本 {{this.currentApi.snapshotVersion}}的API定义，复制到编辑环境</p>
                <p>此操作将覆盖掉当前编辑结果，请您确认</p>
              </div>
          </div>
          <div slot="footer" style="border:0;">
              <Button id="api_publish_btn10" type="ghost"  @click="cancelApiRecordModel">取消</Button>
              <Button id="api_publish_btn11" type="primary"  @click="confirmCopy">确定</Button>
          </div>
    </Modal>
    <!-- api切换版本弹框 -->
    <Modal v-model="checkoutApiFlag"  title="切换api">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
              <Icon type="ios-information-circle"></Icon>
              <span>API版本切换</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;">
                <i class="ivu-icon ivu-icon-help-circled"></i>
              </div>
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                <p style="color:red"></p>
                <p >您将在 {{this.currentApi.env == 'PRODUCTION' ? '生产' : '灰度'}}环境，切换API: <Button id="api_publish_btn12" size="small" type="primary">{{this.currentApi.requestMethod}}</Button> {{this.currentApi.requestPath}}的版本，版本切换后将直接生效，请您确认。</p>
                <p>当前版本：{{this.currentApi.currentSnapshotVersion ? this.currentApi.currentSnapshotVersion : '无'}}</p>
                <p>切换版本：{{this.currentApi.snapshotVersion}}</p>
                <label for="" class="reasonType"><span style="color:red;font-size: 12x;">*</span>原因：</label>
                <Input type="textarea" v-model="checkoutApiCause" style="width:85%;font-size: 12x;margin-top:8px;" placeholder="" placeholder-style="font-size:12px;"></Input>
              </div>
          </div>
          <div slot="footer" style="border:0;">
              <Button id="api_publish_btn13" type="ghost"  @click="cancelApiRecordModel">取消</Button>
              <Button id="api_publish_btn14" type="primary"  @click="confirmCheckoutApi">切换</Button>
          </div>
    </Modal>
    <!-- api快照 -->
    <Modal v-model="apiSnapshotShow" width="800">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;">
                <Icon type="ios-information-circle"></Icon>
                <span>详情</span>   
            </p>
        <apisSnapShot ref="apisSnapShort"></apisSnapShot>
        <div slot="footer">
            <Button id="api_publish_btn15" type="ghost" @click="apiSnapshotShow = false">关闭</Button>
            <!-- <Button type="ghost" style="border:0"></Button> -->
        </div>
    </Modal>
    </div>
</template>

<script>
    import commonSelect from '../../common-components/select-components/selectCommon';
    import api from'../../../api/api'
    import Vue from 'vue';
    import qs from 'qs';
    import loading from '../../my-components/loading/loading';
    import util from '../../../libs/util';

    import modal_opt_record from "../modify_models/modal_change_record";
    import apis_to_create from '../modify_models/apis_to_create'
    import apisSnapShot from '../modify_models/apisSnapShot'
    import apis_to_override from '../modify_models/apis_to_override'
    import spis_to_create from '../modify_models/spis_to_create'
    import spis_to_override from '../modify_models/spis_to_override'
    import models_to_create from '../modify_models/models_to_create'
    import models_to_override from '../modify_models/models_to_override'
    // import
    export default {
        name: 'api_Management_Open',
        components:{
            loading,
            commonSelect,
            apisSnapShot,
            modal_opt_record,
            apis_to_create,
            apis_to_override,
            spis_to_create,
            spis_to_override,
            models_to_create,
            models_to_override,
        },
        data () {
            return {
                // 覆盖api
                checkAllGroupApisToOverrideArr:[],
                checkAllGroupApisToOverride:[],
                indeterminateApisToOverride: true,
                checkAllApisToOverride: false,

                // 覆盖spi
                checkAllGroupSpisToOverrideArr:[],
                checkAllGroupSpisToOverride:[],
                indeterminateSpisToOverride: true,
                checkAllSpisToOverride: false,

                
                // 覆盖model
                checkAllGroupModelsToOverrideArr:[],
                checkAllGroupModelsToOverride:[],
                indeterminateModelsToOverride: true,
                checkAllModelsToOverride: false,
                
                apisToOverrideArr:[],
                spisToOverrideArr:[],
                modelsToOverrideArr:[], 
                
                result:{},
                data_Template:{},
                collapse_label: ['0'],
                testMultiSelectedData:[],

                // 分析
                apisToCreateList:[],
                apisToOverrideList:[],

                spisToCreateList:[],
                spisToOverrideList:[],
                modelsToCreateList:[],
                modelsToOverrideList:[],

                apisToIgnore:[],
                spisToIgnore:[],
                unusedSpis:[],
                modelsToIgnore:[],
                unusedModels:[],
                // 导入api
                form_data_spi:{
                    data_select_apiGroup_import:"",
                    apiGroup:"",
                    fileContent:"",
                    spiType:""
                },
                // 导出api
                form_data_spi_export:{
                    data_select_apiGroup_import:"",
                    apiGroup:"",
                    fileContent:"",
                    spiType:""
                },
                // 导出结果
                analysis_result_show:false,
                data_spi_type:[{value:"YAML"},{value:"JSON"}],
                // 导入api
                import_api_show:false,
                export_spi_show:false,
                // api分组数据绑定
                data_apiGroup: '',
                // api类型数据绑定
                data_apiType: '',
                // 倒入功能ip动态变量
                importIP : localStorage.remoteIP+'/rest/api/manage/import',
                /**
                 * 注册弹窗数据
                 */
                name_upload:'apiFile',
                // 端点url数据绑定
                endpoint_Url : '',
                // 显示端点url部分
                show_pointUrl : false,
                // 端点类名数据绑定
                endpoint_Name: '',
                // 是否幂等数据绑定
                idempotent: '否',
                // 端点url自定义
                pointUrl_user_defined: false,
                // 适配类型下拉框数据绑定
                data_select_type: 'TRANSFORM',
                // 适配类型下拉框数据
                data_type_List: [
                    // {
                    //     value: 'TRANSFORM',
                    //     label: '转换'
                    // },
                    {
                        value: 'MAPPING',
                        label: '映射'
                    },
                    {
                        value: 'PASSTHROUGH',
                        label: '透传'
                    }
                ],
                // 端点协议下拉框数据绑定
                data_select_EndpointProtocol: 'HESSIAN',
                // 端点协议下拉框数据
                data_EndpointProtocol_List: [
                    {
                        value: 'HESSIAN',
                        label: 'hessian'
                    }
                ],
                // 端点方法下拉框数据绑定
                data_select_EndpointMethod: '',
                // 端点方法下拉框数据
                data_EndpointMethod_List: [],
                // 当前端点名称内容
                current_content : '',
                // 当前端点url内容
                current_content_url : '',
                // url校验结果
                pass_url : true,
                /**
                 * 主界面部分数据
                 */
                // 上传header绑定
                header_upload: {
                    Authorization: 'Bearer ' + localStorage.accesstoken
                },
                // api列表加载动画数据绑定
                show_loading_apiList : true,
                // switch点击行数
                switch_index: 0,
                // 注册web api对话框显示
                modal_Show_register: false,
                // APIuri数据绑定
                data_interface_uri : '',
                httpMethod:"",
                // API名称
                data_interface_name : '',
                interface_Title:"",
                interface_URI_httpMethod:"",
                // 状态下拉框数据绑定
                data_select_status: '',
                // 状态下拉框选项数据
                data_status_List: [],
                // api分组下拉框数据绑定
                data_select_apiGroup: '',
                // api分组下拉框数据
                data_apiGroup_List: [],
                // 安全需求下拉框数据绑定
                data_safety_request: '',
                // api类型选择数据绑定
                data_select_apiType: '',
                // api类型
                data_apiType_List:[],
                // api操作类型
                apiOpTypes: [],
                // 发布环境
                apiEnvList: [],
                // api 下线开关
                downApiFlag: false,
                // 升级kaiguan
                devToProFlag: false,
                // 发布记录开关
                publishRecordFlag: false,
                // 发布api 开关
                publishFlag: false,
                // api快照开关
                apiSnapshotShow: false,
                // 复制弹框
                copyFlag: false,
                // 切换版本api
                checkoutApiFlag: false,
                // 切换api原因
                checkoutApiCause: '',
                // 当前api 相关参数
                currentApi: {},
                // 当前api 索引
                currentApiIndex: '',
                // 下线原因
                offline_api_cause: '',
                // 发布原因
                publish_api_cause: '',
                // api环境
                apiEnv: '',
                // 发布环境
                publishApiEnv: 'CANARY',
                /* == 发布记录查询相关== */
                // 发布记录查询字段
                recordSearch:{
                    // api 分组
                    apiGroup: '', 
                    // 路径
                    requestPath: '',
                    // 方法
                    requestMethod: '',
                    // 环境
                    env: '',
                    // 操作类型
                    opType: '',
                    // 时间
                    startDate: '',
                    endDate: ''
                },
                //发布记录分页
                recordPageTotal: 10,
                recordPageNo: 1,
                // 发布记录 请求接口参数暂存
                searchRecordParam: {},
                // 请求方法
                requestMethodList:[
                    {label:"GET",value:"GET"},
                    {label:"POST",value:"POST"},
                ],
                // apiAPI列表列属性
                columns_ApiInterfaceList: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    {
                        title: 'API名称',
                        key: 'name',
                        width: 110,
                    },
                     {
                        title: 'API标题',
                        key: 'interface_Title',
                        width: 105,

                    },
                    
                    {
                        title: 'API分组',
                        key: 'apiGroup',
                        type:'html',
                        width: 90,

                        align: 'center'
                        // render: (h,params) => {
                        //     return h ('div',params.row.APIgroupTitle+'('+params.row.APIgroupCode+')')
                        // }
                    },
                    {
                        title: '方法/请求路径',
                        render: (h, params) => {
                            var color = 'blue';
                            return h('div',[
                                h('Tag',{
                                    style:{
                                        align: 'center'
                                    },
                                    props:{
                                        color: color
                                    }
                                },params.row.httpMethod),
                                h('p',params.row.interface_URI)]);
                        }
                    },
                    {
                        title: 'API类型',
                        width:90,
                        key: 'APItype',
                        align: 'center',
                    },
                    {
                        title: '状态',
                        key: 'statusDesc',
                        width:100,
                        align: 'center',
                        render: (h, params) => {
                            var color = 'red';
                            var statusDesc = ""
                            if (params.row.statusDesc == 'ENABLED') {
                                color = 'green';
                                statusDesc = "已启用"
                            }
                            if (params.row.statusDesc == 'DISABLED') {
                                color = 'grey';
                                statusDesc = "已禁用"
                            }
                            return h('div',[
                                h('Tag',{
                                    style:{
                                        align: 'center'
                                    },
                                    props:{
                                        color: color
                                    }
                                },statusDesc)
                            ]);
                        }
                    },
                    {
                        title: '发布状态',
                        key: 'statusDesc',
                        width: 252,
                        align: 'center',
                        class: 'padding-none',
                        render: (h, params) => {
                            let htmlEle = [];
                            return h('div', [
                                h('Button', {
                                    domProps:{
                                        id: "api_publish_edit_btn1"
                                    },
                                    class:`btndesAPI margin-right-5 padding-none ${params.row.deployStatus ? (params.row.deployStatus.CANARY ? '' : 'hide') : 'hide'}`,
                                    props: {
                                        type: 'warning',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            
                                        }
                                    }
                                }, `${params.row.deployStatus ? (params.row.deployStatus.CANARY ? '灰度' : '') : ''}`),
                                h('Tooltip', {
                                    class:`description ${params.row.deployStatus ? (params.row.deployStatus.CANARY ? '' : 'hide') : 'hide'}`,
                                    props: {
                                        placement: "top",
                                        content: `${params.row.deployStatus ? (params.row.deployStatus.CANARY ? params.row.deployStatus.CANARY.description : '') : ''}`
                                    },
                                    on: {
                                        click: () => {
                                        
                                        }
                                    }
                                }, `${params.row.deployStatus ? (params.row.deployStatus.CANARY ? params.row.deployStatus.CANARY.snapshotVersion : '') : ''}`),

                                h('Button', {
                                    domProps:{
                                        id: "api_publish_edit_btn2"
                                    },
                                    class:`btndesAPI padding-none ${params.row.deployStatus ? (params.row.deployStatus.CANARY ? '' : 'hide') : 'hide'}`,
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api/manage/offline'}
                                    }],
                                    props: {
                                        type: 'error',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.currentApi = params.row;
                                            this.apiEnv = 'CANARY'
                                            // 赋值当前索引
                                            this.currentApiIndex = params.index 
                                            this.downApiConfirm()
                                        }
                                    }
                                }, `${params.row.deployStatus ? (params.row.deployStatus.CANARY ? '下线' : '') : '' }`),
                                // 升级按钮
                                
                                h('Button', {
                                    domProps:{
                                        id: "api_publish_edit_btn3"
                                    },
                                    class: `padding-none up-grade ${params.row.deployStatus ? (params.row.deployStatus.CANARY ? '' : 'hide') : 'hide'}`,
                                    props: {
                                        type: 'primary',
                                        size: 'small',
                                        icon: 'arrow-down-a'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api/manage/upgrade-production'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.currentApi = params.row;
                                            this.devToProFlag = true;
                                            // 赋值当前索引
                                            this.currentApiIndex = params.index 
                                        }
                                    }
                                }, ''),

                                h('Button', {
                                    domProps:{
                                        id: "api_publish_edit_btn4"
                                    },
                                    class: `padding-none ${params.row.deployStatus ? (params.row.deployStatus.PRODUCTION ? '' : 'hide') : 'hide'}`,
                                    props: {
                                        type: 'primary',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                        }
                                    }
                                },`${params.row.deployStatus ? (params.row.deployStatus.PRODUCTION ? '生产' : '') : ''}`),

                                h('Tooltip', {
                                    class: `description ${params.row.deployStatus ? (params.row.deployStatus.PRODUCTION ? '' : 'hide') : 'hide'}`,
                                    props: {
                                        placement: "top",
                                        content: `${params.row.deployStatus ? (params.row.deployStatus.PRODUCTION ? params.row.deployStatus.PRODUCTION.description : '') : ''}`
                                    },
                                    on: {
                                        click: () => {
                                        }
                                    }
                                }, `${params.row.deployStatus ? (params.row.deployStatus.PRODUCTION ? params.row.deployStatus.PRODUCTION.snapshotVersion : '') : ''}`),
                                
                                h('Button', {
                                    domProps:{
                                        id: "api_publish_edit_btn5"
                                    },
                                    class:`btndesAPI padding-none  ${params.row.deployStatus ? (params.row.deployStatus.PRODUCTION ? '' : 'hide') : 'hide'}`,
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/api/manage/offline'}
                                    }],
                                    props: {
                                        type: 'error',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.currentApi = params.row;
                                            this.apiEnv = 'PRODUCTION'
                                            // 赋值当前索引
                                            this.currentApiIndex = params.index 
                                            this.downApiFlag = true
                                        }
                                    }
                                }, `${params.row.deployStatus ? (params.row.deployStatus.PRODUCTION ? '下线' : '') : ''}`),
                            ]);
                            
                        }
                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '创建时间'),
                                h('p', '最后更新时间')
                            ]);
                        },
                        key: 'Date',
                        type: 'html',
                        align: 'center',

                        width:160,
                        // render: (h,params) => {
                        //     return h('div',[('p',params.row.createTime),
                        //         ('p',params.row.lastModifyTime)])
                        // }
                    },
                    {
                        title: '操作',
                        key: 'operations',
                        align: 'center',
                        // width: 90,
                        render: (h, params) => {
                            if(params.row.operations !== undefined){
                                let buttonStatus = ''
                                if(params.row.operations === false){
                                    buttonStatus = '启用';
                                    return h('div', [
                                        h('Button', {
                                            domProps:{
                                                id: "api_publish_edit_btn6"
                                            },
                                            class:"btnModifyAPI",
                                            props: {
                                                type: 'text',
                                                size: 'small',
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/update'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.interface_modify(params.row);
                                                }
                                            }
                                        }, '修改'),
                                        h('Button', {
                                            domProps:{
                                                id: "api_publish_edit_btn7"
                                            },
                                            class:"btndesAPI",
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/detail'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.interface_des(params.row.APIid);
                                                }
                                            }
                                        }, '详情'),
                                        // h('Button', {
                                        //     domProps:{
                                        // id: "api_publish_edit_btn8"
                                        // },
                                        //     props: {
                                        //         type: 'text',
                                        //         size: 'small'
                                        //     },
                                        //     on: {
                                        //         click: () => {
                                        //             this.interface_submit(params.row.APIid);
                                        //         }
                                        //     }
                                        // }, '提交审核'),
                                        // h('Button', {
                                        //         domProps:{
                                        //     id: "api_publish_edit_btn9"
                                        // },
                                        //     props: {
                                        //         type: 'text',
                                        //         size: 'small'
                                        //     },
                                        //     on: {
                                        //         click: () => {
                                        //             this.interface_doc(params.row.APIid);
                                        //         }
                                        //     }
                                        // }, '预览'),

                                        h('Button', {
                                            domProps:{
                                                id: "api_publish_edit_btn10"
                                            },
                                            class:"btnEnabledAPI",
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/enable'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.interface_use(params.index,params.row.operations,params.row.APIid);
                                                }
                                            }
                                        }, buttonStatus),
                                        h('Button', {
                                            domProps:{
                                                id: "api_publish_edit_btn11"
                                            },
                                            class:"btndeleteAPI",
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/delete'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.interface_delete(params.index,params.row.operations,params.row.APIid);
                                                }
                                            }
                                        }, '删除'),
                                        h('Button', {
                                            domProps:{
                                                id: "api_publish_edit_btn12"
                                            },
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/change-record'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.toChangeRecode(params.row)
                                                }
                                            }
                                        }, '变更记录'),
                                        // h('Button', {
                                        //         domProps:{
                                        //     id: "api_publish_edit_btn13"
                                        // },
                                        //     props: {
                                        //         type: 'text',
                                        //         size: 'small'
                                        //     },
                                        //     on: {
                                        //         click: () => {
                                        //             this.interface_version(params.row.APIid);
                                        //         }
                                        //     }
                                        // }, '版本记录')
                                    ]);
                                }else {
                                    buttonStatus = '禁用';
                                    return h('div', [
                                        h('Button', {
                                            domProps:{
                                                id: "api_publish_edit_btn14"
                                            },
                                            class:"btnModifyAPI",

                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/update'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.interface_modify(params.row);
                                                }
                                            }
                                        }, '修改'),
                                        h('Button', {
                                            domProps:{
                                                id: "api_publish_edit_btn15"
                                            },
                                            class:"btndesAPI",
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/detail'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.interface_des(params.row.APIid);
                                                }
                                            }
                                        }, '详情'),
                                        h('Button', {
                                            domProps:{
                                                id: "api_publish_edit_btn16"
                                            },
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/deploy'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.currentApi = params.row;
                                                    this.publishFlag = true;
                                                }
                                            }
                                        }, '发布'),
                                        // h('Button', {
                                            // domProps:{
                                            //     id: "api_publish_edit_btn17"
                                            // },
                                        //     props: {
                                        //         type: 'text',
                                        //         size: 'small'
                                        //     },
                                        //     on: {
                                        //         click: () => {
                                        //             this.interface_submit(params.row.APIid);
                                        //         }
                                        //     }
                                        // }, '提交审核'),
                                        // h('Button', {
                                            // domProps:{
                                            //     id: "api_publish_edit_btn18"
                                            // },
                                        //     props: {
                                        //         type: 'text',
                                        //         size: 'small'
                                        //     },
                                        //     on: {
                                        //         click: () => {
                                        //             this.interface_doc(params.row.APIid);
                                        //         }
                                        //     }
                                        // }, '预览'),

                                        h('Button', {
                                            // domProps:{
                                            //     id: "api_publish_edit_btn19"
                                            // },
                                            class:"btnDisabledAPI",
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/disable'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.interface_use(params.index,params.row.operations,params.row.APIid);
                                                }
                                            }
                                        }, buttonStatus),
                                        h('Button', {
                                            // domProps:{
                                            //     id: "api_publish_edit_btn20"
                                            // },
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/change-record'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.toChangeRecode(params.row)
                                                }
                                            }
                                        }, '变更记录'),
                                        h('Button', {
                                            // domProps:{
                                            //     id: "api_publish_edit_btn21"
                                            // },
                                            props: {
                                                type: 'text',
                                                size: 'small'
                                            },
                                            directives: [{
                                                name: 'url',
                                                value: {url: '/rest/api/manage/deploy-record/list'}
                                            }],
                                            on: {
                                                click: () => {
                                                    this.currentApi = params.row;
                                                    this.publishRecordFlag = true;
                                                    // 赋值当前索引
                                                    this.currentApiIndex = params.index 
                                                    this.searchApiPublishRecord()
                                                }
                                            }
                                        }, '发布记录'),
                                        // h('Button', {
                                            // domProps:{
                                            //     id: "api_publish_edit_btn22"
                                            // },
                                        //     props: {
                                        //         type: 'text',
                                        //         size: 'small'
                                        //     },
                                        //     on: {
                                        //         click: () => {
                                        //             this.interface_version(params.row.APIid);
                                        //         }
                                        //     }
                                        // }, '版本记录')
                                    ]);
                                }

                            }else{
                                return h('div', [
                                    h('Button', {
                                        domProps:{
                                            id: "api_publish_edit_btn23"
                                        },
                                        class:"btnModifyAPI",
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api/manage/update'}
                                        }],
                                        on: {
                                            click: () => {
                                                this.interface_modify(params.row);
                                            }
                                        }
                                    }, '修改'),
                                    h('Button', {
                                        domProps:{
                                            id: "api_publish_edit_btn24"
                                        },
                                        class:"btndesAPI",
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api/manage/detail'}
                                        }],
                                        on: {
                                            click: () => {
                                                this.interface_des(params.row.APIid);
                                            }
                                        }
                                    }, '详情'),
                                    h('Button', {
                                        domProps:{
                                            id: "api_publish_edit_btn25"
                                        },
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api/manage/change-record'}
                                        }],
                                        on: {
                                            click: () => {
                                                this.toChangeRecode(params.row)
                                            }
                                        }
                                    }, '变更记录'),
                                    h('Button', {
                                        domProps:{
                                            id: "api_publish_edit_btn26"
                                        },
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api/manage/deploy-record/list'}
                                        }],
                                        on: {
                                            click: () => {
                                                this.currentApi = params.row;
                                                this.publishRecordFlag = true;
                                                // 赋值当前索引
                                                this.currentApiIndex = params.index 
                                            }
                                        }
                                    }, '发布记录'),
                                    // h('Button', {
                                        // domProps:{
                                        //     id: "api_publish_edit_btn27"
                                        // },
                                    //     props: {
                                    //         type: 'text',
                                    //         size: 'small'
                                    //     },
                                    //     on: {
                                    //         click: () => {
                                    //             this.interface_submit(params.row.APIid);
                                    //         }
                                    //     }
                                    // }, '提交审核'),
                                    // h('Button', {
                                        // domProps:{
                                        //     id: "api_publish_edit_btn28"
                                        // },
                                    //     props: {
                                    //         type: 'text',
                                    //         size: 'small'
                                    //     },
                                    //     on: {
                                    //         click: () => {
                                    //             this.interface_doc(params.row.APIid);
                                    //         }
                                    //     }
                                    // }, '预览'),
                                    // h('Button', {
                                        // domProps:{
                                        //     id: "api_publish_edit_btn29"
                                        // },
                                    //     props: {
                                    //         type: 'text',
                                    //         size: 'small'
                                    //     },
                                    //     on: {
                                    //         click: () => {
                                    //             this.interface_version(params.row.APIid);
                                    //         }
                                    //     }
                                    // }, '版本记录')
                                ]);
                            }

                        }
                    }
                ],
                // api发布记录表格
                apiPublishRecordList:[
                    {
                        title: 'API分组',
                        key: 'apiGroup',
                        align: 'center',
                        type: 'html',
                        width: 90,
                    },
                    {
                        title: '方法/请求路径',
                        align: 'center',
                        render: (h, params) => {
                            var color = 'blue';
                            return h('div',[
                                h('Tag',{
                                    style:{
                                        align: 'center'
                                    },
                                    props:{
                                        color: color
                                    }
                                },params.row.requestMethod),
                                h('p',params.row.requestPath)]);
                        }
                    },
                    {
                        title: '发布环境',
                        align: 'center',
                        key: 'env',
                        width: 90,
                        render: (h, params) => {
                            let envText = '',
                                color = '';
                            if (params.row.env == 'PRODUCTION') {
                                color = 'green';
                                envText = '生产'
                            }else{
                                color = 'grey';
                                envText = '灰度'
                            }
                            return h('div',[
                                h('Tag',{
                                    style:{
                                        align: 'center'
                                    },
                                    props:{
                                        color: color
                                    }
                                },envText)
                            ]);
                            
                        }
                    },
                    {
                        title: '快照版本',
                        align: 'center',
                        key: 'snapshotVersion',
                        width: 140,
                    },
                    {
                        title: '操作类型',
                        align: 'center',
                        key: 'opType',
                        width: 90,
                        render: (h, params) => {
                            let opTypeText = '';
                            if (params.row.opType == 'DEPLOY'){
                                opTypeText = '发布'
                            }
                            if (params.row.opType == 'UPGRADE'){
                                opTypeText = '升级'
                            }
                            if (params.row.opType == 'OFFLINE'){
                                opTypeText = '下线'
                            }
                            return h('div',[
                                h('Button',{
                                    style:{
                                        align: 'center'
                                    },
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                },opTypeText)
                            ]);
                            
                        }
                    },
                    {
                        title: '操作人',
                        align: 'center',
                        key: 'operator',
                        width: 100,
                    },
                    {
                        title: '备注',
                        align: 'center',
                        key: 'cause',
                        width: 100,
                    },
                    {
                        title: '操作时间',
                        align: 'center',
                        key: 'createdDate',
                        width: 160,
                    },
                    {
                        title: '操作',
                        key: 'operations',
                        align: 'center',
                        render: (h, params) => {
                            let ifshowOperation = params.row.snapshotVersion ? true :  false;
                            return h('div', [
                                    h('Button', {
                                        domProps:{
                                            id: "api_publish_edit_btn30"
                                        },
                                        class:"btnModifyAPI ",
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api/manage/snapshot/view'}
                                        }],
                                        on: {
                                            click: () => {
                                                this.apiPreview(params.row)
                                            }
                                        }
                                    }, `${ifshowOperation ? '查看' : ''}`),
                                    h('Button', {
                                        domProps:{
                                            id: "api_publish_edit_btn31"
                                        },
                                        class:"btndesAPI",
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api/manage/snapshot/copy'}
                                        }],
                                        on: {
                                            click: () => {
                                                this.copyFlag = true
                                                this.currentApi = params.row            
                                            }
                                        }
                                    },`${ifshowOperation ? '复制' : ''}`),
                                    h('Button', {
                                        domProps:{
                                            id: "api_publish_edit_btn32"
                                        },
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        directives: [{
                                            name: 'url',
                                            value: {url: '/rest/api/manage/deploy-env/upgrade'}
                                        }],
                                        on: {
                                            click: () => {
                                                this.currentApi = params.row
                                                this.checkoutApiFlag = true
                                            }
                                        }
                                    },`${ifshowOperation ? '切换此版本' : ''}`)
                                ]);
                        }
                        
                    },

                ],
                apiPublishRecordTable: [],
                // 表格数据
                data_ApiInterfaceList: []
                ,
                // 表格选中数据
                multiSelectedData: [],
                multiSelectedAPIId:[],
                // 分页单页个数
                pageNo : 1,
                // 分页总数
                pageTotal: 10,
                // 当前查询参数
                current_params : {
                    pageNo : 1,
                    pageSize : 20
                },
                delete_api_cause:"",
                id_delete:"",
                delete_or_normal_modal_show:false,
                show_import_api_loading:false,
                sure_import_api_loading:false,
                value0:"page0",
                innerValue0:"innerPage_0",
                innerValue1:"innerPage_1",
                innerValue2:"innerPage_2",
                innerValue3:"innerPage_3",
                innerValue4:"innerPage_4",
                innerValue5:"innerPage_5",
                sys_params_list:[]
            };
        },
        methods: {
            // 全选覆盖api列表
            
            handleCheckAllApisToOverride () {
                if (this.indeterminateApisToOverride) {
                    this.checkAllApisToOverride = false;
                } else {
                    this.checkAllApisToOverride = !this.checkAllApisToOverride;
                }
                this.indeterminateApisToOverride = false;
                if (this.checkAllApisToOverride) {
                    this.checkAllGroupApisToOverride = this.checkAllGroupApisToOverrideArr
                    this.checkAllApisToOverride = true;
                } else {
                    this.checkAllGroupApisToOverride = [];
                }
            },

                        // 覆盖api列表
            checkAllGroupApisToOverrideChange (data) {
                this.checkAllGroupApisToOverride = data
                if (data.length == this.apisToOverrideList.length) {
                    this.indeterminateApisToOverride = false;
                    this.checkAllApisToOverride = true;
                } else if (data.length > 0) {
                    this.indeterminateApisToOverride = true;
                    this.checkAllApisToOverride = false;
                } else {
                    this.indeterminateApisToOverride = false;
                    this.checkAllApisToOverride = false;
                }
            },
        // 全选覆盖spi列表
            
            handleCheckAllSpisToOverride () {
                if (this.indeterminateSpisToOverride) {
                    this.checkAllSpisToOverride = false;
                } else {
                    this.checkAllSpisToOverride = !this.checkAllSpisToOverride;
                }
                this.indeterminateSpisToOverride = false;
                if (this.checkAllSpisToOverride) {
                    this.checkAllGroupSpisToOverride = this.checkAllGroupSpisToOverrideArr
                    this.checkAllSpisToOverride = true;
                } else {
                    this.checkAllGroupSpisToOverride = [];
                }
            },

                        // 覆盖spi列表
            checkAllGroupSpisToOverrideChange (data) {
                this.checkAllGroupSpisToOverride = data
                if (data.length == this.spisToOverrideList.length) {
                    this.indeterminateSpisToOverride = false;
                    this.checkAllSpisToOverride = true;
                } else if (data.length > 0) {
                    this.indeterminateSpisToOverride = true;
                    this.checkAllSpisToOverride = false;
                } else {
                    this.indeterminateSpisToOverride = false;
                    this.checkAllSpisToOverride = false;
                }
            },

            // 全选覆盖model列表
            
            handleCheckAllModelsToOverride () {
                if (this.indeterminateModelsToOverride) {
                    this.checkAllModelsToOverride = false;
                } else {
                    this.checkAllModelsToOverride = !this.checkAllModelsToOverride;
                }
                this.indeterminateModelsToOverride = false;
                if (this.checkAllModelsToOverride) {
                    this.checkAllGroupModelsToOverride = this.checkAllGroupModelsToOverrideArr;
                    this.checkAllModelsToOverride = true;
                } else {
                    this.checkAllGroupModelsToOverride = [];
                }
            },

                        // 覆盖model列表
            checkAllGroupModelsToOverrideChange (data) {
                this.checkAllGroupModelsToOverride = data
                if (data.length === this.modelsToOverrideList.length) {
                    this.indeterminateModelsToOverride = false;
                    this.checkAllModelsToOverride = true;
                } else if (data.length > 0) {
                    this.indeterminateModelsToOverride = true;
                    this.checkAllModelsToOverride = false;
                } else {
                    this.indeterminateModelsToOverride = false;
                    this.checkAllModelsToOverride = false;
                }
            },
            // 开始日期更新
            update_start_date(val) {
                this.recordSearch.startDate = val;
            },
            // 结束日期更新
            update_end_date(val) {
                this.recordSearch.endDate = val;
            },
            cancelApiModel(type){
                // 发布弹框 
                this.publishFlag = false;
                // 下线弹框
                this.downApiFlag = false;
                // 升级弹框
                this.devToProFlag = false;
                // 清空发布api原因 
                this.publish_api_cause = '';
                this.offline_api_cause = ''
            },
            // 发布记录重置
            resetRecordSearch(){
                // this.recordSearch = {}
                this.$refs.clearRecordEnv.clearSingleSelect();
                this.$refs.clearRecordMethod.clearSingleSelect();
                this.$refs.clearRecordType.clearSingleSelect();
                this.$refs.clearRecordApiGroup.clearSingleSelect();
                this.recordSearch.requestPath = '';
                this.$refs.start.handleClear();
                this.$refs.end.handleClear();
            },
            // 发布记录弹框取消
            cancelApiRecordModel(){
                // 复制弹框取消
                this.copyFlag = false;
                // 切换api 弹框取消
                this.checkoutApiFlag = false;
                // 清空切换api原因
                this.checkoutApiCause = ''
            },
            // 确定下线API
            downApiConfirm(){
                if(this.apiEnv === 'PRODUCTION'){
                    if(!this.offline_api_cause){
                        this.$Message.error('请输入下线原因');
                        return false;
                    }
                }
                let param ={
                    apiId: this.currentApi.APIid,
                    env: this.apiEnv, //环境
                    remark: this.offline_api_cause
                }
                
                api.yop_apiManagement_offline(param).then(
                    (response) => {
                        if(response.status === "success"){
                            this.offline_api_cause = ''
                            this.$ypMsg.notice_success(this,'下线成功');
                            this.downApiFlag = false
                            // 更新当前这一条api 发布状态
                            this.apiStatusUpdate('down')
                        }else{
                            this.downApiFlag = false
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }

                    }
                )
            },
            // 升级api
            upGrade(){
                let param = {
                    apiId: this.currentApi.APIid,
                    snapshotVersion: this.currentApi.deployStatus.CANARY.snapshotVersion
                }
                api.yop_apiManagement_upgrade_production(param).then(
                    (response) => {
                        if(response.status === "success"){
                            this.$ypMsg.notice_success(this,'升级成功');
                            this.devToProFlag = false
                        }else{
                            this.devToProFlag = false
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            
                        }

                    }
                )
            },
            // 发布api
            confirmPublish(){
                if(!this.publish_api_cause){
                    this.$Message.error('请输入发布原因');
                    return false;   
                }
                let param = {
                    apiId: this.currentApi.APIid,
                    version: this.currentApi.apiVersion,
                    env: this.publishApiEnv,
                    remark: this.publish_api_cause
                }
                api.yop_apiManagement_deploy(param).then(
                    (response) => {
                        // 清空原因
                        this.publish_api_cause = '';
                        if(response.status === "success"){
                            this.publishFlag = false
                            this.$ypMsg.notice_success(this,'发布成功');
                            
                        }else{
                            this.publishFlag = false
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }

                    }
                )
            },
            // 单条 api发布状态查询
            apiStatusUpdate(type){
                let param = {
                    apiId: this.currentApi.APIid,
                }
                api.yop_apiManagement_deploy_status_view(param).then(
                    (response) => {
                        if(response.data.status === "success"){
                            this.data_ApiInterfaceList[this.currentApiIndex].deployStatus = response.data.data.result
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }

                    }
                )
            },
            // 复制api
            confirmCopy(){
                let param = {
                    apiId: this.currentApi.APIid,
                    snapshotVersion: this.currentApi.snapshotVersion
                }
                api.yop_apiManagement_snapshot_copy(param).then(
                    (response) => {
                        
                        if(response.status === "success"){
                            this.copyFlag = false
                            this.$ypMsg.notice_success(this,'复制成功');
                        }else{
                            this.copyFlag = false
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }

                    }
                )
            },
            // 切换api版本
            confirmCheckoutApi(){
                if(!this.checkoutApiCause){
                    this.$Message.error('请输入原因');
                    return false;   
                }
                let param = {
                    apiId: this.currentApi.APIid,
                    snapshotVersion: this.currentApi.snapshotVersion,
                    env: this.currentApi.env,
                    remark: this.checkoutApiCause
                }
                api.yop_apiManagement_deployenv_upgrade(param).then(
                    (response) => {
                        // 清空原因
                        this.checkoutApiCause = '';
                        if(response.status === "success"){
                            this.checkoutApiFlag = false
                            this.$ypMsg.notice_success(this,'切换成功');
                        }else{
                            this.checkoutApiFlag = false
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }

                    }
                )
            },
            // 查看快照功能
            apiPreview(row){
                var operateDate = row.createdDate
                let params = {
                    apiId: row.APIid,
                    snapshotVersion: row.snapshotVersion
                }
                var self = this                
                api.yop_modalManagement_snapshot_view(params).then(
                    (response) => {
                        var response = response.data
                        if(response.status == "success"){
                            var result = response.data.content
                            self.$refs.apisSnapShort.setResult(result,"changeRecord",row);
                            self.apiSnapshotShow = true;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.message
                            });
                        }
                    }
                )
            },
            // api 发布记录
            // 搜索api记录
            searchApiPublishRecord() {
                this.show_loading_apiList = true ;
                let paramsTemp = {
                    apiGroup : this.recordSearch.apiGroup,
                    requestPath: this.recordSearch.requestMethod,
                    env: this.recordSearch.env,
                    opType: this.recordSearch.opType,
                    startDate: this.recordSearch.startDate,
                    endDate: this.recordSearch.endDate,
                    _pageNo : 1
                }
                this.searchRecordParam = paramsTemp
                api.yop_apiManagement_deploy_record_list(paramsTemp).then(
                    (response) => {
                        this.show_loading_apiList = false ;
                        this.apiRecordTableData(response.data.data.page.items);
                        this.pageNo = response.data.data.page.pageNo;
                        if(response.data.data.page.items){
                            if(response.data.data.page.items.length < 10){
                                this.recordPageTotal=response.data.data.page.items.length;
                            }else{
                                this.recordPageTotal=NaN;
                            }
                        }else{
                            this.recordPageTotal=NaN;
                        }
                    }
                );
            },
            // 处理api 发布记录
            apiRecordTableData(items){
                let dataTemp = [];
                for (var i in items){
                    dataTemp.push({
                        APIid : items[i].apiId,
                        apiGroup: this.apiGroup_title_handler(items[i].apiGroupName,items[i].apiGroup)+'<br/>('+items[i].apiGroup+')',
                        version : items[i].version,
                        requestPath: items[i].requestPath,
                        requestMethod:items[i].requestMethod,
                        env:items[i].env,
                        currentSnapshotVersion: items[i].currentSnapshotVersion ? items[i].currentSnapshotVersion : '无',
                        snapshotVersion: items[i].snapshotVersion ? items[i].currentSnapshotVersion : '无',
                        opType:items[i].opType,
                        operator: items[i].operator,
                        cause: items[i].cause,
                        createdDate: items[i].createdDate,
                        
                    });
                }
                this.apiPublishRecordTable = dataTemp
            },
            // 发布记录分页
            pageRecordRefresh(val){
                let paramsTemp = {
                    apiGroup : this.recordSearch.apiGroup,
                    requestPath: this.recordSearch.requestPath,
                    requestMethod: this.recordSearch.requestMethod,
                    env: this.recordSearch.env,
                    opType: this.recordSearch.opType,
                    startDate: this.recordSearch.startDate,
                    endDate: this.recordSearch.endDate,
                    _pageNo : val
                }
                this.searchRecordParam._pageNo = val;
                api.yop_apiManagement_deploy_record_list(paramsTemp).then(
                    (response) => {
                        this.apiRecordTableData(response.data.data.page.items);
                        this.recordPageNo = response.data.data.page.pageNo;
                        if(response.data.data.page.items){
                            if(response.data.data.page.items.length < 10){
                                this.recordPageTotal=response.data.data.page.items.length;
                            }else{
                                this.recordPageTotal=NaN;
                            }
                        }else{
                            this.recordPageTotal=NaN;
                        }
                    }
                );
            },
            // 导入
            sure_import(){
                this.sure_import_api_loading = true
                var apisToOverrideArr = []
                for(var i = 0 ; i < this.checkAllGroupApisToOverride.length ; i ++){
                    var obj = {}
                    var checkAllGroupApisToOverrideI = this.checkAllGroupApisToOverride[i]
                    obj["path"] = checkAllGroupApisToOverrideI.split(" ")[0]
                    obj["httpMethod"] = checkAllGroupApisToOverrideI.split(" ")[1]
                    apisToOverrideArr.push(obj)
                }
                var param = {
                    // apiGroup:this.form_data_spi.data_select_apiGroup_import,
                    requestId:this.requestId,
                    apisToOverride:apisToOverrideArr,
                    spisToOverride:this.checkAllGroupSpisToOverride,
                    modelsToOverride:this.checkAllGroupModelsToOverride
                }
                var createLength = 0
                if(!this.apisToCreateList){
                    createLength = 0
                }else{
                    createLength = this.apisToCreateList.length
                }
                if(apisToOverrideArr.length == 0 && createLength == 0){
                        this.$ypMsg.notice_warning(this, "没有导入内容，请重新确认");
                        this.sure_import_api_loading = false
                        return false
                    }
                api.yop_apiManagement_api_import(param).then(
                    (response) => {
                        if(response.status === "success"){
                                var total = response.data.result.total
                                var success = response.data.result.success || "0"
                                var failed = response.data.result.failed || "0"
                                var importFailed = response.data.result.failedDetails;
                                var successHtml = '',
                                    failedHtml = '';
                                    successHtml = `<p>导入数量：${total} </p><p>成功数量：${success} </p><p>失败数量：${failed} </p>`
                                successHtml = '<p>导入结果</p>'+successHtml
                                if(importFailed){
                                    for(var l = 0 ; l< importFailed.length ; l ++){
                                        failedHtml += `<p>原因：${importFailed[l].reason} </p><p>路径：${importFailed[l].key.path} </p><p>HTTP方法：${importFailed[l].key.httpMethod}</p>`
                                    }
                                    failedHtml = '<p>导入失败</p'+failedHtml
                                }
                                if(!importFailed){
                                    this.$Modal.success({
                                        title: "详情",
                                        content: successHtml
                                    });
                                }
                                if(importFailed){
                                    this.$Modal.warning({
                                        title: "详情",
                                        content: successHtml+failedHtml
                                    });
                                }
                                this.analysis_result_show = false
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.message
                            });
                        }
                        this.sure_import_api_loading = false

                    }
                )
            },
            hide_import(){
                this.analysis_result_show = false
            },
            clear(){
                this.$refs.upload.clearFiles();//清除上次上传记录
            },
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
            // 修改按钮调用方法
            interface_modify (val) {
                localStorage.apiInfo = 'modify';
                localStorage.apiId = val.APIid;
                localStorage.interface_URI = val.interface_URI;
                localStorage.APIgroupCode = val.APIgroupCode;
                this.$router.replace({
                    name: 'api_basics_edit'
                });
            },
            // 描述按钮调用方法
            interface_des (val) {
                localStorage.apiInfo='description';
                localStorage.apiId = val;
                this.$router.replace({
                    name: 'api_basics_desc'
                });
            },
            // 文档预览按钮调用方法
            interface_doc (val) {
                console.log(val);
                // alert('预览');
            },
            // 提交审核按钮调用方法
            interface_submit (val) {
                console.log(val);
                // alert('提交审核');
            },
            // 删除按钮调用方法
            interface_delete (index,operations,id) {
                this.delete_or_normal_modal_show = true
                this.delete_api_cause = "";
                this.index_delete = index
                this.operations_delete = operations
                this.id_delete = id
            },
            toChangeRecode(row){
                var apiGroup=row.APIgroupCode
                var interface_URI=row.interface_URI
                this.$refs.modal_opt_record.reset_search();
                if(apiGroup){
                this.$refs.modal_opt_record.set_appId(apiGroup,interface_URI);
                }
                this.$refs.modal_opt_record.modal_preview();
                this.$refs.modal_opt_record.search();
            },
           
           
            hide_model(){
                this.delete_or_normal_modal_show = false
            },
            // 启用按钮调用方法
            interface_use (index,operations,id) {
                if(operations){
                    this.$Modal.confirm({
                        title: '提示',
                        content: '禁用API将导致API无法调用，您还要继续吗？',
                        'ok-text':'禁用',
                        onOk: () =>{
                            // this.data_ApiInterfaceList[index].operations = !operations;
                            this.forbidden_ok(index,operations,id);
                        }
                        // onCancel: this.forbidden_cancel(index,operations)
                    });
                }else{
                    this.$Modal.confirm({
                        title: '提示',
                        'ok-text':'启用',
                        content: '您将启用API，请确认是否维护好API信息，您还要继续吗',
                        onOk: () =>{
                            this.enabled_ok(index,operations,id);
                            // this.data_ApiInterfaceList[index].operations = !operations;
                        },
                        // onCancel: this.forbidden_cancel(index,operations)
                    });
                }
                // this.data_ApiInterfaceList[index].operations = !operations;
                // alert('启用');
            },
            // 启用提示框ok
            enabled_ok (index,operations,id) {
                api.yop_api_manage_enable({
                    apiId : id
                }).then(
                    (response) =>{
                        if(response.status === 'success'){
                            if(localStorage.needAudit === 'true'){
                                this.$ypMsg.notice_info(this,response.message);
                            }else{
                                this.$ypMsg.notice_success(this,'所选API启用成功','启用成功');
                            }
                            this.data_ApiInterfaceList[index].statusDesc = '已启用'
                            this.data_ApiInterfaceList[index].operations = !operations;
                            this.pageRefresh();

                        }else{
                            this.$ypMsg.notice_error(this,'启用失败',response.message,response.solution);
                        }
                    }
                )
                // this.data_ApiInterfaceList[index].operations = !operations;
            },
            // 禁用提示框ok
            forbidden_ok (index,operations,id) {
                api.yop_api_manage_disable({
                    apiId : id
                }).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'所选API禁用成功','禁用成功');
                            this.data_ApiInterfaceList[index].statusDesc = '已禁用';
                            this.data_ApiInterfaceList[index].operations = !operations;
                            this.pageRefresh();

                        }else{
                            this.$ypMsg.notice_error(this,'禁用失败',response.message,response.solution);
                        }
                    }
                )
                // this.data_ApiInterfaceList[index].operations = !operations;

            },
            // 删除提示框ok
            delete_ok (index,operations,id) {
                // 如果此按钮需要审核则执行
                if(util.checkAudit('/rest/api/manage/delete')){
                    this.delete_or_normal_modal_show = false;
                }
                api.yop_api_manage_delete({
                    apiId : this.id_delete,
                    cause:this.delete_api_cause
                }).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'所选API删除成功','删除成功');
                        }else{
                            this.$ypMsg.notice_error(this,'删除失败',response.message,response.solution);
                        }
                        this.pageRefresh();
                    this.delete_or_normal_modal_show = false
                    }
                )
                // this.data_ApiInterfaceList[index].operations = !operations;

            },
            // 版本记录按钮调用方法
            interface_version () {
                // console.log(val);
                alert('版本记录');
            },
            // 界面初始化函数
            init () {
                
                this.show_loading_apiList = true;
            //    请求api 发布记录
            this.searchApiPublishRecord()
                // 初始化页面表格数据
                // api.yop_api_manage_list({
                //     _pageNo : 1,
                //     pageSize : 20
                // }).then(
                //     (response) => {
                //         if(response.data.status === 'success'){
                //             this.tabledataGet(response.data.data.page.items);
                //             this.pageNo = response.data.data.page.pageNo;
                //             if(response.data.data.page.items){
                //                     this.pageTotal=NaN;
                //             }else{
                //                 this.pageTotal=NaN;
                //             }
                //             this.show_loading_apiList = false;
                //         }else{
                //             this.$Modal.error({
                //                 title: '错误',
                //                 content: response.data.message
                //             });
                //         }
                //     }
                // );
                // api状态列表
                api.yop_apiManagement_apiCommonStatusList().then(
                    (response)=>{
                        let resultTemp = response.data.data.statusList
                        this.data_status_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].value,
                                label: resultTemp[i].desc
                            })
                        }
                        // this.data_status_List = dataTemp;
                        this.data_status_List = [{
                                value: "ENABLED",
                                label: "已启用"
                            },{
                                value: "DISABLED",
                                label: "已禁用"
                            }]

                    }
                );
                // api类型列表
                api.yop_apiManagement_apiCommonTypeList().then(
                    (response)=>{
                        let resultTemp = response.data.data.result
                        this.data_apiType_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].value,
                                label: resultTemp[i].desc
                            })
                        }
                        // this.data_apiType_List = dataTemp;
                        this.data_apiType_List = [{
                                value: "COMMON",
                                label: "普通"
                            },{
                                value: "FILE_UPLOAD",
                                label: "文件上传"
                            },{
                                value: "FILE_DOWNLOAD",
                                label: "文件下载"
                            }]
                    }
                );
                // api分组列表
                api.yop_dashboard_apis_list().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.data_apiGroup_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].apiGroupCode,
                                label: resultTemp[i].apiGroupCode+'('+resultTemp[i].apiGroupName+')',
                                name: resultTemp[i].apiGroupName
                            })
                        }
                        this.data_apiGroup_List = dataTemp;
                    }
                );
                // api发布操作类型枚举
                api.yop_apiManagement_deploy_optypes().then(
                    (response) => {
                        this.apiOpTypes = []
                        response.data.data.result.forEach(item => {
                            this.apiOpTypes.push({
                                label: item.desc,
                                value: item.value
                            })
                        });
                    }
                );
                // api环境枚举
                api.yop_apiManagement_deploy_env().then(
                    (response) => {
                        this.apiEnvList = [];
                        response.data.data.result.forEach(item => {
                            this.apiEnvList.push({
                                label: item.desc,
                                value: item.value
                            })
                        });
                    }
                );
            },
            // 页面刷新
            pageRefresh (val) {
                this.show_loading_apiList = true ;
                this.current_params._pageNo = val;
                api.yop_api_manage_list(this.current_params).then(
                    (response) => {
                        this.tabledataGet(response.data.data.page.items);
                        this.pageNo = response.data.data.page.pageNo;
                        if(response.data.data.page.items){
                                    this.pageTotal=NaN;
                            }else{
                                this.pageTotal=NaN;
                            }
                        this.show_loading_apiList = false ;
                    }
                )
            },
            // 表格赋值
            tabledataGet (items) {
                this.data_ApiInterfaceList = [];
                let dataTemp = [];
                for (var i in items){
                    let DateTemp = items[i].createdDateTime + '<br/>' + items[i].lastModifiedDateTime 
                    let interface_URI_httpMethod = items[i].path + '<br/>' + items[i].httpMethod
                   
                    let operationsTemp = false;
                    if(items[i].status === 'ENABLED'){
                        operationsTemp = true;
                    }
                    if(items[i].status === 'DISABLED'){
                        operationsTemp = false
                    }

                    let apiType = ""
                    if(items[i].apiType === 'COMMON'){
                        apiType = "普通"
                    }
                    if(items[i].apiType === 'FILE_UPLOAD'){
                        apiType = "文件上传";
                    }
                    if(items[i].apiType === 'FILE_DOWNLOAD'){
                        apiType = "文件下载"
                    }
                      
                    dataTemp.push({
                        APIid : items[i].apiId,
                        version : items[i].version,
                        name: items[i].name,
                        interface_URI:items[i].path,
                        interface_Title:items[i].title,
                        APItype: apiType,
                        httpMethod:items[i].httpMethod,
                        APItypeDesc: items[i].description,
                        APIgroupTitle: items[i].apiGroupName,
                        // interface_Tag: securityTemp,
                        Date: DateTemp,
                        interface_URI_httpMethod:interface_URI_httpMethod,
                        apiGroup: this.apiGroup_title_handler(items[i].apiGroupName,items[i].apiGroup)+'<br/>('+items[i].apiGroup+')',
                        APIgroupCode:items[i].apiGroup,
                        status: items[i].status,
                        statusDesc: items[i].status,
                        apiSecurity: items[i].apiSecurity,
                        groupSecurity: items[i].groupSecurity,
                        createTime: items[i].createdDateTime,
                        lastModifyTime: items[i].lastModifiedDateTime,
                        operations : operationsTemp,
                        deployStatus: items[i].deployStatus
                    });
                    if(items[i].status === 'OFFLINE'){
                        delete dataTemp[i]['operations'];
                    }
                }
                this.data_ApiInterfaceList = dataTemp;
            },
            
            // api分组标题处理
            apiGroup_title_handler (title,code) {
                if(title && title !== ''){
                    return title;
                }else{
                    return this.apiGroup_title_transfer(code);
                }
            },
            // api分组名称转义
            apiGroup_title_transfer (code) {
                for(var i in this.data_apiGroup_List){
                    if(this.data_apiGroup_List[i].value === code){
                        return  this.data_apiGroup_List[i].name;
                    }
                }
                return '';
            },
            
            // 空处理函数
            ParamHandle (Param){
                if(Param || (Param === 0)){
                    return Param;
                }else{
                    return '';
                }
            },
            // 表格内容改动时
            handleselection(value){
                this.multiSelectedData = [];
                this.multiSelectedAPIId = []
                this.testMultiSelectedData = [];

                for(var j =  0 ;j < value.length ; j ++){
                    var valueJ = value[j]
                    for(var i in valueJ){
                        if(i == "APIgroupCode"){
                            this.multiSelectedData.push(valueJ[i]);
                            this.testMultiSelectedData.push(valueJ[i]);
                        }
                        if(i == "APIid"){
                            this.multiSelectedAPIId.push(valueJ[i]);
                        }
                    }
                }
            },
            // 查询apiAPI函数
            search_Interface () {
                let paramsTemp = {
                    name: this.data_interface_name.trim(),
                    path: this.data_interface_uri.trim(),
                    apiType: this.data_select_apiType,
                    apiGroup : this.data_select_apiGroup,
                    status: this.data_select_status,
                    // securityReq: this.data_safety_request,
                    _pageNo : 1,
                    pageSize: 20
                }
                if(this.data_interface_name === ''){
                    paramsTemp['apiTitle'];
                }
                if(this.data_interface_uri === ''){
                    delete paramsTemp['apiUri'];
                }
                if(this.data_select_apiType === ''){
                    delete paramsTemp['apiType'];
                }
                if(this.data_select_apiGroup === ''){
                    delete paramsTemp['apiGroupCode'];
                }
                if(this.data_select_status === ''){
                    delete paramsTemp['status'];
                }
                if(this.data_safety_request === ''){
                    delete paramsTemp['securityReq'];
                }
                this.current_params = paramsTemp;
                api.yop_api_manage_list(paramsTemp).then(
                    (response) => {
                        this.tabledataGet(response.data.data.page.items);
                        this.pageNo = response.data.data.page.pageNo;
                        if(response.data.data.page.items){
                                    this.pageTotal=NaN;
                            }else{
                                this.pageTotal=NaN;
                            }
                    }
                );
            },
            // 重置函数
            reset_search () {
                this.$refs.select_apiM_0.clearSingleSelect();
                this.data_interface_name = '';
                this.data_interface_uri = '';
                this.data_select_apiType = '';
                this.data_select_status = '';
                this.data_safety_request = '';
                this.current_status ={
                    pageNo : 1,
                    pageSize: 20
                };
            },
            // 注册web api函数
            webApi_Register () {
                localStorage.apiInfo='create_model';
                this.$router.push({
                    name: 'api_basics'
                });
            },
            sure_import_api(val){
                console.log(1)
                this.show_import_api_loading = true
                this.$refs[val].validate((valid) => {
                    if (valid) {
                            this.upload()
                            this.import_api_show = false;
                        } else {
                        this.$Message.error('请检查');
                    }
                this.show_import_api_loading = false

                })
            },
            upload () { // 上传文件
                this.value0 = "page0"
                this.innerValue0="innerPage_0"
                this.innerValue1="innerPage_1"
                this.innerValue2="innerPage_2"
                this.innerValue3="innerPage_3"
                this.innerValue4="innerPage_4"
                this.innerValue5="innerPage_5"
                this.clear();
                let formData = new FormData();
                formData.append('dataFormat', this.form_data_spi.spiType);
                formData.append('data', this.form_data_spi.fileContent);
                // formData.append('apiGroup', this.form_data_spi.data_select_apiGroup_import);
                // var param = {
                //     dataFormat:this.form_data_spi.spiType,
                //     content:this.form_data_spi.fileContent,
                //     apiGroup:this.form_data_spi.data_select_apiGroup_import,
                // }
                // console.log("fileContent",this.form_data_spi.fileContent)
                // TODO
                api.yop_apiManagement_api_import_analysis(formData).then(
                    (response) => {
                            if(response.status == "success"){//todo
                                this.$ypMsg.notice_info(this,'文件上传成功');
                                this.result = response.data.result;

                                this.requestId = this.result.requestId
                                this.apisToCreateList = this.result.apisToCreate 
                                this.apisToOverrideList = this.result.apisToOverride
                                
                                this.spisToCreateList = this.result.spisToCreate 
                                this.spisToOverrideList = this.result.spisToOverride
                                this.modelsToCreateList = this.result.modelsToCreate
                                this.modelsToOverrideList = this.result.modelsToOverride

                                this.apisToIgnore = this.result.apisToIgnore
                                this.spisToIgnore = this.result.spisToIgnore
                                this.unusedSpis = this.result.unusedSpis
                                this.modelsToIgnore = this.result.modelsToIgnore
                                this.unusedModels = this.result.unusedModels


                                if(this.result.apisToOverride){
                                    for(var h = 0; h <this.result.apisToOverride.length ; h ++){
                                        this.apisToOverrideArr.push(this.result.apisToOverride[h]["api"].basic.name);
                                        this.checkAllGroupApisToOverrideArr.push(this.result.apisToOverride[h]["api"].request.path+' '+this.result.apisToOverride[h]["api"].request.httpMethod);
                                    }
                                }
                                if(this.result.spisToOverride){
                                    for(var m = 0; m <this.result.spisToOverride.length ; m ++){
                                        this.spisToOverrideArr.push(this.result.spisToOverride[m]["spi"].basic.name);
                                        this.checkAllGroupSpisToOverrideArr.push(this.result.spisToOverride[m]["spi"].basic.name);
                                    }
                                }
                                if(this.result.modelsToOverride){
                                    for(var n = 0; n <this.result.modelsToOverride.length ; n ++){
                                        this.modelsToOverrideArr.push(this.result.modelsToOverride[n]["model"].name);
                                        this.checkAllGroupModelsToOverrideArr.push(this.result.modelsToOverride[n]["model"].id);
                                    }
                                }
                                var self = this
                                setTimeout(function(){
                                    if(self.spisToCreateList){
                                        for(var a = 0 ;a < self.spisToCreateList.length ; a++){
                                            (self.$refs.spisToCreate[a]).setResult(self.spisToCreateList[a]["spi"])
                                        }
                                    }
                                    if(self.spisToOverrideList){
                                        for(var b = 0 ;b < self.spisToOverrideList.length ; b++){
                                            (self.$refs.spisToOverride[b]).setResult(self.spisToOverrideList[b])
                                        }
                                    }
                                    if(self.modelsToCreateList){
                                        for(var c = 0 ;c < self.modelsToCreateList.length ; c++){
                                            (self.$refs.modelsToCreate[c]).setResult(self.modelsToCreateList[c]["model"])
                                        }
                                    }
                                    if(self.modelsToOverrideList){
                                        for(var d = 0 ;d < self.modelsToOverrideList.length ; d++){
                                            (self.$refs.modelsToOverride[d]).setResult(self.modelsToOverrideList[d],d)
                                        }
                                    }
                                    if(self.apisToCreateList){
                                        for(var e = 0 ;e < self.apisToCreateList.length ; e++){
                                            (self.$refs.apisToCreate[e]).setResult(self.apisToCreateList[e]["api"])
                                        }
                                    }
                                    if(self.apisToOverrideList){
                                        for(var f = 0 ;f < self.apisToOverrideList.length ; f++){
                                            (self.$refs.apisToOverride[f]).setResult(self.apisToOverrideList[f],f)
                                        }
                                    }
                                    
                                },500);
                                this.checkAllGroupApisToOverride = [];
                                this.checkAllGroupSpisToOverride = [];
                                this.checkAllGroupModelsToOverride = [];
                                
                                this.analysis_result_show = true;
                                   
                            }else{
                                var detail = response.data.detail
                                if(detail){
                                    var html = '';
                                    for(var k = 0 ; k< detail.length ; k ++){
                                        html += `<p>项目：${detail[k].item} ;值：${detail[k].value} ;原因：${detail[k].reason}</p>`
                                    }
                                    this.$Modal.error({
                                        title: "失败",
                                        content: html
                                    });
                                }else{
                                    this.$Notice.error({
                                    title: '失败',
                                    desc: response.message,
                                    duration: 10
                                });
                                }
                                 
                            }
                    }
                )
            }, 
            hide_import_spi(){
                this.import_api_show = false;
            },
            clear(){
                this.$refs.upload.clearFiles();//清除上次上传记录
            },
            // 导入API函数
            interface_Import () {
                this.import_api_show = true
                this.clear();
                this.$refs.importApi.resetFields();
                this.$refs.modal_apiM_select_5.clearSingleSelect();
                // this.$refs.select_apiM_1.clearSingleSelect();
                this.form_data_spi.apiGroup = "";
            },
            //导出API函数
            interface_Export () {
                this.$refs.form_data_spi_export.resetFields();
                this.form_data_spi_export.spiType = "";
                this.$refs.apiM_select_1.clearSingleSelect();
                // this.$refs.select_apiM_2.clearSingleSelect();
                if (this.multiSelectedData == null || this.multiSelectedData.length == 0){
                    //还有其他函数判定
                    this.$ypMsg.notice_warning(this,'请选择需要导出的数据后再点击');
                }else {
                    let diff=0
                    for (var i = 0; i < this.testMultiSelectedData.length; i++) {
                        if (this.testMultiSelectedData[i] !== this.testMultiSelectedData[0]) {
                            diff++
                        }
                    }
                    if (diff === 0) {
                        this.export_spi_show = true;
                        this.form_data_spi_export.data_select_apiGroup_import = this.testMultiSelectedData[0]
                    } else {
                        this.$Modal.error({
                            title: '错误',
                            content: "请选择相同的API分组"
                        });
                    }
                }
            },
            sure_export_spi(val){
                this.$refs[val].validate((valid) => {
                if (valid) {
                        var  param = {
                            apiGroup:this.form_data_spi_export.data_select_apiGroup_import,
                            dataFormat:this.form_data_spi_export.spiType,
                            api:this.multiSelectedAPIId,
                        }
                        var type=""
                        if(this.form_data_spi_export.spiType == "JSON"){
                            type="json"
                        }else if(this.form_data_spi_export.spiType == "YAML"){
                            type="yaml"
                        }
                        api.yop_apiManagement_api_export(param,type).then(
                            (response) => {
                                if(response.data.status == "success"){

                                }else{

                                }
                            }
                        )
                        this.export_spi_show = false;
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            hide_export_spi(){
                this.export_spi_show = false;
            },
            /**
             * 新建弹窗方法
             */
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
           
          
            // 本地校验
            IsLocal (string){
                if(string.trim().substring(0,32)  === 'com.yeepay.g3.yop.center.combapi'){
                    if(this.data_select_type !== 'LOCAL'){
                        this.$Modal.warning({
                            title: '警告',
                            content: '您配置的端点方法适配类型为本地，系统将帮您的适配类型转化为本地！',
                            onOk: () =>{
                                this.data_select_type ='LOCAL';
                            }
                        });
                    }

                }
            },
            // url校验
            IsUrl (str_url) {
                let strRegex = '^((https|http|ftp|rtsp|mms)?://)'
                    + '?(([0-9a-z_!~*\'().&=+$%-]+: )?[0-9a-z_!~*\'().&=+$%-]+@)?' //ftp的user@
                    + '(([0-9]{1,3}.){3}[0-9]{1,3}' // IP形式的URL- **************
                    + '|' // 允许IP和DOMAIN（域名）
                    + '([0-9a-z_!~*\'()-]+.)*' // 域名- www.
                    + '([0-9a-z][0-9a-z-]{0,61})?[0-9a-z].' // 二级域名
                    + '[a-z]{2,6})' // first level domain- .com or .museum
                    + '(:[0-9]{1,4})?' // 端口- :80
                    + '((/?)|' // a slash isn't required if there is no file name
                    + '(/[0-9a-z_!~*\'().;?:@&=+$,%#-]+)+/?)$';
                let re=new RegExp(strRegex);
                if (re.test(str_url)) {
                    return true;
                } else {
                    return false;
                }
            },
            // 上传文件
            uploading(event){

            },
            //文件上传状态绑定函数
            handleFormatError (file) {
                this.$ypMsg.notice_warning(this,'文件 ' + file.name + ' 格式不正确，请选择图片文件。','文件格式不正确');
            },
            handleBeforeUpload (file) {
                // this.clear();
                this.form_data_spi.fileContent = file
                this.$ypMsg.notice_warning(this,'文件 ' + file.name + ' 准备上传。','文件准备上传')
                return false
            },
            handleProgress (event, file) {
                this.$ypMsg.notice_info(this,'文件 ' + file.name + ' 正在上传。','文件正在上传')
            },
            handleSuccess (event, file) {
                let total = event.data.result.total
                let success = event.data.result.success
                let failure = event.data.result.failure
                this.$ypMsg.notice_success(this,'全部API数量：' + total + ' <br/>'+'成功API数量：' + success + ' <br/>'+'失败API数量：' + failure,'文件上传完成结果');
                
            },
            handleError (event, file,fileList) {
                this.$ypMsg.notice_error_simple(this,'文件上传失败','文件 ' + fileList.name + ' 上传失败。');
            },
            // 下拉框加载完处理函数
            select_callBack () {

            },
            // api分组下拉框数据更新
            updateSelect_apiGroup (val) {
                this.data_apiGroup = val;
            },
            // api类型下拉框数据更新
            updateSelect_apiType (val) {
                this.data_apiType = val;

            },
           
            // 冒泡阻止，防止点击输入框，触发手风琴操作
            stopProp (e) {
                e.stopPropagation();
            },
        },
        mounted () {
            // 挂载时调用页面初始化函数
            this.init();
            localStorage.removeItem('apiInfo');
           
            api.yop_apiManagement_commons_sys_params().then(
                        (response) =>{
                            var response = response.data
                            this.sys_params_list = [];
                            let sys_params = response.data.result;
                            for(var i in sys_params){
                                this.sys_params_list.push(
                                    {
                                        label: sys_params[i].name,
                                        value:sys_params[i].code,
                                        name: sys_params[i].name,
                                        parameterName:sys_params[i].parameterName
                                    }
                                );
                            }
                            this.$store.state.sys_params_list = this.sys_params_list
                            localStorage.setItem("sys_params_list",JSON.stringify(this.sys_params_list))
                        }
                    );
        },
        created (){
            // localStorage.removeItem('apiInfo');
        },

    };
</script>

<style scoped>

</style>
