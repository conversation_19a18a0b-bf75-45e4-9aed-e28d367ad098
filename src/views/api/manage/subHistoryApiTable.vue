<template>
	<div class="subHistoryApiTable">
		<Table
			border
			:loading="loading"
			:columns="columns_ApiInterfaceList"
			:data="list"
			:show-header="false"
			@on-selection-change="handleselection"
		></Table>
		<DeleteModal ref="deleteModal" @delete_ok="delete_ok" />
		<PublishModal ref="publishModal" @confirm="publish" />
		<DownApiModal ref="downApiModal" @confirm="downApiConfirm" />
		<UpGradeModal ref="upGradeModal" @confirm="upGradeConfirm" />
	</div>
</template>

<script>
import Api from '~/api/api';
import operateList from './operateList';
import DeleteModal from '../modals/deleteModal';
import PublishModal from '../modals/publishModal';
import DownApiModal from '../modals/downApiModal';
import UpGradeModal from '../modals/upGradeModal';
export default {
    components: {
        DeleteModal,
        DownApiModal,
        UpGradeModal,
        PublishModal
    },
    props: {
        params: {
            type: Object,
            default: () => {}
        },
        data_status_List: {
            type: Array,
            default: () => []
        },
        index: {
            type: Number
        }
    },
    data () {
        return {
            apiEnv: 'CANARY',
            currentApi: null,
            // apiAPI列表列属性
            columns_ApiInterfaceList: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: 'API',
                    key: 'name',
                    width: 110,
                    render (h, params) {
                        return h('div',
                            [
                                h('div', params.row.name),
                                h('div', params.row.interface_Title)
                            ]);
                    }
                },
                {
                    title: 'API分组',
                    key: 'apiGroup',
                    type: 'html',
                    width: 90,
                    align: 'center'
                },
                {
                    title: '方法/请求路径',
                    minWidth: 160,
                    render: (h, params) => {
                        var color = 'blue';
                        return h('div',[
                            h('Tag',{
                                style:{
                                    align: 'center'
                                },
                                props:{
                                    color: color
                                }
                            },params.row.httpMethod),
                            h('span',params.row.interface_URI)]);
                    }
                },
                {
                    title: 'API类型',
                    width: 80,
                    key: 'APItype',
                    align: 'center',
                },
                {
                    title: 'API状态',
                    key: 'statusDesc',
                    width: 130,
                    align: 'center',
                    render: (h, params) => {
                        let color = 'green';
                        let statusDesc = '';
                        const { status } = params.row;
                        const apiStatusItem = this.data_status_List.find(item => item.value === status);
                        if (apiStatusItem) {
                            statusDesc = apiStatusItem.label;
                            if (apiStatusItem.value === 'DISABLED' || apiStatusItem.value === 'DOC_OFFLINE') {
                                color = 'grey';
                            }
                        }
                        return h('div', [
                            h('Tag', {
                                style: {
                                    align: 'center'
                                },
                                props: {
                                    color: color
                                }
                            }, statusDesc)
                        ]);
                    }
                },
                {
                    title: '路由状态',
                    key: 'statusDesc',
                    width: 100,
                    align: 'center',
                    render (h, params) {
                        let color = '';
                        let statusDesc = '';
                        if (params.row.routeStatus === 'PUBLISHED') {
                            color = 'green';
                            statusDesc = '已发布';
                        } else if (params.row.routeStatus === 'UNPUBLISHED') {
                            color = 'grey';
                            statusDesc = '待发布';
                        }
                        return h('div', [
                            h('Tag', {
                                style: {
                                    align: 'center'
                                },
                                props: {
                                    color: color
                                }
                            }, statusDesc)
                        ]);
                    }
                },
                {
                    renderHeader: (h, params) => {
                        return h('div', [
                            h('p', '创建时间'),
                            h('p', '最后更新时间')
                        ]);
                    },
                    key: 'Date',
                    type: 'html',
                    align: 'center',
                    width: 160
                },
                {
                    title: '操作',
                    key: 'operations',
                    align: 'center',
                    width: 160,
                    render: operateList.bind(window.newApiManageVm)
                }
            ],
            loading: false,
            list: [],
            deleteApiId: ''
        };
    },
    created () {
        this.getList();
    },
    destroyed () {
        this.$emit('handelSubHistorySelect', {
            key: this.index,
            value: []
        });
    },
    methods: {
        handleselection (value) {
            this.$emit('handelSubHistorySelect', {
                key: this.index,
                value: value.map(item => ({...item, APIid: item.apiId, APIgroupCode: item.apiGroup}))
            });
        },
        getList () {
            this.loading = true;
            this.$emit('handelSubHistorySelect', {
                key: this.index,
                value: []
            });
            Api.yop_api_history_list({
                apiId: this.params.APIid
            })
                .then(res => {
                    const { status, message, solutions, data } = res.data;
                    if (status === 'success') {
                        this.list = window.newApiManageVm.tabledataGetForHistory(data.result)
                        .map(item => {
                            item.isHistoryItem = true
                            return item
                        })
                    } else {
                        this.$ypMsg.notice_error(
                            this,
                            '列表获取错误',
                            message,
                            solutions
                        );
                    }
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        // 启用按钮调用方法
        interface_use (operations, id) {
            if (operations) {
                this.$Modal.confirm({
                    title: '提示',
                    content: '禁用API将导致API无法调用，您还要继续吗？',
                    'ok-text': '禁用',
                    onOk: () => {
                        this.forbidden_ok(operations, id);
                    }
                });
            } else {
                this.$Modal.confirm({
                    title: '提示',
                    'ok-text': '启用',
                    content: '您将启用API，请确认是否维护好API信息，您还要继续吗',
                    onOk: () => {
                        this.enabled_ok(operations, id);
                    }
                });
            }
        },
        // 启用提示框ok
        enabled_ok (operations, id) {
            Api.yop_api_manage_enable({
                apiId: id
            }).then(
                (response) => {
                    if (response.status === 'success') {
                        if (localStorage.needAudit === 'true') {
                            this.$ypMsg.notice_info(this, response.message);
                        } else {
                            this.$ypMsg.notice_success(this, '所选API启用成功', '启用成功');
                        }
                        this.getList();
                    } else {
                        this.$ypMsg.notice_error(this, '启用失败', response.message, response.solution);
                    }
                }
            );
        },
        // 禁用提示框ok
        forbidden_ok (operations, id) {
            Api.yop_api_manage_disable({
                apiId: id
            }).then(
                (response) => {
                    if (response.status === 'success') {
                        this.$ypMsg.notice_success(this, '所选API禁用成功', '禁用成功');
                        this.getList();
                    } else {
                        this.$ypMsg.notice_error(this, '禁用失败', response.message, response.solution);
                    }
                }
            );
        },
        deleteApi (apiId) {
            this.deleteApiId = apiId;
            this.$refs.deleteModal.show();
        },
        delete_ok (cause) {
            Api.yop_api_manage_delete({
                apiId: this.deleteApiId,
                cause
            }).then(
                (response) => {
                    if (response.status === 'success') {
                        this.$ypMsg.notice_success(this, '所选API删除成功', '删除成功');
                    } else {
                        this.$ypMsg.notice_error(this, '删除失败', response.message, response.solution);
                    }
                    this.getList();
                }
            );
        },
        showPublish (row) {
            this.currentApi = row;
            this.$refs.publishModal.show(row);
        },
        publish (params) {
            let param = {
                apiId: this.currentApi.apiId,
                apiVersion: this.currentApi.version,
                env: params.publishApiEnv,
                remark: params.publish_api_cause
            };
            Api.yop_apiManagement_deploy(param).then(
                (response) => {
                    if (response.status === 'success') {
                        this.$ypMsg.notice_success(this, '发布成功');
                        this.getList();
                    } else {
                        this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
                    }
                }
            );
        },
        downApiConfirm (params = '') {
            let param = null;
            if (params) {
                param = {
                    apiId: this.currentApi.apiId,
                    env: params.apiEnv, // 环境
                    remark: params.offline_api_cause
                };
            } else {
                param = {
                    apiId: this.currentApi.apiId,
                    env: this.apiEnv, // 环境
                    remark: ''
                };
            }
            Api.yop_apiManagement_offline(param).then(
                (response) => {
                    if (response.status === 'success') {
                        this.getList();
                        this.$ypMsg.notice_success(this, '下线成功');
                    } else {
                        this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
                    }
                }
            );
        },
        upGradeConfirm () {
            let param = {
                apiId: this.currentApi.apiId,
                snapshotVersion: this.currentApi.deployStatus.CANARY.snapshotVersion
            };
            Api.yop_apiManagement_upgrade_production(param).then(
                (response) => {
                    if (response.status === 'success') {
                        this.$ypMsg.notice_success(this, '升级成功');
                        this.getList();
                    } else {
                        this.devToProFlag = false;
                        this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
                    }
                }
            );
        }
    }
};
</script>
<style lang="less">
	.subHistoryApiTable {
		.ivu-table td {
			background: #f5f7fa;
		}
	}
</style>