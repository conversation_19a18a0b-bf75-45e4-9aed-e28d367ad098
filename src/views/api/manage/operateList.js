const getBtnList = function (h, params) {
  const leftList = [
    h('Button', {
      domProps: {
        id: 'api_mangent_edit_btn6'
      },
      class: 'btnModifyAPI',
      props: {
        type: 'text',
        size: 'small'
      },
      directives: [{
        name: 'url',
        value: {url: '/rest/api/manage/update'}
      }],
      on: {
        click: () => {
          this.interface_modify(params.row);
        }
      }
    }, '修改'),
    h('Button', {
      domProps: {
        id: 'api_mangent_edit_btn7'
      },
      class: 'btndesAPI',
      props: {
        type: 'text',
        size: 'small'
      },
      directives: [{
        name: 'url',
        value: {url: '/rest/api/manage/detail'}
      }],
      on: {
        click: () => {
          this.interface_des(params.row.APIid);
        }
      }
    }, '详情'),
    h('Button', {
      domProps: {
        id: 'api_mangent_edit_btn28'
      },
      class: 'btnUpdataAPI',
      props: {
        type: 'text',
        size: 'small'
      },
      directives: [{
        name: 'url',
        value: {url: '/rest/api/manage/create'}
      }],
      on: {
        click: () => {
          this.webApi_Register(params.row.APIid);
        }
      }
    }, '升级'),
    h('Button', {
      domProps: {
        id: 'api_mangent_edit_btn28'
      },
      class: 'btnUpdataAPI',
      props: {
        type: 'text',
        size: 'small'
      },
      directives: [{
        name: 'url',
        value: {url: '/rest/api/route/list'}
      }],
      on: {
        click: () => {
          window.localStorage.setItem('apiRouteParams', JSON.stringify(params.row));
          // this.$router.replace('/api_route_manage');
          this.$router.push({
            name: 'api_route_manage',
            params: {
              name: this.data_interface_name,
              path: this.data_interface_uri,
              apiType: this.data_select_apiType,
              method: this.data_select_method,
              routeStatus: this.routeStatus,
              apiGroup: this.data_select_apiGroup,
              status: this.data_select_status,
              _pageNo: this.current_params._pageNo
            }
          });
        }
      }
    }, '路由')
  ];
  const rightList = [
    h('Button', {
      domProps: {
        id: 'api_mangent_edit_btn16'
      },
      props: {
        type: 'text',
        size: 'small'
      },
      directives: [{
        name: 'url',
        value: {url: '/rest/api/manage/deploy-record/list'}
      }],
      on: {
        click: () => {
          this.$refs.apiPublishRecordList.showModal(params.row);
        }
      }
    }, '发布记录'),
    h('Button', {
      domProps: {
        id: 'api_mangent_edit_btn21'
      },
      props: {
        type: 'text',
        size: 'small'
      },
      on: {
        click: () => {
          this.relevance_error_code(params.row);
        }
      }
    }, '错误码配置'),
    h('Button', {
      domProps: {
        id: 'api_mangent_edit_btn30'
      },
      props: {
        type: 'text',
        size: 'small'
      },
      on: {
        click: () => {
          this.$refs.apiRelationship.showModal(params.row);
          // this.relevance_error_code(params.row);
        }
      }
    }, 'API关系'),
    h('Button', {
      props: {
        type: 'text',
        size: 'small'
      },
      directives: [{
        name: 'url',
        value: {url: '/rest/api/security-req'}
      }],
      on: {
        click: () => {
          this.api_security(params.row.APIid, params.row.APIgroupCode);
        }
      }
    }, '安全需求'),
    h('Button', {
      domProps: {
        id: 'api_mangent_edit_btn24'
      },
      class: 'btnModifyAPI',
      props: {
        type: 'text',
        size: 'small'
      },
      directives: [{
        name: 'url',
        value: {url: '/rest/api/manage/sub-ref/list'}
      }],
      on: {
        click: () => {
          this.$refs.selection1.toggleExpand(params.row._index);
        }
      }
    }, '历史版本')
  ];
  if (params.row.status === 'DISABLED') {
    leftList.splice(1, 0,
      h('Button', {
        domProps: {
          id: 'api_mangent_edit_btn9'
        },
        class: 'btndeleteAPI',
        props: {
          type: 'text',
          size: 'small'
        },
        directives: [{
          name: 'url',
          value: {url: '/rest/api/manage/delete'}
        }],
        on: {
          click: () => {
            this.interface_delete(params.index, params.row.operations, params.row.APIid);
          }
        }
      }, '删除')
    );
    leftList.splice(4, 0,
      h('Button', {
        domProps: {
          id: 'api_mangent_edit_btn8'
        },
        class: 'btnEnabledAPI',
        props: {
          type: 'text',
          size: 'small'
        },
        directives: [{
          name: 'url',
          value: {url: '/rest/api/manage/enable'}
        }],
        on: {
          click: () => {
            this.interface_use(params.index, false, params.row.APIid);
          }
        }
      }, '启用')
    );
  } else {
    // 文档已发布
    if (params.row.status === 'DOC_PUBLISHED') {
      rightList.push(
        h('Button', {
          domProps: {
            id: 'api_mangent_edit_btn19'
          },
          props: {
            type: 'text',
            size: 'small'
          },
          on: {
            click: () => {
              this.$refs.downDoc.showModal(params.row.APIid);
            }
          }
        }, '下线')
      );
    } else {
      leftList.splice(4, 0,
        h('Button', {
          domProps: {
            id: 'api_mangent_edit_btn14'
          },
          class: 'btnDisabledAPI',
          props: {
            type: 'text',
            size: 'small'
          },
          directives: [{
            name: 'url',
            value: {url: '/rest/api/manage/disable'}
          }],
          on: {
            click: () => {
              this.interface_use(params.index, true, params.row.APIid);
            }
          }
        }, '禁用')
      );
      rightList.push(
        h('Button', {
          props: {
            type: 'text',
            size: 'small'
          },
          directives: [
            {
              name: 'show',
              value: params.row.status !== 'PUBLISHED'
            }
          ],
          on: {
            click: () => {
              this.$refs.publishDocModal.showModal(params.row.APIid);
            }
          }
        }, '发布')
      );
    }
  }
  if (params.row.isHistoryItem) {
    leftList.splice(2, 1)
    rightList.splice(3, 1)
  }
  return h('div',
    {
      style: 'display: flex'
    },
    [
      h('div', leftList),
      h('div', rightList)
    ]);
};

export default getBtnList;
