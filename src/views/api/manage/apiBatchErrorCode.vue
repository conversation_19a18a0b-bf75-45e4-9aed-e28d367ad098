<style lang="less">
    @import '../../../styles/common.less';
    .api-batch-errcode-model {
      .ivu-modal-wrap .flex-box {
        display: flex;
        justify-content: space-around;
        > p {
          width: 40%;
        } 
        .ivu-col {
          
        }
      }
    }
</style>
<template>
    <div class="api-batch-errcode">
        <Modal
          v-model="visable" 
          width="55%" 
          @on-cancel="cancel"
          class="api-batch-errcode-model"
          title="批量添加错误码位置"
        >
          <Row>
            <Form :key="allFormKey" ref="form_errCode" :model="form_errCode" :rules="errCodeRules" :label-width="120">
              <FormItem label="错误码位置：" prop="errorCodeLocation">
                <Input clearable id="input_resList_1" type="text"  v-model="form_errCode.errorCodeLocation" style="width:85%"></Input>
              </FormItem>
              <FormItem label="错误消息位置：" prop="messageLocation">
                <Input clearable id="input_resList_2" type="text"  v-model="form_errCode.messageLocation" style="width:85%"></Input>
              </FormItem>
              <FormItem label="请求成功类型：" prop="requestSuccessType">
                <Select clearable ref="select_apiM_3" style="width:85%"  id='select_apiM_3' @on-change="chooseSuccessType" v-model="form_errCode.requestSuccessType" filterable placeholder="请选择">
                  <Option value="NO_BACK_CODE">成功不返回错误码</Option>
                  <Option value="BACK_CODE">成功返回错误码</Option>
                </Select>
              </FormItem>
              <FormItem :key="codeKey" v-if="form_errCode.requestSuccessType === 'BACK_CODE'" label="请求成功码：" prop="requestSuccessValue">
                <Input clearable id="input_resList_4" type="text"  v-model="form_errCode.requestSuccessValue" style="width:85%"></Input>
              </FormItem>
            </Form>
          </Row>
          <Row>
            <Form >
              <FormItem :key="apiKey" label="API分组：" :label-width="120">
                <Select ref="select_apiM_0" style="width:85%" @on-change="selectApiGroup" id='selectAPIGroup1' class="margin-top-5"  v-model="data_select_apiGroup" filterable clearable placeholder="请选择">
                  <Option v-for="item in data_apiGroup_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
              </FormItem>
            </Form>
          </Row>
          <Alert type="warning">API列表（仅包含未配置错误码扩展信息）</Alert>
          <Row>
            <Col span="24">
              <Table @on-selection-change="handleselection" height="380" id='table_api_error' border ref="table_api_error" :columns="errorListColums" :data="errorListData" ></Table>
            </Col>
          </Row>
          <div slot="footer">
            <Button type="primary" @click="saveInfo('form_errCode')" >确认</Button>
            <Button @click="cancel" >取消</Button>
          </div>
        </Modal>
    </div>
</template>
<script>
import Api from '~/api/api'
import util from '~/libs/util'
export default {
    name: 'apiBatchErrorCode',
    components: {
    },
    props: ['data_apiGroup_List'],
    data() {
      return {
        allFormKey: 1,
        apiKey: 1, //渲染form
        codeKey: 1,
        visable: false,
        // api分组下拉框数据
        data_select_apiGroup: '',
        // 错误码位置
        errCodePosition: '',
        // 修改错误码form
        form_errCode: {
          errorCodeLocation: '',
          messageLocation: '',
          requestSuccessValue: '',
          requestSuccessType: '',
        },
        // 错误码校验规则
        errCodeRules: {
          errorCodeLocation: [
            {required: true, message: '错误码位置不能为空', trigger: 'change'},
          ],
          messageLocation: [
            {required: true, message: '错误消息位置不能为空'},
          ],
          requestSuccessType: [
            {required: true, message: '请求成功类型不能为空'},
          ],
          requestSuccessValue: [
            {required: true, message: '请求成功码不能为空'},
          ],
        },
        errorListData: [],
        // 错误列表 表头
        errorListColums: [
          {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          {
            title: `API标题`,
            key: 'title',
            align: 'left',
          },
          {
            title: 'API路径',
            key: 'path',
            align: 'left'
          },
        ],
        // 批量勾选api列表
        choosedList: []
      }
    },
    mounted() {
      
    },
    methods: {
      // 展示批量操作弹框
      showModel() {
        this.visable = true
        this.data_select_apiGroup = ''
        this.errorListData = []
        this.apiKey += 1
        this.allFormKey += 1
      },
      // 切换api分组
      selectApiGroup() {
        if(this.data_select_apiGroup) {
          this.getApiGroupList()
        }
      },
      // 获取api分组列表
      getApiGroupList() {
        let params = {
          apiGroup: this.data_select_apiGroup
        };
        Api.yop_errorCode_manage_ids(params)
        .then(res => {
          const { status, message, data } = res.data;
          if (status === 'success') {
            this.errorListData = data.apiList
          } else {
            this.$ypMsg.notice_error(
              this,
              message
            );
          }
        })
      },
      // 选择成功类型 重置下请求成功码 必填
      chooseSuccessType(val) {
        this.codeKey += 1
        if(val === 'BACK_CODE') {
          this.errCodeRules.requestSuccessValue[0].required = true
        } else {
          this.errCodeRules.requestSuccessValue[0].required = false
        }
      },
      handleselection (value) {
        // 批量勾选的数据
        this.choosedList = value
      },
      // 取消
      cancel() {
        this.visable = false
        this.form_errCode.errorCodeLocation = ''
        this.form_errCode.messageLocation = ''
        this.form_errCode.requestSuccessValue = ''
        this.form_errCode.requestSuccessType = ''
        this.$refs.form_errCode.resetFields();
      },
      // 提交
      saveInfo(val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            let choosedApiIds = [];
            choosedApiIds = this.choosedList.map(item => item.apiid )
            let params = {
              apiIds: choosedApiIds,
              errorCodeLocation: this.form_errCode.errorCodeLocation,
              messageLocation: this.form_errCode.messageLocation,
              requestSuccessValue: this.form_errCode.requestSuccessValue,
              requestSuccessType: this.form_errCode.requestSuccessType,
            };
            Api.yop_errorCode_batch_add(params)
            .then(res => {
              const { status, message, data } = res;
              if (status === 'success') {
                this.$ypMsg.notice_success(
                  this,
                  '保存成功'
                );
                this.visable = false
                this.cancel()
              } else {
                this.$ypMsg.notice_error(
                  this,
                  message
                );
              }
            })
          } else {
            this.$Message.error('请检查');
          }
        })
        
      },
    },
}
</script>