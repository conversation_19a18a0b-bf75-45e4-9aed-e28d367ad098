<template>
  <div>
    <Modal v-model="hasErroCodeShow" width="50%">
      <p slot="header" style="color: #2d8cf0;">
        <span style="color: black;">错误码配置</span>
      </p>
      <Card dis-hover :bordered="false">
        <h4>扩展信息配置</h4>
        <Row class="margin-top-20 err-select" type="flex" justify="start"> 
          <Col span="4" class="text-left">
            <span>错误码位置:</span>
          </Col>
          <Col class="">
            <span>{{form_errCode.errorCodeLocation ? form_errCode.errorCodeLocation : '未填写'}}</span>
          </Col>
        </Row>
        <Row class="margin-top-20 err-select" type="flex" justify="start"> 
          <Col span="4" class="text-left">
            <span>错误消息位置:</span>
          </Col>
          <Col class="">
            <span>{{form_errCode.messageLocation ? form_errCode.messageLocation : '未填写'}}</span>
          </Col>
        </Row>
        <Row class="margin-top-20 err-select" type="flex" justify="start"> 
          <Col span="4" class="text-left">
            <span>请求成功类型:</span>
          </Col>
          <Col class="">
            <span v-if="!!!form_errCode.requestSuccessType" >未填写</span>
            <span v-if="form_errCode.requestSuccessType === 'BACK_CODE'" >成功返回错误码</span>
            <span v-if="form_errCode.requestSuccessType === 'NO_BACK_CODE'" >成功不返回错误码</span>
          </Col>
        </Row>
        <Row class="margin-top-20 err-select" type="flex" justify="start"> 
          <Col span="4" class="text-left">
            <span>请求成功码:</span>
          </Col>
          <Col class="edit-errorinfo" type="flex" justify="space-around">
            <span>{{form_errCode.requestSuccessValue ? form_errCode.requestSuccessValue : '未填写'}}</span>
            <Button class="margin-left-10" type="primary" @click="showErrcodeFlag"  id="btnShow">修改信息</Button>
          </Col>
        </Row>
        <div class="line"></div>
        <h4>关联错误码</h4>
        <Row class="margin-top-20 flex-end" type="flex">
          <Col>
            <Button class="margin-right-10" type="ghost" @click="deleteErrorCode" id="btnDelete">批量删除</Button>
          </Col>
          <Col>
            <Button type="ghost" @click="showAddErrorCodeModal" id="btnShow">添加错误码</Button>
          </Col>
        </Row>
        <Row class="margin-top-20">
          <Table
            id="table1"
            border
            :columns="columns"
            :data="hasErrorCodeData"
            @on-selection-change="deleteErrorCodeSelection"
          ></Table>
          <loading :show="showLoadingHasErrorCode"></loading>
        </Row>
        <Row class="margin-top-20" type="flex" justify="end">
          <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
            <Page
              :total="hasErrorCodeParams.total"
              :current="hasErrorCodeParams.pageNo"
              show-elevator
              @on-change="onHasErrorCodeChange"
            />
          </Tooltip>
        </Row>
      </Card>
      <div slot="footer">
        <Button type="ghost" id="popupClose" @click="hasErroCodeShow = false">关闭</Button>
      </div>
    </Modal>
    <Modal v-model="model_show" :closable="false" width="55%">
      <p slot="header" style="color: #2d8cf0;">
        <span style="color: black;">添加子错误码</span>
      </p>
      <Card dis-hover :bordered="false">
        <Row type="flex" align="middle">
          <Col span="18">
            <Col span="3" class="margin-top-10">
              <span>子错误码:</span>
            </Col>
            <Col span="16">
              <Input
                clearable
                id="inputSubErrorCode"
                class="margin-top-5"
                v-model="noRelevanceCodeParams.subErrorCode"
                placeholder="子错误码"
              ></Input>
            </Col>
          </Col>
          <Col span="6">
            <Col class="margin-top-10" span="11" style="text-align: center;">
              <Button id="btnSearch" type="primary" @click="search_Interface">查询</Button>
            </Col>
            <Col class="margin-top-10" span="11" style="text-align: center;">
              <Button id="btnReset" type="ghost" @click="reset_Interface">重置</Button>
            </Col>
          </Col>
        </Row>
        <Row class="margin-top-20">
          <Table
            id="table2"
            border
            ref="singleTable"
            :columns="columns_no_relevance"
            :data="noRelevanceCodeData"
            @on-selection-change="addErrorCodeSelection"
            row-key="id"
          ></Table>
          <loading :show="showLoadingNoRelevanceCode"></loading>
        </Row>
        <Row class="margin-top-20" type="flex" justify="end">
          <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
            <Page
              :total="noRelevanceCodeParams.total"
              :current="noRelevanceCodeParams.pageNo"
              show-elevator
              @on-change="noRelevanceCodeChange"
            />
          </Tooltip>
        </Row>
      </Card>
      <div slot="footer">
        <Button type="primary" id="popupConfirm" @click="confirmAddErrorCode">确定</Button>
        <Button type="ghost" id="popupCancel" @click="close_modal">取消</Button>
      </div>
    </Modal>
    <!-- 修改错误码位置相关信息 -->
    <Modal v-model="errCodeInfoFlag" :closable="false" width="40%">
      <p slot="header" style="color: #2d8cf0;">
        <span style="color: black;">错误码位置</span>
      </p>
      <!-- <Card :bordered="false"> -->
        <Row>
          <Form ref="form_errCode" :model="form_errCode" :key="formKey" :rules="errCodeRules" :label-width="120">
            <FormItem label="错误码位置：" prop="errorCodeLocation">
                <Select clearable ref="select_apiM_3" style="width:85%"  id='select_apiM_3' v-model="form_errCode.errorCodeLocation" filterable placeholder="请选择">
                  <Option v-for="el in errCodePosList" :key="el" :value="el" >{{el}}</Option>
                </Select>
            </FormItem>
            <FormItem label="错误消息位置：" prop="messageLocation">
              <Select clearable ref="select_apiM_5" style="width:85%"  id='select_apiM_3' v-model="form_errCode.messageLocation" filterable placeholder="请选择">
                <Option v-for="el in errCodePosList" :key="el" :value="el" >{{el}}</Option>
              </Select>
            </FormItem>
            <FormItem label="请求成功类型：" prop="requestSuccessType">
                <Select clearable ref="select_apiM_3" style="width:85%"  id='select_apiM_3' @on-change="chooseSuccessType" v-model="form_errCode.requestSuccessType" filterable placeholder="请选择">
                  <Option value="NO_BACK_CODE">成功不返回错误码</Option>
                  <Option value="BACK_CODE">成功返回错误码</Option>
                </Select>
            </FormItem>
            <FormItem v-if="form_errCode.requestSuccessType === 'BACK_CODE'"  label="请求成功码：" prop="requestSuccessValue">
                <Input clearable id="input_resList_4" type="text"  v-model="form_errCode.requestSuccessValue" style="width:85%"></Input>
            </FormItem>
          </Form>
        </Row>
      <!-- </Card> -->
      <div slot="footer">
        <Button type="primary" id="popupConfirm" @click="saveErrcodeInfo('form_errCode')">确定</Button>
        <Button type="ghost" id="popupCancel" @click="cancelErrcode">取消</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import Api from '~/api/api';
import loading from '~/views/my-components/loading/loading';
export default {
    name: 'modal-error-code-dialog',
    components: {
      loading,
    },
    data () {
        return {
            hasErroCodeShow: false,
            showLoadingHasErrorCode: false,
            showLoadingNoRelevanceCode: false,
            model_show: false,
            errCodeInfoFlag: false, //错误码位置相关
            formKey: 1, //渲染form
            errCodePosList: [],
            // 错误码信息
            errCodeInfo: {
            },
            // 修改错误码form
            form_errCode: {
              errorCodeLocation: '',
              messageLocation: '',
              requestSuccessValue: '',
              requestSuccessType: '',
            },
            // 错误码校验规则
            errCodeRules: {
              errorCodeLocation: [
                {required: true, message: '错误码位置不能为空', trigger: 'change'},
              ],
              messageLocation: [
                {required: true, message: '错误消息位置不能为空', trigger: 'change'},
              ],
              requestSuccessValue: [
                {required: true, message: '请求成功码不能为空', trigger: 'blur'},
              ],
              requestSuccessType: [
                {required: true, message: '请求成功类型不能为空', trigger: 'change'},
              ],
            },
            hasErrorCodeParams: {
                apiId: null,
                pageNo: 1,
                total: 10
            },
            hasErrorCodeData: [],
            noRelevanceCodeParams: {
                apiId: null,
                apiGroupCode: null,
                subErrorCode: null,
                pageNo: 1,
                total: 10
            },
            noRelevanceCodeData: [],
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: 'API分组',
                    key: 'apiGroupCode',
                    align: 'center'
                },
                {
                    title: '子错误码',
                    key: 'subErrorCode',
                    align: 'center'
                },
                {
                    title: '子错误码描述',
                    key: 'subErrorMsg',
                    align: 'center'
                },
                // {
                //     title: '语义化错误码',
                //     key: 'semantizationCode',
                //     align: 'center'
                // },
                {
                    title: '操作',
                    width: 100,
                    render: (h, params) => {
                        return h('Button', {
                            props: {
                                type: 'text',
                                size: 'small'
                            },
                            on: {
                                click: () => {
                                    this.deleteErrorCodeList = [params.row];
                                    this.deleteErrorCode();
                                }
                            }
                        }, '移除');
                    },
                    align: 'center'
                }
            ],
            deleteErrorCodeList: [],
            addErrorCodeList: [],
            // 表格头数据绑定
            columns_no_relevance: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: 'API分组',
                    key: 'apiGroupCode',
                    align: 'center'
                },
                {
                    title: '子错误码',
                    key: 'subErrorCode',
                    align: 'center'
                },
                {
                    title: '子错误码描述',
                    key: 'subErrorMsg',
                    align: 'center'
                }
            ],
            // 表格数据绑定
            tableData: [],
            tableOldData: [],
            // 加载动画数据绑定
            show_loading_apiGroup: false
        };
    },
    methods: {
      // 获取错误码信息相关
      getErrorCodeInfo(apiId) {
        let params = {
          apiId: apiId
        };
        Api.yop_errorCode_errorcode_location(params)
        .then(res => {
          const { status, message, data } = res.data;
          if (status === 'success') {
            this.form_errCode = data.errorCodeConfig
          } else {
            this.$ypMsg.notice_error(
              this,
              message
            );
          }
        })
      },
        search_Interface () {
            this.noRelevanceCodeParams.pageNo = 1;
            this.noRelevanceCodeParams.total = 10;
            this.getNoRelevanceCode();
        },
        reset_Interface () {
            this.noRelevanceCodeParams.subErrorCode = null;
            this.noRelevanceCodeParams.pageNo = 1;
            this.noRelevanceCodeParams.total = 10;
            this.getNoRelevanceCode();
        },
        show_model ({ APIid, APIgroupCode }) {
            this.hasErrorCodeParams.apiId = APIid;
            this.noRelevanceCodeParams.apiId = APIid;
            this.noRelevanceCodeParams.apiGroupCode = APIgroupCode;
            this.hasErroCodeShow = true;
            this.getHasRelevanceCode();
            this.initModels(APIid, APIgroupCode)
        },
        onlyAddErrorCodeModal ({ APIid, APIgroupCode }) {
            this.hasErrorCodeParams.apiId = APIid;
            this.noRelevanceCodeParams.apiId = APIid;
            this.noRelevanceCodeParams.apiGroupCode = APIgroupCode;
            this.showAddErrorCodeModal()
        },
        close_modal () {
            this.model_show = false;
        },
        // 初始化弹框获取数据
        initModels(APIid, APIgroupCode) {
          this.getErrorCodeInfo(APIid)
          this.getErrorCodeSelect(APIid)
        },
        // 选择成功类型 重置下请求成功码 必填
        chooseSuccessType(val) {
          this.formKey +=1
          if(val === 'BACK_CODE') {
            this.errCodeRules.requestSuccessValue[0].required = true
          } else {
            this.errCodeRules.requestSuccessValue[0].required = false
          }
        },
        // 错误位置下拉数据
        getErrorCodeSelect(apiId) {
          let params = {
            apiId: apiId
          };
          Api.yop_errorCode_model_codes(params)
          .then(res => {
            const { status, message, data } = res.data;
            if (status === 'success') {
              let obj = JSON.parse(data.codes).properties ? JSON.parse(data.codes).properties : {}
              // to do format 如果是err-code 则错误码位置为默认
              this.errCodePosList = []
              for (const key in obj) {
                this.errCodePosList.push(key)
                if(obj[key].format == 'error-code'){
                  this.form_errCode.errorCodeLocation = key
                }
              }
            }
          })
        },
        deleteErrorCode () {
            if (this.deleteErrorCodeList.length === 0) {
                this.$ypMsg.notice_warning(this, '请选择需要删除错误码后再点击');
                return;
            }
            this.$Modal.confirm({
                title: '删除错误码',
                content: '确定删除该错误码吗？',
                'ok-text': '删除',
                onOk: () => {
                    Api.yop_errorCode_delete_api({
                        ids: this.deleteErrorCodeList.map(item => item.id)
                    })
                        .then(res => {
                            const { status, message } = res;
                            if (status === 'success') {
                                this.$ypMsg.notice_success(
                                    this,
                                    '移除成功'
                                );
                                this.deleteErrorCodeList = [];
                                this.getHasRelevanceCode();
                            } else {
                                this.$ypMsg.notice_error(
                                    this,
                                    '错误',
                                    message
                                );
                            }
                        });
                }
            });
        },
        showAddErrorCodeModal () {
            this.model_show = true;
            this.getNoRelevanceCode();
        },
        // 错误码位置信息配置
        showErrcodeFlag() {
          this.errCodeInfoFlag = true
        },
        // 取消修改错误码信息
        cancelErrcode() {
          this.errCodeInfoFlag = false
          this.getErrorCodeInfo(this.hasErrorCodeParams.apiId)
        },
        // 修改错误码位置信息
        saveErrcodeInfo(val) {
          this.$refs[val].validate((valid) => {
            Object.assign(this.form_errCode, {apiId: this.hasErrorCodeParams.apiId});
            if (valid) {
              Api.yop_errorCode_location_save(this.form_errCode)
              .then(res => {
                  const { status, message, solutions, data } = res;
                  if (status === 'success') {
                    this.$ypMsg.notice_success(this,'修改成功');
                    this.errCodeInfoFlag = false
                    this.getErrorCodeInfo(this.hasErrorCodeParams.apiId)
                  } else {
                    this.$ypMsg.notice_error(
                      this,
                      '列表获取错误',
                      message,
                      solutions
                    );
                  }
              });
            } else {
              this.$Message.error('请检查');
            }
          })
        },
        confirmAddErrorCode () {
            Api.yop_errorCode_api_add_error_code(
                {
                    apiId: this.hasErrorCodeParams.apiId,
                    errcodeIds: this.addErrorCodeList.map(item => item.id)
                }
            )
                .then(res => {
                    const { status, message } = res;
                    if (status === 'success') {
                        this.$ypMsg.notice_success(
                            this,
                            '添加成功'
                        );
                        this.model_show = false;
                        this.getHasRelevanceCode();
                        // 刷新api选项的错误码
                        this.$emit('refreshErrorCodeList')
                    } else {
                        this.$ypMsg.notice_error(
                            this,
                            message
                        );
                    }
                });
        },
        computPageTotal (pageNo, pageSize, totalPageNum, arr, cb) {
            if (!arr) return 0;
            if (!totalPageNum) return 0;
            if (pageNo > totalPageNum && totalPageNum > 0) {
                this.getApiParams.pageNo = totalPageNum;
                cb();
                return;
            }
            if (pageNo <= totalPageNum - 1) {
                return NaN;
            }
            return (totalPageNum - 1) * pageSize + arr.length;
        },
        // 获取已关联api的code
        getHasRelevanceCode () {
            this.showLoadingHasErrorCode = true;
            Api.yop_errorCode_has_relevance_api(this.hasErrorCodeParams)
                .then(res => {
                    const { status, message, solutions, data } = res.data;
                    if (status === 'success') {
                        const { items, pageNo, totalPageNum } = data.page;
                        this.hasErrorCodeParams.pageNo = pageNo;
                        this.hasErrorCodeData = items;
                        this.hasErrorCodeParams.total = this.computPageTotal(pageNo, 10, totalPageNum, items, () => {
                            this.getHasRelevanceCode();
                        });
                    } else {
                        this.$ypMsg.notice_error(
                            this,
                            '列表获取错误',
                            message,
                            solutions
                        );
                    }
                }).finally(() => { this.showLoadingHasErrorCode = false; });
        },
        // 获取未关联api的code
        getNoRelevanceCode () {
            this.showLoadingNoRelevanceCode = true;
            Api.yop_errorCode_no_relevance_api(this.noRelevanceCodeParams)
                .then(res => {
                    const { status, message, solutions, data } = res.data;
                    if (status === 'success') {
                        const { items, pageNo, totalPageNum } = data.page;
                        this.noRelevanceCodeParams.pageNo = pageNo;
                        this.noRelevanceCodeData = items;
                        this.noRelevanceCodeParams.total = this.computPageTotal(pageNo, 10, totalPageNum, items, () => {
                            this.getNoRelevanceCode();
                        });
                    } else {
                        this.$ypMsg.notice_error(
                            this,
                            '列表获取错误',
                            message,
                            solutions
                        );
                    }
                })
                .finally(() => {
                    this.showLoadingNoRelevanceCode = false;
                });
        },
        addErrorCodeSelection (val) {
            this.addErrorCodeList = val;
        },
        deleteErrorCodeSelection (val) {
            this.deleteErrorCodeList = val;
        },
        onHasErrorCodeChange (val) {
            this.hasErrorCodeParams.pageNo = val;
            this.getHasRelevanceCode();
        },
        noRelevanceCodeChange (val) {
            this.noRelevanceCodeParams.pageNo = val;
            this.getNoRelevanceCode();
        }
    }
};
</script>

<style scoped lang='less'>
.err-select {
  align-items: center;
}
.text-left {
  text-align: right;
  & > span {
    margin-right: 10px;
  }
}
.line {
  height: 2px;
  background-color: #ccc;
  margin: 20px 0 40px;
  width: 100%;
}
.edit-errorinfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 83%;
}
.flex-end {
  justify-content: flex-end;
}
</style>

