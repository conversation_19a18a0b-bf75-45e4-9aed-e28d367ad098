<template>
  <Modal v-model="delete_or_normal_modal_show" width="500"  :closable="false">
          <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
              <Icon type="ios-information-circle"></Icon>
              <span>删除</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;">
                <i class="ivu-icon ivu-icon-help-circled"></i>
              </div>
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                <p style="color:red"></p>
                <p >确定要删除吗？</p>
                <label for="" class="reasonType"><span style="color:red;font-size: 12x;">*</span>原因：</label>
                <Input type="textarea" v-model="delete_api_cause" style="width:85%;font-size: 12x;margin-top:8px;" placeholder="" placeholder-style="font-size:12px;" :maxlength="60"></Input>
                <p style="color:#888;font-size:12px;margin-top:10px;margin-left:50px">最长可输入60字符</p>
              </div>
          </div>
          <div slot="footer" style="border:0;">
              <Button id="api_mange_btn3" type="ghost"  @click="hide_model">取消</Button>
              <Button id="api_mange_btn4" type="primary"  @click="delete_ok">确定</Button>
          </div>
      </Modal>
</template>

<script>
export default {
    data () {
        return {
            delete_or_normal_modal_show: false,
            delete_api_cause: ''
        };
    },
    methods: {
        hide_model () {
            this.delete_or_normal_modal_show = false;
        },
        show () {
            this.delete_or_normal_modal_show = true;
        },
        delete_ok () {
            if (this.delete_api_cause && this.delete_api_cause.length > 60) {
                this.$ypMsg.notice_error(this, '最长可输入60字');
                return;
            }
            this.delete_or_normal_modal_show = false;
            this.$emit('delete_ok', this.delete_api_cause);
        }
    }
};
</script>

<style>

</style>