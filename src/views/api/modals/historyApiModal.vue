<template>
    <Modal v-model="isShow" class-name="historyApiModal" width="700">
      <Table border ref="table" 
        :columns="columns" 
        :data="copyList" 
        @on-select="onSelectHistoryApi" 
        @on-select-cancel="onSelectCancel" />
      <div slot="footer">
          <Button type="primary" id="popupConfirm" @click="confirm">确定</Button>
          <Button type="ghost" id="popupCancel" @click="cancel">取消</Button>
        </div>
    </Modal>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep';
export default {
    props: {
        params: {
            type: Object,
            default: () => {}
        }
    },
    data () {
        return {
            isShow: false,
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: 'API名称',
                    key: 'name',
                    align: 'center'
                },
                {
                    title: '方法/请求路径',
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h('Tag', {
                                style: {
                                    align: 'center'
                                },
                                props: {
                                    color: 'blue'
                                }
                            }, params.row.httpMethod),
                            h('p', params.row.path)]);
                    }
                },
                {
                    title: '创建时间',
                    key: 'createdDateTime',
                    align: 'center'
                }
            ],
            selectApi: null,
            cbName: null,
            copyList: []
        };
    },
    methods: {
        show_model (list, cbName) {
            this.copyList = cloneDeep(list);
            this.isShow = true;
            this.cbName = cbName;
        },
        confirm () {
            if (!this.selectApi) {
                this.$ypMsg.notice_warning(this, '请选择一个api');
                return;
            }
            this.isShow = false;
            this.$emit('update:latestRef', this.selectApi);
            this.$emit('confirm', this.cbName);
        },
        cancel () {
            this.isShow = false;
            this.$emit('cancel');
        },
        onSelectCancel (selection, row) {
            const { apiId } = row;
            if (apiId === this.selectApi) {
                this.selectApi = null;
            }
        },
        onSelectHistoryApi (val, row) {
            this.selectApi = row.apiId;
            if (val.length === 1) return;
            const { APIid } = row;
            const { objData } = this.$refs.table;
            for (let i in objData) {
                if (objData[i]._isChecked && objData[i].APIid !== APIid) {
                    objData[i]._isChecked = false;
                }
            }
        }
    }
};
</script>

<style lang="less">
.historyApiModal {
  .ivu-table-header .ivu-checkbox-wrapper {
      display: none;
  }
}

</style>