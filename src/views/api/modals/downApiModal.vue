<template>
<Modal v-model="downApiFlag" title="">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
              <Icon type="ios-information-circle"></Icon>
              <span>下线API</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;">
                <i class="ivu-icon ivu-icon-help-circled"></i>
              </div>
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                <p style="color:red"></p>
                <p >您将要下线一下API： <Button id="api_mange_btn5" size="small" type="primary">{{httpMethod}}</Button>  {{interface_URI}}</p>
                <p>下线环境 : {{this.apiEnv == 'CANARY' ? '灰度' : '生产'}}</p>
                <label for="" class="reasonType"><span style="color:red;font-size: 12x;">*</span>下线原因：</label>
                <Input type="textarea" v-model="offline_api_cause" style="width:85%;font-size: 12x;margin-top:8px;" placeholder="" placeholder-style="font-size:12px;"></Input>
                <!-- <p style="color:#888;font-size:12px;margin-top:10px;margin-left:50px">最长可输入60字符</p> -->
                <p>该操作将导致生产环境该API不可用,请仔细确认</p>
              </div>
          </div>
          <div slot="footer" style="border:0;">
              <Button id="api_mange_btn6" type="ghost"  @click="hide_model">取消</Button>
              <Button id="api_mange_btn7" type="primary"  @click="confirmPublish">下线</Button>
          </div>
    </Modal>
</template>

<script>
export default {
    data () {
        return {
            downApiFlag: false,
            offline_api_cause: '',
            httpMethod: '',
            interface_URI: '',
            apiEnv: 'CANARY'
        };
    },
    methods: {
        hide_model () {
            this.downApiFlag = false;
        },
        show (params) {
            this.httpMethod = params.httpMethod;
            this.interface_URI = params.path;
            this.apiEnv = params.apiEnv;
            this.downApiFlag = true;
        },
        confirmPublish () {
            if (!this.offline_api_cause && this.apiEnv === 'PRODUCTION') {
                this.$Message.error('请输入下线原因');
                return;
            }
            this.downApiFlag = false;
            this.$emit('confirm', {
                offline_api_cause: this.offline_api_cause,
                apiEnv: this.apiEnv
            });
        }
    }
};
</script>

<style>

</style>