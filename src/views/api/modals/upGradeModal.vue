<template>
    <Modal v-model="devToProFlag" title="升级">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
              <Icon type="ios-information-circle"></Icon>
              <span>升级</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                <p style="color:red"></p>
                <p >确定将 API: <Button id="api_mange_btn8" size="small" type="primary">{{httpMethod}}</Button>  {{interface_URI}}</p>
                <p>从灰度环境升级到生产环境吗？</p>
                <p>该操作将导致生产环境的该API被覆盖，灰度环境的该API被删除，请仔细确认</p>
              </div>
          </div>
          <div slot="footer" style="border:0;">
              <Button id="api_mange_btn9" type="ghost"  @click="hide_model">取消</Button>
              <Button id="api_mange_btn10" type="primary"  @click="confirmUpGrade">升级</Button>
          </div>
    </Modal>
</template>

<script>
export default {
    data () {
        return {
            devToProFlag: false,
            httpMethod: '',
            interface_URI: ''
        };
    },
    methods: {
        hide_model () {
            this.devToProFlag = false;
        },
        show (params) {
            this.httpMethod = params.httpMethod;
            this.interface_URI = params.path;
            this.devToProFlag = true;
        },
        confirmUpGrade () {
            this.devToProFlag = false;
            this.$emit('confirm');
        }
    }
};
</script>

<style>

</style>