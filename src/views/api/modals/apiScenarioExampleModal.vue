<template>
  <Modal v-model="show" :transfer="false" width="1000">
    <p slot="header" style="color:#2d8cf0;">
      <span style="color:black">API场景示例</span>
    </p>
    <div slot="footer">
      <Button type="text" @click="show = false">取消</Button>
      <Button type="primary" :loading="loading" @click="update">确定</Button>
    </div>
    <loading :show="detailLoading"></loading>
    <ApiScenarioExample 
      v-if="!detailLoading"
      ref="apiScenarioExample"
    />
  </Modal>
</template>

<script>
import api from '~/api/newApiManage/apiScenarioExample';
import loading from "~/views/my-components/loading/loading";
import ApiScenarioExample from './apiScenarioExample'
export default {
  components: {
    loading,
    ApiScenarioExample
  },
  data() {
    return {
      show: false,
      apiId: '',
      loading: false,
      detailLoading: false
    }
  },
  methods: {
    showModal({ apiId, apiGroupCode }) {
      this.apiId = apiId
      this.getDetail()
      this.show = true
    },
    getDetail() {
      this.detailLoading = true
      api.getDetail({ apiId: this.apiId }).then(res => {
        this.detailLoading = false
        if (res.data.status === 'success') {
          const result = res.data.data.result
          this.$nextTick(() => {
            this.$refs.apiScenarioExample.setValue(result.exampleScenes)
          })
        } else {
          this.$ypMsg.notice_error(this, '错误', res.data.message)
        }
      }).catch(() => {
        this.detailLoading = false
      })
    },
    update() {
      if (!this.$refs.apiScenarioExample.saveCheck()) {
        return
      }
      this.loading = true
      api.update({
        apiId: this.apiId,
        exampleScenes: this.$refs.apiScenarioExample.getExampleScenes()
      }).then((res) => {
        this.loading = false
        if (res.status === 'success') {
          this.show = false
          this.$ypMsg.notice_success(this, '设置成功')
          this.$emit('refreshList')
        } else {
          this.$ypMsg.notice_error(this, '错误', res.message)
        }
      })
    },
  }
}
</script>