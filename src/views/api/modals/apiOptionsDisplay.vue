<template>
  <Tabs value="name1" style="width: 500px;">
    <TabPane label="幂等规则" name="name1" v-if="optionsRule.find(item => item.name === 'IDEMPOTENT')">
      <Form ref="idempotent_form_data" :label-width="110" :model="idempotent" inline>
        <FormItem
          label="是否幂等："
          class="width-100-perc"
        >
          <p>{{ idempotent.supported ? '是' : '否' }}</p>
        </FormItem>
        <div v-if="idempotent.supported">
          <FormItem
            label="幂等参数标识："
            class="width-100-perc"
          >
            {{ idempotent.useAllParams ? '全部参数' : '部分参数' }}
            <br />
            <div v-if="!idempotent.useAllParams">
              <span v-for="item in idempotent.params" :key="item">{{ item }} </span>
            </div>
          </FormItem>
          <FormItem
            label="返回错误码："
            class="width-100-perc"
          >
            {{ idempotent.hasBizError ? '是' : '否' }}
          </FormItem>
          <FormItem
            label="错误码："
            class="width-100-perc"
            v-if="idempotent.hasBizError"
          >
            {{ idempotent.bizErrorCode }}
          </FormItem>
        </div>
      </Form>
    </TabPane>
    <TabPane label="沙箱环境" name="name2" v-if="optionsRule.find(item => item.name === 'SANDBOX')">
      <Form ref="idempotent_form_data" :label-width="120" :model="sandbox" inline>
        <FormItem
          label="是否支持沙箱环境："
          class="width-100-perc"
        >
          {{ sandbox.supported ? '是' : '否' }}
        </FormItem>
      </Form>
    </TabPane>
    <TabPane label="唯一约束规则" name="name3" v-if="apiOptions.find(item => item.code === 'UNIQUE_PARAMS')">
      <Form ref="idempotent_form_data" :label-width="120" :model="sandbox">
        <FormItem
          label="是否包含全局唯一的参数："
          prop="data_select_apiGroup"
        >
          {{ uniqueParams.supported ? '是' : '否' }}
        </FormItem>
        <FormItem
          label="请求参数："
          prop="data_select_apiGroup"
        >
          <ParamsTable v-model="uniqueParams.reqParams" status="detail" />
        </FormItem>
        <FormItem
          label="响应参数："
          prop="data_select_apiGroup"
        >
          <ParamsTable v-model="uniqueParams.respParams" status="detail" />
        </FormItem>
      </Form>
    </TabPane>
    <TabPane label="同步&异步规则" name="name4" v-if="apiOptions.find(item => item.code === 'SYNC_ASYNC')">
      <Form ref="idempotent_form_data" :label-width="120" :model="sandbox">
        <FormItem
          label="调用类型："
          prop="data_select_apiGroup"
        >
          {{ invokeTypeMap[syncAsync.invokeType] }}
        </FormItem>
        <FormItem
          label="是否有补偿："
          prop="data_select_apiGroup"
        >
          {{ syncAsync.hasCompensation ? '是' : '否' }}
        </FormItem>
        <FormItem
          v-if="syncAsync.hasCompensation"
          label="补偿机制："
          prop="data_select_apiGroup"
        >
          {{ syncAsync.compensationDesc }}
        </FormItem>
        <FormItem
          label="通知机制："
          prop="data_select_apiGroup"
        >
          <ParamsTable v-model="syncAsync.notifyRule" />
        </FormItem>
      </Form>
    </TabPane>
  </Tabs>
</template>

<script>
export default {
  props: ['optionsRule'],
  data() {
    return {
      idempotent: {
        supported: false,
        useAllParams: true,
        params: [],
        hasBizError: false,
        bizErrorCode: '',
      },
      sandbox: {
        supported: false,
      },
      uniqueParams: {
        supported: true,
        reqParams: [],
        respParams: []
      },
      syncAsync: {
        invokeType: '',
        hasCompensation: false,
        compensationDesc: false,
        notifyRule: [],
      },
      invokeTypeMap: {
        SYNC: '同步',
        ASYNC: '异步',
        SYNC_ASYNC: '同步+异步',
      }
    }
  },
  watch: {
    optionsRule: {
      handler(newValue) {
        if (newValue) {
          this.init()
        }
      },
      immediate: true
    },
  },
  methods: {
    init() {
      const idempotent = this.optionsRule.find(item => item.name === 'IDEMPOTENT')
      const sandbox = this.optionsRule.find(item => item.name === 'SANDBOX')
      const uniqueParams = this.optionsRule.find(item => item.name === 'UNIQUE_PARAMS')
      const syncAsync = this.optionsRule.find(item => item.name === 'SYNC_ASYNC')
      if (idempotent) {
        this.idempotent = idempotent.config
      }
      if (sandbox) {
        this.sandbox = sandbox.config
      }
      if (uniqueParams) {
        this.uniqueParams = uniqueParams.config
      }
      if (syncAsync) {
        this.syncAsync = syncAsync.config
      }
    }
  }
}
</script>