<template>
	<div>
		<div style="margin-bottom: 20px;" v-if="status !== 'description'">
      <el-button
        size="small"
        @click="addExampleScene()"
      >
        新增场景
      </el-button>
    </div>
    <el-tabs id="scenarioExample_sortable_tabs" v-model="tabsValue"
      :closable="status !== 'description'"
      :before-leave="beforeLeave"
      @tab-remove="removeExampleScene"
    >
      <el-tab-pane
        v-for="(item, index) in exampleScenes"
        :key="item.custom_key"
        :label="item.title"
        :name="item.custom_key"
      >
      </el-tab-pane>
    </el-tabs>
		<Form ref="scenarioExampleForm" :label-width="110" :model="formData" v-if="formData">
			<FormItem label="场景名称：" prop="title" :rules="{required:true,message:'场景名称不能为空'}">
				<el-input
					v-model="formData.title"
					placeholder="请输入"
					size="small"
					style="width: 300px;"
					:disabled="status === 'description' || formData.id !== undefined"
				>
				</el-input>
			</FormItem>
			<FormItem label="场景描述：" prop="description">
				<el-input
					v-model="formData.description"
					placeholder="请输入"
					size="small"
					style="width: 300px;"
					:disabled="status === 'description'"
				>
				</el-input>
			</FormItem>
			<FormItem label="场景示例：" prop="examples"
        :rules="{ required: true, type: 'array', min: 1, message: '场景示例最少一条', trigger: 'change' }"
      >
				<ScenarioExample
					v-model="formData.examples"
          :disabled="status === 'description'"
        />
			</FormItem>
		</Form>
	</div>
</template>
<script>
import { Tabs, TabPane } from 'element-ui'
import Sortable from "sortablejs"
import {v4 as uuidv4} from 'uuid'
import ScenarioExample from './scenarioExample.vue'
export default {
  props: ['status'],
  components: {
    ElTabs: Tabs,
    ElTabPane: TabPane,
    ScenarioExample
  },
	data() {
    return {
      tabsValue: '0',
      exampleScenes: [],
      isAdd: false
		};
  },
  computed: {
    formData() {
      const item = this.exampleScenes.find(e => e.custom_key === this.tabsValue)
      return item
    }
  },
  mounted() {
    document.querySelector('#scenarioExample_sortable_tabs .el-tabs__nav').removeChild( document.querySelector('#scenarioExample_sortable_tabs .el-tabs__nav .el-tabs__active-bar'))
    this.dragSort()
  },
  methods: {
    dragSort() {
      const tbody = document.querySelector('#scenarioExample_sortable_tabs .el-tabs__nav')
      const that = this
      Sortable.create(tbody, {
        filter: '.el-tabs__active-bar', 
        onEnd({ newIndex, oldIndex }) {
          [that.exampleScenes[newIndex], that.exampleScenes[oldIndex]] = [that.exampleScenes[oldIndex], that.exampleScenes[newIndex]]
          that.exampleScenes = that.exampleScenes.filter(item => item)
          that.$forceUpdate()
        }
      })
    },
    setValue(exampleScenes) {
      this.exampleScenes = exampleScenes.map(item => {
        return {
          ...item,
          custom_key: uuidv4().substring(0, 10)
        }
      })
      if (this.exampleScenes.length > 0) {
        this.tabsValue = this.exampleScenes[0].custom_key
      }
    },
    async saveCheck() {
      let  valid = true
      if (this.$refs.scenarioExampleForm) {
        valid = await this.$refs.scenarioExampleForm.validate()
      }
      return valid
    },
    beforeLeave() {
      if (this.isAdd) {
        return true
      }
      return new Promise((resolve, reject) => {
        this.$refs.scenarioExampleForm.validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            reject(false)
          }
        })
      }) 
    },
    getExampleScenes() {
      return this.exampleScenes
    },
    async addExampleScene() {
      let  valid = true
      if (this.$refs.scenarioExampleForm) {
        valid = await this.$refs.scenarioExampleForm.validate()
      }
      if (!valid) return
      this.isAdd = true
      const custom_key = uuidv4().substring(0, 10)
      this.exampleScenes.push({
        title: '场景' + (this.exampleScenes.length + 1),
        description: '',
        examples: [
          {
            title: '',
            description: '',
            reqExample: '',
            respExample: '',
          }
        ],
        custom_key,
      })
      this.tabsValue = custom_key
      setTimeout(() => {
        this.isAdd = false
      }, 0)
    },
    removeExampleScene(custom_key) {
      const index = this.exampleScenes.findIndex(e => e.custom_key === custom_key)
      this.exampleScenes.splice(index, 1)
      if (this.tabsValue === custom_key) {
        this.tabsValue = ''
        if (this.exampleScenes.length > 0) {
          this.tabsValue =this.exampleScenes[0].custom_key
        }
      }
    }
  }
};
</script>
