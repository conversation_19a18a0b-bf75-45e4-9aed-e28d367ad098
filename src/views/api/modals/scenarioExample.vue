<template>
	<div class="scenarioExample">
    <el-table
      :data="tableData"
      border
      :cell-style="{textAlign: 'center',fontSize: '12px' }"
      :header-cell-style="{textAlign: 'center', padding: '5px', fontSize: '12px'}"
      id="scenarioExample_sortable_table"
      row-key="custom_key"
    >
      <el-table-column prop="title">
        <template slot="header">
          <span style="color: red;">*</span>示例名称
        </template>
        <template slot-scope="scope">
          <FormItem label="" :prop="`examples.${scope.$index}.title`" :rules="{required:true,message:'示例名称不能为空'}">
            <el-input
              size="small" 
              v-model="scope.row.title"
              placeholder="请输入"
              clearable
              :disabled="disabled || scope.row.id !== undefined"
            >
            </el-input>
          </FormItem>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="示例描述">
        <template slot-scope="scope">
          <el-input size="small"  v-model="scope.row.description" placeholder="请输入" clearable :disabled="disabled" />
        </template>
      </el-table-column>
      <el-table-column prop="requestExample" label="请求示例">
        <template slot-scope="scope">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
            :disabled="disabled"
            v-model="scope.row.reqExample">
          </el-input>
        </template>
      </el-table-column>
      <el-table-column prop="responseExample" label="响应示例">
        <template slot-scope="scope">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
            :disabled="disabled"
            v-model="scope.row.respExample">
          </el-input>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="操作" width="150" v-if="!disabled">
        <template slot-scope="scope">
          <Button v-if="tableData.length > 1" type="primary" @click="deleteOne(scope.$index)" size="small">移除</Button>
          <Button type="primary" @click="cloneOne(scope.$index)" size="small">复制</Button>
        </template>
      </el-table-column>
    </el-table>
    <div class="add-icon" v-if="!disabled">
      <Icon type="plus-circled" :size="20" @click="addOne"></Icon>
    </div>
	</div>
</template>
<script>
import Sortable from "sortablejs"
import {v4 as uuidv4} from 'uuid'
export default {
  props: ['value', 'disabled'],
  data () {
    return {
    }
  },
  computed: {
    tableData() {
      if (!this.value || this.value.length < 1) { 
        return []
      }
      return this.value.map(item => {
        item.custom_key = uuidv4().substring(0, 10)
        return item
      })
    }
  },
  mounted() {
    this.dragSort()
  },
  methods: {
    dragSort() {
      const tbody = document.querySelector('#scenarioExample_sortable_table tbody')
      const that = this
      Sortable.create(tbody, {
        onEnd({ newIndex, oldIndex }) {
          const arr = JSON.parse(JSON.stringify(that.value))
          const item = arr.splice(oldIndex, 1)[0]
          arr.splice(newIndex, 0, item)
          that.$emit('input', arr)
        }
      })
    },
    deleteOne(index) {
      this.value.splice(index, 1)
      this.$emit('input', this.value)
    },
    cloneOne(index) {
      const val = JSON.parse(JSON.stringify(this.value[index]))
      delete val.id
      this.value.splice(index + 1, 0, val)
      this.$emit('input', this.value)
    },
    addOne() {
      this.value.push({
        title: '',
        description: '',
        reqExample: '',
        respExample: '',
      })
      this.$emit('input', this.value)
    }
  }
}
</script>
<style lang="less" scoped>
	.scenarioExample {
		display: flex;
		align-items: flex-end;
		margin-right: 20px;
		.add-icon {
			margin-left: 10px;
			cursor: pointer;
		}
    /deep/ .el-table .cell {
      overflow: initial;
    }
	}
</style>