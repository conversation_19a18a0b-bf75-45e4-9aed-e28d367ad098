<template>
<Modal v-model="publishFlag"  title="发布api">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;border:0;">
              <Icon @click="hide_model" type="ios-information-circle"></Icon>
              <span>发布API</span>   
          </p>
          <div style="text-align:left;font-size: 12px;">
              <div style="display:inline-block;width:12%;font-size:36px;color:#f90;position:absolute;top:54px;">
                <i class="ivu-icon ivu-icon-help-circled"></i>
              </div>
              <div style="display:inline-block;margin:0 0 0 56px;width:75%">
                <p style="color:red"></p>
                <p >您将要发布以下API: <Button id="api_mange_btn11" size="small" type="primary">{{this.httpMethod}}</Button>  {{this.interface_URI}}</p>
                <p>请选择发布的环境： </p>
                <p>
                    <RadioGroup v-model="publishApiEnv" >
                        <Radio id="publish_env_1" label='CANARY'>灰度</Radio>
                        <Radio id="publish_env_2" label='PRODUCTION'>生产</Radio>
                    </RadioGroup>
                </p>
                
                <label for="" class="reasonType"><span style="color:red;font-size: 12x;">*</span>发布原因：</label>
                <Input type="textarea" v-model="publish_api_cause" style="width:85%;font-size: 12x;margin-top:8px;" placeholder="" placeholder-style="font-size:12px;"></Input>
                <!-- <p style="color:#888;font-size:12px;margin-top:10px;margin-left:50px">最长可输入60字符</p> -->
                <p>该操作将导致{{publishApiEnv == 'CANARY' ? '灰度' : '生产'}}环境下该api被覆盖，请仔细确认</p>
              </div>
          </div>
          <div slot="footer" style="border:0;">
              <Button id="api_mange_btn12" type="ghost"  @click="hide_model">取消</Button>
              <Button id="api_mange_btn13" type="primary"  @click="confirmPublish">发布</Button>
          </div>
    </Modal>
</template>

<script>
export default {
    data () {
        return {
            publishFlag: false,
            publish_api_cause: '',
            httpMethod: '',
            interface_URI: '',
            publishApiEnv: 'CANARY'
        };
    },
    methods: {
        hide_model () {
            this.publishFlag = false;
        },
        show (params) {
            this.httpMethod = params.httpMethod;
            this.interface_URI = params.interface_URI;
            this.publishApiEnv = 'CANARY';
            this.publishFlag = true;
        },
        confirmPublish () {
            if (!this.publish_api_cause) {
                this.$Message.error('请输入发布原因');
                return;
            }
            this.publishFlag = false;
            this.$emit('confirm', {
                publish_api_cause: this.publish_api_cause,
                publishApiEnv: this.publishApiEnv
            });
        }
    }
};
</script>

<style>

</style>