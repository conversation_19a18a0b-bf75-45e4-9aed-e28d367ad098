<template>
  <el-table :data="value" style="width: 100%" border>
    <el-table-column prop="names" label="参数列表">
      <template slot-scope="scope" v-if="status !== 'detail'">
        <el-select 
          v-model="scope.row.names" 
          filterable
          multiple
          placeholder="请选择"
          size="small"
          style="width: 100%"
          :disabled="disabled"
        >
          <el-option
            v-for="item in paramList"
            :key="item.name"
            :value="item.name"
            :label="item.name"
            :disabled="getDisabled(scope.row.names, item.name)"
          >
          </el-option>
        </el-select>
      </template>
    </el-table-column>
    <el-table-column prop="desc" label="约束说明">
      <template slot-scope="scope" v-if="status !== 'detail'">
        <el-input 
          v-model="scope.row.desc" 
          placeholder="请输入"
          size="small"
          clearable
          :disabled="disabled"
        >
        </el-input>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="120" v-if="!disabled || status !== 'detail'">
      <template slot-scope="scope">
        <el-button v-if="scope.$index === value.length - 1" type="text" @click="add">新增</el-button>
        <el-button type="text" @click="remove(scope.$index)">删除</el-button>
      </template>
    </el-table-column>
    <template slot="empty" v-if="!disabled || status !== 'detail'">
      <el-button type="text" @click="add">新增</el-button>
    </template>
  </el-table>
</template>

<script>
export default {
  name: 'ParamsTable',
  props: ['value', 'disabled', 'paramList', 'status'],
  data() {
    return {
      params: [],
      valueOptions: []
    };
  },
  methods: {
    add() {
			this.value.push({
				names: [],
				desc: '',
      })
		},
		remove(index) {
      this.value.splice(index, 1)
    },
    getDisabled(names, name) {
      if((names || []).includes(name)) return false
      let arr = []
      this.value.forEach(v => {
        arr.push(...v.names)
      });
      if (arr.includes(name)) return true
      return false
    }
  }
};
</script>

<style scoped>
/* Add your styles here */
</style>