<template>
  <el-table :data="value" style="width: 100%" border>
    <el-table-column prop="spi">
      <template slot="header" slot-scope="scope">
        <span style="color: red;">*</span>
        结果通知
      </template>
      <template slot-scope="scope" v-if="status !== 'detail'">
        <el-select 
          v-model="scope.row.spi" 
          filterable 
          placeholder="请选择"
          style="width: 100%"
          size="small"
          :disabled="disabled"
        >
          <el-option 
            v-for="item in callbackList" 
            :key="item" 
            :label="item" 
            :value="item"
            :disabled="value.map(v => v.spi).includes(item) && scope.row.spi !== item"
          >
          </el-option>
        </el-select>
      </template>
    </el-table-column>
    <el-table-column prop="timeliness">
      <template slot="header" slot-scope="scope">
        <span style="color: red;">*</span>
        通知时效
      </template>
      <template slot-scope="scope" v-if="status !== 'detail'">
        <el-select 
          v-model="scope.row.timeliness" 
          filterable 
          placeholder="请选择"
          style="width: 100%"
          size="small"
          :disabled="disabled"
        >
          <el-option label="T+0" value="T+0"></el-option>
          <el-option label="T+1" value="T+1"></el-option>
          <el-option label="T+N" value="T+N"></el-option>
        </el-select>
      </template>
    </el-table-column>
    <el-table-column prop="desc">
      <template slot="header" slot-scope="scope">
        <span style="color: red;">*</span>
        通知机制
      </template>
      <template slot-scope="scope" v-if="status !== 'detail'">
        <el-input 
          v-model="scope.row.desc" 
          placeholder="请输入"
          clearable
          :disabled="disabled"
        >
        </el-input>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="120" v-if="!disabled || status !== 'detail'">
      <template slot-scope="scope">
        <el-button v-if="scope.$index === value.length - 1" type="text" @click="add">新增</el-button>
        <el-button type="text" @click="remove(scope.$index)">删除</el-button>
      </template>
    </el-table-column>
    <template slot="empty" v-if="!disabled || status !== 'detail'">
      <el-button type="text" @click="add">新增</el-button>
    </template>
  </el-table>
</template>

<script>
export default {
  name: 'ParamsTable',
  props: ['value', 'disabled', 'callbackList', 'status'],
  data() {
    return {
      params: [],
    };
  },
  methods: {
    add() {
			this.value.push({
				spi: '',
				timeliness: '',
				desc: '',
      })
		},
		remove(index) {
      this.value.splice(index, 1)
		},
  }
};
</script>

<style scoped>
/* Add your styles here */
</style></div>