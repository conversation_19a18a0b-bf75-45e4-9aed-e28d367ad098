<template>
  <Modal v-model="show" :transfer="false" width="1000">
    <p slot="header" style="color:#2d8cf0;">
      <span style="color:black">API选项</span>
    </p>
    <div slot="footer">
      <Button type="text" @click="show = false">取消</Button>
      <Button type="primary" :loading="loading" @click="update">确定</Button>
    </div>
    <loading :show="detailLoading"></loading>
    <Tabs value="name1" v-if="!detailLoading">
      <TabPane label="幂等规则" name="name1" v-if="apiOptions.find(item => item.code === 'IDEMPOTENT')">
        <Form ref="idempotent_form_data" :label-width="110" :model="idempotent" inline>
          <FormItem
            label="是否幂等："
            prop="data_select_apiGroup"
          >
            <el-select
              v-model="idempotent.supported"
              placeholder="请选择"
              size="small"
              style="width: 300px;"
            >
              <el-option :value="true" label="是"></el-option>
              <el-option :value="false" label="否"></el-option>
            </el-select>
          </FormItem>
          <div v-if="idempotent.supported">
            <FormItem
              label="幂等参数标识："
              prop="data_select_apiGroup"
            >
              <el-select
                v-model="idempotent.useAllParams"
                placeholder="请选择"
                size="small"
                style="width: 300px;margin-bottom: 8px;"
                transfer
              >
                <el-option :value="true" label="全部参数"></el-option>
                <el-option :value="false" label="部分参数"></el-option>
              </el-select><br />
              <el-select
                v-model="idempotent.params"
                multiple
                placeholder="请选择"
                size="small"
                style="width: 300px;"
                v-if="!idempotent.useAllParams"
                class="idempotent-param-select"
              >
                <el-option
                  v-for="item in paramList"
                  :key="item.paramName"
                  :value="item.paramName"
                  :label="item.paramName"
                >
                </el-option>
              </el-select>
            </FormItem>
            <FormItem
              label="返回错误码："
              prop="data_select_apiGroup"
            >
              <el-select
                v-model="idempotent.hasBizError"
                placeholder="请选择"
                size="small"
                
                style="width: 300px;"
              >
                <el-option :value="true" label="是"></el-option>
                <el-option :value="false" label="否"></el-option>
              </el-select>
            </FormItem>
            <FormItem
              label="错误码："
              prop="data_select_apiGroup"
              v-if="idempotent.hasBizError"
            >
              <el-select
                v-model="idempotent.bizErrorCode"
                placeholder="请选择"
                size="small"
                filterable
                style="width: 300px;"
                v-if="errorCodeList.length > 0"
              >
                <el-option
                  v-for="item in errorCodeList"
                  :key="item.subErrorCode"
                  :value="item.subErrorCode"
                  :label="item.subErrorMsg"
                >
                </el-option>
              </el-select>
              <div v-else style="display: flex; align-items: center;">
                无错误码<Button style="margin-left: 8px;" type="ghost" size="small" @click="showAddErrorCodeModal">添加错误码</Button>
              </div>
            </FormItem>
          </div>
        </Form>
        <div v-html="msg">
        </div>
      </TabPane>
      <TabPane label="沙箱环境" name="name2" v-if="apiOptions.find(item => item.code === 'SANDBOX')">
        <Form ref="idempotent_form_data" :label-width="120" :model="sandbox" inline>
          <FormItem
            label="是否支持沙箱环境："
            prop="data_select_apiGroup"
          >
            <el-select
              v-model="sandbox.supported"
              placeholder="请选择"
              size="small"
              style="width: 300px;"
            >
              <el-option :value="true" label="是"></el-option>
              <el-option :value="false" label="否"></el-option>
            </el-select>
          </FormItem>
        </Form>
      </TabPane>
      <TabPane label="唯一约束规则" name="name3" v-if="apiOptions.find(item => item.code === 'UNIQUE_PARAMS')">
        <Form ref="idempotent_form_data" :label-width="160" :model="sandbox">
          <FormItem
            label="是否包含全局唯一的参数："
            prop="data_select_apiGroup"
          >
            <el-select
              v-model="uniqueParams.supported"
              placeholder="请选择"
              size="small"
              style="width: 300px;"
            >
              <el-option :value="true" label="是"></el-option>
              <el-option :value="false" label="否"></el-option>
            </el-select>
          </FormItem>
          <FormItem
            label="请求参数："
            prop="data_select_apiGroup"
          >
            <ParamsTable v-model="uniqueParams.reqParams" :paramList="paramList" />
          </FormItem>
          <FormItem
            label="响应参数："
            prop="data_select_apiGroup"
          >
            <ParamsTable v-model="uniqueParams.respParams" :paramList="resParamList" />
          </FormItem>
        </Form>
      </TabPane>
      <TabPane label="同步&异步规则" name="name4" v-if="apiOptions.find(item => item.code === 'SYNC_ASYNC')">
        <Form ref="idempotent_form_data" :label-width="120" :model="sandbox">
          <FormItem
            label="调用类型："
            prop="data_select_apiGroup"
          >
            <el-select
              v-model="syncAsync.invokeType"
              placeholder="请选择"
              size="small"
              style="width: 300px;"
            >
              <el-option value="SYNC" label="同步"></el-option>
              <el-option value="ASYNC" label="异步"></el-option>
              <el-option value="SYNC_ASYNC" label="同步+异步"></el-option>
            </el-select>
          </FormItem>
          <FormItem
            label="是否有补偿："
            prop="data_select_apiGroup"
          >
            <el-select
              v-model="syncAsync.hasCompensation"
              placeholder="请选择"
              size="small"
              style="width: 300px;"
            >
              <el-option :value="true" label="是"></el-option>
              <el-option :value="false" label="否"></el-option>
            </el-select>
          </FormItem>
          <FormItem
            v-if="syncAsync.hasCompensation"
            label="补偿机制："
            prop="data_select_apiGroup"
            :rules="{ required: true }"
          >
            <el-input
              v-model="syncAsync.compensationDesc"
              placeholder="请输入"
              size="small"
              style="width: 300px;"
            >
            </el-input>
          </FormItem>
          <FormItem
            label="通知机制："
            prop="data_select_apiGroup"
          >
            <RuleTable v-model="syncAsync.notifyRule" :callbackList="callbackList.map(c => c.name)" />
          </FormItem>
        </Form>
      </TabPane>
    </Tabs>
    <AddErrorCodeDia ref="addErrorCodeDia" @refreshErrorCodeList="refreshErrorCodeList" />
  </Modal>
</template>

<script>
import api from '~/api/newApiManage/idempotent';
import loading from "~/views/my-components/loading/loading";
import AddErrorCodeDia from './addErrorCodeDia'
import ParamsTable from './components/ParamsTable.vue'
import RuleTable from './components/RuleTable.vue'
export default {
  components: {
    loading,
    AddErrorCodeDia,
    ParamsTable,
    RuleTable,
  },
  data() {
    return {
      show: false,
      apiId: '',
      apiGroupCode: '',
      msg: '',
      paramList: [],
      errorCodeList: [],
      apiOptions: [],
      idempotent: {
        supported: false,
        useAllParams: true,
        params: [],
        hasBizError: false,
        bizErrorCode: '',
      },
      sandbox: {
        supported: false,
      },
      uniqueParams: {
        supported: false,
        reqParams: [],
        respParams: []
      },
      syncAsync: {
        invokeType: 'SYNC',
        hasCompensation: false,
        compensationDesc: '',
        notifyRule: [],
      },
      loading: false,
      detailLoading: false,
      callbackList: [],
      resParamList: []
    }
  },
  watch: {
    idempotent: {
      handler(newValue) {
        if (newValue && newValue.supported) {
          this.preview()
        }
      },
      deep: true
    },
  },
  methods: {
    showAddErrorCodeModal() {
      this.$refs.addErrorCodeDia.onlyAddErrorCodeModal(
        {
          APIid: this.apiId,
          APIgroupCode: this.apiGroupCode
        }
      )
    },
    showModal({ apiId, apiGroupCode, apiUri }) {
      this.init(apiId, apiGroupCode)
      this.getApiOption()
      this.getDetail()
      this.getParamList()
      this.getResParamList()
      this.getCallbacks(apiUri)
      this.getErrorCodeList()
      this.show = true
    },
    init(apiId, apiGroupCode) {
      this.msg = ''
      this.apiId = apiId
      this.apiGroupCode = apiGroupCode
      this.idempotent = {
        supported: false,
        useAllParams: true,
        params: [],
        hasBizError: false,
        bizErrorCode: '',
      }
      this.sandbox = {
        supported: false,
      }
      this.uniqueParams = {
        supported: false,
        reqParams: [],
        respParams: []
      }
      this.syncAsync = {
        invokeType: 'SYNC',
        hasCompensation: false,
        compensationDesc: '',
        notifyRule: [],
      }
    },
    getParamList() {
      api.getParamList({
        apiId: this.apiId,
        depth: 1
      }).then(res => {
        this.paramList = res.data.data.result
        if (this.paramList && this.paramList.length > 0) {
          this.paramList = this.paramList.map(item => {
            return {
              ...item,
              name: item.paramName,
              paramTitle: item.paramTitle.replace(/<[^>]+>/g,"")
            }
          })
        }
      })
    },
    getResParamList() {
      api.getResParamList({
        apiId: this.apiId,
        depth: 1
      }).then(res => {
        this.resParamList = res.data.data.result
        if (this.resParamList && this.resParamList.length > 0) {
          this.resParamList = this.resParamList.map(item => {
            return {
              ...item,
              name: item.paramName,
              paramTitle: item.paramTitle.replace(/<[^>]+>/g,"")
            }
          })
        }
      })
    },
    getCallbacks(apiUri) {
      api.getCallbacks({
        apiUri,
      }).then(res => {
        this.callbackList = res.data.data.result
      })
    },
    async refreshErrorCodeList() {
      await this.getErrorCodeList()
      this.idempotent.bizErrorCode = this.errorCodeList[0].subErrorCode
    },
    getErrorCodeList() {
      return api.getErrorCodeList({
        apiId: this.apiId,
        pageNo: 1
      }).then(res => {
        if (res.data.status === 'success') {
          this.errorCodeList = res.data.data.page.items
        } else {
          this.$ypMsg.notice_error(this, '错误', res.data.message)
        }
      })
    },
    getApiOption() {
      api.getApiOption().then(res => {
        if (res.data.status === 'success') {
          this.apiOptions = res.data.data.result
        } else {
          this.$ypMsg.notice_error(this, '错误', res.data.message)
        }
      })
    },
    getDetail() {
      this.detailLoading = true
      api.getDetail({ apiId: this.apiId }).then(res => {
        if (res.data.status === 'success') {
          const result = res.data.data.result
          const idempotent = result.find(item => item.name === 'IDEMPOTENT')
          const sandbox = result.find(item => item.name === 'SANDBOX')
          const uniqueParams = result.find(item => item.name === 'UNIQUE_PARAMS')
          const syncAsync = result.find(item => item.name === 'SYNC_ASYNC')
          if (idempotent) {
            this.idempotent = {
              ...this.idempotent,
              ...idempotent.config
            }
          }
          if (sandbox) {
            this.sandbox = sandbox.config
          }
          if (uniqueParams) {
            this.uniqueParams = uniqueParams.config
          }
          if (syncAsync) {
            this.syncAsync = syncAsync.config
          }
        } else {
          this.$ypMsg.notice_error(this, '错误', res.data.message)
        }
        this.detailLoading = false
      })
    },
    preview() {
      api.preview({
        apiId: this.apiId,
        apiOption: {
          name: 'IDEMPOTENT',
          config: this.idempotent
        }
      }).then((res) => {
        if (res.status === 'success') {
          this.msg = res.data.result
        }
      })
    },
    update() {
      this.loading = true
      api.update({
        apiId: this.apiId,
        optionsRule: [
          {
            name: 'IDEMPOTENT',
            config: this.idempotent
          },
          {
            name: 'SANDBOX',
            config: this.sandbox
          },
          {
            name: 'UNIQUE_PARAMS',
            config: this.uniqueParams
          },
          {
            name: 'SYNC_ASYNC',
            config: this.syncAsync
          }
        ]
      }).then((res) => {
        this.loading = false
        if (res.status === 'success') {
          this.show = false
          this.$ypMsg.notice_success(this, '设置成功')
          this.$emit('refreshList')
        } else {
          this.$ypMsg.notice_error(this, '错误', res.message)
        }
      })
    },
  }
}
</script>