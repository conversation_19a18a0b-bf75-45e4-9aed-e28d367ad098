<style lang="less">
    .width-50-perc{
        width:45%;
    }
    .width-100-perc{
        width:100%;
    }
    .margin-top-0{
        margin-top : 0!important;
    }
     .max_min .ivu-form-item-error-tip {
        white-space: nowrap!important;
    }
</style>
<template>
    <Row>
        <Col span="12">
        <!--<Card>-->
        <Form :ref="'form_data_left'+this.depth" :model="form_data" label-position="top" inline>
            <FormItem class="width-50-perc" label="类型：" prop="type" :rules="{required:true,message:'类型不能为空'}">
                <Cascader :data="type_data_cut" v-model="form_data.type" style="width:170px" @on-change="cascader_change"></Cascader>
            </FormItem>
           
            <FormItem v-if="this.showRef" label="models:" class="width-50-perc" prop="model" :rules="{required:true,message:'model不能为空'}">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>模型：</p>
                    <Select ref="modal_apiM_select_5" id='modal_apiM_select_5' size="small" v-model="form_data.model" style="width:170px"  placeholder="请选择">
                        <Option v-for="item in data_api_model_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
                    </Select>
                    <p style="color: #2d8cf0;cursor: pointer;" @click="watchModelDetail">查看详情</p>
            </FormItem>

            <FormItem v-if="!this.showRef" class="width-100-perc" label="描述：" prop="description">
              <TextEditor ref="textEditor" size="350px" :id="id" :restrictElements="true" />
            </FormItem>
        </Form>
        <!--</Card>-->
        </Col>
        <Col span="12">
        <!--<Card>-->
        <Form :ref="'form_data_right'+this.depth" :model="form_data" :rules="form_data_right"  label-position="top" inline>
            <FormItem
               v-if="
                this.more_attr 
                && this.form_data.type[0] != 'object' 
                && this.form_data.type[0] != 'array' 
                && this.form_data.type[1] != 'enum'
              "
              label="示例值："
              prop="example"
              class="width-50-perc"
            >
              <Input type="text" v-model="form_data.example" @on-change="onExampleChange"></Input>
              <p style="color: #999;white-space: nowrap;">勾选“设为默认值”后，会同时作为默认值进行文档展示。</p>
            </FormItem>
            <FormItem
              v-if="
                this.more_attr 
                && this.form_data.type[0] != 'object' 
                && this.form_data.type[0] != 'array' 
                && this.form_data.type[1] != 'enum'
              "
              label=""
              prop="default"
              class="width-50-perc"
            >
              <div style="padding-top: 22px">
                <Checkbox
                  :value="!!form_data.default && form_data.default === form_data.example"
                  @on-change="onDefaultChange"
                  :disabled="!form_data.example"
                >
                设为默认值
                </Checkbox>
              </div>
            </FormItem>
            <FormItem v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc max_min" label="最大值：" prop="maximum">
                <Input type="text" v-model="form_data.maximum"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc" label="包含最大值：" prop="exclusiveMaximum">
                <i-switch size="large"  :true-value="false"  :false-value="true" v-model="form_data.exclusiveMaximum">
                    <Icon slot="open"></Icon>
                    <Icon slot="close"></Icon>
                </i-switch>
            </FormItem>
            <FormItem  v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc max_min" label="最小值：" prop="minimum">
                <Input type="text" v-model="form_data.minimum"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc" label="包含最小值：" prop="exclusiveMinimum">
                <i-switch size="large" :true-value="false"  :false-value="true" v-model="form_data.exclusiveMinimum">
                    <Icon slot="open"></Icon>
                    <Icon slot="close"></Icon>
                </i-switch>
            </FormItem>
            <FormItem  v-show="this.form_data_visual.length && this.more_attr" class="width-50-perc max_min" label="最大长度：" prop="maxLength">
                <Input type="text" v-model="form_data.maxLength"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem  v-show="this.form_data_visual.length && this.more_attr" class="width-50-perc max_min" label="最小长度：" prop="minLength">
                <Input type="text" v-model="form_data.minLength"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem  v-show="this.form_data_visual.itemSize && this.more_attr" class="width-50-perc max_min" label="最大数目：" prop="maxItems">
                <Input type="text" v-model="form_data.maxItems"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem  v-show="this.form_data_visual.itemSize && this.more_attr" class="width-50-perc max_min" label="最小数目：" prop="minItems">
                <Input type="text" v-model="form_data.minItems"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem v-show="this.form_data_visual.pattern && this.more_attr" label="正则表达式校验："  class="width-100-perc" prop="pattern">
                <Input type="text" v-model="form_data.pattern"
                       style="width:55%"></Input>
                <common-select ref="select_common_pattern" @on-update="updateSelect_common_pattern"
                               type="normal"
                               keyWord="result"
                               holder="常用正则表达式"
                               code="value"
                               title="title"
                               group="api_common_pattern"
                               @on-loaded="select_callBack"
                               :default="this.data_select_pattern"
                               :uri="this.$store.state.select.api_common_pattern.uri"
                               style="width:30%;margin-top:0"></common-select>
            </FormItem>
            <FormItem v-show="this.form_data_visual.enums && this.more_attr" class="width-100-perc" label="枚举：" prop="enum">
                <AddEnum ref="addEnum" />
            </FormItem>

        </Form>
        </Col>
    </Row>
</template>

<script>
import { nanoid } from 'nanoid'
import commonSelect from '../../common-components/select-components/selectCommon';
import api from '../../../api/api'
import label_icon from '../../common-components/icon-components/star_red'
import AddEnum from '../../model/modify_models/AddEnum.vue'
import TextEditor from '../../my-components/text-editor/text-editor-size';
export default {
  name: 'param_main_content_model_map',
  components: {
    TextEditor,
    commonSelect,
    label_icon,
    AddEnum
  },
  props: {
    depth: Number,
    type_format: Array,
    type_format_cut: Array,
    apiGroupCode: String
  },
  data () {
    const validate_number = (rule, value, callback) => {
      if (value === '' || value === undefined) {
        callback();
      } else
      if (isNaN(value * 1)) {
        callback(new Error('请输入数字'));
      } else if (this.form_data.maximum && this.form_data.minimum) {
        if (this.form_data.maximum * 1 < this.form_data.minimum * 1) {
          callback(new Error('最小值应不大于最大值'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validate_number_1 = (rule, value, callback) => {
      if (value === '' || value === undefined) {
        callback();
      } else
      if (!(/^[1-9]\d*$/.test(value * 1))) {
        callback(new Error('请输入正整数'));
      } else if (this.form_data.maxItems && this.form_data.minItems) {
        if (this.form_data.maxItems * 1 < this.form_data.minItems * 1) {
          callback(new Error('最小数目应不大于最大数目'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validate_number_2 = (rule, value, callback) => {
      if (value === '' || value === undefined) {
        callback();
      } else
      if (!(/^[1-9]\d*$/.test(value * 1))) {
        callback(new Error('请输入正整数'));
      } else if (this.form_data.maxLength && this.form_data.minLength) {
        if (this.form_data.maxLength * 1 < this.form_data.minLength * 1) {
          callback(new Error('最小长度应不大于最大长度'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validate_enum_2 = (rule, value, callback) => {
      if (this.form_data.type[1] != 'enum') {
        callback()
      }
      if (value.length < 1) {
        callback(new Error('枚举值至少填写一个'))
      }
      if (value.length !== [...new Set(value)].length) {
        callback(new Error('枚举值参数不能重复'))
      }
      callback()
    }
    return {
      form_data_right: {
        maximum: [
          {required: false, validator: validate_number, trigger: 'blur,change'}
        ],
        minimum: [
          {required: false, validator: validate_number, trigger: 'blur,change'}
        ],
        maxItems: [
          {required: false, validator: validate_number_1, trigger: 'blur,change'}
        ],
        minItems: [
          {required: false, validator: validate_number_1, trigger: 'blur,change'}
        ],
        default: [
          {required: false}
        ],
        param_name: [
          {required: false}
        ],
        exclusiveMaximum: [
          {required: false}
        ],
        exclusiveMinimum: [
          {required: false}
        ],
        maxLength: [
          {required: false, validator: validate_number_2, trigger: 'blur,change'}
        ],
        minLength: [
          {required: false, validator: validate_number_2, trigger: 'blur,change'}
        ],
        pattern: [
          {required: false}
        ],
        enum: [
          {required: false, validator: validate_enum_2, trigger: 'submit'}
        ]

      },
      data_api_model_list: [],
      showRef: false,
      // 表格内容数据绑定
      form_data: {
        // old
        // param: '', // 参数
        // name: '', // 名称
        // type: [], // 类型
        // sample: '', // 示例值
        // description: '', // 描述
        // required: false, // 是否必填

        // new
        // param: '', // 参数
        title: '', // 名称
        type: [], // 类型
        sample: '', // 示例值
        description: '', // 描述
        required: false, // 是否必填
        deprecated: false, // 是否废弃
        // new
        internal: false, // 参数
        default: '', // 默认值
        sensitive: false, // 名称
        param_index: '', // 类型
        param_name: '', // 示例值
        maximum: '', // 最大值
        exclusiveMaximum: true, // 包含最大值
        minimum: '', // 最小值
        exclusiveMinimum: true, // 包含最小值，
        pattern: '', // 正则表达式
        maxLength: '', // 最大长度
        minLength: '', // 最小长度
        maxItems: '', // 最大数目
        minItems: '', // 最小数目
        orderNo: '', // 业务订单号
        model: '',
        enum: [],
        example: ''
        // constrains:""
      },

      // 表格显示绑定
      form_data_visual: {
        extremum: false,
        enums: false,
        length: false,
        pattern: false,
        itemSize: false,
        model: false,
        additionalProperties: false,
        items: false,
        type: false,
        $ref: false
      },
      current_panel: 'create_request_json',
      // 窗口是否展示
      modal_show: false,
      // 示例值绑定
      data_select_sample: '',
      // 常用正则表达式数据绑定
      data_select_pattern: '',
      // 枚举数据绑定
      enum_data: [{
        value: '',
        index: 0,
        status: 1
      }],
      enum_index: 0,
      // 是否展示更多属性 false 为隐藏 true 为显示
      more_attr: true,

      type_data: [],
      type_data_cut: [],
      sample_data: [
        {
          value: 'integer',
          desc: 'integer',
          formats: [
            {
              value: 'int32',
              desc: 'int32',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            },
            {
              value: 'int64',
              desc: 'int64',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            }
          ]
        },
        {
          value: 'number',
          desc: 'number',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            },
            {
              value: 'float',
              desc: 'float',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            },
            {
              value: 'double',
              desc: 'double',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            }
          ]
        },
        {
          value: 'boolean',
          desc: 'boolean',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [

              ]
            }
          ]
        },
        {
          value: 'string',
          desc: 'string',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [
                'length',
                'pattern'
              ]
            },
            {
              value: 'merchant-no',
              desc: 'merchant-no',
              defaulted: true
            },
            {
              value: 'binary',
              desc: 'binary',
              defaulted: true
            },
            {
              value: 'date',
              desc: 'date',
              defaulted: true
            },
            {
              value: 'date-time',
              desc: 'date-time',
              defaulted: true
            },
            {
              value: 'password',
              desc: 'password',
              defaulted: true
            },
            {
              value: 'email',
              desc: 'email',
              defaulted: true
            },
            {
              value: 'mobile',
              desc: 'mobile',
              defaulted: true
            },
            {
              value: 'idcard',
              desc: 'idcard',
              defaulted: true
            },
            {
              value: 'bankcard',
              desc: 'bankcard',
              defaulted: true
            },
            {
              value: 'cvv',
              desc: 'cvv',
              defaulted: true
            },
            {
              value: 'uuid',
              desc: 'uuid',
              defaulted: true
            },
            {
              value: 'notify-url',
              desc: 'notify-url',
              defaulted: true
            },
            {
              value: 'error-code',
              desc: 'error-code',
              defaulted: true
            },
            {
              value: 'error-msg',
              desc: 'error-msg',
              defaulted: true
            },
            {
              value: 'enum',
              desc: 'enum',
              defaulted: true,
              constrains: [
                'enums'
              ]
            },
            {
              value: 'byte',
              desc: 'byte',
              defaulted: true
            }
          ]
        }

      ],
      modelCurrent: '',
      id: ''
    };
  },
  created () {
    this.id = 'endt' + nanoid(5)
  },
  methods: {
    watchModelDetail () {
      if (!this.form_data.model) return
      const model = this.data_api_model_list.find(item => item.name === this.form_data.model)
      this.$emit('detailModel', {
        ...model,
        apiGroup: this.apiGroupCode
      })
    },
    onExampleChange () {
      if (this.form_data.default) {
        this.form_data.default = this.form_data.example
      }
    },
    onDefaultChange (value) {
      if (value) {
        this.$set(this.form_data, 'default', this.form_data.example)
      } else {
        this.form_data.default = ''
      }
    },
    setEnumAndDesc () {
      const { description } = this.form_data
      if (description) {
        const descList = description.split('可选项如下:\n')
        this.form_data.description = descList[0] || ' '
        const enumParams = {
          enum: this.form_data.enum || [],
          default: this.form_data.default || '',
          description: descList[1]
        }
        this.$refs.textEditor.setContent(this.form_data.description)
        this.$refs.addEnum.setData(enumParams)
      } else {
        this.$refs.textEditor.setContent(' ')
        this.$refs.addEnum.setData({
          default: null,
          description: ''
        })
      }
    },
    saveEnumAndDesc () {
      if (!this.showRef) {
        this.form_data.description = this.$refs.textEditor.getContent()
      }
      if (this.form_data.type[1] != 'enum') return
      const enumParams = this.$refs.addEnum.getData()
      this.form_data.enum = enumParams.enum
      this.form_data.default = enumParams.default
      this.form_data.example = enumParams.default
      this.form_data.description = this.form_data.description + enumParams.description
    },
    // 页面初始化
    init () {
      this.data_format(this.sample_data);
      // this.content_status = this.newItem
      if (this.depth < 2) {
        const type_temp = this.type_format;
        this.type_data = type_temp;
        // this.type_data_cut = this.type_format_cut;
        this.form_data_visual.type = false;
      } else {
        // this.type_format_handler(this.type_format_cut);
        this.form_data_visual.type = true;
        // this.type_data
      }
      this.cascader_format();
    },
    // 重置select
    resetSelect () {
      // this.$refs.modal_apiM_select_3.clearSingleSelect()
      // this.$refs.modal_apiM_select_4.clearSingleSelect()
      // this.$refs.select_common_default.resetSelected();
      this.$refs.select_common_pattern.resetSelected();
      this.enum_data = [{
        value: '',
        index: 0,
        status: 1
      }]
      this.enum_index = 0
    },
    getModelsList (param) {
      api.yop_modalManagement_modal_simple_list(param).then(
        (response) => {
          var response = response.data
          this.data_api_model_list = [];
          let apigroup = response.data.result;
          // this.data_api_model= apigroup[0].groupCode;
          for (var i in apigroup) {
            this.data_api_model_list.push(
              {
                label: apigroup[i].description + '(' + apigroup[i].name + ')',
                value: apigroup[i].name,
                ...apigroup[i]
              }
            )
          }
          if (this.modelCurrent) {
            this.form_data.model = this.modelCurrent;
          }
        }
      );
    },
    set_showRef_value (val) {
      this.showRef = val
    },
    // 下拉框赋值
    type_format_handler (data) {
      let temp = data;
      for (let i = temp.length - 1; i >= 0; i--) {
        if (temp[i].value === 'object' || temp[i].value === 'array') {
          temp.splice(i, 1);
        }
      }
      this.type_data_cut = temp;
    },
    // 表单取消
    cancel () {
      this.modal_show = false;
    },
    // 窗口显示
    show () {
      this.modal_show = true;
    },
    // 数据初始化
    form_data_init () {

    },
    // 数据处理
    data_format (array) {
      this.type_data_cut = [
        {
          value: '$ref',
          label: '$ref',
          children: [{
            value: '$ref',
            label: '$ref',
            constrains: []
          }]
        }
      ];
      for (var i in array) {
        let subContent = array[i].formats;
        let childrenTemp = []
        for (var j in subContent) {
          if (subContent[j].value === '-') {
            childrenTemp.push(
              {
                value: array[i].value,
                label: array[i].desc,
                constrains: subContent[j].constrains ? subContent[j].constrains : []
              }
            );
          } else {
            childrenTemp.push(
              {
                value: subContent[j].value,
                label: subContent[j].desc,
                constrains: subContent[j].constrains ? subContent[j].constrains : []
              }
            );
          }
        }
        this.type_data_cut.push(
          {
            value: array[i].value,
            label: array[i].desc,
            children: childrenTemp
          }
        )
      }
    },
    // 级联 参数类型 数据获取及处理
    cascader_format () {

    },
    // 常用默认值 更新
    updateSelect_common_default (val) {
      this.data_select_sample = val;
      this.form_data.default = val;
    },
    // 常用正则表达式 更新
    updateSelect_common_pattern (val) {
      this.data_select_pattern = val;
      this.form_data.pattern = val;
    },
    //
    updateSelect_model (val) {

    },
    // 加载完成回调函数
    select_callBack () {

    },
    // 枚举删除函数
    enumRemove (index) {
      this.enum_data[index].status = 0;
    },
    // 枚举改变函数
    enum_change (index) {
      if (this.enum_data[index].value !== '') {
        if (index === this.enum_index) {
          this.enum_index++;
          this.enum_data.push({
            value: '',
            index: this.enum_index,
            status: 1
          })
        }
      }
    },
    // 判断枚举值是否为最后一个
    enum_last_check (index) {
      if (index === this.enum_index) {
        return true;
      } else {
        for (var i = index; i < this.enum_index; i++) {
          if (this.enum_data[i].status === 1) {
            return false
          }
        }
        return true
      }
    },
    // 枚举值统计
    enum_result () {
      let result = [];
      for (var i in this.enum_data) {
        if (this.enum_data[i].status === 1 && this.enum_data[i].value !== '') {
          result.push(this.enum_data[i].value);
        }
      }
      return result;
    },
    // 类型选定 界面展示
    cascader_change (value, data) {
      this.form_data.format = value[1]
      this.form_data.type = JSON.parse(JSON.stringify(value))
      this.type_rule_init();

      if (value[0] == '$ref') {
        this.showRef = true
        var param = {apiGroup: this.apiGroupCode}
        this.modelCurrent = ''
        this.getModelsList(param);
        this.form_data.constrains = ['$ref']
      } else {
        this.form_data.model = ''
        this.modelCurrent = ''
        this.form_data.$ref = ''
        this.showRef = false
        this.form_data.constrains = data[1].constrains
      }

      this.$refs['form_data_right' + this.depth].resetFields();
      this.resetSelect()
      // this.more_attr = false;
      if (value && value.length > 0) {
        let visual_items = data[1].constrains
        if (visual_items && visual_items.length > 0) {
          for (var i in visual_items) {
            this.form_data_visual[visual_items[i]] = true;
          }
        }
      }
    },
    form_data_set (data) {
      var dataTemp = data
      var data = dataTemp.items
      this.form_data = data
      if (typeof data.type == 'string') {
        var format = data.format ? data.format : data.type
        this.form_data.type = data.type = [data.type, format]
        this.form_data.format = format
      }
      this.type_rule_init();

      if (data.type) {
        if (data.type[0] == '$ref') {
          this.form_data.constrains = ['$ref']
          data.constrains = ['$ref']
          this.showRef = true
          this.modelCurrent = data.model
          var param = {apiGroup: this.apiGroupCode}
          this.getModelsList(param);
        } else if (data.type[0] == 'boolean') {
          this.form_data.constrains = ['boolean']
          this.showRef = false
          this.modelCurrent = ''
        } else {
          this.showRef = false
          this.modelCurrent = ''
        }
      } else {
        if (data.$ref) {
          this.showRef = true
          this.form_data.constrains = data.constrains
          this.modelCurrent = data.$ref.split('/').pop();
          this.form_data.type = ['$ref', '$ref']
          var param = {apiGroup: this.apiGroupCode}
          this.getModelsList(param);
        } else {
          this.showRef = false
        }
      }
      if (this.form_data.format == 'int32' || this.form_data.format == 'int64' || this.form_data.format == 'number' || this.form_data.format == 'float' || this.form_data.format == 'double' || this.form_data.format == 'double') {
        data.constrains = ['extremum']
      } else if (this.form_data.format == 'string') {
        data.constrains = ['length', 'pattern']
      }
      if (data.constrains && data.constrains.length > 0) {
        let visual_items = data.constrains
        if (visual_items && visual_items.length > 0) {
          for (var i in visual_items) {
            this.form_data_visual[visual_items[i]] = true;
          }
        }
      }
      this.setEnumAndDesc()
    },
    // 所有校验规则初始化
    type_rule_init () {
      for (var i in this.form_data_visual) {
        this.form_data_visual[i] = false;
      }
      this.showRef = false
    },
    // 展开按钮点击
    attr_expand  () {
      this.more_attr = true;
    },
    // 隐藏按钮点击
    attr_hide () {
      this.more_attr = false;
    },
    // 提交校验
    submit () {
      this.saveEnumAndDesc()
      this.form_submit_validate('form_data_left' + this.depth, 'form_data_right' + this.depth)
    },
    // 继续验证
    next_validate () {
      // if(this.form_data_visual.additionalProperties){
      //     this.$refs['map_'+this.depth+1].submit();
      // }else if(this.form_data_visual.items){
      //     this.$refs['array_'+this.depth+1].submit();
      // }else{
      // 新建或者编辑操作执行
      var data = JSON.parse(JSON.stringify(this.form_data))
      this.$emit('getSubContent', data, 'map');
      // }
    },
    form_submit_validate (val1, val2) {
      let result1 = 0;
      let result2 = 0;
      let check = 0;
      this.$refs[val1].validate((valid) => {
        if (valid) {
          result1 = 1;
          if (result2 === 1) {
            this.next_validate();
          }
        } else {
          this.form_validate_failed(result2)
        }
      })
      this.$refs[val2].validate((valid) => {
        if (valid) {
          result2 = 1;
          if (result1 === 1) {
            this.next_validate();
          }
        } else {
          this.form_validate_failed(result1)
        }
      })
    }
  },
  mounted () {
    this.init();
  }
};
</script>

<style scoped>

</style>
