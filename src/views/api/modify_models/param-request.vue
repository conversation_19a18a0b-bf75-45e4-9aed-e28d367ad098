<style lang="less">
    @import '../../../styles/common.less';
    @import '../api_Mangement_Open.less';
    @import '../../regression-test/regression.less';
    .param-request td.ivu-table-expanded-cell {
        padding: 10px 10px !important;
        background: #fff;
    }
    .param-request .flex-con {
      .ivu-form-item-content {
        display: flex;
        align-items: center;
        .link-dto {
          margin-left: 10px;
        }
      }
    }
    .edit-dto {
      margin-left: 10px;
    }

</style>
<template>
    <Row class-name="param-request">
        <Card dis-hover>
            <Row>
                <Form ref="form_data"  :model="form_data" :label-width="120" inline >

                            <Col span="24">
                            <Row>
                                <template v-if="current_status == 'create'">
                                    <FormItem key="create-api-req1" label="请求路径：" class="inlineBlock" prop="requestVersion" :rules="{required:true,validator:this.validate_version,message:'请输入正确格式：/(rest|yos)/v([1-9][0-9]*).(0|[1-9][0-9]*)/([a-z][a-z0-9\\-]*)*[a-z0-9]+(/([a-z][a-z0-9\\-]*)*[a-z0-9]+)+'}">
                                        <Input id="inputRequsetVersion"  size="small" type="text" v-model="form_data.requestVersion"  style="width:auto" class="spiName" >
                                            <span slot="prepend"  class="white">{{form_data.prependPath}}</span>
                                        </Input>
                                    </FormItem>
                                    <FormItem key="create-api-req2" label=""  class="inlineBlock fixMargin" prop="requestUrl" :rules="{required:true,validator:this.validate_version,message:''}">
                                        <Input id="inputRequsetPath"  size="small" type="text" v-model="form_data.requestUrl" @on-blur="checkPath" style="width:292px" class="spiName" >
                                            <span slot="prepend"  class="white">{{form_data.prependApiGroup}}</span>
                                        </Input>
                                    </FormItem>
                                    <span key="create-api-span" style="color:gray">eg：/rest/v1.0/{api-group}/{api}  推荐 中划线 连接单词</span>
                                </template>
                                <template v-if="current_status == 'modify'">
                                    <FormItem  key="modify-api-req" label="请求路径：" class="inlineBlock" prop="requestVersion">
                                        <Input disabled size="small" type="text" v-model="form_data.requestUrl"  style="width:380px" class="spiName_gray" >
                                        </Input>
                                    </FormItem>
                                </template>
                                <template v-if="current_status == 'description'">
                                    <FormItem key="description-api-req" label="请求路径：" class="inlineBlock" prop="requestVersion">
                                        <p>{{form_data.requestUrl}}</p>
                                    </FormItem>
                                </template>
                            </Row>
                            <Row v-if="showhttpMethod">
                                <FormItem label="HTTP请求方法：" class="width-100-perc" prop="httpMethod" :rules="{required:true,message:'HTTP请求方法不能为空'}">

                                    <Select v-show="this.current_status !== 'description'" id="selectHTTPMethod" ref="select_httpMethod" :disabled="this.current_status == 'modify' ? 'disabled' : false"  size="small" v-model="form_data.httpMethod" style="width:380px" placeholder="请输入" @on-change="setContentType">
                                        <Option v-for="(item,index) in httpMethod_list" :value="item.value" :key="index">{{ item.label }}</Option>
                                    </Select>
                                    <p  v-if="this.current_status == 'description'">{{form_data.httpMethod}}</p>
                                </FormItem>

                            </Row>
                            <!-- <Row>
                                <FormItem label="请求体：" class="width-100-perc" prop="requestBody" :rules="{required:true,message:'请求方法不能为空'}">
                                    <Input v-if='false'  size="small" type="text" v-model="form_data.requestBody" disabled style="width:100%"></Input>
                                </FormItem>
                            </Row> -->
                            <Row>

                                <FormItem  class="width-100-perc"  label="contentType：" prop="contentType" :rules="{required:true,message:'contentType不能为空'}">
                                    <Select  v-show="this.current_status !== 'description'" id="selectContentType" ref="select_contentType" :disabled="this.current_status == 'modify' ? 'disabled' : false "  size="small" v-model="form_data.contentType" style="width:380px" placeholder="请输入" :clearable="this.current_status == 'modify' ? false : true" @on-change="content_type">
                                        <Option v-for="(item,index) in contentType_list" :value="item.value" :key="item.value">{{ item.value }}</Option>
                                    </Select>
                                    <p  v-if="this.current_status == 'description'">{{form_data.contentType}}</p>
                                </FormItem>
                                <!-- <FormItem  class="width-100-perc"  label="入参请求模式：" prop="queryType" :rules="{required:true,message:'入参请求模式不能为空'}">
                                    <Select v-show="this.current_status !== 'description'" id="selectParameterModel" ref="select_queryType" :disabled="this.current_status == 'description' ? 'disabled':false"  size="small" v-model="form_data.queryType" style="width:380px" placeholder="请输入"  @on-change="query_type">
                                        <Option v-for="(item,index) in queryType_list" :value="item.value" :key="index">{{ item.label }}</Option>
                                    </Select>
                                    <p  v-if="this.current_status == 'description'">{{queryTypeText}}</p>

                                </FormItem> -->
                                <Row>
                                    <FormItem label="是否加密：" class="width-100-perc">
                                        <Checkbox
                                          v-model="form_data.encrypt"
                                          :disabled="current_status == 'description' ? 'disabled' : false"
                                        />
                                    </FormItem>
                                </Row>
                                <Row v-if="paramPositionShow == 'query'">
                                    <p style="font-size: 12px; padding-left: 49px;line-height:30px;">参数列表：</p>
                                    <Button style="margin-left:120px;"  id="btnAddProperty" v-if="current_status !=='description'" class="margin-bottom-10" type="primary"
                                            @click="new_param('param_operate',true)">新增参数
                                    </Button>
                                    <Table  style="margin-left:120px;" id='table_1' border ref="properties" :columns="current_status !=='description' ? columns_propertiesList: columns_propertiesList_detail" :data="data_propertiesList"></Table>
                                    <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                                        <Page class="margin-top-10" style="float: right" :total="20" :page-size="20" :current="1" show-elevator @on-change=""></Page>
                                    </Tooltip> -->
                                </Row>
                                <Row  v-show="paramPositionShow == 'body_schema'">
                                    <FormItem v-show="!modelDisabled" label="content：" class="width-50-perc flex-con" prop="model" :rules="{required:paramPositionShow == 'body'? true : false, message:'content不能为空'}">
                                        <Select id="selectParameterContent" :disabled="this.current_status == 'description' ? 'disabled': false" ref="modal_apiM_select_4" size="small" v-model="form_data.model" style="width:380px" placeholder="请输入">
                                            <Option v-for="(item,index) in data_api_model_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
                                        </Select>
                                        <Button v-show="this.current_status !== 'description'" class="edit-dto" @click="seeDto(form_data.model, 'edit')" type="primary">修改</Button>
                                        <Button class="link-dto" v-if="this.current_status == 'description' ? 'disabled': false" @click="seeDto(form_data.model, 'detail')" type="primary">查看</Button>
                                    </FormItem>
                                    <FormItem v-show="modelDisabled" label="content：" class="width-100-perc" prop="model">
                                      <Input disabled style="width:380px" v-model='data_propertiesStr'
                                      ></Input>
                                      <!-- <Button class="link-dto" @click="seeDto(form_data.model)" type="primary">查看4</Button> -->
                                      <!-- <TableModelContent type="reqSchema" ref="tableModelContent" />-->
                                    </FormItem>
                                    <FormItem  class="width-100-perc" label="描述：" prop='description'  :rules="{required:paramPositionShow == 'body'? true : false, message:'描述不能为空'}">
                                        <Input id="inputParameterDes_1" v-if="this.current_status !== 'description'" type="textarea" style="width:380px" v-model='form_data.description'></Input>
                                        <p  v-if="this.current_status == 'description'">{{form_data.description}}</p>

                                    </FormItem>
                                    <p style="font-size: 12px; padding-left: 52px;line-height:30px;">example：</p>

                                    <Button v-if="this.current_status !== 'description'" type="primary" style="margin-bottom: 20px;margin-left:120px;"  @click="add_example">新增example</Button>
                                    <Table style="margin-left:120px;" id='table_2' border ref="selection" :columns="this.current_status !== 'description'?spi_example_List:spi_example_List_display" :data="data_spi_example_list" @on-selection-change=""></Table>
                                    <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                                        <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                                    </Tooltip> -->

                                </Row>
                                <Row  v-if="paramPositionShow == 'body_query'">
                                    <!-- <FormItem style="margin-top: 20px;" class="width-100-perc" label="描述：" prop='description'  :rules="{required:paramPositionShow == 'body'?true:false,message:'描述不能为空'}">
                                        <Input id="inputParameterDes_2"  v-if="this.current_status !== 'description'" type="textarea" v-model='form_data.description'
                                            style="width:200px"></Input>
                                        <p  v-if="this.current_status == 'description'">{{form_data.description}}</p>

                                    </FormItem> -->
                                    <p style="font-size: 12px; padding-left: 49px;line-height:30px;">参数列表：</p>
                                    <Button style="margin-left:120px;" v-if="current_status !=='description'" class="margin-bottom-10" type="primary"
                                            @click="new_param('param_operate',true)">新增参数
                                    </Button>
                                    <Table  style="margin-left:120px;" id='table_1' border ref="properties" :columns="current_status !=='description'?columns_propertiesList:columns_propertiesList_detail" :data="data_propertiesList">

                                    </Table>
                                    <p style="font-size: 12px;padding-left: 52px;line-height:30px;">example：</p>
                                    <Button v-if="this.current_status !== 'description'" type="primary" style="margin-bottom: 10px;margin-left:120px;"  @click="add_example">新增example</Button>
                                    <Table  style="margin-left:120px;" id='table_2' border ref="selection" :columns="this.current_status !== 'description'?spi_example_List:spi_example_List_display" :data="data_spi_example_list" ></Table>
                                </Row>
                                <Row  v-show="paramPositionShow == 'binary'">
                                    <FormItem label="模型：" class="width-50-perc">
                                       string/binary
                                    </FormItem>
                                    <!-- <FormItem  class="width-100-perc" label="描述：" prop='description'  :rules="{required:paramPositionShow == 'body'?true:false,message:'描述不能为空'}">
                                        <Input id="inputParameterDes_3"  v-if="this.current_status !== 'description'" type="textarea" v-model='form_data.description'
                                            style="width:200px"></Input>
                                        <p  v-if="this.current_status == 'description'">{{form_data.description}}</p>

                                    </FormItem> -->
                                </Row>

                            </Row>
                            </Col>
                            </Form>

            </Row>
        </Card>
        <Modal v-model="modal_Show_example" width="600" :closable="false" :mask-closable="false" style="margin-left:0">
            <p slot="header">
                <span id="btnAddParameterExample"  class="margin-right-10">新增example</span>
            </p>
            <div style="padding-left:30px;">
                <Form ref="form_data_example"  :model="form_data_example" :rules="exampleValidate" :label-width="120" inline>
                    <FormItem  class="width-100-perc" label="名称：" prop="name">
                        <Input id="inputExampleName" size="small" style="width: 380px;" v-model="form_data_example.name" placeholder="请输入"></Input>
                    </FormItem>
                    <!-- <FormItem  class="width-100-perc" label="标题：" prop="title">
                        <Input id="inputExampleTitle" size="small" style="width: 380px;" v-model="form_data_example.title" placeholder="请输入"></Input>
                    </FormItem> -->
                    <FormItem id="inputExampleDes" class="width-100-perc" label="描述：" prop="description" >
                        <Input type="textarea" size="small" style="width: 380px;" v-model="form_data_example.description" placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem id="inputExampleValue" class="width-100-perc" label="示例值：" prop="value">
                        <Input type="textarea" size="small"  :rows="9" style="width: 300px;" v-model="form_data_example.value" placeholder="请输入"></Input>
                        <Button type="primary"  class="margin-left-10" @click="format_json">格式化</Button>
                    </FormItem>
                </Form>
            </div>
            <div slot="footer">
                <Button id="btnConfirmExample" type="primary" @click="ok_example">确定</Button>
                <Button id="btnCancelExample" type="ghost"  @click="cancel_example">取消</Button>
            </div>
        </Modal>
        <param_operate ref="param_operate"  @data_update="data_update_model"></param_operate>
<!--        <param_response ref="param_response" v-show="false"></param_response>-->
        <point_service ref="point_service" v-show="false"></point_service>
        <model_detail_page ref="model_detail_page" :index="current_index" @data_update="data_update_model"></model_detail_page>
        <modal_param_content_model_external ref="modal_param_content_model_external" index="kj2" @data_update="data_update_model_external" ></modal_param_content_model_external>
    </Row>
</template>

<script>
/* eslint-disable no-console, camelcase, eqeqeq, no-redeclare */
import loading from '../../my-components/loading/loading';
import param_content from './api-components/param_content';
// import param_response from './param-response'
import point_service from './point_service'
import param_operate from './param_operate'
import model_detail_page from './model_detail_page'
import modal_param_content_model_external from '~/views/model/modify_models/modal_param_content_model_external';
import Sortable from 'sortablejs';
import util from '../../../libs/util'
import api from '../../../api/api'
import TableModelContent from './TableModelContent'
import bus from '~/libs/bus'

export default {
  name: 'param-request',
  props: ['createdByExitedInterface', 'reqModelSchema'],
  computed: {
    modelDisabled () {
      if (this.createdByExitedInterface && this.form_data.contentType === 'application/json') {
        return true
      }
      return false
    }
  },
  components: {
    loading,
    param_content,
    param_operate,
    // param_response,
    point_service,
    model_detail_page,
    TableModelContent,
    modal_param_content_model_external
  },
  data () {
    const validate_example_value = (rule, value, callback) => {
      if (!this.isJson(value)) {
        callback(new Error('示例值为json格式'));
      } else {
        callback();
      }
    }
    return {
      currentApiData: {
        apiGroup: null,
        name: '',
        id: '',
        name: '',
        description: '',
      },
      data_spi_example_list: [],
      nowId: '', //当前模型id
      // 路径校验标识
      checkPathFlag: false,
      // 当前模型
      currentModel: {},
      // 传到后端服务的数组
      backendNameListData: [],
      mapping_list_temp: [],
      // 参数位置
      paramPosition: '',
      paramPositionShow: '',
      columns_propertiesList: [
        {
          title: '排序',
          width: 100,
          key: 'Action',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Icon', {
                class: 'iconDocOrder',
                props: {
                  type: 'arrow-move'
                },
                style: {
                  color: '#ad8893',
                  cursor: 'move',
                  marginRight: '5px',
                  fontSize: '18px'
                }
              })
            ]);
          }

        },
        {
          title: '名称',
          key: 'name',
          align: 'center',
          render: (h, params) => {
            const { name, title } = params.row
            if (name && title) {
              return h('div', `${name}(${title})`);
            } else {
              return h('div', name || '--');
            }
          }
        },
        {
          title: '类型-格式',
          key: 'type',
          align: 'center',
          render: (h, params) => {
            if (typeof params.row.type == 'object') {
              return h('div', [h('p', params.row.type[0]),
                h('p', params.row.type[1])]);
            } else if (params.row.format) {
              return h('div', [h('p', params.row.type),
                h('p', params.row.format)]);
            } else {
              return h('div', [h('p', params.row.type),
                h('p', params.row.type)]);
            }
          }

        },
        {
          title: '必需',
          align: 'center',
          key: 'must',
          render: (h, params) => {
            // if(params.row.model){
            //     return h('div','--');
            // }else{
            return params.row.required ? h('div', '是') : h('div', '否');
            // }
          }
        },
        {
          title: '废弃',
          align: 'center',
          key: 'deprecated',
          render: (h, params) => {
            // if(params.row.model){
            //     return h('div','--');
            // }else{
            return params.row.deprecated ? h('div', '是') : h('div', '否');
            // }
          }
        },
        {
          title: '敏感字段',
          align: 'center',
          key: 'sensitive',
          render: (h, params) => {
            return params.row.sensitive ? h('div', '是') : h('div', '否');
          }
        },
        {
          type: 'html',
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '约束',
          align: 'center',
          render: (h, params) => {
            var minimum, maximum, maxLength, minLength, notMin, notMax, maxItems, minItems, pattern
            notMin = params.row.exclusiveMinimum ? '不包含' : '包含'
            notMax = params.row.exclusiveMaximum ? '不包含' : '包含'
            maximum = params.row.maximum ? `最大值：${params.row.maximum}(${notMax})` : ''
            minimum = params.row.minimum ? `最小值：${params.row.minimum}(${notMin})` : ''
            maxLength = params.row.maxLength ? `最大长度：${params.row.maxLength}` : ''
            minLength = params.row.minLength ? `最小长度：${params.row.minLength}` : ''
            maxItems = params.row.maxItems ? `最大数量：${params.row.maxItems}` : ''
            minItems = params.row.minItems ? `最小数量：${params.row.minItems}` : ''
            pattern = params.row.pattern ? `正则：${params.row.pattern}` : ''
            return h('div', [
              h('p', minimum),
              h('p', maximum),
              h('p', maxLength),
              h('p', minLength),
              h('p', maxItems),
              h('p', minItems),
              h('p', pattern)
            ]);
          }
        },

        {
          title: '操作',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.edit_param(params.index, params.row, 'json', 'param_operate')
                  }
                }
              }, '编辑'),
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.delete_param_in_panel(params.index, params);
                  }
                }
              }, '删除')
            ]);
          }

        }
      ],
      columns_propertiesList_detail: [
        {
          title: '排序',
          width: 100,
          key: 'Action',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Icon', {
                class: 'iconDocOrder',
                props: {
                  type: 'arrow-move'
                },
                style: {
                  color: '#ad8893',
                  cursor: 'move',
                  marginRight: '5px',
                  fontSize: '18px'
                }
              })
            ]);
          }

        },
        {
          title: '名称',
          align: 'center',
          key: 'name',
          render: (h, params) => {
            if (params.row.name) {
              return h('div', params.row.name);
            } else {
              return h('div', '--');
            }
          }
        },
        {
          title: '类型-格式',
          key: 'type',
          align: 'center',
          render: (h, params) => {
            if (typeof params.row.type == 'object') {
              return h('div', [h('p', params.row.type[0]),
                h('p', params.row.type[1])]);
            } else if (params.row.format) {
              return h('div', [h('p', params.row.type),
                h('p', params.row.format)]);
            } else {
              return h('div', [h('p', params.row.type),
                h('p', params.row.type)]);
            }
          }
        },
        {
          align: 'center',
          title: '必需',
          key: 'must',
          render: (h, params) => {
            // if(params.row.model){
            //     return h('div','--');
            // }else{
            return params.row.required ? h('div', '是') : h('div', '否');
            // }
          }
        },
        {
          title: '废弃',
          align: 'center',
          key: 'deprecated',
          render: (h, params) => {
            return params.row.deprecated ? h('div', '是') : h('div', '否');
          }
        },
        {
          title: '敏感字段',
          align: 'center',
          key: 'sensitive',
          render: (h, params) => {
            return params.row.sensitive ? h('div', '是') : h('div', '否');
          }
        },
        {
          title: '描述',
          key: 'description',
          align: 'center',
          type: 'html',
          // render: (h, params) => {
          //   return h('div', params.row.description.replace(/&nbsp;/g, " ").replace(/&lt;/g, "<").replace(/&gt;/g, ">"));
          // }
        },
        {
          title: '约束',
          align: 'center',
          render: (h, params) => {
            var minimum, maximum, maxLength, minLength, notMin, notMax, maxItems, minItems, pattern
            notMin = params.row.exclusiveMinimum ? '不包含' : '包含'
            notMax = params.row.exclusiveMaximum ? '不包含' : '包含'
            minimum = params.row.minimum ? `最小值：${params.row.minimum}(${notMin})` : ''
            maximum = params.row.maximum ? `最小值：${params.row.maximum}(${notMax})` : ''
            maxLength = params.row.maxLength ? `最大长度：${params.row.maxLength}` : ''
            minLength = params.row.minLength ? `最大长度：${params.row.minLength}` : ''
            maxItems = params.row.maxItems ? `最大数量：${params.row.maxItems}` : ''
            minItems = params.row.minItems ? `最小数量：${params.row.minItems}` : ''
            pattern = params.row.pattern ? `正则${params.row.pattern}` : ''
            return h('div', [
              h('p', minimum),
              h('p', maximum),
              h('p', maxLength),
              h('p', minLength),
              h('p', maxItems),
              h('p', minItems),
              h('p', pattern)
            ]);
          }
        },
        {
          title: '操作',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.detail_param(params.index, params.row, 'detail_model_show', 'model_detail_page')
                  }
                }
              }, '详情')
            ]);
          }

        }

      ],
      data_propertiesList: [],
      // 接受接口类型
      interfaceType: '',
      spi_example_List: [
        {
          title: '名称',
          key: 'name',
          align: 'center'
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '示例值',
          align: 'center',
          key: 'value'
        },
        {
          title: '操作',
          key: 'operations',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  // name: 'url',
                  // value: {url: '/rest/api/config/edit'}
                }],
                on: {
                  click: () => {
                    this.example_modify(params.row);
                  }
                }
              }, '修改'),
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  // name: 'url',
                  // value: {url: '/rest/api/config/edit-for-doc'}
                }],
                on: {
                  click: () => {
                    this.example_delete(params.row.index);
                  }
                }
              }, '删除')
            ]);
          }
        }
      ],
      spi_example_List_display: [
        {
          title: '名称',
          key: 'name',
          align: 'center'
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '值',
          align: 'center',
          key: 'value'
        }
      ],
      // 参数
      exampleValidate: {
        name: [{required: true, message: '名称不能为空'}],
        // title:[{required:true,message:'标题不能为空'}],
        description: [{required: true, message: '描述不能为空'}],
        value: [{required: true, message: '示例值不能为空'}, { validator: validate_example_value, trigger: 'blur' }]
      },
      editExample: false,

      modal_Show_example: false,
      form_data_example: {
        name: '',
        // title:"",
        description: '',
        value: ''
      },
      data_api_model_list: [],
      showContent: false,
      contentType_list: [
        // {value: 'application/x-www-form-urlencoded', label: 'application/x-www-form-urlencoded'}
        // {value:"application/x-www-form-urlencoded",label:"application/x-www-form-urlencoded"},
        // {value:"application/json",label:"application/json"},
        // {value:"application/octet-stream",label:"application/octet-stream"},
        // {value:"multipart/form-data",label:"multipart/form-data"},
      ],
      httpMethod_list: [],
      queryType_list: [
        // {
        //     value: 'TRANSFORM',
        //     label: '转换'
        // },

        {
          value: 'PASSTHROUGH',
          label: '透传'
        },
        {
          value: 'MAPPING',
          label: '映射'
        }
      ],
      queryTypeText: '',
      current_status: 'create',
      form_data: {
        requestVersion: '1.0',
        prependApiGroup: '',
        prependPath: '',
        queryType: '',
        requestUrl: '',
        httpMethod: '',
        contentType: '',
        model: '',
        description: '',
        encrypt: false
        // requestBody:"requestBody"
      },
      // 当前tab
      current_tab: '',
      // json tab显示
      json_show: true,
      // json label 显示
      json_label: 'application/json',
      // form tab 显示
      form_show: false,
      // form label 显示
      form_label: 'application/x-www-form-urlencoded',

      data_request_json: [
        {
          param: 'p1', // 参数
          name: '参数1', // 名称
          type: ['string', 'string'], // 类型
          sample: '123', // 示例值
          description: '123', // 描述
          required: true, // 是否必填
          deprecated: false, // 是否废弃
          orderNo: false,
          internal: false, // 参数
          default: '', // 默认值
          sensitive: false, // 名称
          param_index: '123', // 类型
          param_name: 'aaaa', // 示例值
          maximum: '', // 最大值
          exclusiveMaximum: true, // 包含最大值
          minimum: '', // 最小值
          exclusiveMinimum: true, // 包含最小值，
          pattern: '', // 正则表达式
          maxLength: '', // 最大长度
          minLength: '', // 最小长度
          maxItems: '', // 最大数目
          minItems: '', // 最小数目
          model: ''
        },
        {
          index: 1,
          originalID: 0,
          children: [],
          params: 'BizSystem',
          name: '参数中文名',
          type: 'STRING',
          parentType: 'STRING',
          must: true,
          description: '',
          example: '',
          defaultValue: '',
          sampleValue: '', //
          internal: true, // 是否内部变量
          sensitive: true, // 是否敏感字段
          paramDataFormat: '', // 格式
          endParamIndex: '', // 端点参数顺序
          endParamName: '', // 端点参数名称
          inheritFromGroup: '', // 继承自api分组
          bucket: '', //
          fileName: '', //
          notEmpty: '',
          _expanded: false,
          length: {min: 1, max: 9}, // minexclusive是啥
          pattern: '', // 正则
          newItem: false

        }
      ],
      // form表格的数据
      data_request_form: [],
      // 当前行
      current_index: -1,

      // 编辑时候的属性
      propertiesDetails: '',
      sensitiveVariables: [],
      bizOrderVariable: {},
      current_apiGroup: '',
      validate_version: '',
      underLine: false,
      data_propertiesStr: '',
      showhttpMethod: true
    }
  },
  watch: {
    data_propertiesList: {
      handler(newValue, oldValue) {
        if (newValue) {
          this.$emit('updateApiOptionParamList', newValue)
        }
      },
      immediate: true,
      deep: true
    },
    'form_data.model': {
      handler(newValue, oldValue) {
        if (newValue) {
          this.detailModel()
        }
      },
      immediate: true,
      deep: true
    },
    data_propertiesStr: {
      handler(newValue, oldValue) {
        if (newValue) {
          const item = this.reqModelSchema['application/json']
          if (item && item.schema) {
            var schema = JSON.parse(item.schema)
            const properties = schema.properties
            const list = []
            Object.keys(properties).forEach(key => {
              list.push({
                name: key
              })
            })
            this.$emit('updateApiOptionParamList', list)
          }
        }
      },
      immediate: true,
      deep: true
    },
    data_api_model_list: {
      handler(newValue, oldValue) {
        if (newValue) {
          this.detailModel() 
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    detailModel() {
      const item = this.data_api_model_list.find(item => item.value === this.form_data.model)
      if(!item || !item.id) return
      api.yop_modalManagement_modal_detail({id: item.id}).then(
        (response) => {
          var response = response.data
          if (response.status === 'success') {
            var schema = JSON.parse(response.data.result.schema)
            const properties = schema.properties
            const list = []
            Object.keys(properties).forEach(key => {
              list.push({
                name: key
              })
            })
            this.$emit('updateApiOptionParamList', list)
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      );
    },
    reset () {
      if (this.createdByExitedInterface) {
        console.log('this.createdByExitedInterface')
        console.log(this.createdByExitedInterface)
        const _data_propertiesList = this.reqModelSchema
        if (this.form_data.contentType === 'multipart/form-data' || this.form_data.contentType === 'application/x-www-form-urlencoded') {
          if (_data_propertiesList[this.form_data.contentType]) {
            console.log(this.reqModelSchema)
            const properties = JSON.parse(_data_propertiesList[this.form_data.contentType].schema).properties
            console.log(properties)
            this.data_propertiesList = Object.keys(properties).map(key => {
              return {
                ...properties[key],
                name: key,
                type: [properties[key].type, properties[key].format]
              }
            })
          }
        } else if (this.form_data.contentType === 'application/json') {
          if (_data_propertiesList[this.form_data.contentType]) {
            this.data_propertiesStr = _data_propertiesList[this.form_data.contentType].modelRef
          }
        }
      }
      // // 默认值form值
      this.$nextTick(() => {
        if(this.current_status == 'create') {
          if(!this.form_data.httpMethod) {
            this.setContentType(this.interfaceType === 'FILE_DOWNLOAD' ? 'GET' : 'POST')
            this.form_data.httpMethod = this.interfaceType === 'FILE_DOWNLOAD' ? 'GET' : 'POST'
            this.content_type('application/x-www-form-urlencoded')
            this.form_data.contentType = 'application/x-www-form-urlencoded'

            if(this.interfaceType === 'FILE_UPLOAD') {
              this.contentType_list = [{value: 'multipart/form-data', label: 'multipart/form-data'},{value: 'application/octet-stream', label: 'application/octet-stream'},]
              this.content_type('multipart/form-data')
              this.form_data.contentType = 'multipart/form-data'
              this.$refs.form_data.validateField('contentType', valid => '');
              this.httpMethod_list = [{value: 'POST', label: 'POST'}, {value: 'PUT', label: 'PUT'}]
            }
            if(this.interfaceType === 'COMMON') {
              this.contentType_list = [{value: 'application/json', label: 'application/json'}]
              this.content_type('application/json')
              this.form_data.contentType = 'application/json'
              this.$refs.form_data.validateField('contentType', valid => '');
              this.httpMethod_list = [{value: 'POST', label: 'POST'}]
            }
          }
        }
      })
    },
    // 表格拖动排序
    dragSort () {
      const tbody = document.querySelector('#table_1 tbody')
      const _this = this
      Sortable.create(tbody, {
        onEnd ({ newIndex, oldIndex }) {
          const currRow = _this.data_propertiesList.splice(oldIndex, 1)[0]
          _this.data_propertiesList.splice(newIndex, 0, currRow)
          _this.$forceUpdate()
        }
      })
    },
    // 页面初始化
    init () {
      var that = this
      this.validate_version = (rule, value, callback) => {
        var str = that.form_data.prependPath.trim() + that.form_data.requestVersion.trim() + that.form_data.prependApiGroup.trim() + that.form_data.requestUrl.trim()
        if (that.current_status === 'modify' || that.current_status === 'description') {
          callback();
        }
        if (!(/\/(rest|yos)\/v([1-9][0-9]*).(0|[1-9][0-9]*)\/([a-z][a-z0-9\\-]*)*[a-z0-9]+(\/([a-z][a-z0-9\\-]*)*[a-z0-9]+)+$/.test(str))) {
          callback(new Error(' '));
        } else {
          callback();
        }
      };
      // this.validate_requestUrl = (rule, value, callback) => {
      //       var str = value + ""
      //     if(!(/(^\/([a-z][a-z\-]*)*[a-z]+)+/.test(value))){
      //         callback(new Error('请输入正确格式的版本号'));
      //     }else{
      //         callback();
      //     }
      // };
    },
    // 查看dto
    seeDto(dtoData, type) {
      // 筛选当前dto id
      this.currentApiData.name = dtoData
      this.data_api_model_list.forEach((item) => {
        if(item.name === dtoData) {
          this.currentApiData.id = item.id
        }
      })
      if(type === 'detail') {
        this.detail_model(this.currentApiData)
      } else {
        this.nowId = this.currentApiData.id
        this.modelList(this.currentApiData)
        this.edit_model(this.currentApiData)
      }
    },
    // 查询下模型列表 找到当前修改的模型
    modelList(row) {
      let paramsTemp = {
        name: row.name,
        apiGroup: '',
        pageNo: 1,
        pageSize: 10
      };
      // 查询modal列表
      api.yop_modalManagement_modal_list(paramsTemp).then(response => {
        let data = response.data
        this.currentModel = data.data.page.items.find((item) => { return item.id === this.nowId})
      });
    },
    // 模型详情弹框
    detail_model (row) {
      var id = row.id;
      var name = row.name;
      var apiGroup = row.apiGroup;
      var description = row.description;
      api.yop_modalManagement_modal_detail({id: id}).then(
        (response) => {
          var response = response.data
          if (response.status === 'success') {
            var schema = JSON.parse(response.data.result.schema)
            var bizOrderVariable = response.data.result.bizOrderVariable
            var sensitiveVariables = response.data.result.sensitiveVariables
            this.propertiesDetails = schema.properties;
            var arr = []
            for (let i in this.propertiesDetails) {
              let o = {};
              o['name'] = i;
              for (var k in schema.required) {
                var requiredName = schema.required[k]
                if (requiredName === i) {
                  o['required'] = true
                }
              }
              for (var j in this.propertiesDetails[i]) {
                o[j] = this.propertiesDetails[i][j]
                if (j == 'type') {
                  o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
                } else if (j == '$ref') {
                  o['type'] = ['object', 'object']
                } else if (j == 'additionalProperties') {
                  o['type'] = ['object', 'map']
                }
              }
              for (var k in o) {
                if (k == 'format') {
                  o['type'][1] = o[k]
                }
              }
              arr.push(o)
            }
            this.$refs.modal_param_content_model_external.current_panel_set('detail_model');
            var detailData = {
              type: schema.type,
              title: schema.title,
              name: name,
              apiGroup: apiGroup,
              description: description,
              data_propertiesList: arr,
              extensions: schema.extensions,
            }
            this.$refs.modal_param_content_model_external.form_data_set(detailData, bizOrderVariable, sensitiveVariables, 'detail');
            // this.current_index = -1;
            this.$refs.modal_param_content_model_external.show();
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      );
    },
    // 修改model
    edit_model (row) {
      var description = '';
      var apiGroup = null
      var name = row.name
      api.yop_modalManagement_modal_detail({id: row.id}).then(
        (response) => {
          var response = response.data
          if (response.status === 'success') {
            var schema = JSON.parse(response.data.result.schema)
            var bizOrderVariable = response.data.result.bizOrderVariable
            var sensitiveVariables = response.data.result.sensitiveVariables
            description = response.data.result.description
            this.propertiesDetails = schema.properties;
            var requiredArr = schema.required
            apiGroup = response.data.result.apiGroup
            var arr = []
            for (let i in this.propertiesDetails) {
              let o = {};
              o['name'] = i;
              for (var j in this.propertiesDetails[i]) {
                // 每个属性
                o[j] = this.propertiesDetails[i][j]
                if (j == 'type') {
                  o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
                } else if (j == '$ref') {
                  o['type'] = ['object', 'object']
                } else if (j == 'additionalProperties') {
                  o['type'] = ['object', 'map']
                }
              }
              for (var k in o) {
                if (k == 'format') {
                  o['type'][1] = o[k]
                }
              }
              arr.push(o);
            }
            if (requiredArr) {
              for (var i = 0; i < requiredArr.length; i++) {
                var requiredArrI = requiredArr[i]
                for (var j = 0; j < arr.length; j++) {
                  if (requiredArrI == arr[j].name) {
                    arr[j].required = true
                  }
                }
              }
            }

            this.$refs.modal_param_content_model_external.current_panel_set('modify_model');
            var detailData = {
              type: schema.type,
              title: schema.title,
              name: name,
              description: description,
              apiGroup: apiGroup,
              data_propertiesList: arr,
              extensions: schema.extensions,
            }
            this.$refs.modal_param_content_model_external.form_data_set(detailData, bizOrderVariable, sensitiveVariables);
            // this.current_index = -1;
            this.$refs.modal_param_content_model_external.show();
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      );
    },
    // 判断是否是json
    isJson (str) {
      if (typeof str == 'string') {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
            return false;
          }
        } catch (e) {
          console.log('error：' + str + '!!!' + e);
          return false;
        }
      }
    },
    set_current_status (val) {
      this.current_status = val
    },
    // 获取model
    getModels (apiGroup, showModel) {
      var param = {apiGroup: apiGroup}
      this.current_apiGroup = apiGroup
      this.setInterfaceType(this.interfaceType)
      this.getModelsList(param, showModel)
    },
    getModelsList (param, showModel) {
      api.yop_modalManagement_modal_simple_list(param).then(
        (response) => {
          var response = response.data
          this.data_api_model_list = [];
          let apigroup = response.data.result;
          // this.data_api_model= apigroup[0].groupCode;
          for (var i in apigroup) {
            this.data_api_model_list.push(
              {
                label: apigroup[i].name + '(' + apigroup[i].description + ')',
                value: apigroup[i].name,
                name: apigroup[i].name,
                id: apigroup[i].id,
              }
            )
          }
          if (showModel == 'showModel') {
            var schema = this.form_data.requestBody.contents['application/json'].schema;
            this.form_data.model = JSON.parse(schema)['$ref'].split('/').pop();
          }
        }
      );
    },
    // 选择入参请求模式决定后端服务是否展示列表
    query_type (value) {
      console.log(value)
      this.$emit('getServiceType', value)
    },
    initSelect () {
      this.$refs.form_data.resetFields();
      this.$refs.select_httpMethod.clearSingleSelect();
      this.$refs.select_contentType.clearSingleSelect();
      this.form_data.requestVersion = '1.0'
      this.form_data.prependPath = ''
      this.form_data.httpMethod = ''
      this.form_data.contentType = ''
      this.showhttpMethod = false
      setTimeout(() => {
        this.showhttpMethod = true
      },100)
    },
    // 校验重复路径
    checkPath() {
      let params = {
        path: `${this.form_data.prependPath}${this.form_data.requestVersion}${this.form_data.prependApiGroup}${this.form_data.requestUrl}`
      }
      api.yop_api_path_exists(params).then(res => {
        if(res.data.status === 'success') {
          this.checkPathFlag = res.data.data.result
          if(this.checkPathFlag) {
            this.$Message.warning('路径重复，请检查');
          }
        }
      })
    },
    // 返回当前校验结果
    returnCheckPath () {
      if(this.checkPathFlag) {
        this.$Message.error('路径重复，请检查');
      }
      return this.checkPathFlag
    },
    // 根据接口类型设置模块
    setInterfaceType (value) {
      this.form_data.prependPath = ''
      this.interfaceType = value
      if (value === 'COMMON') {
        this.form_data.prependPath = '/rest/v'
        this.form_data.prependApiGroup = '/' + this.current_apiGroup + '/'
        this.httpMethod_list = [{value: 'GET', label: 'GET'}, {value: 'POST', label: 'POST'}]
      }
      if (value === 'FILE_UPLOAD') {
        this.form_data.prependPath = '/yos/v'
        this.form_data.prependApiGroup = '/' + this.current_apiGroup + '/'
        // this.httpMethod_list = [{value:"GET",label:"GET"},{value:"POST",label:"POST"},{value:"PUT",label:"PUT"},{value:"DELETE",label:"DELETE"}]
        this.httpMethod_list = [{value: 'POST', label: 'POST'}, {value: 'PUT', label: 'PUT'}]
      }
      if (value === 'FILE_DOWNLOAD') {
        this.form_data.prependPath = '/yos/v'
        this.form_data.prependApiGroup = '/' + this.current_apiGroup + '/'
        this.httpMethod_list = [{value: 'GET', label: 'GET'}]
      }
      console.log('hits.httpMethod_list')
      console.log(this.httpMethod_list)
    },
    setContentType (value) {
      this.data_propertiesList = []
      this.data_spi_example_list = []
      this.$refs.select_contentType.clearSingleSelect();
      this.$emit('passQueryWay', value)
      this.contentType_list = []
      if (this.interfaceType === 'COMMON') {
        if (value === 'GET') {
          this.contentType_list = [
            {value: 'application/x-www-form-urlencoded', label: 'application/x-www-form-urlencoded'}]
          this.paramPosition = 'WEB_get'
        }
        if (value === 'POST') {
          this.contentType_list = [
            {value: 'application/x-www-form-urlencoded', label: 'application/x-www-form-urlencoded'},
            {value: 'application/json', label: 'application/json'}]
          this.paramPosition = 'WEB_post'
        }
      }
      if (this.interfaceType === 'FILE_UPLOAD') {
        if (value === 'GET') {
          this.contentType_list = [
            {value: 'application/x-www-form-urlencoded', label: 'application/x-www-form-urlencoded'}]
          this.paramPosition = 'yos_get'
        }
        if (value === 'POST') {
          this.contentType_list = [
            // {value: 'application/x-www-form-urlencoded', label: 'application/x-www-form-urlencoded'},
            // {value: 'application/json', label: 'application/json'},
            {value: 'application/octet-stream', label: 'application/octet-stream'},
            {value: 'multipart/form-data', label: 'multipart/form-data'}]
          this.paramPosition = 'yos_post'
        }
        if (value === 'PUT') {
          this.contentType_list = [
            {value: 'application/octet-stream', label: 'application/octet-stream'}]
          this.paramPosition = 'yos_put'
        }
        if (value === 'DELETE') {
          this.contentType_list = [
            {value: 'application/x-www-form-urlencoded', label: 'application/x-www-form-urlencoded'}]
          this.paramPosition = 'yos_delete'
        }
      }
      if (this.interfaceType === 'FILE_DOWNLOAD') {
        if (value === 'GET') {
          this.contentType_list = [
            {value: 'application/x-www-form-urlencoded', label: 'application/x-www-form-urlencoded'}]
          this.paramPosition = 'yos_download_get'
        }
      }
      // 默认form
      // this.$nextTick(() => {
      //   if(this.current_status == 'create') { 
      //     this.form_data.contentType = 'application/x-www-form-urlencoded'
      //     this.content_type('application/x-www-form-urlencoded')
      //     this.$refs.form_data.validateField('contentType', valid => '');
      //   }
      // })
    },
    content_type (value) {
      this.data_propertiesList = []
      this.data_spi_example_list = []
      this.paramPositionShow = ''
      // this.$refs.param_response.initSelect();
      if (value) {
        if (this.paramPosition === 'WEB_get' || this.paramPosition === 'yos_get' || this.paramPosition === 'yos_delete' || this.paramPosition === 'yos_download_get') {
          this.paramPositionShow = 'query'// 简单非文件 array  QUERY
          this.$refs.param_operate.init_nofile();
        }

        if ((this.paramPosition === 'WEB_post' || this.paramPosition === 'yos_post') && value === 'application/x-www-form-urlencoded') {
          this.paramPositionShow = 'body_query'// 简单非文件 array  BODY
          this.$refs.param_operate.init_nofile();
        }
        if ((this.paramPosition === 'WEB_post' || this.paramPosition === 'yos_post') && value === 'application/json') {
          this.paramPositionShow = 'body_schema'// schema      BODY
        }
        if (this.paramPosition === 'yos_put' || (this.paramPosition === 'yos_post' && value === 'application/octet-stream')) {
          this.paramPositionShow = 'binary'// string/binary BODY
        }
        if (value === 'multipart/form-data') {
          this.paramPositionShow = 'body_query'// 简单文件 array 至少一个文件  BODY
          this.$refs.param_operate.init();
        }
        if (value === 'multipart/form-data' || value === 'application/x-www-form-urlencoded') {
          if (this.createdByExitedInterface) {
            const _data_propertiesList = this.reqModelSchema
            console.log('type-change')
            console.log(_data_propertiesList)
            if (_data_propertiesList[value]) {
              const properties = JSON.parse(_data_propertiesList[value].schema).properties
              this.data_propertiesList = Object.keys(properties).map(key => {
                return {
                  ...properties[key],
                  name: key,
                  type: [properties[key].type, properties[key].format]
                }
              })
            }
          }
        }
        if (value === 'application/json') {
          if (this.createdByExitedInterface) {
            const _data_propertiesList = this.reqModelSchema
            if (_data_propertiesList[value]) {
              this.data_propertiesStr = _data_propertiesList[value].modelRef
            }
          } else {
            this.queryType_list = [
              {
                value: 'PASSTHROUGH',
                label: '透传'
              }
            ]
          }
        } else {
          this.queryType_list = [
            {
              value: 'PASSTHROUGH',
              label: '透传'
            },
            {
              value: 'MAPPING',
              label: '映射'
            }

          ]
        }
        this.form_data.queryType = '';

        // 设置返回信息
        if (this.paramPosition === 'WEB_get') {
          this.$emit('passHTTPCode', [{value: 200, label: '200'}, {value: 302, label: '302'}])
        }
        if (this.paramPosition === 'WEB_post') {
          this.$emit('passHTTPCode', [{value: 200, label: '200'}, {value: 302, label: '302'}])
        }
        if (this.paramPosition === 'yos_get') {
          this.$emit('passHTTPCode', [{value: 200, label: '200'}])
        }
        if (this.paramPosition === 'yos_download_get') {
          this.$emit('passHTTPCode', [{value: '200_binary', label: '200'}])
        }
        if (this.paramPosition !== 'WEB_get' && this.paramPosition !== 'WEB_post' && this.paramPosition !== 'yos_get' && this.paramPosition !== 'yos_download_get') {
          this.$emit('passHTTPCode', [{value: 200, label: '200'}])
        }
      }
      if (this.form_data.httpMethod == 'POST' || this.form_data.httpMethod == 'GET') {
        this.$refs.param_operate.set_showOrder(value)
      } else {
        this.$refs.param_operate.set_showOrder('')
      }
      this.$nextTick(() => {
        this.dragSort()
      })
      // this.$emit('changeRequestContentType')
    },
    setBackendNameListData (val) {
      console.log(val, 'list');

      this.backendNameListData = val
      this.setBackendName();
    },
    quick_sort (arr) {
      var arry1, arry2, mid;
      if (arr.length < 2) return arr;
      else if (arr.length == 2) {
        if (arr[0] > arr[1]) { return arr.reverse(); } else { return arr; }
      } else {
        arry1 = new Array();
        arry2 = new Array();
        mid = arr[0];
        for (var i = 1; i < arr.length; i++) {
          if (arr[i] < mid) { arry1.push(arr[i]); } else { arry2.push(arr[i]); }
        }
      }
      return (this.quick_sort(arry1).concat(mid, this.quick_sort(arry2)));
    },
    setBackendName () {
      console.log(this.backendNameListData, '1122')
      if (this.backendNameListData.length == 0) {
        for (let i = 0; i < this.data_propertiesList.length; i++) {
          console.log('in');

          delete this.data_propertiesList[i].backendLocation
          delete this.data_propertiesList[i].location
          delete this.data_propertiesList[i].backendName
          delete this.data_propertiesList[i]['x-yop-apigateway-backend-name']
          delete this.data_propertiesList[i]['x-yop-apigateway-backend-location']
        }
      }

      for (var i in this.data_propertiesList) {
        delete this.data_propertiesList[i].backendLocation
        delete this.data_propertiesList[i].location
        delete this.data_propertiesList[i].backendName
        delete this.data_propertiesList[i]['x-yop-apigateway-backend-name']
        delete this.data_propertiesList[i]['x-yop-apigateway-backend-location']
      }
      for (let i = 0; i < this.backendNameListData.length; i++) {
        var backendNameI = this.backendNameListData[i].backendName
        var nameI = this.backendNameListData[i].name
        for (var j in this.data_propertiesList) {
          if (nameI == this.data_propertiesList[j].name) {
            this.data_propertiesList[j].backendName = backendNameI
          }
          // else{
          //     delete this.data_propertiesList[i].backendLocation
          //     delete this.data_propertiesList[i].location
          // }
        }
      }
    },
    // 点击保存校验
    saveCheck () {
      this.setBackendName();
      console.log(this.data_propertiesList, 'llllllllll');
      // string/string 类型 去掉format
      try {
        this.data_propertiesList.forEach((item) =>{
        if (item.type[1] == "string") {
          delete item.format
          let schemaObj = JSON.parse(item.schema)
          delete schemaObj.format
          item.schema = JSON.stringify(schemaObj)
        }
      })
      } catch (error) {
        console.log(error)
      }
      
      // 基本信息的验证
      return this.$refs['form_data'].validate((valid) => {
        if (valid) {
          var schema = ''
          if ((this.paramPositionShow == 'body_query' || this.paramPositionShow == 'query') && this.data_propertiesList.length <= 0) {
            this.$Message.error('属性不能为空');
            return false
          } else {
            if (this.paramPositionShow == 'query') {
              // if(this.paramPositionShow == "query"){//todo
              try {
                schema = this.format_submit_param_query(this.data_propertiesList)
              } catch (error) {
                console.log(error)
              }
            } else {
              let _data_propertiesList = this.data_propertiesList.map(item => {
                if (item.type[1] != 'enum') {
                  delete item.enum
                }
                return item
              })
              const _modelSchema = this.format_submit_param(_data_propertiesList)
              if (this.createdByExitedInterface) {
                Object.keys(_modelSchema.properties).map(i => {
                  if (!_modelSchema.properties[i].format && _modelSchema.properties[i].format !== null) {
                    _modelSchema.properties[i].format = _modelSchema.properties[i].type
                  }
                })
              }
              schema = _modelSchema
            }
            console.log('this.form_data')
            console.log(this.form_data)
            var param = {
              paramForm: this.form_data,
              schema: schema,
              paramPosition: this.paramPositionShow,
              sensitiveVariables: this.sensitiveVariables,
              bizOrderVariable: this.bizOrderVariable,
              examples: this.data_spi_example_list
            }
            this.$emit('passParamRequest', JSON.parse(JSON.stringify(param)))
            if (this.form_data.contentType === 'multipart/form-data' || this.form_data.contentType === 'application/x-www-form-urlencoded') {
              if (this.createdByExitedInterface) {
                const _data_propertiesList = this.reqModelSchema
                let modelSchema
                if (this.paramPositionShow == 'query') {
                  const _properties = {}
                  const _modelSchema = this.format_submit_param_query(this.data_propertiesList)
                  _modelSchema.forEach(item => {
                    const _schema = JSON.parse(item.schema)
                    _properties[item.name] = { ..._schema }
                  })
                  modelSchema = {
                    type: 'object',
                    properties: _properties
                  }
                } else {
                  modelSchema = schema
                }
                _data_propertiesList[this.form_data.contentType] = {
                  ..._data_propertiesList[this.form_data.contentType],
                  schema: JSON.stringify(modelSchema)
                }
                console.log('_data_propertiesList')
                console.log(_data_propertiesList)
                this.$emit('changeReqModelSchema', _data_propertiesList)
              }
            }
            return true
          }
        } else {
          this.$Message.error('请检查请求参数信息是否填写完全');
          return false
        }
      });
    },
    format_submit_param (param) {
      var param = JSON.parse(JSON.stringify(param));
      var tempProperties = {type: 'object', required: [], properties: {}}
      var requiredArr = []
      this.sensitiveVariables = []
      for (var k = 0; k < param.length; k++) {
        var paramK = param[k]
        tempProperties['properties'][paramK['name']] = {};
        for (var i in paramK) {
          if (paramK['type'][1] == 'map' && i == 'additionalProperties') {
            tempProperties['properties'][paramK['name']]['additionalProperties'] = {}
            for (var o in paramK[i]) {
              if (paramK.additionalProperties.model) {
                tempProperties['properties'][paramK['name']]['additionalProperties']['$ref'] = `#/components/schemas/${paramK[i].model}`
              } else {
                if (o == 'type') {
                  if (typeof paramK[i][o] == 'string') {
                    paramK[i]['type'] = [paramK[i]['type'], paramK[i]['format']]
                  }
                  // tempProperties["properties"][paramK["name"]]["additionalProperties"]["format"] = paramK[i]["type"][1]
                  tempProperties['properties'][paramK['name']]['additionalProperties']['type'] = paramK[i]['type'][0]
                } else if (paramK[i][o] && o != 'constrains') {
                  tempProperties['properties'][paramK['name']]['additionalProperties'][o] = paramK[i][o]
                }
              }
            }
          }
          if (paramK['type'][1] == 'array' && i == 'items') {
            tempProperties['properties'][paramK['name']]['items'] = {}
            for (var r in paramK.items) {
              if (paramK.items.model) {
                tempProperties['properties'][paramK['name']]['items']['$ref'] = `#/components/schemas/${paramK[i].model}`
              } else {
                if (r == 'type') {
                  if (typeof paramK[i][r] == 'string') {
                    paramK[i]['type'] = [paramK[i]['type'], paramK[i]['format']]
                  }
                  // tempProperties["properties"][paramK["name"]]["items"]["format"] = paramK[i]["type"][1]
                  tempProperties['properties'][paramK['name']]['items']['type'] = paramK[i]['type'][0]
                } else if (paramK[i][r] && r != 'constrains') {
                  tempProperties['properties'][paramK['name']]['items'][r] = paramK[i][r]
                }
              }
            }
          }
          if (i == 'type') {
            if (paramK[i][1] == 'object') {
              tempProperties['properties'][paramK['name']][i] = 'object'
              tempProperties['properties'][paramK['name']]['$ref'] = `#/components/schemas/${paramK.model}`;
            } else {
              paramK.$ref = ''
              tempProperties['properties'][paramK['name']][i] = paramK[i][0]
              if (paramK[i][0] != paramK[i][1]) {
                tempProperties['properties'][paramK['name']]['format'] = paramK[i][1]
              }
            }
          }

          if (i == 'required' && paramK[i]) {
            requiredArr.push(paramK['name']);
            tempProperties[i] = requiredArr
          }
          if (i == 'sensitive' && paramK['sensitive']) {
            this.sensitiveVariables.push({
              'part': 'REQUEST',
              'location': 'BODY',
              'variableName': paramK['name']
            })
          }
          if (i == 'orderNo' && paramK['orderNo']) {
            this.bizOrderVariable = {
              'part': 'REQUEST',
              'location': 'BODY',
              'variableName': paramK['name']
            }
          }
          if (paramK[i] != '' && i != 'type' && i != 'required' && i != 'name' && i != 'model' && i != '_index' && i != '_rowKey' && i != 'format' && i != 'items' && i != 'additionalProperties' && i != 'constrains' && i == 'backendName') {
            tempProperties['properties'][paramK['name']][i] = paramK[i]
          }
          console.log(i, paramK[i])
          if (i == 'backendName' || i == 'x-yop-apigateway-backend-name') {
            tempProperties['properties'][paramK['name']]['x-yop-apigateway-backend-name'] = paramK[i]
            tempProperties['properties'][paramK['name']]['x-yop-apigateway-backend-location'] = 'BODY'
          }
          if (paramK[i]) {
            if (i == 'enum' || i == 'example' || i == 'title' || i == 'default' || i == 'deprecated' || i == 'description' || i == 'examples' || i == 'maxLength' || i == 'minLength' || i == 'pattern' || i == 'orderNo' || i === 'maximum' || i === 'minimum' || i === 'extensions') {
              tempProperties['properties'][paramK['name']][i] = paramK[i]
            }
          }
        }
      }
      if (requiredArr.length > 0) {
        console.log(requiredArr)
        tempProperties['required'] = this.quick_sort(requiredArr)
      } else {
        delete tempProperties.required
      }
      console.log(tempProperties, '=====')
      return tempProperties
    },
    format_submit_param_query (param) {
      var param = JSON.parse(JSON.stringify(param));
      var tempProperties = {}
      this.sensitiveVariables = []
      for (var k = 0; k < param.length; k++) {
        var paramK = param[k]
        tempProperties[paramK['name']] = {};
        tempProperties[paramK['name']]['schema'] = {}
        for (var i in paramK) {
          if (paramK['type'][1] == 'map' && i == 'additionalProperties') {
            tempProperties[paramK['name']]['additionalProperties'] = {}
            for (var o in paramK[i]) {
              if (paramK.additionalProperties.model) {
                tempProperties[paramK['name']]['additionalProperties']['$ref'] = `#/components/schemas/${paramK[i].model}`
              } else {
                if (o == 'type') {
                  if (typeof paramK[i][o] == 'string') {
                    paramK[i]['type'] = [paramK[i]['type'], paramK[i]['format']]
                  }
                  // tempProperties[paramK["name"]]["additionalProperties"]["format"] = paramK[i]["type"][1]
                  tempProperties[paramK['name']]['additionalProperties']['type'] = paramK[i]['type'][0]
                } else if (paramK[i][o] && o != 'constrains') {
                  tempProperties[paramK['name']]['additionalProperties'][o] = paramK[i][o]
                }
              }
            }
          }
          if (paramK['type'][1] == 'array' && i == 'items') {
            tempProperties[paramK['name']]['schema']['items'] = {}
            for (var r in paramK.items) {
              if (paramK.items.model) {
                tempProperties[paramK['name']]['schema']['items']['$ref'] = `#/components/schemas/${paramK[i].model}`
              } else {
                if (r == 'type') {
                  if (typeof paramK[i][r] == 'string') {
                    paramK[i]['type'] = [paramK[i]['type'], paramK[i]['format']]
                  }
                  // tempProperties[paramK["name"]]["items"]["format"] = paramK[i]["type"][1]
                  tempProperties[paramK['name']]['schema']['items']['type'] = paramK[i]['type'][0]
                } else if (paramK[i][r] && r != 'constrains') {
                  tempProperties[paramK['name']]['schema']['items'][r] = paramK[i][r]
                }
              }
            }
          }
          if (i == 'type') {
            if (paramK[i][1] == 'object') {
              tempProperties[paramK['name']][i] = 'object'
              tempProperties[paramK['name']]['$ref'] = `#/components/schemas/${paramK.model}`;
            } else {
              paramK.$ref = ''
              tempProperties[paramK['name']]['schema'][i] = paramK[i][0]
              if (paramK[i][0] != paramK[i][1]) {
                tempProperties[paramK['name']]['schema']['format'] = paramK[i][1]
              }
            }
          }
          if (paramK[i] != '' && i != 'model' && i != 'type' && i != '_index' && i != '_rowKey' && i != 'items' && i != 'required' && i != 'name' && i != 'constrains' && i != 'description' && i != 'deprecated' && i != 'examples' && i != 'additionalProperties' && i != 'backendName' && i != 'backendLocation' && i != 'location' && i != 'schema' && i != 'sensitive' && i != 'orderNo') {
            tempProperties[paramK['name']]['schema'][i] = paramK[i]
          }
          if (i == 'required' || i == 'description' || i == 'deprecated' || i == 'examples' || i == 'name' || i == 'backendName') {
            tempProperties[paramK['name']][i] = paramK[i]
          }
          if (i == 'sensitive' && paramK['sensitive']) {
            this.sensitiveVariables.push({
              part: 'REQUEST',
              location: 'QUERY',
              variableName: paramK['name']
            })
          }
          if (i == 'orderNo' && paramK['orderNo']) {
            this.bizOrderVariable = {
              'part': 'REQUEST',
              'location': 'QUERY',
              'variableName': paramK['name']
            }
          }
          tempProperties[paramK['name']]['location'] = 'QUERY'
          if (paramK[i] && paramK['backendName']) {
            tempProperties[paramK['name']]['backendLocation'] = 'QUERY'
          }
        }
        var schemaString = JSON.stringify(tempProperties[paramK['name']]['schema']);
        tempProperties[paramK['name']]['schema'] = schemaString
      }
      var backArr = []
      for (var n in tempProperties) {
        backArr.push(tempProperties[n])
      }
      return backArr
    },
    edit_format_submit_param (param) {
      var param = JSON.parse(JSON.stringify(param));
      var tempProperties = {properties: {}}
      var requiredArr = []

      for (var k = 0; k < param.length; k++) {
        var paramK = param[k]
        tempProperties['properties'][paramK['name']] = {};
        for (var i in paramK) {
          if (paramK['type'][1] == 'map' && i == 'additionalProperties') {
            tempProperties['properties'][paramK['name']]['additionalProperties'] = {}
            for (var o in paramK[i]) {
              tempProperties['properties'][paramK['name']]['additionalProperties'][o] = paramK[i][o]
            }
            paramK[i] = tempProperties['properties'][paramK['name']]['additionalProperties']
          }
          if (paramK['type'][1] == 'array' && i == 'items') {
            tempProperties['properties'][paramK['name']]['items'] = {}
            for (var r in paramK.items) {
              tempProperties['properties'][paramK['name']]['items'][o] = paramK[i][o]
            }
            paramK[i] = tempProperties['properties'][paramK['name']]['items']
          }
          if (i == 'type') {
            if (paramK[i][1] == 'object') {
              tempProperties['properties'][paramK['name']][i] = 'object'
              tempProperties['properties'][paramK['name']]['$ref'] = `#/components/schemas/${paramK.model}`;
            } else {
              tempProperties['properties'][paramK['name']][i] = paramK[i][0]
              if (paramK[i][0] != paramK[i][1]) {
                tempProperties['properties'][paramK['name']]['format'] = paramK[i][1]
              }
            }
          }

          if (i == 'required' && paramK[i]) {
            requiredArr.push(paramK['name']);
            tempProperties[i] = requiredArr
          }
          if (paramK[i] != '' && i != 'type' && i != 'required' && i != 'name' && i != 'model' && i != '_index' && i != '_rowKey' && i != 'format' && i != 'items' && i != 'additionalProperties' && i != 'constrains') {
            tempProperties['properties'][paramK['name']][i] = paramK[i]
          }
        }
      }
    },
    edit_format_submit_param_query (param) {
      var param = JSON.parse(JSON.stringify(param));
      for (var k = 0; k < param.length; k++) {
        var paramK = param[k]
        console.log(paramK, 'paramK')
        if (paramK.name.indexOf('_') > -1) {
          this.underLine = true
          this.$emit('setUnderLine', this.underLine)
        }
        var schema = JSON.parse(paramK.schema)
        for (var l in schema) {
          paramK[l] = schema[l]
          if (this.bizOrderVariable && this.bizOrderVariable.variableName && this.bizOrderVariable.variableName == paramK['name']) {
            schema.orderNo = true
          }
          if (this.sensitiveVariables) {
            for (var m = 0; m < this.sensitiveVariables.length; m++) {
              var sensitiveVariablesM = this.sensitiveVariables[m]
              for (var n in sensitiveVariablesM) {
                if (n == 'variableName' && sensitiveVariablesM[n] == paramK['name']) {
                  paramK.sensitive = true
                }
              }
            }
          }
        }
        schema['format'] = schema['format'] || schema['type']
        paramK['type'] = [schema['type'], schema['format']]
      }
      return param
    },
    // 新增example
    add_example () {
      this.$refs.form_data_example.resetFields()
      this.modal_Show_example = true;
      this.editExample = false
    },
    example_modify (row) {
      this.editExample = true
      var name = row.name;
      var title = row.title;
      var description = row.description;
      var value = row.value;
      this.form_data_example.name = name;
      // this.form_data_example.title = title;
      this.form_data_example.description = description;
      this.form_data_example.value = value;
      this.row_index = row._index;
      this.modal_Show_example = true;
    },
    format_json () {
      if (!this.form_data_example.value) {
        return
      }
      var json = JSON.parse(this.form_data_example.value);
      console.log(json);
      var that = this
      if (typeof json != 'string') {
        json = JSON.stringify(json, null, 4);
      }
      json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');

      return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
        function (match) {
          var cls = 'number';
          if (/^"/.test(match)) {
            if (/:$/.test(match)) {
              cls = 'key';
            } else {
              cls = 'string';
            }
          } else if (/true|false/.test(match)) {
            cls = 'boolean';
          } else if (/null/.test(match)) {
            cls = 'null';
          }
          that.form_data_example.value = json
          return match;
        }
      );
    },
    ok_example () {
      var self = this
      this.$refs.form_data_example.validate((valid) => {
        if (valid) {
          var example = {
            name: self.form_data_example.name,
            // title:self.form_data_example.title,
            description: self.form_data_example.description,
            value: self.form_data_example.value
          }
          if (!self.editExample) {
            self.data_spi_example_list.push(example)
          } else {
            self.data_spi_example_list.splice(self.row_index, 1, example)
          }
          self.modal_Show_example = false;
        } else {
          self.$Message.error('请检查');
        }
      })
    },
    cancel_example () {
      this.modal_Show_example = false;
    },
    example_delete (index) {
      this.$Modal.confirm({
        title: '提示',
        content: '确认删除示例么？',
        'ok-text': '确认',
        onOk: () => {
          this.data_spi_example_list.splice(index, 1);
        }
      });
    },
    // 新函数
    showContentPart () {
      if (this.form_data.contentType === 'application/json') {
        this.showContent = true;
      }
    },
    // 更新model外部的数据
    data_update_model_external (data, type, index) {
      if (type === 'create_model') {
        // 现在为不包含子项的处理 需要在提交的时候2次数据规范
        api.yop_modalManagement_modal_create(data).then(
          (response) => {
            if (response.status === 'success') {
              this.$ypMsg.notice_success(this, '创建成功');
            } else {
              this.$Modal.error({
                title: '错误',
                content: response.message
              });
            }
          }
        );
      } else {
        for (var i in data) {
          if (i == 'name' || i == 'apiGroup') {
            delete data[i]
          }
        }
        data.id = this.nowId;
        data.version = this.currentModel.version;
        api.yop_modalManagement_modal_update(data).then(
          (response) => {
            if (response.status === 'success') {
              this.$ypMsg.notice_success(this, '修改成功');
            } else {
              this.$Modal.error({
                title: '错误',
                content: response.message
              });
            }
          }
        );
      }
      this.$refs.modal_param_content_model_external.cancel();
    },
    // 更新参数列表哦
    data_update_model (data, type, index, subContent) {
      console.log(data, '<--更新后的data')
      // this.example = data.example
      // var data = data.form_data
      data.additionalProperties = {}
      data.items = {}
      if (data.constrains) {
        if (data.constrains[0] == 'additionalProperties') {
          data.additionalProperties = subContent
        } else if (data.constrains[0] == 'items') {
          data.items = subContent
        }
      }
      var data = JSON.parse(JSON.stringify(data))
      if (type === 'create') {
        this.data_propertiesList.push(data);
        // 现在为不包含子项的处理 需要在提交的时候2次数据规范
      } else {
        // this.data_propertiesList.push(data);

        // 编辑查看
        // if(data.type[1]){
        //     data["format"] = data.type[1]
        // }
        for (var i in data) {
          this.data_propertiesList[index][i] = data[i]
        }
      }
      for (var i = 0; i < this.data_propertiesList.length; i++) {
        var data_propertiesListI = this.data_propertiesList[i]
        for (var j in data_propertiesListI) {
          if (j == 'orderNo') {
            if (data_propertiesListI[j]) {
              this.bizOrderVariable = {
                'part': 'REQUEST',
                'location': 'QUERY',
                'variableName': data_propertiesListI['name']
              }
            } else {
              delete data_propertiesListI['orderNo']
              this.bizOrderVariable = {}
            }
          }
        }
      }
      this.$refs.param_operate.cancel();
      this.$refs.param_operate.$refs.form_data_right.resetFields();
      this.$refs.param_operate.$refs.form_data_left.resetFields();
      // 新增后端服务mapping列表
      this.setMappingListData();
    },
    setMappingListData (initData) {
      var position = ''
      if (this.paramPositionShow == 'query') {
        position = 'QUERY'
      } else {
        position = 'BODY'
      }
      this.mapping_list_temp = [];
      console.log(this.data_propertiesList, '设置映射列表初始数据');
      for (var j = 0; j < this.data_propertiesList.length; j++) {
        var obj = {};
        var data_propertiesListJ = this.data_propertiesList[j]
        // 去掉 array/array  数组元素 string/string enum值
        if(data_propertiesListJ.type[0] === 'array' && data_propertiesListJ.type[1] === 'array') {
          if(data_propertiesListJ.items) {
            if (data_propertiesListJ.items.type[0] === 'string' && data_propertiesListJ.items.type[1] === 'string') {
              delete data_propertiesListJ.items.enum
            }
          }
        }
        for (var i in data_propertiesListJ) {
          if (i === 'name') {
            obj.interface = data_propertiesListJ[i]
          }
          if ((i === 'backendName' || i === 'x-yop-apigateway-backend-name') && data_propertiesListJ[i]) {
            obj.backend_name = obj.backendName = data_propertiesListJ[i]
          }
          if (i === 'type') {
            obj.type = data_propertiesListJ[i][0]
          }
          obj.backend_prosition = position
          obj.position = position
        }
        this.mapping_list_temp.push(obj)// todo
      }
      console.log(this.mapping_list_temp, '<--mapping_list_temp')
      console.log(initData, '<--initData')
      this.$emit('setServiceList', this.mapping_list_temp, initData)
    },
    // 新增参数
    new_param (panel, external) {
      this.$refs[panel].compatible(this.underLine)
      this.$refs.param_operate.data_spi_example_list = []
      if (this.paramPositionShow == 'body_schema' || this.paramPositionShow == 'body_query') {
        this.$refs.param_operate.setBodyType(false);
      }

      if (!this.form_data.apiGroup && !localStorage.APIgroupCode) {
        this.$Modal.warning({
          title: '警告',
          content: '请先选择API分组'
        });
      } else {
        this.form_data.$ref = ''
        this.$refs[panel].current_panel_set('create');
        // var showOrder = false
        // if(this.form_data.httpMethod == "POST" || this.form_data.httpMethod == "GET"){
        //     if(this.form_data.contentType == "application/x-www-form-urlencoded" || this.form_data.contentType == "multipart/form-data"){
        //         showOrder = true
        //     }
        // }
        this.$refs[panel].show(this.data_propertiesList, 'create', false);
        this.$refs[panel].resetRef();
        var self = this
        setTimeout(function () {
          self.$refs[panel].$refs.form_data_left.resetFields();
          self.$refs[panel].$refs.form_data_right.resetFields();
          // self.$refs[panel].$refs.subContent_map.$refs.form_data_left2.resetFields();
          // self.$refs[panel].$refs.subContent_map.$refs.form_data_right2.resetFields();
          self.$refs[panel].$refs.subContent_array.$refs.form_data_left2.resetFields();
          self.$refs[panel].$refs.subContent_array.$refs.form_data_right2.resetFields();
          // self.$refs[panel].$refs.subContent_map.type_rule_init();
          self.$refs[panel].$refs.subContent_array.type_rule_init();
          self.$refs[panel].type_rule_init();
          self.$refs[panel].form_data.name = '';
          self.$refs[panel].form_data.orderNo = false;
        }, 300)
        this.$refs[panel].form_data.type = [];
        var apiGroup = this.form_data.apiGroup || this.current_apiGroup
        this.$refs[panel].setApiGroup(apiGroup);
        this.$refs[panel].type_rule_init();
        // this.current_index = -1;
        if (external) {
          // this.current_modify_model = dataTemp;
        }
      }
    },
    // 数据更新
    update_form_data_json (index, data1, data2) {
      this.data_request_json[index].name = data1.name;
    },
    // 本地数据获取
    setRequestData (data, bizOrderVariable, sensitiveVariables) {
      console.log('setRequestData--------')
      console.log(data, '<--<---参数列表')
      console.log(bizOrderVariable)
      console.log(sensitiveVariables)
      this.bizOrderVariable = bizOrderVariable
      this.sensitiveVariables = sensitiveVariables
      this.form_data.httpMethod = data.httpMethod
      this.form_data.encrypt = data.encrypt
      this.setContentType(data.httpMethod)
      if (this.current_status !== 'create') {
        this.form_data.requestUrl = data.path
      }

      if (data.httpMethod === 'GET' || data.httpMethod === 'DELETE') {
        this.form_data.contentType = 'application/x-www-form-urlencoded'
        this.content_type('application/x-www-form-urlencoded')
        this.data_propertiesList = this.edit_format_submit_param_query(data.parameters)
        this.setMappingListData(true);// 设置服务的数据
      }
      if (data.httpMethod === 'PUT') {
        this.form_data.contentType = 'application/octet-stream'
        this.content_type('application/octet-stream')
        this.form_data.description = data.requestBody.description
      }
      if (data.httpMethod === 'POST') {
        this.form_data.contentType = Object.keys(data.requestBody.contents)[0]// 可能有多个返回数据，这个不好处理
        this.content_type(this.form_data.contentType);
        if (this.form_data.contentType === 'application/x-www-form-urlencoded' || this.form_data.contentType === 'multipart/form-data') {
          this.$refs.param_operate.setBodyType(false);
          this.form_data.description = data.requestBody.description
          this.data_spi_example_list = data.requestBody.contents[this.form_data.contentType].examples || []
          var schema = JSON.parse(data.requestBody.contents[this.form_data.contentType].schema);
          this.propertiesDetails = schema.properties;
          var requiredArr = schema.required
          var arr = []
          for (let i in this.propertiesDetails) {
            let o = {};
            o['name'] = i;
            // 兼容旧的
            if (i.indexOf('_') > -1) {
              this.underLine = true
              this.$emit('setUnderLine', this.underLine)
            }
            for (var j in this.propertiesDetails[i]) {
              // 每个属性
              o[j] = this.propertiesDetails[i][j]
              if (j == 'type') {
                o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
              } else if (j == '$ref') {
                o['type'] = ['object', 'object']
              } else if (j == 'additionalProperties') {
                o['type'] = ['object', 'map']
              }
            }
            for (var k in o) {
              if (k == 'format') {
                o['type'][1] = o[k]
              }
            }
            arr.push(o);
          }
          if (requiredArr) {
            for (var i = 0; i < requiredArr.length; i++) {
              var requiredArrI = requiredArr[i]
              for (var j = 0; j < arr.length; j++) {
                if (requiredArrI == arr[j].name) {
                  arr[j].required = true
                }
              }
            }
          }
          if (this.sensitiveVariables) {
            for (var i = 0; i < this.sensitiveVariables.length; i++) {
              var sensitiveArrName = this.sensitiveVariables[i].variableName
              for (var j = 0; j < arr.length; j++) {
                if (sensitiveArrName == arr[j].name) {
                  arr[j]['sensitive'] = true
                }
              }
            }
          }
          var detailData = {
            data_propertiesList: arr
          }
          this.data_propertiesList = detailData.data_propertiesList
          this.setMappingListData(true);// 设置服务的数据
          this.edit_format_submit_param(detailData.data_propertiesList);
        }
        if (this.form_data.contentType === 'application/json') {
          this.form_data.model = JSON.parse(data.requestBody.contents[this.form_data.contentType].schema).$ref.split('/').pop()
          this.data_spi_example_list = data.requestBody.contents[this.form_data.contentType].examples || []
          this.form_data.description = data.requestBody.description
        }
        if (this.form_data.contentType === 'application/octet-stream') { this.form_data.description = data.requestBody.description }
      }

      this.form_data.queryType = data.parameterHandlingType
      if (data.parameterHandlingType === 'MAPPING') { this.queryTypeText = '映射' }
      if (data.parameterHandlingType === 'PASSTHROUGH') { this.queryTypeText = '透传' }
      this.query_type(data.parameterHandlingType)// 选择映射
    },
    // 数据赋值
    data_assignment (subData, param) {
      if (subData.content[param]) {
        let data_temp = subData.content[param].schema.properties;
        return this.data_rehandler(data_temp);
      } else {
        return [];
      }
    },
    // 数据处理
    data_rehandler (data) {
      let result = []
      for (var i in data) {
        let temp = {
          param: i,
          type: []
        }
        for (var j in data[i]) {
          if (util.swagger_properties[j] === 'type') {
            temp['type'][0] = data[i][j];
            if (data[i][j] === 'object') {
              if (data[i]['additionalProperties']) {
                temp['type'][1] = 'map';
                temp['children'] = this.data_rehandler(data[i]['additionalProperties'])
              }
            } else if (data[i][j] === 'array') {
              if (data[i]['items']) {
                temp['type'][1] = 'array';
                temp['children'] = this.data_rehandler(data[i]['items'])
              }
            }
          } else if (util.swagger_properties[j] === 'format') {
            temp['type'][1] = data[i][j];
          } else {
            temp[util.swagger_properties[j]] = data[i][j];
          }
        }
        result.push(temp);
      }
      return result;
    },
    // 点击model跳转
    model_click (model_name) {
      let result = model_name.split('/')
      this.$emit('model_jump', result[result.length - 1])
    },

    detail_param (index, data, current_data, panel) {
      // this.$refs[panel].$refs.form_data_left.resetFields();
      // this.$refs[panel].$refs.form_data_right.resetFields();
      this.$refs[panel].current_panel_set(current_data);
      this.$refs[panel].current_index_set(index);
      var apiGroup = this.current_modelCode
      this.$refs[panel].form_data_set(JSON.parse(JSON.stringify(data)), this.sensitiveVariables, this.bizOrderVariable);
      this.current_index = -1;
      this.$refs[panel].show();
    },
    edit_param (index, data, current_data, panel) {
      if (!data.orderNo) {
        delete data.orderNo
      }
      // 业务编号

      // this.$refs[panel].$refs.form_data_left.resetFields();
      // this.$refs[panel].$refs.form_data_right.resetFields();
      this.$refs[panel].current_panel_set('modify');
      this.$refs[panel].current_index_set(index);
      var apiGroup = this.current_apiGroup || localStorage.getItem('modelApiGroup')
      this.$refs[panel].setApiGroup(apiGroup);
      this.$refs[panel].compatible(this.underLine)
      // if(data.format){
      //     data.type = [data.type,data.format]
      // }else{
      //     data.type = data.type
      // }
      if (typeof data.type == 'object') {
        data.type = data.type
      } else {
        data.type = [data.type, data.type == 'object' ? 'map' : data.type]
      }
      // string没有formate，组件需要
      if (data.type[0] === 'string' && !data.type[1]) {
        data.type[1] = 'string'
      }
      // if(data.format){
      //     data.type[1] = data.format
      // }
      var showOrder = false
      if (this.form_data.httpMethod == 'POST' || this.form_data.httpMethod == 'GET') {
        if (this.form_data.contentType == 'application/x-www-form-urlencoded' || this.form_data.contentType == 'multipart/form-data') {
          showOrder = true
        }
      }
      if (this.bizOrderVariable && this.bizOrderVariable.variableName && (this.bizOrderVariable.variableName == data.name)) {
        showOrder = true
        data['orderNo'] = true
      }
      if (this.bizOrderVariable && this.bizOrderVariable.variableName && (this.bizOrderVariable.variableName !== data.name)) {
        showOrder = false
      }
      if (!this.bizOrderVariable || !this.bizOrderVariable.variableName) {
        showOrder = true
      }
      this.$refs[panel].form_data_set(JSON.parse(JSON.stringify(data)), this.data_propertiesList, showOrder);
      this.current_modify_model = current_data;
      this.current_index = -1;
      this.$refs[panel].show(this.data_propertiesList, 'modify', showOrder);
    },
    delete_param_in_panel (index, params) {
      var self = this
      this.$Modal.confirm({
        title: '提示',
        content: '确定删除？',
        'ok-text': '确认',
        onOk: () => {
          this.data_propertiesList.splice(index, 1);
          this.setMappingListData(true);
        }
      });
    },
    // 过滤描述中的标签
    filter_data_propertiesList_des(data) {
      data.forEach((item) => {
        let reg=/<[^<>]+>/g;             
        item.description=item.description.replace(reg,'')
      })
    },
  },
  mounted () {
    console.log(this.reqModelSchema)
    this.init();
    // 更新dto名称
    bus.$on('updateDtoName', (name) =>{
      this.reqModelSchema[this.form_data.contentType].modelRef = name
      this.data_propertiesStr = name
    });
  },
  beforeDestroy () {
    bus.$off('updateDtoName');
  },
};
</script>

<style>
.ivu-input-group-small>.ivu-input-group-prepend {
        background: #fff!important;
        padding-right:0!important;

    }
.spiName input,.spiName_gray input {
        padding-left: 10!important;
    }
    .ivu-input-group-small .ivu-input{
        padding-left: 0!important;
    }
    #inputRequsetVersion .ivu-input{
        /* border-left:0!important;
        border-right:0!important; */
        width:50px!important;
    }
    #inputRequsetPath .ivu-input{
        /* border-left:0!important; */
    }
   .ivu-input-group-small>.ivu-input-group-prepend.gray{
        background:#f3f3f3!important;
        padding-right:0!important;
        color:#ccc!important;;
    }
    .white {
        background:#fff;
    }
    .gray {
        background:#f3f3f3;
    }
    .inlineBlock {
        display:inline-block!important;
    }
    .fixMargin>div {
        margin-left:-16px!important;
    }
    .inlineBlock .ivu-form-item-error-tip {
        white-space: nowrap!important;
    }
</style>
