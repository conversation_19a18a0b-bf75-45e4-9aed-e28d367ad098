<style lang="less">
    @import '../../../styles/common.less';
    @import '../api_Mangement_Open.less';
    @import '../../regression-test/regression.less';
</style>
<template>
    <Row>
        <Card dis-hover>
            <Row>
                <Col class="margin-bottom-10">
                <p v-show="this.current_status !== 'description'">新增：
                        <Select id="select_basic_1" ref = "selectSpi" v-model="spi_group_model" size="small" class="yop-length-450" filterable clearable placeholder="" @on-change="addSpiList($event)">
                            <Option v-for="item in spi_group_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
                        </Select>
                </p>
                </Col>
                <Col class="margin-bottom-30">
                <Table id='table_spi' border ref="spiList" :columns="current_status !=='description'?columns_spiList:columns_spiList_detail" :data="data_spiList"></Table>
                <loading :show="show_loading_errCommon"></loading>
                </Col>
            </Row>

        </Card>
    </Row>
</template>

<script>
    import api from '../../../api/api.js';
    import loading from '../../my-components/loading/loading';
    import util from '../../../libs/util.js'
    export default {
        name: 'business_error-code',
        components :{
            loading
        },
        data () {
            return {
                spi_group_model:"",
                spi_group_list:"",
                //当前apiUri
                current_apiUri: '',
                // 当前api分组
                current_apiGroup: '',
                // 当前api分组名称
                current_apiGroupName: '',
                /**
                 * 表格部分数据
                 */
                // spec loading显示数据绑定
                show_loading_errSpec : true,
                // common loading 显示数据绑定
                show_loading_errCommon : false,
                // 当前弹窗状态
                current_status: 'create',
                // 当前修改行数
                current_index:0,
                // 表格列数据common
                columns_spiList : [
                    {
                        title: '名称',
                        key: 'value',

                    },
                    {
                        title: '描述',
                        align: 'center',
                        key: 'name',
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'solution',
                        width: 150,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.deleteSpi(params.row);
                                        }
                                    }
                                }, '移除')
                            ]);
                        }
                    }
                ],
                data_spiList:[],
                data_spiList_params:[],
                
                columns_spiList_detail:[
                {
                        title: '名称',
                        key: 'value',

                    },
                    {
                        title: '描述',
                        align: 'center',
                        key: 'name',
                    }
                ],
                // 表格数据common
                editInlineData_common : [
                ],
                // 表格列数据spec
                editInlineColumns_spec : [
                    {
                        title: '名称',
                        key: 'value',

                    },
                    {
                        title: '描述',
                        align: 'center',
                        key: 'name',
                    },
                   
                ],
                // 表格数据spec
                editInlineData_spec : [],

                /**
                 * 错误码详情弹窗部分数据
                 */
                // 错误码详情弹窗是否显示
                modal_create_errorCode : false,
                // 新增和编辑label  true:新增/false: 编辑　
                create_orEdit: true,
               
                // 解决方案当前tab
                tabNow: 'err_solution_in',
                /**
                 * 解决方案详情弹窗部分数据
                 */
                // 解决方案弹窗label
                modal_solution: false,
                // 对内解决方案数据绑定
                innerSolution : '',
                // 对外解决方案数据绑定
                outerSolution : '',
              
                // 解决方案loading
                show_loading_solution :false,
                spiGroupList:[]
            }
        },
        methods : {
            /**
             * 表格部分方法
             */
            // 新增参数按钮调用方法
            new_errorCode () {
                this.create_orEdit = true;
                this.modal_create_errorCode = true;
            },
            handleOnstart1 () {

            },
            handleOnend1 () {

            },
            // spi列表
            getSpisList(apiGroup){
                    var param = {apiGroup:apiGroup}
                    api.yop_spiManagement_spi_simple_list(param).then(
                        (response) =>{
                            var response = response.data
                            this.spi_group_list = [];
                            let spigroup = response.data.result;
                            // this.data_api_model= apigroup[0].groupCode;
                            for(var i in spigroup){
                                // select列表
                                this.spi_group_list.push(
                                    {
                                        label: spigroup[i].title+'('+spigroup[i].name+')',
                                        value: spigroup[i].name,
                                        name: spigroup[i].title
                                    }
                                )
                            }
                            this.setSpiGroupList(this.spiGroupList);//表格列表
                        }
                    );
            },
            addSpiList(value){
                if(this.data_spiList.length <= 0){
                    this.data_spiList_params.push(value)//传参
                    for(var j = 0 ;j < this.spi_group_list.length ; j ++){
                        var spi_group_listJ = this.spi_group_list[j]
                        if(spi_group_listJ["value"] == value){
                            this.data_spiList.push(spi_group_listJ)//表格数据
                        }
                    }
                }else{
                    if(value){
                        var flag = true
                        for(var i in this.data_spiList_params){
                            var data_spiList_paramsI = this.data_spiList_params[i]
                            if(data_spiList_paramsI === value){
                                this.$ypMsg.notice_warning(this, "此回调已添加");
                                flag = false
                                return flag
                            }else{
                                flag = true
                            }
                        }
                        if(flag){
                             for(var j = 0 ;j < this.spi_group_list.length ; j ++){
                                var spi_group_listJ = this.spi_group_list[j]
                                if(spi_group_listJ["value"] == value){
                                    console.log(spi_group_listJ,2)
                                    this.data_spiList.push(spi_group_listJ)//表格数据
                                }
                            }
                            this.data_spiList_params.push(value)//传参
                        }
                        this.clearSpiList();
                    }
                }
            },
            clearSpiList(){
                this.spi_group_model = ""
                this.$refs.selectSpi.clearSingleSelect();
            },
            deleteSpi(row){
                this.$Modal.confirm({
                    title: '删除spi',
                    content: '确定删除spi？',
                    'ok-text': '确认',
                    onOk: () => {
                        var index = row._index
                        console.log(index)
                        this.data_spiList.splice(index,1)
                        this.data_spiList_params.splice(index,1)
                        this.$forceUpdate();
                    }
                });
            },
            setSpiGroupList(spiGroupList){
                for(var i = 0 ;i < spiGroupList.length ; i ++){
                    var spiGroupListI = spiGroupList[i]
                    for(var j = 0 ;j < this.spi_group_list.length ; j ++){
                        var spi_group_listJ = this.spi_group_list[j]
                        if(spi_group_listJ["value"] == spiGroupListI){
                            console.log(spi_group_listJ,3)
                            this.data_spiList.push(spi_group_listJ)//表格数据
                        }
                    }
                }
                console.log(spiGroupList,4)
                this.data_spiList_params = spiGroupList//参数

            },
            // 本地数据获取
            setRequestData (data) {
                this.spiGroupList = data
            },
            saveCheck(){
                // 回调信息的验证
                // if(this.data_spiList_params.length <= 0){
                //     this.$ypMsg.notice_error(this,'错误',"至少选择一条回调");
                //     return false
                // }else{
                    var param = {
                        paramList : this.data_spiList_params,
                    }
                    this.$emit("passParamCallback",JSON.parse(JSON.stringify(param)))
                    return true
                // }
                
            },
            
            set_current_status(val){
                this.current_status = val
            },
            // 设置表格spec数据
            setDataSpec (data) {
                this.current_apiUri = data;
                // this.editInlineData_spec =data;
                this.specData_get();
            },
            // spec表格数据获取
            specData_get(){
                this.show_loading_errSpec =true;
                this.editInlineData_spec = []
                api.yop_errorCode_apiSpecific_list({apiUri : this.current_apiUri}).then(
                    (response)=>{
                        if(response.data.status === 'success'){
                            let result = response.data.data.result
                            for(var i in result){
                                this.editInlineData_spec.push({
                                    id : result[i].id,
                                    errorCodeType: 'API特有',
                                    errorCode: result[i].errorCode,
                                    subCode: result[i].subErrorCode,
                                    subMsg: result[i].subErrorMsg
                                })
                            }
                            this.show_loading_errSpec =false;
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            this.show_loading_errSpec =false;
                        }

                    }
                )
            },
            // 去除spec loading
            clearLoading (){
                this.show_loading_errSpec =false;
            },
          
        }
    };
</script>

<style scoped>
    a:hover {
        font-size: 13px;
        color: black;
        /*text-decoration: underline;*/
    }
</style>
