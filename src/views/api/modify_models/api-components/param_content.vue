<template>
    <!--<Card dis-hover>-->
        <!--<a href="#" slot="extra" @click.prevent="changeLimit">-->
            <!--<Icon type="ios-loop-strong"></Icon>-->
            <!--编辑-->
        <!--</a>-->
        <Row>
            <Col span="24" style="text-align: right;margin-bottom: 20px;">
            <!--<a href="#" slot="extra" @click.prevent="changeLimit">-->
            <!--<Icon type="ios-loop-strong"></Icon>-->
            <!--编辑-->
            <!--</a>-->
            <Button v-show="!content_status" type="primary" icon="edit" @click="status_change">编辑</Button>
            <Button v-show="content_status" type="success" icon="archive" @click="status_change">保存</Button>
            </Col>
            <Col span="12">
                <!--<Card>-->
                    <Form ref="form_data_left" :model="form_data_left" :label-width="120">
                        <FormItem v-show="content_status" label="参数：" prop="param">
                            <Input type="text"  v-model="form_data_left.param"
                                   style="width:85%"></Input>
                        </FormItem>
                        <FormItem v-show="!content_status && form_data_left.param" label="参数：" prop="param">
                            {{form_data_left.param}}
                        </FormItem>
                        <FormItem v-show="content_status" label="名称：" prop="name">
                            <Input type="text"  v-model="form_data_left.name"
                                   style="width:85%"></Input>
                        </FormItem>
                        <FormItem v-show="!content_status && form_data_left.name" label="名称：" prop="name">
                            {{form_data_left.name}}
                        </FormItem>
                        <FormItem v-show="content_status" label="类型：" prop="type">
                            <Input type="text"  v-model="form_data_left.type"
                                   style="width:85%"></Input>
                        </FormItem>
                        <FormItem v-show="!content_status && form_data_left.type" label="类型：" >
                            {{form_data_left.type}}
                        </FormItem>
                        <FormItem v-show="content_status" label="示例值：" prop="sample">
                            <Input type="textarea"  v-model="form_data_left.sample"
                                   style="width:85%"></Input>
                        </FormItem>
                        <FormItem v-show="!content_status && form_data_left.sample" label="示例值：" >
                            {{form_data_left.sample}}
                        </FormItem>
                        <FormItem v-show="content_status" label="描述：" prop="description">
                            <text-editor-size style="width: 85%" size="'85%'" :ref="textEditorId" :id="textEditorId"></text-editor-size>
                        </FormItem>
                        <FormItem v-show="!content_status && form_data_left.description" label="描述：">
                            <div v-html="form_data_left.description"></div>
                        </FormItem>
                    </Form>
                <!--</Card>-->
            </Col>
            <Col span="12">
                <!--<Card>-->
                    <Form ref="form_data_right" :model="form_data_right" :label-width="120">
                        <FormItem v-show="content_status" label="端点参数顺序：" prop="param">
                            <Input type="text"  v-model="form_data_right.param_index"
                                   style="width:85%"></Input>
                        </FormItem>
                        <FormItem v-show="!content_status && form_data_right.param_index" label="端点参数顺序：">
                            {{form_data_right.param_index}}
                        </FormItem>
                        <FormItem v-show="content_status" label="端点参数名称：" prop="name">
                            <Input type="text"  v-model="form_data_right.param_name"
                                   style="width:85%"></Input>
                        </FormItem>
                        <FormItem v-show="!content_status && form_data_right.param_name" label="端点参数名称：">
                            {{form_data_right.param_name}}
                        </FormItem>
                        <FormItem v-show="content_status" label="内部参数：" prop="type">
                            <Input type="text"  v-model="form_data_right.internal"
                                   style="width:85%"></Input>
                        </FormItem>

                        <FormItem v-show="content_status" label="敏感字段：" prop="sample">
                            <Input type="textarea"  v-model="form_data_right.sensitive"
                                   style="width:85%"></Input>
                        </FormItem>

                    </Form>
                <!--</Card>-->
            </Col>
        </Row>
    <!--</Card>-->
</template>

<script>
    import textEditorSize from '../../../my-components/text-editor/text-editor-size';
    export default {
        name: 'param_content',
        components :{
            textEditorSize
        },
        props: {
            textEditorId: String,
            newItem : Boolean,
            index : Number
        },
        // watch:{
        //     newItem : {
        //         handler(nval,oval) {
        //             this.content_status =nval;
        //         },
        //         deep : true
        //     }
        // },
        data (){
            return {
                // 左侧表格内容数据绑定
                form_data_left : {
                    param : '', // 参数
                    name : '', // 名称
                    type : '', // 类型
                    sample: '', // 示例值
                    description : '', // 描述
                    required : false

                },
                //
                // 左侧表格内容数据绑定
                form_data_right : {
                    internal : '', // 参数
                    sensitive : '', // 名称
                    param_index : '', // 类型
                    param_name: '', // 示例值



                },
                // 当前内容是编辑还是预览 true 为编辑 false为预览
                content_status: false
            }
        },
        methods : {
            // 页面初始化
            init(){
                // this.content_status = this.newItem
            },
            // 编辑预览状态转变
            status_change () {
                this.content_status =!this.content_status;
                // 若果是保存 数据更新
                if(!this.content_status){
                    this.form_data_left.description = this.$refs[this.textEditorId].getContent()
                    this.$emit('update_form_data',this.index,this.form_data_left,this.form_data_right)
                }
            },
            // 设置当前的状态
            set_current_status (status) {
                this.content_status = status
            }
        },
        mounted () {
            this.init();
        },
    };
</script>

<style scoped>

</style>
