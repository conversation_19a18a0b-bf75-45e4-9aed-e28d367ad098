<style lang="less">
    .width-50-perc{
        width:45%;
    }
    .width-100-perc{
        width:100%;
    }
    .margin-top-0{
        margin-top : 0!important;
    }
</style>
<template>
    <Row>
        <Col span="12">
        <!--<Card>-->
        <Form :ref="'form_data_left'+this.depth" :model="form_data" :label-width="120" inline>
            <!--<FormItem class="width-50-perc" label="参数：" prop="param" :rules="{required:true,message:'参数不能为空',trigger: 'blur'}">-->
            <!--<Input type="text" v-model="form_data.param"-->
            <!--style="width:85%"></Input>-->
            <!--</FormItem>-->

            <!--<FormItem class="width-50-perc" label="名称：" prop="name" :rules="{required:true,message:'名称不能为空',trigger: 'blur'}">-->
            <!--<Input type="text" v-model="form_data.name"-->
            <!--style="width:85%"></Input>-->
            <!--</FormItem>-->
            <FormItem class="width-50-perc" v-if="!this.form_data_visual.type" label="类型：" prop="type" :rules="{required:true,message:'类型不能为空'}">
                <Cascader :data="type_data" v-model="form_data.type" style="width:85%" @on-change="cascader_change"></Cascader>
            </FormItem>
            <FormItem class="width-50-perc" v-if="this.form_data_visual.type" label="类型：" prop="type" :rules="{required:true,message:'类型不能为空'}">
                <Cascader :data="type_data_cut" v-model="form_data.type" style="width:85%" @on-change="cascader_change"></Cascader>
            </FormItem>
            <!--<FormItem class="width-50-perc" label="是否必填：" prop="required">-->
            <!--<i-switch  size="large" v-model="form_data.required">-->
            <!--<Icon type="android-done" slot="open"></Icon>-->
            <!--<Icon type="android-close" slot="close"></Icon>-->
            <!--</i-switch>-->
            <!--</FormItem>-->
            <FormItem v-show="this.form_data.type[1] !== 'object' " class="width-50-perc">

            </FormItem>
            <FormItem v-show="this.form_data.type[1] === 'object' " label="models:" class="width-50-perc" prop="model" :rules="{required:true,message:'model不能为空'}">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>models：</p>
                
                <common-select ref="select_model" @on-update="updateSelect_model"
                               type="normal"
                               keyWord="result"
                               holder="请选择model"
                               code="value"
                               title="title"
                               group="api_model"
                               @on-loaded="select_callBack"
                               :default="this.form_data.model"
                               :uri="this.$store.state.select.api_model.uri"
                               style="width:85%;margin-top:0!important"></common-select>
            </FormItem>
            <FormItem class="width-100-perc" label="示例值：" prop="sample">
                <Input type="textarea" v-model="form_data.sample"
                       style="width:85%"></Input>
            </FormItem>

            <FormItem class="width-100-perc" label="描述：" prop="description">
                <text-editor-size style="width: 85%" size="'85%'" ref="description_param"
                                  :id="'description_param_model'+this.depth"></text-editor-size>
            </FormItem>
        </Form>
        <!--</Card>-->
        </Col>
        <Col span="12">
        <!--<Card>-->
        <Form :ref="'form_data_right'+this.depth" :model="form_data" :label-width="120" inline>
            <!--<FormItem  class="width-50-perc" label="端点参数顺序：" prop="param_index" :rules="{required:true,message:'端点参数顺序不能为空',trigger: 'blur'}">-->
            <!--<Input type="text" v-model="form_data.param_index"-->
            <!--style="width:85%"></Input>-->
            <!--</FormItem>-->
            <!--<FormItem  class="width-50-perc" label="端点参数名称：" prop="param_name">-->
            <!--<Input type="text" v-model="form_data.param_name"-->
            <!--style="width:85%"></Input>-->
            <!--</FormItem>-->
            <FormItem v-show="this.more_attr" label="默认值：" prop="default" class="width-100-perc">
                <Input type="text" v-model="form_data.default"
                       style="width:55%"></Input>
                <common-select ref="select_common_default" @on-update="updateSelect_common_default"
                               v-show="this.form_data.type[1] !== 'boolean' "
                               type="normal"
                               keyWord="result"
                               holder="常用默认值"
                               code="value"
                               title="title"
                               group="api_commmon_sample"
                               @on-loaded="select_callBack"
                               :default="this.data_select_sample"
                               :uri="this.$store.state.select.api_commmon_sample.uri"
                               style="width:30%;margin-top:0"></common-select>
            </FormItem>
            <!--<FormItem v-show="this.more_attr" class="width-50-perc" label="内部参数：" prop="internal" >-->
            <!--<i-switch size="large" v-model="form_data.internal ">-->
            <!--<Icon type="android-done" slot="open"></Icon>-->
            <!--<Icon type="android-close" slot="close"></Icon>-->
            <!--</i-switch>-->
            <!--</FormItem>-->

            <FormItem v-show="this.more_attr" class="width-100-perc" label="敏感字段：" prop="sensitive" >
                <i-switch  size="large" v-model="form_data.sensitive">
                    <Icon type="android-done" slot="open"></Icon>
                    <Icon type="android-close" slot="close"></Icon>
                </i-switch>
            </FormItem>
            <FormItem v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc" label="最大值：">
                <Input type="text" v-model="form_data.maximum"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc" label="是否排除最大值：">
                <i-switch size="large" v-model="form_data.exclusiveMaximum">
                    <Icon type="android-done" slot="open"></Icon>
                    <Icon type="android-close" slot="close"></Icon>
                </i-switch>
            </FormItem>
            <FormItem  v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc" label="最小值：">
                <Input type="text" v-model="form_data.minimum"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc" label="是否排除最小值：">
                <i-switch size="large" v-model="form_data.exclusiveMinimum">
                    <Icon type="android-done" slot="open"></Icon>
                    <Icon type="android-close" slot="close"></Icon>
                </i-switch>
            </FormItem>
            <FormItem  v-show="this.form_data_visual.length && this.more_attr" class="width-50-perc" label="最大长度：">
                <Input type="text" v-model="form_data.maxLength"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem  v-show="this.form_data_visual.length && this.more_attr" class="width-50-perc" label="最小长度：">
                <Input type="text" v-model="form_data.minLength"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem  v-show="this.form_data_visual.itemSize && this.more_attr" class="width-50-perc" label="最大数目：">
                <Input type="text" v-model="form_data.maxItems"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem  v-show="this.form_data_visual.itemSize && this.more_attr" class="width-50-perc" label="最小数目：">
                <Input type="text" v-model="form_data.minItems"
                       style="width:85%"></Input>
            </FormItem>
            <FormItem v-show="this.form_data_visual.pattern && this.more_attr" label="正则表达式校验："  class="width-100-perc">
                <Input type="text" v-model="form_data.pattern"
                       style="width:55%"></Input>
                <common-select ref="select_common_pattern" @on-update="updateSelect_common_pattern"
                               type="normal"
                               keyWord="result"
                               holder="常用正则表达式"
                               code="value"
                               title="title"
                               group="api_common_pattern"
                               @on-loaded="select_callBack"
                               :default="this.data_select_pattern"
                               :uri="this.$store.state.select.api_common_pattern.uri"
                               style="width:30%;margin-top:0"></common-select>
            </FormItem>
            <FormItem v-show="this.form_data_visual.model && this.more_attr" label="model："  class="width-100-perc">
                <common-select ref="select_model" @on-update="updateSelect_model"
                               type="normal"
                               keyWord="result"
                               holder="请选择model"
                               code="value"
                               title="title"
                               group="api_model"
                               @on-loaded="select_callBack"
                               :default="this.data_select_pattern"
                               :uri="this.$store.state.select.api_model.uri"
                               style="width:85%"></common-select>
            </FormItem>
            <FormItem v-show="this.form_data_visual.enums && this.more_attr" class="width-100-perc" label="枚举：">
                <div v-if="item.status" v-for="(item,index) in enum_data" style="margin-bottom:10px;">
                    <Input type="text" v-model="item.value" style="width:70%" @on-change="enum_change(index)"></Input><Button style="width:10%;margin-left: 10px;" v-if="index+1 !== enum_data.length" type="error" @click="enumRemove(index)">删除</Button>
                </div>
            </FormItem>
            <FormItem  class="width-100-perc" >
                <div style="width: 85%">
                    <Button v-show="!this.more_attr" style="float:right;" size="small" circle type="info" @click="attr_expand">更多属性</Button>
                    <Button v-show="this.more_attr" style="float:right;" size="small" circle type="info" @click="attr_hide">隐藏</Button>
                </div>
            </FormItem>

        </Form>
        </Col>
        <Col v-if="this.form_data_visual.additionalProperties||this.form_data_visual.items" style="border-bottom: 1px solid #eee;margin-bottom: 20px;" span="24">

        </Col>
        <Col v-if="this.form_data_visual.additionalProperties" span="24">
        <Tag type="dot" color="blue">映射值：</Tag>
        <param_main_content :ref="'map_'+this.depth+1" :depth="this.depth+1" :type_format="this.type_data" :type_format_cut="this.type_data_cut"></param_main_content>
        </Col>
        <Col v-if="this.form_data_visual.items" span="24">
        <Tag type="dot" color="green">数组元素：</Tag>
        <param_main_content  :ref="'array_'+this.depth+1":depth="this.depth+1" :type_format="this.type_data" :type_format_cut="this.type_data_cut"></param_main_content>
        </Col>
    </Row>
</template>

<script>
    import textEditorSize from '../../../my-components/text-editor/text-editor-size';
    import commonSelect from '../../../common-components/select-components/selectCommon';
    import label_icon from '../../../common-components/icon-components/star_red'

    export default {
        name: 'param_main_content_model',
        components: {
            textEditorSize,
            commonSelect,
            label_icon
        },
        props: {
            depth: Number,
            type_format: Array,
            type_format_cut : Array
        },
        data () {
            return {
                // 表格内容数据绑定
                form_data: {
                    param: '', // 参数
                    name: '', // 名称
                    type: [], // 类型
                    sample: '', // 示例值
                    description: '', // 描述
                    required: false, // 是否必填

                    // internal : false, // 参数
                    default : '', // 默认值
                    sensitive : false, // 名称
                    param_index : '', // 类型
                    param_name : '', // 示例值
                    maximum : '', // 最大值
                    exclusiveMaximum : false, // 是否排除最大值
                    minimum : '', // 最小值
                    exclusiveMinimum : false,// 是否排除最小值，
                    pattern : '', // 正则表达式
                    maxLength : '', // 最大长度
                    minLength : '', // 最小长度
                    maxItems : '', //最大数目
                    minItems :'' //最小数目
                },

                // 表格显示绑定
                form_data_visual: {
                    extremum : false,
                    enums : false,
                    length : false,
                    pattern : false,
                    itemSize : false,
                    model : false,
                    additionalProperties: false,
                    items : false,
                    type: false
                },
                current_panel: 'create_request_json',
                // 窗口是否展示
                modal_show : false,
                // 示例值绑定
                data_select_sample : '',
                // 常用正则表达式数据绑定
                data_select_pattern : '',
                // 枚举数据绑定
                enum_data : [{
                    value : '',
                    index : 0,
                    status : 1
                }],
                enum_index : 0,
                // 是否展示更多属性 false 为隐藏 true 为显示
                more_attr : false,

                type_data:[],
                type_data_cut:[],
                sample_data:[
                    {
                        value : 'integer',
                        desc : 'integer',
                        formats :[
                            {
                                value : 'int32',
                                desc : 'int32',
                                defaulted : true,
                            },
                            {
                                value : 'int64',
                                desc : 'int64',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'number',
                        desc : 'number',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                            },
                            {
                                value : 'float',
                                desc : 'float',
                                defaulted : true,
                            },
                            {
                                value : 'double',
                                desc : 'double',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'boolean',
                        desc : 'boolean',
                        constrains : [
                            'enums',
                            'length',
                            'pattern'
                        ]

                        // formats :[
                        //     {
                        //         value : '-',
                        //         desc : '-',
                        //         defaulted : true,
                        //     }
                        // ]
                    },
                    {
                        value : 'string',
                        desc : 'string',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                                constrains : [
                                    'enums',
                                    'length',
                                    'pattern'
                                ]
                            },
                            {
                                value : 'byte',
                                desc : 'byte',
                                defaulted : true,
                            },
                            {
                                value : 'binary',
                                desc : 'binary',
                                defaulted : true,
                            },
                            {
                                value : 'date',
                                desc : 'date',
                                defaulted : true,
                            },
                            {
                                value : 'date-time',
                                desc : 'date-time',
                                defaulted : true,
                            },
                            {
                                value : 'password',
                                desc : 'password',
                                defaulted : true,
                            },
                            {
                                value : 'email',
                                desc : 'email',
                                defaulted : true,
                            },
                            {
                                value : 'mobile',
                                desc : 'mobile',
                                defaulted : true,
                            },
                            {
                                value : 'idcard',
                                desc : 'idcard',
                                defaulted : true,
                            },
                            {
                                value : 'bankcard',
                                desc : 'bankcard',
                                defaulted : true,
                            },
                            {
                                value : 'cvv',
                                desc : 'cvv',
                                defaulted : true,
                            },
                            {
                                value : 'uuid',
                                desc : 'uuid',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'array',
                        desc : 'array',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'object',
                        desc : 'object',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                            },
                            {
                                value : 'map',
                                desc : 'map',
                                defaulted : true,
                            }
                        ]
                    },
                ],

            };
        },
        methods: {
            // 页面初始化
            init () {
                // this.content_status = this.newItem
                // this.data_format(this.sample_data);
                if(this.depth < 2){
                    const type_temp = this.type_format;
                    this.type_data = type_temp;
                    this.type_data_cut = this.type_format_cut;
                    this.form_data_visual.type =false;
                }else{
                    this.type_format_handler(this.type_format_cut);
                    this.form_data_visual.type =true;
                    // this.type_data
                }
                this.cascader_format();
            },
            // 下拉框赋值
            type_format_handler (data) {
                let temp = data;
                for(let i = temp.length-1;i>=0;i--){
                    if(temp[i].value === 'object' || temp[i].value === 'array'){
                        temp.splice(i,1);
                    }
                }
                this.type_data_cut = temp;
            },
            // 表单取消
            cancel () {
                this.modal_show = false;
            },
            // 窗口显示
            show () {
                this.modal_show = true;
            },
            // 数据初始化
            form_data_init () {

            },
            // 数据处理
            data_format (array){
                this.type_data = [];
                for (var i in array){
                    let subContent = array[i].formats;
                    let childrenTemp = []
                    for(var j in subContent){
                        if(subContent[j].value === '-'){
                            childrenTemp.push(
                                {
                                    value : array[i].value,
                                    label : array[i].desc,
                                    constrains : subContent[j].constrains ? subContent[j].constrains : [],
                                }
                            );
                        }else{
                            childrenTemp.push(
                                {
                                    value : subContent[j].value,
                                    label : subContent[j].desc,
                                    constrains : subContent[j].constrains ? subContent[j].constrains : [],
                                }
                            );
                        }
                    }
                    this.type_data.push(
                        {
                            value : array[i].value,
                            label : array[i].desc,
                            children: childrenTemp
                        }
                    )
                }
            },
            // 级联 参数类型 数据获取及处理
            cascader_format () {

            },
            // 常用默认值 更新
            updateSelect_common_default (val) {
                this.data_select_sample = val;
                this.form_data.default = val ;
            },
            // 常用正则表达式 更新
            updateSelect_common_pattern (val) {
                this.data_select_pattern = val;
                this.form_data.pattern = val;
            },
            //
            updateSelect_model (val) {

            },
            // 加载完成回调函数
            select_callBack () {

            },
            // 枚举删除函数
            enumRemove(index){
                this.enum_data[index].status = 0;
            },
            // 枚举改变函数
            enum_change (index){
                if(this.enum_data[index].value !== ''){
                    if(index === this.enum_index){
                        this.enum_index++;
                        this.enum_data.push({
                            value : '',
                            index : this.enum_index,
                            status : 1
                        })
                    }
                }
            },
            // 判断枚举值是否为最后一个
            enum_last_check (index){
                if(index === this.enum_index){
                    return true;
                }else{
                    for(var i = index; i<this.enum_index; i++){
                        if(this.enum_data[i].status === 1){
                            return false
                        }
                    }
                    return true
                }
            },
            // 枚举值统计
            enum_result () {
                let result = [];
                for (var i in this.enum_data){
                    if(this.enum_data[i].status === 1 && this.enum_data[i].value !== ''){
                        result.push(this.enum_data[i].value);
                    }
                }
                return result;
            },
            // 类型选定 界面展示
            cascader_change (value,data) {
                this.type_rule_init();
                if(value && value.length > 0){
                    let visual_items = data[1].constrains
                    if(visual_items && visual_items.length > 0){
                        for(var i in visual_items){
                            this.form_data_visual[visual_items[i]] = true;
                        }
                    }
                }
            },
            // 所有校验规则初始化
            type_rule_init () {
                for(var i in this.form_data_visual){
                    this.form_data_visual[i] = false;
                }
            },
            // 展开按钮点击
            attr_expand  () {
                this.more_attr = true;
            },
            // 隐藏按钮点击
            attr_hide () {
                this.more_attr = false;
            },
            // 提交校验
            submit (){
                this.form_submit_validate('form_data_left'+this.depth,'form_data_right'+this.depth)
            },
            // 继续验证
            next_validate(){
                if(this.form_data_visual.additionalProperties){
                    this.$refs['map_'+this.depth+1].submit();
                }else if(this.form_data_visual.items){
                    this.$refs['array_'+this.depth+1].submit();
                }else{
                    // 新建或者编辑操作执行
                }
            },
            form_submit_validate (val1,val2) {
                let result1 = 0;
                let result2 = 0;
                let check = 0;
                this.$refs[val1].validate((valid) => {
                    if (valid) {
                        result1 =1;
                        if(result2 === 1){
                            this.next_validate();
                        }
                    }else {
                        this.form_validate_failed(result2)
                    }
                })
                this.$refs[val2].validate((valid) => {
                    if (valid) {
                        result2 =1;
                        if(result1 === 1){
                            this.next_validate();
                        }
                    }else {
                        this.form_validate_failed(result1)
                    }
                })
            },
        },
        mounted () {
            this.init();
        },
    };
</script>

<style scoped>

</style>
