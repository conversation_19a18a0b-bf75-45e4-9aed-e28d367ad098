<style lang="less">
    @import '../../../styles/common.less';
    @import '../api_Mangement_Open.less';
    @import '../../regression-test/regression.less';
</style>
<template>
    <Row>
        <Card dis-hover>
            <Row>
                <Col class="yop-title-red yop-template-in-title margin-bottom-10">
                *API分组公共错误码，请前往 "API分组-错误码"  页面配置
                </Col>
                <Col class="margin-bottom-30">
                <Tag class="margin-bottom-10" type="dot" color="blue">API分组公共错误码:</Tag>
                <DragableTable
                        v-model="editInlineData_common"
                        :columns-list="editInlineColumns_common"
                        @on-start="handleOnstart1"
                        @on-end="handleOnend1"
                        :header="true"
                        no-data-text="该API分组暂时没有通用业务错误码"
                ></DragableTable>
                <loading :show="show_loading_errCommon"></loading>
                </Col>

                <Col >
                <Tag class="margin-bottom-10" type="dot" color="blue">API特有错误码:</Tag> <Button id="btn_errorcode_1" class="margin-left-10 margin-bottom-10" type="primary" @click="new_errorCode">新增错误码</Button>
                <Table border
                        :data="editInlineData_spec"
                        :columns="editInlineColumns_spec"
                        :header="true"
                ></Table>
                <loading :show="show_loading_errSpec"></loading>
                </Col>
            </Row>

        </Card>
        <Modal id="modal_errorcode_1" v-model="modal_create_errorCode" width="600" :closable="false">
            <p slot="header" style="color:#2d8cf0;">
                <span style="color:black" v-show="create_orEdit">新增错误码</span>
                <span style="color:black" v-show="!create_orEdit">编辑错误码</span>
            </p>
            <div>
                <Form ref="formCustom" :model="formCustom" :rules="ruleCustom" :label-width="120">
                    <FormItem label="错误码类型：">
                        <Col>
                        {{formCustom.errorCodeType}}</Col>
                    </FormItem>
                    <FormItem label="错误码：">
                        <Col>
                        {{formCustom.errorCode_public}}</Col>
                    </FormItem>
                    <FormItem label="子错误码：" prop="subErrorCode">
                        <Input type="text" :disabled="!create_orEdit" size="small" v-model="formCustom.subErrorCode" style="width:80%"></Input>
                    </FormItem>
                    <p class="yop-explain-120">支持：字母、数字、中划线；全部小写</p>
                    <FormItem label="子错误码描述：" prop="subErrorCodeDes">
                        <Input type="textarea" size="small" v-model="formCustom.subErrorCodeDes"
                               style="width:80%"></Input>
                    </FormItem>
                    <p class="yop-explain-120">最大支持50字</p>
                    <FormItem label="解决方案：" prop="solution">
                        <Tabs size="small" :value="tabNow" style="width:80%">
                            <TabPane label="解决方案(对内)" name="err_solution_in">
                                <text-editor ref="err_solution_in" size="350px" :id="'err_solution_in'"></text-editor>
                            </TabPane>
                            <TabPane label="解决方案(对外)" name="err_solution_out">
                                <text-editor style="margin-left:2px;" ref="err_solution_out" size="350px"
                                             :id="'err_solution_out'"></text-editor>
                            </TabPane>
                        </Tabs>
                    </FormItem>
                    <p class="yop-explain-120">最大支持1000字</p>
                </Form>
            </div>
            <div slot="footer">
                <Button id="modal_request_btn_2" type="primary" @click="ok_create_errorCode('formCustom')">确定
                </Button>
                <Button id="modal_request_btn_1" type="ghost" @click="cancel_create_errorCode">取消</Button>
            </div>
        </Modal>
        <Modal id="modal_errorcode_2" v-model="modal_solution" width="90%" :closable="false">
            <p slot="header" style="color:#2d8cf0;">
                <span style="color:black">解决方案</span>
            </p>
            <div>
                <Table id='table_2' border ref="selection" :columns="columns_solutionList"
                       :data="data_solutionList"></Table>
                <Tabs value="solution_in_modal">
                    <TabPane label="解决方案(对内)" name="solution_in_modal">
                        <div v-html="innerSolution"></div>
                    </TabPane>
                    <TabPane label="解决方案(对外)" name="solution_out_modal">
                        <div v-html="outerSolution"></div>
                        <!--{{outerSolution}}-->
                    </TabPane>
                </Tabs>
            </div>
            <div slot="footer">
                <Button id="modal_request_btn_3" type="primary" @click="solution_close">关闭</Button>
            </div>
            <loading :show="show_loading_solution"></loading>
        </Modal>
    </Row>
</template>

<script>
    import textEditor from '../../my-components/text-editor/text-editor-size';
    import DragableTable from '../../tables/components/dragableTable';
    import api from '../../../api/api.js';
    import loading from '../../my-components/loading/loading';
    import util from '../../../libs/util.js'
    export default {
        name: 'business_error-code',
        components :{
            DragableTable,
            textEditor,
            loading
        },
        data () {
            // 子错误码验证
            const validate_subErrorCodeDes = (rule, value, callback) => {
                if(util.getLength(value) > 50){
                    callback(new Error('长度不能大于50'));
                }else{
                    callback();
                }
            };
            // 子错误码描述验证
            const validate_subErrorCode = (rule, value, callback) => {
                if(util.formatCheck2(value)){
                    callback(new Error('格式不正确'));
                }else{
                    callback();
                }
            };
            // 解决方案验证
            const validate_solution = (rule, value, callback) => {
                if(!this.$refs.err_solution_in.getContent_text()){
                    callback(new Error('解决方案(对内)不能为空'));
                }
                if(!this.$refs.err_solution_out.getContent_text()){
                    callback(new Error('解决方案(对外)不能为空'));
                }
                if(util.getLength(this.$refs.err_solution_in.getContent_text()) > 1000){
                    callback(new Error('解决方案(对内)不能超过1000字'));
                }else if(util.getLength(this.$refs.err_solution_out.getContent_text()) > 1000){
                    callback(new Error('解决方案(对外)不能超过1000字'));
                }else{
                    callback();
                }
            };
            return {
                //当前apiUri
                current_apiUri: '',
                // 当前api分组
                current_apiGroup: '',
                // 当前api分组名称
                current_apiGroupName: '',
                /**
                 * 表格部分数据
                 */
                // spec loading显示数据绑定
                show_loading_errSpec : true,
                // common loading 显示数据绑定
                show_loading_errCommon : false,
                // 当前弹窗状态
                current_Status: 'create',
                // 当前修改行数
                current_index:0,
                // 表格列数据common
                editInlineColumns_common : [
                    {
                        title: '错误码类型',
                        align: 'center',
                        key: 'errorCodeType',
                        width: 150,

                    },
                    {
                        title: '错误码',
                        align: 'center',
                        key: 'errorCode',
                        width: 150,

                    },
                    {
                        title: '子错误码',
                        align: 'center',
                        key: 'subCode',
                        width: 200,

                    },
                    {
                        title: '子错误码描述',
                        align: 'center',
                        key: 'subMsg'
                    },
                    {
                        title: '解决方案',
                        align: 'center',
                        key: 'solution',
                        width: 150,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.errorCode_config_solution(params.row.id,'common');
                                        }
                                    }
                                }, '解决方案')
                            ]);
                        }

                    }
                ],
                // 表格数据common
                editInlineData_common : [],
                // 表格列数据spec
                editInlineColumns_spec : [
                    {
                        title: '错误码类型',
                        key: 'errorCodeType',
                        width: 150,

                    },
                    {
                        title: '错误码',
                        align: 'center',
                        key: 'errorCode',
                        width: 150,

                    },
                    {
                        title: '子错误码',
                        align: 'center',
                        key: 'subCode',
                        width: 200,

                    },
                    {
                        title: '子错误码描述',
                        type: 'html',
                        key: 'subMsg'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        width: 250,
                        render: (h, params) => {
                                return h('div', [
                                    h('Button', {
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        style: {
                                            marginRight: '10px'
                                        },
                                        on: {
                                            click: () => {
                                                this.errorCode_config(params.row.id);
                                            }
                                        }
                                    }, '编辑'),
                                    h('Button', {
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        on: {
                                            click: () => {
                                                this.delete_errorCode_config(params.row.id);
                                            }
                                        }
                                    }, '删除'),
                                    h('Button', {
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        on: {
                                            click: () => {
                                                this.errorCode_config_solution(params.row.id,'special');
                                            }
                                        }
                                    }, '解决方案')
                                ]);
                        }
                    }
                ],
                // 表格数据spec
                editInlineData_spec : [],

                /**
                 * 错误码详情弹窗部分数据
                 */
                // 错误码详情弹窗是否显示
                modal_create_errorCode : false,
                // 新增和编辑label  true:新增/false: 编辑　
                create_orEdit: true,
                // 表单数据绑定
                formCustom: {
                    id : '',
                    errorCodeType : 'API指定',
                    errorCode_public : '40044 （业务处理失败）',
                    subErrorCode : '',
                    subErrorCodeDes : '',
                    solution : '123',
                    version : ''
                },
                // 表单验证规则数据绑定
                ruleCustom: {
                    subErrorCode: [
                        {required: true, message: '子错误码不能为空', trigger: 'blur'},
                        { validator: validate_subErrorCode, trigger: 'blur' }
                    ],
                    subErrorCodeDes: [
                        {required: true, message: '子错误码描述不能为空', trigger: 'blur'},
                        { validator: validate_subErrorCodeDes, trigger: 'blur' }
                    ],
                    solution: [
                        {required: true},
                        { validator: validate_solution, trigger: 'blur' }
                    ]
                },
                // 解决方案当前tab
                tabNow: 'err_solution_in',
                /**
                 * 解决方案详情弹窗部分数据
                 */
                // 解决方案弹窗label
                modal_solution: false,
                // 对内解决方案数据绑定
                innerSolution : '',
                // 对外解决方案数据绑定
                outerSolution : '',
                // 错误码管理列表表头
                columns_solutionList: [
                    {
                        title: '错误码类型',
                        key: 'errorCodeType',
                        'min-width': 110,
                        align: 'center'
                    },
                    {
                        title: 'API分组',
                        'min-width': 140,
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.apiGroupName),
                                h('p', '(' + params.row.apiGroupCode + ')')
                            ]);
                        },
                        align: 'center'
                    },
                    {
                        title: '指定API',
                        'min-width': 150,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                // h('p', params.row.APIName),
                                h('p',  params.row.APIURI )
                            ]);
                        },

                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '错误码'),
                                h('p', '错误码描述')
                            ]);
                        },
                        'min-width': 130,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.errorCode),
                                h('p', '(' + params.row.errorCodeDes + ')')
                            ]);
                        },

                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '子错误码'),
                                h('p', '错误码描述')
                            ]);
                        },
                        'min-width': 130,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.subErrorCode),
                                h('p', '(' + params.row.subErrorCodeDes + ')')
                            ]);
                        },

                    }
                ],
                // 错误码管理列表数据
                data_solutionList: [
                    {
                        id: '1L',
                        apiGroupName: '短信用途',
                        apiGroupCode: 'message',
                        errorCodeType: 'API特有',
                        APIName: '修改结算卡信息',
                        APIURI: '/rest/v1.0/bc-app/netin/modifySettleCardInfo',
                        errorCode: '40044',
                        errorCodeDes: '业务处理失败',
                        subErrorCode: '40011111',
                        subErrorCodeDes: '查询错误',
                        createTime: '2018-02-01 10:30:00',
                        lastModifyTime: '2018-05-03 18:40:09'
                    }
                ],
                // 解决方案loading
                show_loading_solution :false
            }
        },
        methods : {
            /**
             * 表格部分方法
             */
            // 新增参数按钮调用方法
            new_errorCode () {
                this.create_orEdit = true;
                this.modal_create_errorCode = true;
            },
            handleOnstart1 () {

            },
            handleOnend1 () {

            },
            // 点击表格中编辑弹窗
            errorCode_config (id){
                this.formData_reset();
                this.create_orEdit = false;
                this.modal_create_errorCode = true;
                api.yop_errorCode_detail({id:id}).then(
                    (response)=>{
                        this.detail_format(response.data.data.result)
                    }
                )
            },
            // 详细信息处理函数
            detail_format (result) {
                this.formCustom.subErrorCode = util.empty_handler(result.subErrorCode);
                this.formCustom.subErrorCodeDes = util.empty_handler(result.subErrorMsg);
                this.formCustom.id = result.id;
                this.formCustom.version = result.version;
                this.$refs.err_solution_in.setContent(result.innerSolution);
                this.$refs.err_solution_out.setContent(result.outerSolution);
            },
            // 点击表格中删除按钮
            delete_errorCode_config (id) {
                this.$Modal.confirm({
                    title: '删除错误码',
                    content: '确定删除该错误码？',
                    'ok-text': '删除',
                    onOk: () => {
                        api.yop_errorCode_delete({id: id}).then(
                            (response) => {
                                if (response.status === 'success') {
                                    this.$ypMsg.notice_success(this,'删除成功');
                                    this.specData_get();
                                } else {
                                    this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                }
                            }
                        );
                    }
                });
            },
            // 初始化清除函数
            clear_errorCode_config () {
                this.$refs.texteditor5.setContent(' ');
                this.$refs.texteditor6.setContent(' ');
                this.input_param = ''
            },
            // 设置表格common数据
            setDataCommon (data,groupName) {
                // this.editInlineData_common = data;
                this.current_apiGroupName = groupName;
                this.current_apiGroup = data;
                this.editInlineData_common = []
                api.yop_errorCode_groupCommon_list({apiGroupCode : data}).then(
                    (response)=>{
                        if(response.data.status === 'success'){
                            let result = response.data.data.result
                            for(var i in result){
                                this.editInlineData_common.push({
                                    id : result[i].id,
                                    errorCodeType: 'API分组公共',
                                    errorCode: result[i].errorCode,
                                    subCode: result[i].subErrorCode,
                                    subMsg: result[i].subErrorMsg
                                })
                            }
                            this.show_loading_errCommon =false;
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            this.show_loading_errCommon =false;
                        }

                    }
                )
            },
            // 设置表格spec数据
            setDataSpec (data) {
                this.current_apiUri = data;
                // this.editInlineData_spec =data;
                this.specData_get();
            },
            // spec表格数据获取
            specData_get(){
                this.show_loading_errSpec =true;
                this.editInlineData_spec = []
                api.yop_errorCode_apiSpecific_list({apiUri : this.current_apiUri}).then(
                    (response)=>{
                        if(response.data.status === 'success'){
                            let result = response.data.data.result
                            for(var i in result){
                                this.editInlineData_spec.push({
                                    id : result[i].id,
                                    errorCodeType: 'API特有',
                                    errorCode: result[i].errorCode,
                                    subCode: result[i].subErrorCode,
                                    subMsg: result[i].subErrorMsg
                                })
                            }
                            this.show_loading_errSpec =false;
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            this.show_loading_errSpec =false;
                        }

                    }
                )
            },
            // 去除spec loading
            clearLoading (){
                this.show_loading_errSpec =false;
            },
            /**
             * 错误码详情弹窗部分方法
             */
            // 错误码详情弹窗确定按钮
            // 弹窗确定按钮功能函数
            ok_create_errorCode (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        if(this.create_orEdit){
                            let  param= {
                                type : 'API_SPECIFIC',
                                apiGroupCode : this.current_apiGroup,
                                apiUri : this.current_apiUri,
                                errorCode : '40044',
                                subErrorCode: this.formCustom.subErrorCode,
                                subErrorMsg: this.formCustom.subErrorCodeDes,
                                innerSolution : this.$refs.err_solution_in.getContent(),
                                outerSolution : this.$refs.err_solution_out.getContent()
                            }
                            util.paramFormat(param);
                            api.yop_errorCode_add(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        this.$ypMsg.notice_success(this,'新增成功');
                                        this.specData_get();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    }
                                }
                            )
                        }else{
                            let param = {
                                id : this.formCustom.id,
                                subErrorCode : this.formCustom.subErrorCode,
                                subErrorMsg : this.formCustom.subErrorCodeDes,
                                version : this.formCustom.version,
                                innerSolution : this.$refs.err_solution_in.getContent(),
                                outerSolution : this.$refs.err_solution_out.getContent()
                            }
                            util.paramFormat(param);
                            //可能需要对密钥处理剔除
                            api.yop_errorCode_update(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        this.$ypMsg.notice_success(this,'修改成功');
                                        this.specData_get();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                    }
                                }
                            )
                        }
                        this.modal_create_errorCode = false;
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 弹窗确定按钮功能函数
            cancel_create_errorCode () {
                this.modal_create_errorCode = false;
                this.formData_reset();
            },
            // 表单数据重置函数
            formData_reset() {
                this.formCustom.id ='';
                this.formCustom.subErrorCode ='';
                this.formCustom.subErrorCodeDes ='';
                this.$refs.err_solution_in.setContent(' ');
                this.$refs.err_solution_out.setContent(' ');
                this.formCheck_reset();
            },
            // 表单状态还原
            formCheck_reset(){
                this.$refs.formCustom.resetFields();
            },
            // 错误码解决方案
            errorCode_config_solution (id,type) {
                this.show_loading_solution = true;
                this.modal_solution = true;
                api.yop_errorCode_detail({id: id}).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat_solution(response.data.data.result,type);
                            this.innerSolution = response.data.data.result.innerSolution;
                            this.outerSolution = response.data.data.result.outerSolution;
                            this.show_loading_solution = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_solution = false;
                        }
                    }
                );
            },
            // 解决方案数据处理
            tableDataFormat_solution (items,type) {
                this.data_solutionList = [];
                let err_type = 'API特有'
                if(type === 'special'){
                    err_type = 'API特有';
                }else if(type === 'common'){
                    err_type = 'API分组公共'
                }
                this.data_solutionList.push({
                    id: items.id,
                    apiGroupName: this.current_apiGroupName,
                    apiGroupCode: util.empty_handler(items.apiGroupCode),
                    errorCodeType: err_type,
                    // APIName : '修改结算卡信息',
                    APIURI: util.empty_handler(items.apiUri),
                    errorCode: '40044',
                    errorCodeDes: '业务处理失败',
                    subErrorCode: util.empty_handler(items.subErrorCode),
                    subErrorCodeDes: util.empty_handler(items.subErrorMsg),
                    createTime: util.empty_handler(items.createdDateTime),
                    lastModifyTime: util.empty_handler(items.lastModifiedDateTime)
                });
            },
            // 解决方案关闭
            solution_close () {
                this.modal_solution = false;
            }
        }
    };
</script>

<style scoped>
    a:hover {
        font-size: 13px;
        color: black;
        /*text-decoration: underline;*/
    }
</style>
