<style lang="less">
    .width-50-perc{
        width:45%;
    }
    .width-100-perc{
        width:100%;
    }
    .margin-top-0{
        margin-top : 0!important;
    }
    /*.margin-right-9{*/
    /*margin-right: 9px!important;*/
    /*}*/
</style>
<template>
    <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="50%">
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black" v-show="current_panel ==='detail_model_show'">查看属性</span>
        </p>
        <Row>
            <Col span="24">
            <!--<Card>-->
            <Form ref="form_data_left"  :model="form_data" inline >
                <FormItem label="名称：" v-show="form_data.name" class="width-50-perc" prop="name">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>名称：</p>

                           <p v-show="current_panel ==='detail_model_show'">{{form_data.name}}</p>
                </FormItem>
                <FormItem  label="模型:" v-show="form_data.model" class="width-50-perc" prop="model">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>模型</p>

                        <p v-show="current_panel ==='detail_model_show'">{{form_data.model}}</p>
                </FormItem>
                <FormItem label="名称：" v-show="form_data.$ref" class="width-50-perc" prop="$ref">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>model：</p>
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.$ref}}</p>
                </FormItem>
                <FormItem class="width-50-perc" v-show="form_data.format" label="类型-格式：">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>类型-格式：</p>

                        <p v-show="current_panel ==='detail_model_show'">{{form_data.type[0]}}-{{form_data.type[1]}}</p>
                </FormItem>
                <FormItem class="width-50-perc" v-show="!form_data.format" label="类型-格式：" prop="type">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>类型-格式：</p>

                        <p v-show="current_panel ==='detail_model_show'">{{form_data.type[0]}}-{{form_data.type[0]}}</p>
                </FormItem>
                <FormItem class="width-50-perc" v-show="form_data.sensitive" label="是否敏感：" prop="sensitive">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>是否敏感：</p>

                    <p v-show="current_panel ==='detail_model_show'">{{form_data.sensitive ?"是":"否"}}</p>
                </FormItem>
                <FormItem class="width-50-perc" v-show="form_data.order" label="业务订单号：" prop="order">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>业务订单号：</p>

                    <p v-show="current_panel ==='detail_model_show'">{{form_data.order?"是":"否"}}</p>
                </FormItem>
                <FormItem class="width-50-perc" v-show="form_data.required" label="是否必填：" prop="required">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>是否必填：</p>

                    <p v-show="current_panel ==='detail_model_show'">{{form_data.required}}</p>
                </FormItem>

                <FormItem  class="width-50-perc" v-show="form_data.description" label="描述：" prop="description">
                    <p v-show="current_panel ==='detail_model_show'">{{form_data.description ? form_data.description.replace(/<[^<>]+>/g, '') : ''}}</p>
                    <!-- <pre>{{form_data.description}}</pre> -->
                </FormItem>
                <FormItem  class="width-50-perc" v-show="form_data.default" label="默认值：" prop="default" >
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.default}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="form_data.maximum" label="最大值：" prop="maximum" >
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.maximum}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="form_data.exclusiveMaximum" label="包含最大值：" prop="exclusiveMaximum" >
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.exclusiveMaximum?"否":"是"}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="form_data.minimum" label="最小值：" prop="minimum" >
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.minimum}}</p>
                </FormItem>

                <FormItem  class="width-50-perc" v-show="form_data.exclusiveMinimum" label="包含最小值：" prop="exclusiveMinimum" >
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.exclusiveMinimum?"否":"是"}}</p>
                </FormItem>
                
                <FormItem  class="width-50-perc" v-show="form_data.pattern" label="正则表达式：" prop="pattern" >
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.pattern}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="form_data.maxLength" label="最大长度：" prop="maxLength" >
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.maxLength}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="form_data.minLength" label="最小长度：" prop="minLength" >
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.minLength}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="form_data.maxItems" label="最大数目：" prop="maxItems" >
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.maxItems}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="form_data.minItems" label="最小数目：" prop="minItems" >
                           <p v-show="current_panel ==='detail_model_show'">{{form_data.minItems}}</p>
                </FormItem>
                <!-- <FormItem  class="width-50-perc">
                    <Row>
                                <Table id='table_2' border ref="selection_2" :columns="response_example_List_display" :data="example"></Table>
                    </Row>
                </FormItem> -->

            </Form>
            <!--</Card>-->
            </Col>
            <Col span="24" v-show="this.showAdditional">
            <Form ref="form_data_bottom" :model="additionalProperties_map" inline >
                <p v-show="this.additionalPropertiesType == 'map'">值：</p>
                <p v-show="this.additionalPropertiesType == 'array'">数组元素：</p>
                <FormItem label="名称：" v-show="additionalProperties_map.$ref" class="width-50-perc" prop="$ref">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>model：</p>
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.$ref}}</p>
                </FormItem>
                <FormItem  label="模型:" v-show="additionalProperties_map.model" class="width-50-perc" prop="model">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>模型：</p>

                        <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.model}}</p>
                </FormItem>
                <FormItem class="width-50-perc" v-show="additionalProperties_map.format" label="类型-格式：">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>类型-格式：</p>

                        <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.type}}-{{additionalProperties_map.format}}</p>
                </FormItem>
                <FormItem class="width-50-perc" v-show="!additionalProperties_map.format && !additionalProperties_map.$ref" label="类型-格式：" prop="type">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>类型-格式：</p>

                        <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.type}}-{{additionalProperties_map.type}}</p>
                </FormItem>
                <FormItem class="width-50-perc" v-show="additionalProperties_map.required" label="是否必填：" prop="required">
                    <p class="margin-right-9" slot="label"><label_icon></label_icon>是否必填：</p>

                    <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.required}}</p>
                </FormItem>

                <FormItem  class="width-50-perc" v-show="additionalProperties_map.description" label="描述：" prop="description">
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.description}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="additionalProperties_map.default" label="默认值：" prop="default" >
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.default}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="additionalProperties_map.maximum" label="最大值：" prop="maximum" >
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.maximum}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="additionalProperties_map.exclusiveMaximum" label="包含最大值：" prop="exclusiveMaximum" >
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.exclusiveMaximum?"否":"是"}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="additionalProperties_map.minimum" label="最小值：" prop="minimum" >
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.minimum}}</p>
                </FormItem>

                <FormItem  class="width-50-perc" v-show="additionalProperties_map.exclusiveMinimum" label="包含最小值：" prop="exclusiveMinimum" >
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.exclusiveMinimum?"否":"是"}}</p>
                </FormItem>
                
                <FormItem  class="width-50-perc" v-show="additionalProperties_map.pattern" label="正则表达式：" prop="pattern" >
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.pattern}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="additionalProperties_map.maxLength" label="最大长度：" prop="maxLength" >
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.maxLength}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="additionalProperties_map.minLength" label="最小长度：" prop="minLength" >
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.minLength}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="additionalProperties_map.maxItems" label="最大数目：" prop="maxItems" >
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.maxItems}}</p>
                </FormItem>
                <FormItem  class="width-50-perc" v-show="additionalProperties_map.minItems" label="最小数目：" prop="minItems" >
                           <p v-show="current_panel ==='detail_model_show'">{{additionalProperties_map.minItems}}</p>
                </FormItem>
            </Form>
            <!--<Card>-->
            </Col>
            <Form inline>
            <FormItem  class="width-100-perc">
                    <Row>
                            <Table id='table_2' border ref="selection_2" :columns="response_example_List_display" :data="example"></Table>
                    </Row>
                </FormItem>
            </Form>
            <Col v-if="this.form_data_visual.additionalProperties||this.form_data_visual.items" style="border-bottom: 1px solid #eee;margin-bottom: 20px;" span="24">
            </Col>
            
        </Row>
        
        <div slot="footer">
            <!-- <Button type="primary" @click="closeThis">确定</Button> -->
            <Button type="ghost" @click="cancel">关闭</Button>
        </div>
    </Modal>
</template>

<script>
    import textEditorSize from '../../my-components/text-editor/text-editor-size';
    import commonSelect from '../../common-components/select-components/selectCommon';
    import label_icon from '../../common-components/icon-components/star_red'
    import subcontent from './param_main_content_model'
    // import subcontentMap from './param_main_content_model_map'
    import api from '../../../api/api'
    import { setTimeout } from 'timers';

    export default {
        name: 'modal_param_content_model',
        components: {
            textEditorSize,
            commonSelect,
            subcontent,
            // subcontentMap,
            label_icon
        },
        props:{
            data_spi_model_code:String
        },
        data () {
            return {
                data_api_model:"",
                data_api_model_list:[],
                form_data_left:{
                    param: '', // 参数
                    name: '', // 名称
                    type: [], // 类型
                    sample: '', // 示例值
                    description: '', // 描述
                    required: false, // 是否必填
                },
                // 表格内容数据绑定
                example:[],
                form_data: {
                    $ref:"",
                    // old
                    // param: '', // 参数
                    // name: '', // 名称
                    // type: [], // 类型
                    // sample: '', // 示例值
                    // description: '', // 描述
                    // required: false, // 是否必填

                    // new
                    // param: '', // 参数
                    title: '', // 名称
                    type: [], // 类型
                    sample: '', // 示例值
                    description: '', // 描述
                    required: false, // 是否必填
                    // new
                    additionalProperties:{},
                    items:{},
                    internal : false, // 参数
                    default : '', // 默认值
                    sensitive : false, // 名称
                    param_index : '', // 类型
                    param_name : '', // 示例值
                    maximum : '', // 最大值
                    exclusiveMaximum : true, // 包含最大值
                    minimum : '', // 最小值
                    exclusiveMinimum : true,// 包含最小值，
                    pattern : '', // 正则表达式
                    maxLength : '', // 最大长度
                    minLength : '', // 最小长度
                    maxItems : '', //最大数目
                    minItems :'', //最小数目
                    orderNo : '', //业务订单号

                    model : '',
                    constrains:""
                },
                // 枚举数据绑定
                enum_data : [{
                    value : '',
                    index : 0,
                    status : 1
                }],
response_example_List_display:[
        {
            title: '名称',
            key: 'name',
            align:"center"
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
            title: '描述',
            align:"center",
            key: 'description'
        },
        {
            title: '值',
            align:"center",
            key: 'value'
        },
    ],
    data_request_example_List:[],
                // 表格显示绑定
                form_data_visual: {
                    extremum : false,
                    enums : false,
                    length : false,
                    pattern : false,
                    itemSize : false,
                    model : false,
                    additionalProperties: false,
                    items : false,
                    order: false,
                    "exclusiveMaximum":false,
                    "minimum":false,
                    "exclusiveMinimum":false,
                    maxItems:false,
                    minItems:false,

                },
                current_panel: 'create_request_json',
                // 窗口是否展示
                modal_show : false,
                // 示例值绑定
                data_select_sample : '',
                // 常用正则表达式数据绑定
                data_select_pattern : '',
                
                enum_index : 0,
                // 是否展示更多属性 false 为隐藏 true 为显示
                more_attr : false,

                type_data:[],
                type_data_cut : [],
                type_data_map:[],
                sample_data:[
                    {
                        value : 'integer',
                        desc : 'integer',
                        formats :[
                            {
                                value : 'int32',
                                desc : 'int32',
                                defaulted : true,
                                constrains : [
                                    "extremum"
                                ]
                            },
                            {
                                value : 'int64',
                                desc : 'int64',
                                defaulted : true,
                                constrains : [
                                    "extremum"
                                ]
                            }
                        ]
                    },
                    {
                        value : 'number',
                        desc : 'number',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                                constrains : [
                                    "extremum"
                                ]
                            },
                            {
                                value : 'float',
                                desc : 'float',
                                defaulted : true,
                                constrains : [
                                    "extremum"
                                ]
                            },
                            {
                                value : 'double',
                                desc : 'double',
                                defaulted : true,
                                constrains : [
                                    "extremum"
                                ]
                            }
                        ]
                    },
                    {
                        value : 'boolean',
                        desc : 'boolean',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                                constrains : [
                                    
                                ]
                            }
                        ]
                    },
                    {
                        value : 'string',
                        desc : 'string',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                                constrains : [
                                    'length',
                                    'pattern'
                                ]
                            },
                            {
                                value : 'byte',
                                desc : 'byte',
                                defaulted : true,
                            },
                            {
                                value : 'binary',
                                desc : 'binary',
                                defaulted : true,
                            },
                            {
                                value : 'date',
                                desc : 'date',
                                defaulted : true,
                            },
                            {
                                value : 'date-time',
                                desc : 'date-time',
                                defaulted : true,
                            },
                            {
                                value : 'password',
                                desc : 'password',
                                defaulted : true,
                            },
                            {
                                value : 'email',
                                desc : 'email',
                                defaulted : true,
                            },
                            {
                                value : 'mobile',
                                desc : 'mobile',
                                defaulted : true,
                            },
                            {
                                value : 'idcard',
                                desc : 'idcard',
                                defaulted : true,
                            },
                            {
                                value : 'bankcard',
                                desc : 'bankcard',
                                defaulted : true,
                            },
                            {
                                value : 'cvv',
                                desc : 'cvv',
                                defaulted : true,
                            },
                            {
                                value : 'uuid',
                                desc : 'uuid',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'array',
                        desc : 'array',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                                constrains : [
                                    'items',
                                    "itemSize"
                                ]
                            }
                        ]
                    },
                    {
                        value : 'object',
                        desc : 'object',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                            },
                            {
                                value : 'map',
                                desc : 'map',
                                defaulted : true,
                                constrains : [
                                    'additionalProperties',
                                ]
                            }
                        ]
                    },
                ],
                // 当前编辑行数
                current_line_index : 0,
                apiGroupCode:"",
                search_model_apiGroup:"",
                subContent:{},
                model_current:"",
                additionalProperties_map:{
                    $ref:"",
                    format:"",
                    type:"",
                    description: '', // 描述
                    required: false, // 是否必填
                    default : '', // 默认值
                    maximum : '', // 最大值
                    exclusiveMaximum : true, // 包含最大值
                    minimum : '', // 最小值
                    exclusiveMinimum : true,// 包含最小值，
                    pattern : '', // 正则表达式
                    maxLength : '', // 最大长度
                    minLength : '', // 最小长度
                    maxItems : '', //最大数目
                    minItems :'', //最小数目
                    model : '',
                }
                ,additionalPropertiesType:""
                ,showAdditional:false,
            };
        },
        methods: {
           
            // 当前页面设置
            current_panel_set (val) {
                this.current_panel= val;
            },
          
            // 页面初始化
            init () {
                this.cascader_format();
            },
            
            closeThis(){
                this.modal_show = false;

            },
            // 表单取消
            cancel () {
                this.modal_show = false;
            },
            // 窗口显示
            show () {
                this.modal_show = true;
            },
           
          
            // 级联 参数类型 数据获取及处理
            cascader_format () {

            },
          
            // 设置当前页面数据
            form_data_set (data,sensitiveVariables,bizOrderVariable) {
                this.form_data = JSON.parse(JSON.stringify(data));
                if(this.form_data.description) {
                    this.form_data.description = this.form_data.description.replace(/&nbsp;/g, " ").replace(/&lt;/g, "<").replace(/&gt;/g, ">")
                }
                var schema = ""
                if(this.form_data.schema){
                    schema = JSON.parse(this.form_data.schema)
                    this.form_data.type=[schema.type,schema.format?schema.format:schema.type]
                    this.form_data.format=schema.format
                }
                this.example=data.examples
                if(sensitiveVariables && sensitiveVariables.variableName){
                    for(var i = 0 ; i < sensitiveVariables.length ; i ++){
                        var sensitiveVariablesI =  sensitiveVariables[i]
                        if(sensitiveVariablesI["variableName"] == data.name){
                            this.form_data.sensitive = true
                        }
                    }
                }
                if(bizOrderVariable && bizOrderVariable.variableName){
                    if(bizOrderVariable["variableName"] == data.name){
                            this.form_data.order = true
                    }
                }
                
                
                if(data.$ref){
                    this.form_data.$ref = data.$ref.split("/").pop();
                }
                if(data.additionalProperties){
                    this.additionalProperties_map = data.additionalProperties
                    if(this.additionalProperties_map.description) {
                        this.additionalProperties_map.description = this.additionalProperties_map.description.replace(/&nbsp;/g, " ").replace(/&lt;/g, "<").replace(/&gt;/g, ">")
                    }
                    if(data.additionalProperties.$ref){
                        this.additionalProperties_map.$ref = data.additionalProperties.$ref.split("/").pop();
                    }
                    this.additionalPropertiesType = "map"
                    this.showAdditional = true
                }else if(data.items){
                    this.additionalProperties_map = data.items
                    if(data.items.$ref){
                        this.additionalProperties_map.$ref = data.items.$ref.split("/").pop();
                    }
                    this.additionalPropertiesType = "array"
                    this.showAdditional = true
                }else{
                    this.showAdditional = false
                }
                // this.additionalProperties_map = data.additionalProperties
            },
            // 设置当前行数
            current_index_set (index) {
                this.current_line_index = index
            }
        },
        mounted () {
            this.init();
        },
    };
</script>

<style scoped>

</style>
