<template>
  <Table
    border
    :columns="columns"
    :data="list">
  </Table>
</template>

<script>
export default {
  props: ['type'],
  data () {
    return {
      list: [],
      columns: [
        {
          title: '名称',
          key: 'name',
          align: 'center',
          render: (h, params) => {
            if (params.row.name) {
              return h('div', params.row.name);
            } else {
              return h('div', '--');
            }
          }
        },
        {
          title: '类型-格式',
          key: 'type',
          align: 'center',
          render: (h, params) => {
            if (typeof params.row.type == 'object') {
              return h('div', [h('p', params.row.type[0]),
                h('p', params.row.type[1])]);
            } else if (params.row.format) {
              return h('div', [h('p', params.row.type),
                h('p', params.row.format)]);
            } else {
              return h('div', [h('p', params.row.type),
                h('p', params.row.type)]);
            }
          }

        },
        {
          title: '必需',
          align: 'center',
          key: 'must',
          render: (h, params) => {
            return params.row.required ? h('div', '是') : h('div', '否');
          }
        },
        {
          title: '废弃',
          align: 'center',
          key: 'deprecated',
          render: (h, params) => {
            return params.row.deprecated ? h('div', '是') : h('div', '否');
          }
        },
        {
          title: '敏感字段',
          align: 'center',
          key: 'sensitive',
          render: (h, params) => {
            return params.row.sensitive ? h('div', '是') : h('div', '否');
          }
        },
        {
          type: 'html',
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '约束',
          align: 'center',
          render: (h, params) => {
            var minimum, maximum, maxLength, minLength, notMin, notMax, maxItems, minItems, pattern
            notMin = params.row.exclusiveMinimum ? '不包含' : '包含'
            notMax = params.row.exclusiveMaximum ? '不包含' : '包含'
            maximum = params.row.maximum ? `最大值：${params.row.maximum}(${notMax})` : ''
            minimum = params.row.minimum ? `最小值：${params.row.minimum}(${notMin})` : ''
            maxLength = params.row.maxLength ? `最大长度：${params.row.maxLength}` : ''
            minLength = params.row.minLength ? `最小长度：${params.row.minLength}` : ''
            maxItems = params.row.maxItems ? `最大数量：${params.row.maxItems}` : ''
            minItems = params.row.minItems ? `最小数量：${params.row.minItems}` : ''
            pattern = params.row.pattern ? `正则：${params.row.pattern}` : ''
            return h('div', [
              h('p', minimum),
              h('p', maximum),
              h('p', maxLength),
              h('p', minLength),
              h('p', maxItems),
              h('p', minItems),
              h('p', pattern)
            ]);
          }
        }
      ]
    }
  },
  methods: {
    setList () {
      const schema = window.sessionStorage.getItem(this.type)
      if (schema) {
        const { properties } = JSON.parse(schema)
        this.list = Object.keys(properties).map(key => {
          return {
            ...properties[key],
            name: key
          }
        })
      }
    }
  }
}
</script>

<style>

</style>
