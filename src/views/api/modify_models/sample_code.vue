<style >
 /*@import './style/codemirror.css';*/
 /*@import './style/monokai.css';*/
 /*@import './style/show-hint.css';*/
</style>
<template>
    <Row>
        <Card dis-hover>
            <Row>
                <Col span="24" class="yop-template-in-title margin-bottom-10">
                <Col span="23" class="yop-title-red">
                *API创建成功，系统默认生成示例代码，请服务提供方自定义示例代码，如服务方自定义示例代码，则按照服务提供方示例代码展示给商户；否则以系统自动生成示例代码展示
                </Col>
                <Col span="1" >
                <!--<Button size="small" type="primary" @click="save">保存</Button>-->
                </Col>
                </Col>

                <Col span="24">
                <Tabs :value="codeArray[0].lang">
                    <TabPane v-for="(item,index) of codeArray" :key="index" :label="item.lang" :name="item.lang" >
                        <codeEditor ref='item.lang' :code="item.code" :cmOptions="item.option" :index="index" @codeModify="codeModify"></codeEditor>
                    </TabPane>
                </Tabs>
                </Col>
            </Row>
            <loading :show="show_loading_sampleCode"></loading>
        </Card>
    </Row>
</template>

<script>
    import codeEditor from '../../common-components/editer-components/codeEditor'
    import api from '../../../api/api'
    import loading from '../../my-components/loading/loading'
    import  md5 from '../../../libs/md5'
    export default {
        name: 'sample_code',
        components :{
            codeEditor,
            loading
        },
        data () {
            return{
                currentUri : '',
                cmOptions: {
                    tabSize: 4,
                    styleActiveLine: false,
                    lineNumbers: true,
                    styleSelectedText: false,
                    line: true,
                    highlightSelectionMatches: { showToken: /\w/, annotateScrollbar: true },
                    mode: 'text/x-java',
                    matchBrackets: true,
                    showCursorWhenSelecting: true,
                    theme: "monokai",
                    extraKeys: { "Ctrl": "autocomplete" }
                },
                codeArray : [{
                    lang: 'java',
                    option: {
                        tabSize: 4,
                        styleActiveLine: false,
                        lineNumbers: true,
                        styleSelectedText: false,
                        line: true,
                        highlightSelectionMatches: {showToken: /\w/, annotateScrollbar: true},
                        mode: 'text/x-java',
                        matchBrackets: true,
                        showCursorWhenSelecting: true,
                        theme: "monokai",
                        extraKeys: {"Ctrl": "autocomplete"}
                    },
                    code: '',
                }

//                     {
//                         lang: 'java',
//                         option: {
//                             tabSize: 4,
//                             styleActiveLine: false,
//                             lineNumbers: true,
//                             styleSelectedText: false,
//                             line: true,
//                             highlightSelectionMatches: { showToken: /\w/, annotateScrollbar: true },
//                             mode: 'text/x-java',
//                             matchBrackets: true,
//                             showCursorWhenSelecting: true,
//                             theme: "monokai",
//                             extraKeys: { "Ctrl": "autocomplete" }
//                         },
//                         code: `@Override
//     protected YopCacheLoader<String, List<CacheItem>> getCacheLoader() {
//         return new YopCacheLoader<String, List<CacheItem>>() {
//             @Override
//             public List<CacheItem> doLoad(String key) throws Exception {
//                 String[] keys = key.split("#");
//
//                 List<CacheItem> publicKeys = Lists.newArrayList();
//                 List<ISVCertDTO> certDTOS = isvCertFacade.queryAvailableKey(keys[0], CertTypeEnum.parse(keys[1]));
//                 if (certDTOS == null || certDTOS.isEmpty()) {
//                     return publicKeys;
//                 }
//                 Date now = new Date();
//                 for (ISVCertDTO certDTO : certDTOS) {
//                     if (certDTO.getEffectiveDate() != null && certDTO.getEffectiveDate().after(now)) {
//                         continue;
//                     }
//                     if (certDTO.getExpiredDate() != null && certDTO.getExpiredDate().before(now)) {
//                         continue;
//                     }
//                     CacheItem cacheItem = new CacheItem();
//                     cacheItem.setCertSource(certDTO.getCertSource());
//                     cacheItem.setPublicKey(RSAKeyUtils.string2PublicKey(certDTO.getKey()));
//                     publicKeys.add(cacheItem);
//                 }
//                 return publicKeys;
//             }
//
//             @Override
//             protected String getCacheName() {
//                 return "isv_rsa_cert_cache";
//             }
//         };
//     }`
//                     },
//                     {
//                         lang: 'php',
//                         option: {
//                             tabSize: 4,
//                             styleActiveLine: false,
//                             lineNumbers: true,
//                             styleSelectedText: false,
//                             line: true,
//                             highlightSelectionMatches: { showToken: /\w/, annotateScrollbar: true },
//                             mode: 'text/x-php',
//                             matchBrackets: true,
//                             showCursorWhenSelecting: true,
//                             theme: "monokai",
//                             extraKeys: { "Ctrl": "autocomplete" }
//                         },
//                         code:`<?php
//   $a = array('a' => 1, 'b' => 2, 3 => 'c');
//
//   echo "$a[a] a[3] /* } comment */ {$a[b]} $a[a]";
//
//   function hello($who) {
//     return "Hello $who!";
//   }
// ?>
// <p>The program says <?= hello("World") ?>.</p>`
//                     },
//                     {
//                         lang: 'python',
//                         option: {
//                             tabSize: 4,
//                             styleActiveLine: false,
//                             lineNumbers: true,
//                             styleSelectedText: false,
//                             line: true,
//                             highlightSelectionMatches: { showToken: /\w/, annotateScrollbar: true },
//                             mode: 'text/x-python',
//                             matchBrackets: true,
//                             showCursorWhenSelecting: true,
//                             theme: "monokai",
//                             extraKeys: { "Ctrl": "autocomplete" }
//                         },
//                         code:`# Python 2 Keywords (otherwise Identifiers)
// exec print
//
// # Python 3 Keywords (otherwise Identifiers)
// nonlocal
//
// # Types
// bool classmethod complex dict enumerate float frozenset int list object
// property reversed set slice staticmethod str super tuple type
//
// # Python 2 Types (otherwise Identifiers)
// basestring buffer file long unicode xrange
//
// # Python 3 Types (otherwise Identifiers)
// bytearray bytes filter map memoryview open range zip
//
// # Some Example code
// import os
// from package import ParentClass
//
// @nonsenseDecorator
// def doesNothing():
//     pass
//
// class ExampleClass(ParentClass):
//     @staticmethod
//     def example(inputStr):
//         a = list(inputStr)
//         a.reverse()
//         return ''.join(a)
//
//     def __init__(self, mixin = 'Hello'):
//         self.mixin = mixin`
//                     },
//                     {
//                         lang: 'csharp',
//                         option: {
//                             tabSize: 4,
//                             styleActiveLine: false,
//                             lineNumbers: true,
//                             styleSelectedText: false,
//                             line: true,
//                             highlightSelectionMatches: { showToken: /\w/, annotateScrollbar: true },
//                             mode: 'text/x-csharp',
//                             matchBrackets: true,
//                             showCursorWhenSelecting: true,
//                             theme: "monokai",
//                             extraKeys: { "Ctrl": "autocomplete" }
//                         },
//                         code:`
// //ensure that we are not exporting
//  //deleted products
//  if (product.IsDeleted && !product.IsExported)
//  {
//        ExportProducts = false;
//  }
//
//  // This is a for loop that prints the 1 million times
//  for (int i = 0; i < 1000000; i++)
//  {
//        Console.WriteLine(i);
//  }`
//                     }
                ],
                show_loading_sampleCode : false
            }
        },
        methods : {
            sampleCode_get (uri) {
                this.currentUri = uri;
                if(uri){
                    let param ={
                        apiUri : uri
                    }
                    this.sampleCode_handler(param)
                }
            },
            sampleCode_handler (param) {
                api.yop_sampleCode_search(param).then(
                    (response) => {
                        let status = response.data.status
                        let data = response.data.data.result;
                        this.codeArray = [];
                        if(status === 'success'){
                            if(data){
                                data.forEach(
                                    (item) =>{
                                        this.codeArray.push({
                                            lang: item.lang,
                                            version : item.version,
                                            option: {
                                                tabSize: 4,
                                                styleActiveLine: false,
                                                lineNumbers: true,
                                                styleSelectedText: false,
                                                line: true,
                                                highlightSelectionMatches: { showToken: /\w/, annotateScrollbar: true },
                                                mode: item.langCode,
                                                matchBrackets: true,
                                                showCursorWhenSelecting: true,
                                                theme: "monokai",
                                                extraKeys: { "Ctrl": "autocomplete" }
                                            },
                                            code: item.sampleCode,
                                            codeMd5: item.md5
                                        })
                                    }
                                )
                                this.show_loading_sampleCode = false;
                            }
                        }else{
                            this.$Modal.confirm({
                                title: '错误',
                                content: '示例代码获取失败，点击确定按钮重新获取',
                                onOk: () => {
                                    this.sampleCode_handler(param);
                                }
                            });
                        }

                    }
                )
            },
            init(){
                this.show_loading_sampleCode = true;
            },
            // 保存示例代码
            save () {
                let param = {
                    apiUri : this.currentUri,
                    updateCodes:this.codes_handler()
                }
                api.yop_sampleCode_update(param).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'示例代码提交成功');
                        }else{
                            this.$ypMsg.notice_error(this,'示例代码提交失败',response.message,response.solution);
                        }
                    }

                )
                console.log(this.codeArray);
            },
            // 代码处理函数
            codes_handler () {
                let currentCodes = []
               if(this.codeArray){
                   this.codeArray.forEach(
                       (item) => {
                           if(item.codeMd5 !== this.md5_handler(item.code)){
                               console.log(item.codeMd5,this.md5_handler(item.code))
                               currentCodes.push(
                                   {
                                       lang : item.lang,
                                       customCode : item.code,
                                       version : item.version
                                   }
                               )
                               console.log(currentCodes);
                           }
                       }
                   )
               }
               return currentCodes;
            },
            onCmReady(cm) {
                console.log('the editor is readied!', cm)
            },
            onCmFocus(cm) {
                console.log('the editor is focus!', cm)
            },
            codeModify(val) {
                // console.log(val)
                this.codeArray[val.index].code = val.code;
                // this.code = newCode
            },
            // md5 生成
            md5_handler (code) {
                return md5(code)
            }
        },
        mounted () {
            this.init();
        }
    };
</script>

<style scoped>
    .CodeMirror {
        height: 700px;
    }
</style>
