<style lang="less">
    // @import '../../../styles/common.less';
    // @import '../api_Mangement_Open.less';
    // @import '../../regression-test/regression.less';
    .margin-top-12 {
        margin-top:12px;
    }
    .secondStyle {
        display:inline-block;
        margin-top:6px;
    }
    .ivu-input-group {
        top:0!important;
    }
    .ivu-table-body table .ivu-table-cell{
        padding: 20px;
        overflow: visible;
    }
    .ivu-table-body table .ivu-table-row {
        overflow: visible;

    }
    .ivu-table {
        overflow: visible;
    }
    .ivu-input-group-with-prepend .ivu-input-group-prepend {
        background: #fff!important;
        padding:2px!important;
    }
    .ivu-input-group-with-prepend .ivu-input{
        padding:1px!important;
    }
</style>
<template>
    <Row>
        <Card dis-hover>
            <Form ref="form_data_service"  :model="form_data_service" :rules="validate_data_service" :label-width="150" inline >

                <Row>
                    <FormItem class="width-100-perc"  label="后端服务类型：" prop="service_type">
                        <Select id="selectEndType" v-show="this.current_status !== 'description'" size="small" v-model="form_data_service.service_type" style="width:380px" placeholder="请输入" @on-change="changeService_type">
                            <Option v-for="(item,index) in service_type_list" :value="item.value" :key="index">{{ item.label }}</Option>
                        </Select>
                        <p  v-if="this.current_status == 'description'">{{form_data_service.service_type}}</p>

                    </FormItem>
                </Row>
                <Row>
                    <FormItem class="width-100-perc"  label="后端服务：" prop="service" >
                        <Select id="selectEndService" v-show="this.current_status !== 'description'"  size="small" v-model="form_data_service.service" style="width:380px" placeholder="请输入" @on-change="getServiceCon">
                            <Option v-for="(item,index) in service_list" :value="item.value" :key="index">{{ item.label }}</Option>
                        </Select>
                        <p  v-if="this.current_status == 'description'">{{form_data_service.service}}</p>

                    </FormItem>
                </Row>
                <Row v-show="showService">
                    <FormItem  class="width-100-perc"  label="后端服务基础path："><!-- prop="service_Url" :rules="{required:true,message:'后端服务基础path不能为空'}"-->
                        <Input  v-if="this.current_status !== 'description'" id="inputEndServicePath" size="small" style="width: 380px;" v-model="form_data_service.basePath" placeholder="请输入" disabled></Input>
                        <p  v-if="this.current_status == 'description'">{{form_data_service.basePath}}</p>
                    
                    </FormItem>
                </Row>
                <Row  v-show="showService">
                    <FormItem  class="width-100-perc"  label="后端请求path：" prop="query_Url" >
                        <Input v-if="this.current_status !== 'description'" id="inputEndRequestPath2" size="small" style="width: 380px;" v-model="form_data_service.query_Url" placeholder="请输入" @on-focus="onFocus_url" @on-blur="onblur_url"></Input>
                        <p id="pEndRequestPath" v-if="this.current_status == 'description'">{{form_data_service.query_Url}}</p>
                    
                    </FormItem>
                </Row>
                <Row  v-show="showService">
                    <FormItem  class="width-100-perc"  label="后端请求方法：" prop="method_Url">
                        <Input v-if="this.current_status !== 'description'" id="method_Url" size="small" style="width: 380px;" v-model="form_data_service.method" placeholder="请输入" @on-focus="onFocus_url" @on-blur="onblur_url" disabled></Input>
                        <p  v-if="this.current_status == 'description'">{{form_data_service.method}}</p>
                    </FormItem>
                </Row>
                <Row  v-show="showService">
                    <FormItem  class="width-50-perc"  label="连接超时：" prop="link_timeout">
                        <Input v-if="this.current_status !== 'description'"  id="inputConnectionTimeout" size="small" style="width:90px;" v-model="form_data_service.link_timeout" placeholder="请输入" @on-focus="onFocus_url" @on-blur="onblur_url"></Input><span  v-if="this.current_status !== 'description'" class="secondStyle">ms</span>
                        <p  v-if="this.current_status == 'description'">{{form_data_service.link_timeout}}ms</p>
                    
                    </FormItem>
                </Row>
                <Row  v-show="showService">
                    <FormItem  class="width-50-perc"  label="读取超时：" prop="read_timeout">
                        <Input v-if="this.current_status !== 'description'"   id="inputReadTimeout" size="small" style="width:90px;" v-model="form_data_service.read_timeout" placeholder="请输入" @on-focus="onFocus_url" @on-blur="onblur_url"></Input><span  v-if="this.current_status !== 'description'"  class="secondStyle">ms</span>
                        <p  v-if="this.current_status == 'description'">{{form_data_service.read_timeout}}ms</p>
                    
                    </FormItem>
                </Row>
                <Row v-show="isMapping">
                    <p style="font-size: 12px; padding-left: 31px;line-height:30px;">后端服务参数映射：</p>
                    <FormItem class="width-100-perc">

                        <Table id='table_1' border ref="selection" :columns="this.current_status !== 'description'? columns_service_params_mapping :columns_service_params_mapping_desc" :data="data_service_params_mapping_list" @on-selection-change="handleselection_mapping"></Table>
                        <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                            <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh_mapping"></Page>
                        </Tooltip> -->
                        <Button id='btn_spiM_1' class="margin-top-12" type="primary" @click="add_mapping" v-if="this.current_status !== 'description'">新增一条</Button>
                    </FormItem>
                </Row>
                <p style="font-size: 12px;padding-left: 75px;line-height:30px;">常量参数：</p>
                <Row>
                    <FormItem class="width-100-perc">
                        <Table id='table_1' border ref="selection" :columns="this.current_status !== 'description'?columns_Constant:columns_Constant_desc" :data="data_ConstantList" @on-selection-change="handleselection_Constant"></Table>
                        <Button id='btnAddConstantParameters' class="margin-top-12" type="primary" @click="add_constantList" v-if="this.current_status !== 'description'">新增一条</Button>
                        <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                            <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh_Constant"></Page>
                        </Tooltip> -->
                    </FormItem>
                </Row>

                <p style="font-size: 12px;padding-left: 75px;line-height:30px;">系统参数：</p>

                <Row>
                    <FormItem class="width-100-perc">
                        <Table id='table_1' border ref="selection" :columns="this.current_status !== 'description'?columns_System:columns_System_desc" :data="data_SystemList" @on-selection-change="handleselection_System"></Table>
                        <Button id='btnAddSystemParameters' class="margin-top-12" type="primary" @click="add_systemList" v-if="this.current_status !== 'description'">新增一条</Button>
                        <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                            <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh_System"></Page>
                        </Tooltip> -->
                    </FormItem>
                </Row>
            </Form>
        </Card>
    </Row>
</template>

<script>
    import api from '../../../api/api.js';
    import utils from '../../../libs/util.js'
import { setTimeout } from 'timers';
    export default {
        name: 'point_service',
        data () {
            
                const validate_const_backend_name = (rule, value, callback) => {
                        var reg = /^[a-z][a-zA-Z0-9]*$/

                        // var reg = /^[a-z]+((\d)|([A-Z0-9][a-z0-9]+))*([A-Z])?$/
                    if(!value){
                        callback(new Error('不能为空'));
                    }else
                    if(!reg.test(value)){
                        callback(new Error('允许小写驼峰格式'));
                    }else{
                        console.log(this.constant_backend_list);
                        console.log(this.data_ConstantList,value);
                        var index =  0
                        for(var j in this.constant_backend_list){
                            if(value == this.constant_backend_list[j].backend_name || value == this.constant_backend_list[j].backendName){
                                index++
                                if(index>=2){
                                    callback(new Error('参数名不允许重复'));
                                }
                            }
                        }
                        callback();
                    }
                };
                const validate_interface = (rule, value, callback) => {
                    if(!value){
                        callback(new Error('不能为空'));
                    }else{
                        callback();
                    }
                }
                const validate_query_url = (rule, value, callback) => {
                        var reg = /^[-a-zA-Z0-9()@:%_+.~#?&/=]*$/
                        if(!value){
                        callback(new Error('后端请求路径不能为空'));
                        }else
                        if(!reg.test(value)){
                            callback(new Error('请符合以下规则：[-a-zA-Z0-9()@:%_+.~#?&/=]'));
                        }else{
                            callback();
                        }
                    }
                const validate_timeout_1 = (rule, value, callback) => {
                    if(!value){
                        callback(new Error('连接超时不能为空'));
                    }else if(isNaN(value*1)){
                        callback(new Error('请输入数字'));
                    }else if(value*1 > 60000){
                        callback(new Error('连接超时不能超过60s'));
                    }else if(!(/^[1-9]\d*$/.test(value*1))){
                        callback(new Error('请输入正整数'));
                    }else {
                        callback();
                    }
                };
                const validate_timeout_2 = (rule, value, callback) => {
                    if(!value){
                        callback(new Error('读取超时不能为空'));
                    }else if(isNaN(value*1)){
                        callback(new Error('请输入数字'));
                    }else if(value*1 > 60000){
                        callback(new Error('读取超时不能超过60s'));
                    }else if(!(/^[1-9]\d*$/.test(value*1))){
                        callback(new Error('请输入正整数'));
                    }else {
                        callback();
                    }
                };
                
            return {
                backendNameList:[],
                // 选择后端服务
                showService:false,
                // 映射
                isMapping:false,    
                current_status:"create",
                // 后端服务
                form_data_service:{
                    service_type:"",
                    service:"",
                    basePath:"",
                    query_Url:"",
                    link_timeout:"",
                    read_timeout:"",
                },
                validate_data_service:{
                    service_type: [
                        {required: false, message:'后端服务类型不能为空', trigger: 'blur'}
                    ],
                    service: [
                        {required: false, message:'后端服务不能为空', trigger: 'blur'}
                    ],
                    query_Url: [
                        {required: false, validator:validate_query_url, trigger: 'blur'}
                    ],
                    link_timeout: [
                        {required: false, validator:validate_timeout_1, trigger: 'blur'}
                    ],
                    read_timeout: [
                        {required: false, validator:validate_timeout_2, trigger: 'blur'}
                    ],
                },
                service_type_list:[],
                service_list:[
                ],
                service_list_map:[],
                setBasePath:"",
                
                pageNo : 1,
                // 分页总数
                pageTotal: 0,
                pageSize:10,
                // 参数映射列表
                mapping_backend_list:[],
                constant_backend_list:[],
                columns_service_params_mapping:[
                    // {
                    //     type: 'selection',
                    //     width: 60,
                    //     align: 'center'
                    // },
                    {
                        title: '后端参数名称',
                        align: 'center',
                        key: 'backendName',

                        render: (h, params) => {
                            let create = this.$createElement

                            var self = this
                            params["form_interface_Name"] = {
                                backendName:params.row.backend_name?params.row.backend_name:params.row.backendName
                            }
                            let dom = create('Form', {
                                    ref:"form_interface_Name-"+params.index,

                                props: {
                                    model: params["form_interface_Name"],
                                    // rules: _this.passwordChangeFormRules
                                },
                            },[
                                
                                h('FormItem', {
                                    props: {
                                        prop: "backendName",
                                        rules: [{required:true,validator:this.validate_backend_name,trigger:"blur"}],
                                    },
                                },[
                                    // 使用说明见示例1
                                    h('Input', {
                                        props: {
                                            value:params["form_interface_Name"].backendName ,
                                        },
                                        on: {
                                            'on-change': (event) => {
                                                
                                            },
                                            'on-blur': (event) => {  
                                                this.data_service_params_mapping_list[params.index]["backend_name"] = event.target.value
                                                params["form_interface_Name"].backendName = event.target.value
                                                this.mapping_backend_list = [];
                                                for(var i in this.data_service_params_mapping_list){
                                                    // if(i != params.index)
                                                    this.mapping_backend_list.push(this.data_service_params_mapping_list[i]) 
                                                }  
                                                console.log(event.target.value)
                                                if(event.target.value){
                                                    this.getBackend(this.data_service_params_mapping_list);                                              
                                                }
                                            }
                                        }
                                    })
                                ])
                            ])
                            return dom
                        }
                    }
                    ,
                    {
                        title: '后端参数位置',
                        key: 'backend_prosition',
                        align: 'center',

                    },

                    {
                         title: '对应入参名称',
                         key: 'interface',
                         align: 'center',


                        render: (h, params) => {
                            let create = this.$createElement

                            var self = this
                            var params = params
                            params["form_interface"] = {
                                interface:params.row.interface
                            }
                            let dom = create('Form', {
                                ref:"form_interface-"+params.index,
                                props: {
                                    model: params["form_interface"],
                                    // rules: _this.passwordChangeFormRules
                                },
                            },[
                                
                                h('FormItem', {
                                    props: {
                                        prop: "interface",
                                        rules: [{required:true,validator:validate_interface}],
                                    },
                                    style: {  
                                        width: "100%",
                                    },
                                },[
                                    // 使用说明见示例1
                                    h('Select', {
                                        props: {
                                            value:params["form_interface"].interface ,

                                        },
                                        style: {  
                                        width: "100%",
                                    },
                                        on: {
                                            'on-change': (event) => {
                                                for(var j in this.data_service_params_mapping_list){
                                                    var tempParam = this.data_service_params_mapping_list[j]
                                                    if(j == params.index && this.data_service_params_mapping_list.length >1){
                                                        continue
                                                    }
                                                    if(event == tempParam.interface){
                                                        this.data_service_params_mapping_list[params.index].backend_prosition = "";
                                                        this.data_service_params_mapping_list[params.index].position = "";
                                                        this.data_service_params_mapping_list[params.index].type = "";
                                                        this.data_service_params_mapping_list[params.index].interface = "";
                                                        this.data_service_params_mapping_list[params.index].backend_name = "";
                                                        params["form_interface"].interface = ""
                                                        params.row.interface = ""
                                                        this.$ypMsg.notice_warning(this, "请选择其他参数");
                                                    }else{
                                                        var listData = {}
                                                        for(var i in params.row.params_list){
                                                            if(params.row.params_list[i]["interface"] == event){
                                                                listData = params.row.params_list[i]
                                                                break;
                                                            }
                                                        }
                                                        this.data_service_params_mapping_list[params.index].backend_prosition = listData.backend_prosition;
                                                        this.data_service_params_mapping_list[params.index].position = listData.position;
                                                        this.data_service_params_mapping_list[params.index].type = listData.type;
                                                        this.data_service_params_mapping_list[params.index].interface = listData.interface;
                                                        params["form_interface"].interface = event
                                                    }
                                                }
                                                var tempList = this.data_service_params_mapping_list
                                                this.data_service_params_mapping_list = []   
                                                for(var j in tempList){
                                                    this.data_service_params_mapping_list.push(tempList[j]);
                                                }
                                                this.getBackend(this.data_service_params_mapping_list);

                                            }
                                        }
                                    },params.row.params_list.map((item) =>{
                                        return h('Option', {
                                            props: {
                                                value: item.interface,
                                                label: item.interface,
                                            }
                                        })
                                    }))
                                ])
                            ])
                            return dom
                        }
                    }
                    ,{
                        title: '对应入参位置',
                        key: 'position',
                        align: 'center',


                    },{
                        title: '对应入参类型',
                        key: 'type',
                        align: 'center',

                    },{
                        title: '操作',
                        align: 'center',
                        width:90,
                        render: (h,params) =>{
                            return h('div',[
                            h('Button', {
                                props: {
                                    type: 'text',
                                    size: 'small',
                                },
                                // directives: [{
                                //     name: 'url',
                                //     value: {url: '/rest/api/security-req'}
                                // }],
                                on: {
                                    click: () => {
                                        this.delete_mapping (params.row);
                                    }
                                }
                            }, '移除'),
                            ])
                        }
                    },
                ],
                columns_service_params_mapping_desc:[
                    {
                        title: '后端参数名称',
                        align: 'center',
                        key: 'backendName',
                    },
                    {
                        title: '后端参数位置',
                        key: 'backend_prosition',
                        align: 'center',

                    },{
                        title: '对应入参名称',
                        key: 'interface',
                        align: 'center',

                    }
                    ,{
                        title: '对应入参位置',
                        key: 'position',
                        align: 'center',

                    },{
                        title: '对应入参类型',
                        key: 'type',
                        align: 'center',

                    }

                ],
                data_service_params_mapping_list:[
               
                ],
                // 常量参数列表
                columns_Constant:[
                    // {
                    //     type: 'selection',
                    //     width: 60,
                    //     align: 'center'
                    // },
                    {
                        title: '后端参数名称',
                        align: 'center',
                        key: 'backendName',
                        render: (h, params) => {
                            let create = this.$createElement

                            var self = this
                            params["form_backend_name"] = {
                                backendName:params.row.backend_name || params.row.backendName
                            }
                            let dom = create('Form', {
                                ref:"form_backend_name-"+params.index,
                                props: {
                                    model: params["form_backend_name"],
                                    // rules: _this.passwordChangeFormRules
                                },
                            },[
                                
                                h('FormItem', {
                                    props: {
                                        prop: "backendName",
                                        rules: [{required:true,validator:validate_const_backend_name,trigger:"blur"}],
                                    },
                                },[
                                    // 使用说明见示例1
                                    h('Input', {
                                        props: {
                                            value:params["form_backend_name"].backendName ,
                                        },
                                        on: {
                                            // 'on-change': (event) => {
                                            //     this.data_ConstantList[params.index].backend_name = event.target.value
                                            //     this.data_ConstantList[params.index].backendName = event.target.value
                                            //     params["form_backend_name"].backendName = event.target.value
                                            // },
                                            'on-blur': (event) => {  
                                                this.data_ConstantList[params.index].backend_name = event.target.value
                                                // this.data_ConstantList[params.index].backendName = event.target.value
                                                params["form_backend_name"].backendName = event.target.value
                                                this.constant_backend_list = [];
                                                for(var i in this.data_ConstantList){
                                                    // if(i != params.index){
                                                        this.constant_backend_list.push(this.data_ConstantList[i]) 
                                                    // }
                                                }                                                
                                            }
                                            
                                        }
                                    },[
                                h('span', {
                                    slot: 'prepend',
                                    props: {
                                        slot: 'prepend'
                                    }
                                },"x-yop-const-")
                            ])
                                ])
                            ])
                            return dom
                        }
                    },{
                        title: '参数值',
                        align: 'center',
                        key: 'value',
                        render: (h, params) => {
                            var create = this.$createElement
                            var self = this
                            params["form_value"] = {
                                value:params.row._value || params.row.value
                            }
                             var dom = create('Form', {
                                ref:"form_value-"+params.index,
                                props: {
                                    model: params["form_value"],
                                    // rules: _this.passwordChangeFormRules
                                },
                            },[
                                h('FormItem', {
                                    props: {
                                        prop: "value",
                                        rules: [{required:true,message:"不允许为空"}],
                                    },
                                },[
                                    // 使用说明见示例1
                                    h('Input', {
                                        props: {
                                            value:params["form_value"].value ,
                                        },
                                        on: {
                                            'on-change': (event) => {
                                                this.data_ConstantList[params.index]._value = event.target.value
                                                params["form_value"].value = event.target.value
                                            },
                                            
                                        }
                                    })
                                ])
                            ])
                            return dom
                        }
                    
                    },{
                        align: 'center',
                        title: '参数位置',
                        width:100,
                        key: 'location',
                    },{
                        title: '描述',
                        align: 'center',
                        key: 'description',
                        render: (h, params) => {
                            let create = this.$createElement
                            var self = this
                            params["form_description"] = {
                                description:params.row._description || params.row.description
                            }
                            let dom = create('Form', {
                                ref:"form_description-"+params.index,
                                props: {
                                    model: params["form_description"],
                                    // rules: _this.passwordChangeFormRules
                                },
                            },[
                                h('FormItem', {
                                    props: {
                                        prop: "description",
                                        rules: [{required:true,message:"不允许为空"}],
                                    },
                                },[
                                    // 使用说明见示例1
                                    h('Input', {
                                        props: {
                                            value:params["form_description"].description ,
                                        },
                                        on: {
                                            'on-change': (event) => {
                                                this.data_ConstantList[params.index]._description = event.target.value
                                                params["form_description"].description = event.target.value
                                            },
                                            
                                        }
                                    })
                                ])
                            ])
                            return dom
                        }
                    },{
                        title: '操作',
                        align: 'center',
                        width:90,

                        render: (h,params) =>{
                            return h('div',[
                            h('Button', {
                                props: {
                                    type: 'text',
                                    size: 'small',
                                },
                                // directives: [{
                                //     name: 'url',
                                //     value: {url: '/rest/api/security-req'}
                                // }],
                                on: {
                                    click: () => {
                                        this.delete_constant (params.row);
                                    }
                                }
                            }, '移除'),
                            ])
                        }
                    },
                ],
                columns_Constant_desc:[
                    {title: '后端参数名称',
                        align: 'center',
                        key: 'backendName',
                        render: (h, params) => {
                            // 在获取数据的时候去除了首部
                            return h('div',"x-yop-const-"+params.row.backendName);
                        }
                    },
                    {
                        title: '参数值',
                        align: 'center',
                        key: 'value',
                    },{
                        align: 'center',
                        title: '参数位置',
                        key: 'location',

                    },{
                        title: '描述',
                        align: 'center',
                        key: 'description'
                    }

                ],
                data_ConstantList:[

                ],
                
                // 系统参数列表
                columns_System:[
                    // {
                    //     type: 'selection',
                    //     width: 60,
                    //     align: 'center'
                    // },

                    {
                         title: '系统参数',
                         key: 'systemName',
                         align: 'center',
                         render: (h, params) => {
                             let create = this.$createElement
                             var params = params
                             params["form_system_name"] = {
                                systemName:params.row.sysParam || params.row.systemName
                            }
                             let dom = create('Form', {
                                ref:"form_system_name-"+params.index,
                                props: {
                                    model: params["form_system_name"],
                                    // rules: _this.passwordChangeFormRules
                                },
                            },[
                                
                                h('FormItem', {
                                    props: {
                                        prop: "systemName",
                                        rules: [{required:true,validator:validate_interface}],
                                    },
                                    style: {  
                                        width: "100%",
                                    },
                                },[
                                    // 使用说明见示例1
                                    h('Select', {
                                     props:{
                                         value: params["form_system_name"].systemName,
                                     },
                                     style: {  
                                        width: "100%",
                                    },
                                     on: {
                                         'on-change':(event) => {
                                                var diffArr = []
                                                diffArr = this.data_SystemList
                                                for(var i in diffArr){
                                                    if(i == params.index){
                                                　　　　 continue;   
                                                　　 }
                                                    for(j in diffArr[i]){
                                                        if(diffArr[i][j] == event){
                                                             this.$ypMsg.notice_warning(this, "请选择其他参数");
                                                            params.row.systemName = ""
                                                            params.row.sysParam = ""
                                                            this.data_SystemList[params.index].backendName = "";
                                                            this.data_SystemList[params.index].systemName = "";
                                                            this.data_SystemList[params.index].sysParam = "";
                                                            var tempList = this.data_SystemList
                                                            this.data_SystemList = []   
                                                            for(var j in tempList){
                                                                this.data_SystemList.push(tempList[j]);
                                                            }
                                                            return
                                                        }
                                                    }
                                                }

                                             var listData = {}
                                             for(var i in params.row.action){
                                                 if(params.row.action[i]["value"] == event){
                                                     listData = params.row.action[i];
                                                     break;
                                                 }
                                             }
                                             this.data_SystemList[params.index].sysParam = event; 
                                             params.row.sysParam = event 
                                             params["form_system_name"].systemName = event   
                                            this.data_SystemList[params.index].backendName = listData.parameterName;
                                            var tempList = this.data_SystemList
                                            this.data_SystemList = []   
                                            for(var j in tempList){
                                                this.data_SystemList.push(tempList[j]);
                                            }
                                            
                                         }
                                     }
                                 },
                                 params.row.action.map((item) =>{
                                     return h('Option', {
                                         props: {
                                             value: item.value,
                                             label: item.name
                                         }
                                     })
                                 }))
                             ])
                            ])
                            return dom
                        }
                    },
                    {
                        title: '后端参数名称',
                        align: 'center',
                        key: 'backendName',
                    },{
                        align: 'center',
                        title: '参数位置',
                        key: 'location',
                        width:100,

                    },
                    // {
                    //     title: '描述',
                    //     width: 140,
                    //     align: 'center',
                    //     key: 'interface_des',
                    //     render:(h,params) => {
                    //         return h('Input',{
                    //             props: {
                    //                 value:params.row.interface_des,
                    //                 size:'small',
                    //             },
                    //             on: {
                    //                 'on-change': (event) => {
                    //                     this.data_SystemList[params.index].description = event.target.value
                    //                 }
                    //             },
                    //         })
                    //     }
                    //   },
                      {
                        title: '操作',
                        align: 'center',
                        width:90,

                        render: (h,params) =>{
                            return h('div',[
                            h('Button', {
                                props: {
                                    type: 'text',
                                    size: 'small',
                                },
                                // directives: [{
                                //     name: 'url',
                                //     value: {url: '/rest/api/security-req'}
                                // }],
                                on: {
                                    click: () => {
                                        this.delete_system (params.row);
                                    }
                                }
                            }, '移除'),
                            ])
                        }
                    },
                ],
                columns_System_desc:[
                    {
                         title: '系统参数',
                         key: 'systemName',
                         align: 'center',
                    },
                    {
                        title: '后端参数名称',
                        align: 'center',
                        key: 'backendName',
                    },{
                        align: 'center',
                        title: '参数位置',
                        key: 'location',
                    },
                    // {
                    //     title: '描述',
                    //     width: 140,
                    //     align: 'center',
                    //     key: 'interface_des',
                    //     render:(h,params) => {
                    //         return h('Input',{
                    //             props: {
                    //                 value:params.row.interface_des,
                    //                 size:'small',
                    //             },
                    //             on: {
                    //                 'on-change': (event) => {
                    //                     this.data_SystemList[params.index].description = event.target.value
                    //                 }
                    //             },
                    //         })
                    //     }
                    //   },
                     
                ],
                data_SystemList:[
                    // {
                    //     action:[
                    //         {value: "#context.appkey", name: "应用标示appKey"},
                    //         {value: "#context.customerNo", name: "商户编号customerNo"},
                    //         {value: "#context.oauth2.userid", name: "oauth2token中还原出的userid"},
                    //         {value: "#context.productCode", name: "产品编码"},
                    //         {value: "#header.requestId", name: "唯一请求标识"},
                    //         {value: "#header.date", name: "请求发起时间"},
                    //         {value: "#header.requestIp", name: "调用发起者IP"},
                    //         {value: "#header.requestSource", name: "调用来源"},
                    //         {value: "#header.content.sha256", name: "变量x-yop-content-sha256"}
                    //     ],
                    //     location:"HEADER",
                    //     backendName:"12",
                    //     interface_des:"121212",
                    // }
                ],
                multiSelectedData_mapping:[],
                multiSelectedData_Constant:[],
                multiSelectedData_System:[],

                // 非空校验结果
                // 端点url数据绑定
                endpoint_Url : '',
                // 显示端点url部分
                show_pointUrl : false,
                // 端点类名数据绑定
                endpoint_Name: '',
                // 是否幂等数据绑定
                idempotent: '否',
                // 端点url自定义
                pointUrl_user_defined: false,
                // 适配类型下拉框disabled
                data_select_type_disabled : false,
                // 适配类型下拉框数据绑定
                data_select_type: 'TRANSFORM',
                // 适配类型下拉框数据
                data_type_List: [
                    // {
                    //     value: 'TRANSFORM',
                    //     label: '转换'
                    // },
                    {
                        value: 'MAPPING',
                        label: '映射'
                    },
                    {
                        value: 'PASSTHROUGH',
                        label: '透传'
                    }
                ],
                // 端点协议下拉框 disabled
                data_select_EndpointProtocol_disabled : false,
                // 端点协议下拉框数据绑定
                data_select_EndpointProtocol: 'HESSIAN',
                // 端点协议下拉框数据
                data_EndpointProtocol_List: [
                    {
                        value: 'HESSIAN',
                        label: 'hessian'
                    }
                ],
                // 端点方法下拉框数据绑定
                data_select_EndpointMethod: '',
                // 端点方法下拉框数据
                data_EndpointMethod_List: [],
                // 当前内容
                current_content : '',
                // 当前端点url内容
                current_content_url : '',
                // url校验结果
                pass_url : true,
                sys_params_list:[],
                mappingList:[],
                validate_backend_name:""
            }
        },
        methods : {
            cache_return () {
                this.sys_params_list =  this.$store.state.sys_params_list.length?this.$store.state.sys_params_list: JSON.parse(localStorage.getItem("sys_params_list"));
            },

            // 页面初始化
            init(){

                this.validate_backend_name = (rule, value, callback) => {
                        // var reg = /^[a-z]+((\d)|([A-Z0-9][a-z0-9]+))*([A-Z])?$/
                        var reg = /^[a-z][a-zA-Z0-9]*$/
                        console.log("操作后的校验",reg)
                    if(!value){
                        callback(new Error('不能为空'));
                    }else
                    if(!reg.test(value)){
                        callback(new Error('允许小写驼峰格式'));
                    }else{
                        console.log(rule,"oooo");
                        var index =  0
                        for(var i in this.mappingList){
                            if(value == this.mappingList[i].interface){
                                
                                    callback(new Error('参数名不允许重复'));
                                }
                        }
                        for(var j in this.mapping_backend_list){
                            if(value == this.mapping_backend_list[j].backend_name || value == this.mapping_backend_list[j].backendName){
                                index++
                                if(index>=2){
                                    callback(new Error('参数名不允许重复'));
                                }
                            }
                        }
                        callback();
                    }
                };
                //  this.sys_params_list= [
                //                 {value: "#context.appkey", name: "应用标示appKey"},
                //                 {value: "#context.customerNo", name: "商户编号customerNo"},
                //                 {value: "#context.oauth2.userid", name: "oauth2token中还原出的userid"},
                //                 {value: "#context.productCode", name: "产品编码"},
                //                 {value: "#header.requestId", name: "唯一请求标识"},
                //                 {value: "#header.date", name: "请求发起时间"},
                //                 {value: "#header.requestIp", name: "调用发起者IP"},
                //                 {value: "#header.requestSource", name: "调用来源"},
                //                 {value: "#header.content.sha256", name: "变量x-yop-content-sha256"}
                //             ];
                
            },
            handleselection_mapping(value){
                this.multiSelectedData_mapping =value;
            },
            add_mapping(){
                        var obj = {
                            backendName:"",
                            backend_prosition:"",
                            params_list:this.mappingList,
                            // interface:"",
                            position:"",
                            type:"",
                        }
                        this.data_service_params_mapping_list.push(JSON.parse(JSON.stringify(obj)))
                
            },
            handleselection_Constant(value){
                this.multiSelectedData_Constant =value;
            },
            add_constantList(){
                    if(this.data_service_params_mapping_list.length + this.data_ConstantList.length > 9){
                        this.$ypMsg.notice_warning(this, "常量参数和系统参数总和不大于10条");
                    }else{
                        var obj = {
                            backendName:"",
                            location:"HEADER",
                            value:"",
                            description:"",
                        }

                        this.data_ConstantList.push(JSON.parse(JSON.stringify(obj)))
                    }
                
            },
            handleselection_System(value){
                this.multiSelectedData_System = value;
            },
            add_systemList(){
                if(this.data_service_params_mapping_list.length + this.data_ConstantList.length > 9){
                        this.$ypMsg.notice_warning(this, "常量参数和系统参数总和不大于10条");
                }else{
                    var obj = {
                        action:this.sys_params_list,
                        location:"HEADER",
                        systemName:"",
                    }
                    this.data_SystemList.push(obj)
                }
            },
            setUnderLine(undeLine){
                this.undeLine = undeLine
                console.log(this.undeLine)
                console.log("hhhhhh")
                if(this.undeLine){
                        this.validate_backend_name = (rule, value, callback) => {
                        // var reg = /^[a-z]+((\d)|([A-Z0-9][a-z0-9]+))*([A-Z])?$/
                    var reg = /^([a-z][a-zA-Z0-9]*)(_[a-z][a-zA-Z0-9]*)*$/;

                        console.log("操作后的校验1",reg)
                        if(!value){
                            callback(new Error('不能为空'));
                        }else
                        if(!reg.test(value)){
                            callback(new Error('小写驼峰允许下划线'));
                        }else{
                            console.log(rule,"oooo");
                            var index =  0
                            for(var i in this.mappingList){
                                if(value == this.mappingList[i].interface){
                                    
                                        callback(new Error('参数名不允许重复'));
                                    }
                            }
                            for(var j in this.mapping_backend_list){
                                if(value == this.mapping_backend_list[j].backend_name || value == this.mapping_backend_list[j].backendName){
                                    index++
                                    if(index>=2){
                                        callback(new Error('参数名不允许重复'));
                                    }
                                }
                            }
                            callback();
                        }
                    };
                }else{
                    this.validate_backend_name = (rule, value, callback) => {
                        // var reg = /^[a-z]+((\d)|([A-Z0-9][a-z0-9]+))*([A-Z])?$/
                        var reg = /^[a-z][a-zA-Z0-9]*$/
                        console.log("操作后的校验2",reg)
                        if(!value){
                            callback(new Error('不能为空'));
                        }else
                        if(!reg.test(value)){
                            callback(new Error('允许小写驼峰格式'));
                        }else{
                            console.log(rule,"oooo");
                            var index =  0
                            for(var i in this.mappingList){
                                if(value == this.mappingList[i].interface){
                                    
                                        callback(new Error('参数名不允许重复'));
                                    }
                            }
                            for(var j in this.mapping_backend_list){
                                if(value == this.mapping_backend_list[j].backend_name || value == this.mapping_backend_list[j].backendName){
                                    index++
                                    if(index>=2){
                                        callback(new Error('参数名不允许重复'));
                                    }
                                }
                            }
                            callback();
                        }
                    };
                }

            },
            setMappingList(mappingList,initData){  
                console.log(initData,mappingList)
                var tempList = []
                for(let j in mappingList){
                    if(mappingList[j].backend_name ){
                        tempList.push(mappingList[j])
                    }
                }
                console.log(mappingList,"==")
                this.getBackend(mappingList)

                this.mappingList = mappingList
                this.data_service_params_mapping_list = []
                for (var i in tempList) {
                    var itemsI = tempList[i]
                    if(itemsI.backendName){
                        this.data_service_params_mapping_list.push({
                            backend_name: itemsI.backendName?itemsI.backendName:itemsI.backend_name,
                            backendName: itemsI.backendName?itemsI.backendName:itemsI.backend_name,
                            interface: itemsI.interface,
                            backend_prosition : itemsI.backend_prosition,
                            params_list : this.mappingList,
                            position : itemsI.position,
                            type : itemsI.type
                        });
                    }
                }
                console.log(this.data_service_params_mapping_list,"---")

            },
            pageRefresh_mapping(){},
            pageRefresh_Constant(){},
            pageRefresh_System(){},
            
            // 删除一条映射
            delete_mapping(row){
                this.$Modal.confirm({
                    title: '删除此这条映射参数',
                    content: '确定删除映射参数？',
                    'ok-text': '确认',
                    onOk: () => {
                        var index = row._index
                        this.data_service_params_mapping_list.splice(index,1)
                        var data_service_params_mapping_list = this.data_service_params_mapping_list
                        this.getBackend(data_service_params_mapping_list);                                              
                        
                    }
                });

            },
            // 删除常量
            delete_constant(row){
                this.$Modal.confirm({
                    title: '删除此这条常量参数',
                    content: '确定删除常量参数？',
                    'ok-text': '确认',
                    onOk: () => {
                        var index = row._index
                        this.data_ConstantList.splice(index,1)
                    }
                });

            },
            // 删除系统
            delete_system(row){
                this.$Modal.confirm({
                    title: '删除此这条系统参数',
                    content: '确定删除系统参数？',
                    'ok-text': '确认',
                    onOk: () => {
                        var index = row._index
                        this.data_SystemList.splice(index,1)
                    }
                });

                
            },

            getServiceList(apiGroup){
                    var param = {};
                    var self = this
                    api.yop_backend_service_simple_list(param).then(
                        (response) =>{
                            var response = response.data
                            this.service_list = [];
                            this.service_list_map = [];
                            let service = response.data.result;
                            // this.data_api_model= apigroup[0].groupCode;
                            for(var i in service){
                                this.service_list.push(
                                    {
                                        label: service[i].name,
                                        value:service[i].name,
                                        name: service[i].name
                                        
                                        // [service[i].connectionTimeout,service[i].readTimeout]
                                    }
                                );
                                var obj = {}
                                var service_name = service[i].name
                                obj[service_name] = {
                                    connectionTimeout:service[i].connectionTimeout,
                                    readTimeout:service[i].readTimeout,
                                    basePath:service[i].basePath,
                                }
                                this.service_list_map.push(obj)
                            }
                            if(self.setBasePath){
                                self.form_data_service.basePath = self.showBasePath(self.setBasePath).basePath;
                            }else{
                                if(this.form_data_service && this.form_data_service.service) {
                                    const currentData = self.showBasePath(this.form_data_service.service)
                                    self.form_data_service.basePath = currentData.basePath
                                    self.form_data_service.link_timeout = currentData.connectionTimeout
                                    self.form_data_service.read_timeout = currentData.readTimeout
                                }   
                            }
                        }
                    );

            },
            set_current_status(val){
                this.current_status = val
            },
            showBasePath(setBasePath){
                for(var i = 0 ;i < this.service_list_map.length ; i ++){
                    var service_list_mapI = this.service_list_map[i]
                    for(var j in service_list_mapI){
                        if(j == setBasePath){
                            return service_list_mapI[j]
                        }
                    }
                }
            },
            get_service_name(service_name){
                for(var i = 0 ;i < this.service_list_map.length ; i ++){
                    var service_list_mapI = this.service_list_map[i]
                    for(var j in service_list_mapI){
                        if(j == service_name){
                            return service_list_mapI[j]
                        }
                    }
                }
            },
            getServiceTypeList(){
                    api.yop_backend_service_simple_type_list().then(
                        (response) =>{
                            var response = response.data
                            this.service_type_list = [];
                            let service_type = response.data.result;
                            // this.data_api_model= apigroup[0].groupCode;
                            // for(var i in service_type){
                            //     this.service_type_list.push(
                            //         {
                            //             label: service_type[i].code+'('+service_type[i].name+')',
                            //             value: service_type[i].code,
                            //             name: service_type[i].name
                            //         }
                            //     )
                            // }
                            this.service_type_list.push(
                                {
                                    label: "HTTP(HTTP(s))",
                                    value: "HTTP",
                                    name: "HTTP(s)"
                                }
                            )
                        }
                    );

            },
            setCurrent_status(val){
                this.current_status == val
            },
            setQueryWay(val){
               this.form_data_service.method = val;
            },
            // 本地数据获取
            setRequestData (data) {
                if(!!!data){
                    return false;
                }
               this.form_data_service.service_type = data.type;
               this.form_data_service.service = data.serviceName
               this.showService = true
               this.form_data_service.query_Url = data.path;
               this.setBasePath = data.serviceName
               this.form_data_service.method = data.method;
               this.form_data_service.link_timeout = data.connectionTimeout;
               this.form_data_service.read_timeout = data.readTimeout;
               if(data.constantParameters){
                   for(var i in data.constantParameters){
                       for(var j in data.constantParameters[i]){
                           if(j == "backendName"){
                               data.constantParameters[i].backendName = data.constantParameters[i]["backendName"].split("x-yop-const-")[1]
                           }
                       }
                   }
                this.data_ConstantList = data.constantParameters || []

               }
            //    this.data_SystemList = data.systemParameters
                if(data.systemParameters){
                    for(var i = 0 ; i < data.systemParameters.length ;i ++){
                        data.systemParameters[i].action = this.sys_params_list
                    }
                    this.data_SystemList = data.systemParameters
                }
            // api.yop_apiManagement_commons_sys_params().then(
            //             (response) =>{
            //                 var response = response.data
            //                 this.sys_params_list = [];
            //                 let sys_params = response.data.result;
            //                 for(var i in sys_params){
            //                     this.sys_params_list.push(
            //                         {
            //                             label: sys_params[i].name,
            //                             value:sys_params[i].code,
            //                             name: sys_params[i].name,
            //                             parameterName:sys_params[i].parameterName
            //                         }
            //                     );
            //                 }
                            
            //             }
            //         );
            },
            backPromise() {
                var promise = new Promise(function(resolve, reject) {
                    // ... some code

                    if (true) {
                        resolve(false); //怎么将这个值作为fn1函数的返回值？
                    } else {
                        reject(true);
                    }
                });

                return promise.then(function(value) {
                    return value;  
                })
            },
            saveCheck_form_interface_Name(value){
                this.mapping_backend_list = this.data_service_params_mapping_list
                var flag = true
                for(let i = 0;i <= value;i ++ ){
                    if(this.$refs["form_interface_Name-"+i])
                    this.$refs["form_interface_Name-"+i].validate((valid) => {
                        if (valid) {
                            return true
                        } else {
                            flag = false
                        }
                    });
                }
                return flag
            },
            saveCheck_form_interface(value){
                var flag = true
                for(let i = 0;i <= value;i ++ ){
                    if(this.$refs["form_interface-"+i])
                    this.$refs["form_interface-"+i].validate((valid) => {
                        if (valid) {
                            return true
                        } else {
                            flag = false
                        }
                    });
                }
                return flag
            },
            saveCheck_form_backend_name(value){
                this.constant_backend_list = this.data_ConstantList
                var flag = true
                for(let i = 0;i <= value;i ++ ){
                    if(this.$refs["form_backend_name-"+i])
                    this.$refs["form_backend_name-"+i].validate((valid) => {
                        if (valid) {
                            return true
                        } else {
                            flag = false
                        }
                    });
                }
                return flag
            },
            saveCheck_form_value(value){
                var flag = true
                for(let i = 0;i <= value;i ++ ){
                    if(this.$refs["form_value-"+i])
                    this.$refs["form_value-"+i].validate((valid) => {
                        if (valid) {
                            return true
                        } else {
                            flag = false
                        }
                    });
                }
                return flag
            },
            saveCheck_form_description(value){
                var flag = true
                for(let i = 0;i <= value;i ++ ){
                    if(this.$refs["form_description-"+i])
                    this.$refs["form_description-"+i].validate((valid) => {
                        if (valid) {
                            return true
                        } else {
                            flag = false
                        }
                    });
                }
                return flag
            },
            saveCheck_form_system_name(value){
                var flag = true
                for(let i = 0;i <= value;i ++ ){
                    if(this.$refs["form_system_name-"+i])
                    this.$refs["form_system_name-"+i].validate((valid) => {
                        if (valid) {
                            return true
                        } else {
                            flag = false
                        }
                    });
                    
                }
                return flag
            },
            toGetBackend(){
                this.getBackend(this.data_service_params_mapping_list)
            },
            getBackend(data_service_params_mapping_list){
                console.log(data_service_params_mapping_list,"bh");
                
                var data_service_params_mapping_list = data_service_params_mapping_list
                this.backendNameList = [];
                if(data_service_params_mapping_list.length == 0){
                    this.backendNameList = []
                }
                for(let j  = 0 ;j <data_service_params_mapping_list.length ; j ++){
                    for(let i in data_service_params_mapping_list[j]){
                        if((i === "backend_name" || i === "backendName") &&data_service_params_mapping_list[j][i]){
                            this.backendNameList.push({backendName:data_service_params_mapping_list[j][i],name:data_service_params_mapping_list[j].interface});
                        }
                       
                    }
                }
                this.$emit("backendNameListFunc",this.backendNameList);
            },
            saveCheck(){
                this.getBackend(this.data_service_params_mapping_list)
                console.log(this.data_service_params_mapping_list,"ll");
                var temp_interface_Name = 0,
                temp_interface= 0,
                temp_backend_name= 0,
                temp_value= 0,
                temp_description= 0,
                temp_system_name= 0
                
                for(var i in this.$refs){
                    if(i.indexOf("form_interface_Name-") > -1){
                        temp_interface_Name = i.split("form_interface_Name-")[1]
                    }
                    if(i.indexOf("form_interface-") > -1){
                        temp_interface = i.split("form_interface-")[1]
                    }
                    if(i.indexOf("form_backend_name-") > -1){
                        temp_backend_name = i.split("form_backend_name-")[1]
                    }
                    if(i.indexOf("form_value-") > -1){
                        temp_value = i.split("form_value-")[1]
                    }
                    if(i.indexOf("form_description-") > -1){
                        temp_description = i.split("form_description-")[1]
                    }
                    if(i.indexOf("form_system_name-") > -1){
                        temp_system_name = i.split("form_system_name-")[1]
                    }
                }
                var flag1 = this.saveCheck_form_interface_Name(temp_interface_Name)
                var flag2 = this.saveCheck_form_interface(temp_interface)
                var flag3 = this.saveCheck_form_backend_name(temp_backend_name)
                var flag4 = this.saveCheck_form_value(temp_value)
                var flag5 = this.saveCheck_form_description(temp_description)
                var flag6 = this.saveCheck_form_system_name(temp_system_name)

                if(!flag1 || !flag2 || !flag3 || !flag4 || !flag5 || !flag6){
                    return this.backPromise()
                }
                
            
        // 基本信息的验证
                return this.$refs["form_data_service"].validate((valid) => {
                    
                    
                    if (valid) {
                        
                        
                        var param = {
                            paramForm : this.form_data_service,
                            data_ConstantList:this.data_ConstantList,
                            data_SystemList:this.data_SystemList
                        }
                        if(this.data_ConstantList.length == 0){
                            delete param.data_ConstantList
                        }
                        if(this.data_SystemList.length == 0){
                            delete param.data_SystemList
                        }
                        
                        this.$emit("passParamService",JSON.parse(JSON.stringify(param)))

                        return true
                    } else {
                        this.$Message.error('请检查');
                        return false
                    }
                });
            },
            changeService_type(value){
                this.form_data_service.service_type = value
            },
            // 后盾服务change
            getServiceCon(value){
                if(!!!this.form_data_service.service_type){
                    this.$Message.warning('请先选择后端服务类型');
                    this.form_data_service.service = ''
                    return false;
                }
                if(localStorage.noEndpoint === 'true'){
                    this.getServiceList('')
                }
                this.showService = true
                if(this.current_status === "create"){
                    this.form_data_service.link_timeout = this.get_service_name(value).connectionTimeout
                    this.form_data_service.read_timeout = this.get_service_name(value).readTimeout
                    this.form_data_service.basePath = this.get_service_name(value).basePath
                }
            },
            setServiceType(value){
                if(value === "MAPPING"){
                    this.isMapping = true
                }else{
                    this.isMapping = false
                }
            },
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
          
            // onblur调用参数
            keydown () {
                if(this.current_content === this.endpoint_Name){

                }else{
                    if(this.pointUrl_user_defined){
                        this.pass_url = this.IsUrl(this.endpoint_Url)
                        if(this.pass_url){
                            this.run_request_point();
                        }else{
                            this.$ypMsg.notice_warning(this,'url不符合规范请修改');
                        }
                    }else{
                        // 请求端点方法
                        this.run_request_point();
                    }

                }

            },
            // 执行请求端点方法
            run_request_point () {
                if(this.endpoint_Name && this.endpoint_Name !== ''){
                    // var params = new URLSearchParams();
                    // params.append('className', this.endpoint_Name);
                    var params ;
                }
                if(this.pointUrl_user_defined && this.endpoint_Url !== ''){
                    // params.append('endServiceUrl', this.endpoint_Url);
                    params = {
                        params:{
                            'className': this.endpoint_Name,
                            'endServiceUrl' : this.endpoint_Url
                        }
                    }
                }else{
                    params= {
                        params:{
                            'className': this.endpoint_Name,
                        }
                    }

                    api.yop_apiManagement_loader_methodQuery(params).then(
                        (response) =>{
                            this.data_EndpointMethod_List = [];
                            this.data_select_EndpointMethod = '';
                            let MethodArray = response.data.data.methodList;
                            if(this.data_select_EndpointMethod){
                                this.data_select_EndpointMethod = MethodArray[0];
                            }
                            for(var i in MethodArray){
                                this.data_EndpointMethod_List.push(
                                    {
                                        label : MethodArray[i],
                                        value : MethodArray[i]
                                    }
                                );
                            }
                        }
                    )
                }
            },
            // onfocus 调用方法
            onFocus () {
                this.current_content = this.endpoint_Name;
            },
            // 端点url onblur调用参数
            onblur_url () {
                if(this.current_content_url === this.endpoint_Url){

                }else{
                    if(this.endpoint_Url === '' && this.pointUrl_user_defined ===false){
                        this.run_request_point();
                    }else{
                        this.pass_url = this.IsUrl(this.endpoint_Url)
                        if(this.pass_url){
                            // 请求端点方法
                            this.run_request_point();
                        }else{
                            this.$ypMsg.notice_warning(this,'url不符合规范请修改');
                        }
                    }
                }

            },
            // 端点url onfocus 调用方法
            onFocus_url () {
                this.current_content_url = this.endpoint_Url;
            },
            // 继承上层界面的数据
            dataInit (val) {
                this.idempotent=val.idempotent;
                this.endpoint_Name = val.endpoint_Name;
                this.endpoint_Url = val.endpoint_Url;
                this.show_pointUrl = val.show_pointUrl;
                this.pointUrl_user_defined = val.pointUrl_user_defined;
                this.data_select_type = val.data_select_type;
                this.data_type_List = val.data_type_List;
                this.data_select_EndpointProtocol = val.data_select_EndpointProtocol;
                this.data_EndpointProtocol_List = val.data_EndpointProtocol_List;
                this.data_select_EndpointMethod = val.data_select_EndpointMethod;
                this.data_EndpointMethod_List =val.data_EndpointMethod_List;
            },
        },
        created(){
            this.cache_return();
        },
        mounted () {
            this.init();
        },
    };
</script>

<style scoped>

</style>
