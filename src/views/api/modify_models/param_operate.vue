<style lang="less">
    .width-50-perc{
        width:45%;
    }
    .width-100-perc{
        width:100%;
    }
    .margin-top-0{
        margin-top : 0!important;
    }
    .max_min .ivu-form-item-error-tip {
        white-space: nowrap!important;
    }
    .ivu-form-item-error-tip {
        white-space: nowrap!important;

    }
    
    /*.margin-right-9{*/
    /*margin-right: 9px!important;*/
    /*}*/
</style>
<template>
    <div>
        <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="80%">
            <p slot="header" style="color:#2d8cf0;">
                <span style="color:black" v-show="current_panel ==='create'">新增参数</span>
                <span style="color:black" v-show="current_panel ==='modify'">修改参数</span>
            </p>
            <Row>
                <Col span="12">
                <!--<Card>-->
                <Form ref="form_data_left"  :model="form_data" inline label-position="top">
                    <FormItem id="model_input_name" label="名称：" class="width-50-perc" prop="name" :rules="{required:true,validator:this.validate_api_name,trigger:'blur'}">
                        <Input id="model_input_name_child" type="text" v-model="form_data.name"
                            style="width:170px"></Input>
                    </FormItem>
                    <FormItem id="model_input_name" label="中文名称：" class="width-50-perc" prop="title" :rules="{required:true, message:'中文名称必填'}">
                        <Input id="model_input_name_child" type="text" v-model="form_data.title"
                            style="width:170px" :maxlength="30"></Input>
                    </FormItem>
                    <FormItem id="model_input_type" class="width-50-perc" label="类型：" prop="type" :rules="{required:true,message:'类型不能为空'}">
                        <Cascader :data="type_data" v-model="form_data.type" style="width:170px" @on-change="cascader_change"></Cascader>
                    </FormItem>
                    
                    <FormItem id="model_switch_must_fill" class="width-50-perc" label="是否必填：" prop="required">
                        <i-switch  size="large" v-model="form_data.required"
                          @on-change="(val) => { if (val) { form_data.extensions['x-yop-api-param-condition'].conditionRequired = false; form_data.extensions['x-yop-api-param-condition'].value = ''}}"
                        >
                            <Icon slot="open"></Icon>
                            <Icon slot="close"></Icon>
                        </i-switch>
                    </FormItem>
                    <FormItem id="model_switch_del" class="width-50-perc" label="是否废弃：" prop="deprecated">
                        <i-switch id="model_switch_del_child" size="large" v-model="form_data.deprecated">
                            <Icon slot="open"></Icon>
                            <Icon slot="close"></Icon>
                        </i-switch>
                    </FormItem>
                    <FormItem class="width-50-perc" label="是否条件必填：" prop="conditionRequired">
                      <i-switch 
                        size="large"
                        v-model="form_data.extensions['x-yop-api-param-condition'].conditionRequired"
                        @on-change="(val) =>{if(val){ form_data.required = false}}"
                      >
                        <Icon slot="open"></Icon>
                        <Icon slot="close"></Icon>
                      </i-switch>
                    </FormItem>
                    <FormItem
                      v-if="form_data.extensions['x-yop-api-param-condition'].conditionRequired"
                      class="width-50-perc" 
                      label="条件：" 
                       prop="extensions.x-yop-api-param-condition.value"
                      :rules="{ required: true, message: '条件必填' }"
                    >
                      <Input 
                        v-model="form_data.extensions['x-yop-api-param-condition'].value" 
                        type="textarea" 
                        :rows="4" 
                        placeholder="用文字描述必填的场景，不超过100字符">
                      </Input>
                    </FormItem>
                    <FormItem id="model_switch_word" v-show="this.more_attr && this.form_data.type[1] !== 'object'" class="width-50-perc" label="敏感字段：" prop="sensitive" >
                        <i-switch id="model_switch_word_child" :disabled="if_edit_sensitive"  size="large" v-model="form_data.sensitive">
                            <Icon slot="open"></Icon>
                            <Icon slot="close"></Icon>
                        </i-switch>
                    </FormItem>
                    <FormItem id="model_select_model" v-if="this.form_data.type[1] === 'object' " label="模型：" class="width-50-perc" prop="model" :rules="{required:true,message:'model不能为空'}">
                        <Select ref="modal_select_4"  id='modal_apiM_select_4' size="small" v-model="form_data.model"  style="width:170px" placeholder="请选择" @on-change="setModel">
                            <Option v-for="item in data_api_model_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
                        </Select>
                        <p style="color: #2d8cf0;cursor: pointer;" @click="watchModelDetail">查看详情</p>
                    </FormItem>

                    <FormItem v-show="this.form_data.type[1] !== 'object' "  class="width-100-perc" label="描述：" prop="description">
                        <TextEditor ref="textEditor" size="350px" :id="id" />
                    </FormItem>
                </Form>
                <!--</Card>-->
                </Col>
                <Col span="12">
                <!--<Card>-->
                <Form 
                  ref="form_data_right"
                  :model="form_data"
                  :rules="form_data_right"
                  inline
                  label-position="top"
                >
                    <FormItem
                      v-if="
                        this.more_attr 
                        && this.form_data.type[0] != 'object' 
                        && this.form_data.type[0] != 'array' 
                        && this.form_data.type[1] != 'enum'
                      "
                      label="示例值："
                      prop="example"
                      class="width-50-perc"
                    >
                      <Input
                        type="text"
                        v-model="form_data.example"
                        @on-change="onExampleChange"
                        ></Input>
                      <p style="color: #999;white-space: nowrap;">勾选“设为默认值”后，会同时作为默认值进行文档展示。</p>
                    </FormItem>
                    <FormItem
                       v-if="
                        this.more_attr 
                        && this.form_data.type[0] != 'object' 
                        && this.form_data.type[0] != 'array' 
                        && this.form_data.type[1] != 'enum'
                      "
                      label=""
                      prop="default"
                      class="width-50-perc"
                    >
                      <div style="padding-top: 22px">
                        <Checkbox
                          :value="!!form_data.default && form_data.default === form_data.example"
                          @on-change="onDefaultChange"
                          :disabled="!form_data.example"
                        >
                        设为默认值
                        </Checkbox>
                      </div>
                    </FormItem>
                    <FormItem v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc max_min" label="最大值：" prop="maximum">
                        <Input  id="model_input_max" type="text" v-model="form_data.maximum"
                            style="width:85%"></Input>
                    </FormItem>
                    <FormItem v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc" label="包含最大值：" prop="exclusiveMaximum">
                        <i-switch size="large" id="model_switch_max" :true-value="false"  :false-value="true"  v-model="form_data.exclusiveMaximum">
                            <Icon slot="open"></Icon>
                            <Icon slot="close"></Icon>
                        </i-switch>
                    </FormItem>
                    <FormItem  v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc max_min" label="最小值：" prop="minimum">
                        <Input type="text" id="model_input_min" v-model="form_data.minimum"
                            style="width:85%"></Input>
                    </FormItem>
                    <FormItem v-show="this.form_data_visual.extremum && this.more_attr" class="width-50-perc" label="包含最小值：" prop="exclusiveMinimum">
                        <i-switch id="model_switch_min" size="large" :true-value="false"  :false-value="true"  v-model="form_data.exclusiveMinimum">
                            <Icon slot="open"></Icon>
                            <Icon slot="close"></Icon>
                        </i-switch>
                    </FormItem>
                    <FormItem 
                      v-show="form_data.type[0] === 'string'" 
                      class="width-50-perc max_min" label="最大长度：" prop="maxLength">
                        <Input type="text" id="model_input_maxlength" v-model="form_data.maxLength"
                            style="width:85%"></Input>
                    </FormItem>
                    <FormItem  v-show="this.form_data_visual.length && this.more_attr" class="width-50-perc max_min" label="最小长度：" prop="minLength">
                        <Input type="text" id="model_input_minlength" v-model="form_data.minLength"
                            style="width:85%"></Input>
                    </FormItem>
                    <FormItem  v-show="this.form_data_visual.itemSize && this.more_attr" class="width-50-perc max_min" label="最大数目：" prop="maxItems">
                        <Input id="model_input_maxsize" type="text" v-model="form_data.maxItems"
                            style="width:85%"></Input>
                    </FormItem>
                    <FormItem  v-show="this.form_data_visual.itemSize && this.more_attr" class="width-50-perc max_min" label="最小数目：" prop="minItems">
                        <Input id="model_input_minsize" type="text" v-model="form_data.minItems"
                            style="width:85%"></Input>
                    </FormItem>
                    
                    <FormItem  v-show="this.showOrder && this.more_attr" class="width-100-perc" label="业务订单号：" prop="param_name" >
                        <i-switch size="large" id="model_switch_orderno" v-model="form_data.orderNo">
                            <Icon slot="open"></Icon>
                            <Icon slot="close"></Icon>
                        </i-switch>
                    </FormItem>
                    <FormItem v-show="this.form_data_visual.pattern && this.more_attr" label="正则表达式校验："  class="width-100-perc" prop="pattern">
                        <Input type="text" id="model_input_reg" v-model="form_data.pattern"
                            style="width:55%"></Input>
                        <common-select ref="select_common_pattern" @on-update="updateSelect_common_pattern"
                                    type="normal"
                                    keyWord="result"
                                    holder="常用正则表达式"
                                    code="value"
                                    title="title"
                                    group="api_common_pattern"
                                    @on-loaded="select_callBack"
                                    :default="this.data_select_pattern"
                                    :uri="this.$store.state.select.api_common_pattern.uri"
                                    style="width:30%;margin-top:0"></common-select>
                    </FormItem>
                    <FormItem v-show="this.form_data.type[1] === 'enum'" class="width-100-perc" label="枚举：" prop="enum">
                      <AddEnum ref="addEnum" />
                    </FormItem>
                </Form>
                </Col>
                <Col v-if="this.form_data_visual.additionalProperties||this.form_data_visual.items" style="border-bottom: 1px solid #eee;margin-bottom: 20px;" span="24">

                </Col>

                <Col v-show="this.form_data_visual.additionalProperties" span="24">
                  <Tag type="dot" color="blue">值：</Tag>
                  <subcontent-map
                    ref="subContent_map"
                    :depth="2"
                    :type_format="this.type_data"
                    :type_format_cut="this.type_data_map"
                    :apiGroupCode="apiGroupCode"
                    @getSubContent="getSubContent"
                    @detailModel="detailModel"
                  ></subcontent-map>
                </Col>
                <Col v-show="this.form_data_visual.items" span="24">
                <Tag type="dot" color="green">数组元素：</Tag>
                  <subcontent 
                    ref="subContent_array" 
                    :depth="2" 
                    :type_format="this.type_data" 
                    :type_format_cut="this.type_data_map" 
                    :apiGroupCode="apiGroupCode" 
                    @getSubContent="getSubContent"
                    @detailModel="detailModel"
                  ></subcontent>
                </Col>
                <Col span="22" style="margin-top: 20px;margin-left:75px;" class="tabs-style2" v-show="showExample">
                        <Button id="model_button_add_example" v-if="this.current_status !== 'description'" type="primary" style="margin-bottom: 20px;"  @click="add_example">新增example</Button>
                        <Table id='table_1' border ref="selection" :columns="this.current_status !== 'description'?spi_example_List:spi_example_List_display" :data="data_spi_example_list" @on-selection-change=""></Table>
                        <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                            <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                        </Tooltip> -->
                        
                </Col>
            </Row>
            <div slot="footer">
                <Button type="primary" id="model_button_add_example_ok" @click="submit">确定</Button>
                <Button type="ghost" id="model_button_add_example_cancle" @click="cancel">取消</Button>
            </div>
        </Modal>
        <Modal v-model="modal_Show_example" width="600" :closable="false" :mask-closable="false" style="margin-left:0" transfer>
            <p slot="header">
                <span  class="margin-right-10">新增example</span> 
            </p>
            <div style="padding-left:30px;">
                <Form ref="form_data_example"  :model="form_data_example" :rules="exampleValidate" :label-width="120" inline>
                    <FormItem  class="width-100-perc" label="名称：" prop="name">
                        <Input size="small" id="model_input_name2" style="width: 380px;" v-model="form_data_example.name" placeholder="请输入"></Input>
                    </FormItem>
                    <!-- <FormItem  class="width-100-perc" label="标题：" prop="title">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.title" placeholder="请输入"></Input>
                    </FormItem> -->
                    <FormItem  class="width-100-perc" label="描述：" prop="description" >
                        <Input type="textarea" id="model_input_des2" size="small" style="width: 380px;" v-model="form_data_example.description" placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem  class="width-100-perc" label="示例值：" prop="value">
                        <Input type="textarea" size="small"  id="model_input_example_val" :rows="9" style="width: 380px;" v-model="form_data_example.value" placeholder="请输入"></Input>
                    </FormItem>
                </Form>
            </div>
            <div slot="footer">
                <Button type="primary" id="model_button_add_example_ok2"  @click="ok_example">确定</Button>
                <Button type="ghost" id="model_button_add_example_cancle2"  @click="cancel_example">取消</Button>
            </div>
        </Modal>
        <ModelDetail ref="modelDetil" />
    </div>
</template>

<script>
import { nanoid } from 'nanoid'
import commonSelect from '../../common-components/select-components/selectCommon';
import label_icon from '../../common-components/icon-components/star_red'
import subcontent from './param_main_content_model'
import subcontentMap from './param_main_content_model_map'
import api from '../../../api/api'
import { setTimeout } from 'timers';
import AddEnum from '../../model/modify_models/AddEnum.vue'
import ModelDetail from '../../model/modify_models/ModelDetail.vue'
import TextEditor from '../../my-components/text-editor/text-editor-size';
export default {
  name: 'modal_param_content_model',
  components: {
    commonSelect,
    subcontent,
    subcontentMap,
    label_icon,
    AddEnum,
    ModelDetail,
    TextEditor
  },
  props: {
    data_spi_model_code: String
  },
  data () {
    const validate_number = (rule, value, callback) => {
      if (value === '' || value === undefined) {
        callback();
      } else
      if (isNaN(value * 1)) {
        callback(new Error('请输入数字'));
      } else if (this.form_data.maximum && this.form_data.minimum) {
        if (this.form_data.maximum * 1 < this.form_data.minimum * 1) {
          callback(new Error('最小值应不大于最大值'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validate_number_1 = (rule, value, callback) => {
      if (value === '' || value === undefined) {
        callback();
      } else
      if (!(/^[1-9]\d*$/.test(value * 1))) {
        callback(new Error('请输入正整数'));
      } else if (this.form_data.maxItems && this.form_data.minItems) {
        if (this.form_data.maxItems * 1 < this.form_data.minItems * 1) {
          callback(new Error('最小数目应不大于最大数目'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validate_number_2 = (rule, value, callback) => {
      if (rule.field === 'maxLength' && this.form_data.type[0] === 'string') {
        if (value === '' || value === undefined) {
          callback('最大长度必填');
          return
        }
      }
      if (value === '' || value === undefined) {
        callback();
      } else
      if (!(/^[1-9]\d*$/.test(value * 1))) {
        callback(new Error('请输入正整数'));
      } else if (this.form_data.maxLength && this.form_data.minLength) {
        if (this.form_data.maxLength * 1 < this.form_data.minLength * 1) {
          callback(new Error('最小长度应不大于最大长度'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validate_enum_2 = (rule, value, callback) => {
      if (this.form_data.type[1] != 'enum') {
        callback()
        return
      }
      if (value.length < 1) {
        callback(new Error('枚举值至少填写一个'))
      }
      if (value.length !== [...new Set(value)].length) {
        callback(new Error('枚举值参数不能重复'))
      }
      callback()
    }
    return {
      validate_api_name: '',
      form_data_right: {
        maximum: [
          {required: false, validator: validate_number, trigger: 'blur'}
        ],
        minimum: [
          {required: false, validator: validate_number, trigger: 'blur'}
        ],
        maxItems: [
          {required: false, validator: validate_number_1, trigger: 'blur'}
        ],
        minItems: [
          {required: false, validator: validate_number_1, trigger: 'blur'}
        ],
        default: [
          {required: false}
        ],
        param_name: [
          {required: false}
        ],
        exclusiveMaximum: [
          {required: false}
        ],
        exclusiveMinimum: [
          {required: false}
        ],
        maxLength: [
          {required: true, validator: validate_number_2, trigger: 'blur'}
        ],
        minLength: [
          {required: false, validator: validate_number_2, trigger: 'blur'}
        ],
        pattern: [
          {required: false}
        ],
        enum: [
          {required: false, validator: validate_enum_2, trigger: 'submit'}
        ]

      },
      if_edit_sensitive: false,
      current_status: '',
      data_api_model: '',
      data_api_model_list: [],
      form_data_left: {
        param: '', // 参数
        name: '', // 名称
        type: [], // 类型
        sample: '', // 示例值
        description: '', // 描述
        required: false, // 是否必填
        deprecated: false// 是否废弃
      },
      showOrder: false,
      // 表格内容数据绑定
      form_data: {
        // old
        // param: '', // 参数
        // name: '', // 名称
        // type: [], // 类型
        // sample: '', // 示例值
        // description: '', // 描述
        // required: false, // 是否必填

        // new
        // param: '', // 参数
        title: '', // 名称
        type: [], // 类型
        sample: '', // 示例值
        description: '', // 描述
        required: false, // 是否必填
        deprecated: false, // 是否废弃
        // new
        additionalProperties: {},
        items: {},
        internal: false, // 参数
        default: '', // 默认值
        sensitive: false, // 名称
        param_index: '', // 类型
        param_name: '', // 示例值
        maximum: '', // 最大值
        exclusiveMaximum: true, // 包含最大值
        minimum: '', // 最小值
        exclusiveMinimum: true, // 包含最小值，
        pattern: '', // 正则表达式
        maxLength: '', // 最大长度
        minLength: '', // 最小长度
        maxItems: '', // 最大数目
        minItems: '', // 最小数目
        orderNo: false, // 业务订单号
        model: '',
        constrains: '',
        enum: [],
        example: '',
        extensions: {
          'x-yop-api-param-condition': {
            conditionRequired: false,
            type: 'PLAIN',
            value: '' 
          }
        }
      },
      // 枚举数据绑定
      enum_data: [{
        value: '',
        index: 0,
        status: 1
      }],

      // 表格显示绑定
      form_data_visual: {
        extremum: false,
        enums: true,
        length: false,
        pattern: false,
        itemSize: false,
        model: false,
        additionalProperties: false,
        items: false,
        // order: true,
        'exclusiveMaximum': false,
        'minimum': false,
        'exclusiveMinimum': false,
        maxItems: false,
        minItems: false

      },
      current_panel: 'create_request_json',
      // 窗口是否展示
      modal_show: false,
      // 示例值绑定
      data_select_sample: '',
      // 常用正则表达式数据绑定
      data_select_pattern: '',

      enum_index: 0,
      // 是否展示更多属性 false 为隐藏 true 为显示
      more_attr: true,

      type_data: [],
      type_data_cut: [],
      type_data_map: [],
      sample_data: [
        {
          value: 'integer',
          desc: 'integer',
          formats: [
            {
              value: 'int32',
              desc: 'int32',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            },
            {
              value: 'int64',
              desc: 'int64',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            }
          ]
        },
        {
          value: 'number',
          desc: 'number',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            },
            {
              value: 'float',
              desc: 'float',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            },
            {
              value: 'double',
              desc: 'double',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            }
          ]
        },
        {
          value: 'boolean',
          desc: 'boolean',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [

              ]
            }
          ]
        },
        {
          value: 'string',
          desc: 'string',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [
                'length',
                'pattern'
              ]
            },
            {
              value: 'merchant-no',
              desc: 'merchant-no',
              defaulted: true
            },
            {
              value: 'binary',
              desc: 'binary',
              defaulted: true
            },
            {
              value: 'date',
              desc: 'date',
              defaulted: true
            },
            {
              value: 'date-time',
              desc: 'date-time',
              defaulted: true
            },
            {
              value: 'password',
              desc: 'password',
              defaulted: true
            },
            {
              value: 'email',
              desc: 'email',
              defaulted: true
            },
            {
              value: 'mobile',
              desc: 'mobile',
              defaulted: true
            },
            {
              value: 'idcard',
              desc: 'idcard',
              defaulted: true
            },
            {
              value: 'bankcard',
              desc: 'bankcard',
              defaulted: true
            },
            {
              value: 'cvv',
              desc: 'cvv',
              defaulted: true
            },
            {
              value: 'uuid',
              desc: 'uuid',
              defaulted: true
            },
            {
              value: 'notify-url',
              desc: 'notify-url',
              defaulted: true
            },
            {
              value: 'error-code',
              desc: 'error-code',
              defaulted: true
            },
            {
              value: 'error-msg',
              desc: 'error-msg',
              defaulted: true
            },
            {
              value: 'enum',
              desc: 'enum',
              defaulted: true,
              constrains: [
                'enums'
              ]
            },
            {
              value: 'byte',
              desc: 'byte',
              defaulted: true
            }
          ]
        },
        {
          value: 'array',
          desc: 'array',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [
                'items',
                'itemSize'
              ]
            }
          ]
        }
        // {
        //     value : 'object',
        //     desc : 'object',
        //     formats :[
        //         {
        //             value : '-',
        //             desc : '-',
        //             defaulted : true,
        //         },
        //         {
        //             value : 'map',
        //             desc : 'map',
        //             defaulted : true,
        //             constrains : [
        //                 'additionalProperties',
        //             ]
        //         }
        //     ]
        // },
      ],
      sample_data_nofile: [
        {
          value: 'integer',
          desc: 'integer',
          formats: [
            {
              value: 'int32',
              desc: 'int32',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            },
            {
              value: 'int64',
              desc: 'int64',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            }
          ]
        },
        {
          value: 'number',
          desc: 'number',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            },
            {
              value: 'float',
              desc: 'float',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            },
            {
              value: 'double',
              desc: 'double',
              defaulted: true,
              constrains: [
                'extremum'
              ]
            }
          ]
        },
        {
          value: 'boolean',
          desc: 'boolean',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [

              ]
            }
          ]
        },
        {
          value: 'string',
          desc: 'string',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [
                'length',
                'pattern'
              ]
            },
            {
              value: 'merchant-no',
              desc: 'merchant-no',
              defaulted: true
            },
            {
              value: 'date',
              desc: 'date',
              defaulted: true
            },
            {
              value: 'date-time',
              desc: 'date-time',
              defaulted: true
            },
            {
              value: 'password',
              desc: 'password',
              defaulted: true
            },
            {
              value: 'email',
              desc: 'email',
              defaulted: true
            },
            {
              value: 'mobile',
              desc: 'mobile',
              defaulted: true
            },
            {
              value: 'idcard',
              desc: 'idcard',
              defaulted: true
            },
            {
              value: 'bankcard',
              desc: 'bankcard',
              defaulted: true
            },
            {
              value: 'cvv',
              desc: 'cvv',
              defaulted: true
            },
            {
              value: 'uuid',
              desc: 'uuid',
              defaulted: true
            },
            {
              value: 'notify-url',
              desc: 'notify-url',
              defaulted: true
            },
            {
              value: 'error-code',
              desc: 'error-code',
              defaulted: true
            },
            {
              value: 'error-msg',
              desc: 'error-msg',
              defaulted: true
            },
            {
              value: 'enum',
              desc: 'enum',
              defaulted: true,
              constrains: [
                'enums'
              ]
            },
            {
              value: 'byte',
              desc: 'byte',
              defaulted: true
            }
          ]
        },
        {
          value: 'array',
          desc: 'array',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true,
              constrains: [
                'items',
                'itemSize'
              ]
            }
          ]
        },
        {
          value: 'object',
          desc: 'object',
          formats: [
            {
              value: '-',
              desc: '-',
              defaulted: true
            },
            {
              value: 'map',
              desc: 'map',
              defaulted: true,
              constrains: [
                'additionalProperties'
              ]
            }
          ]
        }
      ],
      // 当前编辑行数
      current_line_index: 0,
      apiGroupCode: '',
      search_model_apiGroup: '',
      subContent: {},
      model_current: '',
      data_spi_example_list: [
      ],
      spi_example_List: [
        {
          title: '名称',
          key: 'name',
          width: 160,
          align: 'center'
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     width:160,
        //     align:"center"

        // },
        {
          title: '描述',
          align: 'center',
          width: 260,
          key: 'description'
        },
        {
          title: '示例值',
          align: 'center',
          key: 'value'
        },
        {
          title: '操作',
          align: 'center',
          key: 'operations',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  // name: 'url',
                  // value: {url: '/rest/api/config/edit'}
                }],
                on: {
                  click: () => {
                    this.example_modify(params.row);
                  }
                }
              }, '修改'),
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  // name: 'url',
                  // value: {url: '/rest/api/config/edit-for-doc'}
                }],
                on: {
                  click: () => {
                    this.example_delete(params.row.index);
                  }
                }
              }, '删除')
            ]);
          }
        }
      ],
      spi_example_List_display: [
        {
          title: 'name',
          key: 'name',
          align: 'center'
        },
        {
          title: 'title',
          key: 'title',
          align: 'center'

        },
        {
          title: 'description',
          align: 'center',
          key: 'description'
        },
        {
          title: 'value',
          align: 'center',
          key: 'value'
        }
      ],
      // 参数
      exampleValidate: {
        name: [{required: true, message: '名称不能为空'}],
        // title:[{required:true,message:'标题不能为空'}],
        description: [{required: true, message: '描述不能为空'}],
        value: [{required: true, message: '示例值不能为空'}]
      },
      editExample: false,

      modal_Show_example: false,
      form_data_example: {
        name: '',
        // title:"",
        description: '',
        value: ''
      },
      showExample: true,
      edit_prop_list: [],
      underLine: false,
      id: ''
    };
  },
  created () {
    this.id = 'endt' + nanoid(5)
  },
  methods: {
    watchModelDetail () {
      if (!this.form_data.model) return
      const model = this.data_api_model_list.find(item => item.name === this.form_data.model)
      this.detailModel({
        ...model,
        apiGroup: this.apiGroupCode
      })
    },
    detailModel (row) {
      var id = row.id;
      var name = row.name;
      var apiGroup = row.apiGroup;
      var description = row.description;
      api.yop_modalManagement_modal_detail({id: id}).then(
        (response) => {
          var response = response.data
          if (response.status === 'success') {
            var schema = JSON.parse(response.data.result.schema)
            var bizOrderVariable = response.data.result.bizOrderVariable
            var sensitiveVariables = response.data.result.sensitiveVariables
            this.propertiesDetails = schema.properties;
            var arr = []
            for (let i in this.propertiesDetails) {
              let o = {};
              o['name'] = i;
              for (var k in schema.required) {
                var requiredName = schema.required[k]
                if (requiredName === i) {
                  o['required'] = true
                }
              }
              for (var j in this.propertiesDetails[i]) {
                o[j] = this.propertiesDetails[i][j]
                if (j == 'type') {
                  o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
                } else if (j == '$ref') {
                  o['type'] = ['object', 'object']
                } else if (j == 'additionalProperties') {
                  o['type'] = ['object', 'map']
                }
              }
              for (var k in o) {
                if (k == 'format') {
                  o['type'][1] = o[k]
                }
              }
              arr.push(o)
            }
            var detailData = {
              type: schema.type,
              title: schema.title,
              name: name,
              apiGroup: apiGroup,
              description: description,
              data_propertiesList: arr,
              extensions: schema.extensions,
            }
            this.$refs.modelDetil.form_data_set(detailData, bizOrderVariable, sensitiveVariables, 'detail');
            this.$refs.modelDetil.show();
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      );
    },
    onExampleChange () {
      if (this.form_data.default) {
        this.form_data.default = this.form_data.example
      }
    },
    onDefaultChange (value) {
      if (value) {
        this.$set(this.form_data, 'default', this.form_data.example)
      } else {
        this.form_data.default = ''
      }
    },
    setEnumAndDesc () {
      const { description } = this.form_data
      if (description) {
        const descList = description.split('可选项如下:\n')
        this.form_data.description = descList[0] || ' '
        const enumParams = {
          enum: this.form_data.enum || [],
          default: this.form_data.default || '',
          description: descList[1] || ''
        }
        this.$refs.textEditor.setContent(this.form_data.description)
        this.$refs.addEnum.setData(enumParams)
      } else {
        this.$refs.textEditor.setContent(' ')
        this.$refs.addEnum.setData({
          default: null,
          description: ''
        })
      }
    },
    saveEnumAndDesc () {
      if (this.form_data.type[1] !== 'object') {
        this.form_data.description = this.$refs.textEditor.getContent()
      }
      if (this.form_data.type[1] != 'enum') return
      const enumParams = this.$refs.addEnum.getData()
      this.form_data.enum = enumParams.enum
      this.form_data.default = enumParams.default
      this.form_data.example = enumParams.default
      this.form_data.description = this.form_data.description + enumParams.description
    },
    // 查询model列表
    getModelsList (param) {
      api.yop_modalManagement_modal_simple_list(param).then(
        (response) => {
          var response = response.data
          this.data_api_model_list = [];
          let apigroup = response.data.result;
          // this.data_api_model= apigroup[0].groupCode;
          for (var i in apigroup) {
            this.data_api_model_list.push(
              {
                label: apigroup[i].description + '(' + apigroup[i].name + ')',
                value: apigroup[i].name,
                ...apigroup[i]
              }
            )
          }
          if (this.model_current) {
            this.form_data.model = this.model_current
          } else {
            this.$refs.modal_select_4.clearSingleSelect();
          }
        }
      );
    },
    setModel (val) {
      this.form_data.model = val
    },
    // 重置select
    resetSelect () {
      // this.$refs.modal_apiM_select_3.clearSingleSelect()
      // this.$refs.modal_apiM_select_4.clearSingleSelect()
      // this.$refs.select_common_default.resetSelected();
      this.$refs.select_common_pattern.resetSelected();
      this.enum_data = [{
        value: '',
        index: 0,
        status: 1
      }]
      this.enum_index = 0
    },
    // 当前页面设置
    current_panel_set (val) {
      this.current_panel = val || localStorage.apiInfo;
    },
    resetRef () {
      delete this.form_data.$ref
    },
    setApiGroup (val) {
      this.apiGroupCode = val
    },
    compatible (underLine) {
      this.underLine = underLine
      if (this.underLine) {
        this.validate_api_name = (rule, value, callback) => {
          // var reg = /^[a-z]+((\d)|([A-Z0-9][a-z0-9]+))*([A-Z])?$/

          var reg = /^([a-z][a-zA-Z0-9]*)(_[a-z][a-zA-Z0-9]*)*$/;
          console.log(reg, 'caozuozhihou1111')
          if (!reg.test(value)) {
            callback(new Error('小写驼峰允许下划线'));
          } else {
            var tempArr = []
            if (this.current_status == 'modify') {
              tempArr = this.edit_prop_list
            } else {
              tempArr = this.data_propertiesList
            }
            for (var i in tempArr) {
              var tempI = tempArr[i]
              if (tempI.name === value) {
                callback(new Error('参数不允许重复'));
              }
            }
            callback();
          }
        }
      } else {
        this.validate_api_name = (rule, value, callback) => {
          // var reg = /^[a-z]+((\d)|([A-Z0-9][a-z0-9]+))*([A-Z])?$/
          var reg = /^[a-z][a-z_A-Z0-9]*$/;
          console.log(reg, '2caozuozhihou2222')
          if (!reg.test(value)) {
            callback(new Error('只允许小写驼峰格式'));
          } else {
            var tempArr = []
            if (this.current_status == 'modify') {
              tempArr = this.edit_prop_list
            } else {
              tempArr = this.data_propertiesList
            }
            for (var i in tempArr) {
              var tempI = tempArr[i]
              if (tempI.name === value) {
                callback(new Error('参数不允许重复'));
              }
            }
            callback();
          }
        }
      }
    },
    // 页面初始化
    init () {
      this.validate_api_name = (rule, value, callback) => {
        // var reg = /^[a-z]+((\d)|([A-Z0-9][a-z0-9]+))*([A-Z])?$/
        var reg = /^[a-z][a-z_A-Z0-9]*$/;
        console.log('操作后的校验', reg)
        if (!reg.test(value)) {
          callback(new Error('只允许小写驼峰格式'));
        } else {
          var tempArr = []
          if (this.current_status == 'modify') {
            tempArr = this.edit_prop_list
          } else {
            tempArr = this.data_propertiesList
          }
          for (var i in tempArr) {
            var tempI = tempArr[i]
            if (tempI.name === value) {
              callback(new Error('参数不允许重复'));
            }
          }
          callback();
        }
      }
      this.data_format(this.sample_data);
      this.cascader_format();
    },
    init_nofile () {
      this.data_format(this.sample_data_nofile);
      this.cascader_format();
    },
    checkNotifyUrl (cb) {
      if (this.form_data.type[1] === 'notify-url') {
        cb()
        return
      }
      if (/.*NotifyUrl$/.test(this.form_data.name) || this.form_data.name === 'notifyUrl') {
        this.$Modal.confirm({
          title: '友情提醒',
          content: '此参数是否为结果通知地址？',
          'ok-text': '是',
          'cancel-text': '否',
          onOk: () => {
            this.form_data.type = ['string', 'notify-url']
            cb()
          },
          onCancel: () => {
            cb()
          }
        });
      } else {
        cb()
      }
    },
    // 表单提交
    submit () {
      this.saveEnumAndDesc()
      this.checkNotifyUrl(() => {
        this.form_submit_validate('form_data_left', 'form_data_right')
      })
    },
    // 继续验证
    next_validate () {
      this.form_data.examples = this.data_spi_example_list// 将示例添加进去
      if (this.form_data_visual.items) {
        this.$refs['subContent_array'].submit();
      } else {
        console.log(this.form_data, '<--编辑后form_data')
        this.$emit('data_update', JSON.parse(JSON.stringify(this.form_data)), this.current_panel, this.current_line_index);
        // 新建或者编辑操作执行
      }
    },
    isJson (str) {
      if (typeof str == 'string') {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
            return false;
          }
        } catch (e) {
          console.log('error：' + str + '!!!' + e);
          return false;
        }
      }
      console.log('It is not a string!')
    },
    // 示例
    add_example () {
      this.$refs.form_data_example.resetFields()
      this.modal_Show_example = true;
      this.editExample = false
    },
    example_modify (row) {
      this.editExample = true
      var name = row.name;
      var description = row.description;
      var value = row.value;
      this.form_data_example.name = name;
      // this.form_data_example.title = title;
      this.form_data_example.description = description;
      this.form_data_example.value = value;
      this.row_index = row._index;
      this.modal_Show_example = true;
    },
    ok_example () {
      this.$refs.form_data_example.validate((valid) => {
        if (valid) {
          var example = {
            name: this.form_data_example.name,
            // title:this.form_data_example.title,
            description: this.form_data_example.description,
            value: this.form_data_example.value
          }
          if (!this.editExample) {
            this.data_spi_example_list.push(example)
          } else {
            this.data_spi_example_list.splice(this.row_index, 1, example)
          }
          this.modal_Show_example = false;
        } else {
          this.form_validate_failed()
        }
      })
    },
    cancel_example () {
      this.modal_Show_example = false;
    },
    example_delete (index) {
      this.$Modal.confirm({
        title: '提示',
        content: '确认删除示例么？',
        'ok-text': '确认',
        onOk: () => {
          this.data_spi_example_list.splice(index, 1);
        }
      });
    },

    getSubContent (getSubContent, type) {
      var getSubContent = this.format_subContent(getSubContent);
      // if(type == "map"){
      //     this.form_data.items={}
      //     this.form_data.additionalProperties = getSubContent
      // }else{
      //     this.form_data.items = getSubContent
      //     this.form_data.additionalProperties={}
      // }
      this.$emit('data_update', JSON.parse(JSON.stringify(this.form_data)), this.current_panel, this.current_line_index, JSON.parse(JSON.stringify(getSubContent)), type);
    },
    format_subContent (getSubContent) {
      var getSubContent = JSON.parse(JSON.stringify(getSubContent))
      for (var i in getSubContent) {
        // if(i == "model"){
        //     this.form_data.model = getSubContent[i]
        // }
        if (!getSubContent[i]) {
          delete getSubContent[i]
        }
      }
      return getSubContent
    },
    // 表单提交验证
    form_submit_validate (val1, val2) {
      let result1 = 0;
      let result2 = 0;
      this.$refs[val1].validate((valid) => {
        if (valid) {
          result1 = 1;
          if (result2 === 1) {
            this.next_validate();
          }
        } else {
          this.form_validate_failed(result2)
        }
      })
      this.$refs[val2].validate((valid) => {
        if (valid) {
          result2 = 1;
          if (result1 === 1) {
            this.next_validate();
          }
        } else {
          this.form_validate_failed(result1)
        }
      })
    },
    // 表单验证失败
    form_validate_failed (result) {
      this.$Message.error('请检查');
    },
    // 表单取消
    cancel () {
      this.modal_show = false;
    },
    showOrderFunc (data_propertiesList, data, model, showOrder) {
      if (showOrder) {
        if (model == 'create') {
          if (data_propertiesList.length > 0) {
            var orderList = [];
            for (var i = 0; i < data_propertiesList.length; i++) {
              var data_propertiesListI = data_propertiesList[i]
              for (var j in data_propertiesListI) {
                if (j == 'orderNo' && data_propertiesListI[j]) {
                  orderList.push(data_propertiesListI[j])
                }
              }
            }
            orderList.filter(d => d);
            if (orderList.length > 0) {
              this.showOrder = false
            } else {
              this.showOrder = true
            }
          } else {
            this.showOrder = true
          }
        } else {
          // 编辑
          if (data.orderNo) {
            this.showOrder = true
          } else {
            if (data_propertiesList.length > 0) {
              var orderList = [];
              for (var i = 0; i < data_propertiesList.length; i++) {
                var data_propertiesListI = data_propertiesList[i]
                for (var j in data_propertiesListI) {
                  if (j == 'orderNo' && data_propertiesListI[j]) {
                    orderList.push(data_propertiesListI[j])
                  }
                }
              }
              orderList.filter(d => d);
              if (orderList.length > 0) {
                this.showOrder = false
              } else {
                this.showOrder = true
              }
            } else {
              this.showOrder = true
            }
          }
        }
      } else {
        this.showOrder = false
      }
    },
    // 窗口显示
    show (data_propertiesList, model, showOrder) {
      this.data_propertiesList = data_propertiesList
      this.current_status = model
      if (model == 'create') {
        this.showOrderFunc(data_propertiesList, '', model, showOrder);
      }
      this.setEnumAndDesc()
      this.modal_show = true;
    },
    setBodyType (val) {
      this.showExample = val
    },
    // 数据初始化
    form_data_init () {

    },
    // 数据处理
    data_format (array) {
      this.type_data = [];
      this.type_data_cut = [];
      this.type_data_map = [
        {
          value: '$ref',
          label: '$ref',
          children: [{
            value: '$ref',
            label: '$ref',
            constrains: []
          }]
        }
      ];

      for (var i in array) {
        let subContent = array[i].formats;
        let childrenTemp = []
        for (var j in subContent) {
          if (subContent[j].value === '-') {
            childrenTemp.push(
              {
                value: array[i].value,
                label: array[i].desc,
                constrains: subContent[j].constrains ? subContent[j].constrains : []
              }
            );
          } else {
            childrenTemp.push(
              {
                value: subContent[j].value,
                label: subContent[j].desc,
                constrains: subContent[j].constrains ? subContent[j].constrains : []
              }
            );
          }
        }
        this.type_data_cut.push(
          {
            value: array[i].value,
            label: array[i].desc,
            children: childrenTemp
          }
        )
        this.type_data_map.push(
          {
            value: array[i].value,
            label: array[i].desc,
            children: childrenTemp
          }
        )
        this.type_data.push(
          {
            value: array[i].value,
            label: array[i].desc,
            children: childrenTemp
          }
        )
      }
    },
    // 级联 参数类型 数据获取及处理
    cascader_format () {

    },
    // 常用默认值 更新
    updateSelect_common_default (val) {
      this.data_select_sample = val;
      this.form_data.default = val;
    },
    // 常用正则表达式 更新
    updateSelect_common_pattern (val) {
      this.data_select_pattern = val;
      this.form_data.pattern = val;
    },
    //
    updateSelect_model (val) {

    },
    // 加载完成回调函数
    select_callBack () {

    },
    // 枚举删除函数
    enumRemove (index) {
      this.enum_data[index].status = 0;
    },
    // 枚举改变函数
    enum_change (index) {
      if (this.enum_data[index].value !== '') {
        if (index === this.enum_index) {
          this.enum_index++;
          this.enum_data.push({
            value: '',
            index: this.enum_index,
            status: 1
          })
        }
      }
    },
    // 判断枚举值是否为最后一个
    enum_last_check (index) {
      if (index === this.enum_index) {
        return true;
      } else {
        for (var i = index; i < this.enum_index; i++) {
          if (this.enum_data[i].status === 1) {
            return false
          }
        }
        return true
      }
    },
    // 枚举值统计
    enum_result () {
      let result = [];
      for (var i in this.enum_data) {
        if (this.enum_data[i].status === 1 && this.enum_data[i].value !== '') {
          result.push(this.enum_data[i].value);
        }
      }
      return result;
    },
    set_showOrder (value) {
      this.contentType_order = value
    },
    // 类型选定 界面展示
    cascader_change (value, data) {
      this.form_data.type = JSON.parse(JSON.stringify(value));
      if (value[1] == 'object') {
        var param = {apiGroup: this.search_model_apiGroup || this.apiGroupCode}
        this.model_current = ''
        this.getModelsList(param)
      } else {
        this.form_data.model = '';
      }
      this.form_data.orderNo = false
      if (value[1] == '$ref') {
        var self = this
        setTimeout(() => {
          self.$refs.subContent_array.$refs.form_data_left2.resetFields();
          self.$refs.subContent_array.$refs.form_data_right2.resetFields();
          self.$refs.subContent_map.$refs.form_data_right2.resetFields();
          self.$refs.subContent_map.$refs.form_data_left2.resetFields();
          self.$refs.subContent_map.$refs.form_data_right2.data_select_sample = ''
          self.$refs.subContent_array.$refs.form_data_left2.data_select_sample = ''
          self.$refs.subContent_array.$refs.modal_apiM_select_5.clearSingleSelect();
          self.$refs.subContent_map.$refs.modal_apiM_select_5.clearSingleSelect();
        }, 500);
      }
      if (value[1] == 'map' || value[1] == 'array') {
        this.apiGroupCode = this.search_model_apiGroup || this.apiGroupCode;
      }
      this.type_rule_init();
      this.$refs.form_data_right.resetFields();
      this.resetSelect()
      // this.more_attr = false;
      if (value && value.length > 0) {
        let visual_items = data[1].constrains
        this.form_data.constrains = data[1].constrains

        if (visual_items && visual_items.length > 0) {
          for (var i in visual_items) {
            this.form_data_visual[visual_items[i]] = true;
          }
        }
      }
      if (value[0] == 'string' && (this.contentType_order == 'application/x-www-form-urlencoded' || this.contentType_order == 'multipart/form-data')) {
        if (!this.showOrder) {
          if (this.data_propertiesList.length > 0) {
            var orderList = [];
            for (var i = 0; i < this.data_propertiesList.length; i++) {
              var data_propertiesListI = this.data_propertiesList[i]
              for (var j in data_propertiesListI) {
                if (j == 'orderNo' && data_propertiesListI[j]) {
                  orderList.push(data_propertiesListI[j])
                }
              }
            }
            orderList.filter(d => d);
            if (orderList.length > 0) {
              this.showOrder = false
            } else {
              this.showOrder = true
            }
          } else {
            this.showOrder = true
          }
        } else {
          this.showOrder = true
        }
      } else {
        this.showOrder = false
      }
      if (value[1] == 'password' || value[1] == 'email' || value[1] == 'mobile' || value[1] == 'idcard' || value[1] == 'bankcard' || value[1] == 'cvv') {
        this.if_edit_sensitive = this.form_data.sensitive = true;
      } else {
        this.if_edit_sensitive = false;
      }
    },
    // 所有校验规则初始化
    type_rule_init () {
      for (var i in this.form_data_visual) {
        this.form_data_visual[i] = false;
      }
      this.if_edit_sensitive = false
    },
    // 展开按钮点击
    attr_expand  () {
      this.more_attr = true;
    },
    // 隐藏按钮点击
    attr_hide () {
      this.more_attr = false;
    },
    // 设置当前页面数据
    form_data_set (data, data_propertiesList, showOrder) {
      console.log(data, '<--当前选择的数据')
      this.data_propertiesList = data_propertiesList
      this.edit_prop_list = []
      for (var i in data_propertiesList) {
        if (i == data._index) {
          continue
        }
        this.edit_prop_list.push(data_propertiesList[i])
      }
      var _self = this
      _self.form_data = JSON.parse(JSON.stringify(data));
      if (_self.form_data.examples && _self.form_data.examples.length >= 0) {
        this.data_spi_example_list = _self.form_data.examples
      }
      this.model_current = ''
      if (!_self.form_data.extensions) {
        _self.$set(_self.form_data, 'extensions', {
          'x-yop-api-param-condition': {
            conditionRequired: false,
            type: 'PLAIN',
            value: '' 
          }
        })
      }
      if (_self.form_data.required === undefined) {
        _self.$set(_self.form_data, 'required', false)
      }
      if (_self.form_data.maxLength === undefined) {
        _self.$set(_self.form_data, 'maxLength', '')
      }
      if (data.type[0] != 'string') {
        showOrder = false
      }
      this.showOrderFunc(data_propertiesList, data, 'modify', showOrder);// 业务订单号
      if (data.$ref && data.type[1] != 'object') {
        data.$ref = ''
        this.form_data.$ref = ''
      }
      if (data.type[0] == 'integer') {
        this.form_data.constrains = data.constrains = ['extremum'];
      } else if (data.type[0] == 'number') {
        this.form_data.constrains = data.constrains = ['extremum']
      } else if (data.type[0] == 'boolean') {
        this.form_data.constrains = data.constrains = []
      } else if (data.type[1] == 'string') {
        this.form_data.constrains = data.constrains = ['length', 'pattern']
        if(this.form_data.format) {
          delete this.form_data.format
        }
      } else if (data.type[1] == 'array') {
        this.form_data.constrains = data.constrains = ['items', 'itemSize']
        this.form_data_visual['items'] = true
        this.form_data_visual['additionalProperties'] = false
      } else if (data.type[1] == 'map') {
        this.form_data.constrains = data.constrains = ['additionalProperties']
        this.form_data_visual['additionalProperties'] = true
        this.form_data_visual['items'] = false
      } else if (data.type[1] == 'object') {
        // this.form_data.constrains = data.constrains = ["$ref"]
        this.model_current = data.model || data.$ref.split('/').pop();
        var param = {apiGroup: this.apiGroupCode}
        this.getModelsList(param)
      }
      if (data.type[1] != 'array' && data.type[1] != 'map') {
        this.form_data_visual.additionalProperties = false
        this.form_data_visual.item = false
      }
      this.type_rule_init();
      if (data.type[1] == 'password' || data.type[1] == 'email' || data.type[1] == 'mobile' || data.type[1] == 'idcard' || data.type[1] == 'bankcard' || data.type[1] == 'cvv') {
        this.if_edit_sensitive = true;
      } else {
        this.if_edit_sensitive = false;
      }
      if (data.constrains && data.constrains.length > 0) {
        let visual_items = data.constrains
        if (visual_items && visual_items.length > 0) {
          for (var i in visual_items) {
            this.form_data_visual[visual_items[i]] = true;
          }
          var self = this
          this.$nextTick(() => {
            if (data.items && JSON.stringify(data.items) != '{}') {
              self.$refs.subContent_array.form_data_set(data)
            }
            if (data.additionalProperties && JSON.stringify(data.additionalProperties) != '{}') {
              self.$refs.subContent_map.form_data_set(data);
            }
          })
        }
      }
    },
    // 设置当前行数
    current_index_set (index) {
      this.current_line_index = index
    }
  },
  mounted () {
    this.init();
  }
};
</script>

<style scoped>

</style>
