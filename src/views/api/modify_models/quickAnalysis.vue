<style lang="less">
@import '../../../styles/common.less';
@import '../api_Mangement_Open.less';
@import '../../regression-test/regression.less';
.param-request td.ivu-table-expanded-cell {
  padding: 10px 10px !important;
  background: #fff;
}
.param-request {
  .width-100-perc {
    .ivu-form-item-error-tip {
      padding-top: 0;
    }
    .ivu-form-item-error-tip:nth-child(3) {
      padding-top: 6px;
    }
  }
}
</style>
<template>
  <Row class-name="param-request">
    <Card dis-hover>
      <Row>
        <Form
          ref="formData"
          :model="formData"
          :label-width="150"
        >
          <Form-item
            label="创建方式："
            prop="createType"
            :rules="{required:true, message:'请选择创建方式'}"
          >
            <Select v-model="formData.createType" placeholder="请选择创建方式" @on-change="onCreateTypeChange">
              <Option value="java">从已有JAVA接口创建</Option>
              <Option value="manual">手工创建</Option>
              <Option value="data-scope">data-scope</Option>
            </Select>
          </Form-item>
          <template v-if="formData.createType === 'java'">
            <Form-item
              label="接口环境："
            >
              <Radio-group v-model="env">
                <Radio v-for="(item, index) in envList" :label="item.name" :key="index">{{item.title}}</Radio>
              </Radio-group>
            </Form-item>
            <FormItem
              class="width-100-perc"
              label="应用名称："
              prop="appName"
              :rules="{required:true, message:'请输入应用名称'}"
            >
              <el-input
                v-show="this.current_status !== 'description'"
                id="input-appName"
                size="small"
                style="width: 100%"
                v-model="formData.appName"
                placeholder="请输入"
                @change="changeAppName"
              ></el-input>
              <span>容器云上的应用名，eg:test-hessian</span>
              <!--              <Input-->
              <!--                v-show="this.current_status !== 'description'"-->
              <!--                id="input-appName"-->
              <!--                size="small"-->
              <!--                style="width: 100%"-->
              <!--                v-model="formData.appName"-->
              <!--                placeholder="请输入"-->
              <!--              ></Input>-->
              <p v-show="this.current_status === 'description'">{{formData.appName}}</p>
            </FormItem>
            <FormItem
              class="width-100-perc"
              label="端点类名："
              prop="className"
              :rules="{required:true, message:'请选择端点类名'}"
            >
              <el-autocomplete
                key="formData.className"
                size="small"
                style="width: 100%"
                type="input"
                v-model="formData.className"
                :fetch-suggestions="getFacadeList"
                clearable
                placeholder="请选择端点方法"
                @input="changeClassName"
              >
                <template slot-scope="{ item }">
                  <div v-for="(text, index) in item.value.split(',')" :key="index">
                    {{ text }}
                  </div>
                </template>
              </el-autocomplete>
              <span>请确保进行了流水线构建以拉取到应用下的所有接口</span>
              <p v-show="this.current_status === 'description'">{{formData.className}}</p>
            </FormItem>
            <FormItem
              class="width-100-perc"
              label="端点方法："
              prop="method"
              :rules="{required:true, message:'请选择端点方法'}"
            >
              <el-autocomplete
                key="formData.method"
                size="small"
                style="width: 100%"
                type="input"
                v-model="formData.method"
                clearable
                :fetch-suggestions="getFacadeDesc"
                placeholder="请选择端点方法"
              >
                <template slot-scope="{ item }">
                  <div v-for="(text, index) in item.value.split(',')" :key="index">
                    {{ text }}
                  </div>
                </template>
              </el-autocomplete>
              <p v-show="this.current_status === 'description'">{{formData.method}}</p>
            </FormItem>
          </template>
          <template v-if="formData.createType === 'data-scope'">
            <FormItem
              class="width-100-perc"
              label="应用名称："
              prop="appName"
            >
              <el-input
                v-show="this.current_status !== 'description'"
                id="input-appName-datascope"
                size="small"
                style="width: 100%"
                v-model="formData.appName"
                disabled
                placeholder="data-scope"
              ></el-input>
              <p v-show="this.current_status === 'description'">{{formData.appName}}</p>
            </FormItem>
            <FormItem
              class="width-100-perc"
              label="端点类名："
              prop="className"
            >
              <el-input
                v-show="this.current_status !== 'description'"
                id="input-className-datascope"
                size="small"
                style="width: 100%"
                v-model="formData.className"
                disabled
                placeholder="com.datascope.facade.IntegrationQueryFacade"
              ></el-input>
              <p v-show="this.current_status === 'description'">{{formData.className}}</p>
            </FormItem>
            <FormItem
              class="width-100-perc"
              label="端点方法："
              prop="method"
            >
              <el-input
                v-show="this.current_status !== 'description'"
                id="input-method-datascope"
                size="small"
                style="width: 100%"
                v-model="formData.method"
                disabled
                placeholder="query(com.datascope.facade.dto.QueryParamDTO)"
              ></el-input>
              <p v-show="this.current_status === 'description'">{{formData.method}}</p>
            </FormItem>
            <FormItem
              class="width-100-perc"
              label="数据源："
              prop="datasourceId"
              :rules="{required:true, message:'请选择数据源'}"
            >
              <Select v-model="formData.datasourceId" placeholder="请选择数据源" @on-change="onDatasourceChange" @on-open-change="onDatasourceOpenChange" :loading="datasourceLoading">
                <Option v-for="item in datasourceList" :value="item.datasourceId" :key="item.datasourceId">{{item.datasourceName}}</Option>
              </Select>
            </FormItem>
            <FormItem
              class="width-100-perc"
              label="暴露查询："
              prop="queryId"
              :rules="{required:true, message:'请选择暴露查询'}"
            >
              <Select v-model="formData.queryId" placeholder="请选择暴露查询" @on-change="onQueryChange" :loading="queryLoading" :disabled="!formData.datasourceId">
                <Option v-for="item in queryList" :value="item.id" :key="item.id">{{item.name}}</Option>
              </Select>
            </FormItem>
          </template>
        </Form>
      </Row>
    </Card>
  </Row>
</template>
<script>
import { Autocomplete } from 'element-ui'
import Api from '../../../api/api'

export default {
  components: {
    ElAutocomplete: Autocomplete
  },
  props: ['current_status'],
  data () {
    return {
      env: '',
      formData: {
        createType: 'java',
        createdByExitedInterface: 1,
        appName: '',
        className: '',
        method: '',
        datasourceId: '',
        queryId: ''
      },
      params: {},
      facadeList: '',
      envList: [],
      datasourceList: [],
      queryList: [],
      datasourceLoading: false,
      queryLoading: false
    }
  },
  mounted () {
    this.getEnvList()
  },
  methods: {
    onCreateTypeChange (value) {
      if (value === 'data-scope') {
        // 自动赋值
        this.formData.appName = 'data-scope'
        this.formData.className = 'com.datascope.facade.IntegrationQueryFacade'
        this.formData.method = 'query(com.datascope.facade.dto.QueryParamDTO)'
      } else if (value === 'java') {
        this.formData.createdByExitedInterface = 1
        this.formData.appName = ''
        this.formData.className = ''
        this.formData.method = ''
      } else if (value === 'manual') {
        this.formData.createdByExitedInterface = 0
        this.formData.appName = ''
        this.formData.className = ''
        this.formData.method = ''
      }
      // 清空data-scope相关字段
      this.formData.datasourceId = ''
      this.formData.queryId = ''
      this.queryList = []
      this.datasourceList = []
    },
    onDatasourceOpenChange (open) {
      // 数据源下拉框打开时获取数据源列表
      if (open && this.datasourceList.length === 0) {
        this.getDatasourceList()
      }
    },
    onDatasourceChange (value) {
      this.formData.queryId = ''
      this.queryList = []
      if (value) {
        this.getQueryList(value)
      }
    },
    onQueryChange (value) {
      if (value) {
        this.getSwaggerInfo(value)
      }
    },
    // 获取数据源列表
    async getDatasourceList () {
      this.datasourceLoading = true
      try {
        const response = await fetch('https://qaboss.yeepay.com/data-scope/api/external/usable-datasource-get', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            page: 1,
            size: 500
          })
        })
        const result = await response.json()
        if (result.success) {
          this.datasourceList = result.data || []
        } else {
          this.$Message.error('获取数据源列表失败：' + result.message)
        }
      } catch (error) {
        this.$Message.error('获取数据源列表失败：' + error.message)
      } finally {
        this.datasourceLoading = false
      }
    },
    // 获取暴露查询列表
    async getQueryList (datasourceId) {
      this.queryLoading = true
      try {
        const response = await fetch('https://qaboss.yeepay.com/data-scope/api/external/usable-query-get', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            loginName: 'jiangtao.su', // 这里应该从用户信息中获取
            datasourceId: datasourceId,
            page: 1,
            size: 500
          })
        })
        const result = await response.json()
        if (result.success) {
          this.queryList = result.data || []
        } else {
          this.$Message.error('获取暴露查询列表失败：' + result.message)
        }
      } catch (error) {
        this.$Message.error('获取暴露查询列表失败：' + error.message)
      } finally {
        this.queryLoading = false
      }
    },
    // 获取swagger信息并解析
    async getSwaggerInfo (queryId) {
      try {
        const response = await fetch(`https://qaboss.yeepay.com/data-scope/api/external/swagger-generate/${queryId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        const result = await response.json()
        if (result.success) {
          // 调用解析接口
          await this.analyzeSwagger(result.data)
        } else {
          this.$Message.error('获取swagger信息失败：' + result.message)
        }
      } catch (error) {
        this.$Message.error('获取swagger信息失败：' + error.message)
      }
    },
    // 解析swagger信息
    async analyzeSwagger (swaggerData) {
      try {
        const result = await Api.getFacadeDescData({ swagger: swaggerData })
        if (result.status === 'success') {
          this.params = result.data.result
          this.$Message.success('swagger信息解析成功')
        } else {
          this.$Message.error('swagger信息解析失败：' + result.message)
        }
      } catch (error) {
        this.$Message.error('swagger信息解析失败：' + error.message)
      }
    },
    changeAppName () {
      this.formData.className = ''
      this.formData.method = ''
    },
    changeClassName () {
      this.formData.method = ''
    },
    setData (data) {
      const { appName, className, method, createType } = data
      if (createType) {
        this.formData.createType = createType
      }
      if (appName) {
        this.formData.createdByExitedInterface = 1
        this.formData.appName = appName
        this.formData.className = className
        this.formData.method = method
      } else {
        this.formData.createdByExitedInterface = 1
        this.formData.appName = ''
        this.formData.className = ''
        this.formData.method = ''
      }
    },
    getEnvList () {
      Api.getManageEnvList()
        .then(res => {
          this.envList = (res.data && res.data.data && res.data.data.result) || []
          this.env = (this.envList && this.envList[0] && this.envList[0].name) || ''
        })
    },
    getFacadeList (queryString, cb) {
      if (!this.formData.appName) return
      Api.getFacadeList({
        appName: this.formData.appName,
        env: this.env
      })
        .then(res => {
          if (res.data.code === '200') {
            const list = res.data.data.facadeList.filter(item => item.indexOf(queryString) !== -1)
            cb(list.map(item => ({ value: item })))
          } else {
            cb()
            this.$Modal.error({
              title: '错误',
              content: res.data.message
            });
          }
        })
    },
    getFacadeDesc (queryString, cb) {
      if (!this.formData.appName) return
      Api.getFacadeDesc({
        appName: this.formData.appName,
        facadeName: this.formData.className,
        env: this.env
      })
        .then(res => {
          if (res.data.code === '200') {
            Api.getFacadeDescData({ swagger: res.data.data })
              .then(res1 => {
                if (res1.status === 'success') {
                  this.params = res1.data.result
                  console.log('methods => ' + res1.data.result.methods)
                  const list = Object.keys(res1.data.result.methods).filter(item => item.indexOf(queryString) !== -1)
                  cb(list.map(item => ({ value: item })))
                } else {
                  cb()
                  this.$Modal.error({
                    title: '错误',
                    content: res1.message
                  });
                }
              })
              .catch(() => {
                cb()
              })
          } else {
            cb()
            this.$Modal.error({
              title: '错误',
              content: res.data.message
            });
          }
        })
        .catch(() => {
          cb()
        })
    },
    getNestModel (data) {
      if (!Array.isArray(data)) {
        return []
      }
      return data.reduce((prev, curt) => {
        const childs = curt.nestModel.length ? this.getNestModel({...curt.nestModel, parent: curt.modelRef}) : []
        return [...prev, curt, ...childs]
      }, [])
    },
    saveCheck () {
      if (this.current_status !== 'create') {
        let emitData = {
          ...this.formData,
          createdByExitedInterface: this.formData.createType === 'java' ? true : false
        }
        this.$emit('passQuickAnalysis', emitData)
        return Promise.resolve(true)
      }
      return this.$refs.formData.validate(valid => {
        if (valid) {
          let emitData = {
            ...this.formData,
            createdByExitedInterface: this.formData.createType === 'java' ? true : false
          }
          if (this.formData.createType === 'java' && this.formData.createdByExitedInterface === 1) {
            const { responseModel, requestModel } = this.params.methods[this.formData.method]
            console.log(requestModel)
            const allResponseModel = [
              {
                modelRef: responseModel.modelRef,
                nestModel: [],
                schema: responseModel.schema,
                modelType: 'response'
              },
              ...responseModel.nestModel.map(item => ({modelType: 'response', ...item}))
            ]
            let allRequestModel = []
            if (requestModel && requestModel['application/json']) {
              allRequestModel = [
                {
                  modelRef: requestModel['application/json'].modelRef,
                  nestModel: [],
                  schema: requestModel['application/json'].schema,
                  modelType: 'request'
                },
                ...requestModel['application/json'].nestModel.map(item => ({modelType: 'request', ...item}))
              ]
            }
            let list = [...allRequestModel, ...allResponseModel]

            const child1 = this.getModelList(allRequestModel)
            const child2 = this.getModelList(allResponseModel)
            const list1 = this.getModelList(list.filter(item => item.modelType === 'request'))
            const list2 = this.getModelList(list.filter(item => item.modelType === 'response'))
            console.log('requestModel')
            console.log(requestModel)
            emitData = {
              ...this.formData,
              createdByExitedInterface: this.formData.createdByExitedInterface === 1,
              allRequestModelList: child1,
              allResponseModelList: child2,
              requestModelList: list1 || [],
              responseModelList: list2 || [],
              reqModelSchema: requestModel,
              resModelSchema: responseModel
            }
          } else if (this.formData.createType === 'data-scope' && this.params && this.params.methods) {
            // 处理data-scope的情况
            const methodNames = Object.keys(this.params.methods)
            if (methodNames.length === 0) {
              this.$Message.error('没有找到可用的方法')
              return false
            }
            const methodName = methodNames[0] // 使用第一个方法
            const { responseModel, requestModel } = this.params.methods[methodName]
            const allResponseModel = [
              {
                modelRef: responseModel.modelRef,
                nestModel: [],
                schema: responseModel.schema,
                modelType: 'response'
              },
              ...responseModel.nestModel.map(item => ({modelType: 'response', ...item}))
            ]
            let allRequestModel = []
            if (requestModel && requestModel['application/json']) {
              allRequestModel = [
                {
                  modelRef: requestModel['application/json'].modelRef,
                  nestModel: [],
                  schema: requestModel['application/json'].schema,
                  modelType: 'request'
                },
                ...requestModel['application/json'].nestModel.map(item => ({modelType: 'request', ...item}))
              ]
            }
            let list = [...allRequestModel, ...allResponseModel]
            const child1 = this.getModelList(allRequestModel)
            const child2 = this.getModelList(allResponseModel)
            const list1 = this.getModelList(list.filter(item => item.modelType === 'request'))
            const list2 = this.getModelList(list.filter(item => item.modelType === 'response'))
            
            emitData = {
              ...this.formData,
              createdByExitedInterface: true,
              allRequestModelList: child1,
              allResponseModelList: child2,
              requestModelList: list1 || [],
              responseModelList: list2 || [],
              reqModelSchema: requestModel,
              resModelSchema: responseModel,
              integrationId: this.formData.queryId // 添加integrationId
            }
          }
          this.$emit('passQuickAnalysis', emitData)
          return true
        }
        this.$Message.error('请检查快速解析参数是否填写完全');
        return false
      })
    },
    getModelList (list) {
      let keys = new Set(list.map(item => item.modelRef))
      return [...keys].map(item => {
        return {
          ...list.filter(child => child.modelRef === item)[0]
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.el-autocomplete-suggestion .el-scrollbar .el-scrollbar__view li> div{
  display: inline-block;
}
.ivu-form-item-error-tip {

}
</style>
