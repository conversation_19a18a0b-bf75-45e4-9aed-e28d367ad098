<style lang="less">
.panel-content {
  margin-top: 8px;
  word-break: break-word;
  padding-right: 10px;
}
.reasonType {
  vertical-align: top;
  display: inline-block;
  font-size: 14px;
  margin-top: 5px;
}
</style>
<template>
  <Row>
    <Card dis-hover>
      <Row class="margin-top-10">
        <Col span="24">
          <Table id='table_1' border ref="selection" :columns="columns_ModalInterfaceList" :data="data_ModalInterfaceList"></Table>
        </Col>
      </Row>
    </Card>
    <modal_param_content_model_external ref="modal_param_content_model_external" :index="current_index_external" @data_update="data_update_model_external" :modelList="responseList"></modal_param_content_model_external>
  </Row>
</template>

<script>
/* eslint-disable no-console, camelcase, eqeqeq, no-redeclare */
import loading from '../../my-components/loading/loading';
import modal_param_content_model_external from '../../model/modify_models/modal_param_content_model_external';

import util from '../../../libs/util'

import api from '../../../api/api';

export default {
  name: 'model',
  components: {
    loading,
    modal_param_content_model_external
  },
  props: {
    requestList: {
      required: false,
      default: []
    },
    responseList: {
      required: false,
      default: []
    },
    apiGroup: {
      required: true,
      default: ''
    },
    resModelSchema: {
      type: Object,
      default: () => {}
    },
    reqModelSchema: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      delete_modal_show: false,
      modal_Show: false,
      // modal表格
      columns_ModalInterfaceList: [
        {
          title: '模型名称',
          key: 'modelRef',
          minWidth: 240,
          align: 'left'
        },
        {
          title: '操作',
          key: 'option',
          width: 130,

          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.detail_model(params.row)
                  }
                }
              }, '详情'),
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.edit_model(params.row, params.index)
                  }
                }
              }, '编辑')
            ]);
          },
          align: 'center'
        }
      ],
      // 表格数据
      data_ModalInterfaceList: [],
      // pageTotal
      pageTotal: 0,
      pageSize: 10000,
      // pageNo
      pageNo: 1,

      // 模型名称
      data_interface_name: '',
      // 描述
      data_interface_description: '',
      // api分组下拉框数据绑定
      data_select_apiGroup: '',
      // api分组下拉框数据
      data_apiGroup_List: [],
      // 临时变量
      data_test: {
        // "AuthIdCardResultDTO": {
        //     "title": "响应结果",
        //     "type": "object",
        //     "properties": {
        //         "fee": {
        //             "title": "未命名",
        //             "type": "number",
        //             "format": "double",
        //             "x-yop-end-param-name": "fee"
        //         },
        //         "cost": {
        //             "title": "未命名",
        //             "type": "number",
        //             "format": "double",
        //             "x-yop-end-param-name": "cost"
        //         },
        //         "name": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "name"
        //         },
        //         "photo": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "photo"
        //         },
        //         "remark": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "remark"
        //         },
        //         "status": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "status"
        //         },
        //         "address": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "address"
        //         },
        //         "orderId": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "orderId"
        //         },
        //         "authType": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "authType"
        //         },
        //         "encryptMsg": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "encryptMsg"
        //         },
        //         "idCardNumber": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "idCardNumber"
        //         },
        //         "invokeRecords": {
        //             "title": "未命名",
        //             "type": "array",
        //             "items": {
        //                 "type": "string"
        //             },
        //             "x-yop-end-param-name": "invokeRecords"
        //         },
        //         "requestFlowId": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestFlowId"
        //         },
        //         "authInterfaceId": {
        //             "title": "未命名",
        //             "type": "integer",
        //             "format": "int32",
        //             "x-yop-end-param-name": "authInterfaceId"
        //         },
        //         "bottomInterface": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "bottomInterface"
        //         },
        //         "externalOrderId": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "externalOrderId"
        //         },
        //         "channelReturnMsg": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "channelReturnMsg"
        //         },
        //         "authInterfaceType": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "authInterfaceType"
        //         },
        //         "channelReturnCode": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "channelReturnCode"
        //         }
        //     },
        //     "description": "响应结果"
        // },
        // "RequestIdCardAuthDTO": {
        //     "title": "方法签名第0个参数",
        //     "type": "object",
        //     "properties": {
        //         "name": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "name"
        //         },
        //         "authType": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "authType"
        //         },
        //         "requestIP": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestIP"
        //         },
        //         "authMethod": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "authMethod"
        //         },
        //         "encryptMsg": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "encryptMsg"
        //         },
        //         "channelName": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "channelName"
        //         },
        //         "excludePhoto": {
        //             "title": "未命名",
        //             "type": "boolean",
        //             "x-yop-end-param-name": "excludePhoto"
        //         },
        //         "idCardNumber": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "idCardNumber"
        //         },
        //         "requestFlowId": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestFlowId"
        //         },
        //         "requestSystem": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestSystem"
        //         },
        //         "requestSystemId": {
        //             "title": "未命名",
        //             "type": "integer",
        //             "format": "int32",
        //             "x-yop-end-param-name": "requestSystemId"
        //         },
        //         "requestCustomerId": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestCustomerId"
        //         },
        //         "requestIdentification": {
        //             "title": "未命名",
        //             "type": "string",
        //             "x-yop-end-param-name": "requestIdentification"
        //         },
        //         "repositoryAvailableDays": {
        //             "title": "未命名",
        //             "type": "integer",
        //             "format": "int32",
        //             "x-yop-end-param-name": "repositoryAvailableDays"
        //         }
        //     },
        //     "description": "方法签名第0个参数，请自行修改arg0等参数的名字"
        // }
      },
      // json表格的数据
      data_model: [],
      // 当前行
      current_index: -1,
      // 外部当前行
      current_index_external: -1,
      // 当前展示的模型
      current_model: [],
      // 当前被编辑模型
      current_modify_model: [],
      current_params: {
        pageNo: 1,
        pageSize: 10000
      },
      propertiesDetails: {},
      nowId: '',
      nowVersion: '',
      cause: '',
      modelIndex: ''
    };
  },
  methods: {

    //   列表刷新
    pageRefresh (val) {
      this.current_params.pageNo = val;
      var param = {
        name: this.data_interface_name,
        apiGroup: this.data_select_apiGroup,
        description: this.data_interface_description,
        pageNo: val,
        pageSize: 10000

      }
      api.yop_modalManagement_modal_list(this.current_params).then(
        (response) => {
          var response = response.data
          if (response.status == 'success') {
            this.tabledataGet(response.data.page.items);
            this.pageNo = response.data.page.pageNo;
            if (response.data.page.items) {
              if (response.data.page.items.length < 10) {
                this.pageTotal = response.data.page.items.length;
              } else {
                this.pageTotal = NaN;
              }
            } else {
              this.pageTotal = NaN;
            }

            // this.show_loading_apiList = false ;
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      )
    },
    tabledataGet (item) {
      this.data_ModalInterfaceList = item
    },
    // 查询列表
    search_Interface () {
      let paramsTemp = {
        name: this.data_interface_name.trim(),
        description: this.data_interface_description,
        // apiType: this.data_select_apiType,
        apiGroup: this.data_select_apiGroup,
        // status: this.data_select_status,
        pageNo: 1,
        pageSize: 10000
      };
      if (this.data_interface_name === '') {
        paramsTemp['apiTitle'];
      }

      if (this.data_select_apiType === '') {
        delete paramsTemp['apiType'];
      }
      if (this.data_select_apiGroup === '') {
        delete paramsTemp['apiGroupCode'];
      }
      if (this.data_select_status === '') {
        delete paramsTemp['status'];
      }
      if (this.data_safety_request === '') {
        delete paramsTemp['securityReq'];
      }
      this.current_params = paramsTemp;
      // 查询modal列表
      api.yop_modalManagement_modal_list(paramsTemp).then(response => {
        var response = response.data
        this.tabledataGet(response.data.page.items);
        this.pageNo = response.data.page.pageNo;
        if (response.data.page.items) {
          if (response.data.page.items.length < 10) {
            this.pageTotal = response.data.page.items.length;
          } else {
            this.pageTotal = NaN;
          }
        } else {
          this.pageTotal = NaN;
        }
      });
    },
    // 重置函数
    reset_Interface () {
      this.$refs.select_apiM_1.clearSingleSelect();
      this.data_interface_description = '';
      this.data_interface_name = '';
      // this.data_select_apiType = '';
      this.data_select_apiGroup = '';
      // this.data_select_status = '';
      this.data_safety_request = '';
      this.current_status = {
        pageNo: 1,
        pageSize: 10000
      };
    },
    // 页面初始化
    init () {
      this.getModalList();
      // api下拉框列表
      api.yop_dashboard_apis_list().then(
        (response) => {
          this.data_apiGroup_List = [];
          if (this.current_status === 'create') {
            this.api_group_model = '';
          }
          let apigroup = response.data.data.result;
          // this.api_group_model= apigroup[0].groupCode;
          for (var i in apigroup) {
            this.data_apiGroup_List.push(
              {
                label: apigroup[i].apiGroupCode + '(' + apigroup[i].apiGroupName + ')',
                value: apigroup[i].apiGroupCode,
                name: apigroup[i].apiGroupName
              }
            )
          }
        }
      );
      // this.data_model = this.data_rehandler(this.data_test);
      // if(this.data_model && this.data_model.length > 0){
      //     this.current_model = [];
      //     this.current_model.push(this.data_model[0].param)
      // }
    },
    getModalList () {
      console.log(this.requestList)
      console.log(this.responseList)
      const list = [...this.requestList, ...this.responseList]
      if (this.requestList.length && this.responseList.length) {
        let keys = new Set(list.map(item => item.modelRef))
        let child = [...keys].map(item => {
          return {
            ...list.filter(child => child.modelRef === item)[0]
          }
        })
        this.tabledataGet(child)
      } else {
        this.tabledataGet(list)
      }
    },
    // 本地数据获取
    // setRequestData (data) {
    //     this.data_model =[] ;
    //     this.data_model = this.data_rehandler(data);
    //     if(this.data_model && this.data_model.length > 0){
    //         this.current_model = [];
    //         this.current_model.push(this.data_model[0].param)
    //     }
    // },
    // 设置当前查看model
    setCurrentModel (name) {
      this.current_model = [];
      this.current_model.push(name)
    },
    // model数据显示处理
    data_rehandler (data) {
      let result = []
      for (var i in data) {
        let temp = {
          param: i,
          type: []
        }
        for (var j in data[i]) {
          if (j === 'properties') {
            temp[j] = this.sub_data_rehandler(data[i][j]);
          } else if (util.swagger_properties[j] === 'type') {
            temp['type'][0] = data[i][j];
          } else {
            temp[util.swagger_properties[j]] = data[i][j];
          }
        }
        result.push(temp)
      }
      return result
    },
    // 子数据处理
    sub_data_rehandler (data) {
      let result = []
      for (var i in data) {
        let temp = {
          param: i,
          type: []
        }
        for (var j in data[i]) {
          if (util.swagger_properties[j] === 'type') {
            temp['type'][0] = data[i][j];
            if (data[i][j] === 'object') {
              if (data[i]['additionalProperties']) {
                temp['type'][1] = 'map';
                temp['children'] = this.data_rehandler(data[i]['additionalProperties'])
              }
            } else if (data[i][j] === 'array') {
              if (data[i]['items']) {
                temp['type'][1] = 'array';
                temp['children'] = this.data_rehandler(data[i]['items'])
              }
            }
          } else if (util.swagger_properties[j] === 'format') {
            temp['type'][1] = data[i][j];
          } else {
            temp[util.swagger_properties[j]] = data[i][j];
          }
        }
        result.push(temp);
      }
      return result;
    },
    // 新增model
    new_param (panel, external) {
      this.$refs[panel].current_panel_set('create_model');
      this.$refs[panel].$refs.form_data_top.resetFields();
      this.$refs[panel].$refs.form_data_left.resetFields();
      this.$refs[panel].$refs.form_data_right.resetFields();
      this.$refs[panel].form_data_set(false);
      this.current_index = -1;
      this.$refs[panel].show();
      this.$refs[panel].data_propertiesList = []
      if (external) {
        this.current_modify_model = data;
      }
    },
    // 删除model
    delete_model (index) {
      this.nowId = index
      this.delete_modal_show = true
      this.cause = ''
      // this.data_model.splice(index,1);
    },
    sure_Delete_model () {
      if (!this.cause) {
        this.$Modal.warning({
          title: '警告',
          content: '请输入原因'
        });
      } else {
        this.$Modal.confirm({
          title: '提示',
          content: '确认删除吗？',
          'ok-text': '确认',
          onOk: () => {
            var param = {
              id: this.nowId,
              cause: this.cause
            }
            api.yop_modalManagement_modal_delete(param).then(
              (response) => {
                if (response.status == 'success') {
                  this.$ypMsg.notice_success(this, '删除成功');
                  this.pageRefresh();
                  this.delete_modal_show = false;
                } else {
                  setTimeout(() => {
                    this.$Modal.warning({
                      title: '警告',
                      content: response.message
                    });
                  }, 1000);
                }
              }
            )
          }

        });
      }
    },
    hide_Delete_model () {
      this.delete_modal_show = false;
    },
    // 修改model
    edit_model (row, index) {
      this.modelIndex = index
      var name = row.modelRef;
      var schema = JSON.parse(row.schema)
      this.propertiesDetails = schema.properties;
      var requiredArr = schema.required
      var arr = []
      for (let i in this.propertiesDetails) {
        let o = {};
        o['name'] = i;
        for (var j in this.propertiesDetails[i]) {
          // 每个属性
          o[j] = this.propertiesDetails[i][j]
          if (j == 'type') {
            o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
          } else if (j == '$ref') {
            o['type'] = ['object', 'object']
          } else if (j == 'additionalProperties') {
            o['type'] = ['object', 'map']
          }
        }
        for (var k in o) {
          if (k == 'format') {
            o['type'][1] = o[k]
          }
        }
        arr.push(o);
      }
      if (requiredArr) {
        for (var i = 0; i < requiredArr.length; i++) {
          var requiredArrI = requiredArr[i]
          for (var j = 0; j < arr.length; j++) {
            if (requiredArrI == arr[j].name) {
              arr[j].required = true
            }
          }
        }
      }

      this.$refs.modal_param_content_model_external.current_panel_set('modify_model');
      console.log(this.apiGroup)
      var detailData = {
        type: schema.type,
        title: schema.title,
        name: name,
        description: '',
        apiGroup: this.apiGroup,
        data_propertiesList: arr
      }
      this.$refs.modal_param_content_model_external.form_data_set(detailData, '', '');
      // this.current_index = -1;
      let canEditName = {
        reqModelSchema: this.reqModelSchema['application/json'] ? this.reqModelSchema['application/json'].modelRef : '',
        resModelSchema: this.resModelSchema ? this.resModelSchema.modelRef : ''
      }
      canEditName = JSON.parse(JSON.stringify(canEditName))
      this.$refs.modal_param_content_model_external.show('local', canEditName);
    },
    // 编辑元素
    edit_param (index, data, current_data, panel) {
      this.$refs[panel].$refs.form_data_left.resetFields();
      this.$refs[panel].$refs.form_data_right.resetFields();
      this.$refs[panel].current_panel_set('modify_model');
      this.$refs[panel].current_index_set(index);
      this.$refs[panel].form_data_set(data);

      // form_data.apiGroup
      this.current_modify_model = current_data;
      this.current_index = -1;
      this.$refs[panel].show();
    },

    detail_model (row) {
      var name = row.modelRef;
      var schema = JSON.parse(row.schema)
      this.propertiesDetails = schema.properties;
      var arr = []
      for (let i in this.propertiesDetails) {
        let o = {};
        o['name'] = i;
        for (var k in schema.required) {
          var requiredName = schema.required[k]
          if (requiredName === i) {
            o['required'] = true
          }
        }
        for (var j in this.propertiesDetails[i]) {
          o[j] = this.propertiesDetails[i][j]
          if (j == 'type') {
            o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
          } else if (j == '$ref') {
            o['type'] = ['object', 'object']
          } else if (j == 'additionalProperties') {
            o['type'] = ['object', 'map']
          }
        }
        for (var k in o) {
          if (k == 'format') {
            o['type'][1] = o[k]
          }
        }
        arr.push(o)
      }
      this.$refs.modal_param_content_model_external.current_panel_set('detail_model');
      var detailData = {
        type: schema.type,
        title: schema.title,
        name: name,
        apiGroup: this.apiGroup,
        description: '',
        data_propertiesList: arr
      }
      this.$refs.modal_param_content_model_external.form_data_set(detailData, '', '', 'detail');
      // this.current_index = -1;
      this.$refs.modal_param_content_model_external.show('local');
    },
    delete_param_in_panel (index, params) {
      params.splice(index, 1);
    },
    // 更新model的数据
    data_update_model (data, type, index) {
      if (type === 'create_model') {
        // 现在为不包含子项的处理 需要在提交的时候2次数据规范
        this.current_modify_model.push(data)
      } else {
        for (var i in data) {
          this.current_modify_model[index][i] = data[i]
        }
      }
      this.$refs.modal_param_content_model.cancel();
    },
    // 更新model外部的数据
    data_update_model_external (data, type, index) {
      this.data_ModalInterfaceList[this.modelIndex] = {
        ...this.data_ModalInterfaceList[this.modelIndex],
        schema: data.schema,
        modelRef: data.name
      }
      this.$set(this.data_ModalInterfaceList, this.modelIndex, this.data_ModalInterfaceList[this.modelIndex])
      this.$refs.modal_param_content_model_external.cancel();
      this.$emit('data-model-update', {
        requestList: this.data_ModalInterfaceList.filter(item => item.modelType === 'request') || [],
        responseList: this.data_ModalInterfaceList.filter(item => item.modelType === 'response') || []
      })
    }

  },
  mounted () {
    if (this.$route.query.data_interface_name) {
      this.data_interface_name = this.$route.query.data_interface_name
    }
    this.init();
  }
};
</script>

<style scoped>
</style>
