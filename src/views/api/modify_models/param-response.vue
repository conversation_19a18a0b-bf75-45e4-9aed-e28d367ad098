<template>
    <Row class="param-res">
        <Card dis-hover>
            <Form ref="form_data"  :model="form_data" :label-width="150" inline >

                <!-- <Row>
                    <FormItem label="请求url：" class="width-100-perc" prop="requestUrl" :rules="{required:true,message:'请求url不能为空'}">
                        <Input  v-if="this.current_status !== 'description'"  size="small" type="text" v-model="form_data.requestUrl"  style="width:380px"></Input>
                        <p  v-if="this.current_status == 'description'">{{form_data.requestUrl}}</p>
                    </FormItem>
                </Row>
                <Row>
                    <FormItem label="请求方法：" class="width-100-perc" prop="httpMethod" :rules="{required:true,message:'请求方法不能为空'}">
                        <Input v-if="this.current_status !== 'description'"  size="small" type="text" v-model="form_data.httpMethod" disabled  style="width:380px"></Input>
                        <p  v-if="this.current_status == 'description'">{{form_data.httpMethod}}</p>
                    </FormItem>

                </Row> -->
                <Row>
                    <FormItem  class="width-100-perc"  label="返回HttpCode：" prop="HttpCode" :rules="{required:true,message:'HttpCode不能为空'}">
                        <Select id="selectReturnHttpCode" ref="select_HttpCode"  v-show="this.current_status !== 'description'"  size="small" v-model="form_data.HttpCode" style="width:380px" placeholder="请输入" @on-change="changeHttpCode">
                            <Option v-for="(item,index) in HttpCode_list" :value="item.value" :key="index">{{ item.label }}</Option>
                        </Select>
                                    <p  v-if="this.current_status == 'description'">{{form_data.HttpCode}}</p>

                    </FormItem>
                </Row>
                <Row v-show="showCode_200" >
                    <FormItem class="width-100-perc"  label="返回contentType：" prop="contentType" :rules="{required:this.showCode_200 ? true : false,message:'contentType不能为空'}" >
                        <Select id="selectReturnContentType" ref="select_contentType"  v-show="this.current_status !== 'description'"   size="small" v-model="form_data.contentType" style="width:380px" placeholder="请输入" @on-change="changeContentType">
                            <Option v-for="(item,index) in contentType_list" :value="item.value" :key="index">{{ item.label }}</Option>
                        </Select>
                        <p  v-if="this.current_status == 'description'">{{form_data.contentType}}</p>
                    </FormItem>
                </Row>
                <Row v-show="form_data.HttpCode != '302' && form_data.HttpCode">
                    <FormItem label="是否加密：" class="width-100-perc">
                        <Checkbox
                          v-model="form_data.encrypt"
                          :disabled="current_status == 'description' ? 'disabled' : false"
                        />
                    </FormItem>
                </Row>
                <Row v-show="showCode_200 && hasHttpCode && form_data.HttpCode">
                    <Col  v-show="showJson">
                        <FormItem  v-show="!modelDisabled" label="返回content：" class="width-50-perc" prop="model" :rules="{required:this.showJson && showCode_200 && !this.modelDisabled ? true : false, message:'content不能为空'}">
                            <div class="flex-box">
                              <Select id="selectReturnContent" v-show="this.current_status !== 'description'" ref="modal_apiM_select_4" size="small" v-model="form_data.model" style="width:380px" placeholder="请输入">
                                <Option v-for="item in data_api_model_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
                              </Select>
                              <Button v-show="this.current_status !== 'description'" class="edit-dto" @click="seeDto(form_data.model, 'edit')" type="primary">修改</Button>
                            </div>
                            
                            <p class="flex-con" v-if="this.current_status == 'description'">
                              {{form_data.model}}
                              <Button class="link-dto" v-show="!modelDisabled" @click="seeDto(form_data.model, 'detail')" type="primary">查看</Button>
                            </p>
                        </FormItem>
                        <FormItem
                          v-show="modelDisabled"
                          label="返回content："
                          class="width-100-perc flex-con"
                          prop="model"
                        >
                          <Input disabled style="width:380px" v-model='data_propertiesStr'></Input>
<!--                          <TableModelContent type="resSchema" ref="tableModelContent" />-->
                          <!-- <Button class="link-dto" @click="seeDto(form_data.model)" type="primary">查看2</Button> -->
                        </FormItem>

                        <p style="font-size: 12px;padding-left: 79px;line-height:30px;">example：</p>

                        <FormItem class="width-100-perc"  label="">
                            <Button id="btnReturnExample" v-if="this.current_status !== 'description'" type="primary" style="margin-bottom: 20px;"  @click="add_example">新增example</Button>
                            <Table id='tableResExample' border ref="selection2" :columns="this.current_status !== 'description'?spi_example_List:spi_example_List_display" :data="data_spi_example_List"></Table>

                        </FormItem>
                    </Col>
                    <FormItem v-show="!showJson" class="width-100-perc"  label="返回content：">
                        <div>string/binary</div>
                    </FormItem>
                </Row>
                <Row>
                    <FormItem v-show="!showCode_200 && hasHttpCode && form_data.HttpCode" class="width-100-perc"  label="返回headers：">
                        <Table id='table_1' border ref="selection" :columns="columns_headerList" :data="data_headerList" @on-selection-change="handleselection"></Table>
                        <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                            <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                        </Tooltip> -->
                    </FormItem>
                </Row>
                </Form>
        </Card>
        <Modal v-model="modal_Show_example" width="600" :closable="false" :mask-closable="false" style="margin-left:0">
            <p slot="header">
                <span  class="margin-right-10">新增example</span>
            </p>
            <div style="padding-left:30px;">
                <Form ref="form_data_example"  :model="form_data_example" :rules="exampleValidate" :label-width="120" inline>
                    <FormItem  class="width-100-perc" label="名称：" prop="name">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.name" placeholder="请输入"></Input>
                    </FormItem>
                    <!-- <FormItem  class="width-100-perc" label="标题：" prop="title">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.title" placeholder="请输入"></Input>
                    </FormItem> -->
                    <FormItem  class="width-100-perc" label="描述：" prop="description" >
                        <Input type="textarea" size="small" style="width: 380px;" v-model="form_data_example.description" placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem  class="width-100-perc" label="示例值：" prop="value">
                        <Input type="textarea" size="small" :rows="9" style="width: 380px;" v-model="form_data_example.value" placeholder="请输入"></Input>
                    </FormItem>
                </Form>
            </div>
            <div slot="footer">
                <Button type="primary" @click="ok_example">确定</Button>
                <Button type="ghost"  @click="cancel_example">取消</Button>
            </div>
        </Modal>
        <modal_param_content_model_external ref="modal_param_content_model_external" index="kjjk" @data_update="data_update_model_external" ></modal_param_content_model_external>
        <modal_param_content_response ref="modal_param_content_response" :index="current_index" @data_update="data_update_response"></modal_param_content_response>
    </Row>
</template>

<script>
/* eslint-disable no-console, camelcase, eqeqeq, no-redeclare */
import loading from '../../my-components/loading/loading';
import param_content from './api-components/param_content';
import modal_param_content_response from './api-components/modal_param_content_response';
import modal_param_content_model_external from '~/views/model/modify_models/modal_param_content_model_external';
import util from '../../../libs/util'
import api from '../../../api/api'
import TableModelContent from './TableModelContent'
import bus from '~/libs/bus'

export default {
  name: 'param-response',
  props: ['createdByExitedInterface', 'resModelSchema', 'httpType'],
  computed: {
    modelDisabled () {
      console.log(this.createdByExitedInterface)
      if (this.createdByExitedInterface && this.form_data.contentType === 'application/json') {
        this.form_data.model = ''
        return true
      }
      return false
    }
  },
  components: {
    loading,
    param_content,
    modal_param_content_response,
    modal_param_content_model_external,
    TableModelContent
  },
  data () {
    return {
      currentApiData: {
        apiGroup: null,
        name: '',
        id: '',
        name: '',
        description: '',
      },
      nowId: '', //当前模型id
      // 当前模型
      currentModel: {

      },
      data_spi_example_List: [
      ],
      spi_example_List: [
        {
          title: '名称',
          key: 'name',
          align: 'center'
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '示例值',
          align: 'center',
          key: 'value'
        },
        {
          title: '操作',
          key: 'operations',
          width: 160,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  // name: 'url',
                  // value: {url: '/rest/api/config/edit'}
                }],
                on: {
                  click: () => {
                    this.example_modify(params.row);
                  }
                }
              }, '修改'),
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                directives: [{
                  // name: 'url',
                  // value: {url: '/rest/api/config/edit-for-doc'}
                }],
                on: {
                  click: () => {
                    this.example_delete(params.row.index);
                  }
                }
              }, '删除')
            ]);
          }
        }
      ],
      spi_example_List_display: [
        {
          title: '名称',
          key: 'name',
          align: 'center'
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '示例值',
          align: 'center',
          key: 'value'
        }
      ],
      modal_Show_example: false,
      editExample: false,
      // 参数
      exampleValidate: {
        name: [{required: true, message: '名称不能为空'}],
        // title:[{required:true,message:'标题不能为空'}],
        description: [{required: true, message: '描述不能为空'}],
        value: [{required: true, message: '示例值不能为空'}]
      },
      form_data_example: {
        name: '',
        // title:"",
        description: '',
        value: ''
      },
      data_api_model_list: [],
      pageNo: 1,
      // 分页总数
      pageTotal: 0,
      pageSize: 10,
      columns_headerList: [
        // {
        //     type: 'selection',
        //     width: 60,
        //     align: 'center'
        // },
        {
          title: '参数名称',
          key: 'header_Name'
        },
        {
          title: '参数格式/类型',
          key: 'header_URI'
        }, {
          title: '描述',
          key: 'header_des'
        }
      ],
      data_headerList: [
        {
          header_Name: 'Location',
          header_URI: 'string/string',
          header_des: '重定向地址'
        }
      ],
      form_data: {
        HttpCode: '',
        contentType: '',
        requestUrl: '',
        httpMethod: '',
        encrypt: false,
        model: ''
      },
      contentType_list: [
        // {value:"application/json",label:"application/json"},
        // {value:"application/octet-stream",label:"application/octet-stream"},
        // {value:"application/x-www-form-urlencoded",label:"application/x-www-form-urlencoded"},
        // {value:"multipart/form-data",label:"multipart/form-data"},
      ],
      HttpCode_list: [],
      current_status: '',
      // json表格的表头

      data_response: [
        {
          index: 0,
          originalID: 0,
          children: [],
          params: 'BizSystem',
          name: '参数中文名',
          type: 'STRING',
          parentType: 'STRING',
          must: true,
          description: '',
          example: '',
          defaultValue: '',
          sampleValue: '', //
          internal: true, // 是否内部变量
          sensitive: true, // 是否敏感字段
          paramDataFormat: '', // 格式
          endParamIndex: '', // 端点参数顺序
          endParamName: '', // 端点参数名称
          inheritFromGroup: '', // 继承自api分组
          bucket: '', //
          fileName: '', //
          notEmpty: '',
          _expanded: false,
          length: {min: 1, max: 9}, // minexclusive是啥
          pattern: '', // 正则
          newItem: false
        },
        {
          index: 1,
          originalID: 0,
          children: [],
          params: 'BizSystem',
          name: '参数中文名',
          type: 'STRING',
          parentType: 'STRING',
          must: true,
          description: '',
          example: '',
          defaultValue: '',
          sampleValue: '', //
          internal: true, // 是否内部变量
          sensitive: true, // 是否敏感字段
          paramDataFormat: '', // 格式
          endParamIndex: '', // 端点参数顺序
          endParamName: '', // 端点参数名称
          inheritFromGroup: '', // 继承自api分组
          bucket: '', //
          fileName: '', //
          notEmpty: '',
          _expanded: false,
          length: {min: 1, max: 9}, // minexclusive是啥
          pattern: '', // 正则
          newItem: false
        }
      ],

      // 当前行
      current_index: -1,
      showCode_200: true,
      passResponseType: '',
      showJson: true,
      hasHttpCode: false,
      data_propertiesStr: ''
    }
  },
  methods: {
    reset () {
      // this.$refs.tableModelContent.setList()
      // 赋默认值
      this.$nextTick(() => {
        if(this.current_status == 'create') {
          if(this.httpType === 'FILE_DOWNLOAD') {
            this.contentType_list = [
              {value:"application/octet-stream",label:"application/octet-stream"},
            ]
          } else {
            this.contentType_list = [
              {value:"application/json",label:"application/json"},
            ]
          }
          if(!this.form_data.HttpCode) {
            this.form_data.HttpCode = this.httpType === 'FILE_DOWNLOAD' ? '200_binary' : 200
            this.changeHttpCode(this.httpType === 'FILE_DOWNLOAD' ? '200_binary' : 200)
            this.form_data.contentType = this.httpType === 'FILE_DOWNLOAD' ? 'application/octet-stream' : 'application/json'
            this.changeContentType(this.httpType === 'FILE_DOWNLOAD' ? 'application/octet-stream' : 'application/json')
          }
        }
      })
    },
    // 页面初始化
    init () {
    },
    // 查看dto
    seeDto(dtoData, type) {
      // 筛选当前dto id
      this.currentApiData.name = dtoData
      this.data_api_model_list.forEach((item) =>{
        if(item.name === dtoData) {
          this.currentApiData.id = item.id
        }
      })
      if(type === 'detail') {
        this.detail_model(this.currentApiData)
      } else {
        this.nowId = this.currentApiData.id;
        this.modelList(this.currentApiData)
        this.edit_model(this.currentApiData)
      }
    },
    async responseParams() {
      let id = ''
      this.data_api_model_list.forEach((item) =>{
        if(item.name === this.form_data.model) {
          id = item.id
        }
      })
      let schema = ''
      if (!this.createdByExitedInterface) {
        if(!id) return []
        await api.yop_modalManagement_modal_detail({id}).then(
          (res) => {
            if (res.data.status === 'success') {
              schema = JSON.parse(res.data.data.result.schema)
            } else {
              this.$Modal.error({
                title: '错误',
                content: res.data.message
              });
              schema = ''
            }
          }
        )
      } else {
        schema = JSON.parse(this.resModelSchema.schema)
      }
      if(!schema) return []
      return Object.keys(schema.properties)
    },
    // 模型详情弹框
    detail_model (row) {
      var id = row.id;
      var name = row.name;
      var apiGroup = row.apiGroup;
      var description = '';
      api.yop_modalManagement_modal_detail({id: id}).then(
        (response) => {
          var response = response.data
          if (response.status === 'success') {
            var schema = JSON.parse(response.data.result.schema)
            var bizOrderVariable = response.data.result.bizOrderVariable
            var sensitiveVariables = response.data.result.sensitiveVariables
            description = response.data.result.description
            this.propertiesDetails = schema.properties;
            var arr = []
            for (let i in this.propertiesDetails) {
              let o = {};
              o['name'] = i;
              for (var k in schema.required) {
                var requiredName = schema.required[k]
                if (requiredName === i) {
                  o['required'] = true
                }
              }
              for (var j in this.propertiesDetails[i]) {
                o[j] = this.propertiesDetails[i][j]
                if (j == 'type') {
                  o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
                } else if (j == '$ref') {
                  o['type'] = ['object', 'object']
                } else if (j == 'additionalProperties') {
                  o['type'] = ['object', 'map']
                }
              }
              for (var k in o) {
                if (k == 'format') {
                  o['type'][1] = o[k]
                }
              }
              arr.push(o)
            }
            this.$refs.modal_param_content_model_external.current_panel_set('detail_model');
            var detailData = {
              type: schema.type,
              title: schema.title,
              name: name,
              apiGroup: apiGroup,
              description: description,
              data_propertiesList: arr,
              extensions: schema.extensions,
            }
            this.$refs.modal_param_content_model_external.form_data_set(detailData, bizOrderVariable, sensitiveVariables, 'detail');
            // this.current_index = -1;
            this.$refs.modal_param_content_model_external.show();
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      );
    },
    // 修改模型弹框
    // 修改model
    edit_model (row) {
      var description = '';
      var apiGroup = row.apiGroup
      var name = row.name
      api.yop_modalManagement_modal_detail({id: row.id}).then(
        (response) => {
          var response = response.data
          if (response.status === 'success') {
            var schema = JSON.parse(response.data.result.schema)
            var bizOrderVariable = response.data.result.bizOrderVariable
            var sensitiveVariables = response.data.result.sensitiveVariables
            description = response.data.result.description
            this.propertiesDetails = schema.properties;
            var requiredArr = schema.required
            var arr = []
            for (let i in this.propertiesDetails) {
              let o = {};
              o['name'] = i;
              for (var j in this.propertiesDetails[i]) {
                // 每个属性
                o[j] = this.propertiesDetails[i][j]
                if (j == 'type') {
                  o[j] = [this.propertiesDetails[i][j], this.propertiesDetails[i][j]]
                } else if (j == '$ref') {
                  o['type'] = ['object', 'object']
                } else if (j == 'additionalProperties') {
                  o['type'] = ['object', 'map']
                }
              }
              for (var k in o) {
                if (k == 'format') {
                  o['type'][1] = o[k]
                }
              }
              arr.push(o);
            }
            if (requiredArr) {
              for (var i = 0; i < requiredArr.length; i++) {
                var requiredArrI = requiredArr[i]
                for (var j = 0; j < arr.length; j++) {
                  if (requiredArrI == arr[j].name) {
                    arr[j].required = true
                  }
                }
              }
            }

            this.$refs.modal_param_content_model_external.current_panel_set('modify_model');
            var detailData = {
              type: schema.type,
              title: schema.title,
              name: name,
              description: description,
              apiGroup: apiGroup,
              data_propertiesList: arr,
              extensions: schema.extensions,
            }
            this.$refs.modal_param_content_model_external.form_data_set(detailData, bizOrderVariable, sensitiveVariables);
            // this.current_index = -1;
            this.$refs.modal_param_content_model_external.show();
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        }
      );
    },
    // 查询下模型列表 找到当前修改的模型
    modelList(row) {
      let paramsTemp = {
        name: row.name,
        apiGroup: '',
        pageNo: 1,
        pageSize: 10
      };
      // 查询modal列表
      api.yop_modalManagement_modal_list(paramsTemp).then(response => {
        let data = response.data
        this.currentModel = data.data.page.items.find((item) => { return item.id === this.nowId})
      });
    },
    // 更新model外部的数据
    data_update_model_external (data, type, index) {
      if (type === 'create_model') {
        // 现在为不包含子项的处理 需要在提交的时候2次数据规范
        api.yop_modalManagement_modal_create(data).then(
          (response) => {
            if (response.status === 'success') {
              this.$ypMsg.notice_success(this, '创建成功');
            } else {
              this.$Modal.error({
                title: '错误',
                content: response.message
              });
            }
          }
        );
      } else {
        for (var i in data) {
          if (i == 'name' || i == 'apiGroup') {
            delete data[i]
          }
        }
        data.id = this.nowId;
        data.version = this.currentModel.version;
        api.yop_modalManagement_modal_update(data).then(
          (response) => {
            if (response.status === 'success') {
              this.$ypMsg.notice_success(this, '修改成功');
            } else {
              this.$Modal.error({
                title: '错误',
                content: response.message
              });
            }
          }
        );
      }
      this.$refs.modal_param_content_model_external.cancel();
    },
    // 新增example
    add_example () {
      this.$refs.form_data_example.resetFields()
      this.modal_Show_example = true;
      this.editExample = false
    },
    ok_example () {
      this.$refs.form_data_example.validate((valid) => {
        if (valid) {
          var example = {
            name: this.form_data_example.name,
            // title:this.form_data_example.title,
            description: this.form_data_example.description,
            value: this.form_data_example.value
          }
          if (!this.editExample) {
            this.data_spi_example_List.push(example)
          } else {
            this.data_spi_example_List.splice(this.row_index, 1, example)
          }
          this.modal_Show_example = false;
        } else {
          this.form_validate_failed()
        }
      })
    },
    cancel_example () {
      this.modal_Show_example = false;
    },
    example_modify (row) {
      this.editExample = true
      var name = row.name;
      var description = row.description;
      var value = row.value;
      this.form_data_example.name = name;
      // this.form_data_example.title = title;
      this.form_data_example.description = description;
      this.form_data_example.value = value;
      this.row_index = row._index;
      this.modal_Show_example = true;
    },
    example_delete (index) {
      this.$Modal.confirm({
        title: '提示',
        content: '确认删除示例么？',
        'ok-text': '确认',
        onOk: () => {
          this.data_spi_example_List.splice(index, 1);
        }
      });
    },
    // 判断是否是json
    isJson (str) {
      if (typeof str === 'string') {
        try {
          var obj = JSON.parse(str);
          if (typeof obj === 'object' && obj) {
            return true;
          } else {
            return false;
          }
        } catch (e) {
          console.log('error：' + str + '!!!' + e);
          return false;
        }
      }
      console.log('It is not a string!')
    },
    set_current_status (val) {
      this.current_status = val
    },
    // 获取model
    getModels (apiGroup, showModel) {
      var param = {apiGroup: apiGroup}
      this.currentApiData.apiGroup = apiGroup
      this.getModelsList(param, showModel)
    },
    getModelsList (param, showModel) {
      api.yop_modalManagement_modal_simple_list(param).then(
        (response) => {
          var response = response.data
          this.data_api_model_list = [];
          let apigroup = response.data.result;
          // this.data_api_model= apigroup[0].groupCode;
          for (var i in apigroup) {
            this.data_api_model_list.push(
              {
                label: apigroup[i].name + '(' + apigroup[i].description + ')',
                value: apigroup[i].name,
                name: apigroup[i].name,
                id: apigroup[i].id,
              }
            )
          }
          if (showModel == 'showModel') {
            var schema = this.form_data.requestBody.contents['application/json'].schema;
            this.form_data.model = JSON.parse(schema)['$ref'].split('/').pop();
          }
        }
      );
    },
    initSelect () {
      this.$refs.form_data.resetFields();
      this.$refs.select_HttpCode.clearSingleSelect();
      this.$refs.select_contentType.clearSingleSelect()
      this.$refs.modal_apiM_select_4.clearSingleSelect()
      this.hasHttpCode = false
    },
    setHTTPCode (val) {
      var self = this
      this.HttpCode_list = [...val]
      if (val) {
        this.$nextTick(function () {
          self.HttpCode_list = [...val]
        })
      } else {
        this.HttpCode_list = [{value: 200, label: '200'}]
      }
      this.initSelect()
    },
    // httpcode改变
    changeHttpCode (e) {
      this.hasHttpCode = true
      if (e === 200) {
        this.showCode_200 = true
        this.passResponseType = 'show_200'
        this.contentType_list = [{value: 'application/json', label: 'application/json'}]
      } else if (e == '200_binary') {
        this.showCode_200 = true
        this.passResponseType = 'show_200_binary'
        this.contentType_list = [{value: 'application/octet-stream', label: 'application/octet-stream'}]
      } else {
        this.passResponseType = 'show_300'
        this.showCode_200 = false
      }
    },
    changeContentType (e) {
      if (e == 'application/octet-stream') {
        this.showJson = false
      } else {
        this.showJson = true
      }
      if (this.modelDisabled) {
        this.data_propertiesStr = this.resModelSchema.modelRef
      }
      this.$emit('changeResponseContentType')
    },
    handleselection (value) {
      this.multiSelectedData = value;
    },
    pageRefresh () {

    },
    // 新增参数
    new_param (data, panel) {
      this.$refs[panel].current_panel_set('create_response');
      this.$refs[panel].$refs.form_data_left.resetFields();
      this.current_index = -1;
      this.$refs[panel].show();
    },
    model_name_handler (model_name) {
      let result = model_name.split('/');
      return result[result.length - 1]
    },
    // 点击model跳转
    model_click (model_name) {
      let result = model_name.split('/')
      this.$emit('model_jump', result[result.length - 1])
    },

    // 点击保存校验
    saveCheck () {
      // 基本信息的验证
      return this.$refs['form_data'].validate((valid) => {
        if (valid) {
          var param = {}
          let resSchema = `{"$ref":"#/components/schemas/${this.form_data.model}"}`
          if (this.modelDisabled) {
            resSchema = `{"$ref":"#/components/schemas/${this.data_propertiesStr}"}`
          }
          if (this.passResponseType === 'show_200') {
            param = {
              httpCode: 200,
              contentType: 'application/json',
              encrypt: this.form_data.encrypt,
              content: {
                schema: resSchema,
                examples: this.data_spi_example_List
              }
            }
          }
          if (this.passResponseType === 'show_200_binary') {
            param = {
              httpCode: 200,
              encrypt: this.form_data.encrypt,
              contentType: 'application/octet-stream',
              content: {
                schema: '{"type":"string","format":"binary"}'
              }
            }
          }
          if (this.passResponseType === 'show_300') {
            param = {
              httpCode: 302,
              headers: [{
                'name': 'location',
                'description': '重定向位置',
                'deprecated': false,
                'schema': '{"type":"string"}',
                'examples': [{
                  'name': 'location',
                  'title': '重定向位置',
                  'description': '重定向位置',
                  'value': 'www.yeepay.com'
                }]
              }]
            }
          }
          this.$emit('passParamResponse', JSON.parse(JSON.stringify(param)))
          return true
        } else {
          this.$Message.error('请检查响应参数是否填写完全');
          return false
        }
      });
    },
    // 本地数据获取
    setRequestData (data) {
      this.form_data.HttpCode = data.httpCode * 1
      if (data.contentType == 'application/octet-stream') {
        this.changeHttpCode('200_binary');
        this.form_data.HttpCode = '200_binary'
      } else {
        this.changeHttpCode(this.form_data.HttpCode);
      }
      this.form_data.encrypt = data.encrypt
      this.form_data.contentType = data.contentType
      this.changeContentType(data.contentType)
      if (this.form_data.HttpCode === 200) {
        if (data.contentType !== 'application/octet-stream') { this.form_data.model = JSON.parse(data.content.schema).$ref.split('/').pop() }
        this.data_spi_example_List = data.content.examples || []
      }
    },
    // 数据赋值
    data_assignment (data) {
      for (var i in data) {
        return this.data_rehandler(data[i].content['application/json'].schema)
      }
    },
    // 数据处理
    data_rehandler (data) {
      let result = [];
      let temp = {
        param: '',
        type: []
      };
      for (var i in data) {
        if (util.swagger_properties[i] === 'type') {
          temp['type'][0] = data[i];
          if (data[i] === 'object') {
            if (data['additionalProperties']) {
              temp['type'][1] = 'map';
              temp['children'] = this.data_rehandler(data['additionalProperties']);
            }
          } else if (data[i] === 'array') {
            if (data['items']) {
              temp['type'][1] = 'array';
              temp['children'] = this.data_rehandler(data['items']);
            }
          }
        } else if (util.swagger_properties[i] === 'format') {
          temp['type'][1] = data[i];
        } else {
          temp[util.swagger_properties[i]] = data[i];
        }
      }
      result.push(temp);
      return result;
    },
    // 编辑元素
    edit_param (index, data, panel) {
      this.$refs[panel].$refs.form_data_left.resetFields();
      this.$refs[panel].$refs.form_data_right.resetFields();
      this.$refs[panel].current_panel_set('modify_response');
      this.$refs[panel].current_index_set(index);
      this.$refs[panel].form_data_set(data);

      this.current_index = -1;
      this.$refs[panel].show();
    },
    // 更新json的数据
    data_update_response (data, type, index) {
      if (type === 'create_response') {
        // 现在为不包含子项的处理 需要在提交的时候2次数据规范
        this.data_response.push(data)
      } else {
        for (var i in data) {
          this.data_response[index][i] = data[i]
        }
      }
      this.$refs.modal_param_content_response.cancel();
    },
    // 删除元素
    delete_param_response (index) {
      this.data_response.splice(index, 1);
    }
  },
  mounted () {
    this.init();
    bus.$on('updateResDtoName', (name) =>{
      this.data_propertiesStr = name
      console.log(this.resModelSchema.modelRef, '<---this.resModelSchema.modelRef')
      this.resModelSchema.modelRef = name
    });
  },
  beforeDestroy () {
    bus.$off('updateResDtoName');
  },
};
</script>

<style lang='less' scoped>
.param-res {
  .flex-box {
    display: flex;
    align-items: center;
    .edit-dto {
      margin-left: 10px;
    }
  }
  .flex-con {
    .ivu-form-item-content {
      display: flex;
      align-items: center;
      .link-dto {
        margin-left: 10px;
      }
    }
  }
} 
</style>
