<style lang="less">
    @import '../../../styles/common.less';
    @import '../../../styles/loading.less';
    @import '../api_Mangement_Open.less';
    @import '../../regression-test/regression.less';
    /*html,body{*/
         /*width: 100%;*/
         /*height: 100%;*/
         /*overflow: scroll;*/
     /*}*/
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab{
        border-radius: 0;
        background: #fff;
    }
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active{
        // border-top: 1px solid #3399ff;
    }
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active:before{
        content: '';
        display: block;
        width: 100%;
        height: 1px;
        background: #3399ff;
        position: absolute;
        top: 0;
        left: 0;
    }
    .ivu-modal-footer {
        border:0!important;
    }
</style>
<template>
    <div>
        
        <Col>

                    <Row>
                        <Col >
                            <Form ref="form_data_top" :label-width="120" inline>
                                <FormItem  class="width-100-perc" label="API分组：">
                                    <p>{{this.form_data_apiGroup}}</p>
                                </FormItem>
                                <Row>

                                <FormItem  class="width-100-perc" label="请求路径：">
                                    <p>{{this.form_data_requestPath}}</p>
                                </FormItem>
                                </Row>

                                <Row>
                                <FormItem label="请求方法：">
                                    <p >{{this.form_data_requestMethod}}</p>
                                </FormItem>
                                </Row>
                            </Form>
                        </Col>
                    </Row>
                    <p v-show="changePart">基本信息</p>
                    <Row v-show="basicShow && changePart">
                        <Col >
                            <Form ref="form_data_basics"  :model="form_data_basics" :label-width="120" inline>
                                <FormItem  class="width-100-perc" label="API名称：" prop="name" >
                                    <p>{{form_data_basics.name}}</p>
                                </FormItem>
                                <Row>

                                <FormItem  class="width-100-perc" label="API标题：" prop="title">
                                    <p>{{form_data_basics.title}}</p>
                                </FormItem>
                                </Row>

                                <Row>
                                <FormItem label="接口类型：" prop="apiType">
                                    <p >{{form_data_basics.apiType == "COMMON"?"普通":form_data_basics.apiType == "FILE_UPLOAD"?"文件上传":"文件下载"}}</p>
                                </FormItem>
                                </Row>
                                <Row>
                                <FormItem  class="width-100-perc" label="API分组：" prop="apiGroup">
                                    <p >{{form_data_basics.apiGroup}}</p>
                                </FormItem>
                                </Row>
                                <FormItem  class="width-100-perc" label="是否幂等：" prop="IDEMPOTENT">
                                    <p>{{form_data_basics.options.IDEMPOTENT?"是":"否"}}</p>
                                </FormItem>
                                <Row>

                                <FormItem  class="width-100-perc" label="描述：" prop="description">
                                    <p v-html="form_data_basics.description"></p>
                                </FormItem>
                                </Row>

                                
                                
                            </Form>
                        </Col>
                    </Row>
                    <p v-show="changePart">安全需求</p>
                    <Form v-show="changePart" ref="form_data_security"  :model="form_data_security" :label-width="120" inline >
                        <FormItem label="是否继承：" class="width-50-perc" prop="requestUrl">
                            <p>{{form_data_security.inheritFlag?"继承自API分组":"自定义安全需求"}}</p>
                        </FormItem>
                        <safety-import-show ref="safetyImportShow"></safety-import-show>
                    </Form>
                    <p v-show="changePart">请求参数</p>
                    <Form v-show="changePart" ref="form_data_request"  :model="form_data_request" :label-width="120" inline >

                        <Col span="24">
                        <Row>
                            <FormItem label="请求路径：" class="width-50-perc" prop="requestUrl">
                                <p>{{form_data_request.path}}</p>
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="http方法：" class="width-50-perc" prop="httpMethod">
                                <p>{{form_data_request.httpMethod}}</p>
                            </FormItem>
                        </Row>
                        <Row  v-show="form_data_request.requestBody">
                            <!-- <p style="margin-left:27px;margin-bottom:20px;">请求体：</p> -->
                            <FormItem label="contentType：" class="width-100-perc" prop="contents">
                                <p>{{this.contentTypeRequest}}</p>
                            </FormItem>
                            <FormItem label="入参请求模式：" class="width-100-perc" prop="description">
                                <p>{{form_data_request.parameterHandlingType  === "MAPPING"?"映射":"透传"}}</p>
                            </FormItem>
                            <FormItem v-show="form_data_request.requestBody?form_data_request.requestBody.contents.schema:false" label="content：" class="width-50-perc" prop="description">
                                <p>{{form_data_request.requestBody?form_data_request.requestBody.contents.schema:""}}</p>
                            </FormItem>
                            <FormItem label="描述：" class="width-100-perc" prop="description">
                                <p>{{form_data_request.requestBody?form_data_request.requestBody.description:""}}</p>
                            </FormItem>
                            <FormItem v-show="this.schemaRequest" class="width-100-perc" label="content：" prop="contentType">
                                <pre>{{this.schemaRequest}}</pre>
                            </FormItem>
                         <p style="font-size: 12px; padding-left: 53px;line-height:30px;">example：</p>

                            <FormItem class="width-100-perc" label="">
                                <Table id='table_1' border ref="selection" :columns="response_example_List_display" :data="examplesRequest"></Table>
                            </FormItem>
                            

                        </Row>
                        <Row  v-show="form_data_request.parameters">
                            <!-- <FormItem label="contentType：" class="width-100-perc" prop="contents">
                                <p>{{this.contentTypeRequest}}</p>
                            </FormItem> -->
                            <FormItem label="入参请求模式：" class="width-100-perc" prop="description">
                                <p>{{form_data_request.parameterHandlingType  === "MAPPING"?"映射":"透传"}}</p>
                            </FormItem>
                            <Row>
                                <Table id='table_1' border ref="selection" :columns="request_parameters_List_display" :data="form_data_request.parameters"></Table>
                            </Row>
                        </Row>
                        </Col>
                    </Form>
                    <p v-show="changePart">响应参数</p>
                    <Form v-show="changePart" ref="form_data_response"  :model="form_data_response" :label-width="120" inline >

                        <Col span="24">
                        <Row>
                            <FormItem label="返回HttpCode：" class="width-50-perc" prop="httpCode">
                                <p>{{form_data_response.httpCode}}</p>
                            </FormItem>
                        </Row>
                        <Row v-show="this.schemaResponse">
                            <FormItem label="返回contentType：" class="width-100-perc" prop="contentType">
                                <p>{{form_data_response.contentType}}</p>
                            </FormItem>
                        </Row>
                        <Row  v-show="this.schemaResponse">
                            <FormItem label="返回content：" class="width-100-perc" prop="description">
                                <p>{{this.schemaResponse}}</p>
                            </FormItem>
                        </Row>
                         <p style="font-size: 12px;padding-left: 53px;line-height:30px;">example：</p>

                        <Row  v-show="form_data_response.httpCode == '200'">
                                <Table id='table_1'  border ref="selection" :columns="response_example_List_display" :data="exampleResponse"></Table>
                        </Row>
                        <Row  v-show="form_data_response.httpCode !== '200'">
                            <FormItem  class="width-100-perc"  label="返回headers：">
                            </FormItem>
                        </Row>
                        <Row  v-show="form_data_response.httpCode !== '200'">
                                <Table id='table_1' border ref="selection" :columns="columns_headerList" :data="data_headerList"></Table>
                        </Row>

                        </Col>
                    </Form>
                    <p v-show="changePart">后端服务</p>
                    <Form v-show="changePart" ref="form_data_endpoint"  :model="form_data_endpoint" :label-width="120" inline >

                        <Col span="24">
                        <Row>
                            <FormItem label="后端服务类型：" class="width-50-perc" prop="type">
                                <p>{{form_data_endpoint.type}}</p>
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="后端服务：" class="width-50-perc" prop="serviceName">
                                <p>{{form_data_endpoint.serviceName}}</p>
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="路径：" class="width-50-perc" prop=" path">
                                <p>{{form_data_endpoint.path}}</p>
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="链接超时：" class="width-50-perc" prop="connectionTimeout">
                                <p>{{form_data_endpoint.connectionTimeout}}ms</p>
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="读取超时：" class="width-50-perc" prop="readTimeout">
                                <p>{{form_data_endpoint.readTimeout}}ms</p>
                            </FormItem>
                        </Row>

                        <Row v-show="this.form_data_request.parameters">   
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">后端服务参数映射：</p>
                                <Table  border ref="selection" :columns="columns_service_params_mapping_desc" :data="resultMappingList"></Table>
                                <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh_mapping"></Page>
                                </Tooltip> -->
                        </Row>
                        <Row>
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">常量参数：</p>
                            
                                 <Table  border ref="selection" :columns="columns_Constant_desc" :data="form_data_endpoint.constantParameters"></Table>
                        </Row>
                        <Row>
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">系统参数：</p>
                                 <Table  border ref="selection" :columns="columns_System_desc" :data="form_data_endpoint.systemParameters"></Table>
                        </Row>

                        </Col>
                    </Form>
                    <p v-show="changePart">回调</p>
                    <Form v-show="changePart" ref="form_data_callbacks" :label-width="120" inline >
                        <Row>
                            <Col span="24">
                                <FormItem label="回调：" class="width-50-perc" prop="type">
                                    <p v-for="(item, index) in form_data_callbacks" :key="index">{{item}}</p>
                                </FormItem>
                            </Col>
                        </Row>
                    </Form>
                   
                    <!-- <Col span="24" style="margin-top: 20px;" class="tabs-style2">
                                <Table id='table_1' border ref="selection" :columns="spi_example_List_display" :data="data_spi_example_List"></Table>
                            <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                                <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                            </Tooltip>
                    </Col> -->

                     <Row style="padding:20px;height:40px;line-height:40px;padding-top:0">
                        <Col span="24" style="margin-top: 20px;" class="tabs-style2">
                            <p v-show="this.operator_back">操作人：{{this.operator_back}}</p>
                            <p v-show="this.operationType_back">操作类型：{{this.operationType_back}}</p>
                            <p v-show="this.operateDate">操作时间：{{this.operateDate}}</p>
                            <p v-show="this.cause">操作原因：{{this.cause}}</p>


                        </Col>
                    </Row>

        </Col>
            
        <Modal v-model="modal_Show_example" width="600" :closable="false" :mask-closable="false">
            <p slot="header">
                <span  class="margin-right-10">新增example</span> 
            </p>
            <div style="padding-left:30px;">
                <Form ref="form_data_example"  :model="form_data_example" :label-width="120" inline>
                    <FormItem  class="width-100-perc" label="名称：" prop="name" :rules="{required:true,message:'name不能为空'}">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.name" placeholder="请输入"></Input>

                    </FormItem>
                    <!-- <FormItem  class="width-100-perc" label="标题：" prop="title" :rules="{required:true,message:'title不能为空'}">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.title" placeholder="请输入"></Input>
                    </FormItem> -->
                    <FormItem  class="width-100-perc" label="描述：" prop="description" :rules="{required:true,message:'description不能为空'}">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.description" placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem  class="width-100-perc" label="值：" prop="value" :rules="{required:true,message:'value不能为空'}">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.value" placeholder="请输入"></Input>
                    </FormItem>
                </Form>
            </div>
            <div slot="footer">
                <Button type="primary" @click="ok_example">确定</Button>
                <Button type="ghost"  @click="cancel_example">取消</Button>
            </div>
           
        </Modal>
        <modal_change_record_detail ref="modal_change_record_detail" @data_update="data_update_model"></modal_change_record_detail>

           
    </div>
    
</template>

<script>
    import api from '../../../api/api.js';
    import util from '../../../libs/util.js';
    import loading from '../../my-components/loading/loading'
    import safetyImportShow from './safety_import_show'
    import modal_change_record_detail from './modal_change_record_detail'

    export default {
        name: 'api_basics',
        components :{
            loading,
            safetyImportShow,
            modal_change_record_detail
        },
        data () {
           return {
    
form_data_apiGroup:"",
form_data_requestPath:"",
form_data_requestMethod:"",
    // 参数
    cause:"",
    operateDate:"",
    operationType_back:"",
    operator_back:"",
    showReturn:true,
    requestUrl:"#app.callbackUrl",
    httpMethod:"POST",
    form_data:{
        requestUrl:"#app.callbackUrl",
        httpMethod:"POST",
        description:"",
        model:"application/json"
    },
    
    contentType_list:[{value:"application/json",label:"application/json"}],
    
    // 响应参数example
    data_response_example_List:[],
    response_example_List_display:[
        {
            title: '名称',
            key: 'name',
            align:"center"
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
            title: '描述',
            align:"center",
            key: 'description'
        },
        {
            title: '值',
            align:"center",
            key: 'value'
        },
    ],
    data_request_example_List:[],
    request_example_List_display:[
        {
            title: '名称',
            key: 'name',
            align:"center"
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
            title: '描述',
            align:"center",
            key: 'description'
        },
        {
            title: '值',
            align:"center",
            key: 'value'
        },
    ],
    data_spi_example_List:[],
    spi_example_List_display:[
        {
            title: '名称',
            key: 'name',
            align:"center"
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
            title: '描述',
            align:"center",
            key: 'description'
        },
        {
            title: '值',
            align:"center",
            key: 'value'
        },
    ],
    request_parameters_List_display:[
        {
            title: '名称',
            align:"center",
            key: 'name',
            render: (h, params) => {
                if(params.row.name){
                    return h('div',params.row.name);
                }else{
                    return h('div','--');
                }
            }
        },
        {
            title: '类型-格式',
            key: 'type',
            align:'center',
            render: (h, params) => {
                    return h('div', [h('p', JSON.parse(params.row.schema).type),
                    h('p', JSON.parse(params.row.schema).format)]);
            }
            // render: (h,params) => {
            //     if(params.row.model){
            //         let _this = this
            //         return h('div', [
            //             h('p',[
            //                 h('a',
            //                     {
            //                         on: {
            //                             click: function(){
            //                                 _this.model_click(params.row.model);
            //                             }
            //                         },
            //                     },params.row.model)
            //             ])
            //         ]);
            //     }else{
            //         return params.row.type[1]? h('div',params.row.type[0]+'/'+params.row.type[1]):h('div',params.row.type[0]);
            //     }
            // }
        },
        {
            align:"center",
            title: '必需',
            key: 'must',
            render: (h, params) => {
                // if(params.row.model){
                //     return h('div','--');
                // }else{
                    return params.row.required ? h('div','是') : h('div','否');
                // }

            }
        },
        {
            title: '废弃',
            align:'center',
            key: 'deprecated',
            render: (h, params) => {
                // if(params.row.model){
                //     return h('div','--');
                // }else{
                    return params.row.deprecated ? h('div','是') : h('div','否');
                // }
            }
        },
        {
            title: '描述',
            key: 'description',
            align:"center"

        },
        {
            title: '约束',
            align:"center",
            render: (h, params) => {
                var minimum,maximum,maxLength,minLength,notMin,notMax,maxItems,minItems,pattern
                notMin = params.row.exclusiveMinimum?"不包含": "包含"
                notMax = params.row.exclusiveMaximum?"不包含": "包含"
                minimum = params.row.minimum?`最小值：${params.row.minimum}(${notMin})`:""
                maximum = params.row.maximum?`最小值：${params.row.maximum}(${notMax})`:""
                maxLength = params.row.maxLength ?`最大长度：${params.row.maxLength}`:""
                minLength = params.row.minLength ?`最大长度：${params.row.minLength}`:""
                maxItems = params.row.maxItems ?`最大数量：${params.row.maxItems}`:""
                minItems = params.row.minItems ?`最小数量：${params.row.minItems}`:""
                pattern = params.row.pattern ?`正则${params.row.pattern}`:""
                return h('div', [
                    h('p', minimum),
                    h('p', maximum),
                    h('p', maxLength),
                    h('p', minLength),
                    h('p', maxItems),
                    h('p', minItems),
                    h('p', pattern),
                    ]);
            }
        },
            {
            title: '操作',
            align: 'center',
            render: (h, params) => {
                return h('div', [
                    h('Button', {
                        props: {
                            type: 'text',
                            size: 'small'
                        },
                        on: {
                            click: () => {
                                this.detail_param(params.index,params.row,'detail_model_show','modal_change_record_detail')
                            }
                        }
                    }, '详情'),
                ]);
            }

        }
        
        // {
        //     title: '最大值/排除最大值',
        //     align:"center",
        //     render: (h, params) => {
        //         return h('div', [h('p', params.row.maximum),
        //             h('p', params.row.exclusiveMaximum)]);
        //     }
        // },
        // {
        //     type:'html',
        //     title: '最小值/排除最小值',
        //     align:"center",
        //     render: (h, params) => {
        //         return h('div', [h('p', params.row.minimum),
        //             h('p', params.row.exclusiveMinimum)]);
        //     }
        // },
        // {
        //     type:'html',
        //     title: '最大长度',
        //     key: 'maxLength',
        //     align:"center"
        // },
        // {
        //     type:'html',
        //     title: '最小长度',
        //     key: 'minLength',
        //     align:"center"
        // },
        // {
        //     type:'html',
        //     title: '最大数目',
        //     key: 'maxItems',
        //     align:"center"
        // },
        // {
        //     type:'html',
        //     title: '最小数目',
        //     key: 'minItems',
        //     align:"center"
        // },
        // {
        //     title: '端点参数',
        //     key: 'param_name',
        //     width: 120
        // },
    ],
    data_propertiesList:[],

    modal_Show_example:false,
    changePart:false,
    form_data_example:{
        name:"",
        // title:"",
        description:"",
        value:"",
    },
    form_data_basics:{
        name:"",
        title:"",
        apiType:"",
        apiGroup:"",
        description:"",
        options:{IDEMPOTENT:false}
    },
    form_data_security:{
        inheritFlag:false,
        securities:{},
    },
    form_data_request:{
        parameterHandlingType:"",
        path:"",
        httpMethod:"",
        requestBody:""
    },
    contentTypeRequest:"",
    schemaRequest:"",
    examplesRequest:[],
    exampleResponse:[],
    form_data_response:{
        httpCode:"",
        contentType:"",
        content:"",
    },
    schemaResponse:"",
    form_data_endpoint:{
        type:"",
        serviceName:"",
        path:"",
        connectionTimeout:"",
        readTimeout:"",
        constantParameters:[],
        systemParameters:[]
    },
    columns_service_params_mapping_desc:[
                    {
                        title: '后端参数名称',
                        align: 'center',
                        render: (h, params) => {
                            return h('div',params.row.backendName || params.row["x-yop-apigateway-backend-name"]);
                        }
                    },
                    {
                        title: '后端参数位置',
                        align: 'center',
                        render: (h, params) => {
                            return h('div',params.row.location);
                        }

                    },{
                        title: '对应入参名称',
                        key: 'name',
                        align: 'center',

                    }
                    ,{
                        title: '对应入参位置',
                        align: 'center',
                        render: (h, params) => {
                            return h('div',params.row.location);
                        }

                    },{
                        title: '对应入参类型',
                        align: 'center',
                        render: (h, params) => {
                            var type = ""
                            if(params.row.schema){
                                type = JSON.parse(params.row.schema).type
                            }
                            return h('div',type);
                        }

                    }

                ],   
    columns_Constant_desc:[
        {title: '后端参数名称',
            align: 'center',
            key: 'backendName',
            render: (h, params) => {
                return h('div',params.row.backendName);
            }
        },
        {
            title: '参数值',
            align: 'center',
            key: 'value',
        },{
            align: 'center',
            title: '参数位置',
            key: 'location',

        },{
            title: '描述',
            align: 'center',
            key: 'description'
        }

    ],


    columns_System_desc:[
        {
                title: '系统参数',
                key: 'systemName',
                align: 'center',
        },
        {
            title: '后端参数名称',
            align: 'center',
            key: 'backendName',
        },{
            align: 'center',
            title: '参数位置',
            key: 'location',
        },
                            
    ],
    form_data_callbacks:[
        ""
    ],
    bizOrderVariable: {
        "part": "",
        "location": "",
        "variableName": ""
    },
    sensitiveVariables: [{
        "part": "",
        "location": "",
        "variableName": ""
    }],

    columns_headerList:[
        // {
        //     type: 'selection',
        //     width: 60,
        //     align: 'center'
        // },
        {
            title: '参数名称',
            key: 'header_Name'
        },
        {
            title: '参数格式/类型',
            key: 'header_URI'
        },{
            title: '描述',
            key: 'header_des'
        }
    ],
    data_headerList:[
        {
            header_Name:"Location",
            header_URI:"string/string",
            header_des:"重定向地址",
        }
    ],
    data_apiGroup_List:[],  
               /**
                * 基础界面变量
                */
               // api 请求方式数据绑定
               data_select_apiMethod : '',
               // api 请求方式下拉框总体数据
               data_apiMethod_list : [],
               // 当前参数tab页
               current_param_tab : 'request_params',
               // api contentType数据绑定
               data_select_apiContent : [],
               // api contentType下拉框总体数据
               data_apiContent_list : [],
               // 基础信息加载数据绑定
               show_loading_basic : false,
               // 显示收起内容
               basicShow : true,
               // 展开收起按钮文字内容绑定
               button_Content : '收起',
               // api分组数据绑定
               api_group_model : '',
               // api分组disabled
               api_group_disabled : false,
               // method post disabled
               request_post_disabled : false,
               // method get disabled
               request_get_disabled : false,
               // 接口uri disabled
               interface_uri_disabled : false,
               // api分组数据列表
               api_group_list : [],
               // api类型选择数据绑定
               data_select_apiType: '',
               // api类型
               data_apiType_List:[],
               // 请求方式绑定第一个参数是post第二个参数是get
               request_post : [false,false],
               // 当前tab页绑定的参数
               tabNow: 'basic',
               // 保存的数据参数
           
               // 接口uri数据绑定
               interface_uri: '',
               // 接口名称数据绑定
               interface_name: '',
               // 当前端点apiUri内容
               current_content_apiUri : '',
               // 基本类型
               cascader_type:[],
               // 当前apiId
               current_spiId : '',
               current_spiVersion:"",
               // 创建还是修改
               current_status: '',
                //
                check_result: true,
               /**
                * 新建弹窗变量
                */
               // 窗口显示绑定
               modal_Show_register : false,
               // 描述禁用
               disabled_description: false,
               // 端点url数据绑定
               endpoint_Url : '',
               // 显示端点url部分
               show_pointUrl : false,
               // 端点类名数据绑定
               endpoint_Name: '',
               // 是否幂等数据绑定
               idempotent: '否',
               // 端点url自定义
               pointUrl_user_defined: false,
               // 适配类型下拉框数据绑定
               data_select_type: 'TRANSFORM',
               // 适配类型下拉框数据
               data_type_List: [
                   {
                       value: 'TRANSFORM',
                       label: '转换'
                   },
                   {
                       value: 'LOCAL',
                       label: '本地'
                   },
                   {
                       value: 'PASSTHROUGH',
                       label: '透传'
                   }
               ],
               // 端点协议下拉框数据绑定
               data_select_EndpointProtocol: 'HESSIAN',
               // 端点协议下拉框数据
               data_EndpointProtocol_List: [
                   {
                       value: 'HESSIAN',
                       label: 'hessian'
                   }
               ],
               // 端点方法下拉框数据绑定
               data_select_EndpointMethod: '',
               // 端点方法下拉框数据
               data_EndpointMethod_List: [],
               // 当前端点名称内容
               current_content : '',
               // 当前端点url内容
               current_content_url : '',
               // url校验结果
               pass_url : true,
               data_api_model_list:[],
               editExample:false,
               row_index:0,
               result:{},
               resultMappingList:[]
           }
        },
        methods : {
            /**
             * 基础界面方法
             */
            detail_param(index,data,current_data,panel){
                // this.$refs[panel].$refs.form_data_left.resetFields();
                // this.$refs[panel].$refs.form_data_right.resetFields();
                this.$refs[panel].current_panel_set(current_data);
                this.$refs[panel].current_index_set(index);
                var apiGroup = this.current_modelCode
                data.type = [data.schema.type,data.schema.format?data.schema.format:data.schema.type]
                this.$refs[panel].form_data_set(JSON.parse(JSON.stringify(data)),this.sensitiveVariables,this.bizOrderVariable);
                this.current_index = -1;
                this.$refs[panel].show();
            },
            data_update_model (data,type,index,subContent) {
                // this.example = data.example
                // var data = data.form_data
                data.additionalProperties={}
                data.items={}
                if(data.constrains){
                    if(data.constrains[0] == "additionalProperties"){
                        data.additionalProperties = subContent
                    }else if(data.constrains[0] == "items"){
                        data.items = subContent
                    }
                }
                var data = JSON.parse(JSON.stringify(data))
                if(type === 'create'){
                    this.data_propertiesList.push(data);
                    //现在为不包含子项的处理 需要在提交的时候2次数据规范
                }else{
                    // this.data_propertiesList.push(data);

                    // 编辑查看
                    // if(data.type[1]){
                    //     data["format"] = data.type[1]
                    // }
                    for(var i in data){
                        this.data_propertiesList[index][i] = data[i]
                    }
                }
                for(var i =  0;i < this.data_propertiesList.length ; i ++){
                    var data_propertiesListI = this.data_propertiesList[i]
                    for(var j in data_propertiesListI){
                        if(j == "orderNo"){
                            if(data_propertiesListI[j]){
                                this.bizOrderVariable = {
                                    "part": "REQUEST",
                                    "location": "QUERY",
                                    "variableName": data_propertiesListI["name"]
                                }
                            }else{
                                delete data_propertiesListI["orderNo"]
                                this.bizOrderVariable = {}
                            }
                        }
                    }
                }
                // this.$refs.param_operate.cancel();
                // this.$refs.param_operate.$refs.form_data_right.resetFields();
                // this.$refs.param_operate.$refs.form_data_left.resetFields();
                // 新增后端服务mapping列表
                    // this.$refs.point_service.setMappingList(mapping_list_temp)

            },
            // 初始化函数
            setResult(val,type,row){
                this.operator_back = val.operator
                if(val.operationType == "DELETE"){
                    this.operationType_back = "删除"
                }
                if(val.operationType == "CREATE"){
                    this.operationType_back = "创建"
                }
                if(val.operationType == "UPDATE"){
                    this.operationType_back = "更新"
                }
                if(val.operationType == "ROLLBACK"){
                    this.operationType_back = "回滚"
                }
                if(val.operationType == "IMPORT"){
                    this.operationType_back = "导入"
                }
                this.operateDate = val.operateDate;
                this.cause = val.cause || ""
                // this.form_data_basics.name = val.name
                this.form_data_basics.apiId = val.apiId
                this.form_data_basics.apiGroup = val.apiGroup
                var valOld = val;
                if(val){
                    if(type == "changeRecord"){
                        val = val.content
                    }
                    
                    if(valOld.operationType === "CREATE"){
                        this.changePart = false
                    }else{
                        this.changePart = true
                    }
                    if(row){
                        this.form_data_apiGroup = row.apiGroup
                        this.form_data_requestPath = row.requestPath
                        this.form_data_requestMethod = row.requestMethod
                    }else{
                        this.form_data_apiGroup = val.basic.apiGroup
                        this.form_data_requestPath = val.request.path
                        this.form_data_requestMethod = val.request.httpMethod
                    }
                    if(!val)
                    return 
                    this.form_data_basics = val.basic
                    this.form_data_security = val.security || { inheritFlag:false, securities:{} }
                    this.$refs.safetyImportShow.modal_show(this.form_data_requestPath,this.form_data_apiGroup,"description",val.security)

                    this.form_data_request = val.request
                    if(this.form_data_request.requestBody){
                        this.contentTypeRequest = Object.keys(this.form_data_request.requestBody.contents)[0]
                        var schemaRequestTemp = JSON.parse(this.form_data_request.requestBody.contents[this.contentTypeRequest].schema);
                        this.schemaRequest = this.syntaxHighlight(schemaRequestTemp)
                        this.examplesRequest = this.form_data_request.requestBody.contents[this.contentTypeRequest].examples
                    }
                    this.resultMappingList = []
                    if(this.form_data_request.parameters){
                        for(let i in this.form_data_request.parameters){
                            if(this.form_data_request.parameters[i]["backendName"] || this.form_data_request.parameters[i]["x-yop-apigateway-backend-name"]){
                                this.resultMappingList.push(this.form_data_request.parameters[i])
                            }
                        }
                    }
                    this.form_data_response = val.response
                    if(val.response.content){
                        var schemaResponseTemp = JSON.parse(val.response.content.schema);
                        if(schemaResponseTemp.$ref){
                            this.schemaResponse= schemaResponseTemp.$ref.split("/").pop()
                        }
                        var schema = ""
                        if(val.response.content.schema){
                        schema = val.response.content.schema
                        }
                        if(JSON.parse(schema).$ref){
                        this.form_data_response.content.schema = JSON.parse(schema).$ref.split("/").pop()
                        }
                        this.exampleResponse = val.response.content.examples
                    }
                    if(val.response.headers){
                        this.form_data_response.headers = val.response.headers
                    }
                    if(val.endpoint) {
                        this.form_data_endpoint = val.endpoint
                    }
                    this.form_data_callbacks = val.callbacks

                    // this.form_data_bizOrderVariable = val.bizOrderVariable
                    // this.form_data_sensitiveVariables = val.sensitiveVariables

                    // this.form_data = val.request
                    // this.form_data.description = val.request.requestBody.description;
                    // var schema = JSON.parse(val.request.requestBody.contents["application/json"].schema)
                    // this.form_data.model = schema["$ref"].split("/").pop();
                    // this.data_spi_example_List = val.request.requestBody.contents["application/json"].examples || []
                }else{

                }
                

            },
            init () {
                 
            },
           
         

        
            ok_example(){
                this.$refs.form_data_example.validate((valid) => {
                    if (valid) {
                        var example = {
                                name:this.form_data_example.name,
                                // title:this.form_data_example.title,
                                description :this.form_data_example.description,
                                value:this.form_data_example.value,
                            }
                        if(!this.editExample){
                            this.data_spi_example_List.push(example)
                        }else{
                            this.data_spi_example_List.splice(this.row_index,1,example)
                        }
                        this.modal_Show_example = false;
                    }else {
                        this.form_validate_failed()
                    }
                })

            },
            form_validate_failed(){

            },
            cancel_example(){
                this.modal_Show_example = false;

            },
            syntaxHighlight(json) {
                if (typeof json != 'string') {
                    json = JSON.stringify(json, undefined, 4);
                }
                // json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
                return json
                // return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
                //     function(match) {
                //         var cls = 'number';
                //         if (/^"/.test(match)) {
                //             if (/:$/.test(match)) {
                //                 cls = 'key';
                //             } else {
                //                 cls = 'string';
                //             }
                //         } else if (/true|false/.test(match)) {
                //             cls = 'boolean';
                //         } else if (/null/.test(match)) {
                //             cls = 'null';
                //         }
                //         return match;
                //     }
                // );
            },
        },
        created () {

        },
        mounted () {
            this.init();
        },
        beforeRouteLeave (to, from, next) {
            this.$destroy();
            next();
        },
        beforeDestroy () {
            // console.log(this.$refs.te);
            // this.$refs.te.destroySelf();
        },
        destroyed () {
            // this.$store.commit('closePage', 'api_basics');
        }
    };

</script>

<style scoped>
    /*.ivu-collapse {*/
        /*background-color : #FFFFFF;*/
    /*}*/
</style>
