<style lang="less">
    .panel-content{
        margin-top:8px;
        word-break: break-word;
        padding-right: 10px;
    }
</style>
<template>
    <Row>
        <Card dis-hover>
            <Row>
            <Col span="24">
            <Button class="margin-bottom-10" style="float: right;" type="primary"
                    @click="new_param(data_model,'modal_param_content_model_external',false)">新增Model
            </Button>
            </Col>
            </Row>
            <el-collapse v-model="current_model">
                <el-collapse-item v-for="(item,index) in data_model" :key="index" :name="item.param" >
                        <template slot="title">
                            {{item.param}}
                            <el-button style="float: right;" class="margin-top-10 margin-right-10" type="warning" size="small" @click="edit_model(index,data_model,'modal_param_content_model_external')">编辑</el-button>
                            <el-button style="float: right;" class="margin-top-10 margin-right-10" type="danger" size="small" @click="delete_model(index)">删除</el-button>
                        </template>
                        <Row>
                            <Col v-show="item.param" span="12">
                                <Col span="5"><Tag type="dot" color="green" style="margin-bottom: 20px;">参数：</Tag></Col>
                                <Col span="19"><p class="panel-content">{{item.param}}</p></Col>
                            </Col>
                            <Col v-show="item.name" span="12">
                                <Col span="5"><Tag type="dot" color="green" style="margin-bottom: 20px;">名称：</Tag></Col>
                                <Col span="19"><p class="panel-content">{{item.name}}</p></Col>
                            </Col>
                            <Col span="12">
                                <Col span="5"><Tag type="dot" color="green" style="margin-bottom: 20px;">类型：</Tag></Col>
                                <Col span="19"><p class="panel-content">object</p></Col>
                            </Col>
                            <Col v-show="item.required" span="12">
                                <Col span="5"><Tag type="dot" color="green" style="margin-bottom: 20px;">是否必填：</Tag></Col>
                                <Col span="19"><p class="panel-content">{{item.required}}</p></Col>
                            </Col>
                            <Col v-show="item.sensitive" span="12">
                                <Col span="5"><Tag type="dot" color="green" style="margin-bottom: 20px;">是否敏感字段：</Tag></Col>
                                <Col span="19"><p class="panel-content">{{item.sensitive}}</p></Col>
                            </Col>
                            <Col v-show="item.orderNo" span="12">
                                <Col span="5"><Tag type="dot" color="green" style="margin-bottom: 20px;">业务订单号：</Tag></Col>
                                <Col span="19"><p class="panel-content">{{item.orderNo}}</p></Col>
                            </Col>
                            <Col v-show="item.description" span="12">
                                <Col span="5"><Tag type="dot" color="green" style="margin-bottom: 20px;">描述：</Tag></Col>
                                <Col span="19"><p class="panel-content">{{item.description}}</p></Col>
                            </Col>
                        </Row>
                        <Row>
                            <Tag type="dot" color="green" style="margin-bottom: 20px;">属性：</Tag>
                            <el-table :data="item.properties" style="width: 100%" border stripe>
                                <el-table-column prop="param" label="参数" width="150"></el-table-column>
                                <el-table-column prop="name" label="名称" width="100">
                                    <template slot-scope="scope">
                                        <div v-if="scope.row.name">{{scope.row.name}}</div>
                                        <div v-if="!scope.row.name">--</div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="type" label="类型" width="130">
                                    <template slot-scope="scope">
                                        <div v-if="scope.row.model"><p><a @click="model_click(scope.row.model)"></a></p></div>
                                        <div v-if="!scope.row.model && scope.row.type[1]">{{scope.row.type[0]+'/'+scope.row.type[1]}}</div>
                                        <div v-if="!scope.row.model && !scope.row.type[1]">{{scope.row.type[0]}}</div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="must" label="必填" width="100">
                                    <template slot-scope="scope">
                                        <div v-if="scope.row.required">是</div>
                                        <div v-if="!scope.row.required">否</div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="描述">
                                    <template slot-scope="scope">
                                        <div v-html="scope.row.description"></div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="示例值">
                                    <template slot-scope="scope">
                                        <div v-html="scope.row.sample"></div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="param_name" label="端点参数" width="120"></el-table-column>
                                <el-table-column label="操作" width="170">
                                    <template slot-scope="scope">
                                        <el-button
                                                size="mini"
                                                type="danger"
                                                @click="delete_param_in_panel(scope.$index, item.properties)">删除</el-button>
                                        <el-button
                                                size="mini"
                                                type="warning"
                                                @click="edit_param(scope.$index,scope.row,item.properties,'modal_param_content_model')">编辑</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <!--<Table no-data-text="暂无属性，请添加"-->
                                   <!--:data="item.properties"-->
                                   <!--:columns="column_model"-->
                            <!--&gt;</Table>-->
                            <Button class="margin-top-10" type="primary"
                                    @click="new_param(item.properties,'modal_param_content_model',true)">新增属性
                            </Button>
                        </Row>
                </el-collapse-item>

            </el-collapse>
        </Card>
        <modal_param_content_model ref="modal_param_content_model" :index="current_index" @data_update="data_update_model"></modal_param_content_model>
        <modal_param_content_model_external ref="modal_param_content_model_external" :index="current_index_external" @data_update="data_update_model_external"></modal_param_content_model_external>
    </Row>
</template>

<script>
    import loading from '../../my-components/loading/loading';
    import param_content from './api-components/param_content';
    import modal_param_content_model from './api-components/modal_param_content_model';
    import modal_param_content_model_external from './api-components/modal_param_content_model_external';
    import util from '../../../libs/util'
    export default {
        name: 'param-ref',
        components :{
            loading,
            param_content,
            modal_param_content_model,
            modal_param_content_model_external
        },
        data (){
            return {
                // json表格的表头
                column_model : [
                    // {
                    //     type: 'expand',
                    //     width: 50,
                    //     render: (h,params) => {
                    //         let create =this.$createElement
                    //         let dom = create(param_content,{
                    //                 ref : 'te'+params.index,
                    //                 props:{
                    //                     textEditorId : 'te'+params.index,
                    //                     newItem : params.row.newItem,
                    //                     index : params.index
                    //                 },
                    //                 on:{
                    //                     update_form_data : this.update_form_data_json
                    //                 }
                    //         })
                    //         return dom;
                    //     }
                    // },
                    {
                        title: '参数',
                        key: 'param',
                        width: 120,
                    },
                    {
                        title: '名称',
                        key: 'name',
                        width: 100,
                        render: (h, params) => {
                            if(params.row.name){
                                return h('div',params.row.name);
                            }else{
                                return h('div','--');
                            }
                        }
                    },
                    {
                        title: '类型',
                        key: 'type',
                        width: 130,
                        render: (h,params) => {
                            if(params.row.model){
                                let _this = this
                                return h('div', [
                                    h('p',[
                                        h('a',
                                            {
                                                on: {
                                                    click: function(){
                                                        _this.model_click(params.row.model);
                                                    }
                                                },
                                            },params.row.model)
                                    ])
                                ]);
                            }else{
                                return params.row.type[1]? h('div',params.row.type[0]+'/'+params.row.type[1]):h('div',params.row.type[0]);
                            }
                        }
                    },
                    {
                        title: '必填',
                        key: 'must',
                        width: 100,
                        render: (h, params) => {
                            if(params.row.model){
                                return h('div','--');
                            }else{
                                return params.row.required ? h('div','是') : h('div','否');
                            }

                        }
                    },
                    {
                        type:'html',
                        title: '描述',
                        key: 'description',
                        'min-width': 180,
                    },
                    {
                        type:'html',
                        title: '示例值',
                        key: 'sample',
                        // width: 100,
                        'min-width': 180,
                    },
                    {
                        title: '端点参数',
                        key: 'param_name',
                        width: 120
                    },
                    {
                        title: '操作',
                        align: 'center',
                        width: 170,
                        render: (h, params) => {
                            return h('div', [
                                h('el-button', {
                                    props: {
                                        type: 'danger',
                                        size: 'mini'
                                    },
                                    style:{
                                        'margin-right': '5px'
                                    },
                                    on: {
                                        click: () => {
                                            this.delete_param_in_panel(params.index,params);
                                        }
                                    }
                                }, '删除'),
                                h('el-button', {
                                    props: {
                                        type: 'warning',
                                        size: 'mini'
                                    },
                                    on: {
                                        click: () => {
                                            this.edit_param(params.index,params.row,'json','modal_param_content_model')
                                        }
                                    }
                                }, '编辑')
                            ]);
                        }

                    }],
                // 临时变量
                data_test  :{
                    "AuthIdCardResultDTO": {
                        "title": "响应结果",
                        "type": "object",
                        "properties": {
                            "fee": {
                                "title": "未命名",
                                "type": "number",
                                "format": "double",
                                "x-yop-end-param-name": "fee"
                            },
                            "cost": {
                                "title": "未命名",
                                "type": "number",
                                "format": "double",
                                "x-yop-end-param-name": "cost"
                            },
                            "name": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "name"
                            },
                            "photo": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "photo"
                            },
                            "remark": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "remark"
                            },
                            "status": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "status"
                            },
                            "address": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "address"
                            },
                            "orderId": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "orderId"
                            },
                            "authType": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "authType"
                            },
                            "encryptMsg": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "encryptMsg"
                            },
                            "idCardNumber": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "idCardNumber"
                            },
                            "invokeRecords": {
                                "title": "未命名",
                                "type": "array",
                                "items": {
                                    "type": "string"
                                },
                                "x-yop-end-param-name": "invokeRecords"
                            },
                            "requestFlowId": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "requestFlowId"
                            },
                            "authInterfaceId": {
                                "title": "未命名",
                                "type": "integer",
                                "format": "int32",
                                "x-yop-end-param-name": "authInterfaceId"
                            },
                            "bottomInterface": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "bottomInterface"
                            },
                            "externalOrderId": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "externalOrderId"
                            },
                            "channelReturnMsg": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "channelReturnMsg"
                            },
                            "authInterfaceType": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "authInterfaceType"
                            },
                            "channelReturnCode": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "channelReturnCode"
                            }
                        },
                        "description": "响应结果"
                    },
                    "RequestIdCardAuthDTO": {
                        "title": "方法签名第0个参数",
                        "type": "object",
                        "properties": {
                            "name": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "name"
                            },
                            "authType": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "authType"
                            },
                            "requestIP": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "requestIP"
                            },
                            "authMethod": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "authMethod"
                            },
                            "encryptMsg": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "encryptMsg"
                            },
                            "channelName": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "channelName"
                            },
                            "excludePhoto": {
                                "title": "未命名",
                                "type": "boolean",
                                "x-yop-end-param-name": "excludePhoto"
                            },
                            "idCardNumber": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "idCardNumber"
                            },
                            "requestFlowId": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "requestFlowId"
                            },
                            "requestSystem": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "requestSystem"
                            },
                            "requestSystemId": {
                                "title": "未命名",
                                "type": "integer",
                                "format": "int32",
                                "x-yop-end-param-name": "requestSystemId"
                            },
                            "requestCustomerId": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "requestCustomerId"
                            },
                            "requestIdentification": {
                                "title": "未命名",
                                "type": "string",
                                "x-yop-end-param-name": "requestIdentification"
                            },
                            "repositoryAvailableDays": {
                                "title": "未命名",
                                "type": "integer",
                                "format": "int32",
                                "x-yop-end-param-name": "repositoryAvailableDays"
                            }
                        },
                        "description": "方法签名第0个参数，请自行修改arg0等参数的名字"
                    }
                },
                // json表格的数据
                data_model : [],
                // 当前行
                current_index : -1,
                // 外部当前行
                current_index_external : -1,
                // 当前展示的模型
                current_model : [],
                // 当前被编辑模型
                current_modify_model : []
            }
        },
        methods : {
            // 页面初始化
            init(){
                // this.data_model = this.data_rehandler(this.data_test);
                // if(this.data_model && this.data_model.length > 0){
                //     this.current_model = [];
                //     this.current_model.push(this.data_model[0].param)
                // }
            },
            // 本地数据获取
            setRequestData (data) {
                this.data_model =[] ;
                this.data_model = this.data_rehandler(data);
                if(this.data_model && this.data_model.length > 0){
                    this.current_model = [];
                    this.current_model.push(this.data_model[0].param)
                }
            },
            // 设置当前查看model
            setCurrentModel (name) {
                this.current_model = [];
                this.current_model.push(name)
            },
            // model数据显示处理
            data_rehandler (data) {
                let result = []
                for(var i in data){
                    let temp = {
                        param : i,
                        type: []
                    }
                    for(var j in data[i]) {
                        if (j === 'properties') {
                            temp[j] = this.sub_data_rehandler(data[i][j]);
                        } else if (util.swagger_properties[j] === 'type') {
                            temp['type'][0] = data[i][j];
                        } else {
                            temp[util.swagger_properties[j]] = data[i][j];
                        }
                    }
                    result.push(temp)
                }
                return result
            },
            // 子数据处理
            sub_data_rehandler (data) {
                let result = []
                for(var i in data){
                    let temp = {
                        param: i,
                        type: []
                    }
                    for(var j in data[i]){
                        if(util.swagger_properties[j] === 'type'){
                            temp['type'][0] = data[i][j];
                            if(data[i][j] === 'object'){
                                if(data[i]['additionalProperties']){
                                    temp['type'][1] = 'map';
                                    temp['children'] = this.data_rehandler(data[i]['additionalProperties'])
                                }
                            }else if(data[i][j] === 'array'){
                                if(data[i]['items']){
                                    temp['type'][1] = 'array';
                                    temp['children'] = this.data_rehandler(data[i]['items'])
                                }
                            }
                        }else if (util.swagger_properties[j] === 'format'){
                            temp['type'][1] = data[i][j];
                        }else{
                            temp[util.swagger_properties[j]] = data[i][j];
                        }
                    }
                    result.push(temp);
                }
                return result;
            },
            // 新增参数
            new_param (data,panel,external){
                this.$refs[panel].current_panel_set('create_model');
                this.$refs[panel].$refs.form_data_left.resetFields();
                this.$refs[panel].$refs.form_data_right.resetFields();
                this.current_index = -1;
                this.$refs[panel].show();
                if(external){
                    this.current_modify_model = data;
                }
            },
            // 删除model
            delete_model (index){
                this.data_model.splice(index,1);
            },
            // 修改model
            edit_model (index,data,panel){
                // this.$refs[panel].$refs.form_data_left.resetFields();
                // this.$refs[panel].$refs.form_data_right.resetFields();
                this.$refs[panel].current_panel_set('modify_model');
                this.$refs[panel].current_index_set(index);
                this.$refs[panel].form_data_set(data[index]);
                this.current_index = -1;
                this.$refs[panel].show();
            },
            // 编辑元素
            edit_param (index,data,current_data,panel) {
                this.$refs[panel].$refs.form_data_left.resetFields();
                this.$refs[panel].$refs.form_data_right.resetFields();
                this.$refs[panel].current_panel_set('modify_model');
                this.$refs[panel].current_index_set(index);
                this.$refs[panel].form_data_set(data);
                this.current_modify_model = current_data;
                this.current_index = -1;
                this.$refs[panel].show();
            },
            delete_param_in_panel (index,params) {
                params.splice(index,1);
            },
            // 更新model的数据
            data_update_model (data,type,index) {
                if(type === 'create_model'){
                    //现在为不包含子项的处理 需要在提交的时候2次数据规范
                    this.current_modify_model.push(data)
                }else{
                    for(var i in data){
                        this.current_modify_model[index][i] = data[i]
                    }
                }
                this.$refs.modal_param_content_model.cancel();
            },
            // 更新model外部的数据
            data_update_model_external (data,type,index) {
                if(data.children && data.children.length > 0){
                    data['properties'] = data.children
                }else{
                    data['properties'] = []
                }
                if(type === 'create_model'){
                    //现在为不包含子项的处理 需要在提交的时候2次数据规范
                    this.data_model.push(data)
                }else{
                    for(var i in data){
                        this.data_model[index][i] = data[i]
                    }
                }
                this.$refs.modal_param_content_model_external.cancel();
            }
        },
        mounted () {
            this.init();
        },
    };
</script>

<style scoped>

</style>
