<style lang="less">
    @import '../../../styles/common.less';
    @import '../../../styles/loading.less';
    @import '../api_Mangement_Open.less';
    @import '../../regression-test/regression.less';
    /*html,body{*/
         /*width: 100%;*/
         /*height: 100%;*/
         /*overflow: scroll;*/
     /*}*/
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab{
        border-radius: 0;
        background: #fff;
    }
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active{
        border-top: 1px solid #3399ff;
    }
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active:before{
        content: '';
        display: block;
        width: 100%;
        height: 1px;
        background: #3399ff;
        position: absolute;
        top: 0;
        left: 0;
    }
    .red {
        border:1px solid red;
    }
</style>
<template>
    <div >
            <Row v-show="basicShow">
                <Row>
                    <Col span="12"   style="border:1px solid #eee;border-radius:6px;padding:3px;overflow:scroll">

                        基本信息
                        <Form ref="form_data_basics"  :model="form_data_basics" :label-width="120" inline>
                            <FormItem  class="width-100-perc" :class="form_data_basics.name != form_data_basics_src.name ?'red':''" label="API名称：" prop="name" >
                                <p>{{form_data_basics.name}}</p>
                            </FormItem>
                            <FormItem  class="width-100-perc" :class="form_data_basics.title != form_data_basics_src.title ?'red':''"  label="API标题：" prop="title">
                                <p>{{form_data_basics.title}}</p>
                            </FormItem>
                            <Row>
                            <FormItem label="API类型：" class="width-100-perc" :class="form_data_basics.apiType != form_data_basics_src.apiType ?'red':''"  prop="apiType">
                                <p>{{form_data_basics.apiType == "COMMON"?"普通":form_data_basics.apiType == "FILE_UPLOAD"?"文件上传":"文件下载"}}</p>
                            </FormItem>
                            </Row>
                            <Row>

                            <FormItem  class="width-100-perc" :class="form_data_basics.apiGroup != form_data_basics_src.apiGroup ?'red':''"  label="API分组：" prop="apiGroup">
                                <p >{{form_data_basics.apiGroup}}</p>
                            </FormItem>
                            </Row>
                            <Row>

                            </Row>

                            <FormItem  class="width-100-perc" :class="form_data_basics.description != form_data_basics_src.description ?'red':''"  label="描述：" prop="description">
                                <p v-html="form_data_basics.description"></p>
                            </FormItem>
                            
                        </Form>
                        <p>API选项</p>
                        <ApiOptionsDisplay v-if="form_data_basics.optionsRule" :optionsRule="form_data_basics.optionsRule" />
                        <p>安全需求</p>
                        <Form ref="form_data_security"  :model="form_data_security" :label-width="120" inline >
                            <FormItem label="请求路径：" class="width-50-perc" prop="requestUrl">
                                <p>{{form_data_security.inheritFlag?"继承自API分组":"自定义安全需求"}}</p>
                            </FormItem>
                            <safety-import-show ref="safetyImportShow"></safety-import-show>
                        </Form>
                        请求参数
                        <Form ref="form_data_request"  :model="form_data_request" :label-width="120" inline >

                            <Row>
                                <FormItem label="请求路径：" class="width-100-perc" :class="form_data_request.path != form_data_request_src.path ?'red':''" prop="path">
                                    <p>{{form_data_request.path}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="http方法：" class="width-100-perc" :class="form_data_request.httpMethod != form_data_request_src.httpMethod ?'red':''" prop="httpMethod">
                                    <p>{{form_data_request.httpMethod}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="是否加密：" class="width-100-perc" :class="form_data_request.encrypt != form_data_request_src.encrypt ?'red':''" prop="httpMethod">
                                    <p>{{form_data_request.encrypt ? '是' : '否'}}</p>
                                </FormItem>
                            </Row>

                            <Row  v-if="form_data_request.requestBody">
                                <!-- <p style="margin-left:27px;margin-bottom:20px;">请求体：</p> -->
                                <FormItem label="contentType：" class="width-100-perc"  :class="this.contentTypeRequest != this.contentTypeRequest_src ?'red':''" prop="contents">
                                    <p>{{this.contentTypeRequest}}</p>
                                </FormItem>
                                <FormItem label="入参请求模式：" class="width-100-perc"  :class="form_data_request.parameterHandlingType != form_data_request_src.parameterHandlingType ?'red':''" prop="description">
                                    <p>{{form_data_request.parameterHandlingType  === "MAPPING"?"映射":"透传"}}</p>
                                </FormItem>
                                <FormItem v-show="this.refRequest" label="content：" class="width-50-perc" :class="this.refRequest != this.refRequest_src ?'red':''" prop="description">
                                    <p>{{this.refRequest}}</p>
                                </FormItem>
                                <FormItem label="描述：" class="width-100-perc" :class="form_data_request.requestBody.description != form_data_request_src.requestBody.description ?'red':''" prop="description">
                                    <p>{{form_data_request.requestBody.description}}</p>
                                </FormItem>
                                <FormItem label="content：" class="width-100-perc" >
                                    <p></p>
                                </FormItem>
                            </Row>
                        </Form>
                    </Col>

                    <Col  span="12"  style="border:1px solid #eee;border-radius:6px;padding:3px;overflow:scroll">

                        原基本信息
                        <Form ref="form_data_basics_src"  :model="form_data_basics_src" :label-width="120" inline>
                            <FormItem  class="width-100-perc" :class="form_data_basics.name != form_data_basics_src.name ?'red':''" label="API名称：" prop="name" >
                                <p>{{form_data_basics_src.name}}</p>
                            </FormItem>
                            <FormItem  class="width-100-perc" :class="form_data_basics.title != form_data_basics_src.title ?'red':''"  label="API标题：" prop="title">
                                <p>{{form_data_basics_src.title}}</p>
                            </FormItem>
                            <Row>
                            <FormItem label="API类型：" class="width-100-perc" :class="form_data_basics.apiType != form_data_basics_src.apiType ?'red':''"  prop="apiType">
                                <p>{{form_data_basics_src.apiType == "COMMON"?"普通":form_data_basics_src.apiType == "FILE_UPLOAD"?"文件上传":"文件下载"}}</p>
                            </FormItem>
                            </Row>
                            <Row>

                            <FormItem  class="width-100-perc" :class="form_data_basics.apiGroup != form_data_basics_src.apiGroup ?'red':''"  label="API分组：" prop="apiGroup">
                                <p >{{form_data_basics_src.apiGroup}}</p>
                            </FormItem>
                            </Row>
                            <Row>

                            </Row>

                            <FormItem  class="width-100-perc" :class="form_data_basics.description != form_data_basics_src.description ?'red':''"  label="描述：" prop="description">
                                <p v-html="form_data_basics_src.description"></p>
                            </FormItem>
                            
                        </Form>
                        <p>API选项</p>
                        <ApiOptionsDisplay v-if="form_data_basics_src.optionsRule" :optionsRule="form_data_basics_src.optionsRule" />
                        <p>原安全需求</p>
                        <Form ref="form_data_security_src"  :model="form_data_security_src" :label-width="120" inline >
                            <FormItem label="请求路径：" class="width-50-perc" prop="requestUrl">
                                <p>{{form_data_security_src.inheritFlag?"继承自API分组":"自定义安全需求"}}</p>
                            </FormItem>
                            <safety-import-show ref="safetyImportShow_src"></safety-import-show>
                        </Form>
                        原请求参数
                        <Form ref="form_data_request_src"  :model="form_data_request_src" :label-width="120" inline >

                            <Row>
                                <FormItem label="请求路径：" class="width-100-perc" :class="form_data_request.path != form_data_request_src.path ?'red':''" prop="path">
                                    <p>{{form_data_request_src.path}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="http方法：" class="width-100-perc" :class="form_data_request.httpMethod != form_data_request_src.httpMethod ?'red':''" prop="httpMethod">
                                    <p>{{form_data_request_src.httpMethod}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="是否加密：" class="width-100-perc" :class="form_data_request.encrypt != form_data_request_src.encrypt ?'red':''" prop="encrypt">
                                    <p>{{form_data_request_src.encrypt ? '是' : '否'}}</p>
                                </FormItem>
                            </Row>

                           <Row  v-if="form_data_request_src.requestBody">
                                <!-- <p style="margin-left:27px;margin-bottom:20px;">请求体：</p> -->
                                <FormItem label="contentType：" class="width-100-perc"  :class="this.contentTypeRequest != this.contentTypeRequest_src ?'red':''" prop="contents">
                                    <p>{{this.contentTypeRequest_src}}</p>
                                </FormItem>
                                <FormItem label="入参请求模式：" class="width-100-perc"  :class="form_data_request.parameterHandlingType != form_data_request_src.parameterHandlingType ?'red':''" prop="description">
                                    <p>{{form_data_request_src.parameterHandlingType  === "MAPPING"?"映射":"透传"}}</p>

                                </FormItem>
                                <FormItem v-show="this.refRequest_src" label="content：" class="width-50-perc" :class="this.refRequest != this.refRequest_src ?'red':''" prop="description">
                                    <p>{{this.refRequest_src}}</p>
                                </FormItem>
                                <FormItem label="描述：" class="width-100-perc" :class="form_data_request.requestBody.description != form_data_request_src.requestBody.description ?'red':''" prop="description">
                                    <p>{{form_data_request_src.requestBody.description}}</p>
                                </FormItem>
                                <FormItem label="content：" class="width-100-perc" >
                                    <p></p>
                                </FormItem>
                            </Row>
                        </Form>
                    </Col>
                </Row>
                    <Row span="24"   style="border:1px solid #eee;border-radius:6px;padding:3px;overflow:scroll">
                        <Form ref="form_data_request"  :model="form_data_request" :label-width="120" inline >
                            <Row  v-if="form_data_request.requestBody">
                                <!-- <FormItem v-show="this.schemaRequest&&this.showOneDiff" class="width-100-perc" :class="this.schemaRequest != this.schemaRequest_src ?'red':''" label="content：" prop="contentType">
                                    <pre>{{this.schemaRequest}}</pre>
                                    <pre v-for="item in this.schemaRequest" v-html="item"></pre>
                                </FormItem> -->
                                <div id="codeMirror" class="codeMirror"></div>
                            </Row>
                        </Form>
                    </Row>
                    <Row span="24"   style="border:1px solid #eee;border-radius:6px;padding:3px;overflow:scroll">
                    
                        <Col span="12"   style="border:1px solid #eee;border-radius:6px;padding:3px;overflow:scroll">
                        <Form ref="form_data_request"  :model="form_data_request" :label-width="120" inline >
                            <Row  v-if="form_data_request.requestBody">
                                <Row>
                                    <p style="font-size: 12px; padding-left: 53px;line-height:30px;">example：</p>
                                    <Table id='table_1' border ref="selection" :columns="request_example_List_display" :data="data_request_example_List"></Table>
                                </Row>
                            </Row>
                            <Row  v-show="form_data_request.parameters">
                                <!-- <FormItem label="contentType：" class="width-100-perc" prop="contents">
                                    <p>{{this.contentTypeRequest}}</p>
                                </FormItem> -->
                                <FormItem label="入参请求模式：" class="width-100-perc" prop="description">
                                    <p>{{form_data_request.parameterHandlingType  === "MAPPING"?"映射":"透传"}}</p>

                                </FormItem>
                                <Row>
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">请求参数：</p>
                                    <Table id='table_1' border ref="selection" :columns="request_parameters_List_display" :data="form_data_request.parameters"></Table>
                                </Row>
                            </Row>
                        </Form>
                        <p>响应参数</p>
                        <Form ref="form_data_response"  :model="form_data_response" :label-width="120" inline >

                            <Col span="24">
                            <Row>
                                <FormItem label="返回HttpCode：" class="width-50-perc" :class="form_data_response.httpCode != form_data_response_src.httpCode ?'red':''" prop="httpCode">
                                    <p>{{form_data_response.httpCode}}</p>
                                </FormItem>
                            </Row>
                            <Row v-show="form_data_response.httpCode ==200">
                                <FormItem label="返回contentType：" class="width-50-perc" :class="form_data_response.contentType != form_data_response_src.contentType ?'red':''" prop="contentType">
                                    <p>{{form_data_response.contentType}}</p>
                                </FormItem>
                            </Row>
                            <Row  v-show="this.schemaResponse">
                                <FormItem label="返回content：" class="width-50-perc" :class="form_data_response.schemaResponse != form_data_response_src.schemaResponse_src ?'red':''" prop="description">
                                    <p>{{this.schemaResponse}}</p>
                                </FormItem>
                            </Row>
                            <Row  v-show="form_data_response.httpCode == '200'">
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">example：</p>
                                    <Table id='table_1' border ref="selection" :columns="response_example_List_display" :data="data_response_example_List"></Table>
                            </Row>
                            <Row  v-show="form_data_response.httpCode !== '200'">
                                    <p style="font-size: 12px; padding-left: 53px;line-height:30px;">返回headers：</p>
                                    <Table id='table_1' border ref="selection" :columns="columns_headerList" :data="data_headerList"></Table>
                            </Row>
                            </Col>
                        </Form>
                        <p>后端服务</p>
                        <Form ref="form_data_endpoint"  :model="form_data_endpoint" :label-width="120" inline >

                            <Col span="24">
                            <Row>
                                <FormItem label="后端服务类型：" class="width-50-perc" :class="form_data_endpoint.type != form_data_endpoint_src.type ?'red':''" prop="type">
                                    <p>{{form_data_endpoint.type}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="后端服务：" class="width-50-perc" :class="form_data_endpoint.serviceName != form_data_endpoint_src.serviceName ?'red':''" prop="serviceName">
                                    <p>{{form_data_endpoint.serviceName}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="路径：" class="width-50-perc" :class="form_data_endpoint.path != form_data_endpoint_src.path ?'red':''" prop=" path">
                                    <p>{{form_data_endpoint.path}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="链接超时：" class="width-50-perc" :class="form_data_endpoint.connectionTimeout != form_data_endpoint_src.connectionTimeout ?'red':''" prop="connectionTimeout">
                                    <p>{{form_data_endpoint.connectionTimeout}}ms</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="读取超时：" class="width-50-perc" :class="form_data_endpoint.readTimeout != form_data_endpoint_src.readTimeout ?'red':''" prop="readTimeout">
                                    <p>{{form_data_endpoint.readTimeout}}ms</p>
                                </FormItem>
                            </Row>
                            <Row  v-show="this.form_data_request.parameters">   
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">后端服务参数映射：</p>
                                <Table  border ref="selection" :columns="columns_service_params_mapping_desc" :data="resultMappingList"></Table>
                                <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh_mapping"></Page>
                                </Tooltip> -->
                            </Row>
                            <Row>
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">常量参数：</p>
                                    <Table  border ref="selection" :columns="columns_Constant_desc" :data="form_data_endpoint.constantParameters"></Table>
                            </Row>
                            <Row>
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">系统参数：</p>
                                    <Table  border ref="selection" :columns="columns_System_desc" :data="form_data_endpoint.systemParameters"></Table>
                            </Row>
                            </Col>
                        </Form>
                        <p>回调</p>
                        <Form ref="form_data_callbacks" :label-width="120" inline >

                            <Col span="24">
                            <Row>
                                <FormItem label="回调：" class="width-50-perc" prop="type">
                                    <p v-for="(item, index) in form_data_callbacks" :key="index">{{item}}</p>
                                </FormItem>
                            </Row>
                            </Col>
                        </Form>
                        <Form  :label-width="120" style="height:30px;" inline >
                            <Col span="24">
                            </Col>
                        </Form>
                    </Col>

                    <Col  span="12"  style="border:1px solid #eee;border-radius:6px;padding:3px;overflow:scroll">
                        <Form ref="form_data_request_src"  :model="form_data_request_src" :label-width="120" inline >

                            <Row  v-if="form_data_request_src.requestBody">
                                <Row>
                                    <p style="font-size: 12px; padding-left: 53px;line-height:30px;">example：</p>

                                    <Table id='table_1' border ref="selection" :columns="request_example_List_display_src" :data="data_request_example_List_src"></Table>
                                </Row>
                            </Row>
                            <Row  v-show="form_data_request_src.parameters">
                                <!-- <FormItem label="contentType：" class="width-100-perc" prop="contents">
                                    <p>{{this.contentTypeRequest}}</p>
                                </FormItem> -->
                                <FormItem label="入参请求模式：" class="width-100-perc" prop="description">
                                    <p>{{form_data_request_src.parameterHandlingType  === "MAPPING"?"映射":"透传"}}</p>

                                </FormItem>
                                <Row>
                                    <p style="font-size: 12px; padding-left: 53px;line-height:30px;">请求参数：</p>

                                    <Table id='table_1' border ref="selection" :columns="request_parameters_List_display" :data="form_data_request_src.parameters"></Table>
                                </Row>
                            </Row>
                        </Form>
                        <p>原响应参数</p>
                        <Form ref="form_data_response_src"  :model="form_data_response_src" :label-width="120" inline >

                            <Col span="24">
                            <Row>
                                <FormItem label="返回HttpCode：" class="width-50-perc" :class="form_data_response.httpCode != form_data_response_src.httpCode ?'red':''" prop="httpCode">
                                    <p>{{form_data_response_src.httpCode}}</p>
                                </FormItem>
                            </Row>
                            <Row v-show="form_data_response_src.httpCode == 200">
                                <FormItem label="返回contentType：" class="width-50-perc" :class="form_data_response.contentType != form_data_response_src.contentType ?'red':''" prop="contentType">
                                    <p>{{form_data_response_src.contentType}}</p>
                                </FormItem>
                            </Row>
                            <Row  v-show="this.schemaResponse_src">
                                <FormItem label="返回content：" class="width-50-perc" :class="form_data_response.schemaResponse != form_data_response_src.schemaResponse_src ?'red':''" prop="description">
                                    <p>{{this.schemaResponse_src}}</p>
                                </FormItem>
                            </Row>
                            <Row  v-show="form_data_response_src.httpCode == '200'">
                                    <p style="font-size: 12px; padding-left: 53px;line-height:30px;">example：</p>

                                    <Table id='table_1' border ref="selection" :columns="response_example_List_display_src" :data="data_response_example_List_src"></Table>
                            </Row>
                            <Row  v-show="form_data_response_src.httpCode !== '200'">
                                    <p style="font-size: 12px; padding-left: 53px;line-height:30px;">返回headers：</p>

                                    <Table id='table_2' border ref="selection" :columns="columns_headerList" :data="data_headerList"></Table>
                            </Row>
                            </Col>
                        </Form>
                        <p>原后端服务</p>
                        <Form ref="form_data_endpoint_src"  :model="form_data_endpoint_src" :label-width="120" inline >

                            <Col span="24">
                            <Row>
                                <FormItem label="后端服务类型：" class="width-50-perc" :class="form_data_endpoint.type != form_data_endpoint_src.type ?'red':''" prop="type">
                                    <p>{{form_data_endpoint_src.type}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="后端服务：" class="width-50-perc" :class="form_data_endpoint.serviceName != form_data_endpoint_src.serviceName ?'red':''" prop="serviceName">
                                    <p>{{form_data_endpoint_src.serviceName}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="路径：" class="width-50-perc" :class="form_data_endpoint.path != form_data_endpoint_src.path ?'red':''" prop=" path">
                                    <p>{{form_data_endpoint_src.path}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="链接超时：" class="width-50-perc" :class="form_data_endpoint.connectionTimeout != form_data_endpoint_src.connectionTimeout ?'red':''" prop="connectionTimeout">
                                    <p>{{form_data_endpoint_src.connectionTimeout}}ms</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="读取超时：" class="width-50-perc" :class="form_data_endpoint.readTimeout != form_data_endpoint_src.readTimeout ?'red':''" prop="readTimeout">
                                    <p>{{form_data_endpoint_src.readTimeout}}ms</p>
                                </FormItem>
                            </Row>
                            <Row v-show="this.form_data_request_src.parameters">   
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">后端服务参数映射：</p>
                                <Table  border ref="selection" :columns="columns_service_params_mapping_desc" :data="resultMappingList_src"></Table>
                                <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh_mapping"></Page>
                                </Tooltip> -->
                            </Row>
                            <Row>
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">常量参数：</p>
                                    <Table  border ref="selection" :columns="columns_Constant_desc" :data="form_data_endpoint_src.constantParameters"></Table>
                            </Row>
                            <Row>
                                <p style="font-size: 12px; padding-left: 53px;line-height:30px;">系统参数：</p>
                                    <Table  border ref="selection" :columns="columns_System_desc" :data="form_data_endpoint_src.systemParameters"></Table>
                            </Row>
                            </Col>
                        </Form>
                        <p>原回调</p>
                        <Form ref="form_data_callbacks" :label-width="120" inline >

                            <Col span="24">
                            <Row>
                                <FormItem label="回调：" class="width-50-perc" prop="type">
                                    <p v-for="(item, index) in form_data_callbacks_src" :key="index">{{item}}</p>
                                </FormItem>
                            </Row>
                            </Col>
                        </Form>
                        <Form  :label-width="120" style="height:30px;" inline >
                            <Col span="24">
                            </Col>
                        </Form>
                    </Col>
                    </Row>

                    
                    
                    
                    
            </Row>
        <model_detail_page ref="model_detail_page" @data_update="data_update_model"></model_detail_page>

            <!-- <loading :show="show_loading_basic"></loading> -->
    </div>
</template>

<script>
/* eslint-disable camelcase */
import loading from '../../my-components/loading/loading'
import safetyImportShow from './safety_import_show'
import model_detail_page from './model_detail_page'

import '../../../libs/codeMirror/lib/codemirror.css'
import '../../../libs/codeMirror/addon/merge.css'
import CodeMirror from '../../../libs/codeMirror/lib/codemirror';
require('../../../libs/codeMirror/mode/xml')
require('../../../libs/codeMirror/addon/merge')
require('../../../libs/codeMirror/addon/fresh')

import ApiOptionsDisplay from '../modals/apiOptionsDisplay.vue'

export default {
  name: 'api_basics',
  components: {
    loading,
    safetyImportShow,
    model_detail_page,
    ApiOptionsDisplay

  },
  data () {
    return {

      // 参数
      showReturn: true,
      requestUrl: '#app.callbackUrl',
      httpMethod: 'POST',
      form_data: {
        requestUrl: '#app.callbackUrl',
        httpMethod: 'POST',
        description: '',
        model: 'application/json'
      },
      form_data_src: {
        requestUrl: '#app.callbackUrl',
        httpMethod: 'POST',
        description: '',
        model: 'application/json'
      },
      contentType_list: [{value: 'application/json', label: 'application/json'}],
      data_spi_example_List: [
      ],

      data_spi_example_List_src: [],
      // 响应参数example
      data_response_example_List: [],
      response_example_List_display: [
        {
          title: '名称',
          key: 'name',
          align: 'center'
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '值',
          align: 'center',
          key: 'value'
        }
      ],
      data_request_example_List: [],
      request_parameters_List_display: [
        {
          title: '名称',
          align: 'center',
          key: 'name',
          render: (h, params) => {
            if (params.row.name) {
              return h('div', params.row.name);
            } else {
              return h('div', '--');
            }
          }
        },
        {
          title: '类型-格式',
          key: 'type',
          align: 'center',
          render: (h, params) => {
            return h('div', [h('p', JSON.parse(params.row.schema).type),
              h('p', JSON.parse(params.row.schema).format)]);
          }
          // render: (h,params) => {
          //     if(params.row.model){
          //         let _this = this
          //         return h('div', [
          //             h('p',[
          //                 h('a',
          //                     {
          //                         on: {
          //                             click: function(){
          //                                 _this.model_click(params.row.model);
          //                             }
          //                         },
          //                     },params.row.model)
          //             ])
          //         ]);
          //     }else{
          //         return params.row.type[1]? h('div',params.row.type[0]+'/'+params.row.type[1]):h('div',params.row.type[0]);
          //     }
          // }
        },
        {
          align: 'center',
          title: '必需',
          key: 'must',
          render: (h, params) => {
            // if(params.row.model){
            //     return h('div','--');
            // }else{
            return params.row.required ? h('div', '是') : h('div', '否');
            // }
          }
        },
        {
          title: '废弃',
          align: 'center',
          key: 'deprecated',
          render: (h, params) => {
            // if(params.row.model){
            //     return h('div','--');
            // }else{
            return params.row.deprecated ? h('div', '是') : h('div', '否');
            // }
          }
        },
        {
          title: '描述',
          key: 'description',
          align: 'center'

        },
        {
          title: '约束',
          align: 'center',
          render: (h, params) => {
            var minimum, maximum, maxLength, minLength, notMin, notMax, maxItems, minItems, pattern
            notMin = params.row.exclusiveMinimum ? '不包含' : '包含'
            notMax = params.row.exclusiveMaximum ? '不包含' : '包含'
            minimum = params.row.minimum ? `最小值：${params.row.minimum}(${notMin})` : ''
            maximum = params.row.maximum ? `最小值：${params.row.maximum}(${notMax})` : ''
            maxLength = params.row.maxLength ? `最大长度：${params.row.maxLength}` : ''
            minLength = params.row.minLength ? `最大长度：${params.row.minLength}` : ''
            maxItems = params.row.maxItems ? `最大数量：${params.row.maxItems}` : ''
            minItems = params.row.minItems ? `最小数量：${params.row.minItems}` : ''
            pattern = params.row.pattern ? `正则${params.row.pattern}` : ''
            return h('div', [
              h('p', minimum),
              h('p', maximum),
              h('p', maxLength),
              h('p', minLength),
              h('p', maxItems),
              h('p', minItems),
              h('p', pattern)
            ]);
          }
        },
        {
          title: '操作',
          align: 'center',
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.detail_param(params.index, params.row, 'detail_model_show', 'model_detail_page')
                  }
                }
              }, '详情')
            ]);
          }

        }

        // {
        //     title: '最大值/排除最大值',
        //     align:"center",
        //     render: (h, params) => {
        //         return h('div', [h('p', params.row.maximum),
        //             h('p', params.row.exclusiveMaximum)]);
        //     }
        // },
        // {
        //     type:'html',
        //     title: '最小值/排除最小值',
        //     align:"center",
        //     render: (h, params) => {
        //         return h('div', [h('p', params.row.minimum),
        //             h('p', params.row.exclusiveMinimum)]);
        //     }
        // },
        // {
        //     type:'html',
        //     title: '最大长度',
        //     key: 'maxLength',
        //     align:"center"
        // },
        // {
        //     type:'html',
        //     title: '最小长度',
        //     key: 'minLength',
        //     align:"center"
        // },
        // {
        //     type:'html',
        //     title: '最大数目',
        //     key: 'maxItems',
        //     align:"center"
        // },
        // {
        //     type:'html',
        //     title: '最小数目',
        //     key: 'minItems',
        //     align:"center"
        // },
        // {
        //     title: '端点参数',
        //     key: 'param_name',
        //     width: 120
        // },
      ],
      request_example_List_display: [
        {
          title: '名称',
          key: 'name',
          align: 'center'
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '值',
          align: 'center',
          key: 'value'
        }
      ],
      //
      data_response_example_List_src: [],
      response_example_List_display_src: [
        {
          title: '名称',
          key: 'name',
          align: 'center'
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '值',
          align: 'center',
          key: 'value'
        }
      ],
      data_request_example_List_src: [],
      request_example_List_display_src: [
        {
          title: '名称',
          key: 'name',
          align: 'center'
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '值',
          align: 'center',
          key: 'value'
        }
      ],
      spi_example_List_display: [
        {
          title: '名称',
          key: 'name',
          align: 'center'
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
          title: '描述',
          align: 'center',
          key: 'description'
        },
        {
          title: '值',
          align: 'center',
          key: 'value'
        }
      ],
      modal_Show_example: false,
      form_data_example: {
        name: '',
        title: '',
        description: '',
        value: ''
      },
      data_apiGroup_List: [],
      /**
                * 基础界面变量
                */
      // api 请求方式数据绑定
      data_select_apiMethod: '',
      // api 请求方式下拉框总体数据
      data_apiMethod_list: [],
      // 当前参数tab页
      current_param_tab: 'request_params',
      // api contentType数据绑定
      data_select_apiContent: [],
      // api contentType下拉框总体数据
      data_apiContent_list: [],
      // 基础信息加载数据绑定
      show_loading_basic: false,
      // 显示收起内容
      basicShow: true,
      // 展开收起按钮文字内容绑定
      button_Content: '收起',
      // api分组数据绑定
      api_group_model: '',
      // api分组disabled
      api_group_disabled: false,
      // method post disabled
      request_post_disabled: false,
      // method get disabled
      request_get_disabled: false,
      // 接口uri disabled
      interface_uri_disabled: false,
      // api分组数据列表
      api_group_list: [],
      // api类型选择数据绑定
      data_select_apiType: '',
      // api类型
      data_apiType_List: [],
      // 请求方式绑定第一个参数是post第二个参数是get
      request_post: [false, false],
      // 当前tab页绑定的参数
      tabNow: 'basic',
      // 保存的数据参数

      // 接口uri数据绑定
      interface_uri: '',
      // 接口名称数据绑定
      interface_name: '',
      // 当前端点apiUri内容
      current_content_apiUri: '',
      // 基本类型
      cascader_type: [],
      // 当前apiId
      current_spiId: '',
      current_spiVersion: '',
      // 创建还是修改
      current_status: '',
      //
      check_result: true,
      /**
                * 新建弹窗变量
                */
      // 窗口显示绑定
      modal_Show_register: false,
      // 描述禁用
      disabled_description: false,
      // 端点url数据绑定
      endpoint_Url: '',
      // 显示端点url部分
      show_pointUrl: false,
      // 端点类名数据绑定
      endpoint_Name: '',
      // 是否幂等数据绑定
      idempotent: '否',
      // 端点url自定义
      pointUrl_user_defined: false,
      // 适配类型下拉框数据绑定
      data_select_type: 'TRANSFORM',
      // 适配类型下拉框数据
      data_type_List: [
        {
          value: 'TRANSFORM',
          label: '转换'
        },
        {
          value: 'LOCAL',
          label: '本地'
        },
        {
          value: 'PASSTHROUGH',
          label: '透传'
        }
      ],
      // 端点协议下拉框数据绑定
      data_select_EndpointProtocol: 'HESSIAN',
      // 端点协议下拉框数据
      data_EndpointProtocol_List: [
        {
          value: 'HESSIAN',
          label: 'hessian'
        }
      ],
      // 端点方法下拉框数据绑定
      data_select_EndpointMethod: '',
      // 端点方法下拉框数据
      data_EndpointMethod_List: [],
      // 当前端点名称内容
      current_content: '',
      // 当前端点url内容
      current_content_url: '',
      // url校验结果
      pass_url: true,
      data_api_model_list: [],
      editExample: false,
      row_index: 0,
      result: {},

      form_data_basics: {
        name: '',
        title: '',
        apiType: '',
        apiGroup: '',
        description: '',
        options: {IDEMPOTENT: false}
      },
      form_data_security: {
        inheritFlag: false,
        securities: {}
      },
      form_data_request: {
        parameterHandlingType: '',
        path: '',
        httpMethod: '',
        requestBody: {
          contents: {}
        }
      },
      contentTypeRequest: '',
      schemaRequest: '',
      refRequest: '',
      form_data_response: {
        httpCode: '',
        contentType: '',
        content: ''
      },
      schemaResponse: '',
      form_data_endpoint: {
        type: '',
        serviceName: '',
        path: '',
        connectionTimeout: '',
        readTimeout: '',
        constantParameters: [],
        systemParameters: []
      },
      columns_service_params_mapping_desc: [
        {
          title: '后端参数名称',
          align: 'center',
          render: (h, params) => {
            return h('div', params.row.backendName || params.row['x-yop-apigateway-backend-name']);
          }
        },
        {
          title: '后端参数位置',
          align: 'center',
          render: (h, params) => {
            return h('div', params.row.location);
          }

        }, {
          title: '对应入参名称',
          key: 'name',
          align: 'center'

        },
        {
          title: '对应入参位置',
          align: 'center',
          render: (h, params) => {
            return h('div', params.row.location);
          }

        }, {
          title: '对应入参类型',
          align: 'center',
          render: (h, params) => {
            var type = ''
            if (params.row.schema) {
              type = JSON.parse(params.row.schema).type
            }
            return h('div', type);
          }

        }

      ],
      columns_Constant_desc: [
        {title: '后端参数名称',
          align: 'center',
          key: 'backendName',
          render: (h, params) => {
            return h('div', params.row.backendName);
          }
        },
        {
          title: '参数值',
          align: 'center',
          key: 'value'
        }, {
          align: 'center',
          title: '参数位置',
          key: 'location'

        }, {
          title: '描述',
          align: 'center',
          key: 'description'
        }

      ],

      columns_System_desc: [
        {
          title: '系统参数',
          key: 'systemName',
          align: 'center'
        },
        {
          title: '后端参数名称',
          align: 'center',
          key: 'backendName'
        }, {
          align: 'center',
          title: '参数位置',
          key: 'location'
        }

      ],
      form_data_callbacks: [

      ],
      bizOrderVariable: {
        'part': '',
        'location': '',
        'variableName': ''
      },
      sensitiveVariables: [{
        'part': '',
        'location': '',
        'variableName': ''
      }],

      columns_headerList: [
        // {
        //     type: 'selection',
        //     width: 60,
        //     align: 'center'
        // },
        {
          title: '参数名称',
          key: 'header_Name'
        },
        {
          title: '参数格式/类型',
          key: 'header_URI'
        }, {
          title: '描述',
          key: 'header_des'
        }
      ],
      data_headerList: [
        {
          header_Name: 'Location',
          header_URI: 'string/string',
          header_des: '重定向地址'
        }
      ],

      form_data_basics_src: {
        name: '',
        title: '',
        apiType: 'COMMON',
        apiGroup: '',
        description: '',
        options: {IDEMPOTENT: false}
      },
      form_data_security_src: {
        inheritFlag: false,
        securities: {}
      },
      form_data_request_src: {
        parameterHandlingType: '',
        path: '',
        httpMethod: '',
        requestBody: {
          contents: {}
        }
      },
      contentTypeRequest_src: '',
      schemaRequest_src: '',
      refRequest_src: '',
      form_data_response_src: {
        httpCode: '',
        contentType: '',
        content: ''
      },
      schemaResponse_src: '',
      form_data_endpoint_src: {
        type: '',
        serviceName: '',
        path: '',
        connectionTimeout: '',
        readTimeout: '',
        systemParameters: [],
        constantParameters: []
      },
      form_data_callbacks_src: [

      ],
      bizOrderVariable_src: {
        'part': '',
        'location': '',
        'variableName': ''
      },
      sensitiveVariables_src: [{
        'part': '',
        'location': '',
        'variableName': ''
      }],

      columns_headerList_src: [
        // {
        //     type: 'selection',
        //     width: 60,
        //     align: 'center'
        // },
        {
          title: '参数名称',
          'min-width': 100,
          key: 'header_Name'
        },
        {
          title: '参数格式/类型',
          width: 250,
          key: 'header_URI'
        }, {
          title: '描述',
          width: 250,
          key: 'header_des'
        }
      ],
      data_headerList_src: [
        {
          header_Name: 'Location',
          header_URI: 'string/string',
          header_des: '重定向地址'
        }
      ],
      showOneDiff: false,
      resultMappingList: [],
      resultMappingList_src: []

    }
  },
  methods: {
    /**
             * 基础界面方法
             */
    // 初始化函数
    light_diff (a, b) {
      var c1 = []
      var c2 = []
      var color = '#555'
      var index_a = 0
      var index_b = 0
      var big_param = a;
      var other_param = b;
      a = a.split('\n')
      b = b.split('\n')
      if (a.length > b.length) {
        big_param = a;
        other_param = b;
      } else {
        big_param = b;
        other_param = a;
      }
      for (var indexB in big_param) {
        if (b[indexB] !== a[indexB]) {
          color = '#ed3f14'
        } else {
          color = '#555'
        }
        var aI = big_param[indexB]
        if (aI === undefined) {
          aI = ''
        } else {
          var d = aI.split('')
          for (var j in d) {
            if (d[j] !== ' ') {
              index_a = j
              break;
            }
            j++
          }
          while (index_a > 0) {
            aI = ' ' + aI
            index_a--
          }
        }
        aI = '<p style="color:' + color + '">' + aI + '</p>'
        c1.push(aI);

        var bI = other_param[indexB]
        if (bI === undefined) {
          bI = ''
        } else {
          var e = bI.split('')
          for (var m in e) {
            if (e[m] !== ' ') {
              index_b = m
              break;
            }
            m++
          }
          while (index_b > 0) {
            bI = ' ' + bI
            index_b--
          }
        }
        bI = '<p style="color:' + color + '">' + bI + '</p>'
        c2.push(bI)
      }
      a = c1
      b = c2
      var c = [c1, c2]
      return c
    },
    setResult (val, index) {
      var that = this
      var api = val.api;

      this.form_data_basics = api.basic
      if (api.security) {
        this.form_data_security = api.security
      }
      this.$refs.safetyImportShow.modal_show(api.request.path, api.basic.apiGroup, 'description', api.security)

      this.form_data_request = api.request

      if (this.form_data_request.requestBody) {
        this.contentTypeRequest = Object.keys(this.form_data_request.requestBody.contents)[0]
        var schemaRequestTemp = JSON.parse(this.form_data_request.requestBody.contents[this.contentTypeRequest].schema);
        if (schemaRequestTemp.$ref) {
          this.refRequest = schemaRequestTemp.$ref.split('/').pop()
        }
        this.schemaRequest = this.syntaxHighlight(schemaRequestTemp)
        this.data_request_example_List = this.form_data_request.requestBody.contents[this.contentTypeRequest].examples
      }
      this.resultMappingList = []
      if (this.form_data_request.parameters) {
        for (let i in this.form_data_request.parameters) {
          if (this.form_data_request.parameters[i]['backendName'] || this.form_data_request.parameters[i]['x-yop-apigateway-backend-name']) {
            this.resultMappingList.push(this.form_data_request.parameters[i])
          }
        }
      }

      this.form_data_response = api.response
      if (api.response.content) {
        var schemaResponseTemp = JSON.parse(api.response.content.schema);
        if (schemaResponseTemp.$ref) {
          this.schemaResponse = schemaResponseTemp.$ref.split('/').pop()
        }
        var schema = ''
        if (api.response.content.schema) {
          schema = api.response.content.schema
        }
        if (JSON.parse(schema).$ref) {
          this.form_data_response.content.schema = JSON.parse(schema).$ref.split('/').pop()
        }
        this.data_response_example_List = api.response.content.examples
      }
      if (api.endpoint) {
        this.form_data_endpoint = api.endpoint
      }
      this.form_data_callbacks = api.callbacks

      var srcApi = val.srcApi
      this.form_data_basics_src = srcApi.basic
      if (srcApi.security) {
        this.form_data_security_src = srcApi.security
      }
      this.$refs.safetyImportShow_src.modal_show(srcApi.request.path, srcApi.basic.apiGroup, 'description', srcApi.security)

      this.form_data_request_src = srcApi.request
      if (this.form_data_request_src.requestBody) {
        this.contentTypeRequest_src = Object.keys(this.form_data_request_src.requestBody.contents)[0]
        var schemaRequestTemp_src = JSON.parse(this.form_data_request_src.requestBody.contents[this.contentTypeRequest_src].schema);
        if (schemaRequestTemp_src.$ref) {
          this.refRequest_src = schemaRequestTemp_src.$ref.split('/').pop()
        }
        this.schemaRequest_src = this.syntaxHighlight(schemaRequestTemp_src)
        this.data_request_example_List_src = this.form_data_request_src.requestBody.contents[this.contentTypeRequest].examples
        setTimeout(function () {
          CodeMirror.k_init('codeMirror', that.schemaRequest_src, that.schemaRequest, index);
        }, 500)
      }

      this.resultMappingList_src = []
      if (this.form_data_request_src.parameters) {
        for (let i in this.form_data_request_src.parameters) {
          if (this.form_data_request_src.parameters[i]['backendName'] || this.form_data_request_src.parameters[i]['x-yop-apigateway-backend-name']) {
            this.resultMappingList_src.push(this.form_data_request_src.parameters[i])
          }
        }
      }
      if (this.schemaRequest && this.schemaRequest_src) {
        this.showOneDiff = false
      }
      if ((this.schemaRequest && !this.schemaRequest_src) || (!this.schemaRequest && this.schemaRequest_src)) {
        this.showOneDiff = true
      }

      this.form_data_response_src = srcApi.response
      if (srcApi.response.content) {
        var schemaResponseTemp_src = JSON.parse(srcApi.response.content.schema);
        if (schemaResponseTemp_src.$ref) {
          this.schemaResponse_src = schemaResponseTemp_src.$ref.split('/').pop()
        }
        if (JSON.parse(schema).$ref) {
          this.form_data_response_src.content.schema = JSON.parse(schema).$ref.split('/').pop()
        }
        this.data_response_example_List_src = srcApi.response.content.examples
      }
      if (srcApi.endpoint) {
        this.form_data_endpoint_src = srcApi.endpoint
      }
      this.form_data_callbacks_src = srcApi.callbacks
    },
    detail_param (index, data, current_data, panel) {
      // this.$refs[panel].$refs.form_data_left.resetFields();
      // this.$refs[panel].$refs.form_data_right.resetFields();
      this.$refs[panel].current_panel_set(current_data);
      this.$refs[panel].current_index_set(index);
      data.type = [data.schema.type, data.schema.format ? data.schema.format : data.schema.type]
      this.$refs[panel].form_data_set(JSON.parse(JSON.stringify(data)), this.sensitiveVariables, this.bizOrderVariable);
      this.current_index = -1;
      this.$refs[panel].show();
    },
    data_update_model (data, type, index, subContent) {
      // this.example = data.example
      // var data = data.form_data
      data.additionalProperties = {}
      data.items = {}
      if (data.constrains) {
        if (data.constrains[0] === 'additionalProperties') {
          data.additionalProperties = subContent
        } else if (data.constrains[0] === 'items') {
          data.items = subContent
        }
      }
      var newData = JSON.parse(JSON.stringify(data))
      if (type === 'create') {
        this.data_propertiesList.push(newData);
        // 现在为不包含子项的处理 需要在提交的时候2次数据规范
      } else {
        // 编辑查看
        for (var key in newData) {
          this.data_propertiesList[index][key] = data[key]
        }
      }
      for (var i = 0; i < this.data_propertiesList.length; i++) {
        var data_propertiesListI = this.data_propertiesList[i]
        for (var j in data_propertiesListI) {
          if (j === 'orderNo') {
            if (data_propertiesListI[j]) {
              this.bizOrderVariable = {
                'part': 'REQUEST',
                'location': 'QUERY',
                'variableName': data_propertiesListI['name']
              }
            } else {
              delete data_propertiesListI['orderNo']
              this.bizOrderVariable = {}
            }
          }
        }
      }
      // this.$refs.param_operate.cancel();
      // this.$refs.param_operate.$refs.form_data_right.resetFields();
      // this.$refs.param_operate.$refs.form_data_left.resetFields();
      // 新增后端服务mapping列表
      // this.$refs.point_service.setMappingList(mapping_list_temp)
    },
    ok_example () {
      this.$refs.form_data_example.validate((valid) => {
        if (valid) {
          var example = {
            name: this.form_data_example.name,
            title: this.form_data_example.title,
            description: this.form_data_example.description,
            value: this.form_data_example.value
          }
          if (!this.editExample) {
            this.data_spi_example_List.push(example)
          } else {
            this.data_spi_example_List.splice(this.row_index, 1, example)
          }
          this.modal_Show_example = false;
        } else {
          this.form_validate_failed()
        }
      })
    },
    form_validate_failed () {

    },
    cancel_example () {
      this.modal_Show_example = false;
    },
    syntaxHighlight (json) {
      if (typeof json !== 'string') {
        json = JSON.stringify(json, undefined, 4);
      }
      // json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
      return json
      // return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
      //     function(match) {
      //         var cls = 'number';
      //         if (/^"/.test(match)) {
      //             if (/:$/.test(match)) {
      //                 cls = 'key';
      //             } else {
      //                 cls = 'string';
      //             }
      //         } else if (/true|false/.test(match)) {
      //             cls = 'boolean';
      //         } else if (/null/.test(match)) {
      //             cls = 'null';
      //         }
      //         return match;
      //     }
      // );
    }

  }
};
</script>
