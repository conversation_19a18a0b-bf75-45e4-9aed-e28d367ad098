<style lang="less">
    @import '../../styles/common.less';
    @import '../API_Management_Open/api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
    .ivu-table-cell {
        padding-left : 10px;
        padding-right : 10px;
    }
    .el-select-dropdown__item{
        font-size: 12px!important;
        color: #495060!important;
    }
    .el-input--small {
        font-size: 12px!important;
    }
    .el-input__inner{
        padding: 0 8px;
    }
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="24">

                <Col class="margin-top-5"  span="5">
                <Col  span="9">
                <Col  class="margin-top-10">请求ID：&nbsp;</Col>
                </Col>
                <Col span="15">
                <Input id='input_invokeL_1' class="margin-top-5" clearable v-model="data_request_id" placeholder="请求ID" @on-enter="search_Interface"></Input>
                </Col>
                </Col>
                <Col class="margin-top-5" offset="1" span="5" >
                <Col  span="9">
                <Col class="margin-top-10">应用标识：</Col>
                </Col>
                <Col span="15">
                <Input id='input_invokeL_2'  class="margin-top-5" clearable v-model="data_app_key" placeholder="应用标识" @on-enter="search_Interface"></Input>
                </Col>
                </Col>

                <Col class="margin-top-5" offset="1" span="5" >
                <Col  span="9">
                <Col class="margin-top-10">API分组：&nbsp;</Col>
                </Col>
                <Col span="15">
                <Select id='select_invokeL_1' ref="select_apiM_1" class="margin-top-5"  v-model="data_select_apiGroup" filterable clearable placeholder="请选择（默认全部）">
                    <Option v-for="item in data_apiGroup_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>


                <Col class="margin-top-5"  offset="1" span="5" v-show="basicShow">
                <Col  span="9">
                <Col class="margin-top-10" >API URI：&nbsp;</Col>
                </Col>
                <Col span="15">
                <Input id='input_invokeL_3' class="margin-top-5" clearable v-model="data_interface_uri" placeholder="API URI" @on-enter="search_Interface"></Input>
                </Col>
                </Col>

                </Col>
                <Col span="24">

                <Col class="margin-top-5"   span="5" v-show="basicShow">
                <Col  span="9">
                <Col class="margin-top-10"> GUID：&nbsp;</Col>
                </Col>
                <Col span="15">
                <Input id='input_invokeL_4' class="margin-top-5" clearable v-model="data_guid" placeholder="GUID" @on-enter="search_Interface"></Input>
                </Col>
                </Col>
                <Col class="margin-top-5" offset="1" span="5" v-show="basicShow">
                <Col  span="9">
                <Col class="margin-top-10" >安全需求：&nbsp;</Col>
                </Col>
                <Col span="15">
                <Select id='select_invokeL_2' ref="select_apiM_3" class="margin-top-5" v-model="data_safety_request"  placeholder="请选择（默认全部）" clearable>
                    <Option v-for="item in data_safetyRequest_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>

                <Col class="margin-top-5" offset="1" span="5" >
                    <Col  span="9">
                    <Col class="margin-top-10" >处理状态：&nbsp;</Col>
                    </Col>
                    <Col span="15">
                        <Select id='select_invokeL_3' ref="select_apiM_2" class="margin-top-5" v-model="data_select_status" placeholder="请选择（默认全部）" clearable>
                            <Option v-for="item in data_status_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                        </Select>
                    </Col>
                </Col>
                <Col class="margin-top-5" offset="1" span="5" >
                    <Col  span="9">
                        <Col class="margin-top-10">后端耗时:&nbsp;</Col>
                    </Col>
                    <Col span="15">
                        <Col class="margin-top-5" span="9">
                            <Input number v-model="time_consume_start" @on-keyup="time_consume_start_format" @on-enter="search_Interface"></Input>
                        </Col>
                        <Col class="margin-top-10" span="2">—</Col>
                        <Col class="margin-top-5" span="9">
                        <Input number v-model="time_consume_end" @on-keyup="time_consume_end_format" @on-enter="search_Interface"></Input>
                        </Col>
                        <Col class="margin-top-10" offset="1" span="2">
                        ms
                        </Col>
                    </Col>
                </Col>
                </Col>
                <Col span="24">

                <Col class="margin-top-5" span="5">
                <Col  span="9">
                <Col class="margin-top-10" >错误码：&nbsp;</Col>
                </Col>
                <Col span="15">
                <Select id='select_invokeL_4' ref="select_apiM_4"  class="margin-top-5" v-model="data_select_errorCode"  placeholder="请选择（默认全部）" clearable @on-change="subErrorCodeChange">
                    <Option v-for="item in data_errorCode_List" :value="item.value" :key="item.value" >{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>

                <Col class="margin-top-5" offset="1" span="5" >
                <Col  span="9">
                <Col class="margin-top-10" >子错误码：&nbsp;</Col>
                </Col>
                <Col span="15">
                <!--<Input id='input_apiM_6' class="margin-top-5" clearable v-model="data_subErrorCode" placeholder="子错误码" @on-enter="search_Interface"></Input>-->
                <!--<AutoComplete-->
                        <!--class="margin-top-5"-->
                        <!--v-model="data_subErrorCode"-->
                        <!--:data="subErrorCodeList"-->
                        <!--@on-select="search_Interface"-->
                        <!--:filter-method="filterMethod"-->
                        <!--placeholder="子错误码"-->
                        <!--placement="bottom"-->
                        <!--clearable></AutoComplete>-->
                <el-select
                        id='select_invokeL_5'
                        style="width:100%"
                        class="margin-top-5"
                        v-model="data_subErrorCode"
                        filterable
                        size="small"
                        allow-create
                        clearable
                        default-first-option
                        placeholder="子错误码">
                    <el-option
                            v-for="item in subErrorCodeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                    </el-option>
                </el-select>
                </Col>
                </Col>
                <Col class="margin-top-5" offset="1" span="12" >

                <Col  span="3">
                <Col class="margin-top-10">请求时间:&nbsp;</Col>
                </Col>
                <Col span="21">
                <Col class="margin-left-20" span="5">
                <DatePicker class="margin-top-5" v-model="requestDateStart" :options="optionsStart" :clearable="false" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择起始日期" ></DatePicker>
                </Col>
                <Col span="5">
                <TimePicker class="margin-top-5" v-model="requestTimeStart" :clearable="false" format="HH:mm:ss" placeholder="选择起始时间"></TimePicker>
                </Col>
                <Col class="margin-top-10" span="1"> &nbsp;—</Col>
                <Col span="5">
                <DatePicker class="margin-top-5" v-model="requestDateEnd" :clearable="false" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择结束日期" ></DatePicker>
                </Col>
                <Col span="5">
                <TimePicker class="margin-top-5" v-model="requestTimeEnd" :clearable="false" format="HH:mm:ss" placeholder="选择结束时间"></TimePicker>
                </Col>
                </Col>
                </Col>

                </Col>
                <Col span="24">

                <Col class="margin-top-5"   span="5" v-show="basicShow">
                <Col  span="9">
                <Col class="margin-top-10"> 请求IP：&nbsp;</Col>
                </Col>
                <Col span="15">
                <Input id='input_invokeL_6' class="margin-top-5" clearable v-model="data_request_ip" placeholder="请求IP" @on-enter="search_Interface"></Input>
                </Col>
                </Col>


                <Col  class="margin-top-5" offset="1" span="5" v-show="basicShow">
                <Col  span="9">
                <Col class="margin-top-10"> 数据中心：&nbsp;</Col>
                </Col>
                <Col span="15">
                <Input id='input_invokeL_7' class="margin-top-5" clearable v-model="dataCenter" placeholder="数据中心" @on-enter="search_Interface"></Input>
                </Col>
                </Col>
                </Col>
                </Col>


                <Col class="margin-top-10"  span="24" style="text-align: center">
                <Button class="margin-right-20" id='btn_invokeL_1' type="primary" v-url="{ url: '/rest/invoke-logging/list' }" :loading="loading_search" @click="search_Interface">查询</Button>
                <Button  id='btn_invokeL_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
                <!--<Col span="4">-->
                <!--<Col class="margin-top-10"  span="11" style="text-align:center">-->
                <!--<Button id='btn_apiM_1' type="primary" @click="search_Interface">查询</Button>-->
                <!--</Col>-->
                <!--<Col class="margin-top-10"  span="11" style="text-align:center">-->
                <!--<Button id='btn_apiM_2' type="ghost" @click="reset_Interface">重置</Button>-->
                <!--</Col>-->
                <!--</Col>-->
            </Row>

            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_invokeL_1' border ref="selection" :columns="columns_ApiInterfaceList" :data="data_ApiInterfaceList" @on-selection-change="handleselection" ></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_apiList"></loading>
            </Row>
        </Card>
        <Modal v-model="modal_Show_detail" width="90%" >
            <p slot="header">
                <span  class="margin-right-10">调用记录详情</span>
            </p>
            <div >
                <Table id='table_invokeL_2' border ref="selection" :columns="columns_invoke_detail_part1" :data="data_invoke_detail_part1" @on-selection-change="handleselection" :show-header="false"></Table>
                <Table  class="yop-table-connect" id='table_invokeL_3' border ref="selection1" :columns="columns_invoke_detail_part2" :data="data_invoke_detail_part2" @on-selection-change="handleselection" :show-header="false"></Table>
            </div>
            <div slot="footer">
                <!--<Button id='modal_apiM_btn_1' type="ghost"  @click="cancel_Newapp">取消</Button>-->
                <Button id='modal_invokeL_btn_1' type="primary" @click="ok_detail">关闭</Button>
            </div>
            <loading :show="show_loading_detail"></loading>
        </Modal>
        <Modal id="modal_invokeL_2" v-model="modal_solution" width="90%" :closable="false">
            <p slot="header" style="color:#2d8cf0;">
                <span style="color:black">解决方案</span>
            </p>
            <div>
                <Table id='table_invokeL_5' border ref="selection" :columns="columns_solutionList"
                       :data="data_solutionList"></Table>
                <Tabs id="modal_invokeL_tab_1" v-model="tabNow_model" >
                    <TabPane label="解决方案(对内)" name="solution_in_modal">
                        <div v-html="innerSolution"></div>
                    </TabPane>
                    <TabPane label="解决方案(对外)" name="solution_out_modal">
                        <div v-html="outerSolution"></div>
                        <!--{{outerSolution}}-->
                    </TabPane>
                </Tabs>
            </div>
            <div slot="footer">
                <Button id="modal_invokeL_btn_2" type="primary" @click="solution_close">关闭</Button>
            </div>
            <loading :show="show_loading_solution"></loading>
        </Modal>
    </div>
</template>

<script>
    import api from '../../api/api'
    import Vue from 'vue';
    import qs from 'qs';
    import loading from '../my-components/loading/loading';
    import util from '../../libs/util'
    export default {
      name: 'list',
      components: {
        loading
      },
      beforeCreated () {
        window.document.title = '调用记录 - 易宝运营后台'
      },
      data () {
        return {
          /**
                 * 主界面部分数据
                 */
          // 查询按钮loading
          loading_search: false,
          // 高级筛选显示
          basicShow: true,
          show_loading_detail: false,
          // api列表加载动画数据绑定
          show_loading_apiList: true,
          // switch点击行数
          switch_index: 0,
          // 调用记录详情对话框显示
          modal_Show_detail: false,
          // APIuri数据绑定
          data_interface_uri: '',
          // 应用标识
          data_app_key: '',
          // requestID
          data_request_id: '',
          // 数据中心
          dataCenter: '',
          // guid
          data_guid: '',
          // 业务订单号
          data_bookNo: '',
          // 后端应用耗时开始
          time_consume_start: '',
          // 后端应用耗时结束
          time_consume_end: '',
          // 请求开始日期
          requestDateStart: '',
          // 请求开始日期额外选项
          optionsStart: {
            disabledDate (date) {
              return date && date.valueOf() > Date.now();
            }
          },
          // 请求开始时间
          requestTimeStart: '00:00:00',
          // 请求结束日期
          requestDateEnd: '',
          // 请求结束时间
          requestTimeEnd: '23:59:59',
          // 请求ip
          data_request_ip: '',
          // 状态下拉框数据绑定
          data_select_status: '',
          // 状态下拉框选项数据
          data_status_List: [
            {
              value: 'SUCCESS',
              label: '成功'
            },
            {
              value: 'FAILED',
              label: '失败'
            }
          ],
          // api分组下拉框数据绑定
          data_select_apiGroup: '',
          // api分组下拉框数据
          data_apiGroup_List: [],
          // 安全需求下拉框数据绑定
          data_safety_request: '',
          // 安全需求下拉框数据
          data_safetyRequest_List: [],
          // 错误码类型选择数据绑定
          data_select_errorCode: '',
          // 错误码类型
          data_errorCode_List: [],
          // 子错误码类型选择数据绑定
          data_subErrorCode: '',
          // apiAPI列表列属性
          columns_ApiInterfaceList: [
            {
              'min-width': 100,
              align: 'center',
              renderHeader: (h, params) => {
                return h('div', [h('p', '请求ID'), h('p', 'GUID')]);
              },
              render: (h, params) => {
                return h('div', [
                  h('p', params.row.request_id),
                  h('p', params.row.guid)
                ]);
              }
            },
            {
              title: '应用标识',
              width: 140,
              key: 'app_key',
              align: 'center'
            },
            {
              'min-width': 155,
              renderHeader: (h, params) => {
                return h('div', [h('p', 'API URI'), h('p', 'API标题')]);
              },
              render: (h, params) => {
                return h('div', [
                  h('p', params.row.interface_URI),
                  h('p', params.row.api_title)
                ]);
              },
              align: 'center'
            },
            {
              title: '安全需求',
              key: 'interface_Tag',
              width: 162,
              align: 'center'
            },
            {
              key: 'dataCenter',
              type: 'html',
              width: 115,
              align: 'center',
              renderHeader: (h, params) => {
                return h('div', [h('p', '请求IP'), h('p', '数据中心')]);
              },
              render: (h, params) => {
                return h('div', [
                  h('p', params.row.request_ip),
                  h('p', params.row.dataCenter)
                ]);
              }
            },
            {
              title: '状态',
              key: 'status',
              width: 90,
              align: 'center',
              render: (h, params) => {
                // if(params.row.status === 'SUCCESS'){
                //     return h ('div','处理成功');
                // }else{
                //     return h ('div','处理失败');
                // }
                let color = 'red';
                let status = '失败';
                if (params.row.status === 'SUCCESS') {
                  color = 'green';
                  status = '成功';
                }
                return h('div', [
                  h('Tag', {
                    style: {
                      align: 'center'
                    },
                    props: {
                      color: color
                    }
                  }, status)
                ])
              }
            },
            {
              // title: '错误码/子错误码',
              key: 'errorCode',
              renderHeader: (h, params) => {
                return h('div', [h('p', '错误码'), h('p', '子错误码')]);
              },
              render: (h, params) => {
                return h('div', [
                  h('p', params.row.errorCodeTemp)
                ]);
              },
              type: 'html',
              width: 140,
              align: 'center'
            },
            {
              renderHeader: (h, params) => {
                return h('div', [h('p', '请求时间'), h('p', '响应时间')]);
              },
              render: (h, params) => {
                return h('div', [
                  h('p', params.row.requestDatetime)
                ]);
              },
              key: 'Date',
              type: 'html',
              width: 135,
              align: 'center'
            },
            {
              title: '操作',
              key: 'operations',
              align: 'center',
              width: 65,
              render: (h, params) => {
                return h('div', [
                  h('Button', {
                    props: {
                      type: 'primary',
                      size: 'small'
                    },
                    attrs: {
                      id: 'btn_invokeL_list_' + params.row.id
                    },
                    directives: [{
                      name: 'url',
                      value: {url: '/rest/invoke-logging/detail'}
                    }],
                    on: {
                      click: () => {
                        this.invoke_detail(params.row.id, params.row.requestDatetime);
                      }
                    }
                  }, '详情')
                ]);
              }
            }
          ],
          // 表格数据
          data_ApiInterfaceList: [
            // {
            //     request_id: '123456789',
            //     guid: "sad8f79asd6f8d7sa6f98ds",
            //     requestId:"51f7e2685b164ec6af7751cba066277b",
            //     app_key: "yop-boss",
            //     interface_URI: "/rest/v1.0/test/auth2/download",
            //     ip:'666.666.666.666<br/>************',
            //     request_ip: "666.666.666.666",
            //     dataCenter: "************",
            //     status: "FAILED",
            //     errorCode: "40042<br>isv.app.not-exists",
            //     subErrorCode: "isv.app.not-exists",
            //     interface_Tag: "YOP_HMAC_AES128",
            //     Date: '2018-05-01 12:00:00<br/>2018-05-01 12:00:00',
            //     requestDatetime: "2018-05-01 12:00:00",
            //     responseDatetime: "2018-05-01 12:00:00"
            // }
          ],
          // 详细信息列属性第一部分
          columns_invoke_detail_part1: [
            {
              title: '名称',
              width: 185,
              key: 'name',
              render: (h, params) => {
                return h('div', [
                  h('strong', params.row.name)
                ]);
              }
            },
            {
              title: '内容',
              'min-width': 300,
              key: 'value',
              // type: 'html',
              render: (h, params) => {
                if (params.row.name === '处理状态(status)') {
                  let color = 'red';
                  let status = '失败';
                  if (params.row.value === 'SUCCESS') {
                    color = 'green';
                    status = '成功';
                  }
                  return h('div', [
                    h('Tag', {
                      style: {
                        align: 'center'
                      },
                      props: {
                        color: color
                      }
                    }, status)
                  ])
                } else {
                  return h('div', params.row.value);
                }
              }
            },
            {
              title: '名称2',
              width: 185,
              key: 'name2',
              render: (h, params) => {
                return h('div', [
                  h('strong', params.row.name2)
                ]);
              }
            },
            {
              title: '内容2',
              'min-width': 300,
              key: 'value2',
              // type: 'html',
              render: (h, params) => {
                if (params.row.name2 === '内部请求标识(GUID)') {
                  let linkCallChain = 'none';
                  let linkLogCenter = 'none';
                  if (localStorage.linkLogCenter && localStorage.linkLogCenter !== '' && localStorage.linkLogCenter !== 'undefined') {
                    linkLogCenter = 'inline-block';
                  }
                  if (localStorage.linkCallChain && localStorage.linkCallChain !== '' && localStorage.linkCallChain !== 'undefined') {
                    linkCallChain = 'inline-block';
                  }
                  return h('div', [
                    h('span', {
                      style: {
                        marginRight: '10px'
                      }
                    }, params.row.value2),
                    h('Button', {
                      props: {
                        type: 'primary',
                        size: 'small'
                      },
                      style: {
                        marginRight: '10px',
                        display: linkLogCenter
                      },
                      attrs: {
                        id: 'modal_invokeL_btn_3'
                      },
                      on: {
                        click: () => {
                          this.related_log(params.row.value2);
                        }
                      }
                    }, '关联日志'),
                    h('Button', {
                      props: {
                        type: 'success',
                        size: 'small',
                        'v-show': linkCallChain
                      },
                      style: {
                        display: linkCallChain
                      },
                      attrs: {
                        id: 'modal_invokeL_btn_4'
                      },
                      on: {
                        click: () => {
                          this.invoke_analyze(params.row.value2);
                        }
                      }
                    }, '调用链分析')
                  ]);
                } else if (params.row.name2 === '子错误码(sub_error_code)') {
                  if (params.row.value2) {
                    return h('div', [
                      h('span', {
                        style: {
                          marginRight: '10px'
                        }
                      }, params.row.value2),
                      h('Button', {
                        props: {
                          type: 'primary',
                          size: 'small'
                        },
                        style: {
                          marginRight: '10px'
                        },
                        attrs: {
                          id: 'modal_invokeL_btn_5'
                        },
                        on: {
                          click: () => {
                            this.error_solution();
                          }
                        }
                      }, '解决方案')
                    ]);
                  } else {
                    return h('div', params.row.value2);
                  }
                  // return h('div', params.row.value2);
                } else {
                  return h('div', params.row.value2);
                }
              }
            }
          ],
          // 详细信息列属性第一部分
          columns_invoke_detail_part2: [
            {
              title: '名称',
              width: 185,
              key: 'name',
              render: (h, params) => {
                return h('div', [
                  h('strong', params.row.name)
                ]);
              }
            },
            {
              title: '内容',
              'min-width': 300,
              key: 'value',
              type: 'html'
            }
          ],
          // 详细信息表格数据第一部分
          data_invoke_detail_part1: [],
          // 详细信息表格数据第二部分
          data_invoke_detail_part2: [],
          // 表格选中数据
          multiSelectedData: [],
          // 分页单页个数
          pageNo: 1,
          // 分页总数
          pageTotal: 0,
          // 当前查询参数
          current_params: {
            pageNo: 1,
            pageSize: 10
          },
          // 详细信息对象第一部分第一列
          detail_trans_part1_1: {
            requestId: '外部请求标识(requestId)',
            appKey: '应用标识(appkey)',
            apiUri: 'API URI(api uri)',
            httpMethod: '请求方式(http method)',
            contentType: '内容类型(content-type)',
            requestIp: '请求IP(request ip)',
            requestDatetime: '请求时间(request time)',
            totalLatency: '整体耗时(网关+后端)',
            status: '处理状态(status)',
            errorCode: '错误码(error_code)'
          },
          // 详细信息对象第一部分第二列
          detail_trans_part1_2: {
            guid: '内部请求标识(GUID)',
            customerNo: '商户编号(customerNo)',
            // apiTitle: 'API标题(api title)',
            apiGroup: 'API分组(api group)',
            securityStrategy: '安全需求(security req)',
            contentCharset: '编码格式(content-charset)',
            dataCenter: '数据中心(data-center)',
            responseDatetime: '响应时间(response time)',
            backendLatency: '后端耗时(仅后端)',
            bizOrderValue: '业务订单号',
            subErrorCode: '子错误码(sub_error_code)'
          },
          // 详细信息对象第二部分
          detail_trans_part2_1: {
            stackTrace: '堆栈跟踪(stack trace)',
            requestHeader: '请求头(request header)',
            requestBody: '请求体(request body)',
            responseHeader: '响应头(response header)',
            responseBody: '响应体(response body)'

          },
          subErrorCodeList: [],
          // 当前错误码解决方案请求信息
          error_solution_info: {
            apiUri: '',
            apiGroup: '',
            errorCode: '',
            subErrorCode: ''
          },
          /**
                 * 解决方案弹窗
                 */
          // 解决方案弹窗label
          modal_solution: false,
          // 对内解决方案数据绑定
          innerSolution: '',
          // 对外解决方案数据绑定
          outerSolution: '',
          // 错误码管理列表表头
          columns_solutionList: [
            {
              title: '错误码类型',
              key: 'errorCodeType',
              'min-width': 110,
              align: 'center'
            },
            {
              title: 'API分组',
              'min-width': 140,
              render: (h, params) => {
                return h('div', [
                  h('p', params.row.apiGroupName),
                  h('p', '(' + params.row.apiGroupCode + ')')
                ]);
              },
              align: 'center'
            },
            {
              title: 'API URI',
              'min-width': 150,
              align: 'center',
              render: (h, params) => {
                return h('div', [
                  h('p', params.row.APIURI)
                ]);
              }

            },
            {
              renderHeader: (h, params) => {
                return h('div', [
                  h('p', '错误码'),
                  h('p', '错误码描述')
                ]);
              },
              'min-width': 130,
              align: 'center',
              render: (h, params) => {
                return h('div', [
                  h('p', params.row.errorCode),
                  h('p', '(' + params.row.errorCodeDes + ')')
                ]);
              }

            },
            {
              renderHeader: (h, params) => {
                return h('div', [
                  h('p', '子错误码'),
                  h('p', '错误码描述')
                ]);
              },
              'min-width': 130,
              align: 'center',
              render: (h, params) => {
                return h('div', [
                  h('p', params.row.subErrorCode),
                  h('p', '(' + params.row.subErrorCodeDes + ')')
                ]);
              }

            }
          ],
          data_solutionList: [],
          tabNow_model: 'solution_in_modal',
          // 解决方案加载绑定
          show_loading_solution: false
        };
      },
      methods: {
        // 解决方案关闭
        solution_close () {
          this.modal_solution = false;
        },
        // 点击自定义checkbox
        check_url (data) {
          this.show_pointUrl = data;
        },
        // 详情按钮调用方法
        invoke_detail (id, time) {
          let params = {
            invokeLogId: id,
            requestDatetime: time
          };
          api.yop_invokeLog_detail(params).then(
            (response) => {
              this.modal_Show_detail = true;
              this.show_loading_detail = true;
              if (response.data.status === 'success') {
                let content = response.data.data.result;
                this.data_invoke_detail_part1 = [];
                this.data_invoke_detail_part2 = [];
                this.table_data_format(content)
                this.show_loading_detail = false;
              } else {
                this.$Modal.error({
                  title: '调用记录详情获取失败',
                  content: response.data.message
                });
                this.show_loading_detail = false;
                this.modal_Show_detail = false;
              }
            }
          );
        },
        // 标题加粗
        title_bolder (title) {
          return '<span style=\'font-weight:bold\'>' + title + '</span>'
        },
        // 错误码显示处理
        errorCode_format (errorCode) {
          for (var i in this.data_errorCode_List) {
            if (errorCode === this.data_errorCode_List[i].value) {
              return this.data_errorCode_List[i].label
            }
          }
          return errorCode;
        },
        // 返回数据处理函数
        table_data_format (content) {
          let data_invoke_detail_part1Temp = [];
          let data_invoke_detail_part2Temp = [];
          for (var i in this.detail_trans_part1_1) {
            // if(i === 'status'){
            //     let dataTemp = "<Tag color='red'>处理失败</Tag>";
            //     if(content[i] === 'SUCCESS') dataTemp = "<Tag color='green'>处理成功</Tag>";
            //     data_invoke_detail_part1Temp.push({
            //         name: this.detail_trans_part1_1[i],
            //         value: dataTemp
            //     })
            // }else
            if (i === 'totalLatency') {
              data_invoke_detail_part1Temp.push({
                name: this.detail_trans_part1_1[i],
                value: content[i] + ' ms'
              })
            } else if (i === 'apiUri') {
              this.error_solution_info.apiUri = content[i]
              data_invoke_detail_part1Temp.push({
                name: this.detail_trans_part1_1[i],
                value: `${content[i]}(${content['apiTitle']})`
              })
            } else if (i === 'errorCode') {
              this.error_solution_info.errorCode = content[i]
              data_invoke_detail_part1Temp.push({
                name: this.detail_trans_part1_1[i],
                value: this.errorCode_format(content[i])
              })
            } else {
              data_invoke_detail_part1Temp.push({
                name: this.detail_trans_part1_1[i],
                value: content[i]
              })
            }
          }
          for (var i in this.detail_trans_part1_2) {
            if (i === 'apiGroup') {
              this.error_solution_info.apiGroup = content['apiGroupCode']
              data_invoke_detail_part2Temp.push({
                name2: this.detail_trans_part1_2[i],
                value2: content['apiGroupCode'] + '(' + util.empty_handler(this.data_name_handler(content['apiGroupCode'] + ' ', this.data_apiGroup_List)) + ')'
              })
            } else if (i === 'bizOrderValue') {
              data_invoke_detail_part2Temp.push({
                name2: this.detail_trans_part1_2[i] + '(' + this.empty_handler(content['bizOrderCode']) + ')',
                value2: content['bizOrderValue']
              })
            } else if (i === 'backendLatency') {
              data_invoke_detail_part2Temp.push({
                name2: this.detail_trans_part1_2[i],
                value2: content[i] + ' ms'
              })
            } else if (i === 'subErrorCode') {
              this.error_solution_info.subErrorCode = content[i]
              data_invoke_detail_part2Temp.push({
                name2: this.detail_trans_part1_2[i],
                value2: content[i]
              })
            } else {
              data_invoke_detail_part2Temp.push({
                name2: this.detail_trans_part1_2[i],
                value2: content[i]
              })
            }
          }
          for (var i in data_invoke_detail_part1Temp) {
            this.data_invoke_detail_part1.push({
              name: data_invoke_detail_part1Temp[i].name,
              value: data_invoke_detail_part1Temp[i].value,
              name2: data_invoke_detail_part2Temp[i].name2,
              value2: data_invoke_detail_part2Temp[i].value2
            })
          }
          for (var i in this.detail_trans_part2_1) {
            if (i === 'requestHeader' || i === 'responseHeader') {
              let temp = '';
              for (var j in JSON.parse(content[i])) {
                temp = temp + j + ' : ' + JSON.parse(content[i])[j] + '<br/>'
              }
              this.data_invoke_detail_part2.push({
                name: this.detail_trans_part2_1[i],
                value: temp
              })
            } else if (i === 'requestBody') {
              let temp4 = ''
              if (content[i]) {
                if (util.jsonParseCheck(content[i])) {
                  temp4 = '<pre style=\'white-space: pre-wrap!important;word-wrap: break-word!important;\'>' + util.formatJson(JSON.parse(content[i])) + '</pre>'
                } else {
                  temp4 = '<pre style=\'white-space: pre-wrap!important;word-wrap: break-word!important;\'>' + content[i] + '</pre>'
                }
              }

              // '<pre>'+JSON.stringify(JSON.parse(content[i]))+'</pre>';

              this.data_invoke_detail_part2.push({
                name: this.detail_trans_part2_1[i],
                value: temp4
              })
            } else if (i === 'responseBody') {
              let temp2 = ''
              if (util.jsonParseCheck(content[i])) {
                temp2 = '<pre style=\'white-space: pre-wrap!important;word-wrap: break-word!important;\'>' + util.formatJson(JSON.parse(content[i])) + '</pre>'
              } else {
                temp2 = '<pre style=\'white-space: pre-wrap!important;word-wrap: break-word!important;\'>' + content[i] + '</pre>'
              }
              this.data_invoke_detail_part2.push({
                name: this.detail_trans_part2_1[i],
                value: temp2
              })
            } else if (i === 'stackTrace') {
              if (content[i]) {
                let temp3 = content[i].replace(/\n/g, '<br/>');
                let arr = temp3.split('<br/>')
                let con = ''
                let title = ''
                for (var j = 0; j < arr.length; j++) {
                  if (j === 0) {
                    title = arr[j]
                  } else if (j === arr.length - 1) {
                    con = con + arr[j]
                  } else {
                    con = con + arr[j] + '<br/>';
                  }
                }
                temp3 = '<span style="color:red">' + title + '</span>' + '<br/>' + con
                temp3 = temp3.replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
                temp3 = '<br/>' + temp3 + '<br/>';
                this.data_invoke_detail_part2.push({
                  name: this.detail_trans_part2_1[i],
                  value: temp3
                })
              } else {
                this.data_invoke_detail_part2.push({
                  name: this.detail_trans_part2_1[i],
                  value: ''
                })
              }
            } else {
              this.data_invoke_detail_part2.push({
                name: this.detail_trans_part2_1[i],
                value: content[i]
              })
            }
          }
        },
        // 关联日志跳转
        related_log (guid) {
          window.open(localStorage.linkLogCenter + guid, '_blank')
        },
        // 调用链分析跳转
        invoke_analyze (guid) {
          window.open(localStorage.linkCallChain + guid, '_blank')
        },
        // 后端应用耗时开始时间实时数字转换
        time_consume_start_format () {
          if (this.time_consume_start.toString().length === 1) {
            this.time_consume_start = this.time_consume_start.toString().replace(/[^1-9]/g, '');
          } else {
            this.time_consume_start = this.time_consume_start.toString().replace(/[^\d]/g, '');
          }
        },
        // 后端应用耗时结束时间实时数字转换
        time_consume_end_format () {
          if (this.time_consume_end.toString().length === 1) {
            this.time_consume_end = this.time_consume_end.toString().replace(/[^1-9]/g, '');
          } else {
            this.time_consume_end = this.time_consume_end.toString().replace(/[^\d]/g, '');
          }
        },
        // 界面初始化函数
        init () {
          this.requestDateStart = util.date_oneDay().after;
          this.requestDateEnd = util.date_oneDay().after;
          this.show_loading_apiList = true;
          this.show_loading_apiList = false;
          // 安全需求列表
          api.yop_apiManagement_securityReqList().then(
            (response) => {
              let resultTemp = response.data.data.result
              this.data_safetyRequest_List = [];
              let dataTemp = [];
              for (var i in resultTemp) {
                dataTemp.push({
                  value: resultTemp[i].name,
                  label: resultTemp[i].name
                })
              }
              this.data_safetyRequest_List = dataTemp;
            }
          );
          // 错误码列表
          api.yop_invokeLog_errorCode().then(
            (response) => {
              let resultTemp = response.data.data.result
              this.data_errorCode_List = [];
              let dataTemp = [];
              for (var i in resultTemp) {
                dataTemp.push({
                  // value: resultTemp[i].errorCode,
                  // label: resultTemp[i].errorCode +'('+resultTemp[i].errorMsg+')'
                  value: resultTemp[i].code,
                  label: resultTemp[i].code + '(' + resultTemp[i].msg + ')',
                  subCodes: resultTemp[i].subCodes
                })
              }
              this.data_errorCode_List = dataTemp;
              this.subErrorCode_total_handler();
            }
          );
          // api分组列表
          api.yop_dashboard_apis_list().then(
            (response) => {
              let resultTemp = response.data.data.result
              this.data_apiGroup_List = [];
              let dataTemp = [];
              for (var i in resultTemp) {
                dataTemp.push({
                  value: resultTemp[i].apiGroupCode + ' ',
                  label: resultTemp[i].apiGroupCode + '(' + resultTemp[i].apiGroupName + ')',
                  name: resultTemp[i].apiGroupName
                })
              }
              this.data_apiGroup_List = dataTemp;
            }
          )
          this.search_Interface()
        },
        // 页面刷新
        pageRefresh (val) {
          this.show_loading_apiList = true;
          this.current_params.pageNo = val;
          api.yop_invokeLog_list(this.current_params).then(
            (response) => {
              if (response.data.status === 'success') {
                this.tabledataGet(response.data.data.page.items);
                this.pageNo = response.data.data.page.pageNo;
                // this.pageTotal = response.data.data.page.totalPageNum * 10;
                if (response.data.data.page.items) {
                  if (response.data.data.page.items.length < 10) {
                    this.pageTotal = response.data.data.page.items.length;
                  } else {
                    this.pageTotal = NaN;
                  }
                } else {
                  this.pageTotal = NaN;
                }
                this.show_loading_apiList = false;
              } else {
                this.$Modal.error({
                  title: '错误',
                  content: response.data.message
                });
                this.show_loading_apiList = false;
              }
            }
          )
          // console.log(a);
        },
        // 表格赋值
        tabledataGet (items) {
          this.data_ApiInterfaceList = [];
          let dataTemp = [];
          for (var i in items) {
            let DateTemp = items[i].requestDatetime + '<br/>' + items[i].responseDatetime;
            let IpTemp = items[i].requestIp + '<br/>' + items[i].dataCenter;
            let errorCodeTemp = this.errorCode_format(items[i].errorCode) + '<br/>' + items[i].subErrorCode;
            if (items[i].status === 'SUCCESS') {
              errorCodeTemp = '';
            }
            dataTemp.push({
              id: items[i].id,
              guid: items[i].guid,
              request_id: items[i].requestId,
              dataCenter: IpTemp,
              app_key: items[i].appKey,
              interface_URI: items[i].apiUri,
              api_title: items[i].apiTitle,
              status: items[i].status,
              errorCode: errorCodeTemp,
              requestDatetime: items[i].requestDatetime,
              interface_Tag: items[i].securityStrategy,
              Date: DateTemp
            });
          }
          this.data_ApiInterfaceList = dataTemp;
        },
        // 安全需求数组处理
        SecurityGenerate (title, array) {
          let securityTemp = title + '<br/>'
          for (var i in array) {
            if (i === array.length) {
              securityTemp = securityTemp + array[i]
            } else {
              securityTemp = securityTemp + array[i] + '<br/>'
            }
          }
          return securityTemp;
        },
        // 空处理函数
        ParamHandle (Param) {
          if (Param || (Param === 0)) {
            return Param;
          } else {
            return '';
          }
        },
        // 表格内容改动时
        handleselection (value) {
          this.multiSelectedData = value;
        },
        // 查询apiAPI函数
        search_Interface () {
          let datebefore = this.date_real(this.requestDateStart, this.requestTimeStart);
          let dateafter = this.date_real(this.requestDateEnd, this.requestTimeEnd);
          if (dateafter.getTime() < datebefore.getTime()) {
            this.$Modal.error({
              title: '错误',
              content: '结束日期不能小于开始日期'
            });
          } else if (this.time_consume_start && this.time_consume_end && (Number(this.time_consume_start) > Number(this.time_consume_end))) {
            this.$Modal.error({
              title: '错误',
              content: '后端应用耗时结束时间不能小于后端应用耗时开始时间'
            });
          } else if (this.date_space() > 1) {
            this.$Modal.error({
              title: '错误',
              content: '结束日期和开始日期间隔不能超过1天'
            });
          } else {
            this.search_apply();
          }
        },

        // 查询API实现
        search_apply () {
          this.loading_search = true;
          this.show_loading_apiList = true;
          let paramsTemp = {
            dataCenter: this.dataCenter.trim(),
            apiUri: this.data_interface_uri.trim(),
            appKey: this.data_app_key.trim(),
            apiGroup: this.data_select_apiGroup.trim(),
            requestStartDate: util.dateFormat_sep(this.requestDateStart, this.requestTimeStart),
            requestEndDate: util.dateFormat_sep(this.requestDateEnd, this.requestTimeEnd),
            guid: this.data_guid.trim(),
            bizOrderNo: this.data_bookNo.trim(),
            requestId: this.data_request_id.trim(),
            requestIp: this.data_request_ip.trim(),
            securityStrategy: this.data_safety_request,
            status: this.data_select_status,
            errorCode: this.data_select_errorCode,
            subErrorCode: this.data_subErrorCode.trim(),
            minBackendLatency: this.time_consume_start,
            maxBackendLatency: this.time_consume_end,
            pageNo: 1,
            pageSize: 10
          }
          util.paramFormat(paramsTemp);
          this.current_params = paramsTemp;
          api.yop_invokeLog_list(paramsTemp).then(
            (response) => {
              if (response.data.status === 'success') {
                this.tabledataGet(response.data.data.page.items);
                this.pageNo = response.data.data.page.pageNo;
                // this.pageTotal = response.data.data.page.totalPageNum * 10;
                if (response.data.data.page.items) {
                  if (response.data.data.page.items.length < 10) {
                    this.pageTotal = response.data.data.page.items.length;
                  } else {
                    this.pageTotal = NaN;
                  }
                } else {
                  this.pageTotal = NaN;
                }
                // this.pageTotal = NaN;
                this.show_loading_apiList = false;
                this.loading_search = false;
              } else {
                this.$Modal.error({
                  title: '错误',
                  content: response.data.message
                });
                this.show_loading_apiList = false;
                this.loading_search = false;
              }
            }
          );
        },
        // 重置函数
        reset_Interface () {
          this.$refs.select_apiM_1.clearSingleSelect();
          this.$refs.select_apiM_2.clearSingleSelect();
          this.$refs.select_apiM_3.clearSingleSelect();
          this.$refs.select_apiM_4.clearSingleSelect();
          this.subErrorCode_total_handler();
          this.data_app_key = '';
          this.data_interface_uri = '';
          this.data_select_errorCode = '';
          this.dataCenter = '';
          this.data_guid = '';
          this.data_bookNo = '';
          this.data_subErrorCode = '';
          this.data_select_apiGroup = '';
          this.data_select_status = '';
          this.requestDateStart = util.date_oneDay().after;
          this.requestTimeStart = '00:00:00';
          this.requestDateEnd = util.date_oneDay().after;
          this.requestTimeEnd = '23:59:59';
          this.data_request_id = '';
          this.data_request_ip = '';
          this.data_safety_request = '';
          this.time_consume_start = '';
          this.time_consume_end = '';
          this.current_status = {
            pageNo: 1,
            pageSize: 10
          };
        },
        // 一周前日期生成
        date_oneWeek () {
          let now = new Date();
          let date = new Date(now.getTime() - 7 * 24 * 3600 * 1000);
          return util.dateFormat(date);
        },
        // 详情按钮确认
        ok_detail () {
          this.modal_Show_detail = false;
        },
        // 高级筛查
        advance_functions () {
          this.basicShow = !this.basicShow;
        },
        // 日期区间
        date_space () {
          let dateAfter = this.date_real(this.requestDateEnd, this.requestTimeEnd);
          let dateBefore = this.date_real(this.requestDateStart, this.requestTimeStart);
          let days = Math.ceil((dateAfter.getTime() - dateBefore.getTime() + 1000) / (24 * 3600 * 1000));
          return days;
        },
        // 请求日期开始具体时间返回
        date_real (date, time) {
          let dateBefore = util.dateFormat_sep(date, time);
          let str = dateBefore.toString();
          str = str.replace('/-/g', '/');
          let odateBefore = new Date(str);
          return odateBefore;
        },
        date_real_before () {
          // let timesBefore = new Date(this.requestTimeStart);
          // let dateBefore = new Date(this.requestDateStart);
          // dateBefore.setHours(timesBefore.getHours(),timesBefore.getMinutes(),timesBefore.getSeconds());
          // return dateBefore;
          let dateBefore = util.dateFormat_sep(this.requestDateStart, this.requestTimeStart);
          console.log(dateBefore);
          let str = dateBefore.toString();
          str = str.replace('/-/g', '/');
          let odateBefore = new Date(str);
          return odateBefore;
        },
        // 请求日期结束具体时间返回
        date_real_after () {
          let timesAfter = new Date(this.requestTimeEnd);
          let dateAfter = new Date(this.requestDateEnd);
          dateAfter.setHours(timesAfter.getHours(), timesAfter.getMinutes(), timesAfter.getSeconds());
          return dateAfter;
        },

        // 如果未返回显示未配置
        empty_handler (val) {
          if (val) {
            return val
          } else {
            return '未配置'
          }
        },
        // 子错误码改变
        subErrorCodeChange (val) {
          if (val) {
            let subCodes = this.subErrorCode_single_handler(val);
            if (subCodes) {
              this.subErrorCodeList = [];
              subCodes.forEach(item => {
                this.subErrorCodeList.push({
                  label: item.code + '(' + item.msg + ')',
                  value: item.code
                })
              })
            }
          } else {
            this.subErrorCode_total_handler();
          }
        },
        // 获取错误码下的子错误码
        subErrorCode_single_handler (errorCode) {
          for (var i in this.data_errorCode_List) {
            if (errorCode === this.data_errorCode_List[i].value) {
              return this.data_errorCode_List[i].subCodes;
            }
          }
          return [];
        },
        // 清楚错误码显示所有子错误码
        clearErrorCode () {
          this.subErrorCode_total_handler();
        },
        // 所有子错误码抓取
        subErrorCode_total_handler () {
          this.subErrorCodeList = [];
          if (this.data_errorCode_List) {
            this.data_errorCode_List.forEach(item => {
              if (item.subCodes) {
                item.subCodes.forEach(subItem => {
                  if (!this.subErrorCodeList.find(item => item.value === subItem.code)) {
                    this.subErrorCodeList.push({
                      label: subItem.code + '(' + subItem.msg + ')',
                      value: subItem.code
                    })
                  }
                })
              }
            })
          }
        },
        filterMethod (value, option) {
          return option.toUpperCase().indexOf(value.toUpperCase()) !== -1;
        },
        // 错误码解决方案请求显示
        error_solution () {
          this.errorCode_solution(this.error_solution_info);
          // console.log(this.error_solution_info);
        },
        // 解决方案函数
        errorCode_solution (info) {
          this.show_loading_solution = true;
          this.modal_solution = true;
          this.tabNow_model = 'solution_in_modal';
          let param = {
            apiGroupCode: info.apiGroup,
            apiUri: info.apiUri,
            errorCode: info.errorCode,
            subErrorCode: info.subErrorCode
          }
          api.yop_invokeLog_errorCode_solution(param).then(
            (response) => {
              if (response.data.status === 'success') {
                if (response.data.data.result) {
                  this.tableDataFormat_solution(response.data.data.result, info);
                  this.innerSolution = util.empty_handler2(response.data.data.result.innerSolution);
                  this.outerSolution = util.empty_handler2(response.data.data.result.outerSolution);
                } else {
                  this.data_solutionList = [];
                  this.innerSolution = '';
                  this.outerSolution = '';
                }
                this.show_loading_solution = false;
              } else {
                this.$Modal.error({
                  title: '错误',
                  content: response.data.message
                });
                this.show_loading_solution = false;
              }
            }
          );
        },
        // 解决方案数据处理
        tableDataFormat_solution (items, info) {
          this.data_solutionList = [];
          this.data_solutionList.push({
            id: items.id,
            apiGroupName: util.empty_handler(this.data_name_handler(info.apiGroup + ' ', this.data_apiGroup_List)),
            apiGroupCode: util.empty_handler(info.apiGroup),
            errorCodeType: util.empty_handler(items.type),
            // APIName : '修改结算卡信息',
            APIURI: util.empty_handler(info.apiUri),
            errorCode: util.empty_handler(items.errorCode),
            errorCodeDes: util.empty_handler(items.errorMsg),
            subErrorCode: util.empty_handler(items.subErrorCode),
            subErrorCodeDes: util.empty_handler(items.subErrorMsg)
          });
        },
        // 返回相应编码的名字
        data_name_handler (code, name) {
          if (code) {
            let group = name;
            for (var i in group) {
              if (group[i].value === code) {
                return group[i].name;
              }
            }
          } else {
            return '';
          }
        }

      },
      mounted () {
        // 挂载时调用页面初始化函数
        setTimeout(() => {
          this.init();
        }, 500);
        localStorage.removeItem('apiInfo');
      },
      created () {
        // localStorage.removeItem('apiInfo');
      }

    };
</script>

<style scoped>

</style>
