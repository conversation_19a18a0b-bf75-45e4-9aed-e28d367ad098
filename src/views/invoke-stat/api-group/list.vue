<style lang="less">
    @import '../../../styles/common.less';
    @import '../../API_Management_Open/api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Tabs :value="tabNow">
                <TabPane id="tab_basic_1" label="API分组调用次数统计" name="invoke_times">
                    <invoke_times ref="request"></invoke_times>
                </TabPane>
                <!--<TabPane id="tab_basic_2" label="API查询耗时统计" name="invoke_latency">-->
                <!--<invoke_latency ref="response"></invoke_latency>-->
                <!--</TabPane>-->
            </Tabs>
        </Card>

    </div>
</template>

<script>
    import loading from '../../my-components/loading/loading';
    import invoke_times from '../../invoke/invoke_statistic_times';
    import invoke_latency from '../../invoke/invoke_statistic_latency';
    export default {
        name: 'list',
        components:{
            loading,
            invoke_times,
            invoke_latency
        },
        data () {
            return {
                tabNow:'invoke_times'
            };
        },
        methods: {

        }

    };
</script>

<style scoped>

</style>
