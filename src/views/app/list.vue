<style lang="less">
@import "../../styles/common.less";
@import "../API_Management_Open/api_Mangement_Open.less";
.demo-badge-alone {
  background: #5cb85c !important;
}
.round {
  width: 16px;
  height: 16px;
  display: inline-block;
  font-size: 20px;
  line-height: 16px;
  text-align: center;
  color: #f00;
  text-decoration: none;
}
</style>
<template>
  <div style="background: #eee;padding:8px">
    <Card :bordered="false">
      <Row type="flex" align="middle">
        <Col span="20">
          <Col span="7">
            <Col span="8" class="margin-top-10">
              <span>应用标识:</span>
            </Col>
            <Col span="16">
              <Input
                id="input_app_1"
                class="margin-top-5"
                v-model="data_search_appKey"
                placeholder="应用标识"
                @on-enter="search"
              ></Input>
            </Col>
          </Col>

          <Col offset="1" span="7">
            <Col span="8" class="margin-top-10">
              <span>应用名称:</span>
            </Col>
            <Col span="16">
              <Input
                id="input_app_2"
                class="margin-top-5"
                v-model="data_search_appName"
                placeholder="应用名称"
                @on-enter="search"
              ></Input>
            </Col>
          </Col>

          <Col offset="1" span="7">
            <Col span="8" class="margin-top-10">
              <span>客户编码:</span>
            </Col>
            <Col span="16">
              <Input
                id="input_app_3"
                class="margin-top-5"
                v-model="data_search_clientCode"
                placeholder="客户编码"
                @on-enter="search"
              ></Input>
            </Col>
          </Col>

          <Col class="margin-top-5" span="7">
            <Col span="8" class="margin-top-10">
              <span>商户编码:</span>
            </Col>
            <Col span="16">
              <Input
                id="input_app_4"
                class="margin-top-5"
                v-model="data_search_commercialCode"
                placeholder="商户编码"
                @on-enter="search"
              ></Input>
            </Col>
          </Col>

          <Col class="margin-top-5" offset="1" span="7">
            <Col span="8" class="margin-top-10">
              <span>应用类型:</span>
            </Col>
            <Col span="16">
              <common-select
                ref="select_appType"
                @on-update="updateSelect_appType"
                type="normal"
                keyWord="result"
                holder="请选择（默认全部）"
                code="code"
                title="name"
                group="app_type"
                @on-loaded="select_callBack"
                :default="this.data_select_appType"
                :uri="this.$store.state.select.app_type.uri"
              ></common-select>
            </Col>
          </Col>

          <Col class="margin-top-5" offset="1" span="7">
            <Col span="8" class="margin-top-10">
              <span>应用状态:</span>
            </Col>
            <Col span="16">
              <common-select
                ref="select_appStatus"
                @on-update="updateSelect_appStatus"
                type="normal"
                keyWord="result"
                holder="请选择（默认全部）"
                code="code"
                title="name"
                group="app_status"
                @on-loaded="select_callBack"
                :default="this.data_select_appStatus"
                :uri="this.$store.state.select.app_status.uri"
              ></common-select>
            </Col>
          </Col>

          <Col class="margin-top-5" span="14">
            <Col span="4" class="margin-top-10">
              <span>创建时间:</span>
            </Col>
            <Col span="18">
              <date-picker
                ref="datepicker"
                @on-start-change="update_start_date"
                @on-end-change="update_end_date"
                :default_start="this.dateStart"
                :default_end="this.dateEnd"
              ></date-picker>
            </Col>
          </Col>
        </Col>

        <Col span="4">
          <Col class="margin-top-10" span="11" style="text-align:center">
            <Button id="btn_app_1" type="primary" v-url="{url:'/rest/app/list'}" @click="search">查询</Button>
          </Col>
          <Col class="margin-top-10" span="11" style="text-align:center">
            <Button id="btn_app_2" type="ghost" @click="reset_search">重置</Button>
          </Col>
        </Col>
      </Row>
      <Row class="margin-top-20">
        <Col span="24">
          <Button
            class="margin-right-10"
            v-url="{url:'/rest/app/create'}"
            type="primary"
            @click="app_add"
          >新增应用</Button>
        </Col>
      </Row>
      <Row class="margin-top-10">
        <Col span="24">
          <Table id="table_1" border :columns="columns_appList" :data="data_appList"></Table>
          <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
            <Page
              class="margin-top-10"
              style="float: right"
              :total="pageTotal"
              :page-size="10"
              :current="pageNo"
              show-elevator
              @on-change="page_refresh"
            ></Page>
          </Tooltip>
        </Col>
        <loading :show="show_loading_appList"></loading>
      </Row>
      <modal_create_edit_app ref="modal_create_edit_app" @search_app_list="search"></modal_create_edit_app>
      <modal_notification_settings ref="modal_notification_settings"  @search_app_list="search"></modal_notification_settings>
    
    </Card>
  </div>
</template>

<script>
import loading from "../my-components/loading/loading";
import commonSelect from "../common-components/select-components/selectCommon";
import util from "../../libs/util";
import api from "../../api/api";
import datePicker from "../common-components/date-components/date-picker";
import modal_create_edit_app from "./modals/modal_create_edit_app";
import modal_notification_settings from "./modals/modal_notification_settings";

export default {
  name: "app-list",
  components: {
    loading,
    commonSelect,
    datePicker,
    modal_notification_settings,
    modal_create_edit_app,
  },
  data() {
    return {
      /**
       * 应用列表部分变量
       */
      // 应用列表loading
      show_loading_appList: false,
      // 搜索框-应用标识数据绑定
      data_search_appKey: "",
      // 搜索框-应用名称数据绑定
      data_search_appName: "",
      // 搜索框-客户编码数据绑定
      data_search_clientCode: "",
      // 搜索框-商户编码数据绑定
      data_search_commercialCode: "",
      // 下拉框-应用类型数据绑定
      data_select_appType: "",
      // 下拉框-应用状态数据绑定
      data_select_appStatus: "",
      // 搜索时间框-创建时间开始时间数据绑定
      dateStart: "",
      // 搜索时间框-创建时间结束时间数据绑定
      dateEnd: "",
      // 下拉框加载统计
      count_select_related: 2,
      // 应用列表表头配置
      columns_appList: [
        {
          title: "应用",
          width: 200,
          render: (h, params) => {
            return h("div", [
              h("p", params.row.appId),
              h("p", "(" + params.row.appName + ")")
            ]);
          },
          align: "center"
        },
        {
          title: "客户编码",
          width: 180,
          key: "subjectNo",
          align: "center"
        },
        {
          title: "商户编码",
          key: "customerNo",
          width: 120,
          align: "center"
        },
        {
          title: "应用类型",
          key: "appType",
          width: 120,
          align: "center",
          render: (h, params) => {
            return h(
              "div",
              this.data_name_handler(params.row.appType, "app_type")
            );
          }
        },
        {
          title: "应用状态",
          key: "status",
          width: 120,
          align: "center",
          render: (h, params) => {
            let color = "red";
            let status = this.data_name_handler(
              params.row.status,
              "app_status"
            );
            if (params.row.status === "ACTIVE") {
              color = "green";
              // status = '活动中';
            } else if (params.row.status === "FORBID") {
              color = "red";
            } else {
              color = "grey";
              // status = '已删除';
            }
            return h("div", [
              h(
                "Tag",
                {
                  style: {
                    align: "center",
                    width: "60px"
                  },
                  props: {
                    color: color
                  }
                },
                status
              )
            ]);
          }
        },
        {
          renderHeader: (h, params) => {
            return h("div", [h("p", "创建时间"), h("p", "最后修改时间")]);
          },
          width: 160,
          align: "center",
          render: (h, params) => {
            return h("div", [
              h("p", params.row.createdDate),
              h("p", params.row.lastModifiedDate)
            ]);
          }
        },
        {
          title: "操作",
          align: "center",
          key: "operations",
          "min-width": 200,
          render: (h, params) => {
            let option = "禁用";
            let optionAction = false;
            let url = "/rest/app/forbid";
            if (params.row.status === "ACTIVE") {
              option = "禁用";
              optionAction = false;
              url = "/rest/app/forbid";
            } else if (params.row.status === "FORBID") {
              option = "启用";
              optionAction = true;
              url = "/rest/app/active";
            } else {
              return "";
            }
            return h("div", [
              h(
                "Button",
                {
                  props: {
                    type: "text",
                    size: "small"
                  },
                  directives: [
                    {
                      name: "url",
                      value: { url: "/rest/app/update" }
                    },
                    {
                      name: "show",
                      value: params.row.appType === "PLATFORM" || params.row.appType === "PARTNER" ||  params.row.appType === "MOBILE"
                    }
                  ],
                  on: {
                    click: () => {
                      this.app_modify(params.row.appId);
                    }
                  }
                },
                "编辑"
              ),
              h(
                "Button",
                {
                  props: {
                    type: "text",
                    size: "small"
                  },
                  directives: [
                    {
                      name: "url",
                      value: { url: url }
                    }
                  ],
                  on: {
                    click: () => {
                      this.app_option(params.row.appId, optionAction);
                    }
                  }
                },
                option
              ),
              h(
                "Button",
                {
                  props: {
                    type: "text",
                    size: "small"
                  },
                  directives: [
                    {
                      name: "url",
                      value: { url: "/rest/app/delete" }
                    },
                    {
                      name: "show",
                      value: params.row.appType === "PLATFORM" || params.row.appType === "PARTNER"||  params.row.appType === "MOBILE"
                    }
                  ],
                  on: {
                    click: () => {
                      this.app_delete(params.row.appId);
                    }
                  }
                },
                "删除"
              ),
              h(
                "Button",
                {
                  props: {
                    type: "text",
                    size: "small"
                  },
                  directives: [
                    {
                      name: "url",
                      value: { url: "/rest/isv/cert/list" }
                    }
                  ],
                  on: {
                    click: () => {
                      this.cert_manage(params.row.appId);
                    }
                  }
                },
                "密钥管理"
              ),
               h(
                "Button",
                {
                  props: {
                    type: "text",
                    size: "small"
                  },
                  directives: [
                    {
                      // name: "url",
                      // value: { url: "/rest/app/update" }
                    },
                    {
                      // name: "show",
                      // value: params.row.appType === "PLATFORM" || params.row.appType === "PARTNER"
                    }
                  ],
                  on: {
                    click: () => {
                      this.notification_settings(params.row.appId,params.row.customerNo);
                    }
                  }
                },
                "商户通知设置"
              )
            ]);
          }
        }
      ],
      // 应用列表数据
      data_appList: [],
      // 总数
      pageTotal: 10,
      // 当前页码
      pageNo: 1
    };
  },
  methods: {
    // 初始化页面
    // init () {
    //   if(this.$store.state.test_template_label){
    //     this.$refs.modal_create_edit_app.form_app.appType = 'PLATFORM_TEST';
    //     this.$refs.modal_create_edit_app.set_modal_preview(true);
    //   }
    // },
    // 数据检索
    search() {
      let params = this.search_pre_option();
      this.list_handler(params);
    },
    // 检索条件初始化
    reset_search() {
      this.$refs.datepicker.reset();
      this.$refs.select_appType.resetSelected();
      this.$refs.select_appStatus.resetSelected();
      this.dateStart = "";
      this.dateEnd = "";
      this.data_search_commercialCode = "";
      this.data_search_clientCode = "";
      this.data_search_appKey = "";
      this.data_search_appName = "";
      this.data_select_appType = "";
      this.data_select_appStatus = "";
    },
    // 请求参数生成前序操作
    search_pre_option() {
      this.show_loading_appList = true;
      let params = {
        appId: this.data_search_appKey.trim(),
        appName: this.data_search_appName.trim(),
        subjectNo: this.data_search_clientCode.trim(),
        customerNo: this.data_search_commercialCode.trim(),
        appType: this.data_select_appType,
        status: this.data_select_appStatus,
        createdStartDate: util.dateFormat_component(this.dateStart),
        createdEndDate: util.dateFormat_component_end(this.dateEnd),
        _pageNo: 1,
        _pageSize: 10
      };
      util.paramFormat(params);
      return params;
    },
    // 列表刷新
    page_refresh(val) {
      let params = this.search_pre_option();
      if (val) {
        params._pageNo = val;
      }
      this.list_handler(params);
    },
    // 列表请求执行函数
    list_handler(params) {
      api.yop_app_list(params).then(response => {
        if (response.data.status === "success") {
          this.tableDataFormat(response.data.data.page.items);
          this.pageNo = response.data.data.page.pageNo;
          if(response.data.data.page.items){
                  if(response.data.data.page.items.length < 10){
                        this.pageTotal=response.data.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
          }
        } else {
          this.$ypMsg.notice_error(
            this,
            "错误",
            response.data.message,
            response.data.solution
          );
        }
        this.show_loading_appList = false;
      });
    },
    // 表格数据处理函数
    tableDataFormat(items) {
      this.data_appList = [];
      for (var i in items) {
        this.data_appList.push({
          appId: util.empty_handler(items[i].appId),
          appName: util.empty_handler(items[i].name),
          subjectNo: util.empty_handler(items[i].subjectNo),
          customerNo: util.empty_handler(items[i].customerNo),
          appType: util.empty_handler(items[i].type),
          status: util.empty_handler(items[i].status),
          createdDate: util.empty_handler(items[i].createdDate),
          lastModifiedDate: util.empty_handler(items[i].lastModifiedDate)
        });
      }
    },
    // 下拉框加载完处理函数
    select_callBack() {
      this.count_select_related--;
      if (this.count_select_related === 0) {
        this.search();
      }
    },
    // 应用类型数据更新
    updateSelect_appType(val) {
      this.data_select_appType = val;
    },
    // 应用状态数据更新
    updateSelect_appStatus(val) {
      this.data_select_appStatus = val;
    },
    // 新增应用
    app_add() {
      this.$refs.modal_create_edit_app.formData_reset();
      this.$refs.modal_create_edit_app.set_modal_status(true);
      this.$refs.modal_create_edit_app.set_modal_preview(true);
    },
    // 应用编辑
    app_modify(appId) {
      this.$refs.modal_create_edit_app.formData_reset();
      let _this = this;
      api.yop_app_detail({ appId: appId }).then(response => {
        let result = response.data.data.result;
        _this.$refs.modal_create_edit_app.set_current_form_data(result);
        _this.$refs.modal_create_edit_app.set_appId(result.appId);
        _this.$refs.modal_create_edit_app.set_modal_status(false);
        _this.$refs.modal_create_edit_app.set_modal_preview(true);
      });
    },
    // 应用启用、禁用
    app_option(appId, optionAction) {
      let titleTemp = "禁用";
      let contentTemp =
        "<p style='color:red'>禁用应用，商户无法调用该应用授权的API接口！</p><p>确定禁用该应用？</p>";
      if (optionAction) {
        titleTemp = "启用";
        contentTemp =
          "<p style='color:red'>启用应用，商户可以调用该应用授权的API接口！</p><p>确定启用该应用？</p>";
      }
      this.$Modal.confirm({
        title: titleTemp,
        content: contentTemp,
        "ok-text": "确认",
        onOk: () => {
          if (optionAction) {
            api.yop_app_active({ appId: appId }).then(response => {
              if (response.status === "success") {
                this.$ypMsg.notice_success(this, "启用应用成功");
                this.search();
              } else {
                this.$ypMsg.notice_error(
                  this,
                  "错误",
                  response.message,
                  response.solution
                );
              }
            });
          } else {
            api.yop_app_forbid({ appId: appId }).then(response => {
              if (response.status === "success") {
                this.$ypMsg.notice_success(this, "禁用应用成功");
                this.search();
              } else {
                this.$ypMsg.notice_error(
                  this,
                  "错误",
                  response.message,
                  response.solution
                );
              }
            });
          }
        }
      });
    },
    // 应用删除
    app_delete(appId) {
      this.$Modal.confirm({
        title: "删除",
        content:
          "<p style='color:red'>删除应用，商户无法调用该应用下授权的API接口？</p><p>确定删除该应用吗？</p>",
        "ok-text": "确认",
        onOk: () => {
          api.yop_app_delete({ appId: appId }).then(response => {
            if (response.status === "success") {
              this.$ypMsg.notice_success(this, "删除应用成功");
              this.search();
            } else {
              this.$ypMsg.notice_error(
                this,
                "错误",
                response.message,
                response.solution
              );
            }
          });
        }
      });
    },
    // 返回相应编码的名字
    data_name_handler(code, name) {
      if (code) {
        if (
          this.$store.state.select[name].data &&
          this.$store.state.select[name].data.length > 0
        ) {
          let group = this.$store.state.select[name].data;
          for (var i in group) {
            if (group[i].value === code) {
              return group[i].name;
            }
          }
        } else {
          return code;
        }
      } else {
        return "";
      }
    },
    // 开始日期更新
    update_start_date(val) {
      this.dateStart = val;
    },
    // 结束日期更新
    update_end_date(val) {
      this.dateEnd = val;
    },
    // 密钥管理跳转
    cert_manage(id) {
      this.$store.state.current_appId = id;
      this.$router.replace({
        name: "/isv/cert/list"
      });
    },
    // 商户通知设置
      notification_settings(appId,customerNo){
          this.$refs.modal_notification_settings.formData_reset();
          let _this = this;
          var result = ""
        console.log(appId)
        api.yop_notify_config_detail({ appId: appId }).then(response => {
          result = response.data.data.result;
          console.log(result)
          if(!result) {
            result = {
              customerNo:customerNo,
            } 
            _this.$refs.modal_notification_settings.set_appId(appId);
          _this.$refs.modal_notification_settings.set_modal_status(true);
          }else{
            _this.$refs.modal_notification_settings.set_appId(result.appId);
          _this.$refs.modal_notification_settings.set_modal_status(false);
          }
          _this.$refs.modal_notification_settings.set_current_form_data(result);
          _this.$refs.modal_notification_settings.set_modal_preview(true);
        });
      }
  },
  // mounted() {
  //   this.init();
  // },
  // beforeRouteLeave(to, from, next) {
  //   this.$store.state.test_template_label = false;
  //   this.$destroy();
  //   next();
  // }
};
</script>

<style scoped>
</style>
