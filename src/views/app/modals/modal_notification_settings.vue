<template>
  <Modal id="modal_request_1" v-model="modal_notification_settings" width="600" :closable="false">
    <p slot="header" style="color:#2d8cf0;">
      <span style="color:black" v-show="create_orEdit">新增商户通知</span>
      <span style="color:black" v-show="!create_orEdit">设置商户通知</span>
    </p>
    <div>
      <Form ref="form_app" :model="form_app" :rules="rule_app" :label-width="120">
        <FormItem label="应用标识：">{{current_appId}}</FormItem>
        <FormItem label="商户编码：">{{form_app.customerNo}}</FormItem>
        <FormItem label="商户通知地址：" prop="notifyUrl">
           <Input type="text" size="small" v-model="form_app.notifyUrl" style="width:80%"></Input>
        </FormItem>
         <p class="yop-explain-120" style="color:red">同一个APPKEY仅支持填写一个http://或者https://开头的商户通知地址。</p>
        <FormItem label="协议类型：" prop="protocol_type">
          <common-select
            ref="select_protocolType_m"
            @on-update="updateSelect_protocolType_m"
            type="normal"
            keyWord="result"
            holder="请选择应用类型"
            code="code"
            title="name"
            group="protocol_type"
            @on-loaded="select_callBack"
            :default="this.form_app.protocol_type"
            size="small"
            :uri="this.$store.state.select.protocol_type.uri"
            style="width:80%"
          ></common-select>
        </FormItem> 
        <p class="" style="margin-bottom:10px;">协议类型说明：</p>
        <p class="" style="margin:0 0 10px 10px">1， 如果商户已对接了商户通知，请选择V1协议；如果商户正准备对接商户通知，请选择V2协议；如果需要升级V1协议到V2协议，需提前告知商户需要重新对接商户通知接口。<span @click="toSettingTip" style="color:#2d8cf0;cursor:pointer">请查看商户通知设置说明<span style="display:inline-block;vertical-align:top;margin-top:-1px;">></span></span></p>
        <p class="" style="margin:0 0 10px 10px;color:red">2，协议类型修改后立即生效，请提前告知商户核实修改协议类型对商户通知影响，再进行处理。</p>
      </Form>
    </div>
    <div slot="footer">
      <Button id="modal_app_btn_2" type="primary" @click="ok_create_app('form_app')">确定</Button>
      <Button id="modal_app_btn_1" type="ghost" @click="cancel_create_app">取消</Button>
    </div>
    <loading :show="show_loading_createApp"></loading>
  </Modal>
</template>

<script>
import loading from "../../my-components/loading/loading";
import commonSelect from "../../common-components/select-components/selectCommon";
import util from "../../../libs/util";
import api from "../../../api/api";

export default {
  name: "modal_notification_settings",
  components: {
    commonSelect,
    loading
  },
  data() {
    // app名称验证
    const validate_app_appName = (rule, value, callback) => {
      if (util.getLength(value) > 60) {
        callback(new Error("长度不能大于60"));
      } else {
        callback();
      }
    };
    const validate_app_customerNo = (rule, value, callback) => {
      setTimeout(() => {
        if (util.getLength(value) > 60) {
          callback(new Error("长度不能大于60"));
        } else {
          api.yop_isv_exists({
              customerNo: value.trim()
            })
            .then(response => {
              if (response.data.status === "success") {
                let result = response.data.data.result;
                if (result) {
                  callback();
                } else {
                  callback(new Error("该商编不存在"));
                }
              } else {
                callback(new Error("商编验重失败"));
              }
            });
        }
      }, 300);
    };
    // app标识验证
    const validate_app_appId = (rule, value, callback) => {
      setTimeout(() => {
        if (util.getLength(value) > 60) {
          callback(new Error("长度不能大于60"));
        } else {
          api.yop_app_exists({
              appId: value.trim(),
              ignoreDeleted : false
            })
            .then(response => {
              if (response.data.status === "success") {
                let result = response.data.data.result;
                if (!result) {
                  callback();
                } else {
                  callback(new Error("该应用标识已存在"));
                }
              } else {
                callback(new Error("应用标识验重失败"));
              }
            });
        }
      }, 300);
    };
    const validate_app_notufyUrl = (rule, value, callback) => {
      var notufyUrlReg = /(http|https):\/\/([\w.]+\/?)\S*/;
      if(notufyUrlReg.test(value)){
        callback()
      }else{
        callback(new Error("商户通知地址格式有误，请重新输入"));
      }
    };
    return {
      // 当前是创建还是编辑 创建：true 编辑：false
      create_orEdit: true,
      // 窗口显示绑定
      modal_notification_settings: false,
      // 表单数据绑定
      form_app: {
        customerNo: "",
        appType: "",
        appName: "",
        appId: "",
        notifyUrl:"",
        protocol_type:"",

      },
      // 数据限制
      rule_app: {
        customerNo: [
          { required: true, message: "商户编码不能为空", trigger: "blur" },
          { validator: validate_app_customerNo, trigger : "blur"}
        ],
        // appType: [{ required: true, message: "应用类型不能为空" }],
        // appName: [
        //   { required: true, message: "应用名称不能为空", trigger: "blur" },
        //   { validator: validate_app_appName, trigger: "blur" }
        // ],
        // appId : [
        //   { required: true, message: "应用标识不能为空", trigger: "blur" },
        //   { validator: validate_app_appId, trigger: "blur" }
        // ],
        protocol_type:[{ required: true, message: "协议不能为空" }],
        notifyUrl:[{required: true, message: "通知地址不能为空" },{ validator: validate_app_notufyUrl, trigger: "blur" }]
      },
      // 当前appId
      current_appId: "",
      // loading 新建应用
      show_loading_createApp: ""
    };
  },
  methods: {
    // 请查看商户通知设置说明
    toSettingTip(){
      window.location.href = "https://open.yeepay.com/docs/platform_profile/5d01ec3514d0aa005d198a90.html"
    },
    // 窗口显示设置
    set_modal_preview(val) {
      this.modal_notification_settings = val;
    },
    // 设置当前为新建或编辑
    set_modal_status(val) {
      this.create_orEdit = val;
    },
    // 设置当前appId
    set_appId(val) {
      this.current_appId = val;
    },
    set_customerNo(val) {
      this.customerNo = val;
    },
    //设置当前表格数据
    set_current_form_data(data) {
      this.form_app.customerNo = data.customerNo;
      this.form_app.appType = data.type;
      this.form_app.appName = util.empty_handler2(data.name);
      this.form_app.notifyUrl = util.empty_handler2(data.notifyUrl);
      this.form_app.protocol_type = util.empty_handler2(data.protocol);
    },
    // 下拉框加载完处理函数
    select_callBack() {
      this.count_select_related--;
      if (this.count_select_related === 0) {
        this.search();
      }
    },
    // 返回相应编码的名字
    data_name_handler(code, name) {
      if (code) {
        if (
          this.$store.state.select[name].data &&
          this.$store.state.select[name].data.length > 0
        ) {
          let group = this.$store.state.select[name].data;
          for (var i in group) {
            if (group[i].value === code) {
              return group[i].name;
            }
          }
        } else {
          return code;
        }
      } else {
        return "";
      }
    },
    // 协议类型数据更新
    updateSelect_protocolType_m(val) {
      this.form_app.protocol_type = val;
    },
    // 新增&修改
    ok_create_app(val) {
      this.$refs[val].validate(valid => {
        if (valid) {
          this.show_loading_createApp = true;
            let param = {
              protocol: this.form_app.protocol_type.trim(),
              notifyUrl: this.form_app.notifyUrl.trim(),
              appId: this.current_appId
            };
            util.paramFormat(param);
            api.yop_notify_config_update(param).then(response => {
              //TODO
              if (response.status === "success") {
                this.$ypMsg.notice_success(
                  this,
                  "设置成功",
                );
                this.$emit("search_app_list");
              } else {
                this.$ypMsg.notice_error(
                  this,
                  "错误",
                  response.message,
                  response.solution
                );
              }
              this.show_loading_createApp = false;
              this.modal_notification_settings = false;
            });
        } else {
          this.$Message.error("请检查");
        }
      });
    },
    // 新增应用取消
    cancel_create_app() {
      this.modal_notification_settings = false;
      this.formData_reset();
    },
    // 列表数据重置
    formData_reset() {
      this.$refs.form_app.resetFields();
      this.$refs.select_protocolType_m.resetSelected();
      this.form_app.customerNo = "";
      this.form_app.appType = "";
      this.form_app.appName = "";
      this.form_app.protocol_type = "";
      this.form_app.notifyUrl = "";
    }
  }
};
</script>

<style scoped>
</style>
