<style lang="less">
    @import '../../styles/common.less';
    @import '../API_Management_Open/api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">

                <Col span="7">
                <Col span="8" class="margin-top-10">
                <span>商户编码:</span>
                </Col>
                <Col span="16">
                <Input id='input_serviceAuth_1' class="margin-top-5" v-model="data_serviceAuth_code" placeholder="商户编码"
                       @on-enter="search_interface(false)"></Input>
                </Col>
                </Col>

                <Col offset="1" span="7">
                <Col span="8" class="margin-top-10">
                <span>应用标识:</span>
                </Col>
                <Col span="16">
                <Input id='input_serviceAuth_2' class="margin-top-5" v-model="data_serviceAuth_appKey" placeholder="应用标识"
                       @on-enter="search_interface(false)"></Input>
                </Col>
                </Col>

                <Col offset="1" span="7">
                <Col span="8" class="margin-top-10">
                <span>服务分组:</span>
                </Col>
                <Col span="16">
                <Input id='input_serviceAuth_3' class="margin-top-5" v-model="data_serviceAuth_serviceGroup" placeholder="服务分组"
                       @on-enter="search_interface(false)"></Input>
                </Col>
                </Col>

                <Col class="margin-top-5" span="7">
                <Col span="8" class="margin-top-10">
                <span>授权状态:</span>
                </Col>
                <Col span="16">
                <common-select ref="select_status" @on-update="updateSelect_status"
                               type="normal"
                               keyWord="result"
                               holder="请选择（默认全部）"
                               code="code"
                               title="name"
                               group="service_authz_status"
                               @on-loaded="select_callBack"
                               :default="this.data_select_status"
                               :uri="this.$store.state.select.service_authz_status.uri"></common-select>
                </Col>
                </Col>

                <Col class="margin-top-5" offset="1" span="7">
                <Col span="8" class="margin-top-10">
                <span>授权有效期:</span>
                </Col>
                <Col span="16">
                <common-select ref="select_validty" @on-update="updateSelect_validty"
                               type="normal"
                               keyWord="result"
                               holder="请选择（默认全部）"
                               code="code"
                               title="name"
                               group="service_authz_expiry"
                               @on-loaded="select_callBack"
                               :default="this.data_select_validty"
                               :uri="this.$store.state.select.service_authz_expiry.uri"></common-select>
                </Col>
                </Col>

                <Col class="margin-top-5" offset="1" span="7">

                </Col>

                </Col>

                <Col span="4">
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_apiM_1' type="primary"   @click="search_interface(false)">查询</Button>
                </Col>
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_apiM_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Button class="margin-right-10"   type="primary" @click="serviceAuth_add">新增服务授权</Button>
                </Col>
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_1' border :columns="columns_serviceAuthList" :data="data_serviceAuthList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10"
                          :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_serviceAuthList"></loading>
            </Row>
            <modal-sp-preview ref="modal_preview" ></modal-sp-preview>
            <modal-sga-expires ref="modal_sga_expand"></modal-sga-expires>
            <modal-sga-new-auth ref="modal_sga_new_auth" @update-list="update_serviceAuthList"></modal-sga-new-auth>
        </Card>
    </div>
</template>

<script>
    import loading from '../my-components/loading/loading'
    import commonSelect from '../common-components/select-components/selectCommon'
    import util from '../../libs/util'
    import api from '../../api/api'
    import modalSpPreview from '../common-components/modal-components/modal-sp-preview'
    import modalSgaExpires from '../common-components/modal-components/modal-sga-expand-expires'
    import modalSgaNewAuth from '../common-components/modal-components/modal-sga-new-auth'
    export default {
        name: 'isp-list',
        components:{
            loading,
            commonSelect,
            modalSpPreview,
            modalSgaExpires,
            modalSgaNewAuth
        },
        data(){
            return {
                // 授权类型列表
                type_list : [],
                // 列表loading
                show_loading_serviceAuthList : false,
                // 商户编码输入框数据绑定
                data_serviceAuth_code : '',
                // 应用标识输入框数据绑定
                data_serviceAuth_appKey : '',
                // 服务分组输入框数据绑定
                data_serviceAuth_serviceGroup : '',
                // 授权状态下拉框数据绑定
                data_select_status : '',
                // 授权有效期数据绑定
                data_select_validty : '',
                // 下拉框个数
                count_select_related : 2,
                // 总数
                pageTotal : 10,
                // 当前页码
                pageNo : 1,
                // 服务授权列表表头
                columns_serviceAuthList : [

                    {
                        title: '商户名称',
                        width: 180,
                        render: (h, params) => {
                            if(params.row.merchantName){
                                return h('div', [
                                    h('p', params.row.merchantName),
                                    h('p', '(' + params.row.merchantCode + ')')
                                ]);
                            }else{
                                return h('div',  params.row.merchantCode);
                            }
                        },
                        align: 'center'
                    },
                    {
                        title: '应用标识',
                        width: 200,
                        render: (h, params) => {
                            if(util.emptyCheck(params.row.appKeyCode)){
                                if(params.row.appKeyName){
                                    return h('div', [
                                        h('p', params.row.appKeyName),
                                        h('p', '(' + params.row.appKeyCode + ')')
                                    ]);
                                }else{
                                    return h('div', params.row.appKeyCode);
                                }

                            }else{
                                return h('div', '全部');
                            }

                        },
                        align: 'center'
                    },
                    {
                        title: '服务分组',
                        width: 160,
                        render: (h, params) => {
                            let _this = this
                            return h('div', [
                                h('p', params.row.serviceGroupName),
                                h('p',[
                                    h('a',
                                        {
                                            on: {
                                                click: function(){
                                                    _this.serviceGroupCode_click(params.row.serviceGroupCode);
                                                }
                                            },
                                        },'(' + params.row.serviceGroupCode + ')')
                                ])
                            ]);
                        },
                        align: 'center'
                    },
                    {
                        title: '授权有效期',
                        width: 130,
                        align: 'center',
                        render: (h, params) => {
                            if(params.row.authTimeStart === '' && params.row.authTimeEnd === ''){
                                return h('div', '永久有效');
                            }else{
                                return h('div', [h('p', util.empty_handler(params.row.authTimeStart)),
                                    h('p', util.empty_handler(params.row.authTimeEnd))]);
                            }

                        }
                    },
                    {
                        title: '状态',
                        render: (h,params) => {
                            let color = 'red';
                            let status = '已授权';
                            if(params.row.status === 'AUTHORIZED'){
                                color ='green';
                                status = '已授权';
                            }else if (params.row.status === 'CANCELLED'){
                                color ='grey';
                                status = '已取消';
                            }else{
                                color = 'red';
                                status = '已过期';
                            }
                            return h('div',[
                                h('Tag',{
                                    style:{
                                        align :'center'
                                    },
                                    props:{
                                        color: color
                                    }
                                },status)
                            ])
                        },
                        width: 120,
                        align: 'center'
                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ]);
                        },
                        width: 160,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [h('p', params.row.createTime),
                                h('p', params.row.lastModifyTime)]);
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'operations',
                        'min-width': 150,
                        render: (h, params) => {
                            if(params.row.status === 'EXPIRED'){
                                return h('div', [
                                    h('Button', {
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        // directives: [{
                                        //     name: 'url',
                                        //     value: {url: '/rest/backend-app/update'}
                                        // }],
                                        on: {
                                            click: () => {
                                                this.reAuth(params.row);
                                            }
                                        }
                                    }, '重新授权')
                                ]);
                            }else{
                                return h('div', [
                                    h('Button', {
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        // directives: [{
                                        //     name: 'url',
                                        //     value: {url: '/rest/backend-app/update'}
                                        // }],
                                        on: {
                                            click: () => {
                                                this.expandExpires(params.row);
                                            }
                                        }
                                    }, '展期'),
                                    h('Button', {
                                        props: {
                                            type: 'text',
                                            size: 'small',

                                        },
                                        directives: [{
                                            name: 'show',
                                            value: params.row.status ==='AUTHORIZED'
                                        }],
                                        // directives: [{
                                        //     name: 'url',
                                        //     value: {url: '/rest/backend-app/delete'}
                                        // }],
                                        on: {
                                            click: () => {
                                                this.cancel_auth(params.row.id);
                                            }
                                        }
                                    }, '取消授权'),
                                    h('Button', {
                                        props: {
                                            type: 'text',
                                            size: 'small'
                                        },
                                        // directives: [{
                                        //     name: 'url',
                                        //     value: {url: '/rest/backend-app/delete'}
                                        // }],
                                        directives: [{
                                            name: 'show',
                                            value: params.row.status ==='CANCELLED'
                                        }],
                                        on: {
                                            click: () => {
                                                this.recover_auth(params.row.id);
                                            }
                                        }
                                    }, '恢复授权')
                                ]);
                            }
                        }

                    }
                ],
                // 服务授权数据
                data_serviceAuthList: [],
            }
        },
        methods:{
            // 服务授权列表查询函数
            search_interface (val) {
                this.show_loading_serviceAuthList = true;
                let params = {
                    customerNo: this.data_serviceAuth_code.trim(),
                    appKey: this.data_serviceAuth_appKey.trim(),
                    code: this.data_serviceAuth_serviceGroup.trim(),
                    status : this.data_select_status,
                    expiryTime : this.data_select_validty,
                    _pageNo: 1,
                    _pageSize: 10
                };
                if (val) {
                    params._pageNo = val;
                }
                util.paramFormat(params);
                api.yop_service_authz_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                            if(response.data.data.page.items && response.data.data.page.items.length > 0){
                                if(response.data.data.page.items.length < 10){
                                    this.pageTotal=response.data.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                        } else {
                            this.$ypMsg.notice_error(this,'服务分组授权获取错误',response.data.message,response.data.solution);
                        }
                        this.show_loading_serviceAuthList = false;
                    }
                );
            },
            // 列表数据处理
            tableDataFormat (items){
                this.data_serviceAuthList = [];
                for (var i in items) {
                    this.data_serviceAuthList.push({
                        id: util.empty_handler2(items[i].id),
                        merchantCode: util.empty_handler(items[i].customerNo),
                        merchantName: util.empty_handler2(items[i].customerName),
                        appKeyCode : util.empty_handler2(items[i].appKey),
                        appKeyName : util.empty_handler2(items[i].appName),
                        serviceGroupCode  : util.empty_handler(items[i].code),
                        serviceGroupName : util.empty_handler(items[i].name),
                        authTimeStart : util.empty_handler2(items[i].startDate),
                        authTimeEnd : util.empty_handler2(items[i].endDate),
                        status: util.empty_handler(items[i].status),
                        createTime : util.empty_handler(items[i].createDate),
                        lastModifyTime : util.empty_handler(items[i].lastModifiedDate)
                    });
                }
            },
            // 重置查询添加函数
            reset_Interface () {
                this.data_serviceAuth_code = '';
                this.data_serviceAuth_appKey = '';
                this.data_serviceAuth_serviceGroup = '';
                this.$refs.select_validty.resetSelected();
                this.$refs.select_status.resetSelected();
            },
            // 新增服务授权函数
            serviceAuth_add  () {

                this.$refs.modal_sga_new_auth.preview_show();
                this.$refs.modal_sga_new_auth.init();
                this.$refs.modal_sga_new_auth.date_picker_init();
                if(this.data_serviceAuth_code){
                    this.$refs.modal_sga_new_auth.customer_no_set(this.data_serviceAuth_code);
                }
            },
            // 下拉框加载完处理函数
            select_callBack () {
                this.count_select_related--;
                if (this.count_select_related === 0) {
                    this.search_interface();
                }
            },
            // 授权状态数据更新函数
            updateSelect_status (val) {
                this.data_select_status = val;
            },
            // 授权有效期数据更新函数
            updateSelect_validty (val) {
                this.data_select_validty = val;
            },
            // 页面刷新
            pageRefresh (val) {

            },
            // 初始化页面
            init  () {
                this.type_list_get();
            },
            // 数据类型获取
            type_list_get () {
                api.yop_service_group_auth_type().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.type_list = [];
                        let dataTemp = [];
                        if(resultTemp && resultTemp.length >0) {
                            resultTemp.forEach(
                                (item)=> {
                                    dataTemp.push(
                                        {
                                            label: item.name,
                                            name : item.code
                                        }
                                    )
                                })
                        }
                        this.type_list = dataTemp;
                    }
                )
            },
            // 服务编码点击
            serviceGroupCode_click (code){
                this.$refs.modal_preview.manage_show();
                this.$refs.modal_preview.tab_init();
                this.$refs.modal_preview.preview_show(false);
                this.$refs.modal_preview.detail_info_get(code,this.type_list);
            },
            // 重新授权
            reAuth (data){
                this.$refs.modal_sga_expand.current_func = false;
                this.expandExpires_init(data);
                // 授权
                // api.yop_service_authz_create(param).then(
                //     (response) =>{
                //         if (response.status === 'success') {
                //             if(response.data.result === 'success'){
                //                 this.$Notice.success({
                //                     title : '成功',
                //                     desc : '删除成功',
                //                     duration : 10
                //                 });
                //                 this.remove(node, data);
                //                 this.$refs.card_edit_sg.preview_cancel();
                //             }else{
                //                 // 请求
                //                 setTimeout (() =>{
                //                     let paramTemp = {
                //                         codes : [data.code],
                //                         force : true
                //                     }
                //                     this.delete_admit(node,data,paramTemp);
                //                 },500);
                //             }
                //         } else {
                //             this.$Notice.error({
                //                 title : '删除错误',
                //                 desc : response.message,
                //                 duration : 10
                //             });
                //         }
                //     }
                // );
            },
            // 展期
            expandExpires (data){
                this.$refs.modal_sga_expand.current_func = true;
                this.expandExpires_init(data);
            },
            // 初始化 展期/重新授权 界面
            expandExpires_init(data){
                this.$refs.modal_sga_expand.set_current_id(data.id);
                this.$refs.modal_sga_expand.preview_show();
                this.$refs.modal_sga_expand.date_picker_init(data.authTimeStart,data.authTimeEnd);
            },
            // 恢复授权
            recover_auth (id) {
                api.yop_service_authz_enable({id:id}).then(
                    (response) =>{
                        if (response.status === 'success') {
                            this.$ypMsg.notice_success(this,'恢复授权成功');
                        } else {
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                        this.search_interface(false);
                    }
                );

            },
            // 取消授权
            cancel_auth (id){
                api.yop_service_authz_disable({id:id}).then(
                    (response) =>{
                        if (response.status === 'success') {
                            this.$ypMsg.notice_success(this,'取消授权成功');
                        } else {
                            this.$ypMsg.notice_error(this,'取消授权错误',response.message,response.solution);
                        }
                        this.search_interface(false);
                    }
                );
            },
            update_serviceAuthList(custom,appKey){
               this.reset_Interface();
               this.data_serviceAuth_code = custom;
               this.data_serviceAuth_appKey = appKey;
               this.search_interface();
            }
        },
        mounted () {
            this.init();
        },

    };
</script>

<style scoped>

</style>
