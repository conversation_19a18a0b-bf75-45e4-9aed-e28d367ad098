<style lang="less">
    @import "./main.less";
    .yp-margin-right-5{
        margin-right:5px;
        color: #19be6b;
    }
    .yp-margin-right-15{
        margin-right:15px;
    }
</style>
<template>
    <div class="main" :class="{'main-hide-text': shrink}">
        <div class="sidebar-menu-con" :style="{width: shrink?'60px':'200px', overflow: shrink ? 'visible' : 'auto'}">
            <shrinkable-menu
                :shrink="shrink"
                :theme="menuTheme"
                :open-names="openedSubmenuArr"
                :menu-list="menuList">
                <div slot="top" class="logo-con">
                    <div v-show="!shrink" class="yp-header-title" key="max-logo">易宝 · 运营后台</div>
                    <!--<img v-show="!shrink"  src="../images/logo.png" key="max-logo" />-->
                    <img v-show="shrink" src="../images/logo-min.png" key="min-logo" />
                </div>
            </shrinkable-menu>
        </div>
        <!--<div class="main-header" :style="{paddingLeft: shrink?'60px':'200px'}">-->
        <div class="main-header-con" :style="{paddingLeft: shrink?'60px':'200px'}">
            <div class="main-header">
                <div class="navicon-con">
                    <Button :style="{transform: 'rotateZ(' + (this.shrink ? '-90' : '0') + 'deg)'}" type="text" @click="toggleClick">
                        <Icon type="navicon" size="32"></Icon>
                    </Button>
                </div>
                <div class="header-middle-con">
                    <div class="main-breadcrumb">
                        <breadcrumb-nav :currentPath="currentPath"></breadcrumb-nav>
                    </div>
                </div>
                <div class="header-avator-con" style="float:right"> 
                    <div class="logout-btn-con">
                        <span class="yp-margin-right-15"><Icon type="person" class="yp-margin-right-5"></Icon>{{userName}}</span>
                        <Button type="error" @click="logout" icon="log-out">登出</Button>
                    </div>
                </div>
                <!--<div class="header-avator-con" style="float:right">-->
                    <!--<full-screen v-model="isFullScreen" @on-change="fullscreenChange"></full-screen>-->
                    <!--<lock-screen></lock-screen>-->
                    <!--<message-tip v-model="mesCount"></message-tip>-->
                    <!--<theme-switch></theme-switch>-->

                    <!--<div class="user-dropdown-menu-con">-->
                        <!--<Row type="flex" justify="end" align="middle" class="user-dropdown-innercon">-->
                            <!--<Dropdown transfer trigger="click" @on-click="handleClickUserDropdown">-->
                                <!--<a href="javascript:void(0)">-->
                                    <!--<span class="main-user-name">{{ userName }}</span>-->
                                    <!--<Icon type="arrow-down-b"></Icon>-->
                                <!--</a>-->
                                <!--<DropdownMenu slot="list">-->
                                    <!--<DropdownItem name="ownSpace">个人中心</DropdownItem>-->
                                    <!--<DropdownItem name="loginout" divided>退出登录</DropdownItem>-->
                                <!--</DropdownMenu>-->
                            <!--</Dropdown>-->
                            <!--<Avatar :src="avatorPath" style="background: #619fe7;margin-left: 10px;"></Avatar>-->
                        <!--</Row>-->
                    <!--</div>-->
                <!--</div>-->
            </div>
            <!--<div class="tags-con">-->
                <!--<tags-page-opened :pageTagsList="pageTagsList"></tags-page-opened>-->
            <!--</div>-->
        </div>
        <div class="single-page-con" :style="{marginLeft: shrink?'60px':'200px', height: clientHeight + 'px'}" >
        <!--<div class="single-page-con" :style="{left: shrink?'60px':'200px'}">-->
            <div class="single-page">
                <keep-alive :include="cachePage">
                    <router-view></router-view>
                </keep-alive>
            </div>
        </div>
    </div>
</template>
<script>
    import shrinkableMenu from './main-components/shrinkable-menu/shrinkable-menu.vue';
    import tagsPageOpened from './main-components/tags-page-opened.vue';
    import breadcrumbNav from './main-components/breadcrumb-nav.vue';
    import fullScreen from './main-components/fullscreen.vue';
    import lockScreen from './main-components/lockscreen/lockscreen.vue';
    import messageTip from './main-components/message-tip.vue';
    import themeSwitch from './main-components/theme-switch/theme-switch.vue';
    import Cookies from 'js-cookie';
    import util from '~/libs/util.js';
    import api from '../api/api'
    import Main from './Main'
    import {page404} from '../router/router'
    export default {
        components: {
            shrinkableMenu,
            tagsPageOpened,
            breadcrumbNav,
            fullScreen,
            lockScreen,
            messageTip,
            themeSwitch,
            Main
        },
        data () {
            return {
                shrink: false,
                clientHeight: 700,
                userName: '',
                userId:'',
                isFullScreen: false,
                openedSubmenuArr: this.$store.state.app.openedSubmenuArr
            };
        },
        computed: {
            menuList () {
                return this.$store.state.app.menuList
            },
            pageTagsList () {
                return this.$store.state.app.pageOpenedList; // 打开的页面的页面对象
            },
            currentPath () {
                return this.$store.state.app.currentPath; // 当前面包屑数组
            },
            avatorPath () {
                return localStorage.avatorImgPath;
            },
            cachePage () {
                return this.$store.state.app.cachePage;
            },
            lang () {
                return this.$store.state.app.lang;
            },
            menuTheme () {
                return this.$store.state.app.menuTheme;
            },
            mesCount () {
                return this.$store.state.app.messageCount;
            }
        },
        methods: {
            init () {
                let pathArr = util.setCurrentPath(this, this.$route.name);

                if (pathArr.length >= 2) {
                    this.$store.commit('addOpenSubmenu', pathArr[1].name);
                }
                // this.userName = Cookies.get('user');
                this.userName = localStorage.userName;
                this.userId = localStorage.userId;
                let messageCount = 3;
                this.messageCount = messageCount.toString();
                this.checkTag(this.$route.name);
                this.$store.commit('setMessageCount', 3);
            },
            toggleClick () {
                this.shrink = !this.shrink;
            },
            menu_handler (data) {
                let children = [];
                if(data){
                    data.forEach(
                        (item)=>{
                            children.push({
                                path : item.url,
                                icon : 'ios-list',
                                name: item.url,
                                title : item.name,
                                component : () => import('~/views'+item.url+'.vue')
                            })
                        }
                    )
                    return children
                }else{
                    return children
                }
            },
            handleClickUserDropdown (name) {
                if (name === 'ownSpace') {
                    util.openNewPage(this, 'ownspace_index');
                    this.$router.push({
                        name: 'ownspace_index'
                    });
                } else if (name === 'loginout') {
                    // 退出登录
                    this.$store.commit('logout', this);
                    this.$store.commit('clearOpenedSubmenu');
                    this.$router.push({
                        name: 'login'
                    });
                }
            },
            checkTag (name) {
                let openpageHasTag = this.pageTagsList.some(item => {
                    if (item.name === name ) {
                        return true;
                    }
                });
                if (!openpageHasTag) { //  解决关闭当前标签后再点击回退按钮会退到当前页时没有标签的问题
                    util.openNewPage(this, name, this.$route.params || {}, this.$route.query || {});
                }
            },
            fullscreenChange (isFullScreen) {
                // console.log(isFullScreen);
            },
            logout () {
                const logoutUrl = localStorage.logoutUrl;
                localStorage.clear();
                location.href = logoutUrl
            },
            computedClientHeight() {
                this.clientHeight = document.documentElement.clientHeight - 60
            }
        },
        watch: {
            '$route' (to) {
                this.$store.commit('setCurrentPageName', to.name);
                let pathArr = util.setCurrentPath(this, to.name);
                if (pathArr.length > 2) {
                    this.$store.commit('addOpenSubmenu', pathArr[1].name);
                }
                this.checkTag(to.name);
                localStorage.currentPageName = to.name;
            },
            lang () {
                util.setCurrentPath(this, this.$route.name); // 在切换语言时用于刷新面包屑
            }
        },
        mounted () {
            this.init();
        },
        created () {
            // 显示打开的页面的列表
            this.$store.commit('setOpenedList');
            window.addEventListener('resize', this.computedClientHeight)
            this.computedClientHeight()

        },
        destroyed () {
            window.removeEventListener('resize', this.computedClientHeight)
        } 
    };
</script>
