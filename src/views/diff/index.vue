<template>
  <div style="background: #eee;padding:8px;" v-loading="loading">
    <Card :bordered="false">
      <div style="margin-bottom: 16px;">
        <span>文档名称：</span>
        <el-select
          style="width:300px;"
          size="mini"
          v-model="value"
          @change="change"
          filterable
          placeholder="请选择对比页面"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.fullTitle"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div>
      <div id="file-compare"></div>
    </Card>
  </div>
</template>
<script>
import Api from '~/api/doc/apiClass'
export default {
	data() {
		return {
			loading: false,
      options: [],
			num: 0,
      dv: {},
      value: '',
      docNo: ''
		}
	},
	created() {
    const { docNo, currentDocVersion } = this.$route.query
    this.docNo = docNo
		this.diffList({
      docNo,
      currentDocVersion
    })
	},
	methods: {
		diffList(params) {
			this.loading = true
			Api.diffList(params)
        .then((res) => {
          this.options = res.data.data.result
          if (this.options && this.options.length > 0) {
            this.value = this.options[0].id
            this.change()
          }
          this.loading = false
				})
				.catch((err) => {
					this.loading = false
				})
    },
    change() {
      const { currentDocVersion = '', lastDocVersion = '', id } = this.options.find(o => o.id === this.value)
      this.getContent({
        currentDocVersion,
        pageId: id,
        lastDocVersion
      })
    },
		getContent(params) {
			this.loading = true
			Api.diff(params)
        .then((res) => {
					const { currentDocVersion, lastDocVersion } = res.data.result
					const value = this.handlerData(currentDocVersion)
					const orig = this.handlerData(lastDocVersion)
					this.setData(value, orig)
					this.loading = false
				})
				.catch((err) => {
					this.loading = false
					this.$ypMsg.notice_error(this, '错误', err.message, err.solution)
				})
		},
		handlerData(data) {
      if (!data) return ''
			let str = data
      try {
        str = JSON.parse(data).reduce((str, item) => {
          str += item.data
          return str
        }, '')
      } catch (error) {
      }
      return str.replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '\'')
        .replace(/&#39;/g, '\'')
        .replace(/<\/string,string><\/string,string>/g, '')
        .replace(/<\/string,>/g, '')
        .replace(/<\/string,string>/g, '')
		},
    setData(value, orig) {
			this.origId = []
			const target = document.getElementById('file-compare')
			target.innerHTML = ''
			this.dv = CodeMirror.MergeView(target, {
				value,
				origLeft: null,
				orig,
				lineNumbers: true,
				highlightFormatting: true,
				allowEditingOriginals: false,
				mode: 'markdown',
				highlightDifferences: true,
				connect: 'align',
        collapseIdentical: false,
        readOnly: true
      })
		},
	},
}
</script>
<style>
.CodeMirror-merge,
.CodeMirror-merge .CodeMirror {
	height: calc(100vh - 176px);
}
.CodeMirror-merge-r-chunk-end {
	border-bottom: 1px solid #f5e6e6;
}
.CodeMirror-merge-r-chunk-start {
	border-top: 1px solid #f5e6e6;
}
.CodeMirror-merge-r-chunk {
	background: #fff1ec;
}
</style>
