<style lang="less">
    @import '../../styles/common.less';
    @import '../API_Management_Open/api_Mangement_Open.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-heigth:16px;text-align:center;color:#f00;text-decoration:none}
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">

                <Col span="7">
                <Col span="8" class="margin-top-10">
                <span>服务提供方:</span>
                </Col>
                <Col span="16">
                <common-select id='select_isp_1' ref="select_sp" @on-update="updateSelect_sp"
                               type="combo"
                               keyWord="result"
                               code="spCode"
                               title="spName"
                               group="serviceSupplier"
                               :default="this.data_select_sp"
                               @on-loaded="select_callBack"
                               holder="请选择（默认全部）"
                               :uri="this.$store.state.select.serviceSupplier.uri"></common-select>
                </Col>
                </Col>

                <Col offset="1" span="7">
                <Col span="8" class="margin-top-10">
                <span>状态:</span>
                </Col>
                <Col span="16">
                <common-select id='select_isp_2' ref="select_status" @on-update="updateSelect_status"
                               holder="选择状态（默认全部）"
                               type="normal"
                               keyWord="result"
                               group="user_status"
                               code="code"
                               title="name"
                               @on-loaded="select_callBack"
                               :default="this.data_select_sp_status"
                               :uri="this.$store.state.select.user_status.uri"></common-select>
                </Col>
                </Col>

                <Col class="margin-top-5" span="7">

                </Col>

                </Col>

                <Col span="4">
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_isp_1' type="primary" v-url="{url:'/rest/isp/list'}"  @click="search_interface">查询</Button>
                </Col>
                <Col class="margin-top-10" span="11" style="text-align:center">
                <Button id='btn_isp_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Button id='btn_isp_3' class="margin-right-10"  v-url="{url:'/rest/isp/create'}" type="primary" @click="sp_add">新增服务提供方</Button>
                </Col>
                <!--<Col span="12"  style="margin-top: 10px;text-align: right;">-->
                <!--<Col offset="11" span="6" >-->
                <!--临近授权过期天数：-->
                <!--</Col>-->
                <!--<Col span="7">-->
                <!--<ButtonGroup size="small" >-->
                    <!--<Button type="ghost" >全部</Button>-->
                    <!--<Button type="ghost" >1</Button>-->
                    <!--<Button type="ghost" >5</Button>-->
                    <!--<Button type="ghost" >10</Button>-->
                    <!--<Button type="ghost" >15</Button>-->
                    <!--<Button type="ghost" >30</Button>-->
                <!--</ButtonGroup>-->
                <!--</Col>-->
                <!--</Col>-->
            </Row>
            <Row class="margin-top-10">
                <Col span="24">
                <Table id='table_isp_1' border :columns="columns_spList" :data="data_spList"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10"
                          :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_spList"></loading>
            </Row>
            <Modal id="modal_isp_1" v-model="modal_sp" width="800" :closable="false">
                <p slot="header" style="color:#2d8cf0;">
                    <span style="color:black" v-show="currentStatus">新增服务提供方</span>
                    <span style="color:black" v-show="!currentStatus">编辑服务提供方</span>
                </p>
                <div>
                    <Form ref="form_sp" :model="form_sp" :rules="rule_sp" :label-width="120">
                        <FormItem label="服务提供方编码：" v-show="currentStatus" prop="spCode">
                            <Input id='modal_isp_input_1' type="text" size="small" v-model="form_sp.spCode" style="width:90%"></Input>
                        </FormItem>
                        <p class="yop-explain-120" v-show="currentStatus">首字符不为数字，只支持大写字母、中划线、下划线、数字，最长可输入14个字符</p>
                        <FormItem label="服务提供方编码：" v-show="!currentStatus">
                            {{form_sp.spCode}}
                        </FormItem>
                        <FormItem label="服务提供方名称：" prop="spName">
                            <Input id='modal_isp_input_2' type="text" size="small" v-model="form_sp.spName" style="width:90%"></Input>
                        </FormItem>
                        <p class="yop-explain-120" v-show="currentStatus">最长可输入64个字符</p>
                        <FormItem label="岗位：" v-show="currentStatus">
                            <common-select id='modal_isp_select_1' ref="position" @on-update="updateSelect_post_model"
                                           type="normal"
                                           keyWord="result"
                                           holder="选择用户岗位"
                                           code="code"
                                           title="name"
                                           size="small"
                                           group="position"
                                           @on-loaded="select_callBack"
                                           :default="this.data_select_post_model"
                                           :uri="this.$store.state.select.position.uri"
                                           style="width:90%"></common-select>
                        </FormItem>
                        <FormItem label="输入用户名：" v-show="currentStatus">
                            <el-select
                                    id='modal_isp_select_2'
                                    style="width:90%"
                                    v-model="form_sp.inputUserName"
                                    filterable
                                    size="mini"
                                    allow-create
                                    clearable
                                    default-first-option
                                    placeholder="用户名">
                                <el-option
                                        v-for="item in data_userNameList_modal"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                            <!--<Input type="text" size="small" v-model="form_role.roleCode" style="width:90%"></Input>-->
                            <span><Button id='modal_isp_btn_1' type="primary" size="small" @click="addUser()">添加</Button></span>
                        </FormItem>
                        <FormItem label="确定用户名：" v-show="currentStatus">
                            <Card dis-hover  style="height:180px;width: 90%">
                                <Scroll height="160">
                                    <Tag v-for="item in auth_user_list" :key="item" :name="item" closable @on-close="handleClose2">{{item}}</Tag>
                                </Scroll>
                            </Card>
                        </FormItem>
                        <FormItem label="描述：" prop="description">
                            <Input type="textarea" size="small" v-model="form_sp.description"
                                   style="width:90%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">最长可输入100个字符</p>
                    </Form>
                </div>
                <div slot="footer">
                    <Button id="modal_isp_btn_2" type="primary" @click="ok_create_sp('form_sp')">确定
                    </Button>
                    <Button id="modal_isp_btn_3" type="ghost" @click="cancel_create_sp">取消</Button>
                </div>
            </Modal>
        </Card>
    </div>
</template>

<script>
    import loading from '../my-components/loading/loading'
    import commonSelect from '../common-components/select-components/selectCommon'
    import util from '../../libs/util'
    import api from '../../api/api'
    export default {
        name: 'isp-list',
        components:{
            loading,
            commonSelect
        },
        data(){
            // 服务提供方编码校验规则
            const validate_sp_code = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('服务提供方编码不能为空'));
                }else if(util.format_check_common(value,/^[A-Z0-9_-]+?$/) || !util.format_check_common(value,/^\d/)){
                    callback(new Error('格式不正确'));
                }else if(util.getLength(value) > 14){
                    callback(new Error('长度不能大于14'));
                }else{
                    callback();
                }
            };
            // 服务提供方编码校验规则
            const validate_sp_name = (rule, value, callback) => {
                if (!value.trim()) {
                    return callback(new Error('服务提供方编码不能为空'));
                }else if(util.getLength(value) > 64){
                    callback(new Error('长度不能大于64'));
                }else{
                    callback();
                }
            };
            // 描述输入验证
            const validate_sp_description = (rule, value, callback) => {
                if(util.getLength(value) > 100){
                    callback(new Error('长度不能大于100'));
                }else{
                    callback();
                }
            };
            return {
                // 授权用户角色下拉框列表数据
                data_userNameList_modal : [],
                // 职位数据绑定
                data_select_post_model : '',
                // 新建还是编辑数据绑定 true为新建 false为编辑
                currentStatus : true,
                // sp列表loading
                show_loading_spList : false,
                // 选择的sp状态绑定
                data_select_sp_status : '',
                // sp下拉框数据绑定
                data_select_sp : '',
                // 下拉框个数
                count_select_related : 2,
                // 总数
                pageTotal : 10,
                // 当前页码
                pageNo : 1,
                // 后端应用列表表头
                columns_spList : [

                    {
                        title: '服务提供方',
                        width: 200,
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.spName),
                                h('p', '(' + params.row.spCode + ')')
                            ]);
                        },
                        align: 'center'
                    },
                    // {
                    //     title: '管理员',
                    //     width: 200,
                    //     render: (h, params) => {
                    //         return h('div', params.row.admin.map((item) =>{
                    //             return h('p',item.name)
                    //         }));
                    //     },
                    //     align: 'center'
                    // },
                    {
                        title: '描述',
                        key: 'description',
                        'min-width': 200
                    },
                    {
                        title: '状态',
                        render: (h,params) => {
                            let color = 'red';
                            let status = this.data_name_handler(params.row.status,'user_status');
                            if(params.row.status === 'NORMAL'){
                                color ='green';
                                // status = '活动中';
                            }else{
                                color = 'red';
                                // status = '已删除';
                            }
                            return h('div',[
                                h('Tag',{
                                    style:{
                                        align :'center',
                                        width: '60px'
                                    },
                                    props:{
                                        color: color
                                    }
                                },status)
                            ])

                        },
                        width: 120,
                        align: 'center'
                    },
                    {
                        renderHeader: (h, params) => {
                            return h('div', [
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ]);
                        },
                        width: 160,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [h('p', params.row.createTime),
                                h('p', params.row.lastModifyTime)]);
                        }
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'operations',
                        width: 200,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/isp/edit'}
                                    },{
                                        name: "show",
                                        value: params.row.status !== 'DELETED'
                                    }],
                                    on: {
                                        click: () => {
                                            this.sp_modify(params.row.spCode);
                                        }
                                    }
                                }, '编辑'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/isp/delete'}
                                    },{
                                        name: "show",
                                        value: params.row.status !== 'DELETED'
                                    }],
                                    on: {
                                        click: () => {
                                            this.sp_delete(params.row.spCode);
                                        }
                                    }
                                }, '删除')
                            ]);
                        }

                    }
                ],
                // 授权用户列表
                auth_user_list : [],
                // 后端应用数据
                data_spList: [
                    // {
                    //     spCode: 'spCode',
                    //     spName: '服务提供方编码',
                    //     admin: [{
                    //         code: 'lele',
                    //         name: '乐乐'
                    //     },
                    //         {
                    //             code: 'xuqian',
                    //             name: '徐倩'
                    //         }],
                    //     description: '我是描述',
                    //     status: 'ACTIVE',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-03 18:40:09'
                    // },{
                    //     spCode: 'spCode',
                    //     spName: '服务提供方编码',
                    //     admin: [{
                    //         code: 'lele',
                    //         name: '乐乐'
                    //     },
                    //         {
                    //             code: 'xuqian',
                    //             name: '徐倩'
                    //         }],
                    //     description: '我是描述',
                    //     status: 'FROZEN',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-03 18:40:09'
                    // },{
                    //     spCode: 'spCode',
                    //     spName: '服务提供方编码',
                    //     admin: [{
                    //         code: 'lele',
                    //         name: '乐乐'
                    //     },
                    //         {
                    //             code: 'xuqian',
                    //             name: '徐倩'
                    //         }],
                    //     description: '我是描述',
                    //     status: 'DELETE',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-03 18:40:09'
                    // },
                ],
                // 表单数据绑定
                form_sp : {
                    spCode: '',
                    spName : '',
                    inputUserName : '',
                    description : ''
                },
                // 表单校验规则
                rule_sp : {
                     spCode : [
                         {required: true, message: '服务提供方编码不能为空', trigger: 'blur'},
                         { validator: validate_sp_code, trigger: 'blur' }
                     ],
                    spName : [
                        {required: true, message: '服务提供方名称不能为空', trigger: 'blur'},
                        { validator: validate_sp_name, trigger: 'blur' }
                    ],
                    description : [
                        { validator: validate_sp_description, trigger: 'blur' }
                    ]
                },
                // sp窗口显示
                modal_sp : false,
            }
        },
        methods:{
            // 确定批量添加用户
            ok_create_sp (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        // 更新下拉框
                        if(this.currentStatus){
                            let  param= {
                                spCode : this.form_sp.spCode,
                                spName : this.form_sp.spName,
                                managerCodes: this.auth_user_list,
                                description : this.form_sp.description
                            }
                            util.paramFormat(param);
                            api.yop_isp_create(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        if(localStorage.needAudit === 'true'){
                                            this.$ypMsg.notice_info(this,response.message);
                                        }else{
                                            this.$ypMsg.notice_success(this,'创建成功');
                                        }
                                        this.modal_sp = false;
                                        this.search_interface();
                                        this.updateList_sp();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        this.modal_sp = false;
                                    }
                                }
                            )
                        }else{
                            let  param= {
                                spCode : this.form_sp.spCode,
                                spName : this.form_sp.spName,
                                description : this.form_sp.description
                            }
                            //可能需要对密钥处理剔除
                            api.yop_isp_edit(param).then(
                                (response) =>{
                                    //需要填写
                                    if(response.status === 'success'){
                                        if(localStorage.needAudit === 'true'){
                                            this.$ypMsg.notice_info(this,response.message);
                                        }else{
                                            this.$ypMsg.notice_success(this,'修改成功');
                                        }
                                        this.modal_sp = false;
                                        this.search_interface();
                                        this.updateList_sp();
                                    }else{
                                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                        this.modal_sp = false;
                                    }
                                }
                            )
                        }

                        this.modal_sp = false;
                    } else {
                        this.$Message.error('请检查');
                    }
                })

            },
            // 取消批量添加用户
            cancel_create_batch_auth () {
                this.modal_batch_auth =false;
            },
            // 用户名单删除
            handleClose2 (event, name) {
                const index = this.auth_user_list.indexOf(name);
                this.auth_user_list.splice(index, 1);
            },
            // 添加用户按钮数据绑定
            addUser () {
                if(!(!!this.form_sp.inputUserName)){
                    this.$Message.warning({
                        content: '用户名不能为空',
                        duration: 5
                    });
                }else{
                    api.yop_acl_user_check_native_user({name: this.form_sp.inputUserName}).then(
                        (response) => {
                            if (response.data.status === 'success') {
                                if(response.data.data.result){
                                    if(this.auth_user_list.indexOf(this.form_sp.inputUserName) === -1){
                                        this.auth_user_list.push(this.form_sp.inputUserName);
                                    }else{
                                        this.$Message.warning({
                                            content: '该员工用户名已存在列表中，不要重复添加',
                                            duration: 5
                                        });
                                    }
                                }else{
                                    this.$Modal.error({
                                        title: '用户不存在',
                                        content: '该用户不存在系统中'
                                    });
                                }
                            } else {
                                this.$Modal.error({
                                    title: '校验错误',
                                    content: response.data.message
                                });
                            }
                        }
                    );
                }
                // this.auth_user_list.push(this.form_role.roleCode);
            },
            // 初始化页面
            init(){
                if(this.$store.state.create_service_supplier_label){
                    this.currentStatus = true;
                    this.modal_sp = true;
                }
            },
            // 用户状态下拉框加载完成函数
            updateSelect_status (val) {
                this.data_select_sp_status =val;
            },
            // sp下拉框加载完成函数
            updateSelect_sp (val) {
                this.data_select_sp = val;
            },
            // 下拉框加载完处理函数
            select_callBack () {
                this.count_select_related--;
                if (this.count_select_related === 0) {
                    this.search_interface();
                }
            },
            // sp查询函数
            search_interface () {
                this.show_loading_spList = true;
                let params = {
                    spCode: this.data_select_sp,
                    status: this.data_select_sp_status,
                    _pageNo: 1,
                    _pageSize: 10
                };
                util.paramFormat(params);
                api.yop_isp_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            this.pageTotal = response.data.data.result.totalPageNum * 10;
                            this.show_loading_spList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_spList = false;
                        }
                    }
                );
            },
            // 表格数据处理函数
            tableDataFormat (items) {
                this.data_spList = [];
                for (var i in items) {
                    this.data_spList.push({
                        description : items[i].description,
                        spName : util.empty_handler(items[i].spName),
                        spCode : util.empty_handler(items[i].spCode),
                        status : util.empty_handler(items[i].status),
                        createTime : util.empty_handler(items[i].createdDate),
                        lastModifyTime : util.empty_handler(items[i].lastModifiedDate),
                    });
                }
            },


            // sp重置函数
            reset_Interface () {
                this.$refs.select_sp.resetSelected();
                this.$refs.select_status.resetSelected();
                this.data_select_sp = '';
                this.data_select_sp_status = '';
            },
            // 页面刷新
            pageRefresh (val) {
                this.show_loading_spList = true;
                let params = {
                    spCode: this.data_select_sp,
                    status: this.data_select_sp_status,
                    _pageNo: val,
                    _pageSize: 10
                };
                util.paramFormat(params);
                api.yop_isp_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.result.items);
                            this.pageNo = response.data.data.result.pageNo;
                            this.pageTotal = response.data.data.result.totalPageNum * 10;
                            this.show_loading_spList = false;
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_spList = false;
                        }
                    }
                );
            },
            // 编辑按钮
            sp_modify (code) {
                this.formData_reset();
                // 详细信息获取
                this.currentStatus = false;
                this.sp_detail_information_handler(code);
                this.modal_sp = true;
            },
            // 详细信息detail查询
            sp_detail_information_handler (code){
                api.yop_isp_detail({spCode:code}).then(
                    (response)=>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.detail_format(result);
                        }else{
                            this.$ypMsg.notice_error(this,'角色详细信息获取失败,请刷新重试',response.data.message,response.data.solution);
                            this.modal_sp= false;
                        }
                    }
                )
            },
            // 详细信息处理函数
            detail_format (result) {
                this.form_sp.spCode = util.empty_handler(result.spCode);
                this.form_sp.spName = util.empty_handler(result.spName);
                this.form_sp.description = result.description;
            },
            // 删除按钮
            sp_delete (code) {
                this.$Modal.confirm({
                    title: '删除服务提供方',
                    content: "<p style='color:red'>请运营与服务提供方确定，已创建的：API分组、API接口、服务分组、在线文档，已通知商户停用并下线或者禁用。确认后，再删除服务提供方！</p>" +
                    "<p>确定删除该服务提供方？</p>",
                    'ok-text':'确认',
                    onOk: () =>{
                        // 删除操作
                        api.yop_isp_delete({spCode: code}).then(
                            (response) => {
                                if (response.status === 'success') {
                                    this.$ypMsg.notice_success(this,'删除成功');
                                    this.search_interface();
                                    this.$refs.select_sp.updateList();
                                } else {
                                    this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                                }
                            }
                        );
                    }
                });
            },
            // 新增服务提供方
            sp_add () {
                this.formData_reset();
                this.currentStatus = true;
                this.modal_sp = true;
            },
            // 取消新建按钮
            cancel_create_sp () {
                this.modal_sp = false;
                this.formData_reset();
            },
            // 列表数据重置
            formData_reset () {
                this.$refs.position.resetSelected();
                this.form_sp.spCode = '';
                this.form_sp.spName = '';
                this.form_sp.description = '';
                this.form_sp.inputUserName = '';
                this.data_select_post_model = '';
                this.data_userNameList_modal = [];
                this.auth_user_list = [];
                this.formRole_reset();
            },
            // sp列表更新
            updateList_sp () {
                this.$refs.select_sp.updateList();
            },
            // 表单状态还原
            formRole_reset(){
                this.$refs.form_sp.resetFields();
            },
            // 职位数据更新
            updateSelect_post_model (val) {
                this.data_select_post_model = val;
                if(!!val){
                    api.yop_acl_user_by_position({position:val}).then(
                        (response) => {
                            if (response.data.status === 'success') {
                                let result= response.data.data.result;
                                this.data_userNameList_modal = [];
                                if(!!result){
                                    result.forEach(
                                        item =>{
                                            this.data_userNameList_modal.push({
                                                value : item,
                                                code: item
                                            })
                                        }
                                    )
                                }

                            } else {
                                this.$Modal.error({
                                    title: '错误',
                                    content: response.data.message
                                });
                            }
                        }
                    );
                }

            },
            // 返回相应编码的名字
            data_name_handler (code, name) {
                if (code) {
                    let group = this.$store.state.select[name].data;
                    for (var i in group) {
                        if (group[i].value === code) {
                            return group[i].name;
                        }
                    }
                } else {
                    return '';
                }
            },

        },
        mounted () {
            this.init();
        },
        beforeRouteLeave(to, from, next) {
            this.$store.state.create_service_supplier_label = false;
            this.$destroy();
            next();
        }


    };
</script>

<style scoped>

</style>
