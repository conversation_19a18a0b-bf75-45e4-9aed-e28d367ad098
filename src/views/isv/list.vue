<style lang="less">
  @import "../../styles/common";
  @import "../API_Management_Open/api_Mangement_Open";

  .demo-badge-alone {
    background: #5cb85c !important;
  }

  .round {
    width: 16px;
    height: 16px;
    display: inline-block;
    font-size: 20px;
    line-height: 16px;
    text-align: center;
    color: #f00;
    text-decoration: none;
  }

  .ivu-table-cell {
    padding-left: 10px;
    padding-right: 10px;
  }
</style>
<template>
  <div style="background: #eee;padding:8px">
    <Card :bordered="false">
      <Row type="flex" align="middle">
        <Col span="20">
        <Col span="7">
        <Col span="7" class="margin-top-10">
        <span>商户编码:</span>
        </Col>
        <Col span="17">
        <Input id="input_cert_1" class="margin-top-5" v-model="data_search_commercialCode" placeholder="商户编码"
          @on-enter="search"></Input>
        </Col>
        </Col>

        <Col offset="1" span="7">
        <Col span="7" class="margin-top-10">
        <span>商户名称:</span>
        </Col>
        <Col span="17">
        <Input id="input_cert_2" class="margin-top-5" v-model="data_search_name" placeholder="商户名称"
          @on-enter="search"></Input>
        </Col>
        </Col>

        <Col offset="1" span="8">
        <Col span="7" class="margin-top-10">
        <span>商户提供方:</span>
        </Col>
        <Col span="17">
          <common-select id="input_cert_3" ref="select_providerCode" @on-update="updateSelect_providerCode" type="normal" keyWord="result"
          holder="请选择（默认全部）" code="code" title="name" group="provider_code" @on-loaded="select_callBack"
          :default="this.data_select_providerCode" :uri="this.$store.state.select.provider_code.uri"></common-select>
        </Col>
        </Col>

        <Col class="margin-top-5" span="7">
        <Col span="7" class="margin-top-10">
        <span>商户类型:</span>
        </Col>
        <Col span="17">
        <common-select ref="select_type" @on-update="updateSelect_Type" type="normal" keyWord="result"
          holder="请选择（默认全部）" code="code" title="name" group="provider_type" @on-loaded="select_callBack"
          :default="this.data_select_type" :uri="this.$store.state.select.provider_type.uri"></common-select>
        </Col>
        </Col>

        <Col class="margin-top-5" offset="1" span="7">
        <Col span="7" class="margin-top-10">
        <span>状态:</span>
        </Col>
        <Col span="17">
        <common-select ref="select_certStatus" @on-update="updateSelect_certStatus" type="normal" keyWord="result"
          holder="请选择（默认全部）" code="code" title="name" group="provider_status" @on-loaded="select_callBack"
          :default="this.data_select_certStatus" :uri="this.$store.state.select.provider_status.uri"></common-select>
        </Col>
        </Col>

        <!-- <Col class="margin-top-5" offset="1" span="8">
        <Col span="7" class="margin-top-10">
        <span>创建时间:</span>
        </Col>
        <date-picker ref="datepicker" @on-start-change="update_start_date" @on-end-change="update_end_date"
          :default_start="this.dateStart" :default_end="this.dateEnd"></date-picker>
        </Col> -->
        </Col>

        <Col span="4">
        <Col class="margin-top-10" span="11" style="text-align:center">
        <Button id="btn_cert_1" type="primary"  @click="search">查询</Button>
        </Col>
        <Col class="margin-top-10" span="11" style="text-align:center">
        <Button id="btn_cert_2" type="ghost" @click="reset_search">重置</Button>
        </Col>
        </Col>
      </Row>
      <Row class="margin-top-20">
        <Col span="24">
        <Button id="btn_cert_3" class="margin-right-10" v-url="{url:'/rest/isv/create'}" type="primary"
          @click="cert_add">新增商户</Button>
        </Col>
      </Row>
      <Row class="margin-top-10">
        <Col span="24">
        <Table id="table_1" border :columns="columns_certList" :data="data_certList"></Table>
        <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
          <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo"
            show-elevator @on-change="page_refresh"></Page>
        </Tooltip>
        </Col>
        <loading :show="show_loading_certList"></loading>
      </Row>
      <modal_edit_merchant ref="modal_edit_merchant" @search_cert_list="search"></modal_edit_merchant> 
      <modal_confirm ref="modal_confirm" :title="modal_confirm_title" :warning_red="modal_confirm_warning_red"
        :warning_black="modal_confirm_warning_black" :reason_fail="modal_confirm_reason_fail" :desc="modal_confirm_desc"
        :reason_length="modal_confirm_reason_length" :reason_show="modal_confirm_reason_show"
        @confirm_handler="confirm_handler"></modal_confirm>
      <modal_opt_record ref="modal_opt_record"></modal_opt_record>
      <!-- <modal_public_key ref="modal_public_key"></!--> 
      <modal_copy ref="modal_copy" :title="copyTitle"></modal_copy>
    </Card>
  </div>
</template>

<script>
  import loading from "../my-components/loading/loading";
  import commonSelect from "../common-components/select-components/selectCommon";
  import util from "../../libs/util";
  import api from "../../api/api";
  import datePicker from "../common-components/date-components/date-picker";
  import modal_edit_merchant from "./edit_merchant";
  import modal_confirm from "../common-components/modal-components/modal_confirm";
  import modal_opt_record from "./modal_change_record";
//   import modal_public_key from "./modals/modal_yeepay_public_key";
  import modal_copy from "../common-components/modal-components/modal_copy";

  import toolTip_common from "../my-components/toolTip/toolTip_common";
  export default {
    name: "cert-list",
    components: {
      loading,
      commonSelect,
      datePicker,
      modal_edit_merchant,
      modal_confirm,
      modal_opt_record,
    //   modal_public_key,
      modal_copy,
      toolTip_common
    },
    data() {
      return {
        /**
         * 密钥列表部分变量
         */
        // 密钥列表loading
        show_loading_certList: false,
        // 搜索框-商户名称据绑定
        data_search_name: "",
        // 搜索框-商户提供方绑定
        data_select_providerCode: "",
        // 搜索框-商户编码数据绑定
        data_search_commercialCode: "",
        // 下拉框-商户类型数据绑定
        data_select_type: "",
        // 下拉框-密钥状态数据绑定
        data_select_certStatus: "",
        // 搜索时间框-创建时间开始时间数据绑定
        dateStart: "",
        // 搜索时间框-创建时间结束时间数据绑定
        dateEnd: "",
        // 下拉框加载统计
        count_select_related: 2,
        // 密钥列表表头配置
        columns_certList: [
          {
            title: "商户编码",
            render: (h, params) => {
              return h("div", [
                h("p", params.row.customerNo),
                h("p", "(" + params.row.name + ")")
              ]);
            },
            align: "center"
          },
        //   {
        //     title: "商户提供方",
        //     width: 125,
        //     align: "center",
        //     render: (h, params) => {
        //       return h("div", [
        //         h("p", params.row.alias),
        //         h(
        //           "a",
        //           {
        //             directives: [
        //               {
        //                 name: "show",
        //                 value: params.row.appType !== "PLATFORM_TEST" && params.row.usage !== "API_INVOKE_CIPHER" && util.format_check_common(params.row.type, /^(AES)/)
        //               }
        //             ],
        //             on: {
        //               click: () => {
        //                 this.cert_copy(params.row.id, params.row.type);
        //               }
        //             }
        //           },
        //           "查看密钥"
        //         )
        //       ]);
        //     }
        //   },
          {
            title: "商户提供方",
            key: "providerCode",
            align: "center",
          },
          {
            title: "商户类型",
            key: "type",
            align: "center",
            // render: (h, params) => {
            //   return h(
            //     "div",
            //     this.data_name_handler(params.row.type, "cert_type_common")
            //   );
            // }
          },
        
          {
            title: "商户状态",
            key: "status",
            width: 100,
            align: "center",
            render: (h, params) => {
              var color =  "red",
              status =  ""
              if (params.row.status === "VERIFIED") {
                color = "green";
                status = '已认证';
              } 
               if (params.row.status === "FROZEN") {
                color = "red";
                status = '冻结';
              } 
                if (params.row.status === "UNVERIFIED"){
                color = "grey";
                status = '未认证';
              }
              return h("div", [
                h(
                  "Tag",
                  {
                    style: {
                      align: "center",
                      width: "60px"
                    },
                    props: {
                      color: color
                    }
                  },
                  status
                )
              ]);
            }
          },
          {
            renderHeader: (h, params) => {
              return h("div", [h("p", "创建时间"), h("p", "最后修改时间")]);
            },
            width: 150,
            align: "center",
            render: (h, params) => {
              return h("div", [
                h("p", params.row.createdDate),
                h("p", params.row.lastModifiedDate)
              ]);
            }
          },
          {
            title: "操作",
            align: "center",
            key: "operations",
            // fixed: "right",
            render: (h, params) => {
              let option = "冻结";
              let optionAction = false;
              let url = "";
              if (params.row.usage === "API_INVOKE_CIPHER") {
                return this.specific_options_return(h, params);
              }


              if (params.row.status === "UNVERIFIED") {
                option = "无";
                optionAction = "UNVERIFIED";
                url = "";
              } else if (params.row.status === "VERIFIED") {
                option = "冻结";
                optionAction = "VERIFIED";
                url = "/rest/isv/frozen";
              }else if (params.row.status === "FROZEN") {
                option = "解冻";
                optionAction = "FROZEN";
                url = "/rest/isv/unfrozen";
              }
              return h("div", [
                  h(
                  "Button",
                  {
                    props: {
                      type: "text",
                      size: "small"
                    },
                    domProps:{
                      id: "btn_cert_edit_1"
                    },
                    directives: [
                      {
                        name: "url",
                        value: { url: "/rest/isv/update" }
                      },
                      {
                        name: "show",
                        value:
                          params.row.status !== "UNVERIFIED"
                      }
                    ],
                    on: {
                      click: () => {
                        this.edit_merchant(params.row);
                      }
                    }
                  },
                  "编辑"
                ),
                h(
                  "Button",
                  {
                    props: {
                      type: "text",
                      size: "small"
                    },
                    domProps:{
                      id: "btn_cert_edit_2"
                    },
                    directives: [
                      {
                        name: "url",
                        value: { url: "/rest/isv/oper/list" }
                      },
                    ],
                    on: {
                      click: () => {
                        this.jump_to_oper(params.row);
                      }
                    }
                  },
                  "操作员管理"
                ),
                h(
                  "Button",
                  {
                    props: {
                      type: "text",
                      size: "small"
                    },
                    domProps:{
                      id: "btn_cert_edit_3"
                    },
                    directives: [
                      {
                        name: "url",
                        value: { url: url }
                      },
                      {
                        name: "show",
                        value:
                          params.row.status !== "UNVERIFIED"
                      }
                    ],
                    on: {
                      click: () => {
                        this.cert_option(params.row.customerNo, optionAction);
                      }
                    }
                  },
                  option
                ),
                h(
                  "Button",
                  {
                    props: {
                      type: "text",
                      size: "small"
                    },
                    domProps:{
                      id: "btn_cert_edit_4"
                    },
                    directives: [
                      {
                        name: "url",
                        value: { url: "/rest/isv/change" }
                      }
                    ],
                    on: {
                      click: () => {
                        this.cert_record(params.row);
                      }
                    }
                  },
                  "变更记录"
                )
              ]);
            }
          }
        ],
        // 密钥列表数据
        data_certList: [],
        // 总数
        pageTotal: 10,
        // 当前页码
        pageNo: 1,
        /**
         * 确认弹窗部分
         */
        // 确认弹窗标题
        modal_confirm_title: "",
        // 确认弹窗红色提示部分
        modal_confirm_warning_red: "",
        // 确认弹窗黑色提示部分
        modal_confirm_warning_black: "",
        // 确认弹窗必填原因规范提示
        modal_confirm_reason_fail: "",
        // 确认弹窗原因描述
        modal_confirm_desc: "",
        // 确认弹窗原因长度限制
        modal_confirm_reason_length: 100,
        // 确认弹窗原因是否展示
        modal_confirm_reason_show: true,

        /**
         * 复制弹窗部分
         */
        // 弹窗标题
        copyTitle: "",
        // 第一列标题
        copyCol1: "",
        // 第二列标题
        copyCol2: ""
      };
    },
    methods: {
      // 页面初始化
      init() {
        this.init_search();
      },
      // 页面初始化查询赋值操作
      init_search() {
        this.search();
      },
      // 数据检索
      search() {
        let params = this.search_pre_option();
        this.list_handler(params);
      },
      // 检索条件初始化
      reset_search() {
        this.$refs.select_type.resetSelected();
        this.$refs.select_certStatus.resetSelected();
        this.$refs.select_providerCode.resetSelected();
        // this.dateStart = "";
        // this.dateEnd = "";
        this.data_search_commercialCode = "";
        this.data_search_name = "";
        this.data_select_providerCode = "";
        this.data_select_type = "";
        this.data_select_certStatus = "";
      },
      
      // 请求参数生成前序操作
      search_pre_option() {
        this.show_loading_certList = true;
        let params = {
          customerNo: this.data_search_commercialCode.trim(),
          name: this.data_search_name.trim(),
          providerCode: this.data_select_providerCode,
          type: this.data_select_type,
          status: this.data_select_certStatus,
          // createdStartDate: util.dateFormat_component(this.dateStart),
          // createdEndDate: util.dateFormat_component_end(this.dateEnd),
          _pageNo: 1
        };
        util.paramFormat(params);
        return params;
      },
      // 列表刷新
      page_refresh(val) {
        let params = this.search_pre_option();
        if (val) {
          params._pageNo = val;
        }
        this.list_handler(params);
      },
      // 列表请求执行函数
      list_handler(params) {
        api.yop_isv_list(params).then(response => {
          if (response.data.status === "success") {
            this.tableDataFormat(response.data.data.page.items);
            this.pageNo = response.data.data.page.pageNo;
            // this.pageTotal = response.data.data.page.totalPageNum * 10;
              if(response.data.data.page.items){
                if(response.data.data.page.items.length < 10){
                    this.pageTotal=response.data.data.page.items.length;
                }else{
                    this.pageTotal=NaN;
                }
              }else{
                  this.pageTotal=NaN;
              }
          } else {
            this.$ypMsg.notice_error(
              this,
              "错误",
              response.data.message,
              response.data.solution
            );
          }


          
          this.show_loading_certList = false;
        });
      },
      // 表格数据处理函数
      tableDataFormat(items) {
        this.data_certList = [];
        var type = "" ,
            status = "",
            provideCode = ""
        for (var i in items) {
            if(items[i].type == "AGENT")
                type = "系统商/代理商"
            if(items[i].type == "SELF")
                type = "直销商"
            if(items[i].type == "TEST")
                type = "测试商户"
            
            // if(items[i].status == "NORMAL")
            //     status = "正常"
            // if(items[i].status == "UNVERIFIED")
            //     status = "未认证"
            // if(items[i].status == "VERIFIED")
            //     status = "活动中"
            // if(items[i].status == "FROZEN")
            //     status = "冻结"
                
                if(items[i].provideCode == "YEEPAY")
                provideCode = "易宝"
            if(items[i].provideCode == "AIR")
                provideCode = "航旅"
            if(items[i].provideCode == "PAYPLUS")
                provideCode = "钱麦"
            if(items[i].provideCode == "YOP")
                provideCode = "开放平台"
                if(items[i].provideCode == "KJ")
                provideCode = "跨境"
              if(items[i].provideCode == "SUNRISE")
                provideCode = "日晟外综服"
          this.data_certList.push({
            customerNo: util.empty_handler(items[i].customerNo),
            name: util.empty_handler(items[i].name),
            providerCode: provideCode,
            type: type,
            status: util.empty_handler(items[i].status),
            createdDate: util.empty_handler(items[i].createdDate),
            lastModifiedDate: util.empty_handler(items[i].lastModifiedDate)
          });
        }
      },
      // 下拉框加载完处理函数
      select_callBack() {
        this.count_select_related--;
        if (this.count_select_related === 0) {
          this.search();
        }
      },
      // 商户提供方更新
      updateSelect_providerCode(val) {
        this.data_select_providerCode = val;
      },
      // 商户类型数据更新
      updateSelect_Type(val) {
        this.data_select_type = val;
      },
      // 状态数据更新
      updateSelect_certStatus(val) {
        this.data_select_certStatus = val;
      },
      // 新增密钥
      cert_add() {
        this.$router.push({
          name: 'isv/add_merchant',
        })
      },
      // 密钥启用、禁用
      cert_option(customerNo, optionAction) {
        console.log(customerNo,"customerNo")
        let dataTemp = {};
        // if (optionAction == "UNVERIFIED") {
        //   dataTemp = {
        //     title: "未认证",
        //     warning_red: "启用密钥后，商户可以使用该密钥调用接口!",
        //     warning_black: "确定启用该密钥？",
        //     reason_fail: "启用原因必填！",
        //     desc: "最长可输入100字符",
        //     length: 100,
        //     show: true
        //   };
        // } 
        if (optionAction == "FROZEN"){
          dataTemp = {
            title: "解冻",
            warning_red: "",
            warning_black: "确定要解冻该商户？",
            reason_fail: "操作原因必填！",
            desc: "最长可输入100字符",
            length: 100,
            show: true
          };
        }
        if (optionAction == "VERIFIED"){
          dataTemp = {
            title: "冻结",
            warning_red: "冻结商户后，商户的所有操作员不能登录开放平台管理中心。",
            warning_black: "确定冻结该商户吗？",
            reason_fail: "操作原因必填！",
            desc: "最长可输入100字符",
            length: 100,
            show: true
          };
        }
        this.modal_confirm_info_init(dataTemp);
        this.$refs.modal_confirm.set_current_id(customerNo);
        this.$refs.modal_confirm.preview_show();
      },
      // 返回相应编码的名字
      data_name_handler(code, name) {
        if (code) {
          if (
            this.$store.state.select[name].data &&
            this.$store.state.select[name].data.length > 0
          ) {
            let group = this.$store.state.select[name].data;
            for (var i in group) {
              if (group[i].value === code) {
                return group[i].name;
              }
            }
          } else {
            return code;
          }
        } else {
          return "";
        }
      },
      // 开始日期更新
      update_start_date(val) {
        this.dateStart = val;
      },
      // 结束日期更新
      update_end_date(val) {
        this.dateEnd = val;
      },
      // 变更记录
      cert_record(row) {
        var id = row.appId
        var customerNo = row.customerNo
        this.$refs.modal_opt_record.reset_search();
        if(customerNo){
          this.$refs.modal_opt_record.set_appId(customerNo);
        }
        this.$refs.modal_opt_record.modal_preview();
        this.$refs.modal_opt_record.search();
      },

      // 确认弹窗初始化函数
      modal_confirm_info_init(data) {
        this.modal_confirm_title = data.title;
        this.modal_confirm_warning_red = data.warning_red;
        this.modal_confirm_warning_black = data.warning_black;
        this.modal_confirm_reason_fail = data.reason_fail;
        this.modal_confirm_desc = data.desc;
        this.modal_confirm_reason_length = data.length;
        this.modal_confirm_reason_show = data.show;
      },
      //  确认弹窗处理函数
      confirm_handler(data) {
        console.log(data);
        let param = {
          customerNo: data.id,
          cause: data.reason
        };
        if (data.title === "冻结") {
          api.yop_isv_frozen(param).then(response => {
            if (response.status === "success") {
              this.$ypMsg.notice_success(this, "冻结成功");
              this.search();
            } else {
              this.$ypMsg.notice_error(
                this,
                "冻结错误",
                response.message,
                response.solution
              );
            }
          });
        } 
         if (data.title === "解冻") {
          api.yop_isv_unfrozen(param).then(response => {
            if (response.status === "success") {
              this.$ypMsg.notice_success(this, "解冻成功");
              this.search();
            } else {
              this.$ypMsg.notice_error(
                this,
                "解冻错误",
                response.message,
                response.solution
              );
            }
          });
        } 
      },
      
      // 特殊状态的操作返回
      specific_options_return(h, params) {
        return h("div", [
        h(
                  "Button",
                  {
                    props: {
                      type: "text",
                      size: "small"
                    },
                    domProps:{
                      id: "btn_cert_edit_5"
                    },
                    on: {
                      click: () => {
                        this.jump_to_oper(params.row);
                      }
                    }
                  },
                  "操作员管理"
                ),
          h(
            "Button",
            {
              props: {
                type: "text",
                size: "small"
              },
              domProps:{
                id: "btn_cert_edit_6"
              },
              directives: [
                {
                  name: "url",
                  value: { url: "/rest/isv/cert/change-record" }
                }
              ],
              on: {
                click: () => {
                  this.cert_record(params.row);
                }
              }
            },
            "变更记录"
          )
        ]);
      },
      edit_merchant(row){
        this.$refs.modal_edit_merchant.set_modal_preview(true,row);
      },
      // 数字签名查看
      jump_to_oper(val) {
        this.$router.push({
          name:"isv/oper/list",
          params: {customerNo: val.customerNo}
        })
      }
     
    },
    mounted() {
      this.init();
    },
    beforeRouteLeave(to, from, next) {
      this.$store.state.current_appId = "";
      this.$destroy();
      next();
    }
  };
</script>

<style scoped>
</style>