<template>
    <Modal id="modal_request_1" v-model="show" width="650" :closable="false">
        <p slot="header">
          重置登录密码
        </p>
        <div style="padding: 0 30px">
          <Alert style="margin:10px 0;" type="info" show-icon>
            点击确定后，将重置此账号的登录密码，并向其绑定邮箱发送一封重置邮件
          </Alert>
          <Form ref="form_cert" :label-width="80" label-position="left">
            <FormItem label="商户编码：" prop="customerNo" style="margin-bottom: 0">
              {{ customerNo }}
            </FormItem>
            <FormItem label="绑定邮箱：" prop="email" style="margin-bottom: 0">
              {{ email }}
            </FormItem>
          </Form>
        </div>
        <div slot="footer">
            <Button id="modal_app_btn_1" type="ghost" @click="show = false">取消</Button>
            <Button id="modal_app_btn_2" type="primary" :loading="loading" @click="confirm">确定</Button>
        </div>
    </Modal>
</template>

<script>
  import api from '../../../../api/api'
  export default {
    name: 'modalResetPassword',
    data() {
      return {
        show: false,
        loading: false,
        customerNo: '',
        email: '',
        id: ''
      }
    },
    methods: {
      showModal(data) {
        this.show = true
        this.loading = false
        this.customerNo = data.customerNo
        this.email = data.email
        this.id = data.id
      },
      confirm() {
        this.loading = true
        api.yop_isv_oper_resetPwd({
          operId: this.id
        }).then(response => {
          if (response.data.status === "success") {
            this.$ypMsg.notice_success(this,'重置成功')
          } else {
            this.$ypMsg.notice_error(
              this,
              "错误",
              response.data.message,
              response.data.solution
            );
          }
        })
        .finally(() => {
          this.loading = false;
          this.show = false
        })
      } 
    },
  };
</script>

<style>
    .ivu-input-group-prepend {
        background: #fff!important;
    }
</style>