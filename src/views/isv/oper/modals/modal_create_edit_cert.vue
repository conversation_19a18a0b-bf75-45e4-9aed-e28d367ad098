<template>
    <Modal id="modal_request_1" v-model="modal_cert" width="650" :closable="false">
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black"  v-show="this.current_status !== 'modify'">新增操作员</span>
            <span style="color:black" v-show="this.current_status == 'modify'">编辑操作员</span>
        </p>
        <div>
            <Form ref="form_cert" :model="form_cert" :rules="rule_cert" :label-width="140">
                <FormItem label="商户编码：" prop="customerNo">
                    <Input v-show="this.current_status !== 'modify'" type="text" size="small" v-model="form_cert.customerNo" style="width:80%"></Input>
                    <p v-show="this.current_status === 'modify'">{{form_cert.customerNo}}</p>
                </FormItem>
                <FormItem label="邮箱：" prop="email">
                    <Input type="text" size="small" v-model="form_cert.email" style="width:80%"></Input>
                </FormItem>
                <FormItem label="手机号码：" prop="mobile">
                    <Input type="text" size="small" v-model="form_cert.mobile" style="width:80%">
                        <span slot="prepend"  style="background:#fff"> + 86 </span>
                    </Input>
                                
                </FormItem>
                <!-- <p v-show="form_cert.usage === 'BOTH'" class="yop-explain-140">支持输入大小写字母、数字、中文、特殊字符，最长可输入60个字符</p> -->
            </Form>
            <Alert style="margin:10px 40px;" type="warning" show-icon>
                温馨提示：
                <template slot="desc">
                    <p style="color:red!important;">运营后台新增操作员，邮箱、手机号。保存成功后，系统自动发送邮箱认证链接至商户邮箱中。<br />请提前告知商户在24小时内点击邮箱验证链接完成认证，未认证是不能登录易宝开放平台。
                    </p>
                </template>
            </Alert>
        </div>
        <div slot="footer">
            <Button id="modal_app_btn_2" type="primary" @click="ok_create_cert('form_cert')">确定</Button>
            <Button id="modal_app_btn_1" type="ghost" @click="cancel_create_cert">取消</Button>
        </div>
        <loading :show="show_loading_createCert"></loading>
    </Modal>
</template>

<script>
    import loading from '../../../my-components/loading/loading'
    import commonSelect from '../../../common-components/select-components/selectCommon_params_single'
    import util from '../../../../libs/util'
    import api from '../../../../api/api'
    export default {
        name: 'modal_create_edit_app',
        components: {
            commonSelect,
            loading
        },
        data() {
            // app名称验证
            const validate_input = (rule, value, callback) => {
                // 模拟异步验证效果
                setTimeout(() => {
                    if (util.getLength(value.trim()) > 60) {
                        callback(new Error('长度不能大于60'));
                    } else {
                        api.yop_isv_exists({
                            customerNo: value.trim()
                        }).then(
                            (response) => {
                                if (response.data.status === 'success') {
                                    let result = response.data.data.result;
                                    if (result) {
                                        callback();
                                    } else {
                                        callback(new Error('该商户编码不存在'));
                                    }
                                } else {
                                    callback(new Error('商户编码验重失败'));
                                }
                            }
                        );
                    }
                }, 300);
            };
            // app名称验证
            const validate_cert_name = (rule, value, callback) => {
                if (util.getLength(value) > 60) {
                    callback(new Error("长度不能大于60"));
                } else {
                    callback();
                }
            };
            return {
                // 窗口显示绑定
                modal_cert: false,
                // 开始时间
                dateStart: '',
                // 结束时间
                dateEnd: '',
                // 表单数据绑定
                form_cert: {
                    customerNo: '',
                    email:"",
                    mobile:"",
                    inputType: 'TEXT',//FILE
                    key: '',
                    type: 'RSA2048',
                    alias: '',
                    certFile: '',
                    usage: 'BOTH',
                    authType: '永久生效'
                },
                // 数据限制
                rule_cert: {
                    customerNo: [
                        { required: true, message: '商户编码不能为空', trigger: 'blur' },
                        { validator: validate_input, trigger: 'blur' }
                    ],
                    email: [
                        { required: true, message: '邮箱不能为空' }
                    ],
                    mobile: [
                        { required: true, message: '手机号码不能为空', trigger: 'blur' },
                        // { validator: validate_cert_name, trigger: 'blur' }
                    ],
                },
                // loading 新建应用
                show_loading_createCert: '',
                // 上传地址
                specificAction: localStorage.remoteIP + '/rest/isv/cert/create',
                // 上传名称绑定
                name_upload: 'certFile',
                // 上传的数据
                upload_data_static: {
                    customerNo: '',
                    inputType: '',
                    usage: 'BOTH',
                    alias: '',
                    type: ''
                },
                format_limit: ['cer'],
                // 上传的header
                header_upload: {
                    Authorization: 'Bearer ' + localStorage.accesstoken
                },
                uploadFile: [], // 需要上传的附件List,
                current_status:"",
                currentId:""
            }
        },
        methods: {
            // 窗口显示设置
            set_modal_preview(val,current_status,row) {
                this.current_status = current_status
                this.currentId =  row.id
                if(current_status === "modify"){
                     api.yop_isv_oper_detail({id:row.id}).then(
                            (response) => {
                                //需要填写
                                console.log("response",response)
                                if (response.data.status === 'success') {
                                    this.form_cert = response.data.data.result
                                    this.$emit("search_cert_list")
                                } else {
                                    this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
                                }
                            }
                        )
                }
                if(current_status === "create"){
                    this.form_cert.customerNo = row
                }
                this.modal_cert = val;
                this.init();
            },
            //设置当前应用标识
            set_current_appId(customerNo) {
                this.form_cert.customerNo = customerNo;
            },
          
            // 新增密钥确定
            ok_create_cert(val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        var param = ""
                        var url = ''
                        if(this.current_status == "modify"){
                            url = "yop_isv_oper_update"
                            param = {
                                id : this.currentId,
                                mobile: this.form_cert.mobile,
                                email: this.form_cert.email
                            }
                        }else{
                            url = "yop_isv_oper_create"
                            param = {
                                customerNo : this.form_cert.customerNo,
                                mobile: this.form_cert.mobile,
                                email: this.form_cert.email
                            }
                        }
                        api[url](param).then(
                            (response) => {
                                //需要填写
                                console.log("response",response)
                                if (response.status === 'success') {
                                    this.$ypMsg.notice_success(this, '成功');
                                    this.modal_cert = false;
                                    this.$emit("search_cert_list")
                                } else {
                                    this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
                                }
                            }
                        )

                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
         
            // 新增应用取消
            cancel_create_cert() {
                this.modal_cert = false;
                this.formData_reset();
            },
            // 列表数据重置
            formData_reset() {
                this.form_cert.customerNo = '';
                this.form_cert.email = '';
                this.form_cert.mobile = '';
                this.$refs.form_cert.resetFields();
            },
            
            // 格式处理
            formatString_handler() {
                if (this.format_limit && this.format_limit.length > 0) {
                    let result = '';
                    for (var i = 0; i < this.format_limit.length; i++) {
                        if (i === this.format_limit.length - 1) {
                            result = result + this.format_limit[i]
                        } else {
                            result = result + this.format_limit[i] + ','
                        }
                    }
                    return result;
                } else {
                    return ''
                }
            },
          
            // 组件初始化
            init() {
                let param = {
                    usage: 'BOTH'
                }
            },
           
        },
    };
</script>

<style>
    .ivu-input-group-prepend {
        background: #fff!important;
    }
</style>