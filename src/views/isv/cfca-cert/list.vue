<style lang="less">
	@import '../../../styles/common';

	.demo-badge-alone {
		background: #5cb85c !important;
	}

	.round {
		width: 16px;
		height: 16px;
		display: inline-block;
		font-size: 20px;
		line-height: 16px;
		text-align: center;
		color: #f00;
		text-decoration: none;
	}

	.ivu-table-cell {
		padding-left: 10px;
		padding-right: 10px;
	}
</style>
<template>
	<div style="background: #eee;padding:8px">
		<Card :bordered="false">
			<Row type="flex" align="middle">
				<Col span="8">
					<Col span="5" class="margin-top-10">
						<span>商户编号:</span>
					</Col>
					<Col span="19">
						<Input id="input_faq_1" class="margin-top-5" v-model="params.customerNo" placeholder="请输入商户编号"></Input>
					</Col>
				</Col>
				<Col offset="1" span="7">
					<Col span="5" class="margin-top-10">
						<span>状态:</span>
					</Col>
					<Col span="19">
						<Select id="select_faq_1" clearable v-model="params.status" class="margin-top-5" style="width:100%">
							<Option :value="item.value" :key="item.value" v-for="item in statusMap">{{ item.label }}</Option>
						</Select>
					</Col>
				</Col>
        <Col offset="1" span="7">
					<Col span="5" class="margin-top-10">
						<span>序列号:</span>
					</Col>
					<Col span="19">
						<Input
							id="input_faq_2"
							class="margin-top-5"
							v-model="params.serialNo"
							placeholder="请输入序列号"
						></Input>
					</Col>
				</Col>
        <Col span="24" style="text-align: right;" class="margin-top-10">
					<Button id="btn_faq_1" type="primary" @click="search" :loading="loading">查询</Button>
					<Button id="btn_faq_2" type="ghost" @click="reset_search">重置</Button>
				</Col>
			</Row>
			<Row class="margin-top-10">
				<Col span="24">
					<Table id="table_1" border :columns="columns" :data="list"></Table>
				</Col>
				<Loading :show="loading"></Loading>
			</Row>
		</Card>
	</div>
</template>

<script>
  import Api from '~/api/api.js'
  import Loading from '../../my-components/loading/loading';
  export default {
    name: 'cfca-list',
    components: {
      Loading
    },
    data () {
      return {
        loading: false,
        statusMap: [
          {
            label: '已吊销',
            value: 'REVOKED'
          },
          {
            label: '已激活',
            value: 'ACTIVATED'
          },
          {
            label: '未激活',
            value: 'UNDOWNLOAD'
          }
        ],
        params: {
          customerNo: '',
          serialNo: '',
          status: '',
        },
        columns: [
          {
            title: '商编',
            key: 'customerNo',
            align: 'center'
          },
          {
            title: '序列号',
            key: 'serialNo',
            align: 'center'
          },
          {
            title: 'DN',
            key: 'dn',
            align: 'center'
          },
          {
            title: '类型',
            key: 'type',
            align: 'center',
            render: (h, params) => { 
              return h('div', [
                h('div', params.row.type),
                h('div', params.row.keyAlg)
              ])
            }
          },
          {
            title: '申请时间',
            key: 'applyDate',
            align: 'center',
            render: (h, params) => { 
              const date = this.formateDate(params.row.applyDate)
              return h('div', date)
            }
          },
          {
            title: '有效期',
            key: 'title',
            align: 'center',
            render: (h, params) => {
              const date1 = this.formateDate(params.row.effectiveDate)
              const date2 = this.formateDate(params.row.expiredDate)
              return h('div', `${date1}至${date2}`)
            }
          },
          {
            title: '状态',
            key: 'status',
            width: 100,
            align: 'center',
            render: (h, params) => {
              const { status } = params.row
              const item = this.statusMap.find(item => item.value === status)
              if (!item) {
                return h('Tag',
                  {
                    style: {
                      align: 'center'
                    },
                    props: {
                      color: 'grey'
                    }
                  }, '--')
              }
              let color = 'green';
              if (status === 'REVOKED') {
                color = 'grey';
              }
              if (status === 'UNDOWNLOAD') {
                color = 'red';
              }
              return h('Tag', {
                style: {
                  align: 'center'
                },
                props: {
                  color: color
                }
              }, item.label)
            }
          },
          {
            title: '是否被使用',
            key: 'used',
            align: 'center',
            render: (h, params) => { 
              return h('Tag',
              {
                style: {
                  align: 'center'
                },
                props: {
                  color: params.row.used ? 'green' : 'red'
                }
              },
              params.row.used ? '是' : '否')
            }
          },
          {
            title: '操作',
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [
                      {
                        name: 'show',
                        value: !params.row.used && params.row.status === 'ACTIVATED'
                      }
                    ],
                    on: {
                      click: () => {
                        this.revoke(params.row);
                      }
                    }
                  },
                  '吊销'
                ),
              ]);
            }
          }
        ],
        list: []
      };
    },
    mounted () {
    },
    methods: {
      formateDate(str) {
        if(!str) return '-'
        const y = str.slice(0,4)
        const m = str.slice(4,6)
        const d = str.slice(6, 8)
        const h = str.slice(8, 10)
        const M = str.slice(10, 12)
        const s = str.slice(12)
        return `${y}-${m}-${d} ${h}:${M}:${s}`
      },
      search () {
        this.getList();
      },
      // 检索条件初始化
      reset_search () {
        this.params = {
          customerNo: '',
          serialNo: '',
          status: '',
        }
      },
      getList() {
        if(!this.params.customerNo && !this.params.serialNo) {
          this.$Message.error('请填写商户编号或序列号');
          return
        }
        this.loading = true
        Api.yop_isv_cfca_cert_list(
          this.params
        )
          .then(res => {
            this.loading = false
            if (res.data.status === 'success') {
              this.list = res.data.data.result
            } else {
              this.$ypMsg.notice_error(this, '错误', res.data.message);
            }
          })
      },
      revoke ({ serialNo}) {
        this.$Modal.confirm({
          title: '确认吊销',
          content: '吊销操作后将无法撤回，是否确认吊销？',
          loading: true,
          onOk: () => {
            Api.yop_isv_cfca_cert_revoke({ serialNo }).then(res => {
              if (res.status === 'success') {
                this.$Message.success('吊销成功');
                this.$Modal.remove();
                this.getList && this.getList();
              } else {
                this.$ypMsg.notice_error(this, '错误', res.message);
              }
            });
          }
        });
      },
    }
  };
</script>

<style scoped>
</style>