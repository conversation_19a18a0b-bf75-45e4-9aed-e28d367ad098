<template>
    <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="75%" >
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">密钥管理变更记录</span>
        </p>
        <Card dis-hover :bordered="false">
            <Row type="flex" >
                <Col span="20">
                <Col span="7" >
                    <Col span="7" class="margin-top-10">
                        <span >应用标识:</span>
                    </Col>
                    <Col span="17">
                        <Input id='modal_record_4' class="margin-top-5" clearable v-model="data_appId" placeholder="请输入应用标识" @on-enter="search"></Input>
                    </Col>
                </Col>
                <Col offset="1" span="7" >
                    <Col span="7" class="margin-top-10">
                        <span >操作时间:</span>
                    </Col >
                        <date-picker ref="datepicker_record" @on-start-change="update_start_date"
                                     @on-end-change="update_end_date"
                                     :default_start="this.dateStart"
                                     :default_end="this.dateEnd"
                        ></date-picker>
                </Col>
                <Col offset="1" span="7" >
                    <Col span="7" class="margin-top-10">
                        <span >操作类型:</span>
                        </Col>
                        <Col span="17" >
                        <common-select ref="select_cert_operateType" @on-update="updateSelect_cert_operateType"
                                       type="normal"
                                       keyWord="result"
                                       holder="请选择（默认全部）"
                                       code="code"
                                       title="name"
                                       group="cert_operate_type"
                                       @on-loaded="select_callBack"
                                       :default="this.data_select_cert_operateType"
                                       :uri="this.$store.state.select.cert_operate_type.uri"></common-select>
                        </Col>
                </Col>
                <Col span="7" >
                    <Col span="7" class="margin-top-10">
                    <span >操作人:</span>
                    </Col>
                    <Col span="17">
                    <Input id='modal_record_1' class="margin-top-5" clearable v-model="data_operator" placeholder="操作人" @on-enter="search"></Input>
                    </Col>
                </Col>
                <Col span="17"></Col>
                </Col>
                
                <Col span="4">
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button id='modal_btn_opt_1' type="primary"   @click="search">查询</Button>
                </Col>
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button id='modal_btn_opt_2' type="ghost" @click="reset_search">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Table id='table_1' border ref="selection" :columns="columns_opt_recordList" :data="data_opt_recordList" ></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="page_refresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_optRecordList"></loading>
            </Row>
        </Card>
        <div slot="footer">
            <Button type="ghost" @click="modal_hide">关闭</Button>
        </div>
    </Modal>
</template>

<script>
    import loading from '../../../my-components/loading/loading'
    import commonSelect from '../../../common-components/select-components/selectCommon'
    import util from '../../../../libs/util'
    import api from '../../../../api/api'
    import datePicker from '../../../common-components/date-components/date-picker'
    export default {
        name: 'modal_change_record',
        components:{
            loading,
            commonSelect,
            datePicker
        },
        data () {
            return {
                // 弹窗显示
                modal_show: false,
                // 搜索时间框-创建时间开始时间数据绑定
                dateStart: '',
                // 搜索时间框-创建时间结束时间数据绑定
                dateEnd: '',
                // 应用标识
                data_appId: '',
                // 操作类型
                data_select_cert_operateType : '',
                // 操作人
                data_operator : '',
                // loading
                show_loading_optRecordList: false,
                // 当前密钥id
                current_certId : '',
                // 总数
                pageTotal : 10,
                // 当前页码
                pageNo : 1,
                // 表样式
                columns_opt_recordList : [
                    {
                        title: '应用标识',
                        key: 'appId',
                        width: 135,
                        align: 'center'
                    },
                    {
                        title: '操作时间',
                        key: 'operateDate',
                        width: 135,
                        align: 'center'
                    },
                    {
                        title: '操作类型',
                        key: 'operateType',
                        width: 100,
                        align: 'center',
                        render: (h,params) => {
                            return h('div',this.data_name_handler(params.row.operateType,'cert_operate_type'))
                        }
                    },
                    {
                        title: '操作人',
                        key: 'operator',
                        width: 110,
                        align: 'center'
                    },
                    {
                        title: '变更原因',
                        key: 'cause',
                        width: 110,
                        align: 'center'
                    },
                    {
                        title: '变更记录详情',
                        key: 'detail',
                        'min-width': 150,
                        type: 'html'
                        // align: 'center'
                    }
                ],
                // 表数据
                data_opt_recordList : [
                ]
            };
        },
        methods: {
            // 开始日期更新
            update_start_date (val) {
                this.dateStart = val;
            },
            // 结束日期更新
            update_end_date (val) {
                this.dateEnd = val;
            },
            // 操作类型数据更新
            updateSelect_cert_operateType (val) {
                this.data_select_cert_operateType = val;
            },
            //
            // 下拉框加载完处理函数
            select_callBack () {

            },
            // 数据检索
            search (){
                let params = this.search_pre_option();
                this.list_handler(params);
            },
            // 检索条件初始化
            reset_search () {
                this.$refs.datepicker_record.reset();
                this.$refs.select_cert_operateType.resetSelected();
                this.dateStart = '';
                this.dateEnd = '';
                this.data_operator = '';
                this.current_certId = '';
                this.data_appId = '';
            },
            // 请求参数生成前序操作
            search_pre_option (){
                this.show_loading_optRecordList = true;
                let params = {
                    appId: this.data_appId,
                    // certId: this.current_certId,
                    operator: this.data_operator.trim(),
                    operatedType: this.data_select_cert_operateType,
                    operatedStartDate: util.dateFormat_component(this.dateStart),
                    operatedEndDate: util.dateFormat_component_end(this.dateEnd),
                    _pageNo: 1
                };
                util.paramFormat(params);
                return params;
            },
            // 列表刷新
            page_refresh (val) {
                let params = this.search_pre_option();
                if (val) {
                    params._pageNo = val;
                }
                this.list_handler(params);
            },
            // 列表请求执行函数
            list_handler (params) {
                api.yop_isv_cert_change_record(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                            if (
                                response.data.data.page.items &&
                                response.data.data.page.items.length > 0
                            ) {
                            if (response.data.data.page.items.length < 10) {
                                this.pageTotal = response.data.data.page.items.length;
                            } else {
                                this.pageTotal = NaN;
                            }
                            } else {
                                this.pageTotal = NaN;
                            }
                        } else {
                            this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                        }
                        this.show_loading_optRecordList = false;
                    }
                );
            },
            // 表格数据处理函数
            tableDataFormat (items) {
                this.data_opt_recordList = [];
                for (var i in items) {
                    this.data_opt_recordList.push({
                        appId : util.empty_handler(items[i].appId),
                        operateDate : util.empty_handler(items[i].operatedDate),
                        operateType : util.empty_handler(items[i].type),
                        operator : util.empty_handler(items[i].operator),
                        cause : util.empty_handler(items[i].cause),
                        detail : util.empty_handler('<p>'+this.detail_handler(items[i].detail)+'</p>'),
                    });
                }
            },
            // 详细信息处理
            detail_handler (val) {
                return val.replace(/\n/g,'<br/>')
            },
            // 设置当前certId
            set_certId (id){
                this.current_certId = id;
            },
            //设置当前appId
            set_appId (appId){
                this.data_appId = appId;
            },
            // 密钥管理变更记录窗口显示
            modal_preview () {
                this.modal_show = true;
            },
            // 密钥管理变更记录窗口隐藏
            modal_hide () {
                this.modal_show =false;
                this.reset_search();
            },
            // 返回相应编码的名字
            data_name_handler (code, name) {
                if (code) {
                    if(this.$store.state.select[name].data && this.$store.state.select[name].data.length > 0){
                        let group = this.$store.state.select[name].data;
                        for (var i in group) {
                            if (group[i].value === code) {
                                return group[i].name;
                            }
                        }
                    }else{
                        return code
                    }
                } else {
                    return '';
                }
            },
        }
    };
</script>

<style scoped>

</style>
