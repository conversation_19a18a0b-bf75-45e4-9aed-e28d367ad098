<template>
  <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="75%">
    <p slot="header" style="color:#2d8cf0;">
      <span style="color:black">易宝公钥</span>
    </p>
    <Card dis-hover :bordered="false">
      <Row class="margin-top-20">
        <Col span="24">
          <Table
            id="table_1"
            border
            ref="selection"
            :columns="columns_publicKeyList"
            :data="data_publicKeyList"
          ></Table>
        </Col>
        <loading :show="show_loading_public_key"></loading>
      </Row>
    </Card>
    <div slot="footer">
      <Button type="primary" @click="modal_hide">关闭</Button>
    </div>
  </Modal>
</template>

<script>
import loading from "../../../my-components/loading/loading";
import util from "../../../../libs/util";
import api from "../../../../api/api";
export default {
  name: "modal_yeepay_public_key",
  components: {
    loading
  },
  data() {
    return {
      // 弹窗显示
      modal_show: false,
      // loading
      show_loading_public_key: false,
      // 表样式
      columns_publicKeyList: [
        {
          title: "公钥类型",
          key: "type",
          width: 130,
          align: "center"
        },
        {
          title: "公钥值",
          key: "value",
          "min-width": 150,
          align: "center"
        },
        {
          title: "操作",
          key: "type",
          width: 130,
          align: "center",
          render: (h, params) => {
            return h("div", [
              h(
                "Button",
                {
                  props: {
                    type: "text",
                    size: "small"
                  },

                  on: {
                    click: () => {
                      this.copy_value(params.row.value);
                    }
                  }
                },
                "复制"
              )
            ]);
          }
        }
      ],
      // 表数据
      data_publicKeyList: []
    };
  },
  methods: {
    // 公钥获取
    get_public_key() {
      api.yop_isv_cert_public_key().then(response => {
        if (response.data.status === "success") {
          this.tableDataFormat(response.data.data.result);
        } else {
          this.$ypMsg.notice_error(
            this,
            "错误",
            response.data.message,
            response.data.solution
          );
        }
      });
    },
    // 数据处理
    tableDataFormat(items) {
      this.data_publicKeyList = [];
      for (var i in items) {
        this.data_publicKeyList.push({
          type: util.empty_handler(items[i].type),
          value: util.empty_handler(items[i].value)
        });
      }
    },
    // 窗口显示
    modal_preview() {
      this.modal_show = true;
    },
    // 窗口隐藏
    modal_hide() {
      this.modal_show = false;
    },
    // 复制公钥值
    copy_value(value) {
      this.$copyText(value);
      this.$ypMsg.notice_success(this, "复制完成");
    }
  }
};
</script>

<style scoped>
</style>
