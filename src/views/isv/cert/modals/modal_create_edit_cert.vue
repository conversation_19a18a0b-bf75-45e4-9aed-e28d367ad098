<template>
    <Modal id="modal_request_1" v-model="modal_cert" width="650" :closable="false">
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">新增密钥</span>
        </p>
        <div>
            <Form ref="form_cert" :model="form_cert" :rules="rule_cert" :label-width="140">
                <FormItem label="应用标识：" prop="appId">
                    <Input type="text" size="small" v-model="form_cert.appId" style="width:80%"></Input>
                </FormItem>
                <p class="yop-explain-140">支持输入大小写字母、数字、中文、特殊字符，最长可输入60个字符</p>
                <FormItem label="密钥用途：" prop="usage" :rules="{required:true,message:'密钥用途不能为空'}">
                    <RadioGroup v-model="form_cert.usage" @on-change="usage_change">
                        <Radio label="BOTH">API调用+SPI通知</Radio>
                        <Radio label="API_INVOKE_CIPHER">加密密钥</Radio>
                    </RadioGroup>
                </FormItem>
                <FormItem label="密钥类型：" prop="type" :rules="{required:true,message:'密钥类型不能为空'}">
                    <common-select ref="select_certType_m" @on-update="updateSelect_certType_m" type="normal"
                        keyWord="result" holder="请选择密钥类型" code="code" title="name" group="cert_type"
                        @on-loaded="select_callBack" :default="this.form_cert.type" size="small"
                        :uri="this.$store.state.select.cert_type.uri" style="width:80%"></common-select>
                </FormItem>
                <FormItem label="密钥输入方式：" prop="inputType" v-show="form_cert.usage === 'BOTH'">
                    <RadioGroup v-model="form_cert.inputType" @on-change="inputType_change">
                        <Radio label="TEXT">密钥文本</Radio>
                        <Radio label="FILE" v-show="/^(RSA)/.test(form_cert.type)">公钥文件</Radio>
                    </RadioGroup>
                </FormItem>
                <FormItem v-show="form_cert.inputType === 'FILE' && form_cert.usage === 'BOTH'">
                    <label slot="label"> <span style="color:red"> * </span>密钥文件：</label>
                    <Upload ref="upload" style="width:80%;" :action="specificAction" type="drag" :name="name_upload"
                        :show-upload-list="true" :data="upload_data_static" :headers="header_upload"
                        :format="format_limit" :on-format-error="handleFormatError"
                        :before-upload="handleBeforeUpload_modal" :on-progress="handleProgress"
                        :on-success="handleSuccess" :on-error="handleError">
                        <div style="padding: 20px 0">
                            <Icon type="ios-cloud-upload" size="20" style="color: #3399ff"></Icon>
                            <p>点击这里或拖拽上传证书文件</p>
                        </div>
                    </Upload>
                </FormItem>
                <FormItem label="密钥：" v-if="form_cert.inputType === 'TEXT' && form_cert.type === 'RSA2048'" prop="key">
                    <Input type="textarea" size="small" v-model="form_cert.key" style="width:80%"></Input>
                </FormItem>
                <FormItem label="是否通知商户：" prop="needNotify">
                    <RadioGroup v-model="form_cert.needNotify">
                        <Radio :label="false">不发生邮件通知</Radio>
                        <Radio :label="true">发送邮件通知</Radio>
                    </RadioGroup>
                </FormItem>
                <FormItem v-show="form_cert.usage === 'BOTH'" label="密钥名称：" prop="alias">
                    <Input type="text" size="small" v-model="form_cert.alias" placeholder="系统默认生成：密钥类型-序号"
                        style="width:80%"></Input>
                </FormItem>
                <p v-show="form_cert.usage === 'BOTH'" class="yop-explain-140">支持输入大小写字母、数字、中文、特殊字符，最长可输入60个字符</p>
            </Form>
            <Alert style="margin:10px 40px;" type="warning" show-icon>
                温馨提示：
                <template slot="desc">
                    <p style="color:red!important;">生成新密钥后，新密钥立即生效，同时原密钥将立即失效。<br />请提前告知商户在切换密钥过程中，涉及到的相关业务需要进行容错处理。
                    </p>
                </template>
            </Alert>
        </div>
        <div slot="footer">
            <Button id="modal_app_btn_2" type="primary" @click="ok_create_cert('form_cert')">确定</Button>
            <Button id="modal_app_btn_1" type="ghost" @click="cancel_create_cert">取消</Button>
        </div>
        <loading :show="show_loading_createCert"></loading>
    </Modal>
</template>

<script>
    import loading from '../../../my-components/loading/loading'
    import commonSelect from '../../../common-components/select-components/selectCommon_params_single'
    import util from '../../../../libs/util'
    import api from '../../../../api/api'
    export default {
        name: 'modal_create_edit_app',
        components: {
            commonSelect,
            loading
        },
        data() {
            // app名称验证
            const validate_input = (rule, value, callback) => {
                // 模拟异步验证效果
                setTimeout(() => {
                    if (util.getLength(value.trim()) > 60) {
                        callback(new Error('长度不能大于60'));
                    } else {
                        api.yop_app_exists({
                            appId: value.trim()
                        }).then(
                            (response) => {
                                if (response.data.status === 'success') {
                                    let result = response.data.data.result;
                                    if (result) {
                                        callback();
                                    } else {
                                        callback(new Error('该应用标识不存在'));
                                    }
                                } else {
                                    callback(new Error('应用标识验重失败'));
                                }
                            }
                        );
                    }
                }, 300);
            };
            // app名称验证
            const validate_cert_name = (rule, value, callback) => {
                if (util.getLength(value) > 60) {
                    callback(new Error("长度不能大于60"));
                } else {
                    callback();
                }
            };
            return {
                // 窗口显示绑定
                modal_cert: false,
                // 开始时间
                dateStart: '',
                // 结束时间
                dateEnd: '',
                // 表单数据绑定
                form_cert: {
                    appId: '',
                    inputType: 'TEXT',//FILE
                    needNotify: false,//FILE
                    key: '',
                    type: 'RSA2048',
                    alias: '',
                    certFile: '',
                    usage: 'BOTH',
                    authType: '永久生效'
                },
                // 数据限制
                rule_cert: {
                    appId: [
                        { required: true, message: '应用标识不能为空', trigger: 'blur' },
                        { validator: validate_input, trigger: 'blur' }
                    ],
                    inputType: [
                        { required: true, message: '密钥输入方式不能为空' }
                    ],
                    needNotify: [
                        { required: true, message: '是否通知商户不能为空' }
                    ],
                    alias: [
                        { validator: validate_cert_name, trigger: 'blur' }
                    ],
                    key: [
                        { required: true, message: '密钥内容不能为空', trigger: 'blur' },
                    ]
                },
                // loading 新建应用
                show_loading_createCert: '',
                // 上传地址
                specificAction: localStorage.remoteIP + '/rest/isv/cert/create',
                // 上传名称绑定
                name_upload: 'certFile',
                // 上传的数据
                upload_data_static: {
                    appId: '',
                    inputType: '',
                    usage: 'BOTH',
                    needNotify: false,
                    alias: '',
                    type: ''
                },
                format_limit: ['cer','p7b'],
                // 上传的header
                header_upload: {
                    Authorization: 'Bearer ' + localStorage.accesstoken
                },
                uploadFile: [], // 需要上传的附件List,
            }
        },
        methods: {
            // 窗口显示设置
            set_modal_preview(val) {
                this.modal_cert = val;
                this.init();
            },
            //设置当前应用标识
            set_current_appId(id) {
                this.form_cert.appId = id;
            },
            // 下拉框加载完处理函数
            select_callBack() {
                this.$refs.select_certType_m.resetSelected();
                console.log(this.form_cert.type)
                if(this.form_cert.type == "RSA2048"){
                    this.form_cert.type = this.form_cert.type
                }
            },
            // 密钥类型数据更新
            updateSelect_certType_m(val) {
                this.form_cert.type = val;
                if (util.format_check_common(val, /^(RSA)/)) {
                    this.form_cert.inputType = "TEXT";
                    this.$refs.upload.clearFiles();
                }
            },
            // 新增密钥确定
            ok_create_cert(val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        let param = {
                            appId : this.form_cert.appId,
                            type: this.form_cert.type,
                            usage: this.form_cert.usage
                        }
                        api.yop_isv_cert_exists(param).then(
                            (response) => {
                                //需要填写
                                if (response.data.status === 'success') {
                                    if (response.data.data.result) {
                                        this.$Modal.confirm({
                                            title: '提示',
                                            content: "<p style='color:red'>配置新密钥后，原密钥立即失效，新密钥立即生效！</p><br/>确定要替换原密钥？ ",
                                            'ok-text': '确定',
                                            onOk: () => {
                                                this.create_cert_handler();
                                            },
                                        });
                                    } else {
                                       this.create_cert_handler();
                                    }
                                } else {
                                    this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
                                }
                            }
                        )

                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 新增密钥执行函数
            create_cert_handler() {
                if (this.form_cert.inputType === 'FILE') {
                    if (this.uploadFile.length === 0) {
                        this.$Message.error('未选择上传密钥文件')
                        return false
                    } else {
                        this.show_loading_createCert = true;
                        this.upload_data_static.appId = this.form_cert.appId;
                        this.upload_data_static.inputType = this.form_cert.inputType;
                        this.upload_data_static.usage = this.form_cert.usage;
                        this.upload_data_static.alias = this.form_cert.alias;
                        this.upload_data_static.needNotify = this.form_cert.needNotify;
                        this.upload_data_static.type = this.form_cert.type;
                    }
                    if (this.uploadFile.length > 0) {
                        for (let i = 0; i < this.uploadFile.length; i++) {
                            let item = this.uploadFile[i]
                            this.$refs.upload.post(item);
                        }
                    }
                    this.uploadFile = [];
                    this.loading_upload_content = '密钥文件上传中..'
                    this.show_loading_cert = true;
                } else {
                    this.show_loading_createCert = true;
                    let param = {
                        appId: this.form_cert.appId,
                        inputType: this.form_cert.inputType,//FILE
                        key: encodeURIComponent(this.form_cert.key.trim()),
                        type: this.form_cert.type,
                        usage: this.form_cert.usage,
                        alias: this.form_cert.alias,
                        needNotify: this.form_cert.needNotify,
                    }
                    if(this.form_cert.usage === 'API_INVOKE_CIPHER'){
                        delete param['inputType'];
                        delete param['key'];
                        delete param['alias'];
                    }
                    if(this.form_cert.type !== 'RSA2048'){
                        if(param['key'] || param['key'] === ''){
                            delete param['key'];
                        }
                    }
                    api.yop_isv_cert_create(param).then(
                        response => {
                            if (response.status === 'success') {
                                this.$ypMsg.notice_success(this, '密钥新建成功');
                                this.modal_cert = false;
                                // 执行刷新列表
                                this.$emit('search_cert_list')
                            } else {
                                this.$ypMsg.notice_error(this, '密钥新建错误', response.message, response.solution);
                            }
                            this.show_loading_createCert = false;
                        }
                    )
                }
            },
            // 新增应用取消
            cancel_create_cert() {
                this.modal_cert = false;
                this.formData_reset();
            },
            // 列表数据重置
            formData_reset() {
                this.$refs.upload.clearFiles();
                this.$refs.select_certType_m.resetSelected();
                this.form_cert.appId = '';
                this.form_cert.inputType = 'TEXT';
                this.form_cert.needNotify = false;
                this.form_cert.key = '';
                this.form_cert.type = 'RSA2048';
                this.form_cert.usage = 'BOTH'
                this.form_cert.alias = '';
                this.form_cert.authType = '永久生效';
                this.dateStart = '';
                this.dateEnd = '';
                this.$refs.form_cert.resetFields();
            },
            // 开始日期更新
            update_start_date(val) {
                this.dateStart = val;
            },
            // 结束日期更新
            update_end_date(val) {
                this.dateEnd = val;
            },
            /**
             * 密钥文件上传
             */
            handleFormatError(file) {
                this.$ypMsg.notice_warning(this, '密钥文件 ' + file.name + ' 格式不正确,已自动清空', '附件格式不正确');
            },
            handleProgress(event, file) {

            },
            handleSuccess(event, file) {
                let status = event.status
                if (status === 'success') {
                    this.$ypMsg.notice_success(this, '密钥文件' + file.name + '<br/>' + '上传成功', '密钥文件上传成功');
                    // 刷新列表
                    this.$emit('search_cert_list')
                } else {
                    this.$ypMsg.notice_error(this, '错误', event.message, event.solution);
                }
                this.show_loading_createCert = false;
                this.modal_cert = false;
                this.formData_reset();
            },
            handleError(event, file, fileList) {
                this.$ypMsg.notice_error_simple(this, '密钥文件上传失败', '附件 ' + fileList.name + '上传失败');
                this.show_loading_createCert = false;
                this.modal_cert = false;
                this.formData_reset();
            },
            handleBeforeUpload_modal(file) {
                let limit = this.formatString_handler();
                let message = ''
                if (limit) {
                    message = '</br>支持的格式为:<br/><span style="width:252px;word-wrap: break-word">' + limit + '</span>'
                }
                if (this.fileType_check(this.fileType_handler(file.name), this.format_limit) === -1) {
                    this.$ypMsg.notice_warning(this, '添加失败，密钥文件格式不正确' + message, '提示');
                } else {
                    this.$ypMsg.notice_info(this, '密钥文件：' + file.name + '<br/>已添加', '提示');
                    this.uploadFile = [];
                    this.uploadFile.push(file);
                }
                return false;
            },
            // 格式处理
            formatString_handler() {
                if (this.format_limit && this.format_limit.length > 0) {
                    let result = '';
                    for (var i = 0; i < this.format_limit.length; i++) {
                        if (i === this.format_limit.length - 1) {
                            result = result + this.format_limit[i]
                        } else {
                            result = result + this.format_limit[i] + ','
                        }
                    }
                    return result;
                } else {
                    return ''
                }
            },
            // 判断附件格式是否符合
            fileType_check(type, arr) {
                if (!Array.indexOf) {
                    Array.prototype.indexOf = function (obj) {
                        for (var i = 0; i < this.length; i++) {
                            if (this[i] == obj) {
                                return i;
                            }
                        }
                        return -1;
                    }
                }
                return arr.indexOf(type);
            },
            // 后缀处理
            fileType_handler(fileName) {
                let arr = fileName.split('.');
                return arr[arr.length - 1];
            },
            inputType_change(status) {
                if (status === 'TEXT') {
                    this.$refs.upload.clearFiles();
                    this.$refs.form_cert.resetFields();
                    // this.form_cert.type = ''
                }
            },
            // 组件初始化
            init() {
                let param = {
                    usage: 'BOTH'
                }
                this.update_select_list(param);
            },
            // 更新下拉框内容
            update_select_list(params) {
                this.$refs.select_certType_m.updateList(params);
            },
            // 密钥用途改变操作
            usage_change(val) {
                let params = {
                    usage: val
                }
                this.$refs.select_certType_m.updateList(params);
                if(val === 'API_INVOKE_CIPHER'){
                    this.form_cert.type= ""
                }else{
                    this.form_cert.type= "RSA2048"
                    this.updateSelect_certType_m("RSA2048")
                }
            }

        },
    };
</script>

<style scoped>

</style>