<style lang="less">
  @import "../../../styles/common";
  @import "../../API_Management_Open/api_Mangement_Open";

  .demo-badge-alone {
    background: #5cb85c !important;
  }

  .round {
    width: 16px;
    height: 16px;
    display: inline-block;
    font-size: 20px;
    line-height: 16px;
    text-align: center;
    color: #f00;
    text-decoration: none;
  }

  .ivu-table-cell {
    padding-left: 10px;
    padding-right: 10px;
  }
</style>
<template>
  <div style="background: #eee;padding:8px">
    <Card :bordered="false">
      <Row type="flex" align="middle">
        <Col span="20">
        <Col span="7">
        <Col span="7" class="margin-top-10">
        <span>商户编码:</span>
        </Col>
        <Col span="17">
        <Input id="input_cert_1" class="margin-top-5" v-model="data_search_commercialCode" placeholder="商户编码"
          @on-enter="search"></Input>
        </Col>
        </Col>

        <Col offset="1" span="7">
        <Col span="7" class="margin-top-10">
        <span>应用标识:</span>
        </Col>
        <Col span="17">
        <Input id="input_cert_2" class="margin-top-5" v-model="data_search_appKey" placeholder="应用标识"
          @on-enter="search"></Input>
        </Col>
        </Col>

        <Col offset="1" span="7">
        <Col span="7" class="margin-top-10">
        <span>应用名称:</span>
        </Col>
        <Col span="17">
        <Input id="input_cert_3" class="margin-top-5" v-model="data_search_appName" placeholder="应用名称"
          @on-enter="search"></Input>
        </Col>
        </Col>

        <Col class="margin-top-5" span="7">
        <Col span="7" class="margin-top-10">
        <span>密钥类型:</span>
        </Col>
        <Col span="17">
        <common-select ref="select_certType" @on-update="updateSelect_certType" type="normal" keyWord="result"
          holder="请选择（默认全部）" code="code" title="name" group="cert_type_common" @on-loaded="select_callBack"
          :default="this.data_select_certType" :uri="this.$store.state.select.cert_type_common.uri"></common-select>
        </Col>
        </Col>

        <Col class="margin-top-5" offset="1" span="7">
        <Col span="7" class="margin-top-10">
        <span>密钥状态:</span>
        </Col>
        <Col span="17">
        <common-select ref="select_certStatus" @on-update="updateSelect_certStatus" type="normal" keyWord="result"
          holder="请选择（默认全部）" code="code" title="name" group="cert_status" @on-loaded="select_callBack"
          :default="this.data_select_certStatus" :uri="this.$store.state.select.cert_status.uri"></common-select>
        </Col>
        </Col>

        <Col class="margin-top-5" offset="1" span="7">
        <Col span="7" class="margin-top-10">
        <span>创建时间:</span>
        </Col>
        <date-picker ref="datepicker" @on-start-change="update_start_date" @on-end-change="update_end_date"
          :default_start="this.dateStart" :default_end="this.dateEnd"></date-picker>
        </Col>
        </Col>

        <Col span="4">
        <Col class="margin-top-10" span="11" style="text-align:center">
        <Button id="btn_cert_1" type="primary" v-url="{url:'/rest/isv/cert/list'}" @click="search">查询</Button>
        </Col>
        <Col class="margin-top-10" span="11" style="text-align:center">
        <Button id="btn_cert_2" type="ghost" @click="reset_search">重置</Button>
        </Col>
        </Col>
      </Row>
      <Row class="margin-top-20">
        <Col span="24">
        <Button id="btn_cert_3" class="margin-right-10" v-url="{url:'/rest/isv/cert/create'}" type="primary"
          @click="cert_add">新增密钥</Button>
          <Button id="btn_cert_4" class="margin-right-10 margin-left-5" v-url="{url:'/rest/isv/cert/change-record'}"
          type="success" @click="cert_record('')">密钥变更记录</Button>
        <Button id="btn_cert_5" class="margin-right-10 margin-left-5" v-url="{url:'/rest/isv/cert/yop-platform-public-key'}"
          type="warning" @click="public_key_preview">查看易宝公钥</Button>
        </Col>
      </Row>
      <Row class="margin-top-10">
        <Col span="24">
        <Table id="table_1" border :columns="columns_certList" :data="data_certList"></Table>
        <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
          <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo"
            show-elevator @on-change="page_refresh"></Page>
        </Tooltip>
        </Col>
        <loading :show="show_loading_certList"></loading>
      </Row>
      <modal_create_edit_cert ref="modal_create_edit_cert" @search_cert_list="search"></modal_create_edit_cert>
      <modal_confirm ref="modal_confirm" :title="modal_confirm_title" :warning_red="modal_confirm_warning_red"
        :warning_black="modal_confirm_warning_black" :reason_fail="modal_confirm_reason_fail" :desc="modal_confirm_desc"
        :reason_length="modal_confirm_reason_length" :reason_show="modal_confirm_reason_show"
        @confirm_handler="confirm_handler"></modal_confirm>
      <modal_opt_record ref="modal_opt_record"></modal_opt_record>
      <modal_public_key ref="modal_public_key"></modal_public_key>
      <modal_copy ref="modal_copy" :title="copyTitle"></modal_copy>
    </Card>
  </div>
</template>

<script>
  import loading from "../../my-components/loading/loading";
  import commonSelect from "../../common-components/select-components/selectCommon";
  import util from "../../../libs/util";
  import api from "../../../api/api";
  import datePicker from "../../common-components/date-components/date-picker";
  import modal_create_edit_cert from "./modals/modal_create_edit_cert";
  import modal_confirm from "../../common-components/modal-components/modal_confirm";
  import modal_opt_record from "./modals/modal_change_record";
  import modal_public_key from "./modals/modal_yeepay_public_key";
  import modal_copy from "../../common-components/modal-components/modal_copy";

  import toolTip_common from "../../my-components/toolTip/toolTip_common";
  export default {
    name: "cert-list",
    components: {
      loading,
      commonSelect,
      datePicker,
      modal_create_edit_cert,
      modal_confirm,
      modal_opt_record,
      modal_public_key,
      modal_copy,
      toolTip_common
    },
    data() {
      return {
        /**
         * 密钥列表部分变量
         */
        // 密钥列表loading
        show_loading_certList: false,
        // 搜索框-应用标识数据绑定
        data_search_appKey: "",
        // 搜索框-应用名称数据绑定
        data_search_appName: "",
        // 搜索框-商户编码数据绑定
        data_search_commercialCode: "",
        // 下拉框-密钥类型数据绑定
        data_select_certType: "",
        // 下拉框-密钥状态数据绑定
        data_select_certStatus: "",
        // 搜索时间框-创建时间开始时间数据绑定
        dateStart: "",
        // 搜索时间框-创建时间结束时间数据绑定
        dateEnd: "",
        // 下拉框加载统计
        count_select_related: 2,
        // 密钥列表表头配置
        columns_certList: [
          {
            title: "商户编码",
            key: "customerNo",
            width: 98,
            align: "center",
            // fixed: "left"
          },
          {
            title: "应用名称",
            // width: 165,
            render: (h, params) => {
              return h("div", [
                h("p", params.row.appId),
                h("p", "(" + params.row.appName + ")")
              ]);
            },
            align: "center"
          },
          {
            title: "密钥名称",
            // width: 200,
            align: "center",
            render: (h, params) => {
              return h("div", [
                h("p", params.row.alias),
                h(
                  "a",
                  {
                    directives: [
                      {
                        name: "show",
                        value: params.row.appType !== "PLATFORM_TEST" && params.row.usage !== "API_INVOKE_CIPHER" && util.format_check_common(params.row.type, /^(AES)/)
                      }
                    ],
                    on: {
                      click: () => {
                        this.cert_copy(params.row.id, params.row.type);
                      }
                    }
                  },
                  "查看密钥"
                )
              ]);
            }
          },
          {
            title: "密钥用途",
            key: "type",
            width: 100,
            align: "center",
            render: (h, params) => {
              return h(
                "div",
                this.data_name_handler(params.row.usage, "cert_usage")
              );
            }
          },
          {
            title: "密钥类型",
            key: "type",
            width: 72,
            align: "center",
            render: (h, params) => {
              return h(
                "div",
                this.data_name_handler(params.row.type, "cert_type_common")
              );
            }
          },
          {
            title: "密钥来源",
            key: "source",
            width: 85,
            align: "center"
          },
          {
            title: "密钥有效期",
            width: 135,
            align: "center",
            render: (h, params) => {
              if (
                params.row.effectiveDate === "" &&
                params.row.expiredDate === ""
              ) {
                return h("div", "永久有效");
              } else {
                return h("div", [
                  h("p", util.empty_handler(params.row.effectiveDate)),
                  h("p", util.empty_handler(params.row.expiredDate))
                ]);
              }
            }
          },
          {
            title: "密钥状态",
            key: "status",
            width: 78,
            align: "center",
            render: (h, params) => {
              let color = "red";
              let status = this.data_name_handler(
                params.row.status,
                "cert_status"
              );
              if (params.row.status === "ENABLED") {
                color = "green";
                // status = '活动中';
              } else if (params.row.status === "DISABLED") {
                color = "red";
              } else {
                color = "grey";
                // status = '已删除';
              }
              return h("div", [
                h(
                  "Tag",
                  {
                    style: {
                      align: "center",
                      width: "60px"
                    },
                    props: {
                      color: color
                    }
                  },
                  status
                )
              ]);
            }
          },
          {
            renderHeader: (h, params) => {
              return h("div", [h("p", "创建时间"), h("p", "最后修改时间")]);
            },
            width: 200,
            align: "center",
            render: (h, params) => {
              return h("div", [
                h("p", params.row.createdDate),
                h("p", params.row.lastModifiedDate)
              ]);
            }
          },
          {
            title: "操作",
            align: "center",
            key: "operations",
            width: 100,
            // fixed: "right",
            // 'min-width': 140,
            // 'max-width': 200,
            render: (h, params) => {
              let option = "禁用";
              let optionAction = false;
              let url = "/rest/app/forbid";
              if (params.row.usage === "API_INVOKE_CIPHER") {
                return this.specific_options_return(h, params);
              }
              if (params.row.status === "ENABLED") {
                option = "禁用";
                optionAction = false;
                url = "/rest/isv/cert/disable";
              } else if (params.row.status === "DISABLED") {
                option = "启用";
                optionAction = true;
                url = "/rest/isv/cert/enable";
              }
              return h("div", [
                h(
                  "Button",
                  {
                    props: {
                      type: "text",
                      size: "small"
                    },
                    on: {
                      click: () => {
                        this.cert_sign(params.row);
                      }
                    }
                  },
                  "数字签名"
                ),
                h(
                  "Button",
                  {
                    props: {
                      type: "text",
                      size: "small"
                    },
                    directives: [
                      {
                        name: "url",
                        value: { url: url }
                      },
                      {
                        name: "show",
                        value:
                          params.row.appType === "PLATFORM" ||
                          params.row.appType === "PARTNER"
                      }
                    ],
                    on: {
                      click: () => {
                        this.cert_option(params.row.id, optionAction);
                      }
                    }
                  },
                  option
                ),
                h(
                  "Button",
                  {
                    props: {
                      type: "text",
                      size: "small"
                    },
                    directives: [
                      {
                        name: "url",
                        value: { url: "/rest/isv/cert/change-record" }
                      }
                    ],
                    on: {
                      click: () => {
                        this.cert_record(params.row.appId);
                      }
                    }
                  },
                  "变更记录"
                )
              ]);
            }
          }
        ],
        // 密钥列表数据
        data_certList: [],
        // 总数
        pageTotal: 10,
        // 当前页码
        pageNo: 1,
        /**
         * 确认弹窗部分
         */
        // 确认弹窗标题
        modal_confirm_title: "",
        // 确认弹窗红色提示部分
        modal_confirm_warning_red: "",
        // 确认弹窗黑色提示部分
        modal_confirm_warning_black: "",
        // 确认弹窗必填原因规范提示
        modal_confirm_reason_fail: "",
        // 确认弹窗原因描述
        modal_confirm_desc: "",
        // 确认弹窗原因长度限制
        modal_confirm_reason_length: 100,
        // 确认弹窗原因是否展示
        modal_confirm_reason_show: true,

        /**
         * 复制弹窗部分
         */
        // 弹窗标题
        copyTitle: "",
        // 第一列标题
        copyCol1: "",
        // 第二列标题
        copyCol2: ""
      };
    },
    methods: {
      // 页面初始化
      init() {
        this.cert_usage_translate();
        this.init_search()
      },
      // 页面初始化查询赋值操作
      init_search() {
        this.data_search_appKey = this.$store.state.current_appId;
        if(this.data_search_appKey) {
          this.search()
        }
      },
      // 获取密钥用途翻译
      cert_usage_translate() {
        let data = this.$store.state.select["cert_usage"].data
        if (data && data.length > 0) return
        api.yop_isv_cert_usage().then(
          (response) => {
            let resultTemp = response.data.data.result
            let dataTemp = [];
            if (resultTemp && resultTemp.length > 0) {
              resultTemp.forEach(
                (item) => {
                  dataTemp.push(
                    {
                      value: item.code,
                      label: item.name,
                      name: item.name
                    }
                  )
                })
            }
            this.$store.state.select["cert_usage"].data = dataTemp;
          }
        )
      },
      // 数据检索
      search() {
        let params = this.search_pre_option();
        this.list_handler(params);
      },
      // 检索条件初始化
      reset_search() {
        this.$refs.datepicker.reset();
        this.$refs.select_certType.resetSelected();
        this.$refs.select_certStatus.resetSelected();
        this.dateStart = "";
        this.dateEnd = "";
        this.data_search_commercialCode = "";
        this.data_search_appKey = "";
        this.data_search_appName = "";
        this.data_select_certType = "";
        this.data_select_certStatus = "";
      },
      // 密钥复制
      cert_copy(id, type) {
        this.copyTitle = "查看密钥";
        this.$refs.modal_copy.col_set("密钥类型", "密钥内容");
        this.$refs.modal_copy.loading_preview();
        this.$refs.modal_copy.modal_preview();
        let params = {
          id: id
        };
        api.yop_isv_cert_value(params).then(response => {
          if (response.data.status === "success") {
            let content = response.data.data.value;
            this.$refs.modal_copy.data_set(type, content);
          } else {
            this.$ypMsg.notice_error(
              this,
              "错误",
              response.data.message,
              response.data.solution
            );
          }
        });
      },
      // 请求参数生成前序操作
      search_pre_option() {
        this.show_loading_certList = true;
        let params = {
          customerNo: this.data_search_commercialCode.trim(),
          appId: this.data_search_appKey.trim(),
          appName: this.data_search_appName.trim(),
          type: this.data_select_certType,
          status: this.data_select_certStatus,
          createdStartDate: util.dateFormat_component(this.dateStart),
          createdEndDate: util.dateFormat_component_end(this.dateEnd),
          _pageNo: 1
        };
        util.paramFormat(params);
        return params;
      },
      // 列表刷新
      page_refresh(val) {
        let params = this.search_pre_option();
        if (val) {
          params._pageNo = val;
        }
        this.list_handler(params);
      },
      // 列表请求执行函数
      list_handler(params) {
        api.yop_isv_cert_list(params).then(response => {
          if (response.data.status === "success") {
            this.tableDataFormat(response.data.data.page.items);
            this.pageNo = response.data.data.page.pageNo;
            if (
              response.data.data.page.items &&
              response.data.data.page.items.length > 0
            ) {
              if (response.data.data.page.items.length < 10) {
                this.pageTotal = response.data.data.page.items.length;
              } else {
                this.pageTotal = NaN;
              }
            } else {
              this.pageTotal = NaN;
            }
          } else {
            this.$ypMsg.notice_error(
              this,
              "错误",
              response.data.message,
              response.data.solution
            );
          }
          this.show_loading_certList = false;
        });
      },
      // 表格数据处理函数
      tableDataFormat(items) {
        this.data_certList = [];
        for (var i in items) {
          this.data_certList.push({
            id: util.empty_handler(items[i].id),
            customerNo: util.empty_handler(items[i].customerNo),
            appId: util.empty_handler(items[i].appId),
            appName: util.empty_handler(items[i].appName),
            appType: util.empty_handler(items[i].appType),
            alias: util.empty_handler(items[i].alias),
            value: util.empty_handler(items[i].value),
            usage: util.empty_handler(items[i].usage),
            signSha1: items[i].signSha1,
            signSha256: items[i].signSha256,
            type: items[i].type,
            digest: items[i].digest,
            source: util.empty_handler(items[i].source),
            effectiveDate: util.empty_handler2(items[i].effectiveDate),
            expiredDate: util.empty_handler2(items[i].expiredDate),
            status: util.empty_handler(items[i].status),
            createdDate: util.empty_handler(items[i].createdDate),
            lastModifiedDate: util.empty_handler(items[i].lastModifiedDate)
          });
        }
      },
      // 下拉框加载完处理函数
      select_callBack() {
        this.count_select_related--;
      },
      // 密钥类型数据更新
      updateSelect_certType(val) {
        this.data_select_certType = val;
      },
      // 密钥状态数据更新
      updateSelect_certStatus(val) {
        this.data_select_certStatus = val;
      },
      // 新增密钥
      cert_add() {
        this.$refs.modal_create_edit_cert.formData_reset();
        this.$refs.modal_create_edit_cert.set_current_appId(
          this.data_search_appKey.trim()
        );
        this.$refs.modal_create_edit_cert.set_modal_preview(true);
      },
      // 密钥启用、禁用
      cert_option(id, optionAction) {
        let dataTemp = {};
        if (optionAction) {
          dataTemp = {
            title: "启用",
            warning_red: "启用密钥后，商户可以使用该密钥调用接口!",
            warning_black: "确定启用该密钥？",
            reason_fail: "启用原因必填！",
            desc: "最长可输入100字符",
            length: 100,
            show: true
          };
        } else {
          dataTemp = {
            title: "禁用",
            warning_red: "禁用密钥后，商户不能使用该密钥调用接口!",
            warning_black: "确定禁用该密钥？",
            reason_fail: "禁用原因必填！",
            desc: "最长可输入100字符",
            length: 100,
            show: true
          };
        }
        this.modal_confirm_info_init(dataTemp);
        this.$refs.modal_confirm.set_current_id(id);
        this.$refs.modal_confirm.preview_show();
      },
      // 返回相应编码的名字
      data_name_handler(code, name) {
        if (code) {
          if (
            this.$store.state.select[name].data &&
            this.$store.state.select[name].data.length > 0
          ) {
            let group = this.$store.state.select[name].data;
            for (var i in group) {
              if (group[i].value === code) {
                return group[i].name;
              }
            }
          } else {
            return code;
          }
        } else {
          return "";
        }
      },
      // 开始日期更新
      update_start_date(val) {
        this.dateStart = val;
      },
      // 结束日期更新
      update_end_date(val) {
        this.dateEnd = val;
      },
      // 密钥变更记录
      cert_record(id) {
        this.$refs.modal_opt_record.reset_search();
        if(id){
          this.$refs.modal_opt_record.set_appId(id);
        }
        this.$refs.modal_opt_record.modal_preview();
        this.$refs.modal_opt_record.search();
      },

      // 展期操作函数
      expand_handler(param) {
        let paramTemp = {
          id: param.id,
          effectiveDate: param.startDate,
          expiredDate: param.endDate,
          cause: param.reason
        };
        api.yop_isv_cert_defer(paramTemp).then(response => {
          if (response.status === "success") {
            this.$ypMsg.notice_success(this, "展期成功");
            this.search();
          } else {
            this.$ypMsg.notice_error(
              this,
              "展期错误",
              response.message,
              response.solution
            );
          }
        });
      },
      // 确认弹窗初始化函数
      modal_confirm_info_init(data) {
        this.modal_confirm_title = data.title;
        this.modal_confirm_warning_red = data.warning_red;
        this.modal_confirm_warning_black = data.warning_black;
        this.modal_confirm_reason_fail = data.reason_fail;
        this.modal_confirm_desc = data.desc;
        this.modal_confirm_reason_length = data.length;
        this.modal_confirm_reason_show = data.show;
      },
      //  确认弹窗处理函数
      confirm_handler(data) {
        console.log(data);
        let param = {
          id: data.id,
          cause: data.reason
        };
        if (data.title === "吊销") {
          api.yop_isv_cert_revoke(param).then(response => {
            if (response.status === "success") {
              this.$ypMsg.notice_success(this, "吊销成功");
              this.search();
            } else {
              this.$ypMsg.notice_error(
                this,
                "吊销错误",
                response.message,
                response.solution
              );
            }
          });
        } else if (data.title === "启用") {
          api.yop_isv_cert_active(param).then(response => {
            if (response.status === "success") {
              this.$ypMsg.notice_success(this, "启用成功");
              this.search();
            } else {
              this.$ypMsg.notice_error(
                this,
                "启用错误",
                response.message,
                response.solution
              );
            }
          });
        } else if (data.title === "禁用") {
          api.yop_isv_cert_forbid(param).then(response => {
            if (response.status === "success") {
              this.$ypMsg.notice_success(this, "禁用成功");
              this.search();
            } else {
              this.$ypMsg.notice_error(
                this,
                "启用错误",
                response.message,
                response.solution
              );
            }
          });
        }
      },
      // 公钥查看
      public_key_preview() {
        this.$refs.modal_public_key.get_public_key();
        this.$refs.modal_public_key.modal_preview();
      },
      // 签名查看
      sign_preview(name, value) {
        this.copyTitle = "查看数据签名";
        this.$refs.modal_copy.col_set("数字签名", "签名加密串");
        this.$refs.modal_copy.loading_preview();
        this.$refs.modal_copy.modal_preview();
        this.$refs.modal_copy.data_set(name, value);
      },
      // 特殊状态的操作返回
      specific_options_return(h, params) {
        return h("div", [
        h(
                  "Button",
                  {
                    props: {
                      type: "text",
                      size: "small"
                    },
                    on: {
                      click: () => {
                        this.cert_sign(params.row);
                      }
                    }
                  },
                  "数字签名"
                ),
          h(
            "Button",
            {
              props: {
                type: "text",
                size: "small"
              },
              directives: [
                {
                  name: "url",
                  value: { url: "/rest/isv/cert/change-record" }
                }
              ],
              on: {
                click: () => {
                  this.cert_record(params.row.appId);
                }
              }
            },
            "变更记录"
          )
        ]);
      },
      // 数字签名查看
      cert_sign(val) {
        const { digest } = val
        const arr = []
        Object.keys(digest).forEach(key => {
          arr.push({
            type: key,
            value: digest[key] 
          })
        })
        this.copyTitle = "查看数据签名";
        this.$refs.modal_copy.col_set("签名算法", "签名串");
        this.$refs.modal_copy.loading_preview();
        this.$refs.modal_copy.modal_preview();
        this.$refs.modal_copy.data_set_arr(arr);
      }
    },
    mounted() {
      this.init();
    },
    beforeRouteLeave(to, from, next) {
      this.$store.state.current_appId = "";
      this.$destroy();
      next();
    }
  };
</script>

<style scoped>
</style>