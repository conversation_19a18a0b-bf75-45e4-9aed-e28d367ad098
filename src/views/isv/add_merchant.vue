<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.el-checkbox__inner {
  width: 12px !important;
  height: 12px !important;
}

.ivu-input {
    width:400px;
}
.custom-tree-node {
  font-size: 12px;
}

.yop-explain-120 {
  font-size: 12px;
  color: grey;
  padding-left: 120px;
  line-height: 30px;
}
.red {
  color: red;
}
.green {
  color: green;
}
.margin-left-30 {
    margin-left: 30px;
}
/*@import '../API_Management_Open/api_Mangement_Open.less';*/
@import "../../styles/common.less";
</style>
<template>
  <div style="background: #eee;padding:8px">
    <Card :bordered="false">
       
      <Row type="flex" align= "middle">
        <Col span="24">
       
        <div>
          <p slot="header" style="color:#2d8cf0;height:34px;border-bottom:1px solid #e9eaec;margin-bottom:20px;">
          <span style="color:black">新增商户</span>
          </p>
          <Col span="20">
            <Form ref="form_detail" :model="form_detail" :rules="rule_detail" :label-width="150">
              <FormItem label="商户提供方：" prop="providerCode">
                <RadioGroup v-model="form_detail.providerCode"  @on-change="getCustomerNo()">
                  <Radio label="YEEPAY">易宝商户</Radio>
                  <Radio label="KJ">跨境商户</Radio>
                  <Radio label="PAYPLUS">钱麦商户</Radio>
                  <Radio label="AIR">航旅钱包商户</Radio>
                  <Radio label="YOP">YOP商户</Radio>
                </RadioGroup>
              </FormItem>
              <p style="color:red!important;margin-left:150px;font-size:12px;">
                          <span v-show="form_detail.providerCode == 'YEEPAY'">易宝商户指通过易宝OP入网的官网商户，需提供易宝商户编码；</span>
                          <span v-show="form_detail.providerCode == 'KJ'">跨境商户指通过跨境平台入网的商户，需提供跨境商户编码；</span>
                          <span v-show="form_detail.providerCode == 'PAYPLUS'">钱麦商户指通过钱麦平台入网的商户，需提供钱麦商户编码；</span>
                          <span v-show="form_detail.providerCode == 'AIR'">航旅钱包商户指通过航旅平台入网的商户，需提供航旅商户编码；</span>
                          <span v-show="form_detail.providerCode == 'YOP'">YOP商户指给YOP内部开通商户不走OP入网，可以不提供商编；</span>
                      </p>
              <!-- <Alert style="margin:10px 40px;" type="warning" >
                show-icon
                  温馨提示：
                  <template slot="desc">
                      
                  </template>
              </Alert> -->
              
              <FormItem label="商户编码：" prop="code">
                <Input
                v-show="!hasCustomerNo"
                  type="text"
                  size="small"
                  v-model="form_detail.code"
                  style="width:85%"
                ></Input>
                <p v-show="hasCustomerNo">{{form_detail.code}}</p>
              </FormItem>
              <FormItem label="商户名称：" prop="name">
                <Input
                  type="text"
                  size="small"
                  v-model="form_detail.name"
                  style="width:85%"
                ></Input>
              </FormItem>
              <!-- <p class="yop-explain-120 margin-left-30">最长可输入64位字符。</p> -->
              <!-- <FormItem label="文档标签：">
                <common-select
                  ref="select_appKey_m"
                  @on-update="updateSelect_appKey_model"
                  type="combo"
                  keyWord="result"
                  holder="请选择文档标签（默认全部）"
                  code="appKey"
                  title="appName"
                  size="small"
                  group="service_authz_appKey"
                  @on-loaded="select_callBack"
                  :default="this.form_detail.appKeyCode"
                  :uri="this.$store.state.select.service_authz_appKey.uri"
                  style="width:55%"
                ></common-select>
                <Input class="edit-detail-textarea margni-top-5" type="text"
                  size="small" v-model="customLabel" @on-blur="onblur" @on-focus="onfocus" placeholder="自定义标签" style="width:20%"></Input><div class="operate-editable" v-show="show_operate"><Button type="primary" size="small" icon="ios-checkmark-empty" @click="sure_add_label"></Button><Button size="small" icon="ios-close-empty" @click="close_operate"></Button></div>
              </FormItem> -->
            
              <FormItem label="商户类型：" prop="authType">
                <RadioGroup v-model="form_detail.authType">
                  <Radio label="AGENT">系统商/代理商</Radio>
                  <Radio label="SELF">直销商</Radio>
                  <Radio label="TEST">测试商户</Radio>
                </RadioGroup>
                <!-- <date-picker
                  ref="datepicker"
                  @on-start-change="update_start_date"
                  @on-end-change="update_end_date"
                  :default_start="this.dateStart"
                  :default_end="this.dateEnd"
                ></date-picker> -->
              </FormItem>
              <!-- <FormItem label="上传文档LOGO：">
                  <div>
                      <div class="demo-upload-list" v-for="(item,index) in uploadList" :key="index">
                        <Input type="text" size="small" v-model="item.url"></Input>
                          <div class="demo-upload-list-cover">
                              <Icon type="ios-trash-outline" @click.native="handleRemove(item)"></Icon>
                          </div>
                      </div>
                      <Upload ref="upload" :show-upload-list="false" :format="['jpg','jpeg','png']" :max-size="2048" :before-upload="handleBeforeUpload" :on-format-error="handleFormatError" :on-exceeded-size="handleMaxSize" type="drag" action="//jsonplaceholder.typicode.com/posts/" style="display: inline-block;width:58px;">
                          <div style="width: 58px;height:58px;line-height: 58px;">
                              <Icon type="camera" size="20"></Icon>
                          </div>
                      </Upload>
                  </div>
              </FormItem> -->
              <FormItem label="操作原因：" prop="cause">
                  <Input type="textarea" size="small" v-model="form_detail.cause"
                          style="width:85%"></Input>
              </FormItem>
              <p class="yop-explain-120 margin-left-30">最长支持输入100个字符</p>
              <div style="text-align:center">
                <Button id="add_merc_btn1" type="primary" @click="expires_submit('form_detail')">确定</Button>
                <Button id="add_merc_btn2" type="ghost" @click="expires_cancel">取消</Button>
              </div>
            </Form>
          </Col>

        </div>
        
        <loading :show="show_loading_preview"></loading>
      </Col>
    </Row>
    </Card>
    <!-- <modal-sga-new-app ref="modal_sga_new_app"></modal-sga-new-app> -->
  </div>
</template>

<script>
import util from "../../libs/util";
import api from "../../api/api";
import loading from "../my-components/loading/loading";
import datePicker from "../common-components/date-components/date-picker-set";
import commonSelect from "../common-components/select-components/selectCommon_params_single";
// import modalSgaNewApp from './modal-sga-new-app'


export default {
  name: "modal-sga-new-auth",
  props: {
    size: String
  },
  watch: {
    filterText(val) {
      if (val || val === 0) {
        this.service_group_top_ctrl(true);
      } else {
        this.service_group_top_ctrl(false);
      }
      this.$refs.tree.filter(val);
    }
  },
  components: {
    loading,
    datePicker,
    commonSelect
    // modalSgaNewApp
  },
  data() {
    const validate_code = (rule, value, callback) => {
      var reg = /^([A-Za-z0-9]+[:]?[A-Za-z0-9]+)$/
        if(!value){
          callback(new Error("商编不能为空"));
        }else if (util.getLength(value) > 32) {
          callback(new Error("长度不能大于32"));
        } else if(!reg.test(value)){
          callback(new Error("请输入正确的商编格式！"));
        }else{
          callback();
        }
    };
    return {
        // 选择yop获取custormer
    hasCustomerNo:false,
      // 显示自定义标签的编辑操作
      show_operate:false,
      // 自定义标签
      customLabel:"自定义标签",
      // 是否可编辑
      // 文件
      uploadList: [],
      // 服务分组loading
      show_loading_tree: false,
      // 筛选文字
      filterText: "",
      // 开始日期绑定
      dateStart: "",
      //结束日期绑定
      dateEnd: "",
      // 基本信息数据绑定
      form_detail: {
        providerCode:"YEEPAY",
        authType: "AGENT",
        code: "",
        name:"",
        cause:"",
      },
      rule_detail: {
        providerCode:[{ required: true, message: "商户提供方不能为空", trigger: "blur" }],
        code: [{ required: true,validator:validate_code, trigger: "blur" }],
        name: [{ required: true, message: "商户名称不能为空", trigger: "blur" }],
        authType: [{ required: true, message: "商户类型不能为空", trigger: "blur" }]
      },
      // 窗口展示
      modal_show: false,
      show_loading_preview: false,
      data_tree: [],
      // 上一次请求的商编
      last_customerNo: ""
    };
  },
  methods: {
    //   选择yop的时候获取customerNo
    getCustomerNo(val){
        if(this.form_detail.providerCode === "YOP"){
          this.hasCustomerNo = true
          this.form_detail.authType = "SELF"
              api.yop_isv_generate_no().then(response => {
                if (response.data.status === "success") {
                  let result = response.data.data.result;
                  this.form_detail.code = result
                } else {
                  this.$Notice.error({
                    title: "错误",
                    desc: response.message,
                    duration: 10
                  });
                }
              });
        }else{
          this.hasCustomerNo = false
          this.form_detail.authType = "AGENT"
          this.form_detail.code=  ""
        }
    },
    onfocus(){
      this.show_operate = true;
      this.customLabel = ""
    },
    // 失去焦点事件
    onblur(){
      this.show_operate = false;
      console.log(this.customLabel)
      this.customLabel = ""
    },
    // 确定添加
    sure_add_label(){
      this.show_operate = false;
      console.log(this.customLabel)
      this.customLabel = ""
    },
    // 不添加
    close_operate(){
      this.show_operate = false;
      this.customLabel = ""
    },
    // 上传图片
    handleBeforeUpload(file) {
        // 创建一个 FileReader 对象
        let reader = new FileReader()
        // readAsDataURL 方法用于读取指定 Blob 或 File 的内容
        // 当读操作完成，readyState 变为 DONE，loadend 被触发，此时 result 属性包含数据：URL（以 base64 编码的字符串表示文件的数据）
        // 读取文件作为 URL 可访问地址
        reader.readAsDataURL(file)

        const _this = this
        reader.onloadend = function (e) {
            file.url = reader.result
            _this.uploadList.push(file)
        }
        console.log(this.uploadList)
    },
    handleRemove(file) {
        this.uploadList.splice(this.uploadList.indexOf(file), 1)
    },
    handleFormatError(file) {
      this.$Notice.warning({
        title: '文件格式不正确',
        desc: '文件 ' + file.name + ' 格式不正确，请上传 jpg 或 png 格式的图片。'
      })
    },
    handleMaxSize(file) {
      this.$Notice.warning({
        title: '超出文件大小限制',
        desc: '文件 ' + file.name + ' 太大，不能超过 2M。'
      })
    },

    // 启用/禁用所有1级服务分组
    service_group_top_ctrl(option) {
      this.data_tree.forEach(item => {
        item.disabled = option;
      });
    },
    // 组件初始化获取
    init() {
      this.$refs.form_detail.resetFields();
      this.form_detail.code = "";
      this.$refs.select_appKey_m.resetSelected();
      this.$refs.select_appKey_m.data_List = [];
      this.$store.state.select.service_authz_appKey.data = [];
      this.data_tree = [];
      // this.information_tree_get();
    },
    // 新增商户
    expires_submit(val) {
      this.$refs[val].validate(valid => {
        if (valid) {
          console.log(valid,this.form_detail.providerCode)
            var  param = {
                providerCode: this.form_detail.providerCode,
                customerNo: this.form_detail.code,
                name:this.form_detail.name,
                type: this.form_detail.authType,
                cause:this.form_detail.cause
              };
           console.log(1,param,"新增商户");
              api.yop_isv_create(param).then(response => {
                if (response.status === "success") {
                  let result = response.data.result;
                  this.$router.push({
                    name:"isv/list"
                  })
                } else {
                  this.$Notice.error({
                    title: "错误",
                    desc: response.message,
                    duration: 10
                  });
                }
              });
        } else {
          this.$Message.error("请检查");
        }
      });
    },
    // 去除父节点
    parentNode_clear(arr) {
      let serviceGroup_top = this.tree_parentNode_get();
      for (let i = arr.length - 1; i >= 0; i--) {
        if (util.IsInArray(serviceGroup_top, arr[i])) {
          arr.splice(i, 1);
        }
      }
      return arr;
    },
    // 获取树的一级节点
    tree_parentNode_get() {
      let resultTemp = [];
      for (var i in this.data_tree) {
        resultTemp.push(this.data_tree[i].code);
      }
      return resultTemp;
    },
    // 部分授权失败字段处理
    failed_reason_handler(result) {
      let resultTemp = "";
      for (var i in result) {
        if (i === result.length - 1) {
          resultTemp =
            resultTemp +
            (parseInt(i) + 1) +
            ".<span style='color:red'>" +
            result[i].code +
            "</span>: " +
            result[i].reason;
        } else {
          resultTemp =
            resultTemp +
            (parseInt(i) + 1) +
            ".<span style='color:red'>" +
            result[i].code +
            "</span>:" +
            result[i].reason +
            "<br/>";
        }
      }
      return resultTemp;
    },
    expires_cancel() {
      this.preview_cancel();
      this.$router.push({
        path:"/isv/list"
      })
    },
    // 窗口关闭按钮
    preview_cancel() {
      this.modal_show = false;
    },
    // 窗口显示按钮
    preview_show() {
    //   this.form_detail.authType = "永久授权";
      this.modal_show = true;
    },
    // 开始日期更新
    update_start_date(val) {
      this.dateStart = val;
    },
    // 结束日期更新
    update_end_date(val) {
      this.dateEnd = val;
    },
    // 日期初始化
    date_picker_init() {
      this.$refs.datepicker.date_now_set();
    },
    // 设置商编
    customer_no_set(data) {
      this.form_detail.code = data;
    },
    // 新增应用函数
    addApp() {
      this.$refs.modal_sga_new_app.set_code_modal();
      this.$refs.modal_sga_new_app.modal_preview();
    },
    // 应用标识数据更新
    updateSelect_appKey_model(val) {
      this.form_detail.appKeyCode = val;
      let param = {};
      if (val) {
        param = {
          customerNo: this.form_detail.code,
          appKey: val
        };
      } else {
        param = {
          customerNo: this.form_detail.code
        };
      }
      this.information_tree_get(param);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    select_callBack() {},
    // 树的结构信息获取
    information_tree_get(param) {
      this.show_loading_tree = true;
      api.yop_service_group_list(param).then(response => {
        let status = response.data.status;
        if (status === "success") {
          let result = response.data.data.result;
          this.data_tree = [];
          this.data_tree = this.information_tree_handler(result, 0);
        } else {
          this.$ypMsg.notice_error(
            this,
            "服务分组列表数据获取失败,请刷新重",
            response.data.message,
            response.data.solution
          );
        }
        this.show_loading_tree = false;
      });
    },
    // 树的结构数据处理
    information_tree_handler(data, index) {
      let topTemp = true;
      if (index === 0) {
        topTemp = true;
      } else {
        topTemp = false;
      }
      let returnData = [];
      index++;
      if (data) {
        data.forEach(item => {
          let enableStatusTemp = true;
          let statusTemp = "启用";
          let optionTemp = "禁用";
          let colorTemp = "green";
          let nameTemp = "";
          if (item.status === "ENABLE") {
            statusTemp = "启用";
            optionTemp = "禁用";
            colorTemp = "green";
            enableStatusTemp = true;
          } else {
            statusTemp = "禁用";
            optionTemp = "启用";
            colorTemp = "red";
            enableStatusTemp = false;
          }
          if (util.getLength(item.name) > 10) {
            nameTemp = item.name.substring(0, 10) + "...";
          } else {
            nameTemp = item.name;
          }
          if (!!item.children) {
            returnData.push({
              code: item.code,
              top: topTemp,
              disabled: false,
              label: nameTemp,
              name: item.name,
              depth: item.level,
              status: statusTemp,
              option: optionTemp,
              color: colorTemp,
              enableStatus: enableStatusTemp,
              children: this.information_tree_handler(item.children, index)
            });
          } else {
            returnData.push({
              // id: item.id,
              // pid : item.pid,
              code: item.code,
              top: topTemp,
              label: nameTemp,
              name: item.name,
              depth: item.level,
              disabled: false,
              status: statusTemp,
              option: optionTemp,
              color: colorTemp,
              enableStatus: enableStatusTemp,
              children: []
            });
          }
        });
      }
      return returnData;
    },
    // 商户编号改变请求应用标识 和服务分组列表
    customerNo_blur() {
      if (
        (this.form_detail.code || this.form_detail.code === 0) &&
        this.form_detail.code !== this.last_customerNo
      ) {
        let param = {
          customerNo: this.form_detail.code,
          appKey: ""
        };
        this.$refs.select_appKey_m.updateList(param);
        this.information_tree_get(param);
      } else {
        this.data_tree = [];
        this.$store.state.select.service_authz_appKey.data = [];
      }
    },
    // 应用标识请求

    // 商户编号聚焦时
    customerNo_focus() {
      this.last_customerNo = this.form_detail.code;
    }
  },
  mounted() {
    // this.init();
  }
};
</script>

<style scoped>
.demo-upload-list {
    display: inline-block;
    width: 60px;
    height: 60px;
    text-align: center;
    line-height: 60px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
    margin-right: 4px;
}

.demo-upload-list img {
    width: 100%;
    height: 100%;
}

.demo-upload-list-cover {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, .6);
}

.demo-upload-list:hover .demo-upload-list-cover {
    display: block;
}

.demo-upload-list-cover i {
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    margin: 0 2px;
}

.ivu-icon {
    line-height: 58px;
}
.edit-detail-textarea {
  height: 20px;
  min-width:100px;
  line-height: 20px;
  display: inline-block;
  margin-top: 5px;
  vertical-align: middle;
  border:0;
}
.edit-detail-textarea input.ivu-input {
  border:0!important;
  margin-top: -1px;
}
.operate-editable {
  display: inline-block;
  margin-left: 10px;
  vertical-align: middle;
  margin-top: 3px;
}

</style>
