<template>
    <Modal id="modal_request_1" v-model="modal_cert" width="650" :closable="false">
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">编辑商户</span>
        </p>
        <div>
            <Form ref="form_cert" :model="form_cert" :rules="rule_cert" :label-width="140">
                <FormItem label="商户提供方：" prop="appId">
                    <!-- <Input type="text" size="small" v-model="form_cert.appId" style="width:80%"></Input> -->
                    <p>{{form_cert.providerCode}}</p>
                </FormItem>
                <FormItem label="商户：" prop="appId">
                    <!-- <Input type="text" size="small" v-model="form_cert.appId" style="width:80%"></Input> -->
                    <p>{{form_cert.customerNo}}({{form_cert.name}})</p>
                </FormItem>
                <!-- <p class="yop-explain-140">支持输入大小写字母、数字、中文、特殊字符，最长可输入60个字符</p> -->
                <FormItem label="商户类型：" prop="type">
                    <RadioGroup v-model="form_cert.type" @on-change="usage_change">
                        <Radio label="AGENT">系统商/代理商</Radio>
                        <Radio label="SELF">直销商</Radio>
                        <Radio label="TEST">测试商户</Radio>
                    </RadioGroup>
                </FormItem>
                <FormItem label="操作原因：" prop="cause">
                    <Input type="textarea" size="small" v-model="form_cert.cause" style="width:80%"></Input>
                </FormItem>
               
                <p  class="yop-explain-140">最长可输入100字符</p>
            </Form>
        </div>
        <div slot="footer">
            <Button id="modal_app_btn_2" type="primary" @click="ok_create_cert('form_cert')">确定</Button>
            <Button id="modal_app_btn_1" type="ghost" @click="cancel_create_cert">取消</Button>
        </div>
        <loading :show="show_loading_createCert"></loading>
    </Modal>
</template>

<script>
    import loading from '../my-components/loading/loading'
    import commonSelect from '../common-components/select-components/selectCommon_params_single'
    import util from '../../libs/util'
    import api from '../../api/api'
    export default {
        name: 'modal_create_edit_app',
        components: {
            commonSelect,
            loading
        },
        data() {
            // app名称验证
            const validate_input = (rule, value, callback) => {
                // 模拟异步验证效果
                setTimeout(() => {
                    if (util.getLength(value.trim()) > 60) {
                        callback(new Error('长度不能大于60'));
                    } else {
                        api.yop_app_exists({
                            appId: value.trim()
                        }).then(
                            (response) => {
                                if (response.data.status === 'success') {
                                    let result = response.data.data.result;
                                    if (result) {
                                        callback();
                                    } else {
                                        callback(new Error('该应用标识不存在'));
                                    }
                                } else {
                                    callback(new Error('应用标识验重失败'));
                                }
                            }
                        );
                    }
                }, 300);
            };
            // app名称验证
            const validate_cert_name = (rule, value, callback) => {
                if (util.getLength(value) > 60) {
                    callback(new Error("长度不能大于60"));
                } else {
                    callback();
                }
            };
            return {
                customerNo:"",
                // 窗口显示绑定
                modal_cert: false,
                // 开始时间
                dateStart: '',
                // 结束时间
                dateEnd: '',
                // 表单数据绑定
                form_cert: {
                    appId: '',
                    inputType: '',//FILE
                    key: '',
                    type: '',
                    alias: '',
                    certFile: '',
                    usage: '',
                    authType: '',
                    cause:""
                },
                // 数据限制
                rule_cert: {
                    // appId: [
                    //     { required: true, message: '应用标识不能为空', trigger: 'blur' },
                    //     { validator: validate_input, trigger: 'blur' }
                    // ],
                    // inputType: [
                    //     { required: true, message: '密钥输入方式不能为空' }
                    // ],
                    // alias: [
                    //     { validator: validate_cert_name, trigger: 'blur' }
                    // ],
                    type: [
                        { required: true, message: '商户类型不能为空', trigger: 'blur' },
                    ],
                    // cause:[
                    //     { required: true, message: '原因不能为空', trigger: 'blur' },
                    // ]
                },
                // loading 新建应用
                show_loading_createCert: '',
                format_limit: ['cer'],
                // 上传的header
                header_upload: {
                    Authorization: 'Bearer ' + localStorage.accesstoken
                },
                uploadFile: [], // 需要上传的附件List,
            }
        },
        methods: {
            // 窗口显示设置
            set_modal_preview(val,customerNo) {
                this.customerNo = customerNo.customerNo
                this.modal_cert = val;
                this.init();
            },
            //设置当前应用标识
            set_current_appId(id) {
                this.form_cert.appId = id;
            },
           
            // 新增确定
            ok_create_cert(val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        let param = {
                            providerCode : this.form_cert.providerCode ,
                            customerNo : this.form_cert.customerNo,
                            cause :this.form_cert.cause,
                            type : this.form_cert.type, 
                        }
                        api.yop_isv_update(param).then(
                            (response) => {
                                //需要填写
                                console.log(response,"responseresponse")
                                if (response.status === 'success') {
                                        this.modal_cert = false
                                        this.$ypMsg.notice_success(this, '编辑商户成功', response.message);
                                        this.$emit('search_cert_list')
                                } else {
                                    this.$ypMsg.notice_error(this, '错误', response.message);
                                }
                            }
                        )

                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
        
            // 新增应用取消
            cancel_create_cert() {
                this.modal_cert = false;
                this.formData_reset();
            },
            // 列表数据重置
            formData_reset() {
                this.form_cert.appId = '';
                this.form_cert.inputType = '';
                this.form_cert.key = '';
                this.form_cert.type = '';
                this.form_cert.usage = ''
                this.form_cert.alias = '';
                this.form_cert.authType = '';
                this.dateStart = '';
                this.dateEnd = '';
                this.$refs.form_cert.resetFields();
            },
            // 开始日期更新
            update_start_date(val) {
                this.dateStart = val;
            },
            // 结束日期更新
            update_end_date(val) {
                this.dateEnd = val;
            },
           
            // 格式处理
            formatString_handler() {
                if (this.format_limit && this.format_limit.length > 0) {
                    let result = '';
                    for (var i = 0; i < this.format_limit.length; i++) {
                        if (i === this.format_limit.length - 1) {
                            result = result + this.format_limit[i]
                        } else {
                            result = result + this.format_limit[i] + ','
                        }
                    }
                    return result;
                } else {
                    return ''
                }
            },
           
            // 组件初始化
            init() {
                
                api.yop_isv_detail({customerNo:this.customerNo}).then(response => {
                    console.log(response,"responseresponse")
                    if (response.data.status === "success") {
                        this.form_cert = response.data.data.result
                        this.form_cert.providerCode = response.data.data.result.providerCode
                        this.form_cert.customerNo = response.data.data.result.customerNo
                        this.form_cert.name = response.data.data.result.name
                        this.form_cert.type = response.data.data.result.type
                    } else {
                        this.$ypMsg.notice_error(
                        this,
                        "错误",
                        response.data.message,
                        response.data.solution
                        );
                    }
                });
            },
          
            // 密钥用途改变操作
            usage_change(val) {
               
                // if(val === 'API_INVOKE_CIPHER'){
                    this.form_cert.type= val
                // }else{
                //     this.form_cert.type= "2048"
                // }
            }

        },
    };
</script>

<style scoped>

</style>