<style lang="less">
	@import '../../../styles/common';
	@import '../../API_Management_Open/api_Mangement_Open';

	.demo-badge-alone {
		background: #5cb85c !important;
	}

	.round {
		width: 16px;
		height: 16px;
		display: inline-block;
		font-size: 20px;
		line-height: 16px;
		text-align: center;
		color: #f00;
		text-decoration: none;
	}
  .total-num {
    position: absolute;
    top: 16px;
    left: -90px;
    > .page-num {

    }
  }
	.ivu-table-cell {
		padding-left: 10px;
		padding-right: 10px;
	}
</style>
<style lang="less" scoped>
	.select-wrap {
		display: flex;
		align-items: center;
		justify-content: space-between;
		.selecteds span {
			color: blue;
		}
	}
</style>
<template>
	<div style="background: #eee;padding:8px">
		<Card :bordered="false">
			<Row type="flex">
				<Col span="20">
					<Row>
            <Col span="7">
              <Col span="7" class="margin-top-10">
                <span>通知订单号:</span>
              </Col>
              <Col span="17">
                <Input
                  id="input_notify_1"
                  class="margin-top-5"
                  v-model="params.notificationId"
                  placeholder="商户订单号"
                ></Input>
              </Col>
            </Col>

            <Col offset="1" span="7">
              <Col span="7" class="margin-top-10">
                <span>商户编号:</span>
              </Col>
              <Col span="17">
                <Input id="input_notify_2" class="margin-top-5" v-model="params.customerNo" placeholder="商户编号"></Input>
              </Col>
            </Col>

            <Col offset="1" span="7">
              <Col span="7" class="margin-top-10">
                <span>商户应用:</span>
              </Col>
              <Col span="17">
                <Input id="input_notify_3" class="margin-top-5" v-model="params.appId" placeholder="商户应用"></Input>
              </Col>
            </Col>
          </Row>
          <Row>
					<Col class="margin-top-5" span="7">
						<Col span="7" class="margin-top-10">
							<span>通知状态:</span>
						</Col>
						<Col span="17">
							<Select id="select_notify" clearable v-model="params.status" style="width:100%" class="margin-top-5">
								<Option v-for="item in statusList" :value="item.value" :key="item.value">{{ item.label }}</Option>
							</Select>
						</Col>
					</Col>
					<Col class="margin-top-5" offset="1" span="7">
						<Col span="7" class="margin-top-10">
							<span>通知编码:</span>
						</Col>
						<Col span="17">
							<Input id="input_notify_4" class="margin-top-5" v-model="params.spiName" placeholder="通知编码"></Input>
						</Col>
					</Col>

					<Col class="margin-top-5" offset="1" span="7">
						<Col span="7" class="margin-top-10">
							<span>通知时间:</span>
						</Col>
						<Col span="17" class="margin-top-5">
							<Date-picker
								:value="time"
                id="date-select"
								@on-change="handleDateChange"
								type="datetimerange"
								placeholder="选择日期和时间"
								style="width: 100%"
								:clearable="false"
								:options="{
                  disabledDate: disabledDate
                }"
							></Date-picker>
						</Col>
					</Col>
          <Col class="margin-top-5" span="7">
						<Col span="7" class="margin-top-10">
							<span>通知地址:</span>
						</Col>
						<Col span="17">
							<Input id="input_notify_4" class="margin-top-5" v-model="params.url" placeholder="通知地址"></Input>
						</Col>
					</Col>
          <Col class="margin-top-5" offset="1" span="7">
						<Col span="7" class="margin-top-10">
							<span>通知规则:</span>
						</Col>
						<Col span="17">
							<Input id="input_notify_4" class="margin-top-5" v-model="params.notifyRule" placeholder="通知规则"></Input>
						</Col>
					</Col>
          <Col class="margin-top-5" offset="1" span="7">
						<Col span="7" class="margin-top-10">
							<span>错误类型:</span>
						</Col>
						<Col span="17">
							<Select id="select_notify" clearable v-model="params.errorCode" style="width:100%" class="margin-top-5">
								<Option v-for="item in errorCodeList" :value="item.code" :key="item.code">{{ item.name }}</Option>
							</Select>
						</Col>
					</Col>
          </Row>
          <Row>
            <Col class="margin-top-5" span="7">
              <Col span="7" class="margin-top-10">
                <span>GUID:</span>
              </Col>
              <Col span="17">
                <Input id="input_notify_14" class="margin-top-5" v-model="params.guid" placeholder="GUID"></Input>
              </Col>
            </Col>
          </Row>
				</Col>
				<Col span="4">
					<Col class="margin-top-10" span="11" style="text-align:center">
						<Button id="btn_notify_1" type="primary" @click="search">查询</Button>
					</Col>
					<Col class="margin-top-10" span="11" style="text-align:center">
						<Button id="btn_notify_2" type="ghost" @click="reset_search">重置</Button>
					</Col>
				</Col>
			</Row>
			<Row class="margin-top-20">
				<Col span="24">
					<Button
            id="btn_notify_3"
            v-url="{ url: '/rest/notify/order/query-resend' }"
            :disabled="multiSelectedData.length > 0"
            class="margin-right-10"
            type="success"
            @click="batchRetryFlag">根据查询条件批量重发</Button>
				</Col>
			</Row>
			<Row class="margin-top-10">
				<Col span="24">
					<Alert v-if="multiSelectedData.length > 0">
						<div class="select-wrap">
							<div class="selecteds">
								已选择
								<span>{{multiSelectedData.length}}</span>
								项
							</div>
							<div class="btns">
								<Button type="text" @click="cancelSelect">取消选择</Button>
								<Button type="primary" @click="batchRetrySelectFlag">批量重发</Button>
							</div>
						</div>
					</Alert>
					<Table
						id="table_1"
            ref="table-notify"
						border
						:columns="computedColumns"
						:data="dataList"
						@on-selection-change="handleselection"
					></Table>
					<Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
            <!-- <span class="total-num">总条数 <span class="page-num">{{this.totalPageNum}}</span></span> -->
						<Page
							class="margin-top-10"
							style="float: right"
							:total="pageTotal"
							:page-size="20"
							:current="pageNo"
							show-elevator
							@on-change="page_refresh"
						></Page>
					</Tooltip>
				</Col>
				<loading :show="loading"></loading>
			</Row>
		</Card>
		<PublishRecordModal ref="PublishRecordModal" />
    <latestErrorName ref="latestErrorName" />
    <Modal v-model="retryFlag" width="500"
    :closable="false" :mask-closable="false"
    >
			<div style="margin-left:10px;font-size: 12x;">
				<label for class="reasonType">
					<span style="color:red">*</span>原因：
				</label>
				<Input
					type="textarea"
					v-model="retryCause"
					style="width:85%;font-size: 12x;"
					placeholder="请输入原因"
				></Input>
			</div>
			<div slot="footer">
				<Button @click="showCloseRetry(false)">取消</Button>
				<Button type="primary" @click="showCloseRetry(true)">确定</Button>
			</div>
		</Modal>
	</div>
</template>

<script>
  import loading from '../../my-components/loading/loading';
  import commonSelect from '../../common-components/select-components/selectCommon';
  import util from '../../../libs/util';
  import api from '~/api/spi/notify';
  import PublishRecordModal from './modals/PublishRecordModal'
  import latestErrorName from './modals/latestErrorName'
  export default {
    name: 'notify-list',
    components: {
      loading,
      commonSelect,
      PublishRecordModal,
      latestErrorName
    },
    data () {
      return {
        loading: false,
        retryFlag: false, // 重发开关
        retryCause: '', //重发原因
        retryRow: null,
        retryType: '', //重发原因 哪里触发的-- 类型
        params: {
          errorCode: '',
          notificationId: '',
          customerNo: '',
          status: undefined,
          appId: undefined,
          guid: '',
          _pageNo: 1,
          _pageSize: 20,
          spiName: '',
          notifyStartDate: null,
          notifyEndDate: null,
          notifyRule: '',
          url: ''
        },
        totalPageNum: '',
        statusList: [
          {
            label: '成功',
            value: 'SUCCESS'
          },
          {
            label: '失败',
            value: 'FAILED'
          },
          {
            label: '通知中',
            value: 'SENDING'
          }
        ],
        errorCodeList: [],
        columns: [
          {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          {
            // title: '通知订单号',
            // key: 'notificationId',
            // align: 'center'
            renderHeader: (h, params) => {
              return h('div', [h('p', 'GUID'), h('p', '通知订单号')]);
            },
            render: (h, params) => {
              return h('div', [
                h('p', params.row.guid ? params.row.guid : '-'),
                h('p', params.row.notificationId)
              ]);
            },
          },
          {
            renderHeader: (h, params) => {
              return h('div', [h('p', '商户编号'), h('p', '商户应用')]);
            },
            render: (h, params) => {
              return h('div', [
                h('p', params.row.realNotifyMerchantNo),
                h('p', params.row.appId)
              ]);
            },
            align: 'center'
          },
          {
            title: '通知地址',
            key: 'url',
            align: 'center'
          },
          {
            title: '通知规则',
            key: 'realNotifyRule',
            align: 'center'
          },
          {
            renderHeader: (h, params) => {
              return h('div', [h('p', '失败次数'), h('p', '总次数')]);
            },
            render: (h, params) => {
              return h('div', [
                h('p', params.row.failTimes),
                h('p', params.row.notifyTimes)
              ]);
            },
            align: 'center'
          },
          {
            renderHeader: (h, params) => {
              return h('div', [h('p', '上一次失败原因'),h('p', '上一次失败信息'), h('p', '上一次通知时间')]);
            },
            align: 'center',
            render: (h, params) => {
              let _this = this
              return h('div', [
                h('a', 
                {
                on: {
                  click: function(){
                    let resetRow = {
                        errorCode: params.row.latestErrorCode,
                        sendDate: params.row.latestFailDate,
                        errorMsg: params.row.latestErrorMsg,
                      }
                    _this.$refs.latestErrorName.showModal(resetRow)
                  }
                },
                }, params.row.latestErrorName),
                h('p', params.row.latestErrorMsg),
                h('p', params.row.latestFailDate),
              ]);
            }
          },
          {
            renderHeader: (h, params) => {
              return h('div', [h('p', '通知名称'), h('p', '通知编码')]);
            },
            key: 'type',
            align: 'center',
            render: (h, params) => {
              return h('div', [
                h('p', params.row.spiTitle),
                h('p', params.row.spiName)
              ]);
            }
          },
          {
            title: '通知状态',
            key: 'status',
            align: 'center',
            render: (h, params) => {
              let color = 'red';
              let text = '失败'
              if (params.row.status === 'SUCCESS') {
                color = 'green';
                text = '成功'
              } else if (params.row.status === 'PENDING' || params.row.status === 'SENDING') {
                color = 'blue';
                text = '通知中'
              }
              return h('div',
                {
                  style: {
                    position: 'relative',
                    width: '60px',
                    margin: '0 auto'
                  }
                }
                , [
                  h(
                    'Tag',
                    {
                      style: {
                        align: 'center',
                        width: '60px'
                      },
                      props: {
                        color: color
                      }
                    },
                    text
                  ),
                  h('div', {
                    style: {
                      position: 'absolute',
                      top: '4px',
                      left: '68px',
                      cursor: 'pointer'
                    }
                  },
                  [
                    h(
                      'Tooltip',
                      {
                        props: {
                          content: '点击同步通知状态'
                        }
                      },
                      [
                        h('Icon', {
                          props: {
                            type: 'refresh'
                          },
                          directives: [
                            {
                              name: 'show',
                              value:
                                params.row.status === 'PENDING'
                            },
                            {
                              name: 'url',
                              value: { url: '/rest/notify/order/resend' }
                            }
                          ],
                          on: {
                            click: () => {
                              this.reload(params.row);
                            }
                          }
                        })
                      ]
                    )
                  ]
                  )
                ]);
            }
          },
          {
            title: '通知时间',
            key: 'orderDate',
            align: 'center'
          },
          {
            title: '操作',
            align: 'center',
            key: 'operations',
            render: (h, params) => {
              // if (params.row.name2 === '内部请求标识(GUID)') {
              //   let linkCallChain = 'none';
              //   let linkLogCenter = 'none';
              //   if (localStorage.linkLogCenter && localStorage.linkLogCenter !== '' && localStorage.linkLogCenter !== 'undefined') {
              //     linkLogCenter = 'inline-block';
              //   }
              //   if (localStorage.linkCallChain && localStorage.linkCallChain !== '' && localStorage.linkCallChain !== 'undefined') {
              //     linkCallChain = 'inline-block';
              //   }
              // }
              return h('div', [
                h(
                  'Button',
                  {
                    props: {
                      type: 'error',
                      size: 'small'
                    },
                    style: {
                      marginLeft: '10px',
                    },
                    directives: [
                      {
                        name: 'url',
                        value: { url: '/rest/notify/order/resend' }
                      }
                    ],
                    on: {
                      click: () => {
                        this.retry(params.row);
                      }
                    }
                  },
                  '重发通知'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    on: {
                      click: () => {
                        this.publishRecord(params.row);
                      }
                    }
                  },
                  '处理记录'
                ),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    style: {
                      marginLeft: '10px',
                      display: params.row.guid ? 'inline-block' : 'none'
                    },
                    on: {
                      click: () => {
                        this.invoke_analyze(params.row.guid);
                      }
                    }
                  },
                  '调用链分析'
                ),
                h('Button', {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    display: params.row.guid ? 'inline-block' : 'none'
                  },
                  attrs: {
                    id: 'modal_invokeL_btn_3'
                  },
                  on: {
                    click: () => {
                      this.related_log(params.row.guid);
                    }
                  }
                }, '关联日志'),
                h(
                  'Button',
                  {
                    props: {
                      type: 'text',
                      size: 'small'
                    },
                    directives: [
                      {
                        name: 'url',
                        value: { url: '/rest/notify/order/latest-content' }
                      }
                    ],
                    on: {
                      click: () => {
                        this.showResult(params.row);
                      }
                    }
                  },
                  '查看报文'
                ),
              ]);
            }
          }
        ],
        dataList: [],
        pageTotal: 10,
        pageNo: 1,
        multiSelectedData: []
      };
    },
    computed: {
      computedColumns () {
        const btns = this.$store.state.app.buttons || []
        const btn = btns.find(btn => btn.url === '/rest/notify/order/resend')
        if (btn && btn.status === 'ENABLE') {
          return this.columns
        }
        return this.columns.splice(1)
      },
      time: {
        get () {
          if (!this.params.notifyStartDate) {
            return []
          }
          return [this.params.notifyStartDate, this.params.notifyEndDate]
        }
      }
    },
    mounted () {
      const currentDate = util.dateFormat(new Date(), 'DAILY')
      this.params.notifyStartDate = util.dateFormat_component(currentDate)
      this.params.notifyEndDate = util.dateFormat_component_end(currentDate)
      this.getErrorCodeList()
      this.search()
    },
    methods: {
      handleselection (value) {
        this.multiSelectedData = value;
      },
      handleDateChange (date) {
        this.params.notifyStartDate = date[0]
        this.params.notifyEndDate = date[1]
        // 将截止时间改为 23:59:59
        if(date[1].slice(-8) === '00:00:00'){
          date[1] = date[1].slice(0,-8) + '23:59:59'
          this.params.notifyEndDate = date[1]
        }
      },
      disabledDate (current) {
        return current.getTime() > new Date().getTime()
      },
      getErrorCodeList() {
        api.getErrCodeList().then(res => {
          if(res.data.code === '000000') {
            this.errorCodeList = res.data.data;
          }
        })
      },
      // 关联日志跳转
      related_log (guid) {
        window.open(localStorage.linkLogCenter + guid, '_blank')
      },
      // 调用链分析
      invoke_analyze (guid) {
        window.open(localStorage.linkCallChain + guid, '_blank')
      },
      // 展示请求报文
      showResult(row) {
        let params = {
          orderDate: row.orderDate,
          orderId: row.orderId,
        };
        api.getNotifyContent(params).then(res => {
          if (res.data.code === "000000") {
            let result = res.data.data ? res.data.data : {}
            const style = {
              width: '100%',
              height: 'auto',
              overflow: 'auto',
              minHeight: '80px',
            }
            this.$Modal.info({
              okText: '关闭',
              title: '查看报文',
              width: '700',
              render: (h) => {
                return h('div', [
                  h('p', '明文'),
                  h('div', [
                    h('pre',
                      { style },
                      JSON.stringify(result.plaintext || '', null, 4)
                    )
                  ]),
                  h('p', '密文'),
                  h('div', [
                    h('pre',
                      { style },
                      JSON.stringify(result.cliphertext || '', null, 4)
                    )
                  ])
                ])
              }
            })
          } else {
            this.$Message.error(res.data.message)
          }
        })
      },
      // 重发原因
      showCloseRetry(flag) {
        if(flag) {
          if(!this.retryCause) {
            this.$Message.warning('请输入原因！')
            return
          }
          // 多个重发原因 通过类型区分
          // 列表操作 重发
          if(this.retryType === 'tableEditType') {
            this.$Modal.confirm({
              title: '提示',
              content: '确认重发通知吗？',
              'ok-text': '确认',
              onOk: () => {
                api.resend([
                  {
                    orderId: this.retryRow.orderId,
                    orderDate: this.retryRow.orderDate,
                    operCause: this.retryCause
                  }
                ]).then(response => {
                  if (response.code === '000000') {
                    this.$Message.success('异步通知重发中，请耐心等待...')
                  } else {
                    this.$Message.error('重发失败')
                  }
                })
                .finally(() => {
                  this.clearCause()
                });
              }
            });
          }
          // 查询条件 批量重发
          if(this.retryType === 'searchType') {
            this.batchRetry()
          }
          // 勾选批量重发
          if(this.retryType === 'selectType') {
            this.batchRetrySelect()
          }
        } else {
          this.clearCause()
        }
      },
      retry (row) {
        this.retryCause = ''
        this.retryType = 'tableEditType'
        this.retryFlag = true;
        this.retryRow = row
      },
      // 关闭清空原因
      clearCause() {
        this.retryCause = ''
        this.retryFlag = false;
      },
      // 条件查询打开开关
      batchRetryFlag() {
        this.retryCause = ''
        this.retryFlag = true
        this.retryType = 'searchType'
      },
      batchRetry () {
        let that = this;
        this.$Modal.confirm({
          title: '确认重发？',
          content: '确认后将根据查询条件重发所有异步通知',
          onOk: () => {
            let params = {};
            Object.assign(params, that.params);
            params.operCause = that.retryCause
            api.resendAll(params).then(res => {
              if (res.message === 'SUCCESS') {
                this.$Message.success('异步通知重发中，请耐心等待...')
              } else {
                this.$Message.error('重发失败')
              }
            })
            .finally(() => {
              this.clearCause()
            });
          },
          onCancel: () => {

          }
        });
      },
      batchRetrySelectFlag() {
        this.retryCause = ''
        this.retryFlag = true
        this.retryType = 'selectType'
      },
      batchRetrySelect () {
        const paramsList = this.multiSelectedData.map(item => {
          return {
            orderId: item.orderId,
            orderDate: item.orderDate,
            operCause: this.retryCause
          }
        })
        api.resend(paramsList).then(response => {
          if (response.code === '000000') {
            this.$Message.success('异步通知重发中，请耐心等待...')
            this.cancelSelect()
          } else {
            this.$Message.error('重发失败')
          }
        })
        .finally(() => {
          this.clearCause()
        });
      },
      cancelSelect () {
        this.$refs['table-notify'].selectAll(false)
      },
      publishRecord (row) {
        this.$refs.PublishRecordModal.showModal(row)
      },
      // 数据检索
      search () {
        this.params._pageNo = 1
        this.pageNo = this.params._pageNo
        this.params._pageSize = 20
        this.list_handler(this.params);
      },
      // 检索条件初始化
      reset_search () {
        this.params = {
          errorCode: '',
          notificationId: '',
          customerNo: '',
          status: undefined,
          appId: undefined,
          guid: '',
          _pageNo: 1,
          _pageSize: 20,
          spiName: '',
          notifyStartDate: null,
          notifyEndDate: null
        }
        const currentDate = util.dateFormat(new Date(), 'DAILY')
        this.params.notifyStartDate = util.dateFormat_component(currentDate)
        this.params.notifyEndDate = util.dateFormat_component_end(currentDate)
      },
      // 列表刷新
      page_refresh (val) {
        if (val) {
          this.params._pageNo = val;
          this.pageNo = this.params._pageNo
        }
        this.list_handler(this.params);
      },
      // 列表请求执行函数
      list_handler (params) {
        console.log(this.compareDate, '<---this.compareDate')
        if (!this.compareDate()) {
          this.$Message.error('开始时间和结束时间间隔不大于7天')
          return
        }
        this.loading = true;
        api.getList(params).then(response => {
          if (response.data.code === '000000') {
            this.dataList = response.data.data.items || []
            // this.pageNo = response.data.data.pageNo;
            this.totalPageNum = response.data.data.totalPageNum || ''
            if (this.dataList.length === 20) {
              this.pageTotal = NaN;
            } else {
              // this.pageTotal = 20 * (this.pageNo - 1) + this.dataList.length;
            }
          } else {
            this.$ypMsg.notice_error(
              this,
              '错误',
              response.data.message,
              response.data.solution
            );
          }
          this.loading = false;
        });
      },
      compareDate () {
        const start = new Date(this.params.notifyStartDate.replace(/-/g,'/')).getTime() + 7 * 24 * 3600 * 1000
        const end = new Date(this.params.notifyEndDate.replace(/-/g,'/')).getTime()
        return end < start
      },
      reload (row) {
        const { orderId, orderDate } = row
        api.syncStatus({
          orderId,
          orderDate
        }).then(res => {
          if (res.message === 'SUCCESS') {
            row.status = res.data
            this.$Message.success('通知状态同步完成')
          } else {
            this.$Message.error('同步失败')
          }
        })
      }
    }
  };
</script>

<style scoped>
</style>
