<style lang="less" >
.page-wrapper {
    // 低代码样式覆盖
    .low-code-renderer {
        .ant-input:focus {
            border-color: #5cadff;
            box-shadow: 0 0 0 2px rgba(51,153,255,.2)
        }
        .ant-input:hover {
            border-color: #5cadff;
        }
        .ant-btn-primary {
            background-color: #2d8cf0;
        }
        .ant-btn:hover{
            color: #5cadff;
            background-color: #fff;
            border-color: #5cadff;
        }
        .ant-btn:focus {
            border-color: #d8d8d8;
            color: #000000a6;
        }
        .ant-btn-primary:hover{
            color: #fff;
            background-color: #5cadff;
            border-color: #5cadff;
        }
        .ant-btn-primary:focus {
            background-color: #2d8cf0;
            color: #fff;
        }
        .ant-select-selection:focus {
            box-shadow: 0 0 0 2px rgba(51,153,255,.2)
        }
        // 分页
        .ant-pagination-item-active {
            border-color: #5cadff;
        }
        .ant-pagination-item-active a {
            color: #5cadff;
        }
        .ant-pagination-item:focus a, .ant-pagination-item:hover a {
            color: #5cadff;
        }
        .ant-pagination-item:focus, .ant-pagination-item:hover {
            border-color: #5cadff;
            transition: all .3s;
        }
        .ant-pagination-item-link:hover {
            border-color: #5cadff;
            color: #5cadff;
        }
    }

}
</style>
<template>
    <div class="page-wrapper">
        <div class="page-con" id="pageCon"></div>
    </div>
</template>

<script>
import { fetchSchema } from '~/api/lowcode'
import axios from 'axios'
// const service = axios.create({
//   baseURL: process.env.baseURl,
//   headers: {
//     'Authorization': `Bearer ${localStorage.accesstoken}`
//   }
// })
export default {
    data () {
        return {
            pageCode: 'unionpay_offline_manage_operate',
            schema: {},
        }
    },
    async mounted () {
      this.schema = await fetchSchema('send_record_list')
        const option = {
          root: 'pageCon',
          id: 'send_record_list',
          amisJSON: this.schema,
          inCanvas: false,
          axiosInstance: axios
        }
        const instance1 = LowCodeRenderFn.init()
        instance1.renderDom(option)
    },
    methods: {

    }
}
</script>
