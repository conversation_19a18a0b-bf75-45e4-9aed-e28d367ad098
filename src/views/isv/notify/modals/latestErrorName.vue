<template>
	<Modal id="modal_invokeL_2" v-model="visible" width="90%" :closable="false">
    <p slot="header" style="color:#2d8cf0;">
      <span style="color:black">解决方案</span>
    </p>
    <div>
      <Table id='table_invokeL_5' border ref="selection" :columns="columns_solutionList" :data="data"></Table>
      <Tabs id="modal_invokeL_tab_1" v-model="tabNow_model" >
        <TabPane label="解决方案(对内)" name="solution_in_modal">
            <div v-html="innerSolution"></div>
        </TabPane>
        <TabPane label="解决方案(对外)" name="solution_out_modal">
            <div v-html="outerSolution"></div>
        </TabPane>
      </Tabs>
    </div>
    <div slot="footer">
      <Button id="modal_invokeL_btn_2" type="primary" @click="modal_hide">关闭</Button>
    </div>
    <loading :show="loading"></loading>
  </Modal>
</template>

<script>
import api from '~/api/spi/notify';
import loading from '../../../my-components/loading/loading';
import expandRow from './expandRow'
import util from '~/libs/util'
export default {
  components: {
    loading,
    expandRow
  },
  data () {
    return {
      visible: false,
      loading: false,
      showTable: true,
      tabNow_model: 'solution_in_modal',
      innerSolution: '',
      outerSolution: '',
      columns_solutionList: [
        {
          title: '失败原因',
          align: 'center',
          width: 120,
          key: 'latestErrorName'
        },
        {
          title: '失败信息',
          align: 'center',
          key: 'latestErrorMsg'
        },
        {
          title: '失败时间',
          align: 'center',
          key: 'latestFailDate'
        },
      ],
      params: {
        orderId: '',
        _pageNo: 1,
        _pageSize: 20,
        orderDate: ''
      },
      data: []
    }
  },
  mounted () {
  },
  methods: {
    modal_hide () {
      this.visible = false
    },
    showModal (data, type) {
      let reseObj = {
        errorCode: data.errorCode,
        latestErrorName: '',
        latestErrorMsg: data.errorMsg,
        latestFailDate: data.sendDate,
      }
      this.visible = true
      let params = {
        errorCode: reseObj.errorCode
      }
      this.data = [
        {
          latestErrorName: '',
          latestErrorMsg: reseObj.latestErrorMsg,
          latestFailDate: reseObj.latestFailDate,
        }
      ]
      if(reseObj.errorCode) {
        api.getErrorCodeSolution(params)
        .then(response => {
          if (response.data.code === '000000') {
            this.data[0].latestErrorName = response.data.data.errorName
            this.innerSolution = util.empty_handler2(response.data.data.innerSolution);
            this.outerSolution = util.empty_handler2(response.data.data.outerSolution);
          } else {
            this.$ypMsg.notice_error(
              this,
              '错误',
              response.data.message,
              response.data.solution
            );
          }
        })
        .finally(() => {
          this.loading = false
        })
      }
      
    },
  }
}
</script>

<style>
</style>
