<style lang="less">

</style>
<template>
  <div class="">
    <Table id="table_2" border ref="selection" :columns="columns" :data="row"></Table>
    <latestErrorName ref="latestErrorName" />
  </div>
</template>

<script>
import api from '~/api/spi/notify';
import loading from '../../../my-components/loading/loading';
import expandRow from './expandRow'
import latestErrorName from './latestErrorName'
export default {
  components: {
    loading,
    expandRow,
    latestErrorName
  },
  props: {
    row: Array
  },
  data () {
    return {
      firstRow: [],
      columns: [
        {
          title: '失败原因',
          key: 'errorName',
          align: 'center',
          render: (h, params) => {
            let _this = this
            return h('div', [
              h('a', 
              {
              on: {
                click: function(){
                  let resetRow = {
                    errorCode: params.row.errorCode,
                    sendDate: params.row.sendDate,
                    errorMsg: params.row.errorMsg,
                  }
                  _this.$refs.latestErrorName.showModal(resetRow)
                }
              },
              }, params.row.errorName)
            ]);
            },
          width: 200
        },
        {
          title: '失败信息',
          align: 'center',
          key: 'errorMsg'
        },
        {
          title: '发送时间',
          align: 'center',
          key: 'sendDate'
        },
        {
          title: '状态',
          align: 'center',
          key: 'status',
          render: (h, params) => {
            let color = 'red';
            let text = '失败'
            if (params.row.status === 'SUCCESS') {
              color = 'green';
              text = '成功'
            } else if (params.row.status === 'PENDING' || params.row.status === 'SENDING') {
              color = 'blue';
              text = '通知中'
            }
            return h('div', [
              h(
                'Tag',
                {
                  style: {
                    align: 'center',
                    width: '60px'
                  },
                  props: {
                    color: color
                  }
                },
                text
              )
            ]);
          }
        },
      ],
    }
  },
  mounted () {
    // if(this.row.length > 0) {
      
    // }
  },
  methods: {

  }
}
</script>

<style>
</style>
