<template>
  <div>
    <Modal id="modal_request_1" v-model="visible"  :z-index="999990" :closable="false" width="75%">
      <p slot="header" style="color:#2d8cf0;">
        <span style="color:black">处理记录</span>
      </p>
      <Card dis-hover :bordered="false">
        <Row>
          <Col span="24">
            <Table v-if="showTable" id="table_1" border ref="selection" :columns="columns" :data="data" @on-expand="onExpand" :key="uuidTable"></Table>
            <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
              <Page
                class="margin-top-10"
                style="float: right"
                :total="pageTotal"
                :page-size="20"
                :current="pageNo"
                show-elevator
                @on-change="page_refresh"
              ></Page>
            </Tooltip> -->
          </Col>
          <loading :show="loading"></loading>
        </Row>
      </Card>
      <div slot="footer">
        <Button type="ghost" @click="modal_hide">关闭</Button>
      </div>
    </Modal>
    <latestErrorName ref="latestErrorName" />
  </div>
</template>

<script>
import api from '~/api/spi/notify';
import loading from '../../../my-components/loading/loading';
import expandRow from './expandRow'
import latestErrorName from './latestErrorName'
export default {
  components: {
    loading,
    expandRow,
    latestErrorName
  },
  data () {
    return {
      visible: false,
      loading: false,
      showTable: true,
      uuidTable: 1,
      columns: [
        // 手风琴
        {
          type: 'expand',
          align: 'center',
          render:(h, params) => {
            return h('div', [
              h('h3','发送记录'),
              h(expandRow, {
                props: {
                  row: params.row.items
                },
              })
            ])
          }
        },
        {
          title: '通知订单号',
          key: 'notificationId',
          align: 'center',
          width: 200
        },
        {
          title: '通知接收地址',
          align: 'center',
          width: 180,
          key: 'url'
        },
        {
          title: '发送时间',
          align: 'center',
          width: 120,
          key: 'sendDate'
        },
        {
          title: '操作人',
          align: 'center',
          key: 'operator'
        },
        {
          title: '操作原因',
          align: 'center',
          key: 'operCause'
        },
        {
          title: '来源字段',
          align: 'center',
          key: 'operSource',
          render: (h, params) => {
             if (params.row.operSource) {
               return h('div', params.row.operSource === 'yop-portal' ? '运营后台' : '开发者中心');
             }
           }
        },
        {
          align: 'center',
          title: '',
          renderHeader: (h, params) => {
            var text = '上一次失败原因<br/> 上一次失败时间<br/> 上一次失败信息';
            return h('div', {
              domProps: {
                innerHTML: text
              }
            })
          },
          minWidth: 100,
          render: (h, params) => {
            let _this = this
            return h('div', [
            h(
                'a',
                {
                  directives: [{
                    name: 'show',
                    value: (params.row && params.row.backend && params.row.backend === 'YOP') || !params.row.backend
                  }],
                  on: {
                    click: function(){
                      let resetRow = {
                        errorCode: params.row.errorCode,
                        sendDate: params.row.latestFailDate,
                        errorMsg: params.row.latestFailMsg,
                      }
                      _this.$refs.latestErrorName.showModal(resetRow)
                    }
                  },
                },
                params.row.latestErrorName || ''
              ),
              h(
                'div',
                {
                  directives: [{
                    name: 'show',
                    value: (params.row && params.row.backend && params.row.backend === 'YOP') || !params.row.backend
                  }]
                },
                params.row.latestFailDate || ''
              ),
              h(
                'div',
                {
                  directives: [{
                    name: 'show',
                    value: (params.row && params.row.backend && params.row.backend === 'YOP') || !params.row.backend
                  }]
                },
                params.row.latestFailMsg || ''
              ),
              h(
                'div',
                {
                  directives: [{
                    name: 'show',
                    value: params.row && params.row.backend && params.row.backend === 'STNO'
                  }]
                },
                '请前往boss3g商户通知查看'
              )
            ]);
          }
        },
        {
          align: 'center',
          title: '',
          width: 100,
          renderHeader: (h, params) => {
            var text = '当前状态<br/> 报文';
            return h('div', {
              domProps: {
                innerHTML: text
              }
            })
          },
          // minWidth: 100,
          key: 'status',
          render: (h, params) => {
            let color = 'red';
            let text = '失败'
            if (params.row.status === 'SUCCESS') {
              color = 'green';
              text = '成功'
            } else if (params.row.status === 'PENDING' || params.row.status === 'SENDING') {
              color = 'blue';
              text = '通知中'
            }
            return h('div', [
              h(
                'Tag',
                {
                  style: {
                    align: 'center',
                    width: '60px'
                  },
                  props: {
                    color: color
                  }
                },
                text
              ),
              h(
                'Tooltip',
                {
                  props: {
                    content: '点击同步通知状态'
                  }
                },
                h('Icon', {
                  props: {
                    type: 'refresh'
                  },
                  directives: [
                    {
                      name: 'show',
                      value:
                          params.row.status === 'PENDING'
                    }
                  ],
                  on: {
                    click: () => {
                      this.reload(params.row);
                    }
                  }
                })
              ),
              h('div', [
                h('Button', {
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  style: {
                    marginRight: '5px',
                    color: '#2d8cf0'
                  },
                  on: {
                    click: () => {
                      this.showDetailModal(params.row)
                    }
                  }
                }, '查看')
              ])
            ]);
          }
        }
        // {
        //   title: '错误信息',
        //   align: 'center',
        //   render: (h, params) => {
        //     if (!params.row.errorMsg) {
        //       return h('span', params.row.errorCode)
        //     }
        //     return h('p', [
        //       h('span', params.row.errorCode),
        //       h('span', `（${params.row.errorMsg}）`)
        //     ])
        //   }
        // },
      ],
      params: {
        orderId: '',
        _pageNo: 1,
        _pageSize: 20,
        orderDate: ''
      },
      sendRecordparam: {
        orderId: '',
        _pageNo: 1,
        _pageSize: 20,
        orderDate: '',
        notifyRecordId: ''
      },
      pageTotal: 10,
      pageNo: 1,
      data: []
    }
  },
  mounted () {
  },
  methods: {
    modal_hide () {
      this.visible = false
    },
    page_refresh (val) {
      if (val) {
        this.params._pageNo = val
      }
      this.getList();
    },
    getList () {
      this.loading = true
      api.getRecordList(this.params)
        .then(response => {
          if (response.data.code === '000000') {
            this.data = response.data.data.items
            // this.data.push(response.data.data.items[0],response.data.data.items[0])
            this.data.forEach((item,i) => {
              item._expanded = false
              item.items = []
            })
            // 第一条展示 发送记录
            this.pageNo = response.data.data.pageNo;
            if (this.data.length === 20) {
              this.pageTotal = NaN;
            } else {
              this.pageTotal = 20 * (this.pageNo - 1) + this.data.length;
            }
            // 获取当前第0条 发送记录
            this.showTable = false
            this.uuidTable += this.uuidTable
            this.getSendRecord(response.data.data.items[0].id, 0, true)
          } else {
            this.$ypMsg.notice_error(
              this,
              '错误',
              response.data.message,
              response.data.solution
            );
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 获取发送记录
    getSendRecord(id,index,status) {
      if(status) {
        this.sendRecordparam.notifyRecordId = id
        api.getSendRecordList(this.sendRecordparam)
        .then(response => {
          if (response.data.code === '000000') {
            this.data[index].items = []
            // this.data[index].items.push(response.data.data.items[0] ? response.data.data.items[0] : {})
            this.data[index].items = response.data.data.items
            this.data.forEach((item) => {
              item._expanded = false
            })
            this.data[index]._expanded = true
          }
        })
        .finally(() => {
          this.showTable = true
          this.uuidTable += this.uuidTable
        })
      }
      
    },
    // 展示发送记录  手风琴效果
    onExpand(row, status){
      this.data.map((item,index)=>{
        if(item.id == row.id){
          item._expanded = true;   //展开选中的行 
          this.getSendRecord(row.id,index,status)
        } else {
          // item._expanded = false;   //其他行关闭
        }
        return item;
      });
    },
    showModal (data) {
      this.params = {
        orderId: data.orderId,
        _pageNo: 1,
        _pageSize: 20,
        orderDate: data.orderDate
      }
      this.sendRecordparam = {
        _pageNo: 1,
        _pageSize: 20,
        orderDate: data.orderDate,
        notifyRecordId: '',
      }
      this.getList()
      this.visible = true
    },
    showDetailModal (data) {
      this.$Modal.info({
        okText: '关闭',
        width: '550',
        render: (h) => {
          const _json = data.content ? JSON.parse(data.content) : ""
          let _jsonData = ''
          if(_json) {
             _jsonData = _json.data && _json.data.data ? _json.data.data : ''
          } else {
            _jsonData = ''
          }
          if (_json && _jsonData) {
            _json.data.data = _jsonData
          }
          const data1 = JSON.stringify(_json, null, 4)
          return h('div', [
            h('h2', '查看报文'),
            h('div', [
              h('pre',
                {
                  style: {
                    width: '100%',
                    height: '260px',
                    overflow: 'auto'
                  }
                },
                data1
              )
            ])
          ])
        }
      })
    }
  }
}
</script>

<style>
</style>
