<template>
<div>

    <Modal id="modal_request_1" v-model="modal_show" :closable="false" width="75%" >
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">变更记录</span>
        </p>
        <Card dis-hover :bordered="false">
            <Row type="flex" >
                <Col span="20">
                <Col span="7" >
                    <Col span="7" class="margin-top-10">
                        <span >通知编码:</span>
                    </Col>
                    <Col span="17">
                        <Input id='modal_record_4' class="margin-top-5" clearable v-model="data_name" placeholder="请输入通知编码" @on-enter="search"></Input>
                    </Col>
                </Col>
                <Col offset="1" span="7" >

                <Col span="7" class="margin-top-10">
                <span >API分组:</span>
                </Col>
                <Col span="17" >
                <Select ref="select_apiM_1" id='select_apiM_1' class="margin-top-5" v-model="data_select_apiGroup" filterable clearable placeholder="请选择（默认全部）">
                    <Option v-for="item in data_apiGroup_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                   
                </Col>
                <Col offset="1" span="7" >
                    <Col span="7" class="margin-top-10">
                        <span >操作类型:</span>
                        </Col>
                        <Col span="17" >
                        <common-select ref="select_spi_opType" @on-update="updateSelect_spi_opType"
                                       type="normal"
                                       keyWord="result"
                                       holder="请选择（默认全部）"
                                       code="code"
                                       title="name"
                                       group="spi_opType"
                                       @on-loaded="select_callBack"
                                       :default="this.data_select_spi_opType"
                                       :uri="this.$store.state.select.spi_opType.uri"></common-select>
                        </Col>
                </Col>
                <Col span="7" >
                    <Col span="7" class="margin-top-10">
                    <span >操作人:</span>
                    </Col>
                    <Col span="17">
                    <Input id='modal_record_1' class="margin-top-5" clearable v-model="data_operator" placeholder="操作人" @on-enter="search"></Input>
                    </Col>
                </Col>
                <Col offset="1" span="7" >
                     <Col span="7" class="margin-top-10">
                        <span >操作时间:</span>
                    </Col >
                        <date-picker ref="datepicker_record" @on-start-change="update_start_date"
                                     @on-end-change="update_end_date"
                                     :default_start="this.dateStart"
                                     :default_end="this.dateEnd"
                        ></date-picker>
                </Col>
                </Col>
                
                <Col span="4">
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button id='modal_btn_opt_1' type="primary"   @click="search">查询</Button>
                </Col>
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button id='modal_btn_opt_2' type="ghost" @click="reset_search">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Table id='table_1' border ref="selection" :columns="columns_opt_recordList" :data="data_opt_recordList" ></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="pageSize" :current="pageNo" show-elevator @on-change="page_refresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_optRecordList"></loading>
            </Row>
        </Card>
        <div slot="footer">
            <Button type="ghost" @click="modal_hide">关闭</Button>
        </div>
        
    </Modal>
    <Modal v-model="rollback_modal_show" width="500">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;">
            <Icon type="ios-information-circle"></Icon>
            <span>SPI回滚</span>   
        </p>
        <div style="text-align:left;margin-left:20px;   font-size: 12px;">
                <p style="color:red">回滚操作会覆盖现有的SPI的内容</p>
            <label for="" class="reasonType"><span style="color:red">*</span>原因：</label>
            <Input type="textarea" v-model="rollback_cause" style="width:85%;font-size: 12x;" placeholder="请输入回滚原因"></Input>
        </div>
        <div slot="footer">
            <Button type="primary"  @click="sure_rollback_model">确定</Button>
            <Button type="primary"  @click="hide_rollback_model">取消</Button>
        </div>
    </Modal>
    <Modal v-model="change_record_show" width="800">
        <p slot="header" style="color:#000;text-align:left;font-size: 18px;">
                <Icon type="ios-information-circle"></Icon>
                <span>详情</span>   
            </p>
        <spis_to_create ref="spisToCreate"></spis_to_create>
        <div slot="footer">
                <Button type="ghost" @click="hide_change_record">关闭</Button>
        </div>
    </Modal>


</div>

</template>

<script>
    import loading from '../../my-components/loading/loading'
    import commonSelect from '../../common-components/select-components/selectCommon'
    import util from '../../../libs/util'
    import api from '../../../api/api'
    import datePicker from '../../common-components/date-components/date-picker'
    import spis_to_create from './spis_to_create'
    export default {
        name: 'modal_change_record',
        components:{
            loading,
            commonSelect,
            datePicker,
            spis_to_create
        },
        data () {
            return {
                pageSize:10,
                change_record_show:false,
                rollback_cause:"",
                rollbackId:"",
                rollback_modal_show:false,
                data_select_apiGroup:"",
                data_apiGroup_List:[],
                selectChangeType:"",
                select_change_type_list:[],
                // 弹窗显示
                modal_show: false,
                // 搜索时间框-创建时间开始时间数据绑定
                dateStart: '',
                // 搜索时间框-创建时间结束时间数据绑定
                dateEnd: '',
                // 应用标识
                data_name: '',
                // 操作类型
                data_select_spi_opType : '',
                // 操作人
                data_operator : '',
                // loading
                show_loading_optRecordList: false,
                // 当前密钥id
                current_certId : '',
                // 总数
                pageTotal : 10,
                // 当前页码
                pageNo : 1,
                // 表样式
                columns_opt_recordList : [
                    
                    {
                        title: '通知编码',
                        key: 'name',
                        align: 'center'
                    },
                    {
                        title: 'API分组',
                        key: 'apiGroup',
                        align: 'center'
                    },
                    
                    {
                        title: '操作类型',
                        key: 'operateType',
                        align: 'center',
                        render: (h,params) => {
                            var operateType = ""
                            if(params.row.operateType == "IMPORT"){
                            operateType = "导入"
                            }
                            if(params.row.operateType == "DELETE"){
                            operateType = "删除"
                            }
                            if(params.row.operateType == "CREATE"){
                            operateType = "创建"
                            }
                            if(params.row.operateType == "ROLLBACK"){
                            operateType = "回滚"
                            }
                            if(params.row.operateType == "UPDATE"){
                            operateType = "编辑"
                            }
                            if(params.row.operateType == "ENABLE"){
                            operateType = "启用"
                            }
                            if(params.row.operateType == "DISABLE"){
                            operateType = "禁用"
                            }
                            return h('div',operateType)
                        }
                    },
                    {
                        title: '操作人',
                        key: 'operator',
                        align: 'center'
                    },
                    // {
                    //     title: '变更原因',
                    //     key: 'cause',
                    //     align: 'center'
                    // },
                    {
                        title: '操作时间',
                        key: 'operateDate',
                        align: 'center'
                    },
                    {
                        title: '操作',
                        key: 'operations',
                        align:"center",
                        width: 230,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/spi/change-record/detail'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.interface_des(params.row)
                                        }
                                    }
                                }, '详情'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/spi/change-record/rollback'}
                                    },{
                                        name: 'show',
                                        value: params.row.operateType == "DELETE"||params.row.operateType == "UPDATE"
                                    }],
                                    on: {
                                        click: () => {
                                            this.rollback(params.row)
                                        }
                                    }
                                }, '回滚')

                            ]);
                        }
                    }
                ],
                // 表数据
                data_opt_recordList : [
                ]
            };
        },
        methods: {
            // 开始日期更新
            update_start_date (val) {
                this.dateStart = val;
            },
            // 结束日期更新
            update_end_date (val) {
                this.dateEnd = val;
            },
            // 操作类型数据更新
            updateSelect_spi_opType (val) {
                this.data_select_spi_opType = val;
            },
            //
            // 下拉框加载完处理函数
            select_callBack () {

            },
            // 数据检索
            search (){
                let params = this.search_pre_option();
                this.list_handler(params);
                this.get_change_type_list();
                this.get_apiGroup_list();
            },
            get_change_type_list(){
                api.yop_spiManagement_spi_change_optype().then(
                    (response) => {
                        console.log(response)
                        if (response.data.status === 'success') {
                            this.select_change_type_list = response.data.data.result
                            console.log(this.select_change_type_list);
                        } else {
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                    }    
                )
            },
            get_apiGroup_list(){
                // api分组列表
                api.yop_dashboard_apis_list().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.data_apiGroup_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].apiGroupCode,
                                label: resultTemp[i].apiGroupCode+'('+resultTemp[i].apiGroupName+')',
                                name: resultTemp[i].apiGroupName
                            })
                        }
                        this.data_apiGroup_List = dataTemp;
                        this.data_select_apiGroup = this.data_select_apiGroup;
                    });
                
            },
            
            // 检索条件初始化
            reset_search () {
                this.$refs.datepicker_record.reset();
                this.$refs.select_apiM_1.clearSingleSelect();
                this.$refs.select_spi_opType.resetSelected();
                this.dateStart = '';
                this.dateEnd = '';
                this.data_operator = '';
                this.current_certId = '';
                this.data_name = '';
            },
            // 请求参数生成前序操作
            search_pre_option (){
                this.show_loading_optRecordList = true;
                let params = {
                    name: this.data_name,
                    apiGroup: this.data_select_apiGroup,
                    operator: this.data_operator.trim(),
                    opType: this.data_select_spi_opType,
                    operatedStartDate: util.dateFormat_component(this.dateStart),
                    operatedEndDate: util.dateFormat_component_end(this.dateEnd),
                    pageNo: 1,
                    pageSize:10

                };
                util.paramFormat(params);
                return params;
            },
            // 列表刷新
            page_refresh (val) {
                let params = this.search_pre_option();
                if (val) {
                    params.pageNo = val;
                }
                this.list_handler(params);
            },
            // 列表请求执行函数
            list_handler (params) {
                api.yop_spiManagement_spi_change_record(params).then(
                    (response) => {
                        console.log(response)
                        var response = response.data
                        if (response.status === 'success') {
                            this.tableDataFormat(response.data.page.items);
                            this.pageNo = response.data.page.pageNo;
                            if(response.data.page.items){
                                if(response.data.page.items.length < 10){
                                    this.pageTotal=response.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }

                        } else {
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                        this.show_loading_optRecordList = false;
                    }
                );
            },
            // 表格数据处理函数
            tableDataFormat (items) {
                this.data_opt_recordList = [];
                for (var i in items) {
                    this.data_opt_recordList.push({
                        name : util.empty_handler(items[i].name),
                        apiGroup : util.empty_handler(items[i].apiGroup),
                        id : util.empty_handler(items[i].id),
                        operateDate : util.empty_handler(items[i].createdDateTime),
                        operateType : util.empty_handler(items[i].opType),
                        operator : util.empty_handler(items[i].operator),
                        cause : util.empty_handler(items[i].cause),
                    });
                }
            },
            // 详细信息处理
            detail_handler (val) {
                return val.replace(/\n/g,'<br/>')
            },
            // 设置当前certId
            set_certId (id){
                this.current_certId = id;
            },
            //设置当前appId
            set_appId (data_select_apiGroup){
                this.data_select_apiGroup = data_select_apiGroup;
            },
            set_appName (data_name){
                this.data_name = data_name;
            },
            // 密钥管理变更记录窗口显示
            modal_preview () {
                this.modal_show = true;
            },
            // 密钥管理变更记录窗口隐藏
            modal_hide () {
                this.modal_show =false;
                this.reset_search();
            },
            // 返回相应编码的名字
            data_name_handler (code, name) {
                if (code) {
                    if(this.$store.state.select[name].data && this.$store.state.select[name].data.length > 0){
                        let group = this.$store.state.select[name].data;
                        for (var i in group) {
                            if (group[i].value === code) {
                                return group[i].name;
                            }
                        }
                    }else{
                        return code
                    }
                } else {
                    return '';
                }
            },
            interface_des(row){
                var id = row.id
                var operateDate = row.operateDate
                var self = this                
                api.yop_spiManagement_spi_change_record_detail({id:id}).then(
                    (response) => {
                        var response = response.data
                        if(response.status == "success"){
                            var result = response.data.result
                            result.operateDate = operateDate
                            self.$refs.spisToCreate.setResult({
                              ...result,
                              ...result.content
                            });
                            self.change_record_show = true;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.message
                            });
                        }
                    }
                )
            },
            hide_change_record(){
                            this.change_record_show = false ;

            },
                 // 回滚
            rollback(row){
                var id = row.id
                console.log(row)
                this.rollback_cause = ""
                var operateType = row.operateType
                if(operateType == "IMPORT" || operateType == "CREATE" ){
                    this.$Modal.error({
                        title: '错误',
                        content: "创建操作不允许回滚"
                    });
                }else{
                     this.rollbackId = id
                    this.rollback_modal_show =  true
                }
            },
            sure_rollback_model(){
            if(this.rollback_cause){
                var param = {
                    id :this.rollbackId,
                    cause :this.rollback_cause,
                }
                
                api.yop_spiManagement_spi_change_record_rollback(param).then(
                        (response) => {
                            if (response.status === 'success') {
                                this.$ypMsg.notice_success(this,'回滚成功');
                                this.search()
                                // this.tabledataGet(response.data.page.items);
                                // this.pageNo = response.data.result.pageNo;
                                // this.pageTotal = response.data.result.totalPageNum * 10;
                                this.rollback_modal_show = false;
                            } else {
                                this.$Modal.error({
                                    title: '错误',
                                    content: response.message
                                });
                            }

                        }
                    );
            }else{
                this.$Modal.warning({
                    title: "回滚",
                    content: "请输入原因"
                });
            }
            },
            hide_rollback_model(){
                this.rollback_modal_show = false;
            },
        }
    };
</script>

<style scoped>
.reasonType {
    vertical-align: top;
    display: inline-block;
    font-size: 14px;
    margin-top: 5px;
}
</style>
