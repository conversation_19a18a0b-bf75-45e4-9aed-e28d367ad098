<style lang="less">
    @import '../../../styles/common.less';
    @import '../../../styles/loading.less';
    @import '../api_Mangement_Open.less';
    @import '../../regression-test/regression.less';
    /*html,body{*/
         /*width: 100%;*/
         /*height: 100%;*/
         /*overflow: scroll;*/
     /*}*/
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab{
        border-radius: 0;
        background: #fff;
    }
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active{
        // border-top: 1px solid #3399ff;
    }
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active:before{
        content: '';
        display: block;
        width: 100%;
        height: 1px;
        background: #3399ff;
        position: absolute;
        top: 0;
        left: 0;
    }
    .ivu-modal-footer {
        border:0!important;
    }
</style>
<template>
    <div>
        
        <Col>
                    <p>基本信息</p>
                    <Row v-show="basicShow">
                        <Col >
                                <Form ref="form_data_basics"  :model="form_data_basics" :label-width="120" inline>
                                <FormItem  class="width-100-perc" label="通知编码：" prop="name" >
                                    <p>{{form_data_basics.name}}</p>
                                </FormItem>
                                <FormItem  class="width-100-perc" label="SPI标题：" prop="title">
                                    <p>{{form_data_basics.title}}</p>
                                </FormItem>
                                <Row>
                                <FormItem label="SPI类型：" prop="spiType">
                                    <p >{{form_data_basics.spiType === "CALLBACK"?"回调":"通知"}}</p>
                                </FormItem>
                                </Row>
                                <Row>

                                    <FormItem  class="width-100-perc" label="API分组：" prop="apiGroup">
                                    <p >{{form_data_basics.apiGroup}}</p>
                                </FormItem>
                                </Row>
                                <FormItem  class="width-100-perc" label="描述：" prop="description">
                                    <p>{{form_data_basics.description}}</p>
                                </FormItem>
                                
                            </Form>
                        </Col>
                    </Row>
                    <p>参数</p>
                    <Form ref="form_data"  :model="form_data" :label-width="120" inline >

                        <Col span="24">
                        <Row>
                            <FormItem label="请求url：" class="width-50-perc" prop="requestUrl">
                                <p>{{form_data.requestUrl}}</p>
                            </FormItem>
                        </Row>
                        <Row>
                            <FormItem label="http方法：" class="width-50-perc" prop="httpMethod">
                                <p>{{form_data.httpMethod}}</p>
                            </FormItem>
                        </Row>
                        <Row>
                        <p style="margin-left:27px;margin-bottom:20px;">请求体：</p>
                            <FormItem label="models：" class="width-50-perc" prop="model">
                                <p>{{form_data.model}}</p>
                            </FormItem>
                        
                            <FormItem  class="width-100-perc" label="contentType：" prop="contentType">
                                <p>application/json</p>
                            </FormItem>
                            <FormItem  class="width-100-perc" label="描述：" prop='description'>
                                <p >{{form_data.description}}</p>
                            </FormItem>
                        </Row>
                        </Col>
                    </Form>
                    <Col span="24" style="margin-top: 20px;" class="tabs-style2">
                            <!-- <p style="margin-left:27px;margin-bottom:20px;">example:</p> -->
                                <Table id='table_1' border ref="selection" :columns="spi_example_List_display" :data="data_spi_example_List"></Table>
                            <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                                <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                            </Tooltip> -->
                    </Col>

                     <Row style="padding:20px;line-height:40px;">
                        <Col span="24" style="margin-top: 20px;" class="tabs-style2">
                            <p v-if="operator_back">操作人：{{operator_back}}</p>
                            <p v-if="operationType_back">操作类型：{{operationType_back}}</p>
                            <p v-if="operateDate">操作时间：{{operateDate}}</p>
                            <p v-if="cause">操作原因：{{cause}}</p>
                        </Col>
                    </Row>

        </Col>
            
        <Modal v-model="modal_Show_example" width="600" :closable="false" :mask-closable="false">
            <p slot="header">
                <span  class="margin-right-10">新增example</span> 
            </p>
            <div style="padding-left:30px;">
                <Form ref="form_data_example"  :model="form_data_example" :label-width="120" inline>
                    <FormItem  class="width-100-perc" label="名称：" prop="name" :rules="{required:true,message:'name不能为空'}">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.name" placeholder="请输入"></Input>

                    </FormItem>
                    <!-- <FormItem  class="width-100-perc" label="标题：" prop="title" :rules="{required:true,message:'title不能为空'}">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.title" placeholder="请输入"></Input>
                    </FormItem> -->
                    <FormItem  class="width-100-perc" label="描述：" prop="description" :rules="{required:true,message:'description不能为空'}">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.description" placeholder="请输入"></Input>
                    </FormItem>
                    <FormItem  class="width-100-perc" label="示例值：" prop="value" :rules="{required:true,message:'value不能为空'}">
                        <Input size="small" style="width: 380px;" v-model="form_data_example.value" placeholder="请输入"></Input>
                    </FormItem>
                </Form>
            </div>
            <div slot="footer">
                <Button type="primary" @click="ok_example">确定</Button>
                <Button type="ghost"  @click="cancel_example">取消</Button>
            </div>
           
        </Modal>
    </div>
</template>

<script>
    import api from '../../../api/api.js';
    import util from '../../../libs/util.js';
    import loading from '../../my-components/loading/loading'
    export default {
        name: 'api_basics',
        components :{
            loading,
        },
        data () {
           return {
    

    // 参数
    cause:"",
    operateDate:"",
    operationType_back:"",
    operator_back:"",
    showReturn:true,
    requestUrl:"#app.callbackUrl",
    httpMethod:"POST",
    form_data:{
        requestUrl:"#app.callbackUrl",
        httpMethod:"POST",
        description:"",
        model:"application/json"
    },
    contentType_list:[{value:"application/json",label:"application/json"}],
    data_spi_example_List:[
    ],
  
    spi_example_List_display:[
        {
            title: '名称',
            key: 'name',
            align:"center"
        },
        // {
        //     title: '标题',
        //     key: 'title',
        //     align:"center"

        // },
        {
            title: '描述',
            align:"center",
            key: 'description'
        },
        {
            title: '示例值',
            align:"center",
            key: 'value'
        },
    ],
    modal_Show_example:false,
    form_data_example:{
        name:"",
        // title:"",
        description:"",
        value:"",
    },
    form_data_basics:{
        name:"",
        title:"",
        spiType:"CALLBACK",
        apiGroup:"",
        description:"",
    },
    data_apiGroup_List:[],  
               /**
                * 基础界面变量
                */
               // api 请求方式数据绑定
               data_select_apiMethod : '',
               // api 请求方式下拉框总体数据
               data_apiMethod_list : [],
               // 当前参数tab页
               current_param_tab : 'request_params',
               // api contentType数据绑定
               data_select_apiContent : [],
               // api contentType下拉框总体数据
               data_apiContent_list : [],
               // 基础信息加载数据绑定
               show_loading_basic : false,
               // 显示收起内容
               basicShow : true,
               // 展开收起按钮文字内容绑定
               button_Content : '收起',
               // api分组数据绑定
               api_group_model : '',
               // api分组disabled
               api_group_disabled : false,
               // method post disabled
               request_post_disabled : false,
               // method get disabled
               request_get_disabled : false,
               // 接口uri disabled
               interface_uri_disabled : false,
               // api分组数据列表
               api_group_list : [],
               // api类型选择数据绑定
               data_select_apiType: '',
               // api类型
               data_apiType_List:[],
               // 请求方式绑定第一个参数是post第二个参数是get
               request_post : [false,false],
               // 当前tab页绑定的参数
               tabNow: 'basic',
               // 保存的数据参数
           
               // 接口uri数据绑定
               interface_uri: '',
               // 接口名称数据绑定
               interface_name: '',
               // 当前端点apiUri内容
               current_content_apiUri : '',
               // 基本类型
               cascader_type:[],
               // 当前apiId
               current_spiId : '',
               current_spiVersion:"",
               // 创建还是修改
               current_status: '',
                //
                check_result: true,
               /**
                * 新建弹窗变量
                */
               // 窗口显示绑定
               modal_Show_register : false,
               // 描述禁用
               disabled_description: false,
               // 端点url数据绑定
               endpoint_Url : '',
               // 显示端点url部分
               show_pointUrl : false,
               // 端点类名数据绑定
               endpoint_Name: '',
               // 是否幂等数据绑定
               idempotent: '否',
               // 端点url自定义
               pointUrl_user_defined: false,
               // 适配类型下拉框数据绑定
               data_select_type: 'TRANSFORM',
               // 适配类型下拉框数据
               data_type_List: [
                   {
                       value: 'TRANSFORM',
                       label: '转换'
                   },
                   {
                       value: 'LOCAL',
                       label: '本地'
                   },
                   {
                       value: 'PASSTHROUGH',
                       label: '透传'
                   }
               ],
               // 端点协议下拉框数据绑定
               data_select_EndpointProtocol: 'HESSIAN',
               // 端点协议下拉框数据
               data_EndpointProtocol_List: [
                   {
                       value: 'HESSIAN',
                       label: 'hessian'
                   }
               ],
               // 端点方法下拉框数据绑定
               data_select_EndpointMethod: '',
               // 端点方法下拉框数据
               data_EndpointMethod_List: [],
               // 当前端点名称内容
               current_content : '',
               // 当前端点url内容
               current_content_url : '',
               // url校验结果
               pass_url : true,
               data_api_model_list:[],
               editExample:false,
               row_index:0,
               result:{}
           }
        },
        methods : {
            /**
             * 基础界面方法
             */
            // 初始化函数
            setResult(val){
                console.log(val, 'val-----')
                this.init();
                this.operator_back = val.operator
                var operationType = ""
                if(val.operationType == "IMPORT"){
                operationType = "导入"
                }
                if(val.operationType == "DELETE"){
                operationType = "删除"
                }
                if(val.operationType == "CREATE"){
                operationType = "创建"
                }
                if(val.operationType == "ROLLBACK"){
                operationType = "回滚"
                }
                if(val.operationType == "UPDATE"){
                operationType = "更新"
                }
                this.operationType_back = operationType
                this.operateDate = val.operateDate;
                this.cause = val.cause || ""
                this.form_data_basics.name = val.spiName
                this.form_data_basics.apiGroup = val.apiGroup
                if(val){
                    this.form_data_basics = val.basic
                    this.form_data = val.request
                    this.form_data.description = val.request.requestBody.description;
                    var schema = JSON.parse(val.request.requestBody.contents["application/json"].schema)
                    this.form_data.model = schema["$ref"].split("/").pop();
                    this.data_spi_example_List = val.request.requestBody.contents["application/json"].examples || []
                }
                

            },
            init () {
                    this.form_data_basics = {
                        name:"",
                        title:"",
                        spiType:"CALLBACK",
                        apiGroup:"",
                        description:"",
                    }
                    this.form_data = {
                        requestUrl:"#app.callbackUrl",
                        httpMethod:"POST",
                        description:"",
                        model:"application/json"
                    }
                    this.data_spi_example_List = []  
            },
           
         

        
            ok_example(){
                this.$refs.form_data_example.validate((valid) => {
                    console.log(valid)
                    if (valid) {
                        var example = {
                                name:this.form_data_example.name,
                                // title:this.form_data_example.title,
                                description :this.form_data_example.description,
                                value:this.form_data_example.value,
                            }
                        if(!this.editExample){
                            this.data_spi_example_List.push(example)
                        }else{
                            this.data_spi_example_List.splice(this.row_index,1,example)
                        }
                        this.modal_Show_example = false;
                    }else {
                        this.form_validate_failed()
                    }
                })

            },
            form_validate_failed(){

            },
            cancel_example(){
                this.modal_Show_example = false;

            },
        },
        beforeRouteLeave (to, from, next) {
            this.$destroy();
            next();
        },
        beforeDestroy () {
            // console.log(this.$refs.te);
            // this.$refs.te.destroySelf();
        },
        destroyed () {
            // this.$store.commit('closePage', 'api_basics');
        }
    };

</script>

<style scoped>
    /*.ivu-collapse {*/
        /*background-color : #FFFFFF;*/
    /*}*/
</style>
