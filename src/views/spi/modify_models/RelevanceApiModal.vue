<template>
	<div>
		<Modal v-model="show" width="1000">
			<p slot="header" style="color: #2d8cf0">
				<span style="color: black">已关联API</span>
			</p>
			<Card dis-hover :bordered="false">
				<Row type="flex" align="middle">
					<Col span="24">
						<Col span="8">
							<Col span="6" class="margin-top-5">
								<span>API名称:</span>
							</Col>
							<Col span="18">
								<Input clearable v-model="params.apiName" id="inputApiName" placeholder="API名称"></Input>
							</Col>
						</Col>
						<Col offset="1" span="7">
							<Col span="6" class="margin-top-5">
								<span>API URL:</span>
							</Col>
							<Col span="18">
								<Input clearable v-model="params.apiUri" id="inputApiUrl" placeholder="API URL"></Input>
							</Col>
						</Col>
						<Col span="7" offset="1">
							<Col span="6" class="margin-top-5">
								<span>API版本:</span>
							</Col>
							<Col span="18">
								<Select id="select_apiM_version" v-model="params.apiVersion" placeholder="请选择">
									<Option value="V1" key="V1">旧版API</Option>
									<Option value="V2" key="V2">新版API</Option>
								</Select>
							</Col>
						</Col>
					</Col>
				</Row>
				<Row type="flex" justify="start" class="margin-top-20">
					<Col span="12">
						<Button id="batch-btn-cancel"  type="ghost" @click="batchUncontactApi">批量取消</Button>
						<Button id="adpp-api-btn" type="primary" @click="addApi">添加关联API</Button>
					</Col>
					<Col span="12" type="flex" align="end">
						<Button id="relve-btnSearch" type="primary" @click="getList">查询</Button>
						<Button id="relve-btnReset" type="ghost" @click="reset_Interface">重置</Button>
					</Col>
				</Row>
				<Row class="margin-top-20">
					<Table
						id="table-relve"
						border
						ref="singleTable"
						:columns="columns"
						:data="data"
						@on-selection-change="handleTableSelection"
						row-key="id"
					></Table>
					<Loading :show="loading"></Loading>
				</Row>
				<Row class="margin-top-20" type="flex" justify="end">
					<Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
						<Page :total="total" :current="params._pageNo" show-elevator @on-change="onPageChange" />
					</Tooltip>
				</Row>
			</Card>
			<div slot="footer">
				<Button type="ghost" id="relveCancel" @click="closeModal">关闭</Button>
			</div>
		</Modal>
		<SelectApiModal ref="SelectApiModal" @confirm="selectApiConfirm" />
	</div>
</template>
<script>
import Loading from '~/views/my-components/loading/loading';
import SelectApiModal from '~/views/common-components/SelectApiModal';
import Api from '~/api/spi'
export default {
  components: {
    Loading,
    SelectApiModal
  },
  data () {
    return {
      show: false,
      loading: false,
      total: NaN,
      params: {
        spiName: '',
        apiName: '',
        apiUri: '',
        apiVersion: 'V1',
        _pageNo: 1,
        _pageSize: 20
      },
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '已关联API',
          key: 'title',
          render: (h, params) => {
            return h('div', [
              h('p', params.row.title),
              h('p', params.row.uri)]
            );
          }
        },
        {
          title: 'API版本',
          width: 150,
          render: (h, params) => {
            return h('div', params.row.version === 'V1' ? '旧版' : '新版');
          }
        },
        {
          title: '操作',
          width: 100,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.uncontactApi([params.row.apiId]);
                  }
                }
              }, '取消关联')
            ])
          }
        }
      ],
      data: [],
      spiName: '',
      selectApiIds: []
    }
  },
  methods: {
    closeModal () {
      this.show = false
    },
    showModal (spiName) {
      this.spiName = spiName
      this.params.spiName = spiName
      this.getList()
      this.show = true
    },
    addApi () {
      this.$refs.SelectApiModal.show_model({
        joinCode: 'SPINAME',
        joinValue: this.spiName
      })
    },
    getList () {
      this.loading = true
      Api.getList(this.params)
        .then(res => {
          if (res.data.status === 'success') {
            this.data = res.data.data.data
            if (this.data.length < 20) {
              this.total = NaN
            } else {
              this.total = 20
            }
          } else {
            this.$Modal.error({
              title: '错误',
              content: res.message
            });
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    reset_Interface () {
      this.params = {
        spiName: this.spiName,
        apiName: '',
        apiUri: '',
        apiVersion: '',
        _pageNo: 1,
        _pageSize: 20
      }
    },
    handleTableSelection (val) {
      this.selectApiIds = val
    },
    selectApiConfirm (apiIds) {
      Api.contactApi({
        spiName: this.spiName,
        apiIds
      })
        .then(res => {
          if (res.status === 'success') {
            this.$ypMsg.notice_success(this, '关联API成功')
            this.getList()
          } else {
            this.$Modal.error({
              title: '错误',
              content: res.message
            });
          }
        })
    },
    batchUncontactApi () {
      if (this.selectApiIds.length === 0) {
        this.$ypMsg.notice_warning(this, '请选择需要取消的API后再点击');
        return;
      }
      this.uncontactApi(this.selectApiIds.map(item => item.apiId))
    },
    uncontactApi (apiIds) {
      this.$Modal.confirm({
        content: '确认取消关联API？',
        okText: '确认',
        onOk: () => {
          Api.uncontactApi({
            spiName: this.spiName,
            apiIds
          })
            .then(res => {
              if (res.status === 'success') {
                this.getList()
              } else {
                this.$Modal.error({
                  title: '错误',
                  content: res.message
                });
              }
            })
        }
      });
    },
    onPageChange (val) {
      this.params._pageNo = val
      this.getList()
    }
  }
}
</script>

<style>
</style>