<style lang="less">
    @import '../../../styles/common.less';
    @import '../../../styles/loading.less';
    @import '../api_Mangement_Open.less';
    @import '../../regression-test/regression.less';
    /*html,body{*/
         /*width: 100%;*/
         /*height: 100%;*/
         /*overflow: scroll;*/
     /*}*/
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab{
        border-radius: 0;
        background: #fff;
    }
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active{
        border-top: 1px solid #3399ff;
    }
    .tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active:before{
        content: '';
        display: block;
        width: 100%;
        height: 1px;
        background: #3399ff;
        position: absolute;
        top: 0;
        left: 0;
    }
    .red {
        border:1px solid red;
    }
</style>
<template>
    <div >
            <Row v-show="basicShow">
                    <Col span="12"   style="border:1px solid #eee;border-radius:6px;padding:3px;overflow:scroll">

                        基本信息
                        <Form ref="form_data_basics"  :model="form_data_basics" :label-width="120" inline>
                            <FormItem  class="width-100-perc" :class="form_data_basics.name != form_data_basics_src.name ?'red':''" label="通知编码：" prop="name" >
                                <p>{{form_data_basics.name}}</p>
                            </FormItem>
                            <FormItem  class="width-100-perc" :class="form_data_basics.title != form_data_basics_src.title ?'red':''"  label="SPI标题：" prop="title">
                                <p>{{form_data_basics.title}}</p>
                            </FormItem>
                            <Row>
                            <FormItem label="SPI类型：" class="width-100-perc" :class="form_data_basics.spiType != form_data_basics_src.spiType ?'red':''"  prop="spiType">
                                <p >{{form_data_basics.spiType}}</p>
                            </FormItem>
                            </Row>
                            <Row>

                                <FormItem  class="width-100-perc" :class="form_data_basics.apiGroup != form_data_basics_src.apiGroup ?'red':''"  label="API分组：" prop="apiGroup">
                                <p >{{form_data_basics.apiGroup}}</p>
                            </FormItem>
                            </Row>
                            <FormItem  class="width-100-perc" :class="form_data_basics.description != form_data_basics_src.description ?'red':''"  label="描述：" prop="description">
                                <p>{{form_data_basics.description}}</p>
                            </FormItem>
                            
                        </Form>
                        请求信息
                        <Form ref="form_data"  :model="form_data" :label-width="120" inline >

                            <Col span="24">
                            <Row>
                                <FormItem label="requestUrl：" class="width-100-perc" :class="form_data.requestUrl != form_data_src.requestUrl ?'red':''" prop="requestUrl">
                                    <p>{{form_data.requestUrl}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="httpMethod：" class="width-100-perc" :class="form_data.httpMethod != form_data_src.httpMethod ?'red':''" prop="httpMethod">
                                    <p>{{form_data.httpMethod}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                            <p style="margin-left:27px;margin-bottom:20px;">requestBody:</p>
                                <FormItem label="models：" class="width-100-perc" :class="form_data.model != form_data_src.model ?'red':''" prop="model">
                                    <p>{{form_data.model}}</p>
                                </FormItem>
                            
                                <FormItem  class="width-100-perc" :class="form_data.description != form_data_src.description ?'red':''" label="contentType：" prop="contentType">
                                    <p>application/json</p>
                                </FormItem>
                                <FormItem  class="width-100-perc" :class="form_data.description != form_data_src.description ?'red':''" label="描述：" prop='description'>
                                    <p >{{form_data.description}}</p>
                                </FormItem>
                            </Row>
                            </Col>
                        </Form>
                        <Col span="24" style="margin-top: 20px;" class="tabs-style2">
                                <Table id='table_1' border ref="selection" :columns="spi_example_List_display" :data="data_spi_example_List" @on-selection-change=""></Table>
                            <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                                <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                            </Tooltip> -->
                            
                        </Col>
                    </Col>
                    <Col  span="12"  style="border:1px solid #eee;border-radius:6px;padding:3px;overflow:scroll">

                        原基本信息
                        <Form ref="form_data_basics"  :model="form_data_basics_src" :label-width="120">
                            <FormItem  class="width-100-perc"  :class="form_data_basics.name != form_data_basics_src.name ?'red':''" label="通知编码：" prop="name" >
                                <p>{{form_data_basics_src.name}}</p>
                            </FormItem>
                            <FormItem  class="width-100-perc" label="SPI标题："  :class="form_data_basics.title != form_data_basics_src.title ?'red':''" prop="title">
                                <p>{{form_data_basics_src.title}}</p>
                            </FormItem>
                            <Row>
                            <FormItem label="SPI类型：" class="width-100-perc" :class="form_data_basics.spiType != form_data_basics_src.spiType ?'red':''" prop="spiType">
                                <p >{{form_data_basics_src.spiType}}</p>
                            </FormItem>
                            </Row>
                            <Row>

                                <FormItem  class="width-100-perc" :class="form_data_basics.apiGroup != form_data_basics_src.apiGroup ?'red':''" label="API分组：" prop="apiGroup">
                                <p >{{form_data_basics_src.apiGroup}}</p>
                            </FormItem>
                            </Row>
                            <FormItem  class="width-100-perc" :class="form_data_basics.description != form_data_basics_src.description ?'red':''" label="描述：" prop="description">
                                <p>{{form_data_basics_src.description}}</p>
                            </FormItem>
                            
                        </Form>
                        原请求信息
                        <Form ref="form_data"  :model="form_data_src" :label-width="120" >

                            <Col span="24">
                            <Row>
                                <FormItem label="requestUrl：" class="width-100-perc" :class="form_data.requestUrl != form_data_src.requestUrl ?'red':''" prop="requestUrl">
                                    <p>{{form_data_src.requestUrl}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                                <FormItem label="httpMethod：" class="width-100-perc" :class="form_data.httpMethod != form_data_src.httpMethod ?'red':''" prop="httpMethod">
                                    <p>{{form_data_src.httpMethod}}</p>
                                </FormItem>
                            </Row>
                            <Row>
                            <p style="margin-left:27px;margin-bottom:20px;">requestBody:</p>
                                <FormItem label="models：" class="width-100-perc" :class="form_data.model != form_data_src.model ?'red':''" prop="model">
                                    <p>{{form_data_src.model}}</p>
                                </FormItem>
                            
                                <FormItem  class="width-100-perc" :class="form_data.contentType != form_data_src.contentType ?'red':''" label="contentType：" prop="contentType">
                                    <p>application/json</p>
                                </FormItem>
                                <FormItem  class="width-100-perc" :class="form_data.description != form_data_src.description ?'red':''" label="描述：" prop='description'>
                                    <p >{{form_data_src.description}}</p>
                                </FormItem>
                            </Row>
                            </Col>
                        </Form>
                        <Col span="24" style="margin-top: 20px;" class="tabs-style2">


                                    <Table id='table_1' border ref="selection" :columns="spi_example_List_display" :data="data_spi_example_List_src" @on-selection-change=""></Table>
                                <!-- <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="20" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                                </Tooltip> -->
                                
                        </Col>
                    </Col>
            </Row>
            <!-- <loading :show="show_loading_basic"></loading> -->
    </div>
</template>

<script>
    import api from '../../../api/api.js';
    import util from '../../../libs/util.js';
    import loading from '../../my-components/loading/loading'
    export default {
        name: 'api_basics',
        components :{
            loading,
        },
        data () {
           return {
    

    // 参数
    showReturn:true,
    requestUrl:"#app.callbackUrl",
    httpMethod:"POST",
    form_data:{
        requestUrl:"#app.callbackUrl",
        httpMethod:"POST",
        description:"",
        model:"application/json"
    },
    form_data_src:{
        requestUrl:"#app.callbackUrl",
        httpMethod:"POST",
        description:"",
        model:"application/json"
    },
    contentType_list:[{value:"application/json",label:"application/json"}],
    data_spi_example_List:[
    ],
    data_spi_example_List_src:[],
  
    spi_example_List_display:[
        {
            title: '名称',
            key: 'name',
            align:"center"
        },
        {
            title: '标题',
            key: 'title',
            align:"center"

        },
        {
            title: '描述',
            align:"center",
            key: 'description'
        },
        {
            title: '值',
            align:"center",
            key: 'value'
        },
    ],
    modal_Show_example:false,
    form_data_example:{
        name:"",
        title:"",
        description:"",
        value:"",
    },
    form_data_basics:{
        name:"",
        title:"",
        spiType:"CALLBACK",
        apiGroup:"",
        description:"",
    },
    form_data_basics_src:{
        name:"",
        title:"",
        spiType:"CALLBACK",
        apiGroup:"",
        description:"",
    },
    data_apiGroup_List:[],  
               /**
                * 基础界面变量
                */
               // api 请求方式数据绑定
               data_select_apiMethod : '',
               // api 请求方式下拉框总体数据
               data_apiMethod_list : [],
               // 当前参数tab页
               current_param_tab : 'request_params',
               // api contentType数据绑定
               data_select_apiContent : [],
               // api contentType下拉框总体数据
               data_apiContent_list : [],
               // 基础信息加载数据绑定
               show_loading_basic : false,
               // 显示收起内容
               basicShow : true,
               // 展开收起按钮文字内容绑定
               button_Content : '收起',
               // api分组数据绑定
               api_group_model : '',
               // api分组disabled
               api_group_disabled : false,
               // method post disabled
               request_post_disabled : false,
               // method get disabled
               request_get_disabled : false,
               // 接口uri disabled
               interface_uri_disabled : false,
               // api分组数据列表
               api_group_list : [],
               // api类型选择数据绑定
               data_select_apiType: '',
               // api类型
               data_apiType_List:[],
               // 请求方式绑定第一个参数是post第二个参数是get
               request_post : [false,false],
               // 当前tab页绑定的参数
               tabNow: 'basic',
               // 保存的数据参数
           
               // 接口uri数据绑定
               interface_uri: '',
               // 接口名称数据绑定
               interface_name: '',
               // 当前端点apiUri内容
               current_content_apiUri : '',
               // 基本类型
               cascader_type:[],
               // 当前apiId
               current_spiId : '',
               current_spiVersion:"",
               // 创建还是修改
               current_status: '',
                //
                check_result: true,
               /**
                * 新建弹窗变量
                */
               // 窗口显示绑定
               modal_Show_register : false,
               // 描述禁用
               disabled_description: false,
               // 端点url数据绑定
               endpoint_Url : '',
               // 显示端点url部分
               show_pointUrl : false,
               // 端点类名数据绑定
               endpoint_Name: '',
               // 是否幂等数据绑定
               idempotent: '否',
               // 端点url自定义
               pointUrl_user_defined: false,
               // 适配类型下拉框数据绑定
               data_select_type: 'TRANSFORM',
               // 适配类型下拉框数据
               data_type_List: [
                   {
                       value: 'TRANSFORM',
                       label: '转换'
                   },
                   {
                       value: 'LOCAL',
                       label: '本地'
                   },
                   {
                       value: 'PASSTHROUGH',
                       label: '透传'
                   }
               ],
               // 端点协议下拉框数据绑定
               data_select_EndpointProtocol: 'HESSIAN',
               // 端点协议下拉框数据
               data_EndpointProtocol_List: [
                   {
                       value: 'HESSIAN',
                       label: 'hessian'
                   }
               ],
               // 端点方法下拉框数据绑定
               data_select_EndpointMethod: '',
               // 端点方法下拉框数据
               data_EndpointMethod_List: [],
               // 当前端点名称内容
               current_content : '',
               // 当前端点url内容
               current_content_url : '',
               // url校验结果
               pass_url : true,
               data_api_model_list:[],
               editExample:false,
               row_index:0,
               result:{}
           }
        },
        methods : {
            /**
             * 基础界面方法
             */
            // 初始化函数
                setResult(val){
                    var spi = val.spi;
                    var srcSpi = val.srcSpi
                    this.form_data_basics = spi.basic
                    this.form_data = spi.request
                    this.form_data.description = spi.request.requestBody.description;
                    var schema = JSON.parse(spi.request.requestBody.contents["application/json"].schema)
                    this.form_data.model = schema["$ref"].split("/").pop();
                    this.data_spi_example_List = spi.request.requestBody.contents["application/json"].examples || []

                    this.form_data_basics_src = srcSpi.basic
                    if(srcSpi.request){
                        this.form_data_src = srcSpi.request
                        this.form_data_src.description = srcSpi.request.requestBody.description;
                        var schemaSrc = JSON.parse(srcSpi.request.requestBody.contents["application/json"].schema)
                        this.form_data_src.model = schemaSrc["$ref"].split("/").pop();
                        this.data_spi_example_List_src = srcSpi.request.requestBody.contents["application/json"].examples || []
                    }
                },
                init () {
                    
                   
            },
           
            ok_example(){
                this.$refs.form_data_example.validate((valid) => {
                    console.log(valid)
                    if (valid) {
                        var example = {
                                name:this.form_data_example.name,
                                title:this.form_data_example.title,
                                description :this.form_data_example.description,
                                value:this.form_data_example.value,
                            }
                        if(!this.editExample){
                            this.data_spi_example_List.push(example)
                        }else{
                            this.data_spi_example_List.splice(this.row_index,1,example)
                        }
                        this.modal_Show_example = false;
                    }else {
                        this.form_validate_failed()
                    }
                })

            },
            form_validate_failed(){

            },
            cancel_example(){
                this.modal_Show_example = false;

            },
           
          
        },
        created () {

        },
        mounted () {
            this.init();
        },
        beforeRouteLeave (to, from, next) {
            this.$destroy();
            next();
        },
        beforeDestroy () {
            // console.log(this.$refs.te);
            // this.$refs.te.destroySelf();
        },
        destroyed () {
            // this.$store.commit('closePage', 'api_basics');
        }
    };

</script>

<style scoped>
    /*.ivu-collapse {*/
        /*background-color : #FFFFFF;*/
    /*}*/
</style>
