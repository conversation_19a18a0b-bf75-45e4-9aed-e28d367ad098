<template>
	<div>
		<Modal v-model="show" width="1000">
			<p slot="header" style="color: #2d8cf0">
				<span style="color: black">已订阅商编</span>
			</p>
			<Card dis-hover :bordered="false">
				<Row type="flex" align="middle">
					<Col span="24">
						<Col span="8">
							<Col span="6" class="margin-top-5">
								<span>商户编码:</span>
							</Col>
							<Col span="18">
								<Input clearable v-model="params.customerNo" id="inputApiName" placeholder="请输入"></Input>
							</Col>
						</Col>
						<Col offset="1" span="7">
							<Col span="6" class="margin-top-5">
								<span>应用标识:</span>
							</Col>
							<Col span="18">
								<Input clearable v-model="params.appId" id="inputApiUrl" placeholder="请输入"></Input>
							</Col>
						</Col>
					</Col>
				</Row>
				<Row type="flex" justify="start" class="margin-top-20">
					<Col span="12">
						<Button id="batch-btn-cancel"  type="ghost" @click="batchUncontact">批量取消</Button>
						<Button id="adpp-api-btn" type="primary" @click="addApi">添加订阅</Button>
					</Col>
					<Col span="12" type="flex" align="end">
						<Button id="relve-btnSearch" type="primary" @click="getList">查询</Button>
						<Button id="relve-btnReset" type="ghost" @click="reset_Interface">重置</Button>
					</Col>
				</Row>
				<Row class="margin-top-20">
					<Table
						id="table-relve"
						border
						ref="singleTable"
						:columns="columns"
						:data="data"
						@on-selection-change="handleTableSelection"
						row-key="id"
					></Table>
					<Loading :show="loading"></Loading>
				</Row>
				<Row class="margin-top-20" type="flex" justify="end">
					<Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
						<Page :total="total" :current="params.pageNo" show-elevator @on-change="onPageChange" />
					</Tooltip>
				</Row>
			</Card>
			<div slot="footer">
				<Button type="ghost" id="relveCancel" @click="closeModal">关闭</Button>
			</div>
		</Modal>
		<SelectMerchantNoModal ref="SelectMerchantNoModal" @refresh="getList" />
	</div>
</template>
<script>
import Loading from '~/views/my-components/loading/loading';
import SelectMerchantNoModal from '~/views/common-components/SelectMerchantNoModal';
import Api from '~/api/spi'
export default {
  components: {
    Loading,
    SelectMerchantNoModal
  },
  data () {
    return {
      show: false,
      loading: false,
      total: NaN,
      params: {
        spiName: '',
        customerNo: '',
        appId: '',
        pageNo: 1,
        pageSize: 10
      },
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '已订阅商编',
          key: 'customerNo',
        },
        {
          title: '应用',
          width: 150,
          key: 'appId'
        },
        {
          title: '通知地址',
          key: 'url',
        },
        {
          title: '操作',
          width: 100,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.uncontact([params.row]);
                  }
                }
              }, '取消订阅')
            ])
          }
        }
      ],
      data: [],
      spiName: '',
      destinations: []
    }
  },
  methods: {
    closeModal () {
      this.show = false
    },
    showModal (spiName) {
      this.spiName = spiName
      this.params.spiName = spiName
      this.getList()
      this.show = true
    },
    addApi () {
      this.$refs.SelectMerchantNoModal.show_model(this.spiName)
    },
    getList () {
      this.loading = true
      Api.getSubscribeList(this.params)
        .then(res => {
          if (res.data.status === 'success') {
            this.data = res.data.data.page.items
            if (this.params.pageNo < res.data.data.page.totalPageNum) {
              this.total = NaN
            } else {
              this.total = res.data.data.page.totalPageNum * 10
            }
          } else {
            this.$Modal.error({
              title: '错误',
              content: res.message
            });
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    reset_Interface () {
      this.params = {
        spiName: this.spiName,
        customerNo: '',
        appId: '',
        pageNo: 1,
        pageSize: 20
      }
    },
    handleTableSelection(val) {
      this.destinations = val
    },
    batchUncontact () {
      if (this.destinations.length === 0) {
        this.$ypMsg.notice_warning(this, '请选择需要取消的商编后再点击');
        return;
      }
      this.uncontact(this.destinations)
    },
    uncontact (destinations) {
      this.$Modal.confirm({
        content: '确认取消订阅商编？',
        okText: '确认',
        onOk: () => {
          Api.unsubscribe({
            spiName: this.spiName,
            destinations
          })
            .then(res => {
              if (res.status === 'success') {
                this.getList()
              } else {
                this.$Modal.error({
                  title: '错误',
                  content: res.message
                });
              }
            })
        }
      });
    },
    onPageChange (val) {
      this.params.pageNo = val
      this.getList()
    }
  }
}
</script>

<style>
</style>