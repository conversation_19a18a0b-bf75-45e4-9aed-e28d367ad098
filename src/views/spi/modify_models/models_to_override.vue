<style lang="less">
	.width-50-perc {
		width: 45%;
	}
	.width-100-perc {
		width: 100%;
	}
	.margin-top-0 {
		margin-top: 0 !important;
	}
	/*.margin-right-9{*/
	/*margin-right: 9px!important;*/
	/*}*/
	.red {
		border: 1px solid red;
	}
	.chemaStyle {
		outline: 1px solid #ccc;
		padding: 5px;
		margin: 5px;
	}
</style>
<template>
	<div>
		<Row>
			<Col span="12" style="border:1px solid #eee;border-radius:6px;padding:3px;overflow:scroll;">
				<Row>
					<Col span="24">
						<p>基本信息</p>
						<Form ref="form_data_top" :model="form_data" :label-width="120" inline>
							<FormItem
								label="模型名称："
								prop="name"
								class="width-100-perc"
								:class="form_data.name != form_data_src.name ?'red':''"
							>
								<p class="margin-right-9">{{form_data.name}}</p>
							</FormItem>
							<FormItem
								label="api分组："
								prop="apiGroup"
								class="width-100-perc"
								:class="form_data.apiGroup != form_data_src.apiGroup ?'red':''"
							>
								<p class="margin-right-9">{{form_data.apiGroup}}</p>
							</FormItem>
							<FormItem
								label="描述："
								class="width-100-perc"
								prop="description"
								:class="form_data.description != form_data_src.description ?'red':''"
							>
								<p class="margin-right-9">{{form_data.description}}</p>
							</FormItem>
						</Form>
					</Col>
					<Col span="24">
						<Form ref="form_data_left" :model="form_data" :label-width="120" inline>
							<FormItem
								label="类型："
								prop="type"
								class="width-100-perc"
								:class="form_data.type !=form_data_src.type?'red':''"
							>
								<p class="panel-content">{{form_data.type}}</p>
							</FormItem>
							<FormItem
								label="标题："
								v-if="form_data.title"
								prop="title"
								class="width-100-perc"
								:class="form_data.title !=form_data_src.title?'red':''"
							>
								<p class="panel-content">{{form_data.title}}</p>
							</FormItem>
							<FormItem class="width-100-perc" label="schema：" prop="schema">
								<!-- <pre class="schemaStyle">{{form_data.schema}}</pre> -->
							</FormItem>
						</Form>
						<!--</Card>-->
					</Col>
					<Col span="24">
						<!--<Card>-->
						<Form ref="form_data_right" :model="form_data" :label-width="120" inline></Form>
					</Col>
				</Row>
				<Row>
					<!-- <Table id='table_1' border ref="properties" :columns="columns_propertiesList_detail" :data="data_propertiesList"></Table> -->
				</Row>
				<div slot="footer">
					<Button type="primary" @click="submit">确定</Button>
					<Button type="ghost" @click="cancel">取消</Button>
				</div>
			</Col>
			<Col span="12" style="border:1px solid #eee;border-radius:6px;padding:3px;overflow:scroll;">
				<Row>
					<Col span="24">
						<p v-if="current_panel !=='detail_model'">原基本信息</p>
						<Form ref="form_data_top" :model="form_data_src" :label-width="120" inline>
							<FormItem
								label="模块名称："
								prop="name"
								class="width-100-perc"
								:class="form_data.name != form_data_src.name ?'red':''"
							>
								<p class="margin-right-9">{{form_data_src.name}}</p>
							</FormItem>
							<FormItem
								label="api分组："
								prop="apiGroup"
								class="width-100-perc"
								:class="form_data.apiGroup != form_data_src.apiGroup ?'red':''"
							>
								<p class="margin-right-9">{{form_data_src.apiGroup}}</p>
							</FormItem>
							<FormItem
								label="描述："
								class="width-100-perc"
								prop="description"
								:class="form_data.description != form_data_src.description ?'red':''"
							>
								<p class="margin-right-9">{{form_data_src.description}}</p>
							</FormItem>
						</Form>
					</Col>
					<Col span="24">
						<Form ref="form_data_left" :model="form_data_src" :label-width="120" inline>
							<FormItem
								label="类型："
								prop="type"
								class="width-100-perc"
								:class="form_data.type !=form_data_src.type?'red':''"
							>
								<p class="panel-content">{{form_data_src.type}}</p>
							</FormItem>
							<FormItem
								label="标题："
								v-if="form_data.title"
								prop="title"
								class="width-100-perc"
								:class="form_data.title !=form_data_src.title?'red':''"
							>
								<p class="panel-content">{{form_data_src.title}}</p>
							</FormItem>
							<FormItem class="width-100-perc" label="schema：" prop="schema">
								<!-- <pre class="schemaStyle">{{form_data_src.schema}}</pre> -->
							</FormItem>
						</Form>
						<!--</Card>-->
					</Col>
					<Col span="24">
						<!--<Card>-->
						<Form ref="form_data_right" :model="form_data" :label-width="120" inline></Form>
					</Col>
				</Row>
				<Row></Row>
				<div slot="footer">
					<Button type="primary" @click="submit">确定</Button>
					<Button type="ghost" @click="cancel">取消</Button>
				</div>
				<!-- <modal_param_content_model ref="modal_param_content_model" :index="current_index" @data_update="data_update_model"></!-->
			</Col>
		</Row>
		<Row>
			<div id="codeMirror_3" class="codeMirror_3"></div>
		</Row>
	</div>
</template>

<script>
    import textEditorSize from '../../my-components/text-editor/text-editor-size';
    import commonSelect from '../../common-components/select-components/selectCommon';
    import label_icon from '../../common-components/icon-components/star_red'
    // import subcontent from './param_main_content_model'
    // import modal_param_content_model from './modal_param_content_model';
    import api from '../../../api/api'

    import "../../../libs/codeMirror/lib/codemirror.css"
    import "../../../libs/codeMirror/addon/merge.css"
    import CodeMirror from '../../../libs/codeMirror/lib/codemirror';
    require("../../../libs/codeMirror/mode/xml")
    require("../../../libs/codeMirror/addon/merge")
    require("../../../libs/codeMirror/addon/fresh")


    export default {
        name: 'modal_param_content_model_external',
        components: {
            textEditorSize,
            commonSelect,
            // subcontent,
            label_icon,
            // modal_param_content_model,
        },
        data () {
            return {
                apiGroup_List:[],
                tempProperties:{},
                columns_propertiesList_detail:[
                    {
                        title: '名称',
                        key: 'name',
                        width: 100,
                        render: (h, params) => {
                            if(params.row.name){
                                return h('div',params.row.name);
                            }else{
                                return h('div','--');
                            }
                        }
                    },
                    {
                        title: '类型',
                        key: 'type',
                        width: 130,
                        align:'center',
                        render: (h, params) => {
                            if(typeof params.row.type == "object"){
                                return h('div',params.row.type[0]);
                            }else{
                                return h('div',params.row.type);
                            }
                        }
                        // render: (h,params) => {
                        //     if(params.row.model){
                        //         let _this = this
                        //         return h('div', [
                        //             h('p',[
                        //                 h('a',
                        //                     {
                        //                         on: {
                        //                             click: function(){
                        //                                 _this.model_click(params.row.model);
                        //                             }
                        //                         },
                        //                     },params.row.model)
                        //             ])
                        //         ]);
                        //     }else{
                        //         return params.row.type[1]? h('div',params.row.type[0]+'/'+params.row.type[1]):h('div',params.row.type[0]);
                        //     }
                        // }
                    },
                    {
                        title: '必填',
                        key: 'must',
                        width: 100,
                        render: (h, params) => {
                            if(params.row.model){
                                return h('div','--');
                            }else{
                                return params.row.required ? h('div','是') : h('div','否');
                            }

                        }
                    },
                    {
                        type:'html',
                        title: '描述',
                        key: 'description',
                        'min-width': 180,
                    },
                    {
                        type:'html',
                        title: '示例值',
                        key: 'sample',
                        // width: 100,
                        'min-width': 180,
                    },
                    // {
                    //     title: '端点参数',
                    //     key: 'param_name',
                    //     width: 120
                    // },
                ],
                data_propertiesList:[],
                data_propertiesList_src:[],
                submit_modal:[],
                current_modify_model:[],
                current_index : -1,
                current_model : [],
                data_model:[],
                // form_data_left:{
                //     param: '', // 参数
                //     title: '', // 名称
                //     type: ['object'], // 类型
                //     sample: '', // 示例值
                //     description: '', // 描述
                //     required: false, // 是否必填
                // },
                // 表格内容数据绑定
                form_data: {
                    name:"",
                    apiGroup:"",
                    description:"",
                    param: '', // 参数
                    title: '', // 名称
                    type: ['object'], // 类型
                    description: '', // 描述
                    required: false, // 是否必填
                    sensitive : false, // 名称
                    schema:{}
                },
                form_data_src:{
                    name:"",
                    apiGroup:"",
                    description:"",
                    param: '', // 参数
                    title: '', // 名称
                    type: ['object'], // 类型
                    description: '', // 描述
                    required: false, // 是否必填
                    sensitive : false, // 名称
                    schema:{}
                },

                // 表格显示绑定
                form_data_visual: {
                    extremum : false,
                    enums : false,
                    length : false,
                    pattern : false,
                    itemSize : false,
                    model : false,
                    additionalProperties: false,
                    items : false,
                    order: false
                },
                current_panel: 'create_request_json',
                // 窗口是否展示
                modal_show : true,
                // 示例值绑定
                data_select_sample : '',
                // 常用正则表达式数据绑定
                data_select_pattern : '',
                // 枚举数据绑定
                enum_data : [{
                    value : '',
                    index : 0,
                    status : 1
                }],
                enum_index : 0,
                // 是否展示更多属性 false 为隐藏 true 为显示
                more_attr : false,

                type_data:[],
                type_data_cut : [],
                sample_data:[
                    {
                        value : 'integer',
                        desc : 'integer',
                        formats :[
                            {
                                value : 'int32',
                                desc : 'int32',
                                defaulted : true,
                            },
                            {
                                value : 'int64',
                                desc : 'int64',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'number',
                        desc : 'number',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                            },
                            {
                                value : 'float',
                                desc : 'float',
                                defaulted : true,
                            },
                            {
                                value : 'double',
                                desc : 'double',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'boolean',
                        desc : 'boolean',
                        constrains : [
                            'enums',
                            'length',
                            'pattern'
                        ],
                        // formats :[
                        //     {
                        //         value : 'boolean',
                        //         desc : 'boolean',
                        //         defaulted : true,
                        //         constrains : [
                        //             'enums',
                        //             'length',
                        //             'pattern'
                        //         ],
                        //     }                            
                        // ]
                    },
                    {
                        value : 'string',
                        desc : 'string',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                                constrains : [
                                    'enums',
                                    'length',
                                    'pattern'
                                ]
                            },
                            {
                                value : 'byte',
                                desc : 'byte',
                                defaulted : true,
                            },
                            {
                                value : 'binary',
                                desc : 'binary',
                                defaulted : true,
                            },
                            {
                                value : 'date',
                                desc : 'date',
                                defaulted : true,
                            },
                            {
                                value : 'date-time',
                                desc : 'date-time',
                                defaulted : true,
                            },
                            {
                                value : 'password',
                                desc : 'password',
                                defaulted : true,
                            },
                            {
                                value : 'email',
                                desc : 'email',
                                defaulted : true,
                            },
                            {
                                value : 'mobile',
                                desc : 'mobile',
                                defaulted : true,
                            },
                            {
                                value : 'idcard',
                                desc : 'idcard',
                                defaulted : true,
                            },
                            {
                                value : 'bankcard',
                                desc : 'bankcard',
                                defaulted : true,
                            },
                            {
                                value : 'cvv',
                                desc : 'cvv',
                                defaulted : true,
                            },
                            {
                                value : 'uuid',
                                desc : 'uuid',
                                defaulted : true,
                            }
                        ]
                    },
                    {
                        value : 'array',
                        desc : 'array',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                                constrains : [
                                    'items',
                                ]
                            }
                        ]
                    },
                    {
                        value : 'object',
                        desc : 'object',
                        formats :[
                            {
                                value : '-',
                                desc : '-',
                                defaulted : true,
                            },
                            {
                                value : 'map',
                                desc : 'map',
                                defaulted : true,
                                constrains : [
                                    'additionalProperties',
                                ]
                            }
                        ]
                    },
                ],
                // 当前编辑行数
                current_line_index : 0

            };
        },
        methods: {
            syntaxHighlight(json) {
                if (typeof json != 'string') {
                    json = JSON.stringify(json, undefined, 4);
                }
                // json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
                return json
                // return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
                //     function(match) {
                //         var cls = 'number';
                //         if (/^"/.test(match)) {
                //             if (/:$/.test(match)) {
                //                 cls = 'key';
                //             } else {
                //                 cls = 'string';
                //             }
                //         } else if (/true|false/.test(match)) {
                //             cls = 'boolean';
                //         } else if (/null/.test(match)) {
                //             cls = 'null';
                //         }
                //         return match;
                //     }
                // );
            },
            format_submit_param(param,subContent,type){
                var tempProperties = {properties:{}}
                var requiredArr = []
                for(var k = 0 ;k < param.length ; k ++){
                    var paramK = param[k]
                    tempProperties["properties"][paramK["name"]] = {};
                    for(var i in paramK){
                        // if(i == "title"){
                        //     tempProperties.title = paramK[i]
                        // }
                        if(i == "type"){
                            if(paramK[i][1] == "map" && type == "map"){
                                console.log(subContent,"点击编辑是map回显的信息")
                                var subContent_fix = {};
                                if(subContent && typeof subContent.type == "object"){
                                    for(var m in subContent){
                                        if(m == "type"){
                                            subContent_fix.format = subContent.type[1] || ""
                                            subContent_fix.type = subContent.type[0]
                                        }else{
                                            subContent_fix[m] = subContent[m]
                                        }
                                    }
                                    paramK["additionalProperties"] = subContent_fix
                                }
                                console.log(subContent_fix,"修正后的subContent")
                                console.log("paramK['additionalProperties']",paramK["additionalProperties"])
                                tempProperties["properties"][paramK["name"]][i] = "object"
                                if(paramK.model){
                                    tempProperties["properties"][paramK["name"]]["additionalProperties"]["$ref"] = `#/components/schemas/${paramK.model}`
                                }else{
                                    tempProperties["properties"][paramK["name"]]["additionalProperties"] = subContent_fix ?subContent_fix: paramK["additionalProperties"]
                                    tempProperties["properties"][paramK["name"]]["additionalProperties"]["format"] = subContent_fix ? subContent_fix["format"] : paramK["additionalProperties"]["format"]
                                    tempProperties["properties"][paramK["name"]]["additionalProperties"]["type"] = subContent_fix ?subContent_fix["type"] : paramK["additionalProperties"]["type"]
                                }
                            }else if (paramK[i][0] == "array" && type == "array"){
                                console.log(subContent,"点击编辑是array回显的信息")
                                var subContent_fix = {};
                                if(subContent && typeof subContent.type == "object"){
                                    for(var m in subContent){
                                        console.log(m)
                                        if(m == "type"){
                                            subContent_fix.format = subContent.type[1] || ""
                                            subContent_fix.type = subContent.type[0]
                                        }else{
                                            subContent_fix[m] = subContent[m]
                                        }
                                    }
                                    paramK["items"] = subContent_fix
                                }
                                console.log(subContent_fix,"修正后的subContent")
                                console.log("paramK['items']",paramK["items"])
                                tempProperties["properties"][paramK["name"]][i] = "array"
                                tempProperties["properties"][paramK["name"]]["items"] = subContent_fix ?subContent_fix: paramK["items"]
                                tempProperties["properties"][paramK["name"]]["items"]["format"] = subContent_fix ? subContent_fix["format"] : paramK["items"]["format"]
                                tempProperties["properties"][paramK["name"]]["items"]["type"] = subContent_fix ?subContent_fix["type"] : paramK["items"]["type"]
                            }else if(paramK[i][1] == "object"){
                                tempProperties["properties"][paramK["name"]][i] = "object"
                                tempProperties["properties"][paramK["name"]]["$ref"] = `#/components/schemas/${paramK.model}`;

                            }else{
                                console.log("i=",i)
                                tempProperties["properties"][paramK["name"]][i] = paramK[i][0]
                                if(paramK[i][0] != paramK[i][1]){
                                    console.log("!=",paramK[i])
                                    tempProperties["properties"][paramK["name"]]["format"] = paramK[i][1]
                                }
                            }
                        }
                        if(i == "required" && paramK[i]){
                            requiredArr.push(paramK["name"]);
                            tempProperties[i] = requiredArr
                        }
                        if(paramK[i] != "" && i != "title" && i != "type" && i != "required" && i != "name" &&i != "model" && i != "_index" && i != "_rowKey" && i != "format" && i != "items" && i != "additionalProperties"){
                            console.log("组装函数：",i,paramK[i])
                            tempProperties["properties"][paramK["name"]][i] = paramK[i]
                        }
                    }
                }
                console.log("最终返回的tempProperties",tempProperties)
                return tempProperties
            },
            // 更新属性列表哦
            data_update_model (data,type,index,subContent,dataType) {
                console.log(subContent,"subContent")
                console.log(data,"2更新属性列表哦")
                var data = JSON.parse(JSON.stringify(data))
                if(type === 'create_model'){
                    this.data_propertiesList.push(data);
                    //现在为不包含子项的处理 需要在提交的时候2次数据规范
                }else{
                    // 编辑查看
                    // if(data.type[1]){
                    //     data["format"] = data.type[1]
                    // }
                    for(var i in data){
                        this.data_propertiesList[index][i] = data[i]
                    }
                }
                
                this.tempProperties = this.format_submit_param(this.data_propertiesList,subContent,dataType);
                this.$refs.modal_param_content_model.cancel();
                this.$refs.modal_param_content_model.$refs.form_data_right.resetFields();
                this.$refs.modal_param_content_model.$refs.form_data_left.resetFields();
            },
            

            // 页面初始化
            init () {
                
            },
            // 表单提交
            submit (){
                if(this.current_panel ==='detail_model'){
                    this.modal_show = false;
                }
                // if(this.current_panel ==='create_model'){
                //     this.form_submit_validate('form_data_top','form_data_left')
                // }else{
                    this.form_submit_validate('form_data_top')
                // }
                // this.form_submit_validate('form_data_top','form_data_left','form_data_right')
            },
            // 继续验证
            next_validate(){
                if(this.form_data_visual.additionalProperties){
                    this.$refs['subContent_map'].submit();
                }else if(this.form_data_visual.items){
                    this.$refs['subContent_array'].submit();
                }else{
                    console.log('验证成功')
                    if(this.current_panel === "create_model"){
                        var param = {
                            name:this.form_data.name,
                            apiGroup:this.form_data.apiGroup,
                            description:this.form_data.description,
                            schema:{
                                title:this.form_data.title,
                                type:"object",
                                properties:this.tempProperties.properties,
                                required:this.tempProperties.required,
                            }

                        }
                      
                    }else{
                         var param = {
                            description:this.form_data.description,
                            schema:{
                                title:this.form_data.title,
                                type:"object",
                                properties:this.tempProperties.properties,
                                required:this.tempProperties.required
                            }

                        }
                    }
                    var stringSchema = JSON.stringify(param.schema)
                    param.schema = stringSchema
                    this.$emit('data_update',param,this.current_panel,this.current_line_index);
                    // 新建或者编辑操作执行
                }
            },
            // 提交函数

            // 表单提交验证
            form_submit_validate (val1,val2) {
                let result1 = 0;
                let result2 = 0;
                let result3 = 0;
                let check = 0;
                // if(this.current_panel === "create_model"){
                //     this.$refs[val1].validate((valid) => {
                //         if (valid) {
                //             result1 =1;
                //             if(result2 === 1){
                //                 this.next_validate();
                //             }

                //         }else {
                //             this.form_validate_failed()
                //         }
                //     })
                //     this.$refs[val2].validate((valid) => {
                //         if (valid) {
                //             result2 =1;
                //             if(result1 === 1){
                //                 this.next_validate();
                //             }
                //         }else {
                //             this.form_validate_failed()
                //         }
                //     })
                // }else{
                    this.$refs[val1].validate((valid) => {
                        if (valid) {
                            this.next_validate();
                        }else {
                            this.form_validate_failed()
                        }
                    })
                // }
                
                // this.$refs[val3].validate((valid) => {
                //     console.log(valid)
                //     if (valid) {
                //         result3 =1;
                //         if(result1 === 1 && result2 === 1){
                //             this.next_validate();
                //         }
                //     }else {
                //         this.form_validate_failed()
                //     }
                // })
            },
            // 表单验证失败
            form_validate_failed (){
                    this.$Message.error('请检查');
            },
            // 表单取消
            cancel () {
                this.modal_show = false;
            },
            // 窗口显示
            show () {
                this.modal_show = true;
            },
          
            setResult(val,index){
                var that = this

                var model = val.model
                var srcModel = val.srcModel
                this.form_data = model;
                var schema = JSON.parse(model.schema);
                this.form_data.schema = this.syntaxHighlight(schema)
                // this.form_data.schema = JSON.stringify(schema, null, 2)
                this.form_data.type = schema.type
                this.form_data.title = schema.title
                this.form_data.description = schema.description
                for (var i in schema.properties){
                    this.data_propertiesList.push(schema.properties[i])
                }

                this.form_data_src = srcModel;
                var schemaSrc = JSON.parse(srcModel.schema);
                this.form_data_src.schema = this.syntaxHighlight(schemaSrc);
                // this.form_data_src.schema = JSON.stringify(schema, null, 2)
                setTimeout(function(){
                    CodeMirror.k_init('codeMirror_3',that.form_data_src.schema,that.form_data.schema,index)
                }, 500);

                this.form_data_src.type = schemaSrc.type
                this.form_data_src.title = schemaSrc.title
                this.form_data_src.description = schemaSrc.description

                for (var i in schemaSrc.properties){
                    this.data_propertiesList_src.push(schemaSrc.properties[i])
                }
                // this.tempProperties = this.format_submit_param(data.data_propertiesList);
            },
        },
        mounted () {
            this.init();
        },
    };
</script>

<style scoped>
</style>
