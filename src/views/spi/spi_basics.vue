<style lang="less">
	@import '../../styles/common.less';
	@import '../../styles/loading.less';
	@import 'api_Mangement_Open.less';
	@import '../regression-test/regression.less';
	/*html,body{*/
	/*width: 100%;*/
	/*height: 100%;*/
	/*overflow: scroll;*/
	/*}*/
	.tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab {
		border-radius: 0;
		background: #fff;
	}
	.tabs-style2 > .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
		border-top: 1px solid #3399ff;
	}
	.tabs-style2
		> .ivu-tabs.ivu-tabs-card
		> .ivu-tabs-bar
		.ivu-tabs-tab-active:before {
		content: '';
		display: block;
		width: 100%;
		height: 1px;
		background: #3399ff;
		position: absolute;
		top: 0;
		left: 0;
	}
	.ivu-input-group-prepend {
		background: #fff;
		padding-right: 0 !important;
	}
	.spiName input {
		border-left: 0 !important;
		padding-left: 0 !important;
	}
	.ivu-input-group-small .ivu-input {
		padding-left: 0 !important;
	}
</style>
<template>
	<div style="background: #eee;padding:8px">
		<Card :bordered="false" dis-hover>
			<Row>
				<Row class="margin-bottom-10">
					<Col>
						<Button type="ghost" @click="returnList">
							<Icon type="chevron-left"></Icon>&nbsp;接口列表
						</Button>
						<span style="float:right">
							<Button
								v-if="current_status !== 'description'"
								class="margin-left-10"
								type="primary"
								@click="save"
							>保存</Button>
						</span>
					</Col>
				</Row>
				<h2>基本信息</h2>
				<Card dis-hover>
					<Row v-show="basicShow">
						<Col>
							<Form
								ref="form_data_basics"
								:model="form_data_basics"
								:rules="basicsValidate"
								label-position="top"
							>
								<Row>
									<Col :span="12">
										<FormItem class="width-100-perc" label="API分组：" prop="apiGroup">
											<Select
												v-show="this.current_status == 'create'"
												ref="select_apiM_1"
												id="select_apiM_1"
												class="margin-top-5"
												v-model="form_data_basics.apiGroup"
												filterable
												placeholder="请选择（默认全部）"
												@on-change="getModels"
												style="width: 380px;"
											>
												<Option
													v-for="item in data_apiGroup_List"
													:value="item.value"
													:key="item.label"
												>{{ item.label }}</Option>
											</Select>
											<p v-show="this.current_status !== 'create'">{{form_data_basics.apiGroup}}</p>
										</FormItem>
									</Col>
									<Col :offset="1" :span="11">
										<FormItem class="width-100-perc" label="通知编码：" prop="name">
											<Input
												v-show="this.current_status == 'create'"
												class="spiName"
												style="width: 380px;border-left:0;"
												v-model="form_data_basics.name"
												placeholder="请输入"
												:disabled="this.current_status == 'modify' ? 'disabled':false"
											>
												<span
													slot="prepend"
													v-if="this.current_status != 'description'"
													style="background:#fff"
												>
													{{form_data_basics.apiGroup}}
													<span v-show="form_data_basics.apiGroup">.</span>
												</span>
											</Input>
											<p
												v-if="this.current_status !== 'create'"
												style="margin-bottom:20px;"
											>{{form_data_basics.name}}</p>
											<p
												v-if="this.current_status == 'create'"
												class="yop-explain-120"
												style="padding-left:0px;"
											>创建后不可修改，支持小写字母、中划线，首字符必须为字母，尾字符不能为"-"</p>
										</FormItem>
									</Col>
									<Col :span="12">
										<FormItem label="通知类型：" prop="spiType">
											<!-- <RadioGroup v-if="this.current_status != 'description'" v-model="form_data_basics.spiType">
                                            <Radio :disabled="this.current_status == 'modify' ? 'disabled':false" id="rbtn_resList_1" label="CALLBACK">回调</Radio>
											</RadioGroup>-->
											<Select
												v-if="this.current_status != 'description'"
												v-model="form_data_basics.spiType"
												style="width:380px"
												placeholder="请输入"
												disabled
											>
												<Option value="CALLBACK">回调</Option>
											</Select>
											<p
												v-if="this.current_status == 'description'"
											>{{form_data_basics.spiType === "CALLBACK"?"回调":"通知"}}</p>
										</FormItem>
									</Col>
									<Col :offset="1" :span="11">
										<FormItem label="通知名称：" prop="title">
											<Input
												v-if="this.current_status != 'description'"
												style="width: 380px;"
												v-model="form_data_basics.title"
												placeholder="请输入"
											></Input>
											<p
												v-if="this.current_status == 'description'"
												style="margin-bottom:20px;"
											>{{form_data_basics.title}}</p>
										</FormItem>
									</Col>
									<Col :span="12">
										<FormItem class="width-100-perc" label="描述：" prop="description">
											<!-- <Input type="textarea" :rows="5" v-if="this.current_status !== 'description'"  style="width: 380px;"  v-model="form_data_basics.description" placeholder="请输入"></Input> -->
											<text-editor
												v-if="this.current_status !== 'description'"
												ref="solution_in"
												size="380px"
												:id="'solution_in'"
											></text-editor>
											<p v-if="this.current_status == 'description'">{{form_data_basics.description}}</p>
										</FormItem>
									</Col>
								</Row>
							</Form>
						</Col>
					</Row>
				</Card>
				<h2 style="margin-top: 10px">通知参数</h2>
				<Card dis-hover>
					<Row>
						<Form ref="form_data" :model="form_data" label-position="top">
							<Row>
								<Col :span="8">
									<FormItem
										label="参数："
										style="margin-bottom: 8px"
										prop="model"
										:rules="{required:true,message:'参数不能为空'}"
									>
										<Select
											v-show="this.current_status !== 'description'"
											ref="modal_apiM_select_4"
											id="modal_apiM_select_4"
											v-model="form_data.model"
											style="width: 100%"
                      filterable
											placeholder="请选择分组中的模型"
											@click="searchModel"
											@on-change="selectModel"
										>
											<Option 
                        v-for="item in data_api_model_list" 
                        :value="item.name" 
                        :key="item.id"
                        :label="item.description ? `${item.name}(${item.description})` : item.name"
                      >
											</Option>
										</Select>
										<p v-show="this.current_status == 'description'">{{form_data.model}}</p>
									</FormItem>
									<div v-show="this.current_status !== 'description'">
										<Button id="add-model-btn" type="primary" @click="addModel">新增模型</Button>
										<Button
											id="model-detail-btn"
											type="primary"
											v-if="form_data.model"
											@click="detailModel"
										>模型详情</Button>
									</div>
								</Col>
								<Col :span="7" :offset="1">
									<FormItem
										label="格式："
										prop="contentType"
										:rules="{required:true,message:'contentType不能为空'}"
									>
										<Select
											v-show="this.current_status == 'create'"
											v-model="form_data.contentType"
											style="width: 100%"
											placeholder="请输入"
											disabled
											@on-change="showContentPart"
										>
											<Option value="application/json">{{ form_data.contentType }}</Option>
										</Select>
										<p v-show="this.current_status !== 'create'">{{form_data.contentType}}</p>
									</FormItem>
								</Col>
							</Row>
						</Form>
					</Row>
				</Card>
				<div style="display:flex;justify-content: space-between;align-items: center;margin: 10px 0">
					<h2>示例报文</h2>
					<Button v-if="this.current_status !== 'description'" type="primary" @click="add_example">新增示例</Button>
				</div>
				<Card v-if="data_spi_example_List.length" dis-hover>
					<Row>
						<Col span="22" style="margin-left:75px;" class="tabs-style2">
							<!-- <Button v-if="this.current_status !== 'description'" type="primary" style="margin-bottom: 20px;"  @click="add_example">新增example</Button>
							<Table id='table_1' border ref="selection" :columns="this.current_status !== 'description'?spi_example_List:spi_example_List_display" :data="data_spi_example_List"></Table>-->
							<div v-for="(item, index) in data_spi_example_List" :key="index">
								<p>{{ item.name }}</p>
								<div
									style="display:flex;justify-content: space-between;align-items: center;margin-top: 8px"
								>
									<p style="color: grey">{{ item.description }}</p>
									<div>
										<Button
											v-if="current_status !== 'description'"
											style="border-color: red;color: red;background:#FFF"
											@click="example_delete(index)"
										>删除</Button>
										<Button
											v-if="current_status !== 'description'"
											type="primary"
											style="margin-left: 20px"
											@click="example_modify(item, index)"
										>修改</Button>
									</div>
								</div>
								<div style="background: black;color: #fff">
									<pre>{{ item.value }}</pre>
								</div>
							</div>
						</Col>
					</Row>
				</Card>
			</Row>
			<Loading :show="detailLoading"></Loading>
		</Card>
		<Modal
			v-model="modal_Show_example"
			width="600"
			:closable="false"
			:mask-closable="false"
			style="margin-left:0"
		>
			<p slot="header">
				<span class="margin-right-10">新增示例</span>
			</p>
			<div style="padding-left:30px;">
				<Form
					ref="form_data_example"
					:model="form_data_example"
					:rules="exampleValidate"
					:label-width="120"
					inline
				>
					<FormItem class="width-100-perc" label="名称：" prop="name">
						<Input style="width: 380px;" v-model="form_data_example.name" placeholder="请输入"></Input>
					</FormItem>
					<FormItem class="width-100-perc" label="描述：" prop="description">
						<Input
							type="textarea"
							style="width: 380px;"
							v-model="form_data_example.description"
							placeholder="请输入"
						></Input>
					</FormItem>
					<FormItem class="width-100-perc" label="示例值：" prop="value">
						<Input
							type="textarea"
							:rows="9"
							style="width: 300px;"
							ref="a"
							v-model="form_data_example.value"
							placeholder="请输入"
						></Input>
						<Button
							type="primary"
							class="margin-left-10"
							@click="format_json(form_data_example.value)"
						>格式化</Button>
					</FormItem>
				</Form>
			</div>
			<div slot="footer">
				<Button type="primary" @click="ok_example">确定</Button>
				<Button type="ghost" @click="cancel_example">取消</Button>
			</div>
		</Modal>
		<AddModelModal ref="AddModelModal" @data_update="updateModel">
            <template slot-scope="row" slot="detail-right">
                <Button type="primary" @click="editModel(row)">修改模型信息</Button>
            </template>
		</AddModelModal>
	</div>
</template>

<script>
    import textEditor from '../my-components/text-editor/text-editor-size';
    import api from '../../api/api.js';
    import Loading from '../my-components/loading/loading'
    import AddModelModal from '~/views/model/modify_models/modal_param_content_model_external';
    export default {
      name: 'api_basics',
      components: {
        textEditor,
        Loading,
        AddModelModal
      },
      data () {
        const validate_example_value = (rule, value, callback) => {
          if (!this.isJson(value)) {
            callback(new Error('示例值为json格式'));
          } else {
            callback();
          }
        }
        const validate_basic_name = (rule, value, callback) => {
          if (!this.isSpiname(value)) {
            callback(new Error('支持小写字母、中划线，首字符必须为字母，尾字符不能为"-"'));
          } else {
            callback();
          }
        }
        return {
          detailLoading: false,
          basicsValidate: {
            apiGroup: [{required: true, message: 'apiGroup不能为空'}],
            name: [{required: true, message: '通知编码不能为空'}, { validator: validate_basic_name, trigger: 'blur' }],
            title: [{required: true, message: '通知名称不能为空'}],
            spiType: [{required: true, message: '通知类型不能为空'}],
            description: [{required: true, message: '描述不能为空'}]
          },
          // 参数
          exampleValidate: {
            name: [{required: true, message: '名称不能为空'}],
            value: [{required: true, message: '示例值不能为空'}, { validator: validate_example_value, trigger: 'blur' }]
          },
          requestUrl: '#app.callbackUrl',
          httpMethod: 'POST',
          form_data: {
            requestUrl: '#app.callbackUrl',
            httpMethod: 'POST',
            description: '',
            contentType: 'application/json',
            model: '',
            requestBody: 'requestBody'
          },
          contentType_list: [{value: 'application/json', label: 'application/json'}],
          data_spi_example_List: [
          ],
          spi_example_List_display: [
            {
              title: '名称',
              key: 'name',
              align: 'center'
            },
            {
              title: '描述',
              align: 'center',
              key: 'description'
            },
            {
              title: '示例值',
              align: 'center',
              key: 'value'
            }
          ],
          modal_Show_example: false,
          form_data_example: {
            name: '',
            // title:"",
            description: '',
            value: ''
          },
          form_data_basics: {
            name: '',
            title: '',
            spiType: 'CALLBACK',
            apiGroup: '',
            description: ''
          },
          data_apiGroup_List: [],
          /**
                * 基础界面变量
                */
          // api 请求方式数据绑定
          data_select_apiMethod: '',
          // api 请求方式下拉框总体数据
          data_apiMethod_list: [],
          // 当前参数tab页
          current_param_tab: 'request_params',
          // api contentType数据绑定
          data_select_apiContent: [],
          // api contentType下拉框总体数据
          data_apiContent_list: [],
          // 基础信息加载数据绑定
          show_loading_basic: false,
          // 显示收起内容
          basicShow: true,
          // 展开收起按钮文字内容绑定
          button_Content: '收起',
          // api分组数据绑定
          api_group_model: '',
          // api分组disabled
          api_group_disabled: false,
          // method post disabled
          request_post_disabled: false,
          // method get disabled
          request_get_disabled: false,
          // 接口uri disabled
          interface_uri_disabled: false,
          // api分组数据列表
          api_group_list: [],
          // api类型选择数据绑定
          data_select_apiType: '',
          // api类型
          data_apiType_List: [],
          // 请求方式绑定第一个参数是post第二个参数是get
          request_post: [false, false],
          // 当前tab页绑定的参数
          tabNow: 'basic',
          // 保存的数据参数
          all_params: {
            basic:
                       {
                         apiUri: '', // 接口uri
                         apiGroup: '', // api分组
                         apiTitle: '', // 接口名称
                         description: '', // 接口描述
                         methods: [], // 请求方式
                         tags: ''// 是否幂等
                       },
            backend:
                       {
                         protocol: '', // （端点）协议http、hessian、spring；http没用起来，spring对一般用户可以隐藏；适配类型可以干掉
                         url: '', // 端点服务URL
                         class: '', // 端点类名,
                         method: '' // 端点方法"
                       },
            request:
                       {
                         form: [
                           {
                             paramName: '', // 参数
                             paramTitle: '', // 名称,
                             paramDataType: '', // 类型,
                             paramDataFormat: '', // 格式,//email?idcard?
                             description: '', // 描述,
                             defaultValue: '', //
                             sampleValue: '', //
                             internal: false, // true/false,//是否内部变量
                             sensitive: true, // true/false//是否敏感字段
                             endParamIndex: '', // 端点参数顺序,
                             endParamName: '', // 端点参数,
                             storage: {
                               inheritFromGroup: true, // true/false//继承自api分组
                               bucket: '', //,
                               fileName: ''//
                             },
                             constrains: {
                               notNull: true / false, // 是否必传
                               notEmpty: true / false, // 是否可为空字符串
                               length: {min: 1, max: 9}, // minexclusive是啥
                               range: { min: 3, max: 10 },
                               pattern: '' // 正则
                             }
                           }
                         ],
                         json: [
                           {
                             paramName: '', // 参数,
                             paramTitle: '', // 名称,
                             paramDataType: '', // 类型,
                             description: '', // 描述,
                             defaultValue: '', // ,
                             sampleValue: '',
                             internal: true, // true/false,//是否内部变量
                             sensitive: true, // true/false//是否敏感字段
                             endParamIndex: '', // 端点参数顺序,
                             endParamName: '', // 端点参数"
                             children: [// 如果是object或array，此处有children
                               {
                                 paramName: '', // 参数,
                                 paramTitle: '' // 名称,
                               }
                             ],
                             constrains: {
                               notNull: true, // true/false,//是否必传
                               notEmpty: true // true/false,//是否可为空字符串
                             }
                           }
                         ]
                       },
            response: [
              {
                paramName: '', // 参数,
                paramTitle: '', // 名称,
                paramDataType: '', // 类型,
                description: '', // 描述,
                sampleValue: '', // 示例值,
                children: [
                ]
              }
            ],
            errorCode: [
              {
                subCode: '', // 业务错误码,
                subMsg: '', // 业务错误描述,
                solution: '' // 解决方案
              }
            ],
            security: {
              inheritFromGroup: true, // true/false//继承自api分组
              config: [
                {
                  name: '', // YOP-RSA2048-SHA256,
                  custom: {
                    CFCA: '' // true/false
                  }
                },
                {
                  name: '', // YOP-OAUTH2,
                  custom: {
                    read: true, // true/false,
                    write: true // true/false
                  }
                }
              ]
            }
          },
    
          // 接口uri数据绑定
          interface_uri: '',
          // 接口名称数据绑定
          interface_name: '',
          // 当前端点apiUri内容
          current_content_apiUri: '',
          // 基本类型
          cascader_type: [],
          // 当前apiId
          current_spiId: '',
          current_spiVersion: '',
          // 创建还是修改
          current_status: '',
          //
          check_result: true,
          /**
                * 新建弹窗变量
                */
          // 窗口显示绑定
          modal_Show_register: false,
          // 描述禁用
          disabled_description: false,
          // 端点url数据绑定
          endpoint_Url: '',
          // 显示端点url部分
          show_pointUrl: false,
          // 端点类名数据绑定
          endpoint_Name: '',
          // 是否幂等数据绑定
          idempotent: '否',
          // 端点url自定义
          pointUrl_user_defined: false,
          // 适配类型下拉框数据绑定
          data_select_type: 'TRANSFORM',
          // 适配类型下拉框数据
          data_type_List: [
            {
              value: 'TRANSFORM',
              label: '转换'
            },
            {
              value: 'LOCAL',
              label: '本地'
            },
            {
              value: 'PASSTHROUGH',
              label: '透传'
            }
          ],
          // 端点协议下拉框数据绑定
          data_select_EndpointProtocol: 'HESSIAN',
          // 端点协议下拉框数据
          data_EndpointProtocol_List: [
            {
              value: 'HESSIAN',
              label: 'hessian'
            }
          ],
          // 端点方法下拉框数据绑定
          data_select_EndpointMethod: '',
          // 端点方法下拉框数据
          data_EndpointMethod_List: [],
          // 当前端点名称内容
          current_content: '',
          // 当前端点url内容
          current_content_url: '',
          // url校验结果
          pass_url: true,
          data_api_model_list: [],
          editExample: false,
          row_index: 0,
          showContent: false
        }
      },
      methods: {
        addModel () {
          const modal = this.$refs.AddModelModal
          modal.current_panel_set('create_model');
          modal.$refs.form_data_top.resetFields();
          modal.$refs.form_data_left.resetFields();
          modal.$refs.form_data_right.resetFields();
          modal.form_data_set(false);
          modal.show();
          modal.data_propertiesList = []
        },
        detailModel () {
          const modal = this.$refs.AddModelModal
          const { id, name, description } = this.data_api_model_list.find(item => item.name === this.form_data.model)
          api.yop_modalManagement_modal_detail({id})
            .then((res) => {
              const response = res.data
              if (response.status === 'success') {
                const schema = JSON.parse(response.data.result.schema)
                const bizOrderVariable = response.data.result.bizOrderVariable
                const sensitiveVariables = response.data.result.sensitiveVariables
                const propertiesDetails = schema.properties;
                var arr = []
                for (let i in propertiesDetails) {
                  let o = {};
                  o['name'] = i;
                  for (var k in schema.required) {
                    var requiredName = schema.required[k]
                    if (requiredName === i) {
                      o['required'] = true
                    }
                  }
                  for (var j in propertiesDetails[i]) {
                    o[j] = propertiesDetails[i][j]
                  }
                  arr.push(o)
                }
                modal.current_panel_set('detail_model');
                var detailData = {
                  type: schema.type,
                  title: schema.title,
                  name,
                  apiGroup: this.form_data_basics.apiGroup,
                  description,
                  data_propertiesList: arr,
                  extensions: schema.extensions,
                }
                modal.form_data_set(detailData, bizOrderVariable, sensitiveVariables, 'detail');
                modal.show();
              } else {
                this.$Modal.error({
                  title: '错误',
                  content: response.message
                });
              }
            }
            );
        },
        updateModel (data, type) {
          if (type === 'create_model') {
            api.yop_modalManagement_modal_create(data)
              .then((response) => {
                if (response.status === 'success') {
                  this.$ypMsg.notice_success(this, '创建成功');
                  this.$refs.AddModelModal.cancel();
                  this.getModels()
                } else {
                  this.$Modal.error({
                    title: '错误',
                    content: response.message
                  });
                }
              }
              )
          }
        },
        editModel ({ row }) {
          this.$refs.AddModelModal.cancel();
          this.$router.push({ name: '/model/list', query: { data_interface_name: row.name } })
        },
        /**
             * 基础界面方法
             */
        // 初始化函数
        init () {
          api.yop_dashboard_apis_list().then(
            (response) => {
              this.data_apiGroup_List = [];
              if (this.current_status === 'create') {
                this.api_group_model = '';
              }
              let apigroup = response.data.data.result;
              // this.api_group_model= apigroup[0].groupCode;
              for (var i in apigroup) {
                this.data_apiGroup_List.push(
                  {
                    label: apigroup[i].apiGroupCode + '(' + apigroup[i].apiGroupName + ')',
                    value: apigroup[i].apiGroupCode,
                    name: apigroup[i].apiGroupName
                  }
                )
              }
            }
          );
    
          //* **
          // console.log(this.all_params_new.result.operation.requestBody);
          // api类型列表
          api.yop_apiManagement_apiCommonTypeList().then(
            (response) => {
              let resultTemp = response.data.data.result
              this.data_apiType_List = [];
              let dataTemp = [];
              for (var i in resultTemp) {
                dataTemp.push({
                  value: resultTemp[i].value,
                  label: resultTemp[i].desc
                })
              }
              this.data_apiType_List = dataTemp;
            }
          );
          if (this.current_status === 'modify' || this.current_status === 'description') {
            this.getInfo('showModel');
          }
        },
        // 符合类型的spi name
        isSpiname (value) {
          // ([a-zA-Z][a-zA-Z\d_]*\.)*[a-zA-Z][a-zA-Z\d_]*
          // /^[a-z].*[a-z]$/
          var reg = /^[a-z].*[a-z]$/
          if (reg.test(value)) {
            return true
          } else {
            return false
          }
        },
        // 判断是否是json
        isJson (str) {
          if (typeof str == 'string') {
            try {
              var obj = JSON.parse(str);
              if (typeof obj == 'object' && obj) {
                return true;
              } else {
                return false;
              }
            } catch (e) {
              console.log('error：' + str + '!!!' + e);
              return false;
            }
          }
          console.log('It is not a string!')
        },
        // 获取model
        getModels (showModel) {
          var param = {apiGroup: this.form_data_basics.apiGroup}
          this.form_data.model = '';
          this.$refs.modal_apiM_select_4.clearSingleSelect();
          this.getModelsList(param, showModel)
        },
        getModelsList (param, showModel) {
          api.yop_modalManagement_modal_simple_list(param).then(
            (response) => {
              var response = response.data
              this.data_api_model_list = response.data.result
              if (showModel == 'showModel') {
                var schema = this.form_data.requestBody.contents['application/json'].schema;
                this.form_data.model = JSON.parse(schema)['$ref'].split('/').pop();
              }
            }
          );
        },
    
        // 新增example
        add_example () {
          // console.log(this.$refs.solution_in.getContent(), this.$refs.solution_in.getContent_text())
          this.$refs.form_data_example.resetFields()
          this.modal_Show_example = true;
          this.editExample = false
        },
        format_json (value) {
          if (!value) {
            return
          }
          var json = JSON.parse(value);
          var that = this
          if (typeof json != 'string') {
            json = JSON.stringify(json, null, 4);
          }
          json = json.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
    
          return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
            function (match) {
              var cls = 'number';
              if (/^"/.test(match)) {
                if (/:$/.test(match)) {
                  cls = 'key';
                } else {
                  cls = 'string';
                }
              } else if (/true|false/.test(match)) {
                cls = 'boolean';
              } else if (/null/.test(match)) {
                cls = 'null';
              }
              that.form_data_example.value = json
              return match;
            }
          );
        },
        ok_example () {
          this.$refs.form_data_example.validate((valid) => {
            if (valid) {
              var example = {
                name: this.form_data_example.name,
                // title:this.form_data_example.title,
                description: this.form_data_example.description,
                value: this.form_data_example.value
              }
              if (!this.editExample) {
                this.data_spi_example_List.unshift(example)
              } else {
                this.data_spi_example_List.splice(this.row_index, 1, example)
              }
              this.modal_Show_example = false;
            } else {
              this.form_validate_failed()
            }
          })
        },
        form_validate_failed () {

        },
        cancel_example () {
          this.modal_Show_example = false;
        },
        example_modify (row, index) {
          this.$refs.form_data_example.resetFields()
          this.editExample = true
          var name = row.name;
          // var title = row.title;
          var description = row.description;
          var value = row.value;
          this.form_data_example.name = name;
          this.form_data_example.description = description;
          this.form_data_example.value = value;
          this.row_index = index;
          this.modal_Show_example = true;
        },
        example_delete (index) {
          this.$Modal.confirm({
            title: '提示',
            content: '确认删除示例么？',
            'ok-text': '确认',
            onOk: () => {
              this.data_spi_example_List.splice(index, 1);
            }
          });
        },
        searchModel () {
          console.log(this.form_data.model)
        },
        selectModel () {
        },
        showContentPart () {
          if (this.form_data.contentType === 'application/json') {
            this.showContent = true;
          }
        },
    
        // 获取信息函数
        getInfo (showModel) {
          this.detailLoading = true
          api.yop_spiManagement_spi_detail({id: localStorage.spiId}).then(
            (response) => {
              var response = response.data
              if (response.status === 'success') {
                this.form_data_basics = response.data.result.basic
                var form_data = response.data.result.request
                this.data_spi_example_List = form_data.requestBody.contents['application/json'].examples || []
                setTimeout(() => {
                  this.current_status === 'modify' && this.$refs.solution_in.setContent(this.form_data_basics.description)
                }, 500)
                this.form_data = form_data
                this.form_data.contentType = 'application/json';
                this.form_data.description = form_data.requestBody.description
                this.getModels(showModel);
              }
              if (response.status === 'error') {
                this.$Modal.error({
                  title: '错误',
                  content: response.message
                });
              }
              this.detailLoading = false
            }
          );
        },
    
        // 保存按钮调用方法
        setCurrentStatus (val) {
          this.current_status = val
        },
        setParam (form_data_basics, form_data) {
          console.log(form_data, 'form_dataform_data')
          if (this.current_status == 'modify') {
            var param = {
              id: localStorage.spiId,
              version: localStorage.spiVersion,
              basic: {
                'apiGroup': form_data_basics.apiGroup,
                'title': form_data_basics.title,
                'description': this.$refs.solution_in.getContent()
              },
              request: {
                requestUrl: form_data.requestUrl,
                httpMethod: form_data.httpMethod,
                requestBody: {
                  description: form_data.description,
                  contents: {
                    'application/json': {
                      schema: `{"$ref":"#/components/schemas/${form_data.model}"}`,
                      examples: this.data_spi_example_List
                    }
                  }
                }
              }
            }
          } else {
            var param = {
              basic: {
                'apiGroup': form_data_basics.apiGroup,
                'name': `${form_data_basics.apiGroup}.${form_data_basics.name}`,
                'title': form_data_basics.title,
                'spiType': form_data_basics.spiType, // 单选按钮，选择
                'description': this.$refs.solution_in.getContent_text()
              },
              request: {
                requestUrl: form_data.requestUrl,
                httpMethod: form_data.httpMethod,
                requestBody: {
                  description: form_data.description,
                  contents: {
                    'application/json': {
                      schema: `{"$ref":"#/components/schemas/${form_data.model}"}`,
                      examples: this.data_spi_example_List
                    }
                  }
                }
              }
            }
          }
    
          return param
        },
        save () {
          var basicsSubmit = false
          var paramsSubmit = false
          if (this.$refs.solution_in.getContent_text()) {
            this.form_data_basics.description = this.$refs.solution_in.getContent_text()
          }
          this.$refs.form_data_basics.validate((valid) => {
            if (valid) {
              basicsSubmit = true;
            } else {
              basicsSubmit = false;
              this.$Message.error('请检查基本信息');
            }
          })
          this.$refs.form_data.validate((valid) => {
            if (valid) {
              paramsSubmit = true;
            } else {
              paramsSubmit = false;
              this.$Message.error('请检查参数');
            }
          })

          var param = {}
          // delete this.form_data["contentType"];
          if (paramsSubmit && basicsSubmit) {
            param = this.setParam(this.form_data_basics, this.form_data);
            if (this.current_status === 'create') {
              api.yop_spiManagement_spi_create(param).then(
                (response) => {
                  if (response.status === 'success') {
                    this.$Modal.success({
                      title: '成功',
                      content: '结果通知创建成功，确认无误后【启用】该通知'
                    });
                    this.returnList();
                  }
                  if (response.status === 'error') {
                    this.$Modal.error({
                      title: '错误',
                      content: response.message
                    });
                  }
                }
              );
            }
            if (this.current_status === 'modify') {
              api.yop_spiManagement_spi_update(param).then(
                (response) => {
                  // console.log(response)
                  if (response.status === 'success') {
                    this.$Modal.success({
                      title: '成功',
                      content: '通知修改成功'
                    });
                    this.returnList();
                  }
                  if (response.status === 'error') {
                    this.$Modal.error({
                      title: '错误',
                      content: response.message
                    });
                  }
                }
              );
            }
          } else {
            this.$Modal.error({
              title: '错误',
              content: '请检查信息是否填写完全'
            });
          }
        },
        // 类型和格式匹配
        defaultedFormat (type, array) {
          for (var i in array) {
            if (type === array[i].value) {
              for (var j in array[i].children) {
                if (array[i].children[j].defaulted === true) {
                  return array[i].children[j].value
                }
              }
            }
          }
        },
        // 端点参数返回处理
        ParamHandle (endpointParam) {
          if (endpointParam || (endpointParam === 0)) {
            return endpointParam
          } else {
            return ''
          }
        },
        // 参数返回处理带1子参数
        ParamHandle1 (param, subParams) {
          if (param) {
            if (subParams === 'pattern') {
              if (param.pattern || param.pattern === 0) {
                return param.pattern
              } else {
                return '';
              }
            } else {
              return '';
            }
          } else {
            return '';
          }
        },
        // 参数返回处理函数布尔格式带子参数
        ParamHandle_boolean2 (param, subParams) {
          if (param) {
            if (subParams === 'notNull') {
              if (param.notNull) {
                return param.notNull
              } else {
                return false;
              }
            } else {
              return false;
            }
          } else {
            return false;
          }
        },
        // 参数返回处理带2子参数
        ParamHandle2 (param, subParams, subSubParams) {
          if (param) {
            if (subParams === 'length') {
              if (param.length) {
                if (subSubParams === 'min') {
                  if (param.length.min || param.length.min === 0) {
                    return param.length.min;
                  } else {
                    return '';
                  }
                } else if (subSubParams === 'max') {
                  if (param.length.max || param.length.max === 0) {
                    return param.length.max;
                  } else {
                    return '';
                  }
                } else {
                  return '';
                }
              } else {
                return '';
              }
            } else if (subParams === 'range') {
              if (param.range) {
                if (subSubParams === 'min') {
                  if (param.range.min || param.range.min === 0) {
                    return param.range.min;
                  } else {
                    return '';
                  }
                } else if (subSubParams === 'max') {
                  if (param.range.max || param.range.max === 0) {
                    return param.range.max;
                  } else {
                    return '';
                  }
                } else {
                  return '';
                }
              } else {
                return '';
              }
            } else {
              return '';
            }
          } else {
            return '';
          }
        },
        // 参数返回处理函数布尔格式
        ParamHandle_boolean (param) {
          if (param) {
            return param
          } else {
            return false;
          }
        },
        // 格式返回处理
        formatHandle (format, type) {
          if (format || (format === 0)) {
            return format;
          } else {
            return this.defaultedFormat(type, this.cascader_type);
          }
        },
        // 遍历返回请求参数json结果生成默认数据
        editinlineDataFormat (array) {
          let finalData = [];
          for (var i in array) {
            let temp = array[i]
            if (temp.children && temp.children.length > 0) {
              finalData.push(
                {
                  originalID: Number(i),
                  children: this.editinlineDataFormat(temp.children),
                  params: temp.paramName,
                  dtoClassName: this.ParamHandle(temp.dtoClassName),
                  name: this.ParamHandle(temp.paramTitle),
                  type: this.formatHandle(temp.paramDataFormat, temp.paramDataType),
                  parentType: temp.paramDataType,
                  must: this.ParamHandle_boolean2(temp.constrains, 'notNull'),
                  // must : false,
                  description: this.ParamHandle(temp.description),
                  example: this.ParamHandle(temp.sampleValue),
                  defaultValue: this.ParamHandle(temp.defaultValue),
                  sampleValue: '', //
                  internal: this.ParamHandle_boolean(temp.internal), // 是否内部变量
                  sensitive: this.ParamHandle_boolean(temp.sensitive), // 是否敏感字段
                  paramDataFormat: this.ParamHandle(temp.paramDataFormat), // 格式
                  endParamIndex: this.ParamHandle(temp.endParamIndex), // 端点参数顺序
                  endParamName: this.ParamHandle(temp.endParamName), // 端点参数名称
                  inheritFromGroup: '', // 继承自api分组
                  bucket: '', //
                  fileName: '', //
                  length: {min: this.ParamHandle2(temp.constrains, 'length', 'min'), max: this.ParamHandle2(temp.constrains, 'length', 'max')}, // minexclusive是啥
                  range: { min: this.ParamHandle2(temp.constrains, 'range', 'min'), max: this.ParamHandle2(temp.constrains, 'range', 'max') },
                  pattern: this.ParamHandle1(temp.constrains, 'pattern') // 正则
                }
              );
            } else {
              finalData.push(
                {
                  originalID: Number(i),
                  children: [],
                  params: temp.paramName,
                  dtoClassName: this.ParamHandle(temp.dtoClassName),
                  name: this.ParamHandle(temp.paramTitle),
                  type: this.formatHandle(temp.paramDataFormat, temp.paramDataType),
                  parentType: temp.paramDataType,
                  must: this.ParamHandle_boolean2(temp.constrains, 'notNull'),
                  // must : false,
                  description: this.ParamHandle(temp.description),
                  example: this.ParamHandle(temp.sampleValue),
                  defaultValue: this.ParamHandle(temp.defaultValue),
                  sampleValue: '', //
                  internal: this.ParamHandle_boolean(temp.internal), // 是否内部变量
                  sensitive: this.ParamHandle_boolean(temp.sensitive), // 是否敏感字段
                  paramDataFormat: this.ParamHandle(temp.paramDataFormat), // 格式
                  endParamIndex: this.ParamHandle(temp.endParamIndex), // 端点参数顺序
                  endParamName: this.ParamHandle(temp.endParamName), // 端点参数名称
                  inheritFromGroup: '', // 继承自api分组
                  bucket: '', //
                  fileName: '', //
                  length: {min: this.ParamHandle2(temp.constrains, 'length', 'min'), max: this.ParamHandle2(temp.constrains, 'length', 'max')}, // minexclusive是啥
                  range: { min: this.ParamHandle2(temp.constrains, 'range', 'min'), max: this.ParamHandle2(temp.constrains, 'range', 'max') },
                  pattern: this.ParamHandle1(temp.constrains, 'pattern') // 正则
                }
              );
            }
          }
          return finalData;
        },
        // 遍历返回请求参数form结果生成默认数据
        editinlineDataFormat_form (array) {
          let finalData = [];
          for (var i in array) {
            let temp = array[i]
            finalData.push(
              {
                params: temp.paramName,
                name: this.ParamHandle(temp.paramTitle),
                type: this.formatHandle(temp.paramDataFormat, temp.paramDataType),
                parentType: temp.paramDataType,
                must: this.ParamHandle_boolean2(temp.constrains, 'notNull'),
                // must : false,
                description: this.ParamHandle(temp.description),
                example: this.ParamHandle(temp.sampleValue),
                defaultValue: this.ParamHandle(temp.defaultValue),
                sampleValue: '', //
                internal: this.ParamHandle_boolean(temp.internal), // 是否内部变量
                sensitive: this.ParamHandle_boolean(temp.sensitive), // 是否敏感字段
                paramDataFormat: this.ParamHandle(temp.paramDataFormat), // 格式
                endParamIndex: this.ParamHandle(temp.endParamIndex), // 端点参数顺序
                endParamName: this.ParamHandle(temp.endParamName), // 端点参数名称
                inheritFromGroup: '', // 继承自api分组
                bucket: '', //
                fileName: '', //
                length: {min: this.ParamHandle2(temp.constrains, 'length', 'min'), max: this.ParamHandle2(temp.constrains, 'length', 'max')}, // minexclusive是啥
                range: { min: this.ParamHandle2(temp.constrains, 'range', 'min'), max: this.ParamHandle2(temp.constrains, 'range', 'max') },
                pattern: this.ParamHandle1(temp.constrains, 'pattern') // 正则
              }
            );
          }
          return finalData;
        },
        // 遍历返回请求参数json结果生成默认数据
        editinlineDataFormat_response (array) {
          let finalData = [];
          for (var i in array) {
            let temp = array[i]
            if (temp.children && temp.children.length > 0) {
              finalData.push(
                {
                  originalID: Number(i),
                  children: this.editinlineDataFormat_response(temp.children),
                  params: temp.paramName,
                  dtoClassName: this.ParamHandle(temp.dtoClassName),
                  name: this.ParamHandle(temp.paramTitle),
                  type: this.formatHandle(temp.paramDataFormat, temp.paramDataType),
                  parentType: temp.paramDataType,
                  description: this.ParamHandle(temp.description),
                  example: this.ParamHandle(temp.sampleValue),
                  sensitive: this.ParamHandle_boolean(temp.sensitive) // 是否敏感字段
                }
              );
            } else {
              finalData.push(
                {
                  originalID: Number(i),
                  children: [],
                  params: temp.paramName,
                  dtoClassName: this.ParamHandle(temp.dtoClassName),
                  name: this.ParamHandle(temp.paramTitle),
                  type: this.formatHandle(temp.paramDataFormat, temp.paramDataType),
                  parentType: temp.paramDataType,
                  description: this.ParamHandle(temp.description),
                  example: this.ParamHandle(temp.sampleValue),
                  sensitive: this.ParamHandle_boolean(temp.sensitive) // 是否敏感字段
                }
              );
            }
          }
          return finalData;
        },
        // 判断数组是否包含某值
        IsInArray (arr, val) {
          var testStr = ',' + arr.join(',') + ',';
          return testStr.indexOf(',' + val + ',') != -1;
        },
    
        // 修改接口uri
        modify_apiUri (value) {
          if (this.current_status === 'create') {
            let arr = this.interface_uri.split('/');
            arr[3] = value;
            let interface_uri_temp = '';
            for (var i in arr) {
              if (Number(i) === (arr.length - 1)) {
                interface_uri_temp = interface_uri_temp + arr[i]
              } else {
                interface_uri_temp = interface_uri_temp + arr[i] + '/';
              }
            }
            this.interface_uri = interface_uri_temp;
            this.apiUriCheck();// api校验
          }
          this.errorCodeSet(value);
          this.safetyRequestSet(value);
        },
        // 安全需求分组获取
        safetyRequestSet (value) {
          api.yop_apiManagement_safetyRequest({
            groupCode: value
          }).then(
            (response) => {
              let currentSR = response[0].data.data.apiGroup.securities;
              let totalSR = response[1].data.data.result;
              let dataTemp = this.format_safeRequest(currentSR, totalSR);
              this.$refs.safetyRequest.setDataCommon(dataTemp);
            }
          )
        },
        // 安全需求继承分组遍历全部数组分组处理函数
        format_safeRequest (items, array) {
          let dataTemp = [];
          for (var p in items) {
            for (var i in array) {
              if (array[i].name === items[p].name) {
                if (array[i].custom) {
                  dataTemp.push({
                    name: items[p].name,
                    custom: this.selfDefine_format(items[p].custom, array[i].custom),
                    desc: array[i].desc
                  })
                } else {
                  dataTemp.push({
                    name: items[p].name,
                    desc: array[i].desc
                  })
                }
              }
            }
          }
          return dataTemp;
        },
        // 安全需求自定义分组处理函数
        selfDefine_format (customGet, customTotal) {
          let dataReturn = [];
          for (var i in customTotal) {
            if (customGet) {
              if (this.IsInArray(customGet, customTotal[i])) {
                dataReturn.push({
                  name: customTotal[i],
                  check: true
                })
              } else {
                dataReturn.push({
                  name: customTotal[i],
                  check: false
                })
              }
            } else {
              dataReturn.push({
                name: customTotal[i],
                check: false
              })
            }
          }
          return dataReturn;
        },
        // 安全需求api遍历全部数组分组处理函数
        format_safeRequest_spec (items, array) {
          let dataTemp = [];
          for (var i in array) {
            if (items) {
              let nameTemp = []
              for (var j in items) {
                nameTemp.push(items[j].name);
              }
              if (this.IsInArray(nameTemp, array[i].name)) {
                for (var p in items) {
                  if (array[i].name === items[p].name) {
                    if (array[i].custom) {
                      dataTemp.push({
                        name: items[p].name,
                        custom: this.selfDefine_format(items[p].custom, array[i].custom),
                        desc: array[i].desc,
                        check: true
                      })
                    } else {
                      dataTemp.push({
                        name: items[p].name,
                        desc: array[i].desc,
                        check: true
                      })
                    }
                  }
                }
              } else {
                if (array[i].custom) {
                  dataTemp.push({
                    name: array[i].name,
                    custom: this.selfDefine_format(false, array[i].custom),
                    desc: array[i].desc,
                    check: false
                  })
                } else {
                  dataTemp.push({
                    name: array[i].name,
                    desc: array[i].desc,
                    check: false
                  })
                }
              }
            } else {
              if (array[i].custom) {
                dataTemp.push({
                  name: array[i].name,
                  custom: this.selfDefine_format(false, array[i].custom),
                  desc: array[i].desc,
                  check: false
                })
              } else {
                dataTemp.push({
                  name: array[i].name,
                  desc: array[i].desc,
                  check: false
                })
              }
            }
          }
          return dataTemp;
        },
        // 获取API分组通用错误码并设置
        errorCodeSet (value) {
          this.$refs.errorCode.show_loading_errCommon = true;
          // api.yop_apiManagement_apiGroupDetail({
          //     groupCode : value
          // }).then(
          //     (response) => {
          //         let data =this.editinlineDataFormat_errorCode(response.data.data.apiGroup.errorCodes);
          //         this.$refs.errorCode.setDataCommon(data);
          //     }
          // )

          this.$refs.errorCode.setDataCommon(value, this.data_name_handler(value));
        },
        // 返回相应编码的名字
        data_name_handler (code) {
          if (code) {
            let group = this.api_group_list;
            for (var i in group) {
              if (group[i].value === code) {
                return group[i].name;
              }
            }
          } else {
            return '';
          }
        },
        // 错误码common格式处理
        editinlineDataFormat_errorCode (array) {
          let finalData = [];
          for (var i in array) {
            let temp = array[i]
            finalData.push(
              {
                errorCode: temp.code,
                subCode: temp.subCode,
                subMsg: temp.subMsg,
                solution: temp.solution,
                errorCodeType: '通用错误码'
              }
            );
          }
          return finalData;
        },
    
        // apiuri 校验函数
        apiUriCheck () {
          let params = {
            groupCode: this.api_group_model,
            apiUri: this.interface_uri
          }
          api.yop_apiManagement_apiConfig_apiUriValidate(params).then(
            (response) => {
              if (response.data.status !== 'success') {
                this.$ypMsg.notice_warning(this, response.data.message, 'apiUri校验不通过请修改');
                this.check_result = false;
              } else {
                this.check_result = true;
              }
            }
          )
        },
        // apiUri onblur调用参数
        onblur_apiUri () {
          if (this.current_content_apiUri === this.interface_uri) {

          } else {
            if (this.current_status === 'create') {
              this.apiUriCheck();// api校验
            }
          }
        },
        // apiUri onfocus 调用方法
        onFocus_apiUri () {
          this.current_content_apiUri = this.interface_uri;
        },

        // 返回api列表
        returnList () {
          // this.$refs.te.destroySelf();
          // var el =document.querySelectorAll('textarea');
          // for(var i =0;i<el.length;i++){
          //     el[i].removeAttribute('id');
          // }
          // this.$refs.request.removeTextEditorId();
          // this.$destroy();
          this.$store.commit('removeTag', 'api_basics');
          this.$store.commit('closePage', 'api_basics');
          this.$router.replace({
            name: '/spi/list'
          });
        },

        /**
             * 新建弹窗方法
             */
        // 点击自定义checkbox
        check_url (data) {
          this.show_pointUrl = data;
        },
        // 点击选择端点类型触发函数
        selected_type (value) {
          if (value === 'LOCAL') {
            this.data_EndpointProtocol_List = [
              {
                value: 'SPRING',
                label: 'spring'
              }];
            this.data_select_EndpointProtocol = 'SPRING';
          } else if (value === 'PASSTHROUGH') {
            this.data_EndpointProtocol_List = [
              {
                value: 'HTTP',
                label: 'http'
              }];
            this.data_select_EndpointProtocol = 'HTTP';
          } else if (value === 'TRANSFORM') {
            this.data_EndpointProtocol_List = [
              {
                value: 'HESSIAN',
                label: 'hessian'
              }];
            this.data_select_EndpointProtocol = 'HESSIAN';
          }
        },
    
        // url校验
        IsUrl (str_url) {
          let strRegex = '^((https|http|ftp|rtsp|mms)?://)' +
                '?(([0-9a-z_!~*\'().&=+$%-]+: )?[0-9a-z_!~*\'().&=+$%-]+@)?' + // ftp的user@
                '(([0-9]{1,3}.){3}[0-9]{1,3}' + // IP形式的URL- **************
                '|' + // 允许IP和DOMAIN（域名）
                '([0-9a-z_!~*\'()-]+.)*' + // 域名- www.
                '([0-9a-z][0-9a-z-]{0,61})?[0-9a-z].' + // 二级域名
                '[a-z]{2,6})' + // first level domain- .com or .museum
                '(:[0-9]{1,4})?' + // 端口- :80
                '((/?)|' + // a slash isn't required if there is no file name
                '(/[0-9a-z_!~*\'().;?:@&=+$,%#-]+)+/?)$';
          let re = new RegExp(strRegex);
          if (re.test(str_url)) {
            return true;
          } else {
            return false;
          }
        }

      },
      created () {
        // if(location.href.indexOf("api_basics?apiId=") != -1){
        //     var apiId=location.href.substring(location.href.indexOf("api_basics?apiId=")+17,location.href.length);
        //     this.current_apiId = apiId;
        //     this.current_status = 'modify';
        // }else if(location.href.indexOf("home?apiId=") != -1){
        //     var apiId=location.href.substring(location.href.indexOf("home?apiId=")+11,location.href.length);
        //     this.current_apiId = apiId;
        //     this.current_status = 'modify';
        // }else{
        //     this.current_status = 'create';
        // }
      },
      mounted () {
        if (localStorage.spiInfo) {
          if (String(localStorage.spiInfo) === 'create') {
            this.current_status = 'create';
          } else if (String(localStorage.spiInfo) === 'modify') {
            // var apiId=location.href.substring(location.href.indexOf("api_basics?apiId=")+17,location.href.length);
            this.current_spiId = localStorage.spiId;
            this.current_spiVersion = localStorage.spiVersion
            this.current_status = 'modify';
            this.showContent = true;
          } else if (String(localStorage.spiInfo) === 'description') {
            this.current_spiId = localStorage.spiId;
            this.current_spiVersion = localStorage.spiVersion
            this.current_status = 'description';
            this.showContent = true;
          } else {
            this.current_status = 'create';
          }
        } else {
          this.current_status = 'create';
        }
        this.init();
      },
      beforeRouteLeave (to, from, next) {
        this.$destroy();
        next();
      },
      beforeDestroy () {
        // console.log(this.$refs.te);
        // this.$refs.te.destroySelf();
      },
      destroyed () {
        // this.$store.commit('closePage', 'api_basics');
      }
    };
</script>

<style scoped>
	/*.ivu-collapse {*/
	/*background-color : #FFFFFF;*/
	/*}*/
</style>
