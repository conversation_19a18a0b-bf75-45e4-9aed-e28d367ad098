<style lang="less">
	@import '../../styles/common.less';
	@import 'api_Mangement_Open.less';
	.demo-badge-alone {
		background: #5cb85c !important;
	}
	.round {
		width: 16px;
		height: 16px;
		display: inline-block;
		font-size: 20px;
		line-height: 16px;
		text-align: center;
		color: #f00;
		text-decoration: none;
	}
	.ivu-checkbox + span {
		// display:none;
	}
	.reasonType {
		vertical-align: top;
		display: inline-block;
		font-size: 14px;
		margin-top: 5px;
	}
	.ivu-modal-confirm-body-icon.ivu-modal-confirm-body-icon-warning + div {
		overflow-wrap: break-word;
	}
	.spi_show .ivu-modal-footer {
		border-top: 1px solid #e9eaec !important;
	}
	.noArrow .ivu-icon-arrow-right-b {
		visibility: hidden !important;
	}
</style>
<template>
	<div style="background: #eee;padding:8px">
		<Card :bordered="false">
			<Row type="flex" align="middle">
				<Col span="20">
					<Col span="7">
						<Col span="7" class="margin-top-10">
							<span>通知编码:</span>
						</Col>
						<Col span="17">
							<Input
								id="input_apiM_2"
								class="margin-top-5"
								clearable
								v-model="data_spi_name"
								placeholder="通知编码"
								@on-enter="search_Interface"
							></Input>
						</Col>
					</Col>

					<Col offset="1" span="7">
						<Col span="7" class="margin-top-10">
							<span>通知类型:</span>
						</Col>
						<Col span="17">
							<Select
								ref="select_apiM_3"
								id="select_apiM_3"
								class="margin-top-5"
								v-model="data_select_spiType"
								placeholder="请选择（默认全部）"
								clearable
							>
								<Option
									v-for="item in data_spiType_List"
									:value="item.value"
									:key="item.value"
								>{{ item.label }}</Option>
							</Select>
						</Col>
					</Col>
					<Col offset="1" span="7">
						<Col span="7" class="margin-top-10">
							<span>API分组:</span>
						</Col>
						<Col span="17">
							<Select
								ref="select_apiM_0"
								id="select_apiM_0"
								class="margin-top-5"
								v-model="data_select_apiGroup"
								filterable
								clearable
								placeholder="请选择（默认全部）"
							>
								<Option
									v-for="item in data_apiGroup_List"
									:value="item.value"
									:key="item.label"
								>{{ item.label }}</Option>
							</Select>
						</Col>
					</Col>

					<Col class="margin-top-5" offset="1" span="7"></Col>
				</Col>
				<Col span="4">
					<Col class="margin-top-10" span="11" style="text-align:center">
						<Button id="btn_apiM_1" type="primary" @click="search_Interface">查询</Button>
					</Col>
					<Col class="margin-top-10" span="11" style="text-align:center">
						<Button id="btn_apiM_2" type="ghost" @click="reset_Interface">重置</Button>
					</Col>
				</Col>
			</Row>
			<!--<input id="fileinput" @change="uploading($event)" type="file" accept="image/*">-->
			<Row class="margin-top-20">
				<Col span="12">
					<!-- v-url="{url:'/rest/spi/import/analysis'}"  -->
					<Button
						id="btn_apiM_4"
						type="ghost"
						v-url="{url:'/rest/spi/import/analysis'}"
						@click="interface_Import"
					>导入通知</Button>
					<Button
						id="btn_apiM_5"
						class="margin-left-10"
						v-url="{url:'/rest/spi/export'}"
						type="ghost"
						@click="interface_Export"
					>导出通知</Button>
					<!-- v-url="{url:'/rest/spi/export'}" -->
					<!-- v-url="{url:'/rest/spi/create'}" -->

					<Button
						class="margin-left-10"
						type="primary"
						v-url="{url:'/rest/spi/create'}"
						@click="interface_add"
					>新增通知</Button>
				</Col>
			</Row>
			<Row class="margin-top-10">
				<Col span="24">
					<Table
						id="table_1"
						border
						ref="selection"
						:columns="columns_ApiInterfaceList"
						:data="data_ApiInterfaceList"
						@on-selection-change="handleselection"
					></Table>
					<Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
						<Page
							class="margin-top-10"
							style="float: right"
							:total="pageTotal"
							:page-size="10"
							:current="pageNo"
							show-elevator
							@on-change="pageRefresh"
						></Page>
					</Tooltip>
				</Col>
				<!--<Spin fix v-show="show_loading_apiList">-->
				<!--<Icon type="load-c" size=18 class="yop-spin-icon-load"></Icon>-->
				<!--<div>数据加载中...</div>-->
				<!--</Spin>-->
			</Row>
		</Card>
		<!-- 导入spi -->
		<Modal v-model="import_spi_show" width="500" class="spi_show">
			<p slot="header" style="color:#2d8cf0;">
				<span style="color:black">导入通知</span>
			</p>
			<div>
				<Form ref="importSpi" :model="form_data_spi" :label-width="120">
					<FormItem
						label="API分组:"
						class="width-100-perc"
						prop="data_select_apiGroup_import"
						:rules="{required:true,message:'api分组不能为空'}"
					>
						<Select
							ref="select_apiM_1"
              style="display: inline-block;width:200px;"
							id="select_apiM_1"
							class="margin-top-5"
							v-model="form_data_spi.data_select_apiGroup_import"
							filterable
							clearable
							placeholder="请选择"
						>
							<Option
								v-for="item in data_apiGroup_List"
								:value="item.value"
								:key="item.label"
							>{{ item.label }}</Option>
						</Select>
					</FormItem>
					<FormItem
						label="格式:"
						class="width-100-perc"
						prop="spiType"
						:rules="{required:true,message:'类型不能为空'}"
					>
						<Select
							ref="modal_apiM_select_5"
							style="display: inline-block;width:200px;"
							id="modal_apiM_select_5"
							v-model="form_data_spi.spiType"
							placeholder="类型"
						>
							<Option v-for="item in data_spi_type" :value="item.value" :key="item.value">{{ item.value }}</Option>
						</Select>
					</FormItem>
					<FormItem label="上传文件" prop="fileContent" :rules="{required:true,message:'文件不能为空'}">
						<Upload
							class="margin-right-50"
							style="display: inline-block;"
							v-model="form_data_spi.fileContent"
							ref="upload"
							:action="importIP"
							:name="name_upload"
							:headers="header_upload"
							:show-upload-list="true"
							:on-format-error="handleFormatError"
							:before-upload="handleBeforeUpload"
							:on-progress="handleProgress"
							:on-success="handleSuccess"
							:on-error="handleError"
						>
							<!-- :data="uploadFile"  -->
							<Button id="btn_apiM_4" type="ghost">点击上传文件</Button>
							<span style="margin-left:10px;">{{this.form_data_spi.fileContent.name}}</span>
						</Upload>
						<!-- <Button type="primary" @click="upload" >点击上传文件</Button> -->
					</FormItem>

					<!-- <p class="yop-explain-120">最大支持1000字</!-->
				</Form>
			</div>
			<div slot="footer">
				<Button id="modal_request_btn_2" type="primary" @click="sure_import_spi('importSpi')">分析</Button>
				<Button id="modal_request_btn_1" type="ghost" @click="hide_import_spi">取消</Button>
			</div>
		</Modal>

		<!-- 导出spi -->
		<Modal v-model="export_spi_show" width="500" class="spi_show">
			<p slot="header" style="color:#2d8cf0;">
				<span style="color:black">导出通知</span>
			</p>
			<div>
				<Form ref="exportSpi" :model="form_data_spi_export" :label-width="120">
					<FormItem
						label="API分组:"
						prop="data_select_apiGroup_import"
						:rules="{required:true,message:'api分组不能为空'}"
					>
						<p>{{form_data_spi_export.data_select_apiGroup_import}}</p>
					</FormItem>
					<FormItem
						label="类型:"
						class="width-50-perc"
						prop="spiType"
						:rules="{required:true,message:'类型不能为空'}"
					>
						<Select
							ref="modal_apiM_select_4"
							id="modal_apiM_select_4"
							style="width:200px;"
							v-model="form_data_spi_export.spiType"
							placeholder="类型"
						>
							<Option v-for="item in data_spi_type" :value="item.value" :key="item.value">{{ item.value }}</Option>
						</Select>
					</FormItem>
					<!-- <p class="yop-explain-120">最大支持1000字</!-->
				</Form>
			</div>
			<div slot="footer">
				<Button id="modal_request_btn_2" type="primary" @click="sure_export_spi('exportSpi')">确定</Button>
				<Button id="modal_request_btn_1" type="ghost" @click="hide_export_spi">取消</Button>
			</div>
		</Modal>

		<Modal v-model="modal_Show_register" width="600">
			<p slot="header">
				<span class="margin-right-10">注册 API</span>
				<span style="color:gray;font-size: 12px;">端点服务</span>
			</p>
			<div style="padding-left:30px;">
				<p>
					* &nbsp;是否幂等：
					<RadioGroup id="modal_rbtn_1" v-model="idempotent">
						<Radio label="否"></Radio>
						<Radio label="是"></Radio>
					</RadioGroup>
				</p>
				<p style="font-size: 12px;color: grey; padding-left: 70px;line-height:30px;">幂等API支持回归测试</p>
				<p style="line-height:40px;">
					* &nbsp;适配类型：
					<Select
						id="modal_apiM_select_1"
						size="small"
						v-model="data_select_type"
						style="width:190px"
						placeholder="请选择"
						@on-change="selected_type"
					>
						<Option v-for="item in data_type_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
					</Select>
				</p>
				<p style="line-height:40px;">
					* &nbsp;API分组：
					<common-select
						id="modal_ag_select_1"
						ref="select_ag_m"
						@on-update="updateSelect_apiGroup"
						type="combo"
						holder="请选择"
						keyWord="result"
						code="apiGroupCode"
						title="apiGroupName"
						group="apiGroup"
						@on-loaded="select_callBack"
						:default="this.data_apiGroup"
						size="small"
						:uri="this.$store.state.select.apiGroup.uri"
						style="width:80%"
					></common-select>
				</p>
				<p style="line-height:40px;">
					* &nbsp;API类型：
					<common-select
						id="modal_at_select_1"
						ref="select_at_m"
						@on-update="updateSelect_apiType"
						type="sub"
						holder="请选择"
						keyWord="result"
						code="value"
						title="desc"
						group="api_type"
						subCode="contentTypeMapping"
						@on-loaded="select_callBack"
						:default="this.data_apiType"
						size="small"
						:uri="this.$store.state.select.api_type.uri"
						style="width:80%"
					></common-select>
				</p>
				<p style="line-height:40px;">
					* &nbsp;端点协议：
					<Select
						id="modal_apiM_select_2"
						size="small"
						v-model="data_select_EndpointProtocol"
						style="width:190px"
						placeholder="请选择"
					>
						<Option
							v-for="item in data_EndpointProtocol_List"
							:value="item.value"
							:key="item.value"
						>{{ item.label }}</Option>
					</Select>
				</p>
				<p style="line-height:40px;" v-show="show_pointUrl">
					&nbsp;&nbsp;&nbsp;端点url：&nbsp;&nbsp;&nbsp;
					<Input
						id="modal_apiM_input_1"
						size="small"
						style="width: 380px;"
						v-model="endpoint_Url"
						placeholder="请输入"
						@on-focus="onFocus_url"
						@on-blur="onblur_url"
					></Input>
				</p>
				<p
					style="font-size: 12px;color: grey; padding-left: 75px; line-height: 30px;"
					v-show="show_pointUrl"
				>可指定访问某台服务器，如：10.10.10.10:8080 //com.yeepay.3g.auth.aaa</p>
				<p style="line-height:40px;">
					* &nbsp;端点类名：&nbsp;
					<Input
						id="input_apiM_4"
						size="small"
						style="width: 380px;"
						v-model="endpoint_Name"
						placeholder="请输入"
						@on-focus="onFocus"
						@on-blur="keydown"
					></Input>
					<Checkbox id="modal_apiM_chk_1" v-model="pointUrl_user_defined" @on-change="check_url"></Checkbox>自定义
				</p>
				<p
					style="font-size: 12px;color: grey; padding-left: 75px; line-height: 30px;"
				>如：com.yeepay.g3.facade.auth2.facade.Auth2Facade</p>
				<p style="line-height:40px;">
					* &nbsp;端点方法：
					<Select
						id="modal_apiM_select_3"
						size="small"
						v-model="data_select_EndpointMethod"
						style="width:380px"
						placeholder="根据端点类名字自动加载"
					>
						<Option
							v-for="item in data_EndpointMethod_List"
							:value="item.value"
							:key="item.value"
						>{{ item.label }}</Option>
					</Select>
				</p>
			</div>
			<div slot="footer">
				<Button id="modal_apiM_btn_1" type="ghost" @click="cancel_Newapp">取消</Button>
				<Button id="modal_apiM_btn_2" type="primary" @click="ok_Newapp">下一步</Button>
			</div>
		</Modal>
		<Modal v-model="delete_spi_show" width="500">
			<p slot="header" style="color:#000;text-align:left;font-size: 18px;">
				<Icon type="ios-information-circle"></Icon>
				<span>删除通知</span>
			</p>
			<div style="margin-left:10px;font-size: 12x;">
				<p style="color:red">请确保没有指向spi的引用，否则可能导致文档生成失败</p>
				<label for class="reasonType">
					<span style="color:red">*</span>原因：
				</label>
				<Input
					type="textarea"
					v-model="cause"
					style="width:85%;font-size: 12x;height:200px;"
					placeholder="请输入原因"
				></Input>
			</div>
			<div slot="footer">
				<Button type="primary" @click="hide_Delete_spi">取消</Button>
				<Button type="primary" @click="sure_Delete_spi">确定</Button>
			</div>
		</Modal>

		<Modal v-model="analysis_result_show" width="1000">
			<p slot="header" style="color:#000;text-align:left;font-size: 18px;">
				<Icon type="ios-information-circle"></Icon>
				<span>导入通知结果</span>
			</p>
			<Collapse active-key accordion>
				<Panel>
					待创建通知（{{this.spisToCreateList?this.spisToCreateList.length:0}}）
					<div slot="content">
						<Collapse accordion :key="index.toString()+1" v-for="(item,index) in spisToCreateList">
							<!-- <Checkbox :label="item.spi.basic.name" style="float:left;margin:6px 0 0 4px;"></Checkbox> -->
							<span style="float:left;margin:10px 0 0 4px;">{{item.spi.basic.name}}</span>

							<Panel>
								<span @click="stopProp">
									<span class="yop-api-collapse-title-10" style="float:left"></span>
								</span>
								<p slot="content">
									<spis_to_create ref="spisToCreate"></spis_to_create>
								</p>
							</Panel>
						</Collapse>
					</div>
				</Panel>

				<Panel>
					待覆盖通知（{{this.spisToOverrideList?this.spisToOverrideList.length:0}}）
					<div slot="content">
						<div style v-show="this.spisToOverrideList">
							<Checkbox
								:indeterminate="indeterminateSpisToOverride"
								:value="checkAllSpisToOverride"
								@click.prevent.native="handleCheckAllSpisToOverride"
							>全选</Checkbox>
						</div>
						<CheckboxGroup
							v-model="checkAllGroupSpisToOverride"
							@on-change="checkAllGroupSpisToOverrideChange"
						>
							<Collapse accordion :key="index.toString()+'_1'" v-for="(item,index) in spisToOverrideList">
								<Checkbox :label="item.spi.basic.name" style="float:left;margin:10px 0 0 4px;"></Checkbox>
								<Panel>
									<span @click="stopProp">
										<span class="yop-api-collapse-title-10" style="float:left"></span>
									</span>
									<p slot="content">
										<spis_to_override ref="spisToOverride"></spis_to_override>
									</p>
								</Panel>
							</Collapse>
						</CheckboxGroup>
					</div>
				</Panel>

				<Panel>
					未修改通知（{{this.spisToIgnore?this.spisToIgnore.length:0}}）
					<div slot="content">
						<Collapse accordion :key="index.toString()+0" v-for="(item,index) in spisToIgnore">
							<!-- <Checkbox :label="item.spi.basic.name" style="float:left;margin:6px 0 0 4px;"></Checkbox> -->
							<span style="float:left;margin:10px 0 0 4px;">{{item}}</span>
							<Panel class="noArrow"></Panel>
						</Collapse>
					</div>
				</Panel>
				<Panel>
					待创建model（{{this.modelsToCreateList?this.modelsToCreateList.length:0}}）
					<div slot="content">
						<Collapse accordion :key="index.toString()+'_2'" v-for="(item,index) in modelsToCreateList">
							<!-- <Checkbox :label="item.model.name" style="float:left;margin:10px 0 0 4px;"></Checkbox> -->
							<span style="float:left;margin:10px 0 0 4px;">{{item.model.name}}</span>

							<Panel>
								<span @click="stopProp">
									<span class="yop-api-collapse-title-10" style="float:left"></span>
								</span>
								<p slot="content">
									<models_to_create ref="modelsToCreate"></models_to_create>
								</p>
							</Panel>
						</Collapse>
					</div>
				</Panel>
				<Panel>
					待覆盖model（{{this.modelsToOverrideList?this.modelsToOverrideList.length:0}}）
					<div slot="content">
						<div style v-show="this.modelsToOverrideList">
							<Checkbox
								:indeterminate="indeterminateModelsToOverride"
								:value="checkAllModelsToOverride"
								@click.prevent.native="handleCheckAllModelsToOverride"
							>全选</Checkbox>
						</div>
						<CheckboxGroup
							v-model="checkAllGroupModelsToOverride"
							@on-change="checkAllGroupModelsToOverrideChange"
						>
							<Collapse
								accordion
								:key="index.toString()+'_3'"
								v-for="(item,index) in modelsToOverrideList"
							>
								<Checkbox :label="item.model.name" style="float:left;margin:10px 0 0 4px;"></Checkbox>
								<Panel>
									<span @click="stopProp">
										<span class="yop-api-collapse-title-10" style="float:left"></span>
									</span>
									<p slot="content">
										<models_to_override ref="modelsToOverride"></models_to_override>
									</p>
								</Panel>
							</Collapse>
						</CheckboxGroup>
					</div>
				</Panel>

				<Panel>
					未修改model（{{this.modelsToIgnore?this.modelsToIgnore.length:0}}）
					<div slot="content">
						<Collapse accordion :key="index.toString()+0" v-for="(item,index) in modelsToIgnore">
							<!-- <Checkbox :label="item.spi.basic.name" style="float:left;margin:6px 0 0 4px;"></Checkbox> -->
							<span style="float:left;margin:10px 0 0 4px;">{{item}}</span>
							<Panel class="noArrow"></Panel>
						</Collapse>
					</div>
				</Panel>
				<Panel>
					未引用model（{{this.unusedModels?this.unusedModels.length:0}}）
					<div slot="content">
						<Collapse accordion :key="index.toString()+0" v-for="(item,index) in unusedModels">
							<!-- <Checkbox :label="item.spi.basic.name" style="float:left;margin:6px 0 0 4px;"></Checkbox> -->
							<span style="float:left;margin:10px 0 0 4px;">{{item}}</span>
							<Panel class="noArrow"></Panel>
						</Collapse>
					</div>
				</Panel>
			</Collapse>
			<div slot="footer">
				<Button type="primary" @click="sure_import">确认导入</Button>
				<Button type="ghost" @click="hide_import">取消</Button>
			</div>
			<loading :show="sure_import_spi_loading"></loading>
		</Modal>

		<modal_opt_record ref="modal_opt_record"></modal_opt_record>
		<RelevanceApiModal ref="RelevanceApiModal" />
		<RelevanceMerchantNoModal ref="RelevanceMerchantNoModal" />
	</div>
</template>

<script>
    import commonSelect from '../common-components/select-components/selectCommon';
    import api from'../../api/api'
    import loading from '../my-components/loading/loading';
    import modal_opt_record from "./modify_models/modal_change_record";
    import spis_to_create from './modify_models/spis_to_create'
    import spis_to_override from './modify_models/spis_to_override'
    import models_to_create from './modify_models/models_to_create'
    import models_to_override from './modify_models/models_to_override'
    import RelevanceApiModal from './modify_models/RelevanceApiModal'
    import RelevanceMerchantNoModal from './modify_models/RelevanceMerchantNoModal'
    import util from '../../libs/util'
    // import
    export default {
        name: 'spiList',
        components:{
            loading,
            commonSelect,
            modal_opt_record,
            spis_to_create,
            spis_to_override,
            models_to_create,
            models_to_override,
            RelevanceApiModal,
            RelevanceMerchantNoModal,
        },
        data () {
            return {
                // 覆盖spi
                checkAllGroupSpisToOverrideArr:[],
                checkAllGroupSpisToOverride:[],
                indeterminateSpisToOverride: true,
                checkAllSpisToOverride: false,

                
                // 覆盖model
                checkAllGroupModelsToOverrideArr:[],
                checkAllGroupModelsToOverride:[],
                indeterminateModelsToOverride: true,
                checkAllModelsToOverride: false,
                
                spisToOverrideArr:[],
                modelsToOverrideArr:[], 
                
                exampleResult:{},

                result:{},
                data_Template:{},
                collapse_label: ['0'],
                spisToCreateList:[],
                spisToOverrideList:[],
                modelsToCreateList:[],
                modelsToOverrideList:[],
                spisToIgnore:[],
                modelsToIgnore:[],
                unusedModels:[],

                analysis_result_show:false,
                // 导入spi
                form_data_spi:{
                    data_select_apiGroup_import:"",
                    apiGroup:"",
                    fileContent:"",
                    spiType:""
                },
                // 导出spi
                form_data_spi_export:{
                    data_select_apiGroup_import:"",
                    apiGroup:"",
                    fileContent:"",
                    spiType:""
                },
                data_spi_type:[{value:"YAML"},{value:"JSON"}],
                import_spi_show:false,
                export_spi_show:false,
                import_type:"",
                cause:"",
                delete_spi_show:false,
                data_spi_title:"",
                data_spi_name:"",
                data_select_apiGroup:"",
                data_select_spiType:"",
                data_spiType_List:[{value:"CALLBACK",label:"回调"},{value:"NOTIFICATION",label:"通知"}],
                // api分组数据绑定
                data_apiGroup: '',
                // api类型数据绑定
                data_apiType: '',
                // 导入功能ip动态变量
                importIP : localStorage.remoteIP+'/rest/spi/import/analysis',
                /**
                 * 注册弹窗数据
                 */
                name_upload:'content',
                // 端点url数据绑定
                endpoint_Url : '',
                // 显示端点url部分
                show_pointUrl : false,
                // 端点类名数据绑定
                endpoint_Name: '',
                // 是否幂等数据绑定
                idempotent: '否',
                // 端点url自定义
                pointUrl_user_defined: false,
                // 适配类型下拉框数据绑定
                data_select_type: 'TRANSFORM',
                // 适配类型下拉框数据
                data_type_List: [
                    // {
                    //     value: 'TRANSFORM',
                    //     label: '转换'
                    // },
                    {
                        value: 'MAPPING',
                        label: '映射'
                    },
                    {
                        value: 'PASSTHROUGH',
                        label: '透传'
                    }
                ],
                // 端点协议下拉框数据绑定
                data_select_EndpointProtocol: 'HESSIAN',
                // 端点协议下拉框数据
                data_EndpointProtocol_List: [
                    {
                        value: 'HESSIAN',
                        label: 'hessian'
                    }
                ],
                // 端点方法下拉框数据绑定
                data_select_EndpointMethod: '',
                // 端点方法下拉框数据
                data_EndpointMethod_List: [],
                // 当前端点名称内容
                current_content : '',
                // 当前端点url内容
                current_content_url : '',
                // url校验结果
                pass_url : true,
                /**
                 * 主界面部分数据
                 */
                // 上传header绑定
                header_upload: {
                    Authorization: 'Bearer ' + localStorage.accesstoken
                },
                // api列表加载动画数据绑定
                show_loading_apiList : true,
                // switch点击行数
                switch_index: 0,
                // 注册web api对话框显示
                modal_Show_register: false,
                // APIuri数据绑定
                data_interface_uri : '',
                // API名称
                data_interface_name : '',
                // 状态下拉框数据绑定
                data_select_status: '',
                // 状态下拉框选项数据
                data_status_List: [],
                // api分组下拉框数据绑定
                data_select_apiGroup: '',
                // api分组下拉框数据
                data_apiGroup_List: [],
                // 安全需求下拉框数据绑定
                data_safety_request: '',
                // 安全需求下拉框数据
                data_safetyRequest_List:[],
                // api类型选择数据绑定
                data_select_apiType: '',
                // api类型
                data_apiType_List:[],
                // apiAPI列表列属性
                columns_ApiInterfaceList: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    // {
                    //     title: 'id',
                    //     'min-width': 100,
                    //     key: 'id'
                    // },
                    // {
                    //     title: 'version',
                    //     'min-width': 100,
                    //     key: 'version'
                    // },
                    {
                        renderHeader: h =>{
                            return h ('div',[
                                h('p', '通知名称'),
                                h('p', '通知编码')
                            ])
                        },
                        key: 'nameandtitle',
                        align: 'center',
                        width:200,
                        render: (h, params) => {
                            return h('div', [h('p', params.row.title),
                                h('p', params.row.name)]);
                        }
                    },
                    {
                        title: '通知类型',
                        key: 'spiType',
                        align: 'center',
                        render: (h,params) => {
                            var spiType = ""
                            if(params.row.spiType === "CALLBACK"){
                                spiType = "回调"
                            }
                            if(params.row.spiType === "NOTIFICATION"){
                                spiType = "通知"

                            }
                            return h ('div',spiType)
                        }
                    },
                    {
                        title: 'API分组',
                        key: 'apiGroup',
                        align: 'center',
                        render: (h, params) => {
                            var apiGroupName = ""
                            for(var i in this.data_apiGroup_List){
                                if(this.data_apiGroup_List[i].value == params.row.apiGroup){
                                    apiGroupName = this.data_apiGroup_List[i].name
                                    break;
                                }
                            }
                            return h('div', [h('p', apiGroupName),h('p', "("+params.row.apiGroup+")")]);
                        }
                    },
                    {
                        title: '状态',
                        key: 'status',
                        align: 'center',
                        render: (h,params) => {
                            let status = "--"
                            let color = 'grey'
                            if(params.row.status === "ENABLED"){
                                status = "启用"
                                color = 'green'
                            }
                            if(params.row.status === "DISABLED"){
                                status = "禁用"
                            }
                            return h("div", [
                                h("Tag",
                                    {
                                        style: {align: "center"},
                                        props: {color: color}
                                    },
                                    status
                                )
                            ]);
                        }
                    },
                    {
                        title: '更新日期/创建日期',
                        key: 'time',
                        align: 'center',
                        width:200,
                        render: (h, params) => {
                            return h('div', [h('p', params.row.lastModifiedDateTime),
                                h('p', params.row.createdDateTime)]);
                        }
                    },
                    {
                        title: '操作',
                        key: 'operations',
                        width: 230,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/spi/update'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.interface_modify(params.row);
                                        }
                                    }
                                }, '修改'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/spi/delete'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.interface_delete(params.row.id);
                                        }
                                    }
                                }, '删除'),
                                 h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/spi/detail'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.interface_des(params.row.id)
                                        }
                                    }
                                }, '查看详情'),
                                 h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    directives: [
                                        {
                                            name: 'url',
                                            value: {url: '/rest/spi/update'}
                                        },
                                        {
                                            name: 'show',
                                            value: params.row.status === "ENABLED"
                                        }
                                    ],
                                    on: {
                                        click: () => {
                                            this.$Modal.confirm({
                                                title: '确认禁用？',
                                                content: '<p>禁用后，将隐藏此通知的文档，不影响正常的结果通知。</p>',
                                                onOk: () => {
                                                    this.updateStatus(params.row);
                                                },
                                                onCancel: () => {
                                                }
                                            });
                                        }
                                    }
                                }, '禁用'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    directives: [
                                        {
                                            name: 'url',
                                            value: {url: '/rest/spi/update'}
                                        },
                                        {
                                            name: 'show',
                                            value: params.row.status === "DISABLED"
                                        }
                                    ],
                                    on: {
                                        click: () => {
                                            this.updateStatus(params.row);
                                        }
                                    }
                                }, '启用'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/spi/change-record'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.toChangeRecode(params.row)
                                        }
                                    }
                                }, '变更记录'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.$refs.RelevanceApiModal.showModal(params.row.name)
                                        }
                                    }
                                }, '关联API'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    on: {
                                        click: () => {
                                            this.$refs.RelevanceMerchantNoModal.showModal(params.row.name)
                                        }
                                    }
                                }, '订阅'),
                            ]);
                        }
                    }
                ],
                // 表格数据
                data_ApiInterfaceList: []
                ,
                // 表格选中数据
                multiSelectedData: [],
                testMultiSelectedData:[],
                // 分页单页个数
                pageNo : 1,
                // 分页总数
                pageTotal: 0,
                // 当前查询参数
                current_params : {
                    pageNo : 1,
                    pageSize : 10
                },
                pageSize:10,
                sure_import_spi_loading:false
            };
        },
        methods: {
            // 更新状态
            updateStatus(item) {
                const { name, status } = item
                if(status === 'ENABLED') {
                    api.yop_spiManagement_spi_disable({
                        spiName: name
                    })
                    .then(res => {
                        if (res.status === 'success') {
                            this.pageRefresh()
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: res.message
                            });
                        } 
                    })
                } else {
                    api.yop_spiManagement_spi_enable({
                        spiName: name
                    })
                    .then(res => {
                        if (res.status === 'success') {
                            this.pageRefresh()
                        } else {
                            this.$Modal.error({
                                title: '错误',
                                content: res.message
                            });
                        } 
                    })
                }
            },
            // 全选覆盖spi列表
            handleCheckAllSpisToOverride () {
                if (this.indeterminateSpisToOverride) {
                    this.checkAllSpisToOverride = false;
                } else {
                    this.checkAllSpisToOverride = !this.checkAllSpisToOverride;
                }
                this.indeterminateSpisToOverride = false;
                if (this.checkAllSpisToOverride) {
                    this.checkAllGroupSpisToOverride = this.checkAllGroupSpisToOverrideArr
                    this.checkAllSpisToOverride = true;
                } else {
                    this.checkAllGroupSpisToOverride = [];
                }
            },

                        // 覆盖spi列表
            checkAllGroupSpisToOverrideChange (data) {
                this.checkAllGroupSpisToOverride = data
                if (data.length == this.spisToOverrideList.length) {
                    this.indeterminateSpisToOverride = false;
                    this.checkAllSpisToOverride = true;
                } else if (data.length > 0) {
                    this.indeterminateSpisToOverride = true;
                    this.checkAllSpisToOverride = false;
                } else {
                    this.indeterminateSpisToOverride = false;
                    this.checkAllSpisToOverride = false;
                }
            },

            // 全选覆盖model列表
            
            handleCheckAllModelsToOverride () {
                if (this.indeterminateModelsToOverride) {
                    this.checkAllModelsToOverride = false;
                } else {
                    this.checkAllModelsToOverride = !this.checkAllModelsToOverride;
                }
                this.indeterminateModelsToOverride = false;
                if (this.checkAllModelsToOverride) {
                    this.checkAllGroupModelsToOverride = this.checkAllGroupModelsToOverrideArr;
                    this.checkAllModelsToOverride = true;
                } else {
                    this.checkAllGroupModelsToOverride = [];
                }
            },

                        // 覆盖model列表
            checkAllGroupModelsToOverrideChange (data) {
                this.checkAllModelsToOverride = data
                if (data.length === this.modelsToOverrideList.length) {
                    this.indeterminateModelsToOverride = false;
                    this.checkAllModelsToOverride = true;
                } else if (data.length > 0) {
                    this.indeterminateModelsToOverride = true;
                    this.checkAllModelsToOverride = false;
                } else {
                    this.indeterminateModelsToOverride = false;
                    this.checkAllModelsToOverride = false;
                }
            },





            // 导入
            sure_import(){
                this.sure_import_spi_loading = true

                var param = {
                    apiGroup:this.form_data_spi.data_select_apiGroup_import, 
                    requestId:this.requestId,
                    spisToOverride:this.checkAllGroupSpisToOverride,
                    modelsToOverride:this.checkAllGroupModelsToOverride
                }
                var createLength = 0
                if(!this.spisToCreateList){
                    createLength = 0
                }else{
                    createLength = this.spisToCreateList.length
                }
                if(this.checkAllGroupSpisToOverride.length == 0 && createLength == 0 && this.checkAllGroupModelsToOverride.length === 0){
                    this.$ypMsg.notice_warning(this, "没有导入内容，请重新确认");
                    this.sure_import_spi_loading = false 
                    return false

                }
                api.yop_spiManagement_spi_import(param).then(
                    (response) => {
                        if(response.status === "success"){
                                var total = response.data.result.total
                                var success = response.data.result.success || "0"
                                var failed = response.data.result.failed || "0"
                                var importFailed = response.data.result.failedDetails;
                                var successHtml = '',
                                    failedHtml = '';
                                    successHtml = `<p>导入数量：${total} </p><p>成功数量：${success} </p><p>失败数量：${failed} </p>`
                                    successHtml = '<p>导入成功</p>'+successHtml
                                if(importFailed){
                                    for(var l = 0 ; l< importFailed.length ; l ++){
                                        failedHtml += `<p>项目：${importFailed[l].spiName} ;原因：${importFailed[l].reason}</p>`
                                    }
                                    failedHtml = '<p>导入失败</p'+failedHtml
                                }
                                if(!importFailed){
                                    this.$Modal.success({
                                        title: "详情",
                                        content: successHtml+failedHtml
                                    });
                                }else{
                                    this.$Modal.warning({
                                        title: "详情",
                                        content: successHtml+failedHtml
                                    });
                                }
                                this.analysis_result_show = false
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.message
                            });
                        }
                    this.sure_import_spi_loading = false 

                    }
                )
            },
            hide_import(){
                this.analysis_result_show = false
            },
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
            clear(){
                this.$refs.upload.clearFiles();//清除上次上传记录
            },
            sure_import_spi(val){
                this.$refs[val].validate((valid) => {
                    if (valid) {
                            this.upload()
                            this.import_spi_show = false;
                        } else {
                        this.$Message.error('请检查');
                    }
                })
                // this.$refs.importSpi
            },
            hide_import_spi(){
                this.import_spi_show = false;
            },
            sure_Delete_spi(){
                this.delete_ok();
            },
            hide_Delete_spi(){
                this.delete_spi_show = false;
            },
            sure_rollback_model(){
                if(this.rollback_cause){
                    var param = {
                        id :this.rollbackId,
                        cause :this.rollback_cause,
                    }
                    api.yop_spiManagement_spi_change_record_rollback(param).then(
                            (response) => {
                                var response = response.data
                                if (response.status === 'success') {
                                    this.pageRefresh()
                                    // this.tabledataGet(response.data.page.items);
                                    // this.pageNo = response.data.result.pageNo;
                                    // this.pageTotal = response.data.result.totalPageNum * 10;
                                    this.rollback_modal_show = false;
                                } else {
                                    this.$Modal.error({
                                        title: '错误',
                                        content: response.message
                                    });
                                }

                            }
                        );
                }else{
                    this.$Modal.warning({
                        title: "警告",
                        content: "请输入原因"
                    });
                }
            },
            hide_rollback_model(){
                this.rollback_modal_show = false;
            },
            // 添加按钮调用方法
            interface_add (val) {
                localStorage.spiInfo = 'create';
                this.$router.replace({
                    name: 'spi_basics'
                });
            },
            // 修改按钮调用方法
            interface_modify (val) {
                localStorage.spiInfo = 'modify';
                localStorage.spiId = val.id;  
                localStorage.spiVersion = val.version;   
                this.$router.replace({
                    name: 'spi_basics_edit'
                });
            },
            // 描述按钮调用方法
            interface_des (val) {
                localStorage.spiInfo='description';
                localStorage.spiId = val;
                this.$router.replace({
                    name: 'spi_basics_desc'
                });
            },
            toChangeRecode(row){
                var apiGroup = row.apiGroup
                var name = row.name
                this.$refs.modal_opt_record.reset_search();
                if(apiGroup){
                this.$refs.modal_opt_record.set_appId(apiGroup);
                }
                if(name){
                this.$refs.modal_opt_record.set_appName(name);
                }
                this.$refs.modal_opt_record.modal_preview();
                this.$refs.modal_opt_record.search();
            },
           
            // 删除按钮调用方法
            interface_delete (id) {
                this.deleteId = id
                this.delete_spi_show = true;
                this.cause = ""
            },
            // 启用按钮调用方法
            interface_use (index,operations,id) {
                if(operations){
                    this.$Modal.confirm({
                        title: '提示',
                        content: '禁用API将导致API无法调用，您还要继续吗？',
                        'ok-text':'禁用',
                        onOk: () =>{
                            // this.data_ApiInterfaceList[index].operations = !operations;
                            this.forbidden_ok(index,operations,id);
                        }
                        // onCancel: this.forbidden_cancel(index,operations)
                    });
                }else{
                    this.$Modal.confirm({
                        title: '提示',
                        'ok-text':'启用',
                        content: '您将启用API，请确认是否维护好API信息，您还要继续吗',
                        onOk: () =>{
                            this.enabled_ok(index,operations,id);
                            // this.data_ApiInterfaceList[index].operations = !operations;
                        },
                        // onCancel: this.forbidden_cancel(index,operations)
                    });
                }
                // this.data_ApiInterfaceList[index].operations = !operations;
                // alert('启用');
            },
            // 启用提示框ok
            enabled_ok (index,operations,id) {
                api.yop_apiManagement_apiActive({
                    apiId : id
                }).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'所选API启用成功','启用成功');
                            this.data_ApiInterfaceList[index].statusDesc = '活动中'
                            this.data_ApiInterfaceList[index].operations = !operations;
                        }else{
                            this.$ypMsg.notice_error(this,'启用失败',response.message,response.solution);
                        }
                    }
                )
                // this.data_ApiInterfaceList[index].operations = !operations;
            },
            // 禁用提示框ok
            forbidden_ok (index,operations,id) {
                api.yop_apiManagement_apiForbid({
                    apiId : id
                }).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'所选API禁用成功','禁用成功');
                            this.data_ApiInterfaceList[index].statusDesc = '已禁用';
                            this.data_ApiInterfaceList[index].operations = !operations;
                        }else{
                            this.$ypMsg.notice_error(this,'禁用失败',response.message,response.solution);
                        }
                    }
                )
                // this.data_ApiInterfaceList[index].operations = !operations;

            },
            // 禁用提示框ok
            delete_ok () {
                if(!this.cause){
                    this.$Modal.warning({
                        title: "警告",
                        content: "请输入原因"
                    });
                }else{
                    this.$Modal.confirm({
                        title: '提示',
                        content: '确认删除吗？',
                        'ok-text':'确认',
                        onOk: () =>{
                                var param = {
                                    id: this.deleteId,
                                    cause:this.cause
                                }
                                // 如果此按钮需要审核则执行
                                if(util.checkAudit('/rest/spi/delete')){
                                    this.delete_spi_show = false;
                                }
                                api.yop_spiManagement_spi_delete(param).then(
                                    (response) =>{
                                        if(response.status === 'success'){
                                            this.$ypMsg.notice_success(this,'所选SPI删除成功','删除成功');
                                            this.delete_spi_show = false;
                                        }else{
                                            this.$ypMsg.notice_error(this,'删除失败',response.message,response.solution);
                                        }
                                        this.pageRefresh();
                                    }
                                    )
                                }
                    });
                }
                
                // this.data_ApiInterfaceList[index].operations = !operations;

            },
            // 版本记录按钮调用方法
            interface_version () {
                alert('版本记录');
            },
            // 界面初始化函数
            init () {
                this.show_loading_apiList = true;
                // 初始化页面表格数据
                api.yop_spiManagement_spi_list({
                    apiGroup:"",
                    name:"",
                    title:"",
                    spiType:"",
                    pageNo : 1,
                    pageSize : 10
                }).then(
                    (response) => {
                        var response = response.data
                        if(response.status === 'success'){
                            this.tabledataGet(response.data.page.items);
                            this.pageNo = response.data.page.pageNo;
                            if(response.data.page.items){
                                if(response.data.page.items.length < 10){
                                    this.pageTotal=response.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }

                            this.show_loading_apiList = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.message
                            });
                        }
                    }
                );
                
                
                // api分组列表
                api.yop_dashboard_apis_list().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.data_apiGroup_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].apiGroupCode,
                                label: resultTemp[i].apiGroupCode+'('+resultTemp[i].apiGroupName+')',
                                name: resultTemp[i].apiGroupName
                            })
                        }
                        this.data_apiGroup_List = dataTemp;
                    }
                )
            },
            // 页面刷新
            pageRefresh (val) {
                let paramsTemp = {
                    title:this.data_spi_title,
                    name:this.data_spi_name,
                    spiType:this.data_select_spiType,
                    apiGroup : this.data_select_apiGroup,
                    pageNo : val,
                    pageSize: 10
                }
                this.current_params = paramsTemp;
                api.yop_spiManagement_spi_list(this.current_params).then(
                    (response) => {
                        var response = response.data
                        this.tabledataGet(response.data.page.items);
                        this.pageNo = response.data.page.pageNo;
                        if(response.data.page.items){
                                if(response.data.page.items.length < 10){
                                    this.pageTotal=response.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                        this.show_loading_apiList = false ;
                    }
                )
            },
            // 表格赋值
            tabledataGet (items) {
                this.data_ApiInterfaceList = [];
                let dataTemp = [];
                for (var i in items){
                    let DateTemp = items[i].lastModifiedDate + '<br/>' + items[i].createdDate
                    dataTemp.push({
                        status: items[i].status,
                        id : items[i].id,
                        name: items[i].name,
                        version: items[i].version,
                        title: items[i].title,
                        apiGroup:items[i].apiGroup,  
                        spiType: items[i].spiType,
                        description:items[i].description,
                        createdDateTime:items[i].createdDateTime,
                        lastModifiedDateTime:items[i].lastModifiedDateTime,

                    });
                    if(items[i].status === 'OFFLINE'){
                        delete dataTemp[i]['operations'];
                    }
                }
                this.data_ApiInterfaceList = dataTemp;
            },
            // api分组标题处理
            apiGroup_title_handler (title,code) {
                if(title && title !== ''){
                    return title;
                }else{
                    return this.apiGroup_title_transfer(code);
                }
            },
            // api分组名称转义
            apiGroup_title_transfer (code) {
                for(var i in this.data_apiGroup_List){
                    if(this.data_apiGroup_List[i].value === code){
                        return  this.data_apiGroup_List[i].name;
                    }
                }
                return '';
            },
            // 安全需求数组处理
            SecurityGenerate (title,array) {
                let securityTemp = title +'<br/>'
                for(var i in array){
                    if(i === array.length){
                        securityTemp = securityTemp +array[i]
                    }else{
                        securityTemp = securityTemp +array[i]+'<br/>'
                    }
                }
                return securityTemp;
            },
            // 空处理函数
            ParamHandle (Param){
                if(Param || (Param === 0)){
                    return Param;
                }else{
                    return '';
                }
            },
            // 表格内容改动时
            handleselection(value){
                this.multiSelectedData = [];
                this.testMultiSelectedData = [];
                for(var i in value){
                    this.multiSelectedData.push(value[i].name);
                    this.testMultiSelectedData.push(value[i].apiGroup);
                }
            },
            // 查询SPI函数
            search_Interface () {
                let paramsTemp = {
                    title:this.data_spi_title,
                    name:this.data_spi_name,
                    spiType:this.data_select_spiType,
                    apiGroup : this.data_select_apiGroup,
                    pageNo : 1,
                    pageSize: 10
                }
                this.current_params = paramsTemp;
                api.yop_spiManagement_spi_list(paramsTemp).then(
                    (response) => {
                        var response = response.data
                        this.tabledataGet(response.data.page.items);
                        this.pageNo = response.data.page.pageNo;
                        if(response.data.page.items){
                                if(response.data.page.items.length < 10){
                                    this.pageTotal=response.data.page.items.length;
                                }else{
                                    this.pageTotal=NaN;
                                }
                            }else{
                                this.pageTotal=NaN;
                            }
                        

                    }
                );
            },
            // 重置函数
            reset_Interface () {
                this.$refs.select_apiM_0.clearSingleSelect();
                this.$refs.select_apiM_3.clearSingleSelect();
                this.data_spi_title = '';
                this.data_spi_name = '';
                this.current_status ={
                    pageNo : 1,
                    pageSize: 10
                };
            },
            // // 注册web api函数
            // webApi_Register () {
            //     // this.modal_Show_register = true;
            //     // alert('注册web api');
            //     localStorage.apiInfo='create';
            //     // localStorage.apiId =
            //     // this.$router.push({
            //     //     name: 'spi_basics'
            //     // });
            //     this.modal_Show_register =true;

            // },
            // 导入SPI函数
            interface_Import () {
                this.import_spi_show = true
                this.clear();
                this.$refs.importSpi.resetFields();
                this.$refs.modal_apiM_select_5.clearSingleSelect();
                this.$refs.select_apiM_1.clearSingleSelect();
                this.form_data_spi.apiGroup = "";
                
                // alert('导入接口');
                // if (this.multiSelectedData == null || this.multiSelectedData.length == 0){
                //     //还有其他函数判定
                //     alert("请选择数据后再点击");
                // }else {
                //     var temp =[];
                //     this.multiSelectedData.forEach( m => {
                //         temp.push(m.APIid);
                //     });
                //     //*（需要删除）
                //     // console.log("一键回归"+temp);
                //     // alert("一键回归"+temp);
                // }
            },
           
            //导出SPI函数
            interface_Export () {
                if (this.multiSelectedData == null || this.multiSelectedData.length == 0){
                    //还有其他函数判定
                    this.$ypMsg.notice_warning(this,'请选择需要导出的数据后再点击');
                }else {
                    let diff=0
                    for (var i = 0; i < this.testMultiSelectedData.length; i++) {
                        if (this.testMultiSelectedData[i] !== this.testMultiSelectedData[0]) {
                            diff++
                        }
                    }
                    if (diff === 0) {
                        this.export_spi_show = true;
                        this.form_data_spi_export.data_select_apiGroup_import = this.testMultiSelectedData[0]
                    } else {
                        this.$Modal.error({
                            title: '错误',
                            content: "请选择相同的API分组"
                        });
                    }
                }
            },
            sure_export_spi(val){
                this.$refs[val].validate((valid) => {
                if (valid) {
                        var  param = {
                            apiGroup:this.form_data_spi_export.data_select_apiGroup_import,
                            dataFormat:this.form_data_spi_export.spiType,
                            spi:this.multiSelectedData,
                        }
                        var type=""
                        if(this.form_data_spi_export.spiType == "JSON"){
                            type="json"
                        }else if(this.form_data_spi_export.spiType == "YAML"){
                            type="yaml"
                        }
                        api.yop_spiManagement_spi_export(param,type).then(
                            (response) => {
                                if(response.data.status != "error"){
                                    this.$ypMsg.notice_success(this,'导出成功');
                                }else{
                                    this.$ypMsg.notice_error(this,'错误',response.data.message,response.data.solution);
                                }
                            }
                        )
                        this.export_spi_show = false;
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            hide_export_spi(){
                this.export_spi_show = false;
            },
            // 下载执行函数
            exec_download (data,) {
                // let blob = data;
                // let link =document.createElement('a');
                // link.style.display = 'none';
                // link.href = window.URL.createObjectURL(blob);
                // link.download =name;
                // link.click();
                if (!data) {
                    return
                }
                let url = window.URL.createObjectURL(new Blob([data]));
                let link = document.createElement('a');
                link.style.display = 'none';
                link.href = url;
                link.setAttribute('download','导出数据.json');
                document.body.appendChild(link);
                link.click()
            },
            /**
             * 新建弹窗方法
             */
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
            // 点击选择端点类型触发函数
            selected_type (value) {
                if(value === 'LOCAL'){
                    this.data_EndpointProtocol_List = [
                        {
                            value: 'SPRING',
                            label: 'spring'
                        }];
                    this.data_select_EndpointProtocol = 'SPRING';
                }else if (value === 'PASSTHROUGH'){
                    this.data_EndpointProtocol_List = [
                        {
                            value: 'HTTP',
                            label: 'http'
                        }];
                    this.data_select_EndpointProtocol = 'HTTP';
                }else if (value === 'TRANSFORM'){
                    this.data_EndpointProtocol_List = [
                        {
                            value: 'HESSIAN',
                            label: 'hessian'
                        }];
                    this.data_select_EndpointProtocol = 'HESSIAN';
                }
            },
            // onblur调用参数
            keydown () {
                if(this.current_content === this.endpoint_Name){

                }else{
                    if(this.pointUrl_user_defined){
                        this.pass_url = this.IsUrl(this.endpoint_Url)
                        if(this.pass_url){
                            this.run_request_point();
                        }else{
                            this.$ypMsg.notice_warning(this,'url不符合规范请修改');
                        }
                    }else{
                        // 请求端点方法
                        this.run_request_point();
                    }

                }
                this.IsLocal(this.endpoint_Name);
            },
            // 执行请求端点方法
            run_request_point () {
                if(this.endpoint_Name && this.endpoint_Name !== ''){
                    // var params = new URLSearchParams();
                    // params.append('className', this.endpoint_Name);
                    var params ;
                }
                if(this.pointUrl_user_defined && this.endpoint_Url !== ''){
                    // params.append('endServiceUrl', this.endpoint_Url);
                    params = {
                        params:{
                            'className': this.endpoint_Name,
                            'endServiceUrl' : this.endpoint_Url
                        }
                    }
                }else{
                    params= {
                        params:{
                            'className': this.endpoint_Name,
                        }
                    }

                    api.yop_apiManagement_loader_methodQuery(params).then(
                        (response) =>{
                            this.data_EndpointMethod_List = [];
                            this.data_select_EndpointMethod = ''
                            let MethodArray = response.data.data.methodList;
                            this.data_select_EndpointMethod = MethodArray[0];
                            for(var i in MethodArray){
                                this.data_EndpointMethod_List.push(
                                    {
                                        label : MethodArray[i],
                                        value : MethodArray[i]
                                    }
                                );
                            }
                        }
                    )
                }
            },
            // onfocus 调用方法
            onFocus () {
                this.current_content = this.endpoint_Name;
            },
            // 端点url onblur调用参数
            onblur_url () {
                if(this.current_content_url === this.endpoint_Url){

                }else{
                    if(this.endpoint_Url === '' && this.pointUrl_user_defined ===false){
                        this.run_request_point();
                    }else{
                        this.pass_url = this.IsUrl(this.endpoint_Url)
                        if(this.pass_url){
                            // 请求端点方法
                            this.run_request_point();
                        }else{
                            this.$ypMsg.notice_warning(this,'url不符合规范请修改');
                        }
                    }
                }

            },
            // 端点url onfocus 调用方法
            onFocus_url () {
                this.current_content_url = this.endpoint_Url;
            },

            // 注册Web API取消按钮
            cancel_Newapp () {
                this.modal_Show_register = false;
            },
            // 实际注册功能
            ok_func (){
                let param = {
                    methodName : this.data_select_EndpointMethod,
                    className : this.endpoint_Name,
                    apiType : this.data_apiType,
                    apiGroup : this.data_apiGroup
                }
                // api 生成函数
                api.yop_apiManagement_config_autoGenerate_uri(param).then(
                    response =>{
                        let status = response.data.status;
                        if (status === 'success') {
                            let result = response.data.data.apiUri;
                            // api uri数据绑定
                            this.$store.state.api_uri = result;
                            // api 分组数据绑定
                            this.$store.state.api_group = this.data_apiGroup;
                            // api 类型数据绑定
                            this.$store.state.api_type = this.data_apiType;
                            // 端点url数据绑定
                            this.$store.state.endpoint_Url=this.endpoint_Url  ;
                            // 显示端点url部分
                            this.$store.state.show_pointUrl = this.show_pointUrl;
                            // 端点类名数据绑定
                            this.$store.state.endpoint_Name = this.endpoint_Name;
                            // 是否幂等数据绑定
                            this.$store.state.idempotent = this.idempotent;
                            // 端点url自定义
                            this.$store.state.pointUrl_user_defined = this.pointUrl_user_defined;
                            // 适配类型下拉框数据绑定
                            this.$store.state.data_select_type = this.data_select_type;
                            // 适配类型下拉框数据
                            this.$store.state.data_type_List = this.data_type_List;
                            // 端点协议下拉框数据绑定
                            this.$store.state.data_select_EndpointProtocol = this.data_select_EndpointProtocol;
                            // 端点协议下拉框数据
                            this.$store.state.data_EndpointProtocol_List = this.data_EndpointProtocol_List;
                            // 端点方法下拉框数据绑定
                            this.$store.state.data_select_EndpointMethod = this.data_select_EndpointMethod;
                            // 端点方法下拉框数据
                            this.$store.state.data_EndpointMethod_List = this.data_EndpointMethod_List;
                            this.$router.push({
                                name: 'spi_basics'
                            });
                        } else {
                            this.$Notice.error({
                                title: 'api uri生成失败',
                                desc: response.data.message,
                                duration: 10
                            });
                        }
                        this.modal_Show_register = false;
                    }
                )
            },
            // 注册Web API确定按钮
            ok_Newapp () {
                if(this.endpoint_Name === '' || this.data_select_EndpointMethod === ''){
                    this.$Modal.warning({
                        title: '警告',
                        content: '请检查必填项是否填写'
                    });
                }else{
                    if(!this.pointUrl_user_defined){
                        this.ok_func();
                    }else{
                        if(this.pass_url){
                            this.ok_func();
                        }else{
                            this.$ypMsg.notice_warning(this,'url不符合规范请修改');
                        }
                    }


                }
            },
            // 本地校验
            IsLocal (string){
                if(string.trim().substring(0,32)  === 'com.yeepay.g3.yop.center.combapi'){
                    if(this.data_select_type !== 'LOCAL'){
                        this.$Modal.warning({
                            title: '警告',
                            content: '您配置的端点方法适配类型为本地，系统将帮您的适配类型转化为本地！',
                            onOk: () =>{
                                this.data_select_type ='LOCAL';
                            }
                        });
                    }

                }
            },
            // url校验
            IsUrl (str_url) {
                let strRegex = '^((https|http|ftp|rtsp|mms)?://)'
                    + '?(([0-9a-z_!~*\'().&=+$%-]+: )?[0-9a-z_!~*\'().&=+$%-]+@)?' //ftp的user@
                    + '(([0-9]{1,3}.){3}[0-9]{1,3}' // IP形式的URL- **************
                    + '|' // 允许IP和DOMAIN（域名）
                    + '([0-9a-z_!~*\'()-]+.)*' // 域名- www.
                    + '([0-9a-z][0-9a-z-]{0,61})?[0-9a-z].' // 二级域名
                    + '[a-z]{2,6})' // first level domain- .com or .museum
                    + '(:[0-9]{1,4})?' // 端口- :80
                    + '((/?)|' // a slash isn't required if there is no file name
                    + '(/[0-9a-z_!~*\'().;?:@&=+$,%#-]+)+/?)$';
                let re=new RegExp(strRegex);
                if (re.test(str_url)) {
                    return true;
                } else {
                    return false;
                }
            },
            // 上传文件
            uploading(event){

            },
            //文件上传状态绑定函数
            handleFormatError (file) {
                this.$ypMsg.notice_warning(this,'文件 ' + file.name + ' 格式不正确，请选择图片文件。','文件格式不正确');
            },
            handleBeforeUpload (file) {
                // this.clear();
                this.form_data_spi.fileContent = file
                this.$ypMsg.notice_warning(this,'文件 ' + file.name + ' 准备上传。','文件准备上传')
                return false
            },
            upload () { // 上传文件
                let formData = new FormData();
                formData.append('dataFormat', this.form_data_spi.spiType);
                formData.append('data', this.form_data_spi.fileContent);
                formData.append('apiGroup', this.form_data_spi.data_select_apiGroup_import);
                var param = {
                    dataFormat:this.form_data_spi.spiType,
                    content:this.form_data_spi.fileContent,
                    apiGroup:this.form_data_spi.data_select_apiGroup_import,
                }
                console.log("fileContent",this.form_data_spi.fileContent)
                api.yop_spiManagement_spi_import_analysis(formData).then(
                    (response) => {
                            if(response.status == "success"){
                                this.$ypMsg.notice_info(this,'文件上传成功');
                                this.result = response.data.result;
                                this.requestId = this.result.requestId
                                this.spisToCreateList = this.result.spisToCreate 
                                this.spisToOverrideList = this.result.spisToOverride
                                this.modelsToCreateList = this.result.modelsToCreate
                                this.modelsToOverrideList = this.result.modelsToOverride
                                this.spisToIgnore = this.result.spisToIgnore
                                this.modelsToIgnore = this.result.modelsToIgnore
                                this.unusedModels = this.result.unusedModels

                                if(this.result.spisToOverride){
                                    for(var m = 0; m <this.result.spisToOverride.length ; m ++){
                                        this.spisToOverrideArr.push(this.result.spisToOverride[m]["spi"].basic.name);
                                        this.checkAllGroupSpisToOverrideArr.push(this.result.spisToOverride[m]["spi"].basic.name);
                                    }
                                    console.log(this.spisToOverrideArr,"this.spisToOverrideArr")
                                    console.log(this.checkAllGroupSpisToOverrideArr,"this.checkAllGroupSpisToOverrideArr")
                                }
                                if(this.result.modelsToOverride){
                                    for(var n = 0; n <this.result.modelsToOverride.length ; n ++){
                                        this.modelsToOverrideArr.push(this.result.modelsToOverride[n]["model"].name);
                                        this.checkAllGroupModelsToOverrideArr.push(this.result.modelsToOverride[n]["model"].name);
                                    }
                                    console.log(this.modelsToOverrideArr,"this.modelsToOverrideArr")
                                    console.log(this.checkAllGroupModelsToOverrideArr,"this.checkAllGroupModelsToOverrideArr")
                                }
                                var self = this
                                setTimeout(function(){
                                    if(self.spisToCreateList){
                                        for(var a = 0 ;a < self.spisToCreateList.length ; a++){
                                            (self.$refs.spisToCreate[a]).setResult(self.spisToCreateList[a]["spi"])
                                        }
                                    }
                                    if(self.spisToOverrideList){
                                        for(var b = 0 ;b < self.spisToOverrideList.length ; b++){
                                            (self.$refs.spisToOverride[b]).setResult(self.spisToOverrideList[b])
                                        }
                                    }
                                    if(self.modelsToCreateList){
                                        for(var c = 0 ;c < self.modelsToCreateList.length ; c++){
                                            (self.$refs.modelsToCreate[c]).setResult(self.modelsToCreateList[c]["model"])
                                        }
                                    }
                                    if(self.modelsToOverrideList){
                                        for(var d = 0 ;d < self.modelsToOverrideList.length ; d++){
                                            (self.$refs.modelsToOverride[d]).setResult(self.modelsToOverrideList[d],d)
                                        }
                                    }
                                    
                                },500)
                                this.analysis_result_show = true
                                   
                            }else{
                                var detail = response.data.detail
                                if(detail){
                                    var html = '';
                                    for(var k = 0 ; k< detail.length ; k ++){
                                        html += `<p>项目：${detail[k].item} ;值：${detail[k].value} ;原因：${detail[k].reason}</p>`
                                    }
                                    this.$Modal.error({
                                        title: "失败",
                                        content: html
                                    });
                                }else{
                                    this.$Notice.error({
                                    title: '失败',
                                    desc: response.message,
                                    duration: 10
                                });
                                }
                                 
                            }
                    }
                )
            },  
            handleProgress (event, file) {
                this.$ypMsg.notice_info(this,'文件 ' + file.name + ' 正在上传。','文件正在上传')
            },
            handleSuccess (event, file) {
                let total = event.data.result.total
                let success = event.data.result.success
                let failure = event.data.result.failure
                this.$ypMsg.notice_success(this,'全部SPI数量：' + total + ' <br/>'+'成功SPI数量：' + success + ' <br/>'+'失败API数量：' + failure,'文件上传完成结果');
            },
            handleError (event, file,fileList) {
                this.$ypMsg.notice_error_simple(this,'文件上传失败','文件 ' + fileList.name + ' 上传失败。');
            },
            // 下拉框加载完处理函数
            select_callBack () {

            },
            // api分组下拉框数据更新
            updateSelect_apiGroup (val) {
                this.data_apiGroup = val;
            },
            // api类型下拉框数据更新
            updateSelect_apiType (val) {
                this.data_apiType = val;

            },
            // api安全需求设置
            api_security (uri,apiGroup){
                this.$refs.modal_security.modal_show(uri,apiGroup);
            } ,
            // 冒泡阻止，防止点击输入框，触发手风琴操作
            stopProp (e) {
                e.stopPropagation();
            },
        },
        mounted () {
            // 挂载时调用页面初始化函数
            this.init();
            localStorage.removeItem('apiInfo');
        },
        created (){
            // localStorage.removeItem('apiInfo');
        },

    };
</script>
