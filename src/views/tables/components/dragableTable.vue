<template>
    <div>
        <Table
            ref="dragable"
            :columns="columnsList"
            :data="value"
            highlight-row
            border
            :show-header="header"
            @on-expand="expand"
        ></Table>
    </div>
</template>

<script>
import Sortable from 'sortablejs';

export default {
    name: 'DragableTable',
    data (){
      return {
          expanded:false,
          el : [],
          sort : Object
      }
    },
    props: {
        columnsList: Array,
        value: Array,
        header: Boolean
    },
    methods: {
        startFunc (e) {
            this.$emit('on-start', e.oldIndex);
            // console.log(this.value);
            this.sort.option("sort",false)
            if(this.expanded){
                this.sort.option("sort",false)
                this.$Message.warning({
                    content: '收缩子项表格才可以进行拖拽',
                    duration: 10
                });
                this.expanded = false;
            }

        },
        endFunc (e) {
            // console.log(this.expanded);
            if(!this.expanded){
                let movedRow = this.value[e.oldIndex];
                this.value.splice(e.oldIndex, 1);
                this.value.splice(e.newIndex, 0, movedRow);
                this.$emit('on-end', {
                    value: this.value,
                    from: e.oldIndex,
                    to: e.newIndex
                });
            }
        },
        chooseFunc (e) {
            this.$emit('on-choose', e.oldIndex);
        },
        expand (row,status){
            if(status === true){
                this.expanded = true
            }else{
                this.expanded = false
            }
            let content={
                row: row,
                status: status
            }
            // console.log(content);
            this.$emit('on-expand',content);
        }
    },
    mounted () {
        if(this.header === true){
            this.el = this.$refs.dragable.$children[1].$el.children[1];
        }else{
            this.el = this.$refs.dragable.$children[0].$el.children[1];
        }
        // let vm = this;
        // this.sort=Sortable.create(this.el, {
        //     onStart: vm.startFunc,
        //     onEnd: vm.endFunc,
        //     onChoose: vm.chooseFunc
        // });
    }
};
</script>

