<style lang="less">
    @import '../../styles/common.less';
    .demo-badge-alone{
        background: #5cb85c !important;
    }
    .round{width:16px;height:16px;display: inline-block;font-size:20px;line-height:16px;text-align:center;color:#f00;text-decoration:none}
    .ivu-checkbox+span{
        // display:none;
    }
    .reasonType {
    vertical-align: top;
    display: inline-block;
    font-size: 14px;
    margin-top: 5px;
}
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">
                <!-- <Col span="7" > -->
                <!-- <Col span="7" class="margin-top-10">
                <span >SPI标题:</span>
                </Col>
                <Col span="17" >
                <Input id='input_apiM_1' class="margin-top-5" clearable v-model="data_spi_title" placeholder="SPI标题" @on-enter="search_Interface"></Input>
                </Col> -->
                <!-- </Col> -->
                <Col span="7" >
                    <Col span="7" class="margin-top-10">
                        <span >服务类型:</span>
                        </Col>
                        <Col span="17" >
                        <Select ref="select_apiM_3"  id='select_apiM_3' class="margin-top-5" v-model="data_service_spiType"  placeholder="请选择（默认全部）" clearable >
                            <Option v-for="item in service_type_list" :value="item.value" :key="item.value">{{ item.label }}</Option>
                        </Select>
                        </Col>
                </Col>
                <Col offset="1" span="7" >
                    <Col span="7" class="margin-top-10">
                    <span >服务名称:</span>
                    </Col>
                    <Col span="17" >
                    <Input id='input_apiM_2' class="margin-top-5" clearable v-model="data_spi_name" placeholder="服务名称" @on-enter="search_Interface"></Input>
                    </Col>
                </Col>
                <Col offset="1" span="7" >
                    <Col span="7" class="margin-top-10">
                        <span >服务提供方:</span>
                        </Col>
                        <Col span="17" >
                        <Select filterable ref="select_apiM_3"  id='select_apiM_4' class="margin-top-5" v-model="spCode"  placeholder="请选择（默认全部）" clearable >
                            <Option v-for="item in spCodeList" :value="item.spCode" :key="item.spCode">{{ item.spName }}</Option>
                        </Select>
                        </Col>
                </Col>
                </Col>
                <Col span="4">
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button id='btn_apiM_1' type="primary" @click="search_Interface">查询</Button>
                </Col>
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button id='btn_apiM_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
            </Row>
                <!--<input id="fileinput" @change="uploading($event)" type="file" accept="image/*">-->
            <Row class="margin-top-20">
                <Col span="12">
                    <Button class="margin-left-10" type="primary"
                        @click="interface_add">新增
                    </Button>
                </Col>
            </Row>
            <Row class="margin-top-10" style="min-height: 500px">
                <Col span="24" v-show="!show_loading_apiList">
                    <Table id='table_1' border ref="selection" :columns="columns_ApiInterfaceList" :data="data_ApiInterfaceList" @on-selection-change="handleselection"></Table>
                    <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                        <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="pageSize" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                    </Tooltip>
                </Col>
                <Spin fix v-show="show_loading_apiList">
                    <Icon type="load-c" size=18 class="yop-spin-icon-load"></Icon>
                    <div>数据加载中...</div>
                </Spin>
            </Row>
        </Card>



       
        <Modal v-model="delete_spi_show" width="500">
            <p slot="header" style="color:#000;text-align:left;font-size: 18px;">
                <Icon type="ios-information-circle"></Icon>
                <span>删除</span>
            </p>
            <div style="margin-left:10px;font-size: 12x;">
                <p style="color:red">删除操作可能会导致服务不可用</p>
                <label for="" class="reasonType"><span style="color:red">*</span>原因：</label>
                <Input type="textarea" v-model="cause" style="width:85%;font-size: 12x;" placeholder="请输入原因"></Input>
            </div>
            <div slot="footer">
                <Button type="primary"  @click="sure_Delete_spi">确定</Button>
                <Button type="ghost"  @click="hide_Delete_spi">取消</Button>
            </div>
        </Modal>

        <Modal v-model="modal_Show_example" width="600" :closable="false" :mask-closable="false">
            <p slot="header">
                <span  class="margin-right-10" v-if="this.current_status == 'create'">新增</span> 
                <span  class="margin-right-10"  v-if="this.current_status == 'modify'">修改</span>
                <span  class="margin-right-10"  v-if="this.current_status == 'description'">详情</span> 
            </p>
            <div style="padding-left:30px;">
                <Form ref="form_data_service"  :model="form_data_service" :rules="form_data_service_rule" :label-width="120" inline>
                    <Row>
                        <FormItem class="width-100-perc"  label="服务提供方：" prop="spCode" >
                            <Select size="small" style="width:200px" filterable ref="select_apiM_3" v-show="this.current_status !== 'description'"  id='select_apiM_4' class="margin-top-5" v-model="form_data_service.spCode"  placeholder="请选择（默认全部）" clearable :disabled="this.current_status == 'modify'?true:false">
                                <Option v-for="item in spCodeList" :value="item.spCode" :key="item.spCode">{{ item.spName }}</Option>
                            </Select>
                            <p  v-if="this.current_status == 'description'">{{form_data_service.spCode}}</p>

                        </FormItem>
                    </Row>
                    <Row>
                        <FormItem class="width-100-perc"  label="后端类型：" prop="service_type" >
                            <Select id="selectEndType" v-show="this.current_status !== 'description'" size="small" v-model="form_data_service.service_type" style="width:200px" placeholder="请输入"  :disabled="this.current_status == 'modify'?true:false">
                                <Option v-for="(item,index) in service_type_list" :value="item.value" :key="index">{{ item.label }}</Option>
                            </Select>
                            <p  v-if="this.current_status == 'description'">{{form_data_service.service_type}}</p>

                        </FormItem>
                    </Row>
                    
                    <Row>
                        <FormItem class="width-100-perc"  label="服务名称：" prop="service">
                            <Input style="width:200px" id="selectEndService"  v-show="this.current_status !== 'description'" size="small" v-model="form_data_service.service" placeholder="请输入"   :disabled="this.current_status == 'modify'?true:false"></Input>
                            <p  v-if="this.current_status == 'description'">{{form_data_service.service}}</p>

                        </FormItem>
                    </Row>
                    <Row >
                </Row>
                
                <Row >
                    <FormItem  class="width-50-perc"  label="连接超时：" prop="link_timeout">
                        <Input style="width:200px" v-if="this.current_status !== 'description'"  id="inputConnectionTimeout" size="small" v-model="form_data_service.link_timeout" placeholder="请输入" @on-focus="onFocus_url" @on-blur="onblur_url"><span slot="append">ms</span></Input>
                        <p  v-if="this.current_status == 'description'">{{form_data_service.link_timeout}}ms</p>
                    
                    </FormItem>
                </Row>
                <Row >
                    <FormItem  class="width-50-perc"  label="读取超时：" prop="read_timeout">
                        <Input style="width:200px" v-if="this.current_status !== 'description'"   id="inputReadTimeout" size="small" v-model="form_data_service.read_timeout" placeholder="请输入" @on-focus="onFocus_url" @on-blur="onblur_url"><span slot="append">ms</span></Input>
                        <p  v-if="this.current_status == 'description'">{{form_data_service.read_timeout}}ms</p>
                    
                    </FormItem>
                </Row>
                <Row >
                   <FormItem  class="width-50-perc"  label="basepath：" prop="basePath" v-if="form_data_service.service_type === 'HTTP'">
                        <Input style="width:200px" v-if="this.current_status !== 'description'" id="inputEndRequestPath" size="small" v-model="form_data_service.basePath" placeholder="请输入" @on-focus="onFocus_url" @on-blur="onblur_url"></Input>
                        <p  v-if="this.current_status == 'description'">{{form_data_service.basePath}}</p>
                    
                    </FormItem>
                </Row>
                </Form>
            </div>
            <div slot="footer">
                <Button type="primary" v-show="this.current_status !== 'description'" @click="ok_example">确定</Button>
                <Button type="ghost" v-show="this.current_status !== 'description'" @click="cancel_example">取消</Button>
                <Button type="ghost" v-show="this.current_status == 'description'" @click="cancel_example">关闭</Button>
            </div>
           
        </Modal>

    </div>
</template>

<script>
    import commonSelect from '../common-components/select-components/selectCommon';
    import api from'../../api/api'
    import Vue from 'vue';
    import axios from 'axios';
    import qs from 'qs';
    import loading from '../my-components/loading/loading';
    import util from '../../libs/util'

import { setTimeout } from 'timers';
    // import
    export default {
        name: 'spiList',
        components:{
            loading,
            commonSelect,
        },
        data () {
            // 是否是url
            const validate_basepath_config = (rule, value, callback) => {
                if (!this.IsUrl(value)){
                    return callback(new Error('请输入正确的basepath'));
                }else{
                    callback();
                }
            };
            // 是否是正整数
            const validate_Integer_config = (rule, value, callback) => {
                if (!this.isPositiveInteger(value)){
                    return callback(new Error('请输入正整数'));
                }else{
                    callback();
                }
            };

            return {
                form_data_service_rule : {
                    spCode : [
                        {required:true,message:'服务提供方不能为空'},
                    ],
                    service_type : [
                        {required:true,message:'后端类型不能为空'},
                    ],
                    service : [
                        {required:true,message:'服务名称不能为空'}
                    ],
                    name : [
                       {required:true,message:'name不能为空'}
                    ],
                    basePath:[
                        {required:true,message:'basepath不能为空'},
                        { validator: validate_basepath_config}

                    ],
                    link_timeout : [
                         {required:true,message:'连接超时不能为空'},
                        { validator: validate_Integer_config}

                    ],
                    read_timeout:[
                         {required:true,message:'读取超时不能为空'},
                        { validator: validate_Integer_config}
                    ]


                },
                showService:false,
                modal_Show_example:false,
                editExample:false,
                current_id:"",
                current_version:"",
               current_status: "create",

                form_data_service:{
                    apiGroup:"",
                    service_type:"",
                    service:"",
                    spCode:"",
                    basePath:"",
                    link_timeout:"",
                    read_timeout:"",
                },
                service_type_list:[],
                service_list:[],

                    pageSize : 10,

                result:{},
                data_Template:{},
                collapse_label: ['0'],
                spisToCreateList:[],
                spisToOverrideList:[],
                modelsToCreateList:[],
                modelsToOverrideList:[],
                spCodeList:[],
                // modelsToIgnoreList:[],
                analysis_result_show:false,
                // 导入spi
                form_data_spi:{
                    data_select_apiGroup_import:"",
                    apiGroup:"",
                    fileContent:"",
                    spiType:""
                },
                // 导出spi
                form_data_spi_export:{
                    data_select_apiGroup_import:"",
                    apiGroup:"",
                    fileContent:"",
                    spiType:""
                },
                data_spi_type:[{value:"YAML"},{value:"JSON"}],
                import_spi_show:false,
                export_spi_show:false,
                import_type:"",
                cause:"",
                delete_spi_show:false,
                data_spi_title:"",
                data_spi_name:"",
                spCode:"",
                data_select_apiGroup:"",
                service_type_list:[
                    {
                        label: "HTTP(HTTP(s))",
                        value: "HTTP",
                        name: "HTTP(s)"
                    },
                    {
                        label: "DUBBO",
                        value: "DUBBO",
                        name: "DUBBO"
                    }
                ],
                data_service_spiType:"",
                data_spiType_List:[{value:"CALLBACK",label:"回调"},{value:"NOTIFICATION",label:"通知"}],
                // api分组数据绑定
                data_apiGroup: '',
                // api类型数据绑定
                data_apiType: '',
                // 导入功能ip动态变量
                importIP : localStorage.remoteIP+'/rest/spi/import/analysis',
                /**
                 * 注册弹窗数据
                 */
                name_upload:'content',
                // 端点url数据绑定
                endpoint_Url : '',
                // 显示端点url部分
                show_pointUrl : false,
                // 端点类名数据绑定
                endpoint_Name: '',
                // 是否幂等数据绑定
                idempotent: '否',
                // 端点url自定义
                pointUrl_user_defined: false,
                // 适配类型下拉框数据绑定
                data_select_type: 'TRANSFORM',
                // 适配类型下拉框数据
                data_type_List: [
                    // {
                    //     value: 'TRANSFORM',
                    //     label: '转换'
                    // },
                    {
                        value: 'MAPPING',
                        label: '映射'
                    },
                    {
                        value: 'PASSTHROUGH',
                        label: '透传'
                    }
                ],
                // 端点协议下拉框数据绑定
                data_select_EndpointProtocol: 'HESSIAN',
                // 端点协议下拉框数据
                data_EndpointProtocol_List: [
                    {
                        value: 'HESSIAN',
                        label: 'hessian'
                    }
                ],
                // 端点方法下拉框数据绑定
                data_select_EndpointMethod: '',
                // 端点方法下拉框数据
                data_EndpointMethod_List: [],
                // 当前端点名称内容
                current_content : '',
                // 当前端点url内容
                current_content_url : '',
                // url校验结果
                pass_url : true,
                /**
                 * 主界面部分数据
                 */
                // 上传header绑定
                header_upload: {
                    Authorization: 'Bearer ' + localStorage.accesstoken
                },
                // api列表加载动画数据绑定
                show_loading_apiList : true,
                // switch点击行数
                switch_index: 0,
                // 注册web api对话框显示
                modal_Show_register: false,
                // APIuri数据绑定
                data_interface_uri : '',
                // API名称
                data_interface_name : '',
                // 状态下拉框数据绑定
                data_select_status: '',
                // 状态下拉框选项数据
                data_status_List: [],
                // api分组下拉框数据绑定
                data_select_apiGroup: '',
                // api分组下拉框数据
                data_apiGroup_List: [],
                // 安全需求下拉框数据绑定
                data_safety_request: '',
                // 安全需求下拉框数据
                data_safetyRequest_List:[],
                // api类型选择数据绑定
                data_select_apiType: '',
                // api类型
                data_apiType_List:[],
                // apiAPI列表列属性
                columns_ApiInterfaceList: [
                    
                    {
                        title: '服务名称',
                        key: 'name'
                    },
                    {
                        title: '服务提供方',
                        render: (h, params) => {
                            return h('div', [h('span', params.row.spCodeName),
                                h('span', ' (' + params.row.spCode + ')')]);
                        }
                    },
                    {
                        title: '后端类型',
                        key: 'type',
                        align: 'center',
                    },
                    {
                        title: '创建日期/最后修改日期',
                        key: 'time',
                        align: 'center',
                        width:200,
                        render: (h, params) => {
                            return h('div', [h('p', params.row.createdDateTime),
                                h('p', params.row.lastModifiedDateTime)]);
                        }
                    },
                    {
                        title: '操作',
                        key: 'operations',
                        width: 230,
                        align: 'center',
                        render: (h, params) => {
                            return h('div', [
                                 h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    // directives: [{
                                    //     name: 'url',
                                    //     value: {url: '/rest/spi/detail'}
                                    // }],
                                    on: {
                                        click: () => {
                                            this.interface_des(params.row.id)
                                        }
                                    }
                                }, '详情'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small',
                                    },
                                    // directives: [{
                                    //     name: 'url',
                                    //     value: {url: '/rest/spi/update'}
                                    // }],
                                    on: {
                                        click: () => {
                                            this.interface_modify(params.row);
                                        }
                                    }
                                }, '修改'),
                              
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    // directives: [{
                                    //     name: 'url',
                                    //     value: {url: '/rest/spi/delete'}
                                    // }],
                                    on: {
                                        click: () => {
                                            this.interface_delete(params.row.id);
                                        }
                                    }
                                }, '删除'),
                                
                              
                            ]);
                        }
                    }
                ],
                // 表格数据
                data_ApiInterfaceList: []
                ,
                // 表格选中数据
                multiSelectedData: [],
                testMultiSelectedData:[],
                // 分页单页个数
                pageNo : 1,
                // 分页总数
                pageTotal: 0,
                // 当前查询参数
                current_params : {
                    pageNo : 1,
                    pageSize : 10
                }
            };
        },
        methods: {
            getSpCodeList() {
                api.yop_backend_service_sp_codes_list().then(
                            (response) => {
                                var response = response.data
                                if (response.status === 'success') {
                                    this.spCodeList = response.data.result
                                } else {
                                    this.$Modal.error({
                                        title: '错误',
                                        content: response.message
                                    });
                                }

                            }
                        );
            },
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
            clear(){
                this.$refs.upload.clearFiles();//清除上次上传记录
            },
          
            sure_Delete_spi(){
                this.delete_ok();
            },
            hide_Delete_spi(){
                this.delete_spi_show = false;
            },
            sure_rollback_model(){
                if(this.rollback_cause){
                    var param = {
                        id :this.rollbackId,
                        cause :this.rollback_cause,
                    }
                    api.yop_spiManagement_spi_change_record_rollback(param).then(
                            (response) => {
                                var response = response.data
                                if (response.status === 'success') {
                                    this.pageRefresh()
                                    // this.tabledataGet(response.data.page.items);
                                    // this.pageNo = response.data.result.pageNo;
                                    // this.pageTotal = response.data.result.totalPageNum * 10;
                                    this.rollback_modal_show = false;
                                } else {
                                    this.$Modal.error({
                                        title: '错误',
                                        content: response.message
                                    });
                                }

                            }
                        );
                }else{
                    this.$Modal.warning({
                        title: "警告",
                        content: "请输入原因"
                    });
                }
            },
            hide_rollback_model(){
                this.rollback_modal_show = false;
            },
            // 添加按钮调用方法
            interface_add (val) {
                this.current_status = "create"
                this.$refs.form_data_service.resetFields();
                this.modal_Show_example = true;
                
            },
            // 修改按钮调用方法
            interface_modify (val) {
                this.current_status = "modify"
                    this.modal_Show_example = true;
                    this.editExample = true
                    this.interface_des(val.id,"modify")
                    this.current_id = val.id
                    this.current_version = val.version
            
            },
            // 描述按钮调用方法
            interface_des (val,current_status) {
                this.current_status = current_status || "description"
                api.yop_backend_service_detail({id:val}).then(
                    (response) =>{
                        var response = response.data
                        if(response.status === 'success'){
                            this.modal_Show_example = true;
                            this.form_data_service.service_type = response.data.result.type
                            this.form_data_service.apiGroup = response.data.result.apiGroup
                            this.form_data_service.service = response.data.result.name || ''
                            this.form_data_service.spCode = response.data.result.spCode
                            this.form_data_service.basePath = response.data.result.properties.basePath || ''
                            this.form_data_service.link_timeout = response.data.result.properties.connectionTimeout 
                            this.form_data_service.read_timeout = response.data.result.properties.readTimeout
                        }else{
                            this.$ypMsg.notice_error(this,'失败',response.message,response.solution);
                        }
                    })
            },
            toChangeRecode(apiGroup){
                this.$refs.modal_opt_record.reset_search();
                if(apiGroup){
                this.$refs.modal_opt_record.set_appId(apiGroup);
                }
                this.$refs.modal_opt_record.modal_preview();
                this.$refs.modal_opt_record.search();
            },
           
            // 删除按钮调用方法
            interface_delete (id) {
                this.deleteId = id
                // this.delete_spi_show = true;
                // this.cause = ""
                this.delete_ok()
            },
            // 启用按钮调用方法
            interface_use (index,operations,id) {
                if(operations){
                    this.$Modal.confirm({
                        title: '提示',
                        content: '禁用API将导致API无法调用，您还要继续吗？',
                        'ok-text':'禁用',
                        onOk: () =>{
                            // this.data_ApiInterfaceList[index].operations = !operations;
                            this.forbidden_ok(index,operations,id);
                        }
                        // onCancel: this.forbidden_cancel(index,operations)
                    });
                }else{
                    this.$Modal.confirm({
                        title: '提示',
                        'ok-text':'启用',
                        content: '您将启用API，请确认是否维护好API信息，您还要继续吗',
                        onOk: () =>{
                            this.enabled_ok(index,operations,id);
                            // this.data_ApiInterfaceList[index].operations = !operations;
                        },
                        // onCancel: this.forbidden_cancel(index,operations)
                    });
                }
                // this.data_ApiInterfaceList[index].operations = !operations;
                // alert('启用');
            },
            // 启用提示框ok
            enabled_ok (index,operations,id) {
                api.yop_apiManagement_apiActive({
                    apiId : id
                }).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'所选API启用成功','启用成功');
                            this.data_ApiInterfaceList[index].statusDesc = '活动中'
                            this.data_ApiInterfaceList[index].operations = !operations;
                        }else{
                            this.$ypMsg.notice_error(this,'启用失败',response.message,response.solution);
                        }
                    }
                )
                // this.data_ApiInterfaceList[index].operations = !operations;
            },
            // 禁用提示框ok
            forbidden_ok (index,operations,id) {
                api.yop_apiManagement_apiForbid({
                    apiId : id
                }).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'所选API禁用成功','禁用成功');
                            this.data_ApiInterfaceList[index].statusDesc = '已禁用';
                            this.data_ApiInterfaceList[index].operations = !operations;
                        }else{
                            this.$ypMsg.notice_error(this,'禁用失败',response.message,response.solution);
                        }
                    }
                )
                // this.data_ApiInterfaceList[index].operations = !operations;

            },
            // 禁用提示框ok
            delete_ok () {
                // if(!this.cause){
                //     this.$Modal.warning({
                //         title: "警告",
                //         content: "请输入原因"
                //     });
                // }else{
                    this.$Modal.confirm({
                        title: '提示',
                        content: '确认删除吗？',
                        'ok-text':'确认',
                        onOk: () =>{
                                var param = {
                                    id: this.deleteId,
                                }
                                // 如果此按钮需要审核则执行
                                if(util.checkAudit('/rest/spi/delete')){
                                    this.delete_spi_show = false;
                                }
                                api.yop_backend_service_delete(param).then(
                                    (response) =>{
                                        if(response.status === 'success'){
                                            this.$ypMsg.notice_success(this,'','删除成功');
                                            this.delete_spi_show = false;
                                        }else{
                                            this.$ypMsg.notice_error(this,'删除失败',response.message,response.solution);
                                        }
                                        this.pageRefresh();
                                    }
                                    )
                                }
                    });
                // }
                
                // this.data_ApiInterfaceList[index].operations = !operations;

            },
            // 版本记录按钮调用方法
            interface_version () {
                alert('版本记录');
            },
            getServiceCon(value){
                this.showService = true
                 this.form_data_service.basePath  = this.showBasePath(value).basePath;
                 this.form_data_service.link_timeout  = this.showBasePath(value).connectionTimeout;
                 this.form_data_service.read_timeout  = this.showBasePath(value).readTimeout;
            },
            // 界面初始化函数
            init () {
                this.show_loading_apiList = true;
                // 初始化页面表格数据
                api.yop_backend_service_list({
                    // apiGroup:"",
                    name:"",
                    type:"",
                    pageNo : 1,
                    pageSize : 10
                }).then(
                    (response) => {
                        var response = response.data
                        if(response.status === 'success'){
                            this.tabledataGet(response.data.page.items);
                            this.pageNo = response.data.page.pageNo;
                            if(this.pageNo === response.data.page.totalPageNum) {
                                this.pageTotal = 0
                            }else {
                                this.pageTotal=NaN;
                            }
                            this.show_loading_apiList = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.message
                            });
                        }
                    }
                );
                
                // api分组列表
                api.yop_dashboard_apis_list().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.data_apiGroup_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].apiGroupCode,
                                label: resultTemp[i].apiGroupCode+'('+resultTemp[i].apiGroupName+')',
                                name: resultTemp[i].apiGroupName
                            })
                        }
                        this.data_apiGroup_List = dataTemp;
                    }
                )
            },
            updateSelect_apiGroup_modal(val){
                // this.getServiceList(val)
            },
            // getServiceList(apiGroup){
            //         var param = {apiGroup:apiGroup}
            //         var self = this
            //         api.yop_backend_service_simple_list(param).then(
            //             (response) =>{
            //                 var response = response.data
            //                 this.service_list = [];
            //                 this.service_list_map = [];
            //                 let service = response.data.result;
            //                 // this.data_api_model= apigroup[0].groupCode;
            //                 for(var i in service){
            //                     this.service_list.push(
            //                         {
            //                             label: service[i].name,
            //                             value:service[i].name,
            //                             name: service[i].name
                                        
            //                             // [service[i].connectionTimeout,service[i].readTimeout]
            //                         }
            //                     );
            //                     var obj = {}
            //                     var service_name = service[i].name
            //                     obj[service_name] = {
            //                         connectionTimeout:service[i].connectionTimeout,
            //                         readTimeout:service[i].readTimeout,
            //                         basePath:service[i].basePath,
            //                     }
            //                     this.service_list_map.push(obj)
            //                 }
                           
            //             }
            //         );

            // },
            showBasePath(setBasePath){
                for(var i = 0 ;i < this.service_list_map.length ; i ++){
                    var service_list_mapI = this.service_list_map[i]
                    for(var j in service_list_mapI){
                        if(j == setBasePath){
                            return service_list_mapI[j]
                        }
                    }
                }
            },
            ok_example(){
                this.$refs.form_data_service.validate((valid) => {
                    if (valid) {
                        var example = ""
                        if(this.current_status == "create"){
                            var param = {
                                type:this.form_data_service.service_type,
                                name:this.form_data_service.service.trim(),
                                spCode: this.form_data_service.spCode,
                                properties: {
                                    basePath:this.form_data_service.basePath.trim(),
                                    readTimeout:this.form_data_service.read_timeout * 1,
                                    connectionTimeout:this.form_data_service.link_timeout * 1
                                }
                            }
                            api.yop_backend_service_create(param).then(
                                (response) =>{
                                    if(response.status === 'success'){
                                        this.$ypMsg.notice_success(this,'','新建成功');
                                        this.pageRefresh();
                                    }else{
                                        this.$ypMsg.notice_error(this,'失败',response.message,response.solution);
                                    }
                                })
                        }
                        if(this.current_status == "modify"){
                            param = {
                                id:this.current_id,
                                type:this.form_data_service.service_type,
                                version:this.current_version,
                                spCode: this.form_data_service.spCode,
                                name:this.form_data_service.service.trim(),
                                properties: {
                                    basePath:this.form_data_service.basePath.trim(),
                                    readTimeout:this.form_data_service.read_timeout * 1,
                                    connectionTimeout:this.form_data_service.link_timeout * 1
                                }
                            }
                            api.yop_backend_service_update(param).then(
                            (response) =>{
                                if(response.status === 'success'){
                                    this.$ypMsg.notice_success(this,'','修改成功');
                                    this.pageRefresh();
                                }else{
                                    this.$ypMsg.notice_error(this,'失败',response.message,response.solution);
                                }
                            })
                        }
                        this.modal_Show_example = false;
                    }else {
                        this.form_validate_failed()
                    }
                })

            },
            cancel_example(){
                this.modal_Show_example = false;
                this.$refs.form_data_service.resetFields();
            },
            // 页面刷新
            pageRefresh (val) {
                var paramsTemp = {
                    name:this.data_spi_name.trim(),
                    spCode:this.spCode,
                    type:this.data_service_spiType,
                    pageNo : 1,
                    pageSize: 10
                }
                if(val){
                    paramsTemp.pageNo = val
                }
                this.current_params = paramsTemp;
                this.show_loading_apiList = true
                api.yop_backend_service_list(this.current_params).then(
                    (response) => {
                        var response = response.data
                        this.tabledataGet(response.data.page.items);
                        this.pageNo = response.data.page.pageNo;
                        if(response.data.page.items){
                            if(response.data.page.items.length < 10){
                                this.pageTotal=response.data.page.items.length;
                            }else{
                                this.pageTotal=NaN;
                            }
                        }else{
                            this.pageTotal=NaN;
                        }
                        this.show_loading_apiList = false ;
                    }
                )
            },
            // 表格赋值
            tabledataGet (items) {
                this.data_ApiInterfaceList = [];
                let dataTemp = [];
                for (var i in items){
                    let DateTemp = items[i].lastModifiedDate + '<br/>' + items[i].createdDate
                    // let securityTemp = []
                    // if(items[i].apiSecurities){
                    //     securityTemp = this.SecurityGenerate('自定义',items[i].apiSecurities);
                    // }else{
                    //     securityTemp = this.SecurityGenerate('继承',items[i].apiGroupSecurities);
                    // }
                    let operationsTemp = false;
                    if(items[i].status === 'ACTIVE'){
                        operationsTemp = true;
                    }
                    if(items[i].status === 'FORBID'){
                        operationsTemp = false
                    }
                    dataTemp.push({
                        id : items[i].id,
                        name: items[i].name,
                        spCodeName: items[i].spCodeName,
                        spCode: items[i].spCode,
                        version: items[i].version,
                        apiGroup:items[i].apiGroup,  
                        type: items[i].type,
                        createdDateTime:items[i].createdDateTime,
                        lastModifiedDateTime:items[i].lastModifiedDateTime,

                    });
                    if(items[i].status === 'OFFLINE'){
                        delete dataTemp[i]['operations'];
                    }
                }
                this.data_ApiInterfaceList = dataTemp;
            },
            // api分组标题处理
            apiGroup_title_handler (title,code) {
                if(title && title !== ''){
                    return title;
                }else{
                    return this.apiGroup_title_transfer(code);
                }
            },
            // api分组名称转义
            apiGroup_title_transfer (code) {
                for(var i in this.data_apiGroup_List){
                    if(this.data_apiGroup_List[i].value === code){
                        debugger;
                        return  this.data_apiGroup_List[i].name;
                    }
                }
                return '';
            },
            // 安全需求数组处理
            SecurityGenerate (title,array) {
                let securityTemp = title +'<br/>'
                for(var i in array){
                    if(i === array.length){
                        securityTemp = securityTemp +array[i]
                    }else{
                        securityTemp = securityTemp +array[i]+'<br/>'
                    }
                }
                return securityTemp;
            },
            // 空处理函数
            ParamHandle (Param){
                if(Param || (Param === 0)){
                    return Param;
                }else{
                    return '';
                }
            },
            // 表格内容改动时
            handleselection(value){
                this.multiSelectedData = [];
                this.testMultiSelectedData = [];
                for(var i in value){
                    this.multiSelectedData.push(value[i].name);
                    this.testMultiSelectedData.push(value[i].apiGroup);
                }
            },
            // 查询SPI函数
            search_Interface () {
                let paramsTemp = {
                    name:this.data_spi_name.trim(),
                    spCode:this.spCode,
                    type:this.data_service_spiType,
                    pageNo : 1,
                    pageSize: 10
                }
                this.current_params = paramsTemp;
                this.show_loading_apiList = true
                api.yop_backend_service_list(paramsTemp).then(
                    (response) => {
                        var response = response.data
                        this.tabledataGet(response.data.page.items);
                        this.pageNo = response.data.page.pageNo;
                        if(this.pageNo === response.data.page.totalPageNum) {
                            this.pageTotal = 0
                        }else {
                            this.pageTotal=NaN;
                        }
                        this.show_loading_apiList = false
                    }
                );
            },
            // 重置函数
            reset_Interface () {
                this.$refs.select_apiM_3.clearSingleSelect();
                this.data_spi_title = '';
                this.data_spi_name = '';
                this.spCode = '';
                this.current_status ={
                    pageNo : 1,
                    pageSize: 10
                };
            },
            // // 注册web api函数
            // webApi_Register () {
            //     // this.modal_Show_register = true;
            //     // alert('注册web api');
            //     localStorage.apiInfo='create';
            //     // localStorage.apiId =
            //     // this.$router.push({
            //     //     name: 'spi_basics'
            //     // });
            //     this.modal_Show_register =true;

            // },
            // 导入SPI函数
            interface_Import () {
                this.import_spi_show = true
                this.clear();
                this.$refs.importSpi.resetFields();
                this.$refs.modal_apiM_select_5.clearSingleSelect();
                this.$refs.select_apiM_1.clearSingleSelect();
                this.form_data_spi.apiGroup = "";
                
                // alert('导入接口');
                // if (this.multiSelectedData == null || this.multiSelectedData.length == 0){
                //     //还有其他函数判定
                //     alert("请选择数据后再点击");
                // }else {
                //     var temp =[];
                //     this.multiSelectedData.forEach( m => {
                //         temp.push(m.APIid);
                //     });
                //     //*（需要删除）
                //     // console.log("一键回归"+temp);
                //     // alert("一键回归"+temp);
                // }
            },
           
            //导出SPI函数
            interface_Export () {
                if (this.multiSelectedData == null || this.multiSelectedData.length == 0){
                    //还有其他函数判定
                    this.$ypMsg.notice_warning(this,'请选择需要导出的数据后再点击');
                }else {
                    let diff=0
                    for (var i = 0; i < this.testMultiSelectedData.length; i++) {
                        if (this.testMultiSelectedData[i] !== this.testMultiSelectedData[0]) {
                            diff++
                        }
                    }
                    if (diff === 0) {
                        this.export_spi_show = true;
                        this.form_data_spi_export.data_select_apiGroup_import = this.testMultiSelectedData[0]
                    } else {
                        this.$Modal.error({
                            title: '错误',
                            content: "请选择相同的API分组"
                        });
                    }
                }
            },
           
            // 下载执行函数
            exec_download (data,) {
                // let blob = data;
                // let link =document.createElement('a');
                // link.style.display = 'none';
                // link.href = window.URL.createObjectURL(blob);
                // link.download =name;
                // link.click();
                if (!data) {
                    return
                }
                let url = window.URL.createObjectURL(new Blob([data]));
                let link = document.createElement('a');
                link.style.display = 'none';
                link.href = url;
                link.setAttribute('download','导出数据.json');
                document.body.appendChild(link);
                link.click()
            },
            /**
             * 新建弹窗方法
             */
            // 点击自定义checkbox
            check_url (data) {
                this.show_pointUrl = data;
            },
        
            // onblur调用参数
            keydown () {
                if(this.current_content === this.endpoint_Name){

                }else{
                    if(this.pointUrl_user_defined){
                        this.pass_url = this.IsUrl(this.endpoint_Url)
                        if(this.pass_url){
                            this.run_request_point();
                        }else{
                            this.$ypMsg.notice_warning(this,'url不符合规范请修改');
                        }
                    }else{
                        // 请求端点方法
                        this.run_request_point();
                    }

                }
                this.IsLocal(this.endpoint_Name);
            },
            // 执行请求端点方法
            run_request_point () {
                if(this.endpoint_Name && this.endpoint_Name !== ''){
                    // var params = new URLSearchParams();
                    // params.append('className', this.endpoint_Name);
                    var params ;
                }
                if(this.pointUrl_user_defined && this.endpoint_Url !== ''){
                    // params.append('endServiceUrl', this.endpoint_Url);
                    params = {
                        params:{
                            'className': this.endpoint_Name,
                            'endServiceUrl' : this.endpoint_Url
                        }
                    }
                }else{
                    params= {
                        params:{
                            'className': this.endpoint_Name,
                        }
                    }

                    api.yop_apiManagement_loader_methodQuery(params).then(
                        (response) =>{
                            this.data_EndpointMethod_List = [];
                            this.data_select_EndpointMethod = ''
                            let MethodArray = response.data.data.methodList;
                            this.data_select_EndpointMethod = MethodArray[0];
                            for(var i in MethodArray){
                                this.data_EndpointMethod_List.push(
                                    {
                                        label : MethodArray[i],
                                        value : MethodArray[i]
                                    }
                                );
                            }
                        }
                    )
                }
            },
            // onfocus 调用方法
            onFocus () {
                this.current_content = this.endpoint_Name;
            },
            // 端点url onblur调用参数
            onblur_url () {
                if(this.current_content_url === this.endpoint_Url){

                }else{
                    if(this.endpoint_Url === '' && this.pointUrl_user_defined ===false){
                        this.run_request_point();
                    }else{
                        this.pass_url = this.IsUrl(this.endpoint_Url)
                        if(this.pass_url){
                            // 请求端点方法
                            this.run_request_point();
                        }else{
                            this.$ypMsg.notice_warning(this,'url不符合规范请修改');
                        }
                    }
                }

            },
            // 端点url onfocus 调用方法
            onFocus_url () {
                this.current_content_url = this.endpoint_Url;
            },

           
            // 本地校验
            IsLocal (string){
                if(string.trim().substring(0,32)  === 'com.yeepay.g3.yop.center.combapi'){
                    if(this.data_select_type !== 'LOCAL'){
                        this.$Modal.warning({
                            title: '警告',
                            content: '您配置的端点方法适配类型为本地，系统将帮您的适配类型转化为本地！',
                            onOk: () =>{
                                this.data_select_type ='LOCAL';
                            }
                        });
                    }

                }
            },
            isPositiveInteger(s){//是否为正整数
                var re = /^[0-9]+$/ ;
                return re.test(s)
            } , 
            // url校验
            IsUrl (str_url) {
                let strRegex = '^((https|http|ftp|rtsp|mms)?://)'
                    + '?(([0-9a-z_!~*\'().&=+$%-]+: )?[0-9a-z_!~*\'().&=+$%-]+@)?' //ftp的user@
                    + '(([0-9]{1,3}.){3}[0-9]{1,3}' // IP形式的URL- **************
                    + '|' // 允许IP和DOMAIN（域名）
                    + '([0-9a-z_!~*\'()-]+.)*' // 域名- www.
                    + '([0-9a-z][0-9a-z-]{0,61})?[0-9a-z].' // 二级域名
                    + '[a-z]{2,6})' // first level domain- .com or .museum
                    + '(:[0-9]{1,4})?' // 端口- :80
                    + '((/?)|' // a slash isn't required if there is no file name
                    + '(/[0-9a-z_!~*\'().;?:@&=+$,%#-]+)+/?)$';
                let re=new RegExp(strRegex);
                if (re.test(str_url)) {
                    return true;
                } else {
                    return false;
                }
            },
           
            // 下拉框加载完处理函数
            select_callBack () {

            },
            // api分组下拉框数据更新
            updateSelect_apiGroup (val) {
                this.data_apiGroup = val;
            },
            // api类型下拉框数据更新
            updateSelect_apiType (val) {
                this.data_apiType = val;

            },
            // api安全需求设置
            api_security (uri,apiGroup){
                this.$refs.modal_security.modal_show(uri,apiGroup);
            } ,
            // 冒泡阻止，防止点击输入框，触发手风琴操作
            stopProp (e) {
                e.stopPropagation();
            },
        },
        mounted () {
            // 挂载时调用页面初始化函数
            this.init();
            this.getSpCodeList()
            localStorage.removeItem('apiInfo');
        },
        created (){
            // localStorage.removeItem('apiInfo');
        },

    };
</script>

<style>
    .path label {
        white-space: nowrap;
        margin-left: -11px;
    }
</style>
