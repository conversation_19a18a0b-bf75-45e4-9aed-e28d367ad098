<style lang="less">
    @import '../styles/menu.less';
    .menu-disabled {
        opacity: .25;
        cursor: not-allowed;
        background: none!important;
    }
</style>

<template>
    <Menu ref="sideMenu" accordion :active-name="$route.name" :open-names="openNames" :theme="menuTheme" width="auto" @on-select="changeMenu">
        <template v-for="item in menuList">
            <MenuItem v-if="item.children.length==1" :name="item.children[0].name" :key="'menuitem' + item.name" :disabled="!item.children[0].perm" >
                <Icon :type="item.children[0].icon || item.icon" :size="iconSize" :key="'menuicon' + item.name"></Icon>
                <span v-bind:class="[!item.children[0].perm ? 'menu-disabled' : '', 'layout-text']" :key="'title' + item.name">{{ itemTitle(item.children[0]) }}</span>
            </MenuItem>

            <Submenu v-if="item.children.length > 1" :name="item.name" :key="item.name">
                <template slot="title">
                    <Icon :type="item.icon" :size="iconSize"></Icon>
                    <span class="layout-text">{{ itemTitle(item) }}</span>
                </template>
                <template v-for="child in item.children">
                    <MenuItem :name="child.name" :key="'menuitem' + child.name" :disabled="!child.perm">
                        <Icon :type="child.icon" :size="iconSize" v-bind:class="[!child.perm ? 'menu-disabled' : '']" :key="'icon' + child.name"></Icon>
                        <span v-bind:class="[!child.perm ? 'menu-disabled' : '', 'layout-text']" :key="'title' + child.name">{{ itemTitle(child) }}</span>
                    </MenuItem>
                </template>
            </Submenu>
        </template>
    </Menu>
</template>

<script>
export default {
    name: 'sidebarMenu',
    props: {
        menuList: Array,
        iconSize: Number,
        menuTheme: {
            type: String,
            default: 'dark'
        },
        openNames: {
            type: Array
        }
    },
    methods: {
        changeMenu (active) {
            this.$emit('on-change', active);
        },
        itemTitle (item) {
            if (typeof item.title === 'object') {
                return this.$t(item.title.i18n);
            } else {
                return item.title;
            }
        }
    },
    updated () {
        this.$nextTick(() => {
            // if (this.$refs.sideMenu) {
            //     this.$refs.sideMenu.updateOpened();
            // }
        });
    }

};
</script>
