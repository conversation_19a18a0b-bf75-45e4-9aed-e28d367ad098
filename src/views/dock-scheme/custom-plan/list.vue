<template>
  <div class="yop-page-content">
    <Card :bordered="false">
      <Form class="yop-search-form" :model="searchForm" :label-width="82" label-position="right">
        <Row type="flex" :gutter="10">
          <Col span="24">
            <Col v-bind="grid">
              <FormItem label="方案名称:">
                <Input
                  id="inputDocName"
                  v-model="searchForm.solutionName"
                  clearable
                  :maxlength="30"
                  placeholder="方案名称"
                  @on-enter="search_interface(false)"
                ></Input>
              </FormItem>
            </Col>
            <Col v-bind="grid">
              <FormItem label="方案编码:">
                <Input
                  id="inputDocCode"
                  v-model="searchForm.solutionCode"
                  clearable
                  :maxlength="30"
                  placeholder="方案编码"
                  @on-enter="search_interface(false)"
                ></Input>
              </FormItem>
            </Col>
            <Col v-bind="grid">
              <FormItem label="创建人:">
                <Input
                  id="inputDocCode"
                  v-model="searchForm.operator"
                  placeholder="创建人"
                  clearable
                  @on-enter="search_interface(false)"
                ></Input>
              </FormItem>
            </Col>
            <Col v-bind="grid">
              <FormItem label="创建时间:">
                <date-picker
                  class="create-date"
                  ref="datepicker"
                  @on-start-change="update_start_date"
                  @on-end-change="update_end_date"
                  :default_start="this.searchForm.createdStartDate"
                  :default_end="this.searchForm.createdEndDate"
                ></date-picker>
              </FormItem>
            </Col>
            <Col v-bind="grid">
              <FormItem :label-width="20">
                <Button id="btnDocSearch" class="margin-right-10" type="primary" @click="search_interface(false)">查询</Button>
                <Button id="btnDocReset" type="ghost" @click="reset_Interface">重置</Button>
              </FormItem>
            </Col>
          </Col>
        </Row>
      </Form>
      <Row class="margin-top-10">
        <Col span="24">
          <Button class="margin-right-10" id="btnCreatedDoc" type="primary" v-url="{url:'/rest/custom-solution/create'}" @click="operation('create')">创建自定义解决方案</Button>
        </Col>
      </Row>
      <Row class="margin-top-10">
        <Col span="24">
          <Table
            id="table_1"
            border
            :columns="columns"
            :data="dataList"
          ></Table>
          <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
            <Page
              class="margin-top-10"
              style="float: right"
              :total="pageTotal"
              :page-size="10"
              :current="pageNo"
              show-elevator
              @on-change="pageRefresh"
            ></Page>
          </Tooltip>
        </Col>
        <loading :show="show_loading_serviceAuthList"></loading>
      </Row>

    </Card>
    <Modal
      v-model="createPlanModal"
      :title="createPlanTitle"
      width="750"
      @on-cancel="cancelPlanForm"
    >
      <Form ref="createPlanForm" class="create-plan-modal margin-top-10" :model="createPlanForm" :rules="planFormRules" :label-width="110">
        <FormItem
          label="方案名称:"
          prop="solutionName"
          key="solutionName"
        >
          <Input
            v-model.trim="createPlanForm.solutionName"
            :maxlength="30"
            placeholder="请输入方案名称"
          ></Input>
          <div class="form-info">将作为方案名称展示到前台，最长支持输入30个字符</div>
        </FormItem>
        <FormItem
          label="方案编码:"
          prop="solutionCode"
          key="solutionCode"
        >
          <div style="display: flex; justify-content: space-between">
            <Input
              v-model.trim="createPlanForm.solutionCode"
              :maxlength="30"
              placeholder="请输入方案编码"
              :disabled="createPlanType === 'update'"
            />
            <Button
              v-if="createPlanType !== 'update'"
              type="primary"
              class="uploadVideo"
              style="width: 60px;margin-left: 10px;"
              @click="createsolutionCode"
            >生成
            </Button>
          </div>
          <div class="form-info">将作为访问文档URL的一部分；仅支持小写字母、数字、“-”、最长输入30个字符；创建后不支持修改</div>
        </FormItem>
        <FormItem
          label="服务提供方编码:"
          prop="spCode"
        >
          <common-select-sp
            ref="select_sp_m"
            @on-update="updateSelect_sp_model"
            v-model="createPlanForm.spCode"
            type="combo"
            keyWord="result"
            holder="请选择服务提供方"
            code="spCode"
            title="spName"
            size="small"
            group="serviceSupplier"
            id="product_id_04"
            @on-loaded="select_callBack_service"
            :default="this.serviceSupplier"
            :uri="this.$store.state.select.serviceSupplier.uri"
            style="width:100%"
          ></common-select-sp>
        </FormItem>
        <FormItem label="描述">
          <el-input
            type="textarea"
            placeholder="请输入方案描述"
            v-model="createPlanForm.description"
            maxlength="300"
            size="small"
            rows="3"
            show-word-limit
          ></el-input>
          <div class="form-info">最长支持输入300个字符</div>
        </FormItem>
      </Form>
      <div class="text-center" slot="footer">
        <Button type="ghost" @click="cancelPlanForm">取消</Button>
        <Button type="primary" @click="confirmPlanForm" :loading="confirmLoading">确认</Button>
      </div>
    </Modal>
    <Modal
      v-model="shareModal"
      class="share-modal"
      title="分享"
      :footer-hide="true"
    >
      <div class="share-content">
        <span class="label">分享链接：</span>
        <span class="link">{{shareLink}}</span>
        <Button class="copy-btn" type="ghost" size="small" style="width: 50px;" @click="copyShareLink(shareLink)">复制</Button>
      </div>
    </Modal>
    <Modal
      v-model="relationProductModal"
      title="关联产品"
      width="650"
      @on-cancel="cancelProductForm"
    >
      <Form ref="relationProductForm" class="margin-top-10" :model="relationProductForm" :rules="relationProductRules" :label-width="74">
        <FormItem
          label="关联产品:"
          prop="productCodeList"
          key="productCodeList"
        >
          <common-select
            id="select_acl_doc"
            ref="select_sp"
            @on-update="updateSelect_sp"
            type="combo"
            keyWord="result"
            holder="请选择关联产品"
            code="spCode"
            title="spName"
            group="serviceSupplier"
            :default="relationProductForm.productCodeList"
            :uri="this.$store.state.select.serviceSupplier.uri"
          ></common-select>
        </FormItem>
      </Form>
      <div class="text-center" slot="footer">
        <Button type="ghost" @click="cancelProductForm">取消</Button>
        <Button type="primary" @click="confirmProductForm" :loading="confirmLoading">确认</Button>
      </div>
    </Modal>
    <RelevanceApiModal ref="RelevanceApiModal" />
  </div>
</template>

<script>
import loading from '../../my-components/loading/loading';
import commonSelect from '../../common-components/select-components/selectCommon_multiple';
import commonSelectSp from '../../common-components/select-components/selectCommon';
import datePicker from '../../common-components/date-components/date-picker';
import util from '../../../libs/util';
import api from '../../../api/api';
// import Api from '~/api/doc/createdDoc';
import RelevanceApiModal from './modify_models/RelevanceApiModal'
import { debounce } from 'throttle-debounce'
import pinyin from 'js-pinyin'
pinyin.setOptions({checkPolyphone: false, charCase: 1})

export default {
  name: 'plan-list',
  components: {
    loading,
    commonSelect,
    commonSelectSp,
    RelevanceApiModal,
    datePicker
  },
  data () {
    return {
      grid: {
        xl: 8,
        lg: 8,
        md: 8,
        sm: 24,
        xs: 24
      },
      searchForm: {
        solutionName: '',
        solutionCode: '',
        operator: '',
        createdStartDate: '',
        createdEndDate: ''
      },
      shareLink: '',
      shareModal: false,
      relationApiModal: false,
      relationProductModal: false,
      confirmLoading: false,
      createPlanModal: false,
      createPlanTitle: '创建自定义解决方案',
      createPlanForm: {
        solutionName: '',
        solutionCode: '',
        description: '',
        spCode: ''
      },
      serviceSupplier: '',
      createPlanType: 'create',
      planFormRules: {
        solutionName: [
          { required: true, message: '请输入方案名称', trigger: 'change' }
        ],
        solutionCode: [
          { required: true, message: '请输入或生成方案编码', trigger: 'change' }
        ],
        spCode: [
          { required: true, message: '请选择服务提供方' }
        ]
      },
      relationProductForm: {
        productCodeList: []
      },
      relationProductRules: {
        productCodeList: [
          { required: true, type: 'array', min: 1, message: '请选择关联产品', trigger: 'change' }
        ]
      },

      // getProductCodesList () {
      //   this.productCodesShow = true
      //   Api.getProductCodesList()
      //     .then(response => {
      //       if (response.data.status === 'success') {
      //         this.productCodesList = response.data.data.result
      //         // 权限分组，仅有权限的可以编辑
      //         if (this.totalProductCodes.length > 0) {
      //           this.totalProductCodes.forEach(item => {
      //             if (this.productCodesList.find(product => product.productCode === item)) {
      //               this.params.productCodes.push(item)
      //             } else {
      //               this.noAuthProductCodesList.push(item)
      //             }
      //           })
      //         }
      //       } else {
      //         this.$ypMsg.notice_error(this, '错误', response.data.message);
      //       }
      //     })
      //     .finally(() => {
      //       this.productCodesShow = false
      //     })
      // },
      //  * 确认弹窗部分
      // 确认弹窗标题
      modal_confirm_title: '',
      // 确认弹窗红色提示部分
      modal_confirm_warning_red: '',
      // 确认弹窗黑色提示部分
      modal_confirm_warning_black: '',
      // 确认弹窗必填原因规范提示
      modal_confirm_reason_fail: '',
      // 确认弹窗原因描述
      modal_confirm_desc: '',
      // 确认弹窗原因长度限制
      modal_confirm_reason_length: 100,
      // 确认弹窗原因是否展示
      modal_confirm_reason_show: true,
      // new
      product_code: '',
      cause: '',
      deleteProduct: false,
      normalProduct: false,
      offlineProduct: false,
      delete_or_normal_modal_show: false,
      productType: '',
      // 新增产品
      modal_add_product: false,
      // 是否是编辑
      create_orEdit: true,
      form_detail: {
      },
      rule_detail: {
      },
      data_select_visible_List: [
        {label: '公开', value: 'PUBLIC'},
        // {label:"私有",value:"PRIVATE"},
        {label: '授权可见', value: 'PROTECTED'}
      ],
      data_select_status_List: [
        {label: '草稿', value: 'DRAFT'},
        {label: '编辑中', value: 'EDIT'},
        {label: '已发布', value: 'PUBLISHED'},
        {label: '已删除', value: 'DELETED'}
      ],
      // 是否是顶级资源
      top_resource: false,
      // 授权类型列表
      type_list: [],
      // 列表loading
      show_loading_serviceAuthList: false,
      // 产品编码输入框数据绑定
      data_product_code: '',
      data_product_name: '',
      data_doc_name: '',
      data_doc_code: '',
      data_product_apiUri: '',
      data_product_type: '',
      service_provision_code: '',
      spCode: '',
      // 授权状态下拉框数据绑定
      data_select_status: '',
      // 授权有效期数据绑定
      data_select_type: '',
      data_select_visible: '',
      data_select_provider: '',
      // 下拉框个数
      count_select_related: 2,
      // 总数
      pageTotal: 10,
      // 当前页码
      pageNo: 1,
      // 文档列表表头
      columns: [
        {
          title: '自定义解决方案',
          key: 'solutionName',
          minWidth: 180
        },
        {
          title: '自定义解决方案编码',
          key: 'solutionCode',
          minWidth: 150
        },
        {
          title: '描述',
          key: 'description',
          minWidth: 200
        },
        {
          title: '创建人',
          key: 'operatorCode',
          minWidth: 120
        },
        {
          title: '创建时间',
          key: 'createdDate',
          minWidth: 150
        },
        {
          title: '操作',
          align: 'center',
          key: 'operations',
          width: 216,
          render: (h, params) => {
            return h('div', [
              h(
                'Button',
                {
                  class: 'btnCustomSolutionRelatedApiAdd',
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  directives: [{
                    name: 'url',
                    value: {url: '/rest/custom-solution/related-api/add'}
                  }],
                  on: {
                    click: () => {
                      this.operation('relationApi', params.row);
                    }
                  }
                },
                '关联API'
              ),
              h(
                'Button',
                {
                  class: 'btnCustomSolutionEdit',
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  directives: [{
                    name: 'url',
                    value: {url: '/rest/custom-solution/edit'}
                  }],
                  on: {
                    click: () => {
                      this.operation('update', params.row);
                    }
                  }
                },
                '编辑'
              ),
              h(
                params.row.hasDoc ? 'Button' : '',
                {
                  class: 'btnCustomSolutionEdit',
                  props: {
                    type: 'text',
                    size: 'small',
                    show: false
                  },
                  directives: [{
                    name: 'url',
                    value: {url: '/rest/custom-solution/copy'}
                  }],
                  on: {
                    click: () => {
                      this.operation('copy', params.row);
                    }
                  }
                },
                '复制'
              ),
              h(
                'Button',
                {
                  class: 'btnCustomSolutionShare',
                  props: {
                    type: 'text',
                    size: 'small'
                  },
                  directives: [{
                    name: 'url',
                    value: {url: '/rest/custom-solution/share'}
                  }],
                  on: {
                    click: () => {
                      this.operation('copyLink', params.row);
                    }
                  }
                },
                '分享'
              )
            ]);
          }
        }
      ],

      // 服务授权数据
      dataList: [],
      // 开始日期绑定
      dateStart: '',
      // 结束日期绑定
      dateEnd: '',
      data_interface_uri: '',
      search_Interface: '',
      productCode: ''
    };
  },
  created () {
    this.$watch('createPlanForm.solutionCode', debounce(200, () => {
      this.createPlanForm.solutionCode = this.createPlanForm.solutionCode.replace(/[^a-z\d-]/g, '')
      this.createPlanForm.solutionCode = this.createPlanForm.solutionCode.substring(0, 30)
    }))
  },
  methods: {
    select_callBack_service (resultTemp) {
      if (resultTemp) {
        this.createPlanForm.spCode = resultTemp.spCode;
        this.serviceSupplier = resultTemp.spCode;
        console.log(this.serviceSupplier, 'lllll')
      }
    },
    updateSelect_sp_model (val) {
      this.serviceSupplier = val;
      this.createPlanForm.spCode = val
    },
    created_doc () {
      this.$refs.CreatedDoc.showAddModal()
    },
    changeVisible (value) {
      this.data_select_visible = value
    },
    changeStatus (value) {
      this.data_select_status = value
    },
    // 产品列表查询函数
    search_interface (val) {
      console.log(this.searchForm.createDate)
      this.show_loading_serviceAuthList = true;
      let params = {
        solutionName: this.searchForm.solutionName.trim(),
        solutionCode: this.searchForm.solutionCode.trim(),
        operatorCode: this.searchForm.operator.trim(),
        createdStartDate: util.dateFormat_component(this.searchForm.createdStartDate),
        createdEndDate: util.dateFormat_component_end(this.searchForm.createdEndDate),
        _pageNo: 1
        // _pageSize: 10
      };
      if (val) {
        params._pageNo = val;
      }
      console.log(params)
      util.paramFormat(params);
      api.yop_custom_solution_list(params).then(response => {
        if (response.data.status === 'success') {
          this.dataList = response.data.data.page.items
          this.pageNo = response.data.data.page.pageNo;
          if (
            response.data.data.page.items &&
            response.data.data.page.items.length > 0
          ) {
            if (response.data.data.page.items.length < 10) {
              this.pageTotal = response.data.data.page.items.length;
            } else {
              this.pageTotal = NaN;
            }
          } else {
            this.pageTotal = NaN;
          }
        } else {
          this.$ypMsg.notice_error(
            this,
            '列表获取错误',
            response.data.message,
            response.data.solution
          );
        }
        this.show_loading_serviceAuthList = false;
      });
    },
    // 重置查询添加函数
    reset_Interface () {
      this.searchForm = {
        solutionName: '',
        solutionCode: '',
        operator: '',
        createdStartDate: '',
        createdEndDate: ''
      }
    },
    update_start_date (val) {
      this.searchForm.createdStartDate = val;
    },
    update_end_date (val) {
      this.searchForm.createdEndDate = val;
    },
    createsolutionCode () {
      const arr = this.createPlanForm.solutionName.split('')
      const _arr = []
      arr.map(item => {
        const _item = pinyin.getFullChars(item)
        _arr.push((_item && _item[0]) || '')
      })
      this.createPlanForm.solutionCode = _arr.join('')
    },
    operation (type, row) {
      this.createPlanType = type
      switch (type) {
        case 'create':
          this.$refs.select_sp_m.resetSelected();
          this.createPlanTitle = '创建自定义解决方案'
          this.createPlanModal = true
          break;
        case 'update':
          this.createPlanForm = {
            id: row.id,
            solutionName: row.solutionName,
            solutionCode: row.solutionCode,
            description: row.description,
            spCode: row.spCode,
            status: 'NORMAL',
            type: 'CUSTOM'
          }
          this.select_callBack_service(this.createPlanForm)
          this.createPlanTitle = '编辑自定义解决方案'
          this.createPlanModal = true
          break;
        case 'copy':
          this.createPlanForm = {
            id: row.id,
            solutionName: '',
            solutionCode: '',
            description: '',
            spCode: row.spCode,
            sourceSolutionCode: row.solutionCode,
            status: 'NORMAL',
            type: 'CUSTOM'
          }
          this.select_callBack_service(this.createPlanForm)
          this.createPlanTitle = '复制自定义解决方案'
          this.createPlanModal = true
          break;
        case 'copyLink':
          this.getShareLink(row.solutionCode)
          break;
        // case 'relationProduct':
        //   this.relationProductForm.productCodeList = []
        //   this.relationProductModal = true
        //   break;
        case 'relationApi':
          this.$refs.RelevanceApiModal.showModal(row.solutionCode || 'solutionCode')
          break;
      }
    },
    getShareLink (solutionCode) {
      this.show_loading_serviceAuthList = true;
      api.yop_custom_solution_share({ solutionCode: solutionCode })
        .then(response => {
          if (response.data.status === 'success') {
            this.shareLink = response.data.data.result.docUrl
            this.shareModal = true
          } else {
            this.$ypMsg.notice_error(this, '错误', response.data.message, response.data.solution);
          }
        })
        .finally(() => {
          this.show_loading_serviceAuthList = false;
        })
    },
    copyShareLink (value) {
      this.$copyText(value);
      this.$ypMsg.notice_success(this, '复制完成')
    },
    confirmPlanForm () {
      this.$refs.createPlanForm.validate((valid) => {
        if (valid) {
          this.confirmLoading = true
          let apiName
          switch (this.createPlanType) {
            case 'update':
              apiName = 'yop_custom_solution_edit'
              break;
            case 'create':
              apiName = 'yop_custom_solution_create'
              break;
            case 'copy':
              apiName = 'yop_custom_solution_copy'
              break;
          }
          api[apiName](this.createPlanForm)
            .then(response => {
              if (response.status === 'success') {
                this.search_interface()
                this.cancelPlanForm();
              } else {
                this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
              }
            })
            .finally(() => {
              this.confirmLoading = false
            })
        }
      });
    },
    cancelPlanForm () {
      this.createPlanModal = false;
      this.$nextTick(() => {
        this.createPlanForm.description = ''
        this.$refs.createPlanForm.resetFields();
      })
    },
    updateSelect_sp (val) {
      this.relationProductForm.productCodeList = val;
    },
    cancelProductForm () {
      this.relationProductModal = false;
      this.$nextTick(() => {
        this.$refs.relationProductForm.resetFields();
      })
    },
    confirmProductForm () {
      this.$refs.relationProductForm.validate((valid) => {
        if (valid) {
          this.confirmLoading = true
          api.createDoc(this.params)
            .then(response => {
              if (response.status === 'success') {
                this.search_interface()
                this.cancelProductForm();
              } else {
                this.$ypMsg.notice_error(this, '错误', response.message, response.solution);
              }
            })
            .finally(() => {
              this.confirmLoading = false
            })
        }
      });
    },
    // 服务提供方页面跳转
    sp_jump () {
      const {href} = this.$router.resolve({
        name: '/isp/list'
      });
      window.open(href, '_blank');
    },
    // 页面刷新
    pageRefresh (val) {
      this.search_interface(val)
    },
    // 初始化页面
    init () {
      this.search_interface();
    }
  },
  mounted () {
    this.init();
  }
};
</script>

<style lang="less" scoped>
@import "../../../styles/common.less";
@import "../../API_Management_Open/api_Mangement_Open.less";

.share-modal {
  .share-content{
    display: flex;
    align-items: self-start;
    justify-content: flex-start;
    padding: 30px 20px 50px;
    font-size: 14px;

    .label {
      width: 74px;
      font-weight: bold;
    }
    .link {
      flex: 1;
      word-break: break-all;
      word-wrap: break-word;
      white-space: pre-wrap;
    }

    .copy-btn{
      margin-left: 20px;
      width: 50px;
    }

  }
}

.create-plan-modal{
  margin: 10px 20px 0 10px;
  /deep/ .el-textarea__inner{
    padding: 4px 7px;
    font-size: 12px;
    color: #495060;
  }
}


.form-info{
  margin-top: 4px;
  line-height: 14px;
  font-size: 12px;
  color: #999999;
}

.ivu-form-item-error {
  margin-bottom: 42px;
  .form-info {
    display:  none;
  }
}
.demo-badge-alone {
  background: #5cb85c !important;
}
.round {
  width: 16px;
  height: 16px;
  display: inline-block;
  font-size: 20px;
  line-height: 16px;
  text-align: center;
  color: #f00;
  text-decoration: none;
}

.reasonType {
  vertical-align: top;
  display: inline-block;
  font-size: 12px;
  margin-top: 5px;
}
.ivu-modal-header ,.ivu-modal-footer{
}
.ivu-icon-help-circled:before{
  content:"\F142";
}
</style>
