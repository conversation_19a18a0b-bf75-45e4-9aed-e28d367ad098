<template>
	<div>
		<Modal v-model="show" width="1000">
			<p slot="header" style="color: #2d8cf0">
				<span style="color: black">已关联API</span>
			</p>
			<Card dis-hover :bordered="false">
				<Row type="flex" align="middle">
					<Col span="24">
						<Col span="8">
							<Col span="6" class="margin-top-5">
								<span>API名称:</span>
							</Col>
							<Col span="18">
								<Input clearable v-model="params.apiTitle" id="inputapiTitle" placeholder="API名称"></Input>
							</Col>
						</Col>
						<Col offset="1" span="7">
							<Col span="6" class="margin-top-5">
								<span>API URL:</span>
							</Col>
							<Col span="18">
								<Input clearable v-model="params.apiUri" id="inputApiUrl" placeholder="API URL"></Input>
							</Col>
						</Col>
					</Col>
				</Row>
				<Row type="flex" justify="start" class="margin-top-20">
					<Col span="12">
						<Button id="batch-btn-cancel"  type="ghost" @click="batchUncontactApi">批量取消</Button>
						<Button id="adpp-api-btn" type="primary" @click="addApi">添加关联API</Button>
					</Col>
					<Col span="12" type="flex" align="end">
						<Button id="relve-btnSearch" type="primary" @click="getList(1)">查询</Button>
						<Button id="relve-btnReset" type="ghost" @click="reset_Interface">重置</Button>
					</Col>
				</Row>
				<Row class="margin-top-20">
					<Table
						id="table-relve"
						border
						ref="singleTable"
						:columns="columns"
						:data="data"
						@on-selection-change="handleTableSelection"
						row-key="id"
					></Table>
					<Loading :show="loading"></Loading>
				</Row>
				<Row class="margin-top-20" type="flex" justify="end">
					<Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
            <Page
              class="margin-top-10"
              style="float: right"
              :total="total"
              :page-size="20"
              :current="pageNo"
              show-elevator
              @on-change="onPageChange"
            ></Page>
<!--						<Page :total="total" :current="params._pageNo" show-elevator @on-change="onPageChange" />-->
					</Tooltip>
				</Row>
			</Card>
			<div slot="footer">
				<Button type="ghost" id="relveCancel" @click="closeModal">关闭</Button>
			</div>
		</Modal>
		<SelectApiModal ref="SelectApiModal" @confirm="selectApiConfirm" />
	</div>
</template>
<script>
import Loading from '~/views/my-components/loading/loading';
import SelectApiModal from './SelectApiModal';
import Api from '~/api/spi'
import api from '../../../../api/api'

export default {
  components: {
    Loading,
    SelectApiModal
  },
  data () {
    return {
      show: false,
      loading: false,
      total: 20,
      pageNo: 1,
      params: {
        solutionCode: '',
        apiTitle: '',
        apiUri: '',
        _pageNo: 1,
        _pageSize: 20
      },
      switchOneInfo: {},
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center'
        },
        {
          title: '已关联API',
          key: 'title',
          render: (h, params) => {
            return h('div', [
              h('p', params.row.apiTitle),
              h('p', params.row.apiUri)]
            );
          }
        },
        {
          title: '是否必要',
          width: 150,
          key: 'title',
          render: (h, params) => {
            return h('i-switch', {
              props: {
                value: params.row.required
              },
              on: {
                'on-change': (value) => {
                  console.log('on-change:', value, params.index)
                  this.changeApiRequired(value, params);
                }
              }
            })
          }
        },
        {
          title: '操作',
          width: 100,
          render: (h, params) => {
            return h('div', [
              h('Button', {
                props: {
                  type: 'text',
                  size: 'small'
                },
                on: {
                  click: () => {
                    this.uncontactApi([params.row.apiId]);
                  }
                }
              }, '取消关联')
            ])
          }
        }
      ],
      data: [],
      solutionCode: '',
      selectApiIds: []
    }
  },
  methods: {
    closeModal () {
      this.show = false
    },
    showModal (solutionCode) {
      this.solutionCode = solutionCode
      this.params.solutionCode = solutionCode
      this.getList(1)
      this.show = true
    },
    addApi () {
      this.$refs.SelectApiModal.show_model({
        solutionCode: this.solutionCode
      })
    },
    getList (val) {
      this.loading = true
      if (val) {
        this.params._pageNo = val
      }
      api.yop_related_api_list(this.params)
        .then(response => {
          if (response.data.status === 'success') {
            this.data = response.data.data.page.items
            this.pageNo = response.data.data.page.pageNo;
            if (
              response.data.data.page.items &&
              response.data.data.page.items.length > 0
            ) {
              if (response.data.data.page.items.length < 10) {
                this.total = response.data.data.page.items.length;
              } else {
                this.total = NaN;
              }
            } else {
              this.total = NaN;
            }
          } else {
            this.$Modal.error({
              title: '错误',
              content: response.message
            });
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    reset_Interface () {
      this.params = {
        solutionCode: this.solutionCode,
        apiTitle: '',
        apiUri: '',
        _pageNo: 1,
        _pageSize: 20
      }
    },
    handleTableSelection (val) {
      this.selectApiIds = val
    },
    selectApiConfirm (apiIds) {
      api.yop_related_api_add({
        solutionCode: this.solutionCode,
        apiIds
      })
        .then(res => {
          if (res.status === 'success') {
            this.$ypMsg.notice_success(this, '关联API成功')
            this.getList()
          } else {
            this.$Modal.error({
              title: '错误',
              content: res.message
            });
          }
        })
    },
    batchUncontactApi () {
      if (this.selectApiIds.length === 0) {
        this.$ypMsg.notice_warning(this, '请选择需要取消的API后再点击');
        return;
      }
      this.uncontactApi(this.selectApiIds.map(item => item.apiId))
    },
    uncontactApi (apiIds) {
      this.$Modal.confirm({
        content: '确认取消关联API？',
        okText: '确认',
        onOk: () => {
          api.yop_related_api_remove({
            solutionCode: this.solutionCode,
            apiIds
          })
            .then(res => {
              if (res.status === 'success') {
                this.getList()
              } else {
                this.$Modal.error({
                  title: '错误',
                  content: res.message
                });
              }
            })
        }
      });
    },
    changeApiRequired (value, params) {
      this.loading = true
      api.yop_related_api_require({
        solutionCode: this.solutionCode,
        apiId: params.row.apiId,
        required: value
      })
        .then(res => {
          if (res.status === 'success') {
            // this.$set(this.data, params.index, {...params.row, required: value});
            this.getList()
          } else {
            this.$Modal.error({
              title: '错误',
              content: res.message
            });
            this.$set(this.data, params.index, {...params.row, required: !value});
          }
        })
        .catch(() => {
          this.$set(this.data, params.index, {...params.row, required: !value});
        })
        .finally(() => {
          this.loading = false
        })
    },
    onPageChange (val) {
      this.params._pageNo = val
      this.getList()
    }
  }
}
</script>

<style>
</style>
