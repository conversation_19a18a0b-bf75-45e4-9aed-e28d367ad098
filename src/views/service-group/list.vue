<style scoped>
    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;
    }

    .el-checkbox__inner {
        width: 12px !important;
        height: 12px !important;
    }

    .custom-tree-node {
        font-size: 12px;
    }

    .yop-explain-120 {
        font-size: 12px;
        color: grey;
        padding-left: 120px;
        line-height: 30px;
    }
    .red{
        color:red
    }
    .green{
        color:green
    }
    /*@import '../API_Management_Open/api_Mangement_Open.less';*/
    @import '../../styles/common.less';
</style>
<template>
    <div style="background: #eee;padding:8px">
        <Card :bordered="false" dis-hover>
            <Row>
                <Col span="24" class="margin-bottom-10">
                <Button type="primary" class="margin-right-20" v-url="{url:'/rest/service-group/create'}" @click="add_top_resource()">新增顶级资源</Button>
                <Button type="primary" class="margin-right-20" v-url="{url:'/rest/service-group/delete'}" @click="batch_delete()">批量删除</Button>
                </Col>
                <Col span="10">
                <Card dis-hover style="min-width:455px;">
                    <p slot="title">
                        服务分组管理
                    </p>
                    <Input size="small" class="margin-bottom-10" placeholder="服务分组名称搜索" v-model="filterText"></Input>
                    <el-tree
                            draggable
                            ref="tree"
                            :data="data_tree"
                            show-checkbox
                            node-key="id"
                            :default-expand-all="false"
                            :filter-node-method="filterNode"
                            :expand-on-click-node="false"
                            :allow-drop="allowDrop"
                            @node-drop="handleDrop"
                            @node-click="nodeClick"
                    >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
        <span><Button v-show="node.data.enableStatus && node.data.depth!==1 " style="color: green" type="text" size="small" icon="ios-albums-outline"></Button>
        <Button v-show="!node.data.enableStatus && node.data.depth!==1" style="color: red" type="text" size="small" icon="ios-albums-outline"></Button>
            <Button v-show="node.data.depth ===1" style="color: grey" type="text" size="small" icon="ios-albums-outline"></Button>{{node.label}}</span>
        <span>
          <Button
                  type="text"
                  size="small"
                  shape="circle"
                  @click="tree_Create(data,$event)"
                  v-url="{url:'/rest/service-group/create'}"
                  v-show="data.depth < 2">

            新增
          </Button>
            <Button
                    type="text"
                    size="small"
                    shape="circle"
                    @click="top_tree_Change(node, data,$event,true)"
                    v-url="{url:'/rest/service-group/enable'}"
                    v-show="node.data.depth ===1"
            >
            启用
          </Button>
            <Button
                    type="text"
                    size="small"
                    shape="circle"
                    @click="top_tree_Change(node, data,$event,false)"
                    v-url="{url:'/rest/service-group/disable'}"
                    v-show="node.data.depth ===1"
            >
            禁用
          </Button>
            <Button
                    type="text"
                    size="small"
                    shape="circle"
                    v-show="node.data.depth !==1 && data.option === '启用'"
                    @click="tree_Change(node, data,$event)"
                    v-url="{url:'/rest/service-group/enable'}"
            >
            <!--启用-->
                {{data.option}}
          </Button>
            <Button
                    type="text"
                    size="small"
                    shape="circle"
                    v-show="data.option === '禁用' & node.data.depth !==1"
                    @click="tree_Change(node, data,$event)"
                    v-url="{url:'/rest/service-group/disable'}"
            >
            <!--启用-->
                {{data.option}}
          </Button>
             <Button
                     type="text"
                     size="small"
                     shape="circle"
                     v-url="{url:'/rest/service-group/delete'}"
                     @click="tree_Delete(node, data,$event)">
            删除
          </Button>
            <Button
                    type="text"
                    size="small"
                    shape="circle"
                    v-url="{url:'/rest/service-group/edit'}"
                    @click="tree_Edit(node, data,$event)">
            编辑
          </Button>
        </span>
      </span>
                    </el-tree>
                </Card>
                <loading :show="show_loading_tree"></loading>
                </Col>
                <Col span="14" v-show="add_resource" style="min-width:634px;">

                <Card class="margin-left-16" dis-hover >
                    <p v-show="!top_resource && create_orEdit" slot="title">
                        新增服务分组
                    </p>
                    <p v-show="top_resource && create_orEdit" slot="title">
                        新增服务分组
                    </p>
                    <p v-show="!create_orEdit" slot="title">
                        编辑服务分组
                    </p>
                    <a slot="extra" @click.prevent="add_close">
                        <Icon type="close"></Icon>
                    </a>
                    <Form ref="form_detail" :model="form_detail" :rules="rule_detail" :label-width="120">
                        <FormItem label="上级服务分组名称：" v-show="!top_resource">
                            {{form_detail.PServiceGroup}}
                        </FormItem>
                        <FormItem label="服务分组编码：" prop="serviceGroupCode" v-show="create_orEdit">
                            <Input type="text" size="small" v-model="form_detail.serviceGroupCode"
                                   style="width:85%"></Input>
                        </FormItem>
                        <FormItem label="服务分组编码：" v-show="!create_orEdit">
                            {{form_detail.serviceGroupCode}}
                        </FormItem>
                        <p class="yop-explain-120" v-show="create_orEdit">支持数字、小写字母、下划线，最长支持输入64个字符</p>
                        <FormItem label="服务分组名称：" prop="serviceGroupName">
                            <Input type="text" size="small" v-model="form_detail.serviceGroupName"
                                   style="width:85%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">支持汉字、数字，最长支持输入64个字符</p>
                        <FormItem label="授权方式：" prop="authType" v-show="!top_resource && create_orEdit">
                            <!--<FormItem label="状态：" prop="status">-->
                            <RadioGroup v-model="form_detail.authType">
                                <Radio :label="item.name"  v-for="(item, index) in type_list" :key="index">{{item.label}}</Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem label="授权方式：" v-show="!top_resource && !create_orEdit">
                            {{form_detail.authType}}
                        </FormItem>
                        <FormItem label="服务提供方：" v-show="top_resource" prop="spCode">
                            <common-select ref="select_sp_m" @on-update="updateSelect_sp_model"
                                           type="combo"
                                           keyWord="result"
                                           holder="请选择服务提供方"
                                           code="spCode"
                                           title="spName"
                                           size="small"
                                           group="serviceSupplier"
                                           @on-loaded="select_callBack"
                                           :default="this.form_detail.spCode"
                                           :uri="this.$store.state.select.serviceSupplier.uri"
                                           style="width:85%"></common-select>
                            <Button type="primary" shape="circle" size="small" icon="loop" style="margin-left:5px;" @click="refresh_sp"></Button>
                        </FormItem>
                        <p class="yop-explain-120"  v-show="top_resource">如果没有合适sp,<a @click="sp_jump">新增SP>></a></p>
                        <FormItem label="描述：" prop="description">
                            <Input type="textarea" size="small" v-model="form_detail.description"
                                   style="width:85%"></Input>
                        </FormItem>
                        <p class="yop-explain-120">最长支持输入300个字符</p>

                    </Form>
                    <div style="text-align: center">
                        <Button type="primary" class="margin-right-10" @click="add_submit('form_detail')">确定</Button>
                        <Button type="ghost" @click="add_close">关闭</Button>
                    </div>
                </Card>

                <loading :show="show_loading_edit"></loading>
                </Col>
                <Col span="14" v-show="edit_not_top_resource" style="min-width:634px;">
                <cardEditServiceGroup ref="card_edit_sg" @update-name="update_sg_name"></cardEditServiceGroup>
                </Col>

                <!--<Button @click="nodeChange">sdad</Button>-->
            </Row>
            <modal-sp-preview ref="modal_preview"></modal-sp-preview>
        </Card>
    </div>
</template>

<script>
    import commonSelect from '../common-components/select-components/selectCommon';
    import loading from '../my-components/loading/loading'
    import api from '../../api/api'
    import util from '../../libs/util'
    import qs from 'qs';
    import modalSpPreview from '../common-components/modal-components/modal-sp-preview'
    import cardEditServiceGroup from './modals/modal-edit-not-top'
    export default {
        name: 'service-group',
        watch: {
            filterText (val) {
                this.$refs.tree.filter(val);
            }
        },
        components:{
            commonSelect,
            loading,
            modalSpPreview,
            cardEditServiceGroup
        },
        data () {
            // 服务分组名称验证
            const validate_sgName = (rule, value, callback) => {
                if(util.format_check_common(value,/^[0-9\u4e00-\u9fa5]+?$/)){
                    callback(new Error('格式不正确'));
                }else if(util.getLength(value) > 64){
                    callback(new Error('长度不能大于64'));
                }else{
                    callback();
                }
            };
            // 服务分组编码验证
            const validate_sgCode = (rule, value, callback) => {
                if(util.format_check_common(value,/^[0-9a-z_]+?$/)){
                    callback(new Error('格式不正确'));
                }else
                    if(util.getLength(value) > 64){
                    callback(new Error('长度不能大于64'));
                }else{
                    // 添加重复校验接口
                    if(this.create_orEdit){
                        api.yop_service_group_check_exist({
                            code : value
                        }).then(
                            (response) =>{
                                if(response.data.status === 'success'){
                                    let result = response.data.data.result
                                    if(result){
                                        callback(new Error('该服务分组编码已存在'));
                                    }else{
                                        callback();
                                    }
                                }else{
                                    callback(new Error('验重失败'))
                                }
                            }
                        )
                    }else{
                        callback();
                    }
                }
            };
            // 服务分组描述验证
            const validate_resourceDes = (rule, value, callback) => {
                if(value){
                    if(util.getLength(value) > 300){
                        callback(new Error('长度不能大于300'));
                    }else{
                        callback();
                    }
                }else{
                    callback();
                }
            };
            return {
                // 授权类型列表
                type_list : [],

                edit_not_top_resource :false,
                // 权限规则数据绑定
                data_per_rule_List : [],
                // 当前是创建和编辑 创建
                create_orEdit : true,
                // 加载树loading
                show_loading_tree : false,
                // 编辑资源loading
                show_loading_edit : false,
                // 查看资源loading
                show_loading_preview : false,
                // 查看页面显示
                detail_resource : false,
                // 新增页面显示
                add_resource : false,
                // 筛选文字
                filterText : '',
                code_md5 : '',
                data_tree: [],
                defaultProps: {
                    children: 'children',
                    label: 'label'
                },
                // 当前选择的权限规则
                data_select_permission_rule : '',
                // 是否为顶级资源
                top_resource: false,
                form_detail: {
                    level: '',
                    PServiceGroup: '',
                    serviceGroupCode : '',
                    serviceGroupName : '',
                    authType: '',
                    description: '',
                    spCode : 'g123',
                },
                // 表单验证规则数据绑定
                rule_detail: {
                    serviceGroupCode: [
                        {required: true, message: '服务分组编码不能为空', trigger: 'blur'},
                        { validator: validate_sgCode, trigger: 'blur' }
                    ],
                    serviceGroupName: [
                        {required: true, message: '服务分组名称不能为空', trigger: 'blur'},
                        { validator: validate_sgName, trigger: 'blur' }
                    ],
                    spCode: [
                        {required: true, message: '服务提供方不能为空'},
                    ],
                    description : [
                        {validator: validate_resourceDes, trigger : 'blur'}
                    ]
                },
                // 当前节点数据
                current_nodeData : Object,
                // 当前节点数据
                current_dataTemp : Object,
                // 选中节点状态合集
                current_dataStatus : [],
                // 查看服务分组详情窗口
                modal_preview : true,
            };
        },
        methods: {
            refresh_sp(){
                this.$refs.select_sp_m.updateList();
            },
            // 树的添加
            tree_Create (data, e) {
                e.stopPropagation();
                this.$refs.card_edit_sg.preview_cancel();
                this.current_dataTemp = data;
                this.create_orEdit =true;
                this.top_resource = false;
                // this.form_detail.level = data.depth+1;
                // this.form_detail.spCode = 'g123';
                // if(this.type_list && this.type_list.length > 0){
                //     this.form_detail.authType = this.type_list[0].name;
                // }
                this.add_resource_data_init(data);
                this.form_detail.PServiceGroup = data.name;
                this.add_resource = true;
                this.detail_resource = false;
            },
            // 新增资源数据初始化
            add_resource_data_init(data){
                this.$refs.form_detail.resetFields();
                this.form_detail.PServiceGroup = '';
                this.form_detail.serviceGroupCode = '';
                this.form_detail.serviceGroupName = '';
                setTimeout (() =>{
                    this.form_detail.spCode = 'g123';
                },500);
                if(this.type_list && this.type_list.length > 0){
                    this.form_detail.authType = this.type_list[0].name;
                }
                this.form_detail.description = '';
                this.form_detail.level = data.depth+1;
            },
            // 树的删除
            tree_Delete (node, data, e) {
                e.stopPropagation();
                this.$Modal.confirm({
                    title: '删除服务分组',
                    content: "<p style='color:red'>如果删除服务分组，则影响正在使用该服务分组商户，导致无法继续调用。</p><p>确定删除服务分组吗？</p>",
                    'ok-text':'确定',
                    onOk: () =>{
                        // 删除操作
                        let param = {
                            codes : [data.code],
                            force : false
                        }
                        this.delete_node_handler(node, data,param);
                    }
                });
                // this.remove(node, data);
            },
            // 删除执行函数
            delete_node_handler (node,data,param) {
                // 新的删除函数
                api.yop_service_group_delete(param).then(
                    (response) =>{
                        if (response.status === 'success') {
                            if(response.data.result === 'success'){
                                this.$ypMsg.notice_success(this,'删除成功');
                                this.remove(node, data);
                                this.$refs.card_edit_sg.preview_cancel();
                            }else{
                                // 请求
                                setTimeout (() =>{
                                    let paramTemp = {
                                        codes : [data.code],
                                        force : true
                                    }
                                    this.delete_admit(node,data,paramTemp);
                                },500);
                            }
                        } else {
                            this.$ypMsg.notice_error(this,'删除错误',response.message,response.solution);
                        }
                    }
                );
            },
            // 删除确认函数
            delete_admit (node,data,param,batch){
                this.$Modal.confirm({
                    title: '删除服务分组',
                    content: "<p style='color:red'>已有商户授权该服务分组，禁用将影响商户无法调用该服务分组接口</p>" +
                    "<p>确定继续删除？</p>",
                    'ok-text':'确定',
                    onOk: () =>{
                        // 启用服务分组操作
                        api.yop_service_group_delete(param).then(
                            (response) =>{
                                if (response.status === 'success') {
                                    if(response.data.result === 'success'){
                                        this.$ypMsg.notice_success(this,'删除成功');
                                        if(batch){
                                            this.information_tree_get();
                                            this.$refs.card_edit_sg.preview_cancel();
                                        }else{
                                            this.remove(node, data);
                                            this.$refs.card_edit_sg.preview_cancel();
                                        }
                                    }else{
                                        this.$ypMsg.notice_error_simple(this,'删除错误','服务分组删除失败');
                                    }
                                } else {
                                    this.$ypMsg.notice_error(this,'删除错误',response.message,response.solution);
                                }
                            }
                        );
                        // 请求
                        // data.option = '启用';
                        // data.status = '禁用';
                        // data.enableStatus = false;
                    }
                });
            },
            // 树的启用禁用
            tree_Change (node, data, e) {
                e.stopPropagation();
                if (data.option === '启用') {
                    this.enable_modal(node,data,e);
                    // this.enable_status(data);
                    // data.status = '禁用';
                } else {
                    this.disable_modal(node,data,e);
                    // data.status = '启用';
                    // this.enable_status(data);
                }
            },
            top_tree_Change(node,data,e,option){
                e.stopPropagation();
                if(option){
                    this.enable_modal(node,data,e);
                }else{
                    this.disable_modal(node,data,e);
                }
            },
            // 启用弹窗
            enable_modal(node, data, e){
                this.$Modal.confirm({
                    title: '启用服务分组',
                    content: "<p style='color:red'>如果启用服务分组，则申请授权该服务分组商户能正常调用API接口。</p>" +
                    "<p>确定启用服务分组吗？</p>",
                    'ok-text':'确定',
                    onOk: () =>{
                        // 启用服务分组操作
                        api.yop_service_group_enable({code : data.code,level : data.depth}).then(
                            (response) =>{
                                if (response.status === 'success') {
                                    this.$ypMsg.notice_success(this,'启用成功');
                                    if(data.children && data.children.length > 0 && data.depth ===1){
                                        data.children.forEach(
                                            item=>{
                                                item.option = '禁用';
                                                item.status = '启用';
                                                item.enableStatus = true;
                                            }
                                        )

                                    }else{
                                        data.option = '禁用';
                                        data.status = '启用';
                                        data.enableStatus = true;
                                    }

                                } else {
                                    this.$ypMsg.notice_error(this,'启用错误',response.message,response.solution);
                                }
                            }
                        );

                    }
                });
            },
            // 禁用弹窗
            disable_modal(node, data, e){
                this.$Modal.confirm({
                    title: '禁用服务提供方',
                    content: "<p style='color:red'>如果禁用服务分组，则影响正在使用该服务分组商户，导致无法继续调用。</p>" +
                    "<p>确定禁用服务分组吗？</p>",
                    'ok-text':'确定',
                    onOk: () =>{
                        // 禁用确认请求
                        api.yop_service_group_disable({code : data.code,level : data.depth,
                            force: false}).then(
                            (response) =>{
                                if (response.status === 'success') {
                                    if(response.data.result === 'success'){
                                        this.$ypMsg.notice_success(this,'禁用成功');
                                        if(data.children && data.children.length > 0 && data.depth ===1){
                                            data.children.forEach(
                                                item=>{
                                                    item.option = '启用';
                                                    item.status = '禁用';
                                                    item.enableStatus = false;
                                                }
                                            )

                                        }else{
                                            data.option = '启用';
                                            data.status = '禁用';
                                            data.enableStatus = false;
                                        }

                                    }else{
                                        // 请求
                                        setTimeout (() =>{
                                            this.disable_admit(data);
                                        },500);
                                    }
                                } else {
                                    this.$ypMsg.notice_error(this,'禁用错误',response.message,response.solution);
                                }
                            }
                        );

                    }
                });
            },
            // 禁用服务确定提供方弹窗
            disable_admit (data){
                this.$Modal.confirm({
                    title: '禁用服务分组',
                    content: "<p style='color:red'>已有商户授权该服务分组，禁用将影响商户无法调用该服务分组接口</p>" +
                    "<p>确定继续禁用？</p>",
                    'ok-text':'确定',
                    onOk: () =>{
                        // 启用服务分组操作
                        api.yop_service_group_disable({code : data.code,
                            force: true,level : data.depth}).then(
                            (response) =>{
                                if (response.status === 'success') {
                                    if(response.data.result === 'success'){
                                        this.$ypMsg.notice_success(this,'禁用成功');
                                        if(data.children && data.children.length > 0 && data.depth ===1){
                                            data.children.forEach(
                                                item=>{
                                                    item.option = '启用';
                                                    item.status = '禁用';
                                                    item.enableStatus = false;
                                                }
                                            )

                                        }else{
                                            data.option = '启用';
                                            data.status = '禁用';
                                            data.enableStatus = false;
                                        }
                                    }else{
                                        this.$ypMsg.notice_error_simple(this,'禁用错误','服务分组禁用失败');
                                    }
                                } else {
                                    this.$ypMsg.notice_error(this,'禁用错误',response.message,response.solution);
                                }
                            }
                        );
                        // 请求
                        // data.option = '启用';
                        // data.status = '禁用';
                        // data.enableStatus = false;
                    }
                });
            },
            // 启用禁用状态数据获取
            enable_status (val) {
                this.current_nodeData = val;
            },
            // 树的编辑
            tree_Edit (node, data, e) {
                e.stopPropagation();
                if (data.top) {
                    this.top_resource = true;
                } else {
                    this.top_resource = false;
                }
                if(data.top){
                    this.edit_not_top_resource = false;
                    this.create_orEdit = false;
                    this.add_resource = true;
                    this.data_add_top_init();
                    this.$refs.card_edit_sg.preview_cancel();
                    this.current_dataTemp = data;
                    this.show_loading_edit = true;
                    this.detail_info_get_top(data.code,this.type_list);
                }else{
                    this.edit_not_top_resource = true;
                    this.add_resource = false;
                    this.current_dataTemp = data;
                    this.$refs.card_edit_sg.tab_init();
                    this.$refs.card_edit_sg.preview_show();
                    this.$refs.card_edit_sg.detail_info_get(data.code,this.type_list);
                }
            },
            // 详细信息获取
            detail_info_get_top (code,list) {
                // 详细信息接口
                this.current_code = code;
                api.yop_service_group_detail({code:code}).then(
                    response =>{
                        let status = response.data.status;
                        if (status === 'success') {
                            let result = response.data.data.result;
                            this.detail_info_handler_top(result,list);
                        } else {
                            this.$ypMsg.notice_error(this,'服务分组详细信息获取失败,请刷新重试',response.data.message,response.data.solution);
                            this.add_resource = false;
                        }
                        this.show_loading_edit = false;
                    }
                )
            },
            // 详细信息处理
            detail_info_handler_top (result,type_list){
                this.data_add_top_init();
                this.form_detail.PServiceGroup = result.pName;
                this.form_detail.serviceGroupCode = result.code;
                this.form_detail.serviceGroupName = result.name;
                if(this.type_list && this.type_list.length > 0){
                    this.form_detail.authType = this.type_list[0].name;
                }else{
                    this.form_detail.authType = 'ONLY_AWARD';
                }
                this.form_detail.spName = this.data_name_handler(result.spCode, 'serviceSupplier');
                this.form_detail.spCode = result.spCode;
                this.form_detail.description = result.description;
                this.form_detail.level = 1;
            },
            // 返回相应编码的名字
            data_name_handler (code, name,list) {
                if (code) {
                    let group = []
                    if(name){
                        group = this.$store.state.select[name].data;
                        for (var i in group) {
                            if (group[i].value === code) {
                                return group[i].name;
                            }
                        }
                    }else{
                        group = list
                        for (var i in group) {
                            if (group[i].name === code) {
                                return group[i].label;
                            }
                        }
                    }

                } else {
                    return '';
                }
            },
            // 树的添加执行函数
            append (data,code,top) {
                let newChild_notTop ={}
                let nameTemp = '';
                if(util.getLength(this.form_detail.serviceGroupName) > 10){
                    nameTemp = this.form_detail.serviceGroupName.substring(0,10)+'...'
                }else{
                    nameTemp = this.form_detail.serviceGroupName
                }
                if(top){
                    let newChild_top ={
                        code: this.form_detail.serviceGroupCode,
                        top : top,
                        label : nameTemp,
                        name : this.form_detail.serviceGroupName,
                        depth: 1,
                        status : '启用',
                        option : '禁用',
                        color : 'green',
                        enableStatus : true
                    }
                    this.data_tree.push(newChild_top);
                }else{
                    newChild_notTop = {
                        code: this.form_detail.serviceGroupCode,
                        top : top,
                        label : nameTemp,
                        name : this.form_detail.serviceGroupName,
                        depth: this.form_detail.level+1,
                        status : '启用',
                        option : '禁用',
                        color : 'green',
                        enableStatus : true
                    };
                    if (!data.children) {
                        this.$set(data, 'children', []);
                    }
                    data.children.push(newChild_notTop);
                }


            },
            // 资源树的删除执行函数
            remove (node, data) {
                const parent = node.parent;
                const children = parent.data.children || parent.data;
                const index = children.findIndex(d => d.code === data.code);
                children.splice(index, 1);
            },
            nodeChange () {
                console.log(this.$refs.tree.getHalfCheckedNodes());
            },
            filterNode (value, data) {
                if (!value) return true;
                return data.label.indexOf(value) !== -1;
            },
            allowDrop (draggingNode, dropNode, type) {
                if(draggingNode.data.depth > 1){
                    if(draggingNode.parent.data.code !== dropNode.parent.data.code && type !== 'inner' &&  dropNode.data.depth === draggingNode.data.depth){
                        return true;
                    }else if(type === 'inner' && dropNode.data.depth+1 === draggingNode.data.depth ){
                        return true;
                    }
                }
            },
            // 拖拽提示
            drag_notice () {
                this.$Message.warning('不允许拖动到此菜单中');
            },
             nodeClick (data) {
                if (data.top) {
                    this.top_resource = true;
                    this.$refs.modal_preview.manage_hide();
                } else {
                    this.top_resource = false;
                    this.$refs.modal_preview.manage_show();
                }
                let param = {
                    code : data.code
                }
                this.add_resource = false;
                // this.modal_preview =true;
                this.$refs.modal_preview.tab_init();
                this.$refs.modal_preview.preview_show(this.top_resource);
                this.$refs.modal_preview.detail_info_get(data.code,this.type_list);
                // this.detail_resource = true;
            },
            // 关闭详情
            detail_close () {
                this.detail_resource = false;
            },
            // 添加提交
            add_submit (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        let param = {}
                        if(!this.top_resource && this.create_orEdit){//新增非顶级服务分组
                            param = this.param_notTop_handler();
                            this.resource_create_handler(param,false);
                        }else if(this.top_resource && this.create_orEdit){
                            param = this.param_top_handler();
                            this.resource_create_handler(param,true);
                        }else if(this.top_resource && !this.create_orEdit){
                            param = this.param_top_edit_handler();
                            this.resource_edit_handler(param);
                        }
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 添加请求处理
            resource_create_handler (param,top) {
                api.yop_service_group_create(param).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'服务分组添加成功');
                            this.add_resource = false;
                            let code = param.code
                            this.append(this.current_dataTemp,code,top);
                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            this.add_resource = false;
                        }
                    }
                )
            },
            // 修改请求处理
            resource_edit_handler (param) {
                api.yop_service_group_edit(param).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'服务分组修改成功');
                            this.add_resource = false;
                            this.update_sg_name(param.name)
                        }else{
                            this.$ypMsg.notice_error(this,'服务分组修改错误',response.message,response.solution);
                            this.add_resource = false;
                        }
                    }
                )
            },
            //更新服务分组名称
            update_sg_name(val){
                let nameTemp = val;
                this.current_dataTemp.name = nameTemp
                if(util.getLength(val) > 10){
                    nameTemp = val.substring(0,10)+'...'
                }else{
                    nameTemp = val
                }
                this.current_dataTemp.label = nameTemp
            },
            //非顶级资源编辑参数处理
            param_notTop_edit_handler () {
                let visiableTypeTemp = 'YOP'
                if(this.form_detail.permission === 'YOP可见'){
                    visiableTypeTemp = 'YOP'
                }else if(this.form_detail.permission === '全部可见'){
                    visiableTypeTemp = 'ALL'
                }else{
                    visiableTypeTemp = 'SP'
                }
                let param = {
                    id : this.form_detail.id,
                    name : this.form_detail.name,
                    url : this.form_detail.url,
                    permId : this.form_detail.permId,
                    description : this.form_detail.description,
                    visibleType : visiableTypeTemp
                }
                return param;
            },
            //顶级资源编辑参数处理
            param_top_edit_handler () {
                let param = {
                    code: this.form_detail.serviceGroupCode,
                    spCode : this.form_detail.spCode,
                    name : this.form_detail.serviceGroupName,
                    description : this.form_detail.description,
                    level : this.form_detail.level
                }
                return param;
            },
            // 非顶级资源参数处理
            param_notTop_handler () {
                let param = {
                    code : this.form_detail.serviceGroupCode,
                    name : this.form_detail.serviceGroupName,
                    pCode : this.current_dataTemp.code,
                    level : this.form_detail.level,
                    authorizeType : this.form_detail.authType,
                    description : this.form_detail.description
                }
                return param;
            },
            // 顶级资源参数处理
            param_top_handler () {
                let param = {
                    code : this.form_detail.serviceGroupCode,
                    name : this.form_detail.serviceGroupName,
                    spCode : this.form_detail.spCode,
                    level : 1,
                    description : this.form_detail.description
                }
                return param;
            },
            // 顶级资源添加数据初始化
            data_add_top_init(){
                this.$refs.form_detail.resetFields();
                this.form_detail.PServiceGroup = '';
                this.form_detail.serviceGroupCode = '';
                this.form_detail.serviceGroupName = '';
                this.$refs.select_sp_m.resetSelected();
                this.form_detail.spCode = 'g123';
                if(this.type_list && this.type_list.length > 0){
                    this.form_detail.authType = this.type_list[0].name;
                }
                this.form_detail.description = '';
                this.form_detail.level = 1;
            },
            // 添加提交
            add_close () {
                this.add_resource = false;
            },
            // 错误数组处理
            error_array_handler (array) {
                let result = ''
                for(var i in array){
                    if(i === array.length-1){
                        result =result + array[i]
                    }else{
                        result =result + array[i]+','
                    }
                }
                return result;
            },
            // 新增顶级资源
            add_top_resource () {
                this.data_add_top_init();
                this.create_orEdit =true;
                this.add_resource = true;
                this.detail_resource = false;
                this.top_resource = true;
                this.$refs.card_edit_sg.preview_cancel();
            },
            // 批量删除
            batch_delete () {
                if(this.get_allCheckedNodes().ids.length > 0){
                    this.$Modal.confirm({
                        title: '删除服务分组',
                        content: "<p style='color:red'>如果删除服务分组，则影响正在使用该服务分组商户，导致无法继续调用。</p><p>确定删除服务分组吗？</p>",
                        'ok-text':'确定',
                        onOk: () =>{
                            // 删除操作
                            let ids = this.get_allCheckedNodes().ids;
                            let nodes = this.get_allCheckedNodes().nodes;
                            let param = {
                                codes : ids,
                                force : false
                            }
                            this.delete_node_handler_batch(nodes,param);
                        }
                    });

                }else{
                    this.$Modal.warning(
                        {
                            title: '警告',
                            content: '至少选择一个资源'
                        }
                    )
                }

            },
            // 批量删除执行函数
            delete_node_handler_batch (nodes,param) {
                api.yop_service_group_delete(param).then(
                    (response) =>{
                        if (response.status === 'success') {
                            if(response.data.result === 'success'){
                                this.$ypMsg.notice_success(this,'删除成功');
                                this.information_tree_get();
                                this.$refs.card_edit_sg.preview_cancel();
                            }else{
                                // 请求
                                setTimeout (() =>{
                                    let paramTemp = {
                                        codes : param.codes,
                                        force : true
                                    }
                                    this.delete_admit(nodes,'',paramTemp,true);
                                },500);
                            }
                        } else {
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                        }
                    }
                );
                // nodes.forEach(
                //     item =>{
                //         this.remove(item, item.data);
                //     }
                // )
                // api.yop_acl_resource_delete(param).then(
                //     (response) =>{
                //         if(response.status === 'success'){
                //             this.$Notice.success({
                //                 title : '成功',
                //                 desc :'资源删除成功',
                //                 duration : 5
                //             })
                //             this.information_tree_get();
                //         }else{
                //             this.$Notice.error({
                //                 title : '错误',
                //                 desc : response.message,
                //                 duration : 10
                //             });
                //         }
                //     }
                // )
            },
            // 初始化
            init () {
                this.information_tree_get();
                this.type_list_get();
            },
            // 数据类型获取
            type_list_get () {
                api.yop_service_group_auth_type().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.type_list = [];
                        let dataTemp = [];
                        if(resultTemp && resultTemp.length >0) {
                                resultTemp.forEach(
                                    (item)=> {
                                        dataTemp.push(
                                            {
                                                label: item.name,
                                                name : item.code
                                            }
                                        )
                                    })
                        }
                        this.type_list = dataTemp;
                        this.form_detail.authType = this.type_list[0].name;
                    }
                )
            },
            updateSelect_permission_rule (val) {
                this.form_detail.per_rule = val;
            },
            // 获取所有选择节点
            get_allCheckedNodes () {
                let ids = []
                let nodes = this.$refs.tree.getCheckedNodes();
                if(!!nodes){
                    nodes.forEach(
                        item =>{
                            ids.push(item.code)
                        }
                    )
                }
                return {
                    ids: ids,
                    nodes: nodes
                };
            },
            // 树的结构信息获取
            information_tree_get () {
                this.show_loading_tree = true;
                api.yop_service_group_list().then(
                    (response) =>{
                        let status =response.data.status
                        if(status === 'success'){
                            let result = response.data.data.result;
                            this.data_tree = [];
                            this.data_tree = this.information_tree_handler(result,0);
                        }else{
                            this.$ypMsg.notice_error(this,'服务分组列表数据获取失败,请刷新重试',response.data.message,response.data.solution);
                        }
                        this.show_loading_tree =false;
                    }
                )
            },
            // 树的结构数据处理
            information_tree_handler (data,index) {
                let topTemp =true;
                if(index === 0){
                    topTemp = true;

                }else{
                    topTemp =false;
                }
                let returnData = [];
                index ++;
                if(data){
                    data.forEach(
                        (item) =>{
                            let enableStatusTemp = true
                            let statusTemp = '启用'
                            let optionTemp = '禁用'
                            let colorTemp = 'green'
                            let nameTemp = ''
                            if(item.status === 'ENABLE'){
                                statusTemp = '启用'
                                optionTemp = '禁用'
                                colorTemp = 'green'
                                enableStatusTemp = true
                            }else{
                                statusTemp = '禁用'
                                optionTemp = '启用'
                                colorTemp = 'red'
                                enableStatusTemp = false
                            }
                            if(util.getLength(item.name) > 10){
                                nameTemp = item.name.substring(0,10)+'...'
                            }else{
                                nameTemp = item.name
                            }
                            if(!!item.children){
                                returnData.push({
                                    // id: item.id,
                                    // pid : item.pid,
                                    code: item.code,
                                    top : topTemp,
                                    label : nameTemp,
                                    name : item.name,
                                    depth: item.level,
                                    status : statusTemp,
                                    option : optionTemp,
                                    color : colorTemp,
                                    enableStatus : enableStatusTemp,
                                    children : this.information_tree_handler(item.children,index)
                                })
                            }else{
                                returnData.push({
                                    // id: item.id,
                                    // pid : item.pid,
                                    code: item.code,
                                    top : topTemp,
                                    label : nameTemp,
                                    name : item.name,
                                    depth: item.level,
                                    status : statusTemp,
                                    option : optionTemp,
                                    color : colorTemp,
                                    enableStatus : enableStatusTemp,
                                    children : []
                                })
                            }
                        }
                    )
                }
                return returnData;
            },
            //  拖拽操作
            nodeDrop (before,after,inner,event) {
                let pid = after.id;
            },

            // 判断是否所有选中的节点状态数组
            allNodes_status (nodes,status){
                if(!!nodes){
                    for(var i in nodes){
                        if(nodes[i].enableStatus !== status){
                            return false;
                        }
                    }
                    return true;
                }else{
                    return false;
                }

            },
            // 拖拽后台执行函数
            handleDrop(draggingNode, dropNode, dropType, ev) {
                if(draggingNode.data.depth > 1){
                    if(dropType === 'inner'){
                        let param ={
                            code : draggingNode.data.code,
                            pCode : dropNode.data.code
                        }
                        this.drop_handler(param);
                    }else{
                        let param ={
                            code : draggingNode.data.code,
                            pCode : dropNode.parent.data.code
                        }
                        this.drop_handler(param);
                    }
                }
            },
            // 拖拽执行函数
            drop_handler (param) {
                api.yop_service_group_drag(param).then(
                    (response) =>{
                        if(response.status === 'success'){

                        }else{
                            this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                            this.information_tree_get();
                        }
                    }
                )
            },
            // 获取appendToId
            appendToId_get (id,parent){
                if(parent){
                    for(var i in parent){
                        if(parent[i].id === id){
                            if(i > 0){
                                return parent[i-1].id
                            }else{
                                return ''
                            }
                        }
                    }
                    return '';
                }
            },
            // 获取pid
            targetPid_get (draggingNode, dropNode, dropType){
                if(dropType === 'inner'){
                    return dropNode.data.id;
                }else{
                    if(dropNode.parent.data.id){
                        return dropNode.parent.data.id;
                    }else{
                        return 0;
                    }

                }
            },
            // 弹窗服务提供方数据更新
            updateSelect_sp_model (val) {
                this.form_detail.spCode = val;
            },
            // 下拉框加载完处理函数
            select_callBack () {

            },
            // 服务提供方页面跳转
            sp_jump () {
                const {href} = this.$router.resolve({
                    name: "/isp/list",
                });
                window.open(href, '_blank');
            }
        },
        mounted () {
            this.init();
        }
    };
</script>


