<style scoped>
    .yop-explain-150 {
        font-size: 12px;
        color: grey;
        padding-left: 150px;
        line-height: 30px;
    }
    .ivu-table-cell {
        padding-left : 10px;
        padding-right : 10px;
    }
    /*@import '../../API_Management_Open/api_Mangement_Open.less';*/
    @import '../../../styles/common.less';
</style>
<template>
    <Card class="margin-left-16" dis-hover v-show="model_show">
        <p slot="title">
            编辑服务分组
        </p>
        <a slot="extra" @click.prevent="preview_cancel">
            <Icon type="close"></Icon>
        </a>
        <Card dis-hover>
            <p slot="title">
                基本信息
            </p>
            <a slot="extra">
                <Button type="primary" size="small" @click="save_basic('form_detail')">保存</Button>
            </a>
            <Form ref="form_detail" :rules="rule_detail" :model="form_detail" :label-width="150">
                <FormItem label="上一级服务分组名称：" >
                    {{form_detail.PServiceGroup}}
                </FormItem>
                <FormItem label="服务分组编码：">
                    {{form_detail.serviceGroupCode}}
                </FormItem>
                <FormItem label="服务分组名称：" prop="serviceGroupName">
                    <Input type="text" size="small" v-model="form_detail.serviceGroupName"
                           style="width:85%"></Input>
                </FormItem>
                <p class="yop-explain-150">支持汉字、数字，最长支持输入64个字符</p>
                <FormItem label="授权方式：" v-show="!top_resource">
                    {{form_detail.authType}}
                </FormItem>
                <FormItem label="描述：" prop="description">
                    <Input type="textarea" size="small" v-model="form_detail.description"
                           style="width:85%"></Input>
                </FormItem>
                <p class="yop-explain-150">最长支持输入300个字符</p>
            </Form>
        </Card>
        <Card dis-hover>
            <p slot="title">
                服务分组API管理
            </p>
            <Tabs :value="tabNow">
                <TabPane id="tab_basic_1" label="API接口" name="api_interface">
                    <Row>
                        <Col span="24">
                        <Button class="margin-right-10"  size="small" type="primary" v-url="{url:'/rest/service-group/api/link'}" @click="api_link_modal_show">添加api</Button>
                        <Button class="margin-right-10"  size="small" type="ghost" v-url="{url:'/rest/service-group/api/unlink'}" @click="batch_unlink_api">批量移除</Button>
                        </Col>
                    </Row>
                    <Row class="margin-top-10">
                        <Col span="24">
                        <Table id='table_1' border ref="api_interface" :columns="columns_ApiInterfaceList"
                               :data="data_ApiInterfaceList" @on-selection-change="handleselection1"></Table>
                        <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                            <Page class="margin-top-10" style="float: right" :total="pageTotalInterface" :page-size="10"
                                  :current="pageNoInteface" show-elevator @on-change="pageRefreshInteface"></Page>
                        </Tooltip>
                        </Col>
                    </Row>
                    <loading :show="show_loading_interface"></loading>
                </TabPane>
                <TabPane id="tab_basic_2" label="API分组" name="api_group">
                    <Row>
                        <Col span="24">
                        <Button class="margin-right-10"  size="small" type="primary" v-url="{url:'/rest/service-group/api-group/link'}" @click="apiGroup_link_modal_show">添加api分组</Button>
                        <Button class="margin-right-10" size="small" type="ghost" v-url="{url:'/rest/service-group/api-group/unlink'}" @click="batch_unlink_apiGroup">批量移除</Button>
                        </Col>
                    </Row>
                    <Row class="margin-top-10">
                        <Col span="24">
                        <Table id='table_2' border ref="api_group" :columns="columns_ApiGroupList"
                               :data="data_ApiGroupList" @on-selection-change="handleselection2"></Table>
                        <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                            <Page class="margin-top-10" style="float: right" :total="pageTotalGroup" :page-size="10"
                                  :current="pageNoGroup" show-elevator @on-change="pageRefreshGroup"></Page>
                        </Tooltip>
                        </Col>
                    </Row>
                    <loading :show="show_loading_group"></loading>
                </TabPane>
            </Tabs>
        </Card>
        <div style="text-align: center;margin-top:10px;">
            <Button type="ghost" @click="preview_cancel">关闭</Button>
        </div>
        <loading :show="show_loading_preview"></loading>
        <modal-link-api ref="modal_link_api" :code="current_code" @update-list="pageRefreshInteface"></modal-link-api>
        <modal-link-api-group ref="modal_link_api_group" :code="current_code" @update-list="pageRefreshGroup"></modal-link-api-group>
    </Card>
</template>

<script>
    import api from '../../../api/api';
    import loading from '../../my-components/loading/loading';
    import util from '../../../libs/util'
    import modalLinkApi from './modal-link-api'
    import modalLinkApiGroup from './modal-link-api-group'
    export default {
        name: 'modal-edit-not-top',
        components: {
            loading,
            modalLinkApi,
            modalLinkApiGroup
        },
        data () {
            // 服务分组名称验证
            const validate_sgName = (rule, value, callback) => {
                if(util.format_check_common(value,/^[0-9\u4e00-\u9fa5]+?$/)){
                    callback(new Error('格式不正确'));
                }else if(util.getLength(value) > 64){
                    callback(new Error('长度不能大于64'));
                }else{
                    callback();
                }
            };
            // 服务分组描述验证
            const validate_resourceDes = (rule, value, callback) => {
                if(value){
                    if(util.getLength(value) > 300){
                        callback(new Error('长度不能大于300'));
                    }else{
                        callback();
                    }
                }else{
                    callback();
                }
            };
            return {
                // api选中
                multiSelectedData_api : [],
                // api分组选中
                multiSelectedData_apiGroup : [],
                // loading interface
                show_loading_interface : false,
                // loading apiGroup
                show_loading_group : false,
                // 是否为顶级资源 true为顶级 false为非顶级
                top_resource: false,
                // 编辑界面显示隐藏
                model_show: true,
                // loading
                show_loading_preview: false,
                // api分组列表
                data_serviceSupplier_List: [],
                // 选择数据绑定
                select_data_serviceSupplier: '',
                // 基本信息数据绑定
                form_detail: {
                    PServiceGroup: '',
                    PServiceGroupCode: '',
                    serviceGroupCode: '',
                    serviceGroupName: '',
                    authType: '',
                    authTypeCode: '',
                    description: ''
                },
                // 表单验证规则数据绑定
                rule_detail: {
                    serviceGroupName: [
                        {required: true, message: '服务分组名称不能为空', trigger: 'blur'},
                        { validator: validate_sgName, trigger: 'blur' }
                    ],
                    description : [
                        {validator: validate_resourceDes, trigger : 'blur'}
                    ]
                },
                tabNow: 'api_interface',
                // api接口列表
                columns_ApiInterfaceList: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    {
                        title: 'API 接口',
                        key: 'name',
                        'min-width': 150,
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.name),
                                h('p', '(' + params.row.uri + ')')
                            ]);
                        },
                        align: 'center'
                    },
                    {
                        title: '版本',
                        width: 70,
                        key: 'version',
                        align: 'center'
                    },
                    {
                        title: '状态',
                        render: (h, params) => {
                            if(params.row.status){
                                let color = 'red';
                                let status = '已删除';
                                if (params.row.status === 'ACTIVE') {
                                    color = 'green';
                                    status = '活动中';
                                } else if (params.row.status === 'FROZEN') {
                                    color = 'grey';
                                    status = '已下线';
                                } else {
                                    color = 'red';
                                    status = '已禁用';
                                }
                                return h('div', [
                                    h('Tag', {
                                        style: {
                                            align: 'center'
                                        },
                                        props: {
                                            color: color
                                        }
                                    }, status)
                                ]);
                            }else{
                                return h('div','--');
                            }


                        },
                        width: 95,
                        align: 'center'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'operations',
                        width: 150,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/regression-test/test/list'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.regression_jump(params.row.uri,'api');
                                        }
                                    }
                                }, '回归测试'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/service-group/api-group/link'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.api_unlink(params.row.uri,'api');
                                        }
                                    }
                                }, '删除')
                            ]);
                        }
                    }

                ],
                // api接口列表数据
                data_ApiInterfaceList: [
                    // {
                    //     id: '1',
                    //     name: ' E账通支付',
                    //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
                    //     version: '1.0',
                    //     status: 'ACTIVE',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-01 10:30:00'
                    // },
                    //
                    // {
                    //     id: '2',
                    //     name: ' 订单查询',
                    //     uri: '/rest/v1.0/balance/cash',
                    //     version: '1.1',
                    //     status: 'FROZEN',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id: '3',
                    //     name: ' 退款\n' +
                    //     '\n',
                    //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
                    //     version: '1.0',
                    //     status: 'DELETE',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id: '4',
                    //     name: ' E账通支付',
                    //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
                    //     version: '1.0',
                    //     status: 'ACTIVE',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id: '5',
                    //     name: ' E账通支付',
                    //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
                    //     version: '1.0',
                    //     status: 'ACTIVE',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-01 10:30:00'
                    // },
                    //
                    // {
                    //     id: '6',
                    //     name: ' 订单查询',
                    //     uri: '/rest/v1.0/balance/cash',
                    //     version: '1.1',
                    //     status: 'FROZEN',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id: '7',
                    //     name: ' 退款\n' +
                    //     '\n',
                    //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
                    //     version: '1.0',
                    //     status: 'DELETE',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id: '8',
                    //     name: ' E账通支付',
                    //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
                    //     version: '1.0',
                    //     status: 'ACTIVE',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-01 10:30:00'
                    // },
                    // {
                    //     id: '9',
                    //     name: ' E账通支付',
                    //     uri: '/rest/v1.0/balance/yop-group-remit/transfer-query',
                    //     version: '1.0',
                    //     status: 'ACTIVE',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-01 10:30:00'
                    // },
                    //
                    // {
                    //     id: '10',
                    //     name: ' 订单查询',
                    //     uri: '/rest/v1.0/balance/cash',
                    //     version: '1.1',
                    //     status: 'FROZEN',
                    //     createTime: '2018-02-01 10:30:00',
                    //     lastModifyTime: '2018-05-01 10:30:00'
                    // }

                ],
                // api接口总页数
                pageTotalInterface: 10,
                // api接口当前页数
                pageNoInteface: 1,
                // api接口列表
                columns_ApiGroupList: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    {
                        title: 'API分组',
                        'min-width': 140,
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.apiGroupName),
                                h('p', '(' + params.row.apiGroupCode + ')')
                            ]);
                        },
                        align: 'center'
                    },

                    {
                        title: 'API分组安全需求',
                        width: 180,
                        key: 'apiGroup_security',
                        align: 'center',
                        type: 'html'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        key: 'operations',
                        width: 150,
                        render: (h, params) => {
                            return h('div', [
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/regression-test/test/list'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.regression_jump(params.row.apiGroupCode,'apiGroup');
                                        }
                                    }
                                }, '回归测试'),
                                h('Button', {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    directives: [{
                                        name: 'url',
                                        value: {url: '/rest/service-group/api-group/unlink'}
                                    }],
                                    on: {
                                        click: () => {
                                            this.api_unlink(params.row.apiGroupCode,'apiGroup');
                                        }
                                    }
                                }, '删除')
                            ]);
                        }
                    }

                ],
                // api接口列表数据
                data_ApiGroupList: [
                    // {
                    //     apiGroupCode: 'test_doc_wy',
                    //     apiGroupName: '双因子',
                    //     apiGroup_security: 'YOP-HMAC-AES128<br/>YOP-HMAC-AES256<br/>YOP-HMAC-AES512',
                    //     description: 'XX子系统',
                    //     createTime: '2018-02-01 10:30:00',
                    //
                    // },
                    // {
                    //     apiGroupCode: 'duanxin',
                    //     apiGroupName: '短信',
                    //     apiGroup_security: 'YOP-HMAC-AES128<br/>YOP-HMAC-AES256<br/>YOP-HMAC-AES512',
                    //     description: 'sertrstyrtydrtdrtfcggchcbvgh',
                    //     createTime: '2018-02-01 10:30:00',
                    //
                    // }
                ],
                // api接口总页数
                pageTotalGroup: 10,
                // api接口当前页数
                pageNoGroup: 1,
                // 当前服务分组编码
                current_code : '',
                // api列表选中

            };
        },
        methods: {
            // 组件初始化获取
            init () {

            },
            // 更新数据
            updateData () {
                this.$emit('on-update', this.select_data_serviceSupplier);
            },
            // 窗口关闭按钮
            preview_cancel () {
                this.model_show = false;
            },
            // 窗口显示按钮
            preview_show () {
                this.model_show = true;
                this.show_loading_preview = true;
                this.tabNow ='api_interface';
            },
            // 获取详细信息
            detail_info_get (code,list) {
                // 详细信息接口
                this.current_code = code;
                api.yop_service_group_detail({code:code}).then(
                    response =>{
                        let status = response.data.status;
                        if (status === 'success') {
                            let result = response.data.data.result;
                            this.detail_info_handler(result,list);
                        } else {
                            this.$ypMsg.notice_error(this,'服务分组详细信息获取失败,请刷新重试',response.data.message,response.data.solution);
                        }
                        this.show_loading_preview = false;
                    }
                )
                this.pageRefreshInteface();
                this.pageRefreshGroup();
            },
            // 获取详细信息
            detail_info_handler (result,type_list) {
                // 详细信息接口
                this.data_add_top_init();
                this.form_detail.PServiceGroup = result.pName;
                this.form_detail.PServiceGroupCode = result.pCode;
                this.form_detail.serviceGroupCode = result.code;
                this.form_detail.serviceGroupName = result.name;
                this.form_detail.authType = this.data_name_handler(result.authorizeType,false,type_list);
                this.form_detail.description = result.description;
            },
            data_add_top_init(){
                this.form_detail.PServiceGroup = '';
                this.form_detail.PServiceGroupCode = '';
                this.form_detail.serviceGroupCode = '';
                this.form_detail.serviceGroupName = '';
                this.form_detail.authType = '';
                this.form_detail.description = '';
            },
            // 返回相应编码的名字
            data_name_handler (code, name,list) {
                if (code) {
                    let group = []
                    if(name){
                        group = this.$store.state.select[name].data;
                        for (var i in group) {
                            if (group[i].value === code) {
                                return group[i].name;
                            }
                        }
                    }else{
                        group = list
                        for (var i in group) {
                            if (group[i].name === code) {
                                return group[i].label;
                            }
                        }
                    }

                } else {
                    return '';
                }
            },
            pageRefreshInteface (val) {
                this.show_loading_interface = true;
                let params = {
                    code : this.current_code,
                    _pageNo: 1,
                    _pageSize: 10
                };
                if (val) {
                    params._pageNo = val;
                }
                util.paramFormat(params);
                api.yop_service_group_api_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat_interface(response.data.data.page.items);
                            this.pageNoInterface = response.data.data.page.pageNo;
                            this.pageTotalInterface = response.data.data.page.totalPageNum * 10;
                            this.show_loading_interface = false;
                        } else {
                            this.$Modal.error({
                                title: 'API接口加载错误',
                                content: response.data.message
                            });
                            this.data_ApiInterfaceList = [];
                            this.show_loading_interface = false;
                        }
                    }
                );
            },
            // 关联接口数据处理
            tableDataFormat_interface (items){
                this.data_ApiInterfaceList = [];
                for (var i in items) {
                    this.data_ApiInterfaceList.push({
                        name : util.empty_handler(items[i].apiName),
                        uri : util.empty_handler(items[i].apiUri),
                        version : util.empty_handler(items[i].version),
                        status : items[i].status,
                        createTime : util.empty_handler(items[i].createdDate)
                    });
                }
            },
            pageRefreshGroup (val) {
                this.show_loading_group = true;
                let params = {
                    code : this.current_code,
                    _pageNo: 1,
                    _pageSize: 10
                };
                if (val) {
                    params._pageNo = val;
                }
                util.paramFormat(params);
                api.yop_service_group_api_group_list(params).then(
                    (response) => {
                        if (response.data.status === 'success') {
                            this.tableDataFormat_group(response.data.data.page.items);
                            this.pageNoGroup = response.data.data.page.pageNo;
                            this.pageTotalGroup = response.data.data.page.totalPageNum * 10;
                            this.show_loading_group = false;
                        } else {
                            this.$Modal.error({
                                title: 'API分组加载错误',
                                content: response.data.message
                            });
                            this.data_ApiGroupList = [];
                            this.show_loading_group = false;
                        }
                    }
                );
            },
            // 关联api分组数据处理
            tableDataFormat_group (items){
                this.data_ApiGroupList = [];
                for (var i in items) {
                    this.data_ApiGroupList.push({
                        apiGroupCode : util.empty_handler(items[i].code),
                        apiGroupName : util.empty_handler(items[i].name),
                        apiGroup_security : this.data_security_handler(items[i].security),//可能是个数组
                        description : items[i].description,
                        createTime : util.empty_handler(items[i].createdDate)
                    });
                }
            },
            // 安全需求处理函数
            data_security_handler(securities){
                let data_security = ''
                if(securities && securities.length > 0){
                    for(var i in securities){
                        if(i === securities.length){
                            data_security = data_security +securities[i]
                        }else{
                            data_security = data_security +securities[i]+'<br/>'
                        }
                    }
                    return data_security;
                }else{
                    return '';
                }
            },
            // tab初始化
            tab_init () {
                this.tabNow = 'api_interface';
            },
            // 基本信息保存
            save_basic (val) {
                this.$refs[val].validate((valid) => {
                    if (valid) {
                        let param = this.param_edit_handler();
                        this.resource_edit_handler(param);
                    } else {
                        this.$Message.error('请检查');
                    }
                })
            },
            // 修改请求处理
            resource_edit_handler (param) {
                api.yop_service_group_edit(param).then(
                    (response) =>{
                        if(response.status === 'success'){
                            this.$ypMsg.notice_success(this,'服务分组修改成功');
                            // let nameTemp = param.name;
                            // this.current_dataTemp.name = nameTemp
                            // if(util.getLength(param.name) > 10){
                            //     nameTemp = param.name.substring(0,10)+'...'
                            // }else{
                            //     nameTemp = param.name
                            // }
                            // this.current_dataTemp.label = nameTemp
                            this.$emit('update-name', param.name);
                        }else{
                            this.$ypMsg.notice_error(this,'服务分组修改错误',response.message,response.solution);
                        }
                    }
                )
            },
            // 资源编辑参数处理
            param_edit_handler(){
                let param = {
                    code: this.form_detail.serviceGroupCode,
                    pCode : this.form_detail.PServiceGroupCode,
                    name : this.form_detail.serviceGroupName,
                    description : this.form_detail.description,
                    level : 2
                }
                return param;
            },
            // 回归测试跳转
            regression_jump (data,type) {
                if(type === 'api'){
                    // api跳转
                    localStorage.rergession_api = data;
                    this.$router.push({
                        name: '/regression-test/test/list'
                    });
                }else{
                    // api分组跳转
                    localStorage.rergession_apiGroup = data;
                    this.$router.push({
                        name: '/regression-test/test/list'
                    });
                }
            },
            // api接口移除
            api_unlink(data,type) {
                if(type === 'api'){
                    //api移除
                    let param = {
                        code : this.current_code,
                        apiUris: [data]
                    }
                    this.api_unlink_handler(param);
                }else{
                    //api分组移除
                    let param = {
                        code: this.current_code,
                        apiGroupCodes: [data]
                    };
                    this.api_group_unlink_handler(param);
                }
            },
            // api接口移除执行
            api_unlink_handler (param) {
                api.yop_service_group_api_unlink(param).then(
                    (response) =>{
                        if (response.status === 'success') {
                            this.$ypMsg.notice_success(this,'移除API成功');
                            this.pageRefreshInteface();
                        } else {
                            this.$ypMsg.notice_error(this,'移除API错误',response.message,response.solution);;
                        }
                    });
            },
            // api接口移除执行
            api_group_unlink_handler (param) {
                api.yop_service_group_api_group_unlink(param).then(
                    (response) => {
                        if (response.status === 'success') {
                            this.$ypMsg.notice_success(this,'移除API分组成功');
                            this.pageRefreshInteface();
                        } else {
                            this.$ypMsg.notice_error(this,'移除API分组错误',response.message,response.solution);
                        }
                    });
            },
            // api关联显示函数
            api_link_modal_show (){
                this.$refs.modal_link_api.show_model();
                this.$refs.modal_link_api.reset_Interface();
                this.$refs.modal_link_api.search_Interface();
            },
            // api批量移除函数
            batch_unlink_api (){
               let apiUris = this.api_uris_selected_get();
               if(apiUris.length > 0){
                   this.$Modal.confirm({
                       title: '移除API',
                       content: "<p style='color:red'>如果删除API，则授权该服务分组商户无法调用此API接口</p>" +
                       "<p>确定移除？</p>",
                       'ok-text':'确定',
                       onOk: () =>{
                           let param ={
                               code: this.current_code,
                               apiUris : apiUris
                           }
                           this.api_unlink_handler(param);
                       }
                   });
               }
            },
            // api分组关联显示函数
            apiGroup_link_modal_show (){
                this.$refs.modal_link_api_group.show_model();
                this.$refs.modal_link_api_group.reset_Interface();
                this.$refs.modal_link_api_group.search_Interface();
            },
            // api分组批量移除函数
            batch_unlink_apiGroup (){
                let apiGroupCodes = this.api_groups_selected_get();
                if(apiGroupCodes.length > 0){
                    this.$Modal.confirm({
                        title: '移除API分组',
                        content: "<p style='color:red'>如果移除API分组，则授权该服务分组商户无法调用此API分组下API接口</p>" +
                        "<p>确定移除？</p>",
                        'ok-text':'确定',
                        onOk: () => {
                            let param = {
                                code: this.current_code,
                                apiGroupCodes: apiGroupCodes
                            };
                            this.api_group_unlink_handler(param);
                        }
                    });
                }
            },
            // 表格内容改动时
            handleselection1(value){
                this.multiSelectedData_api =value;
            },
            // 表格内容改动时
            handleselection2(value){
                this.multiSelectedData_apiGroup =value;
            },
            // 已选择api uri获取
            api_uris_selected_get(){
                if (this.multiSelectedData_api == null || this.multiSelectedData_api.length == 0){
                    //还有其他函数判定
                    this.$ypMsg.notice_warning(this,'请至少选择一个API');
                    return [];
                }else {
                    var temp =[];
                    this.multiSelectedData_api.forEach( m => {
                        temp.push(m.uri);
                    });
                    return temp;
                }
            },
            // 已选择api分组获取
            api_groups_selected_get(){
                if (this.multiSelectedData_apiGroup == null || this.multiSelectedData_apiGroup.length == 0){
                    //还有其他函数判定
                    this.$ypMsg.notice_warning(this,'请至少选择一个API分组');
                    return []
                }else {
                    var temp =[];
                    this.multiSelectedData_apiGroup.forEach( m => {
                        temp.push(m.apiGroupCode);
                    });
                    return temp;
                }
            },
            //
        },
        mounted () {
            this.init();
        }
    };
</script>

<style scoped>

</style>
