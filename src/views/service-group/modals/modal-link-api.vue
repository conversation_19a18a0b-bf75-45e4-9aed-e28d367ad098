<template>
    <Modal id="modal_request_1" v-model="model_show" :closable="false" width="75%" >
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">关联API接口</span>
        </p>
        <Card dis-hover :bordered="false">
            <Row type="flex" align="middle">
                <Col span="20">
                <Col span="7" >
                <Col span="7" class="margin-top-10">
                <span >API分组:</span>
                </Col>
                <Col span="17" >
                <Select ref="select_apiM_1" id='select_apiM_1' class="margin-top-5" v-model="data_select_apiGroup" filterable clearable placeholder="请选择（默认全部）">
                    <Option v-for="item in data_apiGroup_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>
                <Col offset="1" span="7" >
                <Col span="7" class="margin-top-10">
                <span >API名称:</span>
                </Col>
                <Col span="17" >
                <Input id='input_apiM_2' class="margin-top-5" clearable v-model="data_interface_name" placeholder="API名称" @on-enter="search_Interface"></Input>
                </Col>
                </Col>
                <Col offset="1" span="7" >
                <Col span="7" class="margin-top-10">
                <span >API URI:</span>
                </Col>
                <Col span="17" >
                <Input id='input_apiM_1' class="margin-top-5" clearable v-model="data_interface_uri" placeholder="API URI" @on-enter="search_Interface"></Input>
                </Col>
                </Col>
                <Col class="margin-top-5" span="7" >
                <Col span="7" class="margin-top-10">
                <span >API类型:</span>
                </Col>
                <Col span="17" >
                <Select id='select_apiM_4' class="margin-top-5" v-model="data_select_apiType"  placeholder="请选择（默认全部）" clearable>
                    <Option v-for="item in data_apiType_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>
                <Col class="margin-top-5" offset="1" span="7" >
                <Col span="7" class="margin-top-10">
                <span >状态:</span>
                </Col>
                <Col span="17" >
                <Select id='select_apiM_2' class="margin-top-5" v-model="data_select_status" placeholder="请选择（默认全部）" clearable>
                    <Option v-for="item in data_status_List" :value="item.value" :key="item.value">{{ item.label }}</Option>
                </Select>
                </Col>
                </Col>
                <Col class="margin-top-5" offset="1" span="7" >

                </Col>
                </Col>
                <Col span="4">
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <!--<Button id='btn_apiM_1' v-url="{url:'/rest/api/list'}" type="primary" @click="search_Interface">查询</Button>-->
                <Button id='btn_apiM_1' type="primary" @click="search_Interface">查询</Button>
                </Col>
                <Col class="margin-top-10"  span="11" style="text-align:center">
                <Button id='btn_apiM_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Table id='table_1' border ref="selection" :columns="columns_ApiInterfaceList" :data="data_ApiInterfaceList" @on-selection-change="handleselection"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="pageRefresh"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_apiList"></loading>
            </Row>
        </Card>
        <div slot="footer">
            <Button type="primary" @click="link_api">确定</Button>
            <Button type="ghost" @click="close_modal">关闭</Button>
        </div>
    </Modal>
</template>

<script>
    import api from '../../../api/api';
    import loading from '../../my-components/loading/loading';
    import util from '../../../libs/util'
    export default {
        name: 'modal-link-api',
        components: {
            loading
        },
        props: {
            code : String, //当前服务分组
        },
        data () {
            return{
                // 窗口显示
                model_show : false,
                /**
                 * 主界面部分数据
                 */
                // 上传header绑定
                header_upload: {
                    Authorization: 'Bearer ' + localStorage.accesstoken
                },
                // api列表加载动画数据绑定
                show_loading_apiList : true,
                // switch点击行数
                switch_index: 0,
                // 注册web api对话框显示
                modal_Show_register: false,
                // APIuri数据绑定
                data_interface_uri : '',
                // API名称
                data_interface_name : '',
                // 状态下拉框数据绑定
                data_select_status: '',
                // 状态下拉框选项数据
                data_status_List: [],
                // api分组下拉框数据绑定
                data_select_apiGroup: '',
                // api分组下拉框数据
                data_apiGroup_List: [],
                // 安全需求下拉框数据绑定
                data_safety_request: '',
                // 安全需求下拉框数据
                data_safetyRequest_List:[],
                // api类型选择数据绑定
                data_select_apiType: '',
                // api类型
                data_apiType_List:[],
                // apiAPI列表列属性
                columns_ApiInterfaceList: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    {
                        title: 'API分组',
                        key: 'apiGroup',
                        width : 110,
                        type:'html',
                        align: 'center'
                    },
                    {
                        title: 'API 接口',
                        'min-width': 160,
                        key: 'interface_Name',
                        render: (h, params) => {
                            return h('div', [
                                h('p', params.row.interface_Name),
                                h('p', '(' + params.row.interface_URI + ')')
                            ]);
                        },
                        align: 'center'
                    },

                    {
                        title: 'API类型',
                        key: 'APItypeDesc',
                        width : 100,
                        align: 'center',
                    },
                    {
                        title: '安全需求',
                        key: 'interface_Tag',
                        type: 'html',
                        width: 167,
                    },
                    {
                        title: '状态',
                        // key: 'statusDesc',
                        align: 'center',
                        width: 95,
                        render: (h, params) => {
                            var color = 'red';
                            if (params.row.statusDesc == '活动中') {
                                color = 'green';
                            }
                            if (params.row.statusDesc == '已下线') {
                                color = 'grey';
                            }
                            return h('div',[
                                h('Tag',{
                                    style:{
                                        align: 'center'
                                    },
                                    props:{
                                        color: color
                                    }
                                },params.row.statusDesc)
                            ]);
                        }
                    },
                    {
                        title: '更新日期/创建日期',
                        key: 'Date',
                        type: 'html',
                        width:150,
                    }
                ],
                // 表格数据
                data_ApiInterfaceList: []
                ,
                // 表格选中数据
                multiSelectedData: [],
                // 分页单页个数
                pageNo : 1,
                // 分页总数
                pageTotal: 0,
                // 当前查询参数
                current_params : {
                    pageNo : 1,
                    pageSize : 10
                }
            }
        },
        methods : {
            // 窗口显示
            show_model(){
                this.model_show = true;
            },
            // 窗口关闭
            close_modal(){
                this.model_show = false;
            },
            // 关联api
            link_api () {
                let apiURIs = this.api_uris_selected_get();
                if(apiURIs.length>0){
                    let param ={
                        code: this.code,
                        apiUris : apiURIs
                    }
                    api.yop_service_group_api_link(param).then(
                        (response) =>{
                            if (response.status === 'success') {
                                let result = response.data.result;
                                if(result && result.length>0){
                                    // this.$Notice.success({
                                    //     title : '部分添加成功',
                                    //     desc : "添加失败总数：<span style='color:red'>"+result.length+"</span>条<br/>" +
                                    //     "失败<span style='color:red'>前5条</span>如下所示<br/>"+this.failed_reason_handler(result),//待修改
                                    //     duration : 15
                                    // });
                                    this.$Modal.success({
                                        title : '部分添加成功',
                                        content : "添加失败总数：<span style='color:red'>"+result.length+"</span>条<br/>" +
                                        "失败<span style='color:red'>前5条</span>如下所示<br/>"+this.failed_reason_handler(result),//待修改
                                        duration : 15
                                    });
                                }else{
                                    this.$ypMsg.notice_success(this,'添加API成功');
                                    // this.model_show = false;
                                    this.$emit('update-list',false)
                                }
                            } else {
                                this.$ypMsg.notice_error(this,'添加API错误',response.data.message,response.data.solution);
                            }
                        });
                }
                // this.model_show = false;
            },
            // 前五条字段处理
            failed_reason_handler(result){
                let resultTemp =''
                if(result.length>=5){
                    for(var i in result){
                        if(i === 5){
                            resultTemp = resultTemp+(parseInt(i)+1)+".<span style='color:red'>"+result[i].apiUri+"</span>: "+ result[i].reason

                        }else if(i < 5){
                            resultTemp = resultTemp+(parseInt(i)+1)+".<span style='color:red'>"+result[i].apiUri+"</span>: "+ result[i].reason+'<br/>'
                        }
                    }
                    return resultTemp;
                } else {

                    for (var i in result) {
                        if (i === result.length - 1) {
                            resultTemp = resultTemp + (parseInt(i)+1)+'.<span style=\'color:red\'>' + result[i].apiUri + '</span>: ' + result[i].reason;
                        } else {
                            resultTemp = resultTemp + (parseInt(i)+1)+'.<span style=\'color:red\'>' + result[i].apiUri + '</span>:' + result[i].reason + '<br/>';
                        }
                    }
                    return resultTemp;
                }

            },
            // 已选择api uri获取
            api_uris_selected_get(){
                if (this.multiSelectedData == null || this.multiSelectedData.length == 0){
                    //还有其他函数判定
                    this.$ypMsg.notice_warning(this,'请至少选择一个API进行关联');
                    return [];
                }else {
                    var temp =[];
                    this.multiSelectedData.forEach( m => {
                        temp.push(m.interface_URI);
                    });
                    return temp;
                }
            },
            init () {
                this.show_loading_apiList = true;
                // 初始化页面表格数据
                api.yop_apiManagement_apiList_page({
                    pageNo : 1,
                    pageSize : 10
                }).then(
                    (response) => {
                        if(response.data.status === 'success'){
                            this.tabledataGet(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                            this.pageTotal = response.data.data.page.totalPageNum * 10;
                            this.show_loading_apiList = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                        }
                    }
                );
                // api状态列表
                api.yop_apiManagement_apiCommonStatusList().then(
                    (response)=>{
                        let resultTemp = response.data.data.statusList
                        this.data_status_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].value,
                                label: resultTemp[i].desc
                            })
                        }
                        this.data_status_List = dataTemp;
                    }
                );
                // 安全需求列表
                api.yop_apiManagement_securityReqList().then(
                    (response)=>{
                        let resultTemp = response.data.data.result
                        this.data_safetyRequest_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].name,
                                label: resultTemp[i].name
                            })
                        }
                        this.data_safetyRequest_List = dataTemp;
                    }
                );
                // api类型列表
                api.yop_apiManagement_apiCommonTypeList().then(
                    (response)=>{
                        let resultTemp = response.data.data.result
                        this.data_apiType_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].value,
                                label: resultTemp[i].desc
                            })
                        }
                        this.data_apiType_List = dataTemp;
                    }
                );
                // api分组列表
                api.yop_dashboard_apis_list().then(
                    (response) => {
                        let resultTemp = response.data.data.result
                        this.data_apiGroup_List = [];
                        let dataTemp = [];
                        for(var i in resultTemp){
                            dataTemp.push({
                                value: resultTemp[i].apiGroupCode,
                                label: resultTemp[i].apiGroupCode+'('+resultTemp[i].apiGroupName+')'
                            })
                        }
                        this.data_apiGroup_List = dataTemp;
                    }
                )
            },
            pageRefresh (val) {
                this.show_loading_apiList = true ;
                this.current_params.pageNo = val;
                api.yop_apiManagement_apiList_page(this.current_params).then(
                    (response) => {
                        this.tabledataGet(response.data.data.page.items);
                        this.pageNo = response.data.data.page.pageNo;
                        this.pageTotal = response.data.data.page.totalPageNum * 10;
                        this.show_loading_apiList = false ;
                    }
                )
            },
            // 表格赋值
            tabledataGet (items) {
                this.data_ApiInterfaceList = [];
                let dataTemp = [];
                for (var i in items){
                    let DateTemp = items[i].lastModifiedDate + '<br/>' + items[i].createdDate
                    let securityTemp = []
                    if(items[i].apiSecurities){
                        securityTemp = this.SecurityGenerate('自定义',items[i].apiSecurities);
                    }else{
                        securityTemp = this.SecurityGenerate('继承',items[i].apiGroupSecurities);
                    }
                    let operationsTemp = false;
                    if(items[i].status === 'ACTIVE'){
                        operationsTemp = true;
                    }
                    if(items[i].status === 'FORBID'){
                        operationsTemp = false
                    }
                    dataTemp.push({
                        APIid : items[i].apiId,
                        interface_Name: items[i].apiTitle,
                        interface_URI: items[i].apiUri,
                        APItype: items[i].apiType,
                        APItypeDesc: items[i].apiTypeDesc,
                        APIgroupTitle: items[i].apiGroupTitle,
                        interface_Tag: securityTemp,
                        Date: DateTemp,
                        apiGroup:items[i].apiGroupTitle+'<br/>('+items[i].apiGroupCode+')',
                        APIgroupCode:items[i].apiGroupCode,
                        status: items[i].status,
                        statusDesc: items[i].statusDesc,
                        apiSecurity: items[i].apiSecurity,
                        groupSecurity: items[i].groupSecurity,
                        createTime: items[i].createdDate,
                        lastModifyTime: items[i].lastModifiedDate,
                        operations : operationsTemp
                    });
                    if(items[i].status === 'OFFLINE'){
                        delete dataTemp[i]['operations'];
                    }
                }
                this.data_ApiInterfaceList =dataTemp;
            },
            // 安全需求数组处理
            SecurityGenerate (title,array) {
                let securityTemp = title +'<br/>'
                for(var i in array){
                    if(i === array.length){
                        securityTemp = securityTemp +array[i]
                    }else{
                        securityTemp = securityTemp +array[i]+'<br/>'
                    }
                }
                return securityTemp;
            },
            // 空处理函数
            ParamHandle (Param){
                if(Param || (Param === 0)){
                    return Param;
                }else{
                    return '';
                }
            },
            // 表格内容改动时
            handleselection(value){
                this.multiSelectedData =value;
            },
            // 查询apiAPI函数
            search_Interface () {
                let paramsTemp = {
                    apiTitle: this.data_interface_name,
                    apiUri: this.data_interface_uri,
                    apiType: this.data_select_apiType,
                    apiGroupCode : this.data_select_apiGroup,
                    status: this.data_select_status,
                    pageNo : 1,
                    pageSize: 10
                }
                if(this.data_interface_name === ''){
                    paramsTemp['apiTitle'];
                }
                if(this.data_interface_uri === ''){
                    delete paramsTemp['apiUri'];
                }
                if(this.data_select_apiType === ''){
                    delete paramsTemp['apiType'];
                }
                if(this.data_select_apiGroup === ''){
                    delete paramsTemp['apiGroupCode'];
                }
                if(this.data_select_status === ''){
                    delete paramsTemp['status'];
                }
                if(this.data_safety_request === ''){
                    delete paramsTemp['securityReq'];
                }
                this.current_params = paramsTemp;
                api.yop_apiManagement_apiList_page(paramsTemp).then(
                    (response) => {
                        this.tabledataGet(response.data.data.page.items);
                        this.pageNo = response.data.data.page.pageNo;
                        this.pageTotal = response.data.data.page.totalPageNum * 10;
                    }
                );
            },
            // 重置函数
            reset_Interface () {
                this.$refs.select_apiM_1.clearSingleSelect();
                this.data_interface_name = '';
                this.data_interface_uri = '';
                this.data_select_apiType = '';
                this.data_select_apiGroup = '';
                this.data_select_status = '';
                this.data_safety_request = '';
                this.current_status ={
                    pageNo : 1,
                    pageSize: 10
                };
            },
        },
        mounted () {
            // 挂载时调用页面初始化函数
            this.init();
            localStorage.removeItem('apiInfo');
        },

    };
</script>

<style scoped>

</style>
