<template>
    <Modal id="modal_request_1" v-model="model_show" :closable="false" width="75%" >
        <p slot="header" style="color:#2d8cf0;">
            <span style="color:black">添加API分组</span>
        </p>
        <Card :bordered="false">
            <Row type="flex" align="middle">
                <Col span="24">
                <Col span="6" >
                <Col  span="8">
                <Col class="margin-top-10" >API分组：&nbsp;</Col>
                </Col>
                <Col span="14">
                <!--<apigroup @on-update="updateSelect_apiGroup"> </apigroup>-->
                <common-select id="select_ag_1" ref="select_ag" @on-update="updateSelect_apiGroup"
                               type="combo"
                               keyWord="result"
                               code="apiGroupCode"
                               title="apiGroupName"
                               group="apiGroup"
                               holder="请选择（默认全部）"
                               :default="this.data_select_apiGroup"
                               :uri="this.$store.state.select.apiGroup.uri"></common-select>
                </Col>
                </Col>
                <Col span="6" >
                <Col  span="8">
                <Col class="margin-top-10" >服务提供方：&nbsp;</Col>
                </Col>
                <Col span="14">
                <common-select id="select_ag_2" ref="select_sp" @on-update="updateSelect_serviceSupplier"
                               type="combo"
                               keyWord="result"
                               code="spCode"
                               title="spName"
                               group="serviceSupplier"
                               :default="this.data_select_serviceSupplier"
                               @on-loaded="select_callBack"
                               holder="请选择（默认全部）"
                               :uri="this.$store.state.select.serviceSupplier.uri"></common-select>
                </Col>
                </Col>
                <Col span="8" >
                <Col  span="6">
                <Col class="margin-top-10">创建时间：&nbsp;</Col>
                </Col>
                <date-picker ref="datepicker" @on-start-change="update_start_date"
                             @on-end-change="update_end_date"
                             :default_start="this.dateStart"
                             :default_end="this.dateEnd"
                ></date-picker>
                <!--<Col span="8">-->
                <!--<DatePicker  class="margin-top-5" :options="optionsStart" :clearable="false" v-model="requestDateStart" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择起始日期" ></DatePicker>-->
                <!--</Col>-->
                <!--<Col class="margin-top-10" span="1">—</Col>-->
                <!--<Col span="8">-->
                <!--<DatePicker  class="margin-top-5" :options="optionsStart" :clearable="false" v-model="requestDateEnd" format="yyyy/MM/dd" type="date" placement="bottom-end" placeholder="选择结束日期" ></DatePicker>-->
                <!--</Col>-->
                </Col>
                <Col class="margin-top-5" span="4" style="text-align: center">
                <!--<Button class="margin-right-20" id='btn_ag_1' v-url="{url:'/rest/api-group/list'}" type="primary" @click="search_Interface()">查询</Button>-->
                <Button class="margin-right-20" id='btn_ag_1'  type="primary" @click="search_Interface()">查询</Button>
                <Button  id='btn_ag_2' type="ghost" @click="reset_Interface">重置</Button>
                </Col>
                </Col>
            </Row>
            <Row class="margin-top-20">
                <Col span="24">
                <Table id='table_ag_1' border ref="selection" :columns="columns_ApiGroupList" :data="data_ApiGroupList" @on-selection-change="handleselection"></Table>
                <Tooltip content="输入页码后点击回车键跳转页数" style="float: right" placement="bottom-end">
                    <Page class="margin-top-10" style="float: right" :total="pageTotal" :page-size="10" :current="pageNo" show-elevator @on-change="search_Interface"></Page>
                </Tooltip>
                </Col>
                <loading :show="show_loading_apiGroup"></loading>
            </Row>
        </Card>
        <div slot="footer">
            <Button type="primary" @click="link_api_group">确定</Button>
            <Button type="ghost" @click="close_modal">关闭</Button>
        </div>
    </Modal>
</template>

<script>
    import api from '../../../api/api';
    import datePicker from '../../common-components/date-components/date-picker'
    import loading from '../../my-components/loading/loading';
    import commonSelect from '../../common-components/select-components/selectCommon'
    import util from '../../../libs/util'
    export default {
        name: 'modal-link-api-group',
        components: {
            loading,
            datePicker,
            commonSelect
        },
        props: {
            code : String, //当前服务分组
        },
        data () {
            return{
                // 窗口显示
                model_show : false,
                // 与表相关的下拉框数目
                count_select_related: 1,
                // api分组选择数据绑定
                data_select_apiGroup : '',
                // 服务提供方选择数据绑定
                data_select_serviceSupplier : '',
                // 表格选中数据
                multiSelectedData: [],
                // 分页总数
                pageTotal: 0,
                // 当前页数
                pageNo : 1,
                // 表格头数据绑定
                columns_ApiGroupList: [
                    {
                        type: 'selection',
                        width: 60,
                        align: 'center'
                    },
                    {
                        title: 'API分组',
                        width: 160,
                        render:(h,params) =>{
                            return h('div',[
                                h('p',params.row.apiGroupName),
                                h('p','('+params.row.apiGroupCode+')')
                            ])
                        },
                        align: 'center'
                    },
                    {
                        renderHeader: (h,params) =>{
                            return h ('div',[
                                h('p', '服务提供方'),
                                h('p', '服务提供方编码')
                            ])
                        },
                        width : 130,
                        align: 'center',
                        render:(h,params) =>{
                            return h('div',[
                                h('p',params.row.serviceSupName),
                                h('p','('+params.row.serviceSupCode+')')
                            ])
                        },

                    },
                    {
                        title: 'API分组级安全需求',
                        key: 'apiGroup_security',
                        width : 170,
                        type: 'html'
                    },
                    {
                        title: '描述',
                        key: 'Desc',
                        'min-width': 120,
                        type: 'html'

                    },
                    {
                        renderHeader: (h,params) =>{
                            return h ('div',[
                                h('p', '创建时间'),
                                h('p', '最后修改时间')
                            ])
                        },
                        key: 'Date',
                        width:160,
                        align: 'center',
                        render: (h,params) => {
                            return h('div',[h('p',params.row.createTime),
                                h('p',params.row.lastModifyTime)])
                        }
                    }
                ],
                // 表格数据绑定
                data_ApiGroupList:[
                    // {
                    //     apiGroupCode: 'apiGroupCode',
                    //     apiGroupName: 'API分组名称',
                    //     serviceSupName: '服务提供方',
                    //     serviceSupCode: 'spCode',
                    //     apiGroup_security: '128<br/>256<br/>512',
                    //     Desc:'sertrstyrtydrtdrtfcggchcbvgh',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-03 18:40:09'
                    // },
                    // {
                    //     apiGroupCode: 'apiGroupCode',
                    //     apiGroupName: 'apiGroupName',
                    //     serviceSupName: '服务提供方',
                    //     serviceSupCode: 'spCode',
                    //     apiGroup_security: '128<br/>256<br/>512',
                    //     Desc:'sertrstyrtydrtdrtfcggchcbvgh',
                    //     createTime : '2018-02-01 10:30:00',
                    //     lastModifyTime : '2018-05-03 18:40:09'
                    // }
                ],
                // 加载动画数据绑定
                show_loading_apiGroup: false,
                // 开始日期绑定
                dateStart: '',
                //结束日期绑定
                dateEnd: ''
            }
        },
        methods : {
            show_model(){
                this.model_show = true;
            },
            close_modal(){
                this.model_show = false;
            },
            // 关联api
            link_api_group () {
                let apiGroupCodes = this.api_groups_selected_get();
                if(apiGroupCodes.length > 0){
                    let param ={
                        code: this.code,
                        apiGroupCodes : apiGroupCodes
                    }
                    api.yop_service_group_api_group_link(param).then(
                        (response) =>{
                            if (response.status === 'success') {
                                let result = response.data.result;
                                if(result && result.length>0){
                                    this.$Modal.success({
                                        title : '部分添加成功',
                                        content : "添加失败总数：<span style='color:red'>"+result.length+"</span>条<br/>" +
                                        "失败<span style='color:red'>前5条</span>如下所示<br/>"+this.failed_reason_handler(result),//待修改
                                        duration : 15
                                    });
                                }else{
                                    this.$ypMsg.notice_success(this,'添加API分组成功');
                                    // this.model_show = false;
                                    this.$emit('update-list',false);
                                }
                            } else {
                                this.$ypMsg.notice_error(this,'添加API分组错误',response.data.message,response.data.solution);
                            }
                        }
                    );
                }
                // console.log(apiGroupCodes);
                // this.model_show = false;
            },
            // 前五条字段处理
            failed_reason_handler(result){
                let resultTemp =''
                if(result.length>=5){
                    for(var i in result){
                        if(i === 5){
                            resultTemp = resultTemp+(parseInt(i)+1)+".<span style='color:red'>"+result[i].code+"</span>: "+ result[i].reason
                        }else if(i < 5){
                            resultTemp = resultTemp+(parseInt(i)+1)+".<span style='color:red'>"+result[i].code+"</span>: "+ result[i].reason+'<br/>'
                        }
                    }
                    return resultTemp;
                } else {

                    for (var i in result) {
                        if (i === result.length - 1) {
                            resultTemp = resultTemp + (parseInt(i)+1)+'.<span style=\'color:red\'>' + result[i].code + '</span>: ' + result[i].reason;
                        } else {
                            resultTemp = resultTemp + (parseInt(i)+1)+'.<span style=\'color:red\'>' + result[i].code + '</span>:' + result[i].reason + '<br/>';
                        }
                    }
                    return resultTemp;
                }

            },
            // 已选择api uri获取
            api_groups_selected_get(){
                if (this.multiSelectedData == null || this.multiSelectedData.length == 0){
                    //还有其他函数判定
                    this.$ypMsg.notice_warning(this,'请至少选择一个API分组进行添加');
                    return [];
                }else {
                    var temp =[];
                    this.multiSelectedData.forEach( m => {
                        temp.push(m.apiGroupCode);
                    });
                    return temp;
                }
            },
            // 页面查询按钮
            search_Interface (val){
                this.show_loading_apiGroup =true;
                let params={
                    apiGroupCode:this.data_select_apiGroup,
                    spCode: this.data_select_serviceSupplier,
                    createdStartDate: util.dateFormat_component(this.dateStart),
                    createdEndDate: util.dateFormat_component_end(this.dateEnd),
                    _pageNo:1,
                    _pageSize:10
                }
                if (val) {
                    params._pageNo = val;
                }
                util.paramFormat(params)
                api.yop_apiGroup_list(params).then(
                    (response) =>{
                        let status = response.data.status
                        if(response.data.status === 'success'){
                            this.tableDataFormat(response.data.data.page.items);
                            this.pageNo = response.data.data.page.pageNo;
                            this.pageTotal = response.data.data.page.totalPageNum * 10;
                            this.show_loading_apiGroup = false;
                        }else{
                            this.$Modal.error({
                                title: '错误',
                                content: response.data.message
                            });
                            this.show_loading_apiGroup = false;
                        }
                    }
                )
            },
            // 页面列表数据处理函数
            tableDataFormat (data) {
                this.data_ApiGroupList =[];
                if(data && data.length >0){
                    data.forEach(
                        (item)=>{
                            this.data_ApiGroupList.push({
                                id: item.id,
                                apiGroupCode: item.apiGroupCode,
                                apiGroupName: item.apiGroupName,
                                serviceSupName: this.data_spCode_handler(item.spCode),
                                serviceSupCode: item.spCode,
                                apiGroup_security: this.data_security_handler(item.securities),
                                Desc:item.description,
                                createTime : item.createdDate,
                                lastModifyTime : item.lastModifiedDate
                            })
                        }
                    )
                }
            },
            // spcode处理函数
            data_spCode_handler (spcode){
                let group = this.$store.state.select['serviceSupplier'].data
                for(var i in group){
                    if(group[i].value === spcode){
                        return  group[i].name
                    }
                }
            },
            // 安全需求处理函数
            data_security_handler(securities){
                let data_security = ''
                if(securities && securities.length > 0){
                    for(var i in securities){
                        if(i === securities.length){
                            data_security = data_security +securities[i]
                        }else{
                            data_security = data_security +securities[i]+'<br/>'
                        }
                    }
                    return data_security;
                }else{
                    return '';
                }
            },
            // 页面重置按钮
            reset_Interface () {
                this.$refs.select_ag.resetSelected();
                // this.data_select_apiGroup = '';

                this.$refs.datepicker.reset();

                this.$refs.select_sp.resetSelected();
                // this.data_select_serviceSupplier = '';
                this.dateStart = '';
                this.dateEnd = '';
            },
            // 开始日期更新
            update_start_date (val) {
                this.dateStart = val;
            },
            // 结束日期更新
            update_end_date (val) {
                this.dateEnd = val;
            },
            // 下拉框加载完处理函数
            select_callBack(){
                this.count_select_related--;
                if(this.count_select_related === 0){
                    this.search_Interface();
                }
            },
            // 同步更新选择api分组函数
            updateSelect_apiGroup (val) {
                this.data_select_apiGroup = val;
            },
            // 同步更新选择api分组函数
            updateSelect_serviceSupplier (val) {
                this.data_select_serviceSupplier = val;
                this.formCustom.supplier =val;
            },
            // 表格内容改动时
            handleselection(value){
                this.multiSelectedData =value;
            },
        }

    };
</script>

<style scoped>

</style>

