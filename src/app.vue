<template>
    <div id="main" class="app-main">
        <template v-if="!loadMenu">
          <div v-if="checkCauseFlag" class="mask"></div>
            <router-view></router-view>
            <Modal v-model="modal_show" width="500">
                <p slot="header" style="color:#2d8cf0;text-align:center;font-size: 18px;">
                    <Icon type="ios-information-circle"></Icon>
                    <span>跳转提示</span>
                </p>
                <div style="text-align:center;font-size: 18px;">
                    <p>当前版本不是最新版本, <span style="color:#2d8cf0;">{{time}}</span>s 后为您跳转至最新版本</p>
                </div>
                <div slot="footer">
                    <Button type="primary"  @click="jump(current_version)">立即跳转</Button>
                </div>
            </Modal>
            <div :mask-closable="true" class="check-con" v-if="checkCauseFlag">
                <h3>审核原因</h3>
                <!-- <p>提交审核成功</p> -->
                <textarea class='check-cause' v-model="checkCause" cols="71" rows="2" placeholder="请输入原因"></textarea>
                <div class="footer">
                    <Button type='text' @click="cancelCheck">取消</Button>
                    <Button type="primary"  @click="goCheckCause">确定</Button>
                </div>
            </div>
        </template>
        <Loading v-else />
    </div>
</template>

<script>
    import api from './api/api';
    import Main from './views/Main'
    import Loading from './views/error-page/loading.vue'
    import { routers } from './router/router'
    import initRouter from './router/initRouter'
    import util from "~/libs/util"
    export default {
        components : {
          Main,
          Loading
        },
        data () {
            return {
                theme: this.$store.state.app.themeColor,
                isRouterAlive : true,
                time: 5,
                modal_show : false,
                checkCauseFlag: false, //审核原因
                current_version : '',
                currentType: '',  // boss3g、yuia 登录类型
                checkCause: ''
            };
        },
         computed:{
            getIfShowCheckFlag(){
              return this.$store.state.ifShowCheckFlag;
            },
            loadMenu() {
              return this.$store.state.loadMenu
            }
        },
         watch:{
            getIfShowCheckFlag(val){
                this.checkCauseFlag = val
            }
        },
        provide () {
            return {
                reload : this.reload
            }
        },
        beforeDestroy () {

        },
        async mounted() {
          let response = {}
          try {
            response = await api.yop_getConfigUrl()
          } catch (error) {
            console.error(error)
          }
          this.currentType = response.data.data.config.loginType
          this.resetConfigData = response.data

          localStorage.linkLogCenter=response.data.data.config.linkLogCenter;
          localStorage.linkCallChain=response.data.data.config.linkCallChain;
          localStorage.logoutUrl = response.data.data.config.logoutUrl;

          if(response.data.data.config.rt){
            localStorage.test_template_tips = response.data.data.config.rt['tips.app.create'];
            localStorage.mail_operation = response.data.data.config.rt['email.app.create'];
          }
          this.token_has_right()
        },
        created () {
          window.localStorage.setItem('remoteIP', process.env.baseURl)
          // location.href = location.href.replace('?belongSystem=-99910070&_menuId=2208&_firstMenuId=481','').replace('#/&yuiassotoken','#/?yuiassotoken')
        },
    methods: {
        // 菜单是否禁用
        enable_check (status) {
            if(status === 'ENABLE'){
                return true;
            }else{
                return false;
            }
        },
        jump(version){
            location.href = this.current_url_generate(version);
        },
        goCheckCause(){
            if(!!!this.checkCause){
                this.$Message.error('请输入审核原因');
                return false;
            }
            // this.$Message.info('输入原因后请再次提交');
            this.$store.state.ifShowCheckFlag = false
            localStorage.setItem('checkCause',this.checkCause) 
            let params = this.$store.state.commonAuditParam;
            api.yop_common_audit(params).then(
                (response) =>{
                    this.checkCause = ''
                    if(response.status === 'success'){
                        this.$ypMsg.notice_success(this,response.message);
                    }else{
                        this.$ypMsg.notice_error(this,'错误',response.message,response.solution);
                    }
                }
            )
            
            
        },
        cancelCheck(){
            this.checkCause = ''
            this.$store.state.ifShowCheckFlag = false
            localStorage.setItem('checkCause','') 
        },
        current_url_generate (version) {
            let arr = window.location.href.split('/');
            let url = '';
            for(var i = 0;i< arr.length;i++){
                if(arr[i] === 'static' && arr[i+2]==='index.html#'){
                    arr[i+1]= version
                    arr.forEach(item =>{
                        url = url+item+'/'
                    });
                    url=url.substring(0,url.length-1)
                    return url;
                }
            }
        },
        // 判断当前版本号是非为最新
        version_check (version) {
            let arr = window.location.href.split('/');
            for(var i = 0;i< arr.length;i++){
                if(arr[i] === 'static' && arr[i+2]==='index.html#'){
                    let current_version = arr[i+1];
                    if(current_version === version){
                        return true;
                    }else{
                        return false;
                    }
                }
            }
            return true;
        },
        // token及菜单按钮权限获取
        token_has_right () {
          this.$nextTick(() => {
            if (!window.localStorage.token) {
              // 权限中心流程
              if(this.currentType === 'yuia') {
                // if(location.href.indexOf("yuiassotoken=") != -1){
                if(localStorage.yuiassotoken){
                    // var token = util.getQueryVariable('yuiassotoken');
                    var token = util.getQueryVariable('yuiassotoken');
                    window.localStorage.token = localStorage.yuiassotoken;
                    if(!localStorage.accesstoken){
                        api.yop_dashboard_actoken('yuiassotoken').then(
                            (res)=>{
                                if(res.data.status === 'success'){
                                    localStorage.accesstoken=res.data.data.accessToken;
                                    localStorage.userType=res.data.data.userType;
                                    localStorage.userId=res.data.data.userId;
                                    localStorage.userName=res.data.data.userName;
                                    this.routeGet();
                                }else{
                                    localStorage.removeItem('token')
                                    alert(res.data.message+' 请重新登录');
                                    api.yop_getConfigUrl().then(
                                        (response) =>{
                                            localStorage.linkLogCenter=response.data.data.config.linkLogCenter;
                                            localStorage.linkCallChain=response.data.data.config.linkCallChain;
                                            localStorage.logoutUrl = response.data.data.config.logoutUrl;
                                            if(response.data.data.config.rt){
                                                localStorage.test_template_tips = response.data.data.config.rt['tips.app.create'];
                                                localStorage.mail_operation = response.data.data.config.rt['email.app.create'];
                                            }
                                            let uriTmp = response.data.data.config.logoutUrl;
                                            // location.href = uriTmp +'?returnUrl='+encodeURIComponent(location.href.substring(0,location.href.indexOf("?callback="))+'?callback='+location.href.substring(0,location.href.indexOf("?callback=")));
                                            location.href = uriTmp +'?returnUrl='+location.href.substring(0,location.href.indexOf("?callback="))+'?callback='+location.href.substring(0,location.href.indexOf("?callback="));
                                        }
                                    );
                                }
                            }
                        ),err => function(err){
                          console.log(err)
                        }
                    } else {
                      this.routeGet();
                    }
                } else {
                  this.jumpPage()
                }
              } else {
                // boss3g流程
                if(location.href.indexOf("&yeepay_sso_token=") != -1){
                    var token=location.href.substring(location.href.indexOf("&yeepay_sso_token=")+18,location.href.length);
                    window.localStorage.token=token;
                    if(!localStorage.accesstoken){
                      api.yop_dashboard_actoken().then(
                        (res)=>{
                                if(res.data.status === 'success'){
                                    localStorage.accesstoken=res.data.data.accessToken;
                                    localStorage.userType=res.data.data.userType;
                                    localStorage.userId=res.data.data.userId;
                                    localStorage.userName=res.data.data.userName;
                                    this.routeGet();
                                }else{
                                    localStorage.removeItem('token')
                                    alert(res.data.message+' 请重新登录');
                                    api.yop_getConfigUrl().then(
                                        (response) =>{
                                            localStorage.linkLogCenter=response.data.data.config.linkLogCenter;
                                            localStorage.linkCallChain=response.data.data.config.linkCallChain;
                                            localStorage.logoutUrl = response.data.data.config.logoutUrl;
                                            if(response.data.data.config.rt){
                                                localStorage.test_template_tips = response.data.data.config.rt['tips.app.create'];
                                                localStorage.mail_operation = response.data.data.config.rt['email.app.create'];
                                            }
                                            let uriTmp = response.data.data.config.logoutUrl;
                                            // location.href = uriTmp +'?returnUrl='+encodeURIComponent(location.href.substring(0,location.href.indexOf("?callback="))+'?callback='+location.href.substring(0,location.href.indexOf("?callback=")));
                                            location.href = uriTmp +'?returnUrl='+location.href.substring(0,location.href.indexOf("?callback="))+'?callback='+location.href.substring(0,location.href.indexOf("?callback="));
                                        }
                                    );
                                }
                            }
                        ),err => function(err){
                            console.log(err)
                        }

                    }else {
                      this.routeGet();
                    }
                }else{
                  this.jumpPage()
                }
              }
            } else {
                if(!localStorage.accesstoken){
                    api.yop_dashboard_actoken().then(
                        (res)=>{
                            if(res.data.status === 'success'){
                                localStorage.accesstoken=res.data.data.accessToken;
                                localStorage.userType=res.data.data.userType;
                                localStorage.userId=res.data.data.userId;
                                localStorage.userName=res.data.data.userName;
                                localStorage.logoutUrl = response.data.data.config.logoutUrl;
                            if(response.data.data.config.rt){
                                localStorage.test_template_tips = response.data.data.config.rt['tips.app.create'];
                                localStorage.mail_operation = response.data.data.config.rt['email.app.create'];
                            }
                                this.routeGet();
                            }else{
                                // localStorage.removeItem('token')
                                localStorage.clear();
                                alert(res.data.message+'请重新登录');
                                api.yop_getConfigUrl().then(
                                    (response) =>{
                                        localStorage.linkLogCenter=response.data.data.config.linkLogCenter;
                                        localStorage.linkCallChain=response.data.data.config.linkCallChain;
                                        localStorage.logoutUrl = response.data.data.config.logoutUrl;
                                        if(response.data.data.config.rt){
                                            localStorage.test_template_tips = response.data.data.config.rt['tips.app.create'];
                                            localStorage.mail_operation = response.data.data.config.rt['email.app.create'];
                                        }
                                        let uriTmp = response.data.data.config.logoutUrl;
                                        // location.href = uriTmp +'?returnUrl='+encodeURIComponent(location.href.substring(0,location.href.indexOf("&yeepay_sso_token="))+'?callback='+location.href.substring(0,location.href.indexOf("&yeepay_sso_token=")));
                                        location.href = uriTmp +'?returnUrl='+location.href.substring(0,location.href.indexOf("&yeepay_sso_token="))+'?callback='+location.href.substring(0,location.href.indexOf("&yeepay_sso_token="));
                                    }
                                );
                            }
                        }
                    ),err => function(err){
                        console.log(err)
                    }
                }else {
                  this.routeGet();
                }
            }
          })
        },
        menu_handler (data) {
            let children = [];
            if(data){
                data.forEach(
                    (item)=>{
                        if(item.icon){
                            if(this.enable_check(item.status)){
                                children.push({
                                    path : item.url,
                                    icon : item.icon,
                                    id : item.id,
                                    name: item.url,
                                    meta: {
                                        title:item.name
                                    },
                                    title : item.name,
                                    status : item.status,
                                    perm : this.enable_check(item.status),
                                    component : () => import('~/views'+item.url+'.vue')
                                })
                            }else{
                                children.push({
                                    path : item.url,
                                    icon : item.icon,
                                    name: item.url,
                                    meta: {
                                        title:item.name
                                    },
                                    title : item.name,
                                    status : item.status,
                                    perm : this.enable_check(item.status),
                                    component : () => import('~/views/error-page/403.vue')
                                })
                            }
                        }else{
                            if(this.enable_check(item.status)){
                                children.push({
                                    path : item.url,
                                    id : item.id,
                                    icon : 'ios-list',
                                    name: item.url,
                                    meta: {
                                        title:item.name
                                    },
                                    title : item.name,
                                    status : item.status,
                                    perm : this.enable_check(item.status),
                                    component : () => import('~/views'+item.url+'.vue')
                                })
                            }else{
                                children.push({
                                    path : item.url,
                                    id : item.id,
                                    icon : 'ios-list',
                                    name: item.url,
                                    meta: {
                                        title:item.name
                                    },
                                    title : item.name,
                                    status : item.status,
                                    perm : this.enable_check(item.status),
                                    component : () => import('~/views/error-page/403.vue')
                                })
                            }
                        }
                    }
                )
                return children
            }else{
                return children
            }
        },
        routeGet () {
            api.yop_auth_get_menus().then(
                (response) =>{
                    if (response.data.status === 'success') {
                        let routeL =[{
                            path: '/',
                            name: 'otherRouter',
                            redirect: '/home',
                            meta: {
                                title :'首页 - 易宝运营后台'
                            },
                            component: Main,
                            children: initRouter
                        },
                        ];
                        let routeX = [{
                            path: '/',
                            name: 'otherRouter',
                            redirect: '/home',
                            meta: {
                                title:'首页 - 易宝运营后台'
                            },
                            component: Main,
                            children: initRouter
                        },
                          ...routers
                        ];
                        let menuL = [];
                        let result = response.data.data.result;
                        if(result){
                            result.forEach(
                                (item)=>{
                                    if(item.icon){
                                        menuL.push({
                                            path : '',
                                            icon: item.icon,
                                            title: item.name,
                                            id : item.id,
                                            meta: {
                                                title:item.name
                                            },
                                            name: item.name,
                                            component : Main,
                                            status : item.status,
                                            perm : this.enable_check(item.status),
                                            children: this.menu_handler(item.children)
                                        })
                                        routeL.push({
                                            path : '',
                                            icon: item.icon,
                                            title: item.name,
                                            meta: {
                                                title:item.name
                                            },
                                            name: item.name,
                                            component : Main,
                                            status : item.status,
                                            perm : this.enable_check(item.status),
                                            children: this.menu_handler(item.children)
                                        })
                                        routeX.push({
                                            path : '',
                                            icon: item.icon,
                                            title: item.name,
                                            name: item.name,
                                            meta: {
                                                title:item.name
                                            },
                                            component : Main,
                                            status : item.status,
                                            perm : this.enable_check(item.status),
                                            children: this.menu_handler(item.children)
                                        })
                                    }else{
                                        menuL.push({
                                            path : '',
                                            icon: 'ios-paperplane',
                                            title: item.name,
                                            name: item.name,
                                            id : item.id,
                                            meta: {
                                                title:item.name
                                            },
                                            component : Main,
                                            status : item.status,
                                            perm : this.enable_check(item.status),
                                            children: this.menu_handler(item.children)
                                        })
                                        routeL.push({
                                            path : '',
                                            icon: 'ios-paperplane',
                                            title: item.name,
                                            name: item.name,
                                            component : Main,
                                            meta: {
                                                title:item.name
                                            },
                                            status : item.status,
                                            perm : this.enable_check(item.status),
                                            children: this.menu_handler(item.children)
                                        })
                                        routeX.push({
                                            path : '',
                                            icon: 'ios-paperplane',
                                            title: item.name,
                                            meta: {
                                                title:item.name
                                            },
                                            name: item.name,
                                            component : Main,
                                            status : item.status,
                                            perm : this.enable_check(item.status),
                                            children: this.menu_handler(item.children)
                                        })
                                    }
                                }
                            )
                        }
                        this.$store.commit('menuGenerate',menuL);
                        this.$router.addRoutes(routeX);
                        this.$store.commit('routerAdd',routeL);
                    } else {
                        this.$ypMsg.notice_error(this,'权限获取失败错误',response.data.message,response.data.solution);
                    }
                }

            ).finally(() => {
              this.$store.commit('setLoadMenu', false)
            })
        },
        // 跳转登录页面
        jumpPage() {
          let uriTmp = this.resetConfigData.data.config.loginUrl;
          
          if(this.currentType === 'yuia'){
            // location.href = `${uriTmp}?callback=${location.href}?time=${Date.now()}`
            let urlStr = location.href.split('#')
            if(urlStr[1].indexOf('?') != -1) {
              location.href = `${uriTmp}?callback=${location.href}&time=${Date.now()}`
            } else {
              location.href = `${uriTmp}?callback=${location.href}?time=${Date.now()}`
            }
          } else {
            if(location.href.indexOf("?callback=") != -1){
              location.href = `${uriTmp}?returnUrl=${util.getQueryVariable('callback')}`
            } else {
              location.href = `${uriTmp}?returnUrl=${location.href}?callback=${location.href}`
            }
          }
        },
        reload () {
            this.isRouterAlive = false;
            this.$nextTick(function () {
                this.isRouterAlive = true;
            })
        }
    }
    };
</script>

<style>
html,body{
    width: 100%;
    height: 100%;
    background: #f0f0f0;
    overflow: hidden;
}
.app-main{
    width: 100%;
    height: 100%;
}
.app-main .mask{
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    background-color: rgba(0, 0, 0, 0.8);
}
.check-cause{
    border-radius: 4px;
}
.check-con{
    width: 500px;
    height: 166px;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1002;
    background: #fff;
    padding: 10px 20px;
    margin: auto;
    border-radius: 6px;
}
.check-con h3{
    margin: 10px 0;
}
.footer{
    margin-top: 18px;
    text-align: right;
}
</style>
