// eslint-disable-next-line camelcase
import { fetch_post, fetch_get_params } from '../api';

export default {
  contactApi (params) {
    return fetch_post('/rest/spi/api/contact', params);
  },
  uncontactApi (params) {
    return fetch_post('/rest/spi/api/uncontact', params);
  },
  getList (params) {
    return fetch_get_params('/rest/spi/api/list', params);
  },
  getSubscribeList (params) {
    return fetch_get_params('/rest/spi/subscribe/list', params);
  },
  subscribe (params) {
    return fetch_post('/rest/spi/subscribe', params);
  },
  unsubscribe (params) {
    return fetch_post('/rest/spi/unsubscribe', params);
  },
  getAppList (params) {
    return fetch_get_params('/rest/app/list/for-subscribe', params);
  },
  checkUrl (params) {
    return fetch_get_params('/rest/spi/subscribe/check-url', params);
  }
};
