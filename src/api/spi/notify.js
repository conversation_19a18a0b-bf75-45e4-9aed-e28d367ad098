// eslint-disable-next-line camelcase
import { fetch_post, fetch_get_params } from '../api';

export default {
  // 查询订单列表
  getList (params) {
    return fetch_get_params('/rest/notify/order/list', params)
  },
  // 重发通知
  resend (params) {
    return fetch_post('/rest/notify/order/resend', params)
  },
  // 批量重发通知
  resendAll (params) {
    return fetch_post('/rest/notify/order/query-resend', params)
  },
  // 处理记录
  getRecordList (params) {
    return fetch_get_params('/rest/notify/order/record/list', params)
  },
  // 同步状态
  syncStatus (params) {
    return fetch_post('/rest/notify/order/sync', params)
  },
  getNotifyContent (params) {
    return fetch_get_params('/rest/notify/order/latest-content', params)
  },
  // 发送记录
  getSendRecordList (params) {
    return fetch_get_params('/rest/notify/order/send-record/list', params)
  },
  // 获取错误码
  getErrCodeList (params) {
    return fetch_get_params('/rest/notify/order/error-code/list', params)
  },
  // 获取失败原因 解决方案
  getErrorCodeSolution (params) {
    return fetch_get_params('/rest/notify/error-code/solution', params);
  }
}
