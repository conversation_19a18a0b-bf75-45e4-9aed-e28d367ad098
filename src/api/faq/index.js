// eslint-disable-next-line camelcase
import { fetch_post, fetch_get_params } from '../api';

export default {
  createFaq (params) {
    return fetch_post('/rest/doc/faq/create', params);
  },
  deleteFaq (params) {
    return fetch_post('/rest/doc/faq/delete', params);
  },
  getDetail (params) {
    return fetch_get_params('/rest/doc/faq/detail', params);
  },
  getList (params) {
    return fetch_get_params('/rest/doc/faq/list', params);
  },
  relateList (params) {
    return fetch_get_params('/rest/doc/faq/list-for-relate', params);
  },
  aclFaq (params) {
    return fetch_get_params('/rest/doc/faq/acl', params);
  },
  faqPublish (params) {
    return fetch_post('/rest/doc/faq/publish', params);
  },
  faqSetTop (params) {
    return fetch_post('/rest/doc/faq/set-top', params);
  },
  updateFaq (params) {
    return fetch_post('/rest/doc/faq/update', params);
  }
};
