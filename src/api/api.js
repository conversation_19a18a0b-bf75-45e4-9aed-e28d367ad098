/* eslint-disable one-var */
/* eslint-disable camelcase */
import axios from 'axios';
import qs from 'qs';
import store from '../store/index';
import iView from 'iview';

axios.defaults.baseURL = process.env.baseURl;
// axios.defaults.baseURL = 'http://172.18.168.76:8088'; // 文博
// axios.defaults.baseURL = 'http://172.18.164.219:8088';

// 响应时间
axios.defaults.timeout = 90000;
// 请求头
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
// http request 拦截器
axios.interceptors.request.use(
  config => {
    if (config.url.indexOf('/rest/') !== -1) {
      // console.log(config.url);
      if (
        window.localStorage.accesstoken &&
                window.localStorage.accesstoken !== 'undefined'
      ) {
        // 判断是否存在token，如果存在的话，则每个http header都加上token
        config.headers.Authorization = `Bearer ${localStorage.accesstoken}`;
      }
    }
    if (store.state.ifShowCheckFlag) {
      localStorage.contentType = config.headers['Content-type']
        ? config.headers['Content-type']
        : 'application/json;charset=UTF-8';
      return false;
    }
    return config;
  },
  err => {
    return Promise.reject(err);
  }
);

// 按钮判断是否设置审核

function checkBtnAudit (currentUrl, params) {
  store.state.commonAuditParam = params;
  let buttonsArr = []
  try {
    buttonsArr = JSON.parse(localStorage.buttonsArr)
  } catch (error) {}
  buttonsArr.forEach(ele => {
    if (ele.url === currentUrl) {
      if (ele.needAudit) {
        // 只要需要审核就将请求提示 改为后台返回字段
        localStorage.needAudit = 'true';
        localStorage.commonUrl = currentUrl;
        if (!localStorage.checkCause) {
          store.state.ifShowCheckFlag = true;
        } else {
          store.state.ifShowCheckFlag = false;
        }
      } else {
        localStorage.needAudit = 'false';
        store.state.ifShowCheckFlag = false;
      }
    }
  });
}

// http response 拦截器
axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 返回 401 清除token信息并跳转到第三方登录页面
          localStorage.clear();
          // location.reload();
          // localStorage.removeItem('token')
          alert('权限失效，请重新登录(如boss已经登录，则自动登录)');
          // todo  跳转权限中心改造
          current_yop_config_url().then(response => {
            localStorage.linkLogCenter = response.data.data.config.linkLogCenter;
            localStorage.linkCallChain = response.data.data.config.linkCallChain;
            localStorage.logoutUrl = response.data.data.config.logoutUrl;
            let uriTmp = response.data.data.config.loginUrl;
            if(response.data.data.config.loginType === 'yuia') {
              location.href = `${uriTmp}?callback=${location.href}`
            } else {
              if (location.href.indexOf('?callback=') !== -1) {
                location.href =  uriTmp +
                              '?returnUrl=' +
                              encodeURIComponent(
                                location.href.substring(
                                  0,
                                  location.href.indexOf('?callback=')
                                ) +
                                      '?callback=' +
                                      location.href.substring(
                                        0,
                                        location.href.indexOf('?callback=')
                                      )
                              );
              } else {
                location.href =  uriTmp + '?returnUrl=' + encodeURIComponent( location.href + '?callback=' + location.href );
              }
            }
            
          });
          break;
        case 400:
          if (
            error.response.config.url.indexOf(
              './dist/serverConfig.json'
            ) !== -1
          ) {
            alert(
              '请清空缓存后重试，如仍无法进入，请联系YOP技术支持人员'
            );
          }
          break;
        case 500:
          iView.Modal.warning({
            title: '',
            content: error.response.data.message
          });
          break;
                // location.href='http://employee.yeepay.com:8001/employee-boss/loginout/showLogin?returnUrl='+encodeURIComponent(location.href+'?callback='+location.href);
      }
    }
    return Promise.reject(error.response.data); // 返回接口返回的错误信息
  }
);
/**
 * 需要审核的 公共请求
 */
export function fetch_audit_post (url, params) {
  return new Promise((resolve, reject) => {
    axios
      .post(url, params, {
        headers: {
          'Content-Type': localStorage.checkCause
            ? localStorage.contentType
            : 'application/json;charset=UTF-8',
          'x-yop-cause': encodeURI(localStorage.checkCause)
        }
      })
      .then(
        response => {
          localStorage.checkCause = '';
          resolve(response.data);
          error_reject(response.data);
        },
        err => {
          reject(err);
        }
      )
      .catch(error => {
        reject(error);
      });
  });
}

// 发送post请求
export function fetch_post (url, params) {
  checkBtnAudit(url, params);
  return new Promise((resolve, reject) => {
    axios
      .post(url, params, {
        headers: {
          // 'Content-Type': 'application/json;charset=UTF-8',
          'x-yop-cause': encodeURI(localStorage.checkCause)
        }
      })
      .then(
        response => {
          localStorage.checkCause = '';
          resolve(response.data);
          error_reject(response.data);
        },
        err => {
          reject(err);
        }
      )
      .catch(error => {
        reject(error);
      });
  });
}
// multi-post请求
export function fetch_post_multi (url, params) {
  checkBtnAudit(url, params);
  return new Promise((resolve, reject) => {
    axios
      .post(url, params, {
        headers: {
          'Content-type': 'multipart/form-data',
          'x-yop-cause': encodeURI(localStorage.checkCause)
        }
      })
      .then(
        response => {
          localStorage.checkCause = '';
          resolve(response.data);
          error_reject(response.data);
        },
        err => {
          reject(err);
        }
      )
      .catch(error => {
        reject(error);
      });
  });
}

// 普通post请求
export function fetch_post_normal (url, params) {
  store.state.commonAuditParam = params;
  checkBtnAudit(url, qs.stringify(params));
  return new Promise((resolve, reject) => {
    axios
      .post(url, qs.stringify(params), {
        headers: {
          'Content-type': 'application/x-www-form-urlencoded',
          'x-yop-cause': encodeURI(localStorage.checkCause)
        }
      })
      .then(
        response => {
          localStorage.checkCause = '';
          resolve(response.data);
          error_reject(response.data);
        },
        err => {
          reject(err);
        }
      )
      .catch(error => {
        reject(error);
      });
  });
}

// 普通post请求
export function fetch_post_normal_array (url, params) {
  checkBtnAudit(url, qs.stringify(params));
  return new Promise((resolve, reject) => {
    axios
      .post(url, qs.stringify(params, { arrayFormat: 'repeat' }), {
        headers: {
          'Content-type': 'application/x-www-form-urlencoded',
          'x-yop-cause': encodeURI(localStorage.checkCause)
        }
      })
      .then(
        response => {
          localStorage.checkCause = '';
          resolve(response.data);
          error_reject(response.data);
        },
        err => {
          reject(err);
        }
      )
      .catch(error => {
        reject(error);
      });
  });
}

// 发送get请求
export function fetch_get (url, params) {
  return new Promise((resolve, reject) => {
    axios
      .get(url, params)
      .then(
        response => {
          resolve(response);
          error_reject(response.data);
        },
        err => {
          reject(err);
        }
      )
      .catch(error => {
        reject(error);
      });
  });
}
// 发送get带参数请求
export function fetch_get_params (url, params) {
  return new Promise((resolve, reject) => {
    var param_real = {
      params: params
    };
    axios
      .get(url, param_real)
      .then(
        response => {
          resolve(response);
          error_reject(response.data);
        },
        err => {
          reject(err);
        }
      )
      .catch(error => {
        reject(error);
      });
  });
}

// 发送get带form文件下载
export function fetch_get_params_normal_blob (url, params, type) {
  return new Promise((resolve, reject) => {
    var param_real = {
      params: params,
      paramsSerializer: params => {
        return qs.stringify(params, { indices: false });
      }
    };
    // console.log(param_real);
    axios
      .get(url, param_real, { responseType: 'json' })
      .then(
        response => {
          if (response.data.status !== 'error') {
            // 文件名
            if (response.headers['content-disposition']) {
              var contentDispositionArr = response.headers[
                  'content-disposition'
                ].split('filename='),
                contentDisposition = contentDispositionArr[1].split(
                  '.'
                );
            } else {
              // eslint-disable-next-line no-redeclare
              var contentDisposition = ['请命名文件'];
            }
            var content = '';
            if (type === 'yaml') {
              content = response.data;
            } else {
              content = JSON.stringify(response.data);
            }
            const blob = new Blob([content]);
            const fileName = `${
              contentDisposition
                ? contentDisposition[0]
                : '请命名文件'
            }.${type}`;
            if ('download' in document.createElement('a')) {
              // 非IE下载
              const elink = document.createElement('a');
              elink.download = fileName;
              elink.style.display = 'none';
              elink.href = URL.createObjectURL(blob);
              document.body.appendChild(elink);
              elink.click();
              URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
            } else {
              // IE10+下载
              navigator.msSaveBlob(blob, fileName);
            }
            resolve(response);
            error_reject(response.data);
          } else {
            resolve(response);
            error_reject(response.data);
          }
        },
        err => {
          reject(err);
        }
      )
      .catch(error => {
        reject(error);
      });
  });
}
// 发送get带form参数请求
export function fetch_get_params_normal (url, params) {
  return new Promise((resolve, reject) => {
    var param_real = {
      params: params,
      paramsSerializer: params => {
        return qs.stringify(params, { indices: false });
      }
    };
    axios
      .get(url, param_real)
      .then(
        response => {
          resolve(response);
          error_reject(response.data);
        },
        err => {
          reject(err);
        }
      )
      .catch(error => {
        reject(error);
      });
  });
}
export function fetch_all (array) {
  return new Promise((resolve, reject) => {
    axios
      .all(array)
      .then(
        response => {
          resolve(response);
        },
        err => {
          reject(err);
        }
      )
      .catch(error => {
        reject(error);
      });
  });
}
// 执行多个并发请求

// function createRequest(url, method = 'get', data = {}){
//     if(method === 'get'){
//         return axios({
//             url,
//             method,
//             params: data
//         })
//     }else {
//         return axios({
//             url,
//             method,
//             data
//         })
//     }
// }
// 请求当前环境ip
function current_yop_config_url () {
  return fetch_get('/rest/config');
}
// 现在时间转时间戳
function nowtimeToTimestamp (time) {
  let times =
        time.getFullYear() +
        '-' +
        (time.getMonth() + 1) +
        '-' +
        time.getDate() +
        ' ' +
        time.getHours() +
        ':' +
        time.getMinutes() +
        ':' +
        time.getSeconds();
  return Date.parse(times);
}
// 缓存过期时间转时间戳
function storageTimeToTimestamp (time) {
  return Date.parse(time);
}

export default {
  /**
     * 获取access token过期时间和refresh token及过期时间并重新附值
     */
  yop_dashboard_actoken (type) {
    return fetch_get(`/signin/sso/boss3g?${type ? type : 'token'}=${localStorage.token}`);
    //     .then(
    //     function(res){
    //         console.log(res);
    //         window.localStorage.accesstoken=res.value;
    //         window.localStorage.accessExpire=res.expiration;
    //         window.localStorage.refreshtoken=res.refreshToken.value;
    //         window.localStorage.refreshExpire=res.refreshToken.expiration;
    //         axios.defaults.headers.common['Authorization'] = 'Bearer '+ localStorage.accesstoken;
    //         console.log(localStorage.accesstoken);
    //     }
    // ),err => function(err){
    //     console.log(err)
    // }
  },
  /**
     * 检查access token是否过过期
     */
  yop_dashboard_checkAccesstoken () {
    var now_Date = new Date();
    var now_Date_Timestamp = nowtimeToTimestamp(now_Date);
    var access_Expire_Date = window.localStorage.accessExpire;
    var access_Expire_Date_Timestamp = storageTimeToTimestamp(
      access_Expire_Date
    );
    if (now_Date_Timestamp >= access_Expire_Date_Timestamp) {
      return true;
    } else {
      return false;
    }
  },
  /**
     * 检查refresh token是否过期
     */
  yop_dashboard_checkRefreshtoken () {
    var now_Date = new Date();
    var now_Date_Timestamp = nowtimeToTimestamp(now_Date);
    var access_Expire_Date = window.localStorage.refreshExpire;
    var access_Expire_Date_Timestamp = storageTimeToTimestamp(
      access_Expire_Date
    );
    if (now_Date_Timestamp >= access_Expire_Date_Timestamp) {
      return true;
    } else {
      return false;
    }
  },
  /**
     * 通过refresh token重新获取access token
     */
  // yop_dashboard_regetAccessToken () {
  // fetch_get('/oauth2/refresh?token='+token).then(
  //     function () {
  //
  // })

  // },
  /**
     * ------------------------------
     * 获取第三方登录地址
     * ------------------------------
     */
  yop_getConfigUrl () {
    return fetch_get('/rest/config');
  },
  /**
     * ------------------------------
     * 获取当前版本号
     * ------------------------------
     */
  yop_getVersion () {
    return fetch_get('/rest/project-version/version-number');
  },
  /**
     * ------------------------------
     * 通用自定义接口
     * ------------------------------
     */
  yop_common_getlist (uri) {
    return fetch_get(uri);
  },

  yop_common_getlist_params (uri, param) {
    return fetch_get_params(uri, param);
  },

  /**
     * 需要审核公共接口请求
     */
  yop_common_audit (params) {
    return fetch_audit_post(`${localStorage.commonUrl}`, params);
  },
  /**
     * api分组安全需求获取
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_security () {
    return fetch_get('/rest/security-def/commons/list');
  },
  /**
     * spcode查询
     */
  yop_invokeSta_spCode () {
    return fetch_get('/rest/isp/commons/sp-codes');
  },

  yop_auth_resource2 () {
    return fetch_get('/rest/resource/has-rights');
  },
  /**
     * ------------------------------
     * api回归测试接口（test.vue）
     * ------------------------------
     */
  /**
     * 获取api list
     */
  yop_dashboard_apilist (params) {
    // axios.defaults.headers.common['Authorization'] = 'Bearer '+ localStorage.accesstoken;
    return fetch_get('/rest/regression-test/test/list');
    // return createRequest('/rest/regression-test/list', 'post');
  },
  /**
     * 获取api list
     */
  yop_dashboard_apilist_query (params) {
    return fetch_get('/rest/regression-test/test/list', params);
  },

  /**
     * 测试用例导出
     * /rest/regression-test/case/export
     */
  yop_regression_test_case_export (params) {
    return fetch_post_normal_array(
      '/rest/regression-test/case/export',
      params
    );
  },
  /**
     * 测试用例导入
     * /rest/regression-test/case/import
     */
  yop_regression_test_case_import (params) {
    return fetch_post('/rest/regression-test/case/import', params);
  },
  /**
     * ------------------------------
     * 权限相关
     * ------------------------------
     */
  yop_auth_resource () {
    return fetch_get('/rest/resource/has-rights');
  },
  yop_auth_get_menus () {
    return fetch_get('/rest/resource/menus');
  },
  yop_auth_get_Buttons (params) {
    return fetch_get('/rest/resource/buttons', params);
  },
  /**
     * 获取api分组信息
     */
  yop_dashboard_ApiGroup () {
    // axios.defaults.headers.common['Authorization'] = 'Bearer '+ localStorage.accesstoken;
    return fetch_get('/rest/api-group/list-with-api');
    // return createRequest('/rest/regression-test/list', 'post');
  },
  /**
     * 查询可执行环境
     */
  yop_dashboard_envList () {
    return fetch_get('/rest/regression-test/commons/env/list');
  },
  /**
     * 一键回归选中api
     */
  yop_dashboard_executeBatch (params) {
    return fetch_post('/rest/regression-test/test/execute-batch', params);
  },
  /**
     * 测试用例结果查询
     */
  yop_dashboard_queryExecuteBatchResult (params) {
    return fetch_get_params(
      '/rest/regression-test/test/query-execution-result',
      params
    );
  },
  /**
     * 回归日志查询
     */
  yop_dashboard_checkLog (params) {
    return fetch_get_params('/rest/regression-test/test/log', params);
  },
  /**
     * ------------------------------
     * 接口测试接口(list_single.vue)
     * ------------------------------
     */
  /**
     * 左侧菜单展示列表获取
     */
  yop_dashboard_caseList (params) {
    // return fetch_get('/rest/api-group/list-with-api',params)
    return fetch_get_params('/rest/regression-test/case/list', params);
  },

  yop_dashboard_interfaceMenuList (params) {
    // return fetch_get('/rest/api-group/list-with-api',params)
    return fetch_get('/rest/api-group/commons/api-group-codes');
  },
  /**
     * 单击获取api测试用例列表
     */
  yop_dashboard_testTemplateList (params) {
    return fetch_post_normal(
      '/rest/regression-test/case/list-single-api',
      params
    );
  },
  /**
     * 单击获取测试用例详细信息
     */
  yop_dashboard_regressionDetail (params) {
    return fetch_get('/rest/regression-test/case/detail', params);
  },
  yop_token_list (params) {
    return fetch_get_params_normal(
      '/rest/regression-test/commons/token/list',
      params
    );
  },
  /**
     * 测试用例执行
     */
  yop_dashboard_executeSingle (params) {
    return fetch_post(
      '/rest/regression-test/case/execute-single-by-raw',
      params
    );
  },
  /**
     * 测试用例保存
     */
  yop_dashboard_saveTemplate (params) {
    return fetch_post('/rest/regression-test/case/save', params);
  },
  /**
     * 测试用例删除
     */
  yop_dashboard_deleteTemplate (params) {
    return fetch_post_normal_array(
      '/rest/regression-test/case/delete',
      params
    );
  },
  /**
     * 测试应用创建
     */
  yop_dashboard_appCreate (params) {
    return fetch_post_normal('/rest/regression-test/app/create', params);
  },
  /**
     * acesstoken创建
     */
  yop_dashboard_token_update (params) {
    return fetch_post('/rest/regression-test/token/update', params);
  },
  /**
     * aceesstoken信息获取
     */
  yop_dashboard_token_detail (params) {
    return fetch_get_params('/rest/regression-test/token/detail', params);
  },
  /**
     * aceesstoken删除
     */
  yop_dashboard_token_delete (params) {
    return fetch_post_normal('/rest/regression-test/token/delete', params);
  },
  /**
     * ------------------------------
     * modal管理接口(modal.vue/及其子组件)
     * ------------------------------
     */
  /**
     *
     * spi分页查询
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_list (params) {
    return fetch_get_params('/rest/spi/list', params);
  },
  /**
     *
     * spi启用
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_enable (params) {
    return fetch_post_normal('/rest/spi/enable', params);
  },
  /**
     *
     * spi禁用
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_disable (params) {
    return fetch_post_normal('/rest/spi/disable', params);
  },
  /**
     *
     * spi 详情查询
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_detail (params) {
    return fetch_get_params('/rest/spi/detail', params);
  },
  /**
     *
     * spi 创建
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_create (params) {
    return fetch_post('/rest/spi/create', params);
  },
  /**
     *
     * spi 修改
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_update (params) {
    return fetch_post('/rest/spi/update', params);
  },
  /**
     *
     * spi 删除
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_delete (params) {
    return fetch_post_normal('/rest/spi/delete', params);
  },
  /**
     *
     * spi spi列表查询 (仅查询CALLlBACK类型)
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_simple_list (params) {
    return fetch_get_params_normal('/rest/spi/simple-list', params);
  },
  /**
     *
     * spi变更记录分页查询
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_change_record (params) {
    return fetch_get_params('/rest/spi/change-record', params);
  },
  /**
     *
     * spi变更记录详情查询
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_change_record_detail (params) {
    return fetch_get_params('/rest/spi/change-record/detail', params);
  },
  /**
     *
     *
     * spi变更操作类型
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_change_optype (params) {
    return fetch_get('/rest/spi/commons/op-type', params);
  },
  /**
     *
     *
     */
  /**
     * spi变更记录回滚
     * @param {*} params
     */

  yop_spiManagement_spi_change_record_rollback (params) {
    return fetch_post_normal('/rest/spi/change-record/rollback', params);
  },
  /**
     *
     * 导入请求分析
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_import_analysis (params) {
    return fetch_post_multi('/rest/spi/import/analysis', params);
  },
  /**
     *
     * 提交导入请求
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_import (params) {
    return fetch_post('/rest/spi/import', params);
  },
  /**
     *
     * 导出SPI
     */
  /**
     *
     * @param {*} params
     */

  yop_spiManagement_spi_export (params, type) {
    return fetch_get_params_normal_blob('/rest/spi/export', params, type);
  },
  /**
     * modal 列表
     *
     */
  yop_modalManagement_modal_list (params) {
    return fetch_get_params('/rest/model/list', params);
  },
  /**
     * modal 详情
     *
     */
  yop_modalManagement_modal_detail (params) {
    return fetch_get_params('/rest/model/detail', params);
  },
  /**
     * modal 新增
     *
     */
  yop_modalManagement_modal_create (params) {
    return fetch_post('/rest/model/create', params);
  },
  /**
     * modal 编辑
     *
     */
  yop_modalManagement_modal_update (params) {
    return fetch_post('/rest/model/update', params);
  },
  /**
     * modal 删除
     *
     */
  yop_modalManagement_modal_delete (params) {
    return fetch_post_normal('/rest/model/delete', params);
  },
  /**
     * model列表查询
     *
     */
  yop_modalManagement_modal_simple_list (params) {
    return fetch_get_params('/rest/model/simple-list', params);
  },
  /**
     *model变更记录分页查询
     *
     */
  yop_modalManagement_change_record (params) {
    return fetch_get_params('/rest/model/change-record', params);
  },
  /**
     *model变更记录详情查询
     *
     */
  yop_modalManagement_change_record_detail (params) {
    return fetch_get_params('/rest/model/change-record/detail', params);
  },

  /**
     *model变更记录回滚
     *
     */
  yop_modalManagement_modal_rollback (params) {
    return fetch_post_normal('/rest/model/change-record/rollback', params);
  },

  /**
     *model变更操作类型
     *
     */
  yop_modalManagement_change_optype (params) {
    return fetch_get('/rest/model/commons/op-type', params);
  },
  /**
     * api管理接口(api/list.vue/api_basics.vue及其子组件) now
     */
  /**
     * api分页查询
     */
  yop_api_manage_list (params) {
    return fetch_get_params('/rest/api/manage/list', params);
  },
  /**
     * api选项
     */
  yop_api_manage_commons_api_options (params) {
    return fetch_get_params('/rest/api/manage/commons/api-option', params);
  },
  // 新增api 路径校验
  yop_api_path_exists (params) {
    return fetch_get_params('/rest/api/path/exists', params);
  },
  /**
     * api删除
     */
  yop_api_manage_delete (params) {
    return fetch_post_normal('/rest/api/manage/delete', params);
  },
  /**
     * api启用
     */
  yop_api_manage_enable (params) {
    return fetch_post_normal('/rest/api/manage/enable', params);
  },
  /**
     * api禁用
     */
  yop_api_manage_disable (params) {
    return fetch_post_normal('/rest/api/manage/disable', params);
  },
  /**
     * api 详情查询
     */
  yop_api_manage_detail (params) {
    return fetch_get_params('/rest/api/manage/detail', params);
  },
  /**
     * api 创建
     */
  yop_api_manage_create (params) {
    return fetch_post('/rest/api/manage/create', params);
  },
  /**
     * api 修改
     */
  yop_api_manage_update (params) {
    return fetch_post('/rest/api/manage/update', params);
  },
  /**
     * api变更记录分页查询
     */
  yop_api_manage_change_record (params) {
    return fetch_get_params('/rest/api/manage/change-record', params);
  },
  /**
     * api变更记录详情查询
     */
  yop_api_manage_change_record_detail (params) {
    return fetch_get_params(
      '/rest/api/manage/change-record/detail',
      params
    );
  },
  /**
     * api变更记录回滚
     */
  yop_api_manage_change_record_rollback (params) {
    return fetch_post_normal(
      '/rest/api/manage/change-record/rollback',
      params
    );
  },

  /**
     * api变更操作类型
     */
  yop_api_manage_commons_op_type (params) {
    return fetch_get_params('/rest/api/manage/commons/op-type', params);
  },
  /**
     * api导入请求分析
     */
  yop_apiManagement_api_import_analysis (params) {
    return fetch_post_multi('/rest/api/manage/import/analysis', params);
  },
  /**
     * 路由导入请求分析
     */
  yop_apiManagement_route_import_analysis (params) {
    return fetch_post_multi('/rest/api/route/import/analysis', params);
  },
  /**
     * api系统参数
     */
  yop_apiManagement_commons_sys_params (params) {
    return fetch_get_params('/rest/api/manage/commons/sys-params', params);
  },

  /**
     *
     * @param {*} params api提交导入请求
     */

  yop_apiManagement_api_import (params) {
    return fetch_post('/rest/api/manage/import', params);
  },
  /**
     *
     * @param {*} params api提交导出请求
     */
  yop_apiManagement_api_export (params, type) {
    return fetch_get_params_normal_blob(
      '/rest/api/manage/export',
      params,
      type
    );
  },
  /**
     *
     * @param {*} params 路由提交导入请求
     */

  yop_apiManagement_route_import (params) {
    return fetch_post('/rest/api/route/import', params);
  },
  /**
     *
     * @param {*} params 路由提交导出请求
     */
  yop_apiManagement_route_export (params, type) {
    return fetch_get_params_normal_blob(
      '/rest/api/route/export',
      params,
      type
    );
  },

  /**
     * api管理接口(api_Management_Open.vue/api_basics.vue及其子组件)
     */
  /**
     * api分页查询
     */
  yop_dashboard_apis_list () {
    return fetch_get('/rest/api-group/commons/api-group-codes');
  },
  /**
     * 端点服务根据类名解析方法
     */
  yop_apiManagement_loader_methodQuery (params) {
    return fetch_get('/rest/loader/method', params);
  },
  /**
     * 常用内部参数获取
     */
  yop_apiManagement_loader_internalVarable () {
    return fetch_get('/rest/loader/internal-variable/expressions');
  },
  /**
     * 解析apiuri
     */
  yop_apiManagement_config_autoGenerate_Api (params) {
    return fetch_get_params_normal(
      '/rest/api/config/auto-generate-operation',
      params
    );
  },
  /**
     * 生成apiOperation
     */
  yop_apiManagement_config_autoGenerate_uri (params) {
    return fetch_get_params('/rest/api/config/auto-generate-uri', params);
  },
  /**
     * 参数格式加载
     */
  yop_apiManagement_config_paramDataType () {
    return fetch_get('/rest/api/commons/param-data-type/list');
  },
  /**
     * apiuri校验
     */
  yop_apiManagement_apiConfig_apiUriValidate (params) {
    return fetch_get_params('/rest/api/config/api-uri-validate', params);
  },
  /**
     * api创建
     */
  yop_apiManagement_apiCreate (params) {
    return fetch_post('/rest/api/config/create', params);
  },
  /**
     * api编辑
     */
  yop_apiManagement_edit_detail (params) {
    return fetch_get_params('/rest/api/config/detail', params);
  },
  /**
     * api修改
     */
  yop_apiManagement_apiConfigEdit (params) {
    return fetch_post('/rest/api/config/edit', params);
  },
  /**
     * api环境枚举
     */
  yop_apiManagement_deploy_env (params) {
    return fetch_get('/rest/api/commons/deploy-env', params);
  },
  /**
     * api 发布操作类型枚举
     */
  yop_apiManagement_deploy_optypes (params) {
    return fetch_get('/rest/api/commons/deploy-op-types', params);
  },
  /**
     * api下线
     */
  yop_apiManagement_offline (params) {
    return fetch_post_normal('/rest/api/manage/offline', params);
  },
  /**
     * api 历史版本
     */
  yop_api_history_list (params) {
    return fetch_get_params('/rest/api/manage/sub-ref/list', params);
  },
  /**
     * api升级
     */
  yop_apiManagement_upgrade_production (params) {
    return fetch_post_normal('/rest/api/manage/upgrade-production', params);
  },
  /**
     * api发布
     */
  yop_apiManagement_deploy (params) {
    return fetch_post_normal('/rest/api/manage/deploy', params);
  },
  /**
     * api复制
     */
  yop_apiManagement_snapshot_copy (params) {
    return fetch_post_normal('/rest/api/manage/snapshot/copy', params);
  },
  /**
     * 切换api版本
     */
  yop_apiManagement_deployenv_upgrade (params) {
    return fetch_post_normal('/rest/api/manage/deploy-env/upgrade', params);
  },
  /**
     * api状态查询
     */
  yop_apiManagement_deploy_status_view (params) {
    return fetch_get_params('/rest/api/manage/deploy-status/view', params);
  },
  /**
     *api快照查询
     *
     */
  yop_modalManagement_snapshot_view (params) {
    return fetch_get_params('/rest/api/manage/snapshot/view', params);
  },
  /**
     * api发布记录
     */
  yop_apiManagement_deploy_record_list (params) {
    return fetch_get_params('/rest/api/manage/publish-record/list', params);
  },
  // 关联api 列表
  yop_api_relation_list (params) {
    return fetch_get_params('/rest/api-relation/list', params);
  },
  // 关联api 下拉数据  
  yop_api_relation_apis (params) {
    return fetch_get_params('/rest/api-relation/apis', params);
  },
  // 结果通知下拉数据
  yop_api_relation_spis (params) {
    return fetch_get_params('/rest/api/related-spis', params);
  },
  // 新增创建关联api关系
  yop_api_relation_create (params) {
    return fetch_post('/rest/api-relation/create', params);
  },
  // 获取详情
  yop_api_relation_detail (params) {
    return fetch_get_params('/rest/api-relation/detail', params);
  },
  // 编辑关联api关系
  yop_api_relation_edit (params) {
    return fetch_post('/rest/api-relation/edit', params);
  },
  // 删除
  yop_api_relation_delete (params) {
    return fetch_post('/rest/api-relation/delete', params);
  },
  /**
     * api管理页面总查询
     */
  yop_apiManagement_apiList () {
    return fetch_get('/rest/api/list');
  },
  /**
     * api管理页面分页查询
     */
  yop_apiManagement_apiList_page (params) {
    return fetch_get_params('/rest/api/list', params);
  },
  /**
     * api管理页面查询安全需求列表
     */
  yop_apiManagement_securityReqList () {
    return fetch_get('/rest/security-def/commons/list');
  },
  /**
     * api管理页面查询API状态列表
     */
  yop_apiManagement_apiCommonStatusList () {
    return fetch_get('/rest/api/commons/status/list');
  },
  /**
     * api管理页面查询API类型列表
     */
  yop_apiManagement_apiCommonTypeList () {
    return fetch_get('/rest/api/commons/type/list');
  },
  /**
     * api管理页面启用
     */
  yop_apiManagement_apiActive (params) {
    return fetch_post_normal('/rest/api/active', params);
  },
  // 获取产品了类型
  yop_apiManagement_productTypeList () {
    return fetch_get('/rest/product/commons/type');
  },
  // api 批量审核
  yop_apiManagement_batchReview (params) {
    return fetch_post('/rest/examine/product-api', params);
  },
  /**
     * api管理页面禁用
     */
  yop_apiManagement_apiForbid (params) {
    return fetch_post_normal('/rest/api/forbid', params);
  },
  /**
     * api管理页面删除
     */
  yop_apiManagement_apiDelete (params) {
    return fetch_post_normal('/rest/api/delete', params);
  },
  /**
     * api分组详情
     */
  yop_apiManagement_apiGroupDetail (params) {
    return fetch_get_params('/rest/api-group/detail', params);
  },
  /**
     * api 导出接口
     */
  yop_apiManagement_apiExport (params) {
    return fetch_get('/rest/api/export', params);
  },

  /**
     * api已关联spi列表
     */
  yop_apiManagement_old_callbacks (params) {
    return fetch_get_params('/rest/api/old/callbacks', params);
  },
  /**
     * spi 新增下拉框列表
     */
  yop_apiManagement_simple_list (params) {
    return fetch_get_params('/rest/spi/simple-list', params);
  },
  /**
     * 保存新增 spi
     */
  yop_apiManagement_callback_add (params) {
    return fetch_post('/rest/api/old/callback/add', params);
  },
  /**
     * 保存新增 spi
     */
  yop_apiManagement_callback_delete (params) {
    return fetch_post('/rest/api/old/callback/delete', params);
  },

  /**
     *
     * @param {*} params
     * 后端服务列表查询
     */
  yop_backend_service_simple_list (params) {
    return fetch_get_params(
      '/rest/backend-service/commons/simple-list',
      params
    );
  },
  /**
     *
     * @param {*} params
     * 后端服务列表查询
     */
  yop_backend_service_sp_codes_list (params) {
    return fetch_get_params('/rest/isp/commons/sp-codes ', params);
  },
  /**
     * api系统参数列表(下拉框)
     */
  yop_commons_sys_params (params) {
    return fetch_get_params('/rest/api/commons/sys-params', params);
  },

  /***
     * 后端服务分页查询
     */
  yop_backend_service_list (params) {
    return fetch_get_params('/rest/backend-service/list', params);
  },
  /***
     * 后端服务详情查询
     */
  yop_backend_service_detail (params) {
    return fetch_get_params('/rest/backend-service/detail', params);
  },

  /***
     * 后端服务创建
     */
  yop_backend_service_create (params) {
    return fetch_post('/rest/backend-service/create', params);
  },
  /***
     * 后端服务修改
     */
  yop_backend_service_update (params) {
    return fetch_post('/rest/backend-service/update', params);
  },
  /***
     * 后端服务删除
     */
  yop_backend_service_delete (params) {
    return fetch_post_normal('/rest/backend-service/delete', params);
  },
  /***
     * 后端服务类型下拉框
     */
  yop_backend_service_simple_type_list (params) {
    return fetch_get('/rest/backend-service/commons/type', params);
  },

  /**
     * api安全需求列表和表项
     */
  yop_apiManagement_safetyRequest (params) {
    return fetch_all([
      this.yop_apiManagement_apiGroupDetail(params),
      this.yop_apiManagement_securityReqList()
    ]);
  },
  /**
     * 获取安全需求全部列表和比api页面编辑数据
     */
  yop_apiManagement_editDetail_safeList (params) {
    return fetch_all([
      this.yop_apiManagement_edit_detail(params),
      this.yop_apiManagement_securityReqList()
    ]);
  },
  /**
     * 获取api安全需求详情
     */
  yop_api_security_detail (params) {
    return fetch_get_params('/rest/api/security-req', params);
  },
  /**
     * api安全需求设置
     */
  yop_api_security_set (params) {
    return fetch_post('/rest/api/security-req/update', params);
  },
  /**
     * ------------------------------
     * 调用记录接口(invoke_log.vue)
     * ------------------------------
     */
  /**
     * 获取全部错误码
     */
  yop_invokeLog_errorCode () {
    // return fetch_get('/rest/error-code/commons/error-codes')
    return fetch_get('/rest/api-group/error-code/commons/list');
    // return fetch_get('/rest/error-code/commons/list')
  },
  /**
     * 调用记录列表查询
     */
  yop_invokeLog_list (params) {
    return fetch_get_params('/rest/invoke-logging/list', params);
  },
  /**
     * 调用记录列表详情
     */
  yop_invokeLog_detail (params) {
    return fetch_get_params('/rest/invoke-logging/detail', params);
  },
  /**
     * 调用记录列表详情
     */
  yop_invokeLog_errorCode_solution (params) {
    return fetch_get_params('/rest/api-group/error-code/solution', params);
  },
  /**
     * ------------------------------
     * 调用统计接口(invoke_statistic.vue)
     * ------------------------------
     */
  /**
     * api-group调用次数统计
     */
  yop_invokeSta_times (params) {
    return fetch_get_params('/rest/invoke-stat/api-group/list', params);
  },
  /**
     * api查询耗时统计
     */
  yop_invokeSta_cost (params) {
    return fetch_get_params('/rest/invoke-stat/latency/api-uri', params);
  },
  /**
     * ------------------------------
     * ceph页面(ceph_config.vue)
     * ------------------------------
     */
  /**
     * ceph列表页面
     */
  yop_ceph_list (params) {
    return fetch_get_params('/rest/filestore/api-group/list', params);
  },
  /**
     * 文件存储配置新增
     */
  yop_ceph_add (params) {
    return fetch_post('/rest/filestore/api-group/create', params);
  },
  /**
     * 文件存储配置单条查看
     */
  yop_ceph_detail_single (params) {
    return fetch_get_params('/rest/filestore/api-group/detail', params);
  },
  /**
     * 文件存储配置删除
     */
  yop_ceph_delete (params) {
    return fetch_post_normal('/rest/filestore/delete', params);
  },
  /**
     * 文件存储配置更新
     */
  yop_ceph_update (params) {
    return fetch_post('/rest/filestore/api-group/update', params);
  },
  /**
     * 文件存储配置类型列表
     */
  yop_ceph_configList (params) {
    return fetch_get('/rest/filestore/commons/store-types');
  },
  /**
     * 文件存储配置默认校验
     */
  yop_ceph_isDefault (params) {
    return fetch_get('/rest/yos-store/has-default');
  },
  /**
     * ------------------------------
     * api分组页面(api_group.vue)
     * ------------------------------
     */
  /**
     * api分组列表
     */
  yop_apiGroup_list (params) {
    return fetch_get_params('/rest/api-group/list', params);
  },
  /**
     * api分组添加
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_add (params) {
    return fetch_post('/rest/api-group/create', params);
  },
  /**
     * api分组修改
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_edit (params) {
    return fetch_post('/rest/api-group/update', params);
  },
  /**
     * api分组验重
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_checkExist (params) {
    return fetch_get_params('/rest/api-group/check-exists', params);
  },
  /**
     * api分组单条查看
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_detail (params) {
    return fetch_get_params('/rest/api-group/detail', params);
  },
  /**
     * api分组删除
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_delete (params) {
    return fetch_post_normal('/rest/api-group/delete', params);
  },
  /**
     * 灰度规则列表
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_grayLevelList (params) {
    return fetch_post_normal('/rest/api-group/gray-policy/query', params);
  },
  /**
     * 灰度规则新增和编辑
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_grayLevelList_update (params) {
    return fetch_post('/rest/api-group/gray-policy/update', params);
  },

  /**
     * 获取git同步 模式
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_gitSync_mode (params) {
    return fetch_get_params('/rest/api-group/git-sync/mode', params);
  },
  /**
     * 获取git同步到生产
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_gitSync_commitProduction (params) {
    return fetch_post_normal(
      '/rest/api-group/git-sync/commit-to-production',
      params
    );
  },
  /**
     * 查询git同步配置
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_gitSync_config (params) {
    return fetch_get_params('/rest/api-group/git-sync/config', params);
  },
  /**
     * git同步配置 检查更新
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_gitSync_latestCommit (params) {
    return fetch_get_params(
      '/rest/api-group/git-sync/latest-commit',
      params
    );
  },
  /**
     * git同步配置 保存信息
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_gitSync_save (params) {
    return fetch_post_normal('/rest/api-group/git-sync/config', params);
  },
  /**
     * git同步配置 变更详情
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_gitSync_diff (params) {
    return fetch_get_params('/rest/api-group/git-sync/diff', params);
  },
  /**
     * git同步配置 确认同步
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_gitSync_sync (params) {
    return fetch_post_normal('/rest/api-group/git-sync/sync', params);
  },
  /**
     * git同步配置 branch 列表
     * @param params
     * @returns {结果}
     */
  yop_apiGroup_gitSync_loadBranch (params) {
    return fetch_get_params('/rest/api-group/git-sync/load-branch', params);
  },
  /**
     * ------------------------------
     * 错误码管理页面(errorCode_manage.vue)
     * ------------------------------
     */
  /**
     * 错误码列表
     */
  yop_errorCode_list (params) {
    return fetch_get_params('/rest/api-group/error-code/list', params);
  },
  /**
     * 错误码关联Api
     */
  yop_errorCode_relevance_api (params) {
    return fetch_get_params('/rest/api-group/error-code/api/list', params);
  },
  /**
     * 可加入错误码的api
     */
  yop_errorCode_join_api (params) {
    return fetch_get_params('/rest/api/list-for-join', params);
  },
  /**
     * 获取api类型
     */
  yop_errorCode_get_api_type (params) {
    return fetch_get_params('/rest/api/commons/type/list', params);
  },
  /**
     * 获取api状态
     */
  yop_errorCode_get_api_status (params) {
    return fetch_get_params('/rest/api/commons/status/list', params);
  },
  /**
     * 移除错误码关联api
     */
  yop_errorCode_delete_api (params) {
    return fetch_post(
      '/rest/api-group/error-code/api/batch-delete',
      params
    );
  },
  /**
   * API 错误码信息相关
   */
    // 错误码位置查询
    yop_errorCode_errorcode_location (params) {
      return fetch_get_params('/rest/api-extend-config/manage/detail', params);
    },
    // 错误码位置下拉框
    yop_errorCode_model_codes (params) {
      return fetch_get_params('/rest/model/codes', params);
    },
    // 错误码位置保存（新增+编辑）
    yop_errorCode_location_save (params) {
      return fetch_post('/rest/api-extend-config/manage/save', params);
    },
    // apiGroup 分组对应列表
    yop_errorCode_manage_ids (params) {
      return fetch_get_params('/rest/api-extend-config/manage/not-config-apis', params);
    },
    // API分组 批量增加错误码
    yop_errorCode_batch_add (params) {
      return fetch_post('/rest/api-extend-config/manage/batch-add', params);
    },
  /**
     * 添加错误码关联api
     */
  yop_errorCode_add_api (params) {
    return fetch_post(
      '/rest/api-group/error-code/api/batch-create',
      params
    );
  },
  /**
     * API未关联错误码
     */
  yop_errorCode_no_relevance_api (params) {
    return fetch_get_params(
      '/rest/api/manage/error-code/list-for-join',
      params
    );
  },
  /**
     * API已关联错误码
     */
  yop_errorCode_has_relevance_api (params) {
    return fetch_get_params('/rest/api/manage/error-code/list', params);
  },
  /**
     * 批量往Api添加错误码
     */
  yop_errorCode_api_add_error_code (params) {
    return fetch_post('/rest/api/manage/error-code/batch-create', params);
  },
  /**
     * 错误码详情查询
     */
  yop_errorCode_detail (params) {
    return fetch_get_params('/rest/api-group/error-code/detail', params);
  },
  /**
     * API分组公共错误码列表
     */
  yop_errorCode_groupCommon_list (params) {
    return fetch_get_params(
      '/rest/api-group/error-code/group-common/list',
      params
    );
  },
  /**
     * 错误码创建
     */
  yop_errorCode_add (params) {
    return fetch_post('/rest/api-group/error-code/create', params);
  },
  /**
     * 错误码创建
     */
  yop_errorCode_add_batch (params) {
    return fetch_post_multi(
      '/rest/api-group/error-code/batch-import',
      params
    );
  },
  /**
     * 错误码更新
     */
  yop_errorCode_update (params) {
    return fetch_post('/rest/api-group/error-code/update', params);
  },
  /**
     * 错误码删除
     */
  yop_errorCode_delete (params) {
    return fetch_post_normal('/rest/api-group/error-code/delete', params);
  },
  /**
     * 错误码类型
     */
  yop_errorCode_typeList () {
    return fetch_get(
      '/rest/api-group/error-code/commons/error-code-type/list'
    );
  },
  /**
     * 指定api下拉列表
     */
  yop_errorCode_apiSpecific (params) {
    return fetch_get_params('/rest/api-group/api-list', params);
  },

  /**
     * 指定api列表
     */
  yop_errorCode_apiSpecific_list (params) {
    return fetch_get_params(
      '/rest/api-group/error-code/api-specific/list',
      params
    );
  },

  /**
     * 文档中心
     */
  /**
     * doc分页查询
     */
  yop_doc_list (params) {
    return fetch_get_params('/rest/doc/list', params);
  },
  /**
     * 页面支持引用列表
     */
  yop_doc_common_page_list_forRef (params) {
    return fetch_get_params('/rest/doc/commons/page/list-for-ref', params);
  },
  /**
     * 编辑文档基本属性
     */
  yop_doc_settings_edit (params) {
    return fetch_post('/rest/doc/settings/edit', params);
  },
  /**
     * doc发布
     */
  yop_doc_publish (params) {
    return fetch_post_normal('/rest/doc/publish', params);
  },
  /**
     * doc发布记录分页
     */
  yop_doc_publish_list (params) {
    return fetch_get_params('/rest/doc/publish/list', params);
  },
  /**
     * doc发布记录回滚
     */
  yop_doc_recover (params) {
    return fetch_post_normal('/rest/doc/recover', params);
  },
  /**
     * 文档排序列表
     */
  yop_doc_category_list (params) {
    return fetch_get_params('/rest/doc/list-for-arrange', params);
  },
  /**
     * doc排序设置
     */
  yop_doc_category_arrange (params) {
    return fetch_post('/rest/doc/arrange', params);
  },
  /**
     * doc分类列表
     */
  yop_doc_commons_category_list () {
    return fetch_get_params('/rest/doc/commons/category/list');
  },

  /**
     * 文档基本属性-展示
     */
  yop_doc_settings_detail (params) {
    return fetch_get_params('/rest/doc/settings/detail', params);
  },
  /**
     * doc可关联产品码列表
     */
  yop_doc_products_list (params) {
    return fetch_get_params('/rest/doc/products/list', params);
  },
  /**
     * 避免重复提交审核
     */
  yop_doc_check_publish (params) {
    return fetch_post_normal('/rest/doc/check-for-publish', params);
  },

  /**
     * 编辑产品文档
     */
  yop_doc_edit_product (params) {
    return fetch_get_params('/rest/doc/edit-product', params);
  },
  /**
     * 编辑平台文档
     */
  yop_doc_edit_platform (params) {
    return fetch_get_params('/rest/doc/edit-platform', params);
  },
  /**
     * ------------------------------
     * 批量更新页面(batch_update.vue)
     * ------------------------------
     */
  /**
     * 文档模版上传
     */
  yop_batchUpdate_model_upload (params) {
    return fetch_post_normal('/rest/docs/control/upload-template', params);
  },
  /**
     * 批量生成静态文档
     */
  yop_batchUpdate_batch_static () {
    return fetch_post_normal('/rest/docs/control/update-all');
  },
  /**
     * 更新指定文档
     */
  yop_batchUpdate_update_specific (params) {
    return fetch_post('/rest/docs/control/update-specified', params);
  },
  /**
     * 上传项目静态资源
     */
  yop_batchUpdate_update_static (params) {
    return fetch_post_normal(
      '/rest/resources/control/static/upload',
      params
    );
  },
  /**
     * 查找项目版本
     */
  yop_batchUpdate_search_verion (params) {
    return fetch_get_params('/rest/resources/control/static/list', params);
  },
  /**
     * 切换项目状态为灰度/发布 false/true
     */
  yop_batchUpdate_active_version (params, status) {
    if (status) {
      return fetch_post_normal(
        '/rest/resources/control/static/publish',
        params
      );
    } else {
      return fetch_post_normal(
        '/rest/resources/control/static/gray',
        params
      );
    }
  },
  /**
     * 删除版本
     */
  yop_batchUpdate_delete_version (params) {
    return fetch_post_normal(
      '/rest/resources/control/static/delete',
      params
    );
  },
  /**
     * 清理历史版本
     */
  yop_batchUpdate_delete_history (params) {
    return fetch_post_normal(
      '/rest/resources/control/static/cleanup',
      params
    );
  },
  /**
     * 查询文档列表
     */
  yop_batchUpdate_search_docList () {
    return fetch_get('/rest/docs/control/list-simple');
  },
  /**
     * ------------------------------
     * 示例代码(sample_code.vue)
     * ------------------------------
     */
  /**
     * 示例代码更新
     */
  yop_sampleCode_update (params) {
    return fetch_post('/rest/api-group/sample-code/batch-update', params);
  },
  /**
     * 示例代码查询
     */
  yop_sampleCode_search (params) {
    return fetch_get_params('/rest/api-group/sample-code/list', params);
  },
  /**
     * ------------------------------
     * 权限资源管理(/acl/resource/test.vue)
     * ------------------------------
     */
  /**
     * 资源列表
     */
  yop_acl_resource_list () {
    return fetch_get('/rest/acl/resource/list');
  },
  /**
     * 查看单个资源
     */
  yop_acl_resource_detail (params) {
    return fetch_get_params('/rest/acl/resource/detail', params);
  },
  /**
     * 新增资源
     */
  yop_acl_resource_create (params) {
    return fetch_post('/rest/acl/resource/create', params);
  },
  /**
     * 删除资源
     */
  yop_acl_resource_delete (params) {
    return fetch_post_normal_array('/rest/acl/resource/delete', params);
  },
  /**
     * 禁用原因列表
     */
  yop_acl_resource_disableReason_list () {
    return fetch_get('/rest/acl/resource/commons/disable-reason/list');
  },
  /**
     * 查看批量资源禁用原因
     */
  yop_acl_resource_disableReason_search (params) {
    return fetch_get('/rest/acl/resource/batch-disable-reason', params);
  },
  /**
     * 启用资源
     */
  yop_acl_resource_enable (params) {
    return fetch_post_normal_array('/rest/acl/resource/enable', params);
  },
  /**
     * 禁用资源
     */
  yop_acl_resource_disable (params) {
    return fetch_post('/rest/acl/resource/disable', params);
  },
  /**
     * 编辑资源
     */
  yop_acl_resource_edit (params) {
    return fetch_post('/rest/acl/resource/edit', params);
  },
  /**
     * 拖拽资源
     */
  yop_acl_resource_drag (params) {
    return fetch_post('/rest/acl/resource/drag', params);
  },
  /**
     * 权限列表
     */
  yop_acl_per_rule_list (params) {
    return fetch_get_params('/rest/acl/resource/filter/list', params);
  },
  /**
     * ------------
     * 新增审核部分
     * ------------
     */
  // 审核详情
  yop_acl_audit_detail (params) {
    return fetch_get_params('/rest/acl/audit/detail', params);
  },
  // 创建/更新审核
  yop_acl_audit_update (params) {
    return fetch_post('/rest/acl/audit/update', params);
  },
  // 撤销审核
  yop_acl_audit_record_delete (params) {
    return fetch_post_normal('/rest/acl/audit/delete', params);
  },
  // 用户列表
  yop_acl_role_user_common_list (params) {
    return fetch_get_params('/rest/acl/role/commons/list', params);
  },
  // 检查用户是否存在
  yop_acl_check_protal_user (params) {
    return fetch_get_params('/rest/acl/user/check-portal-user', params);
  },
  // 审批单列表
  yop_acl_audit_record_list (params) {
    return fetch_get_params('/rest/acl/audit/record/list', params);
  },
  // 取消审核
  yop_acl_audit_record_cancel (params) {
    return fetch_post_normal('/rest/acl/audit/record/cancel', params);
  },
  // 办理审核单
  yop_acl_audit_record_handle (params) {
    return fetch_post_normal('/rest/acl/audit/record/handle', params);
  },
  // 审核单查看更多数据
  yop_acl_audit_record_content (params) {
    return fetch_get_params('/rest/acl/audit/record/content', params);
  },
  // 通过审核
  yop_acl_audit_record_accept (params) {
    return fetch_post_normal('/rest/acl/audit/record/accept', params);
  },
  // 拒绝审核
  yop_acl_audit_record_refuse (params) {
    return fetch_post_normal('/rest/acl/audit/record/refuse', params);
  },
  // 审核状态枚举值
  yop_ac_audit_record_commons_status (params) {
    return fetch_get_params(
      '/rest/acl/audit/record/commons/status',
      params
    );
  },
  // 审核操作枚举值
  yop_ac_audit_record_commons_operate (params) {
    return fetch_get_params(
      '/rest/acl/audit/record/commons/operate',
      params
    );
  },

  /**
     * 申请单部分
     */

  // 审请单列表
  yop_acl_audit_requisition_list (params) {
    return fetch_get_params('/rest/acl/audit/requisition/list', params);
  },
  // 撤回审核
  yop_acl_audit_requisition_revoke (params) {
    return fetch_post_normal('/rest/acl/audit/requisition/revoke', params);
  },
  // 审核单详情
  yop_acl_audit_record_detail (params) {
    return fetch_get_params('/rest/acl/audit/record/detail', params);
  },
  // 角色列表
  yop_acl_role_commons_users (params) {
    return fetch_get_params('/rest/acl/role/commons/users', params);
  },

  /**
     * ------------------------------
     * 后端应用管理(/backend-app/list.vue)
     * ------------------------------
     */
  /**
     * 后端应用列表查询
     */
  yop_backend_app_list (params) {
    return fetch_get_params('/rest/backend-app/list', params);
  },
  /**
     * 后端应用详情
     */
  yop_backend_app_detail (params) {
    return fetch_get_params('/rest/backend-app/detail', params);
  },
  /**
     * 部署环境下拉框
     */
  yop_backend_app_deploy_mode () {
    return fetch_get('/rest/backend-app/commons/deploy-mode/list');
  },
  /**
     * 类加载方式下拉框
     */
  yop_backend_app_class_load () {
    return fetch_get('/rest/backend-app/commons/class-load-mode/list');
  },
  /**
     * 后端服务方式下拉框
     */
  yop_backend_app_rpc_type () {
    return fetch_get('/rest/backend-app/commons/rpc-mode/list');
  },
  /**
     * 创建后端应用
     */
  yop_backend_app_create (params) {
    return fetch_post('/rest/backend-app/create', params);
  },
  /**
     * 更新后端应用
     */
  yop_backend_app_update (params) {
    return fetch_post('/rest/backend-app/update', params);
  },
  /**
     * 删除后端应用
     */
  yop_backend_app_delete (params) {
    return fetch_post_normal('/rest/backend-app/delete', params);
  },
  /**
     * 批量初始化
     */
  yop_backend_app_batch_init () {
    return fetch_post('/rest/backend-app/batch-init');
  },
  /**
     * 重建CLassLoader
     */
  yop_backend_app_rebuild (params) {
    return fetch_post('/rest/backend-app/class-loader/rebuild', params);
  },
  /**
     * 导出api
     */
  yop_backend_app_exportapi (params, type) {
    return fetch_get_params_normal_blob(
      '/rest/backend-app/export-apis',
      params,
      type
    );
  },
  /**
     * ------------------------------
     * 日志查询(/acl/log/list.vue)
     * ------------------------------
     */
  /**
     * 操作记录列表
     *
     */
  yop_acl_log_list (params) {
    return fetch_get_params('/rest/acl/log/list', params);
  },
  /**
     * 操作日志详情

     *
     */
  yop_acl_log_detail (params) {
    return fetch_get_params('/rest/acl/log/detail', params);
  },
  /**
     * 操作日志功能
     *
     */

  yop_dashboard_acl_log_top_resource (params) {
    return fetch_get_params('/rest/acl/log/commons/top-resource', params);
  },
  yop_dashboard_acl_log_resource (params) {
    return fetch_get_params('/rest/acl/log/commons/resources', params);
  },
  /**
     * ------------------------------
     * 角色管理(/acl/role/list.vue)
     * ------------------------------
     */
  /**
     * 后端应用列表查询
     */
  yop_acl_role_list (params) {
    return fetch_get_params('/rest/acl/role/list', params);
  },
  /**
     * 查看角色详情
     */
  yop_acl_role_detail (params) {
    return fetch_get_params('/rest/acl/role/detail', params);
  },
  /**
     * 新建角色
     */
  yop_acl_role_create (params) {
    return fetch_post('/rest/acl/role/create', params);
  },
  /**
     * 编辑角色
     */
  yop_acl_role_update (params) {
    return fetch_post('/rest/acl/role/update', params);
  },
  /**
     * 批量授权用户
     */
  yop_acl_role_auth_add (params) {
    return fetch_post('/rest/acl/role/auth/add', params);
  },
  /**
     * 取消授权
     */
  yop_acl_role_auth_delete (params) {
    return fetch_post('/rest/acl/role/auth/delete', params);
  },
  /**
     * 角色类型列表
     */
  yop_acl_role_type () {
    return fetch_get('/rest/acl/role/type');
  },
  /**
     * 根据服务提供方编码查询角色列表
     */
  yop_acl_role_by_spcodes (params) {
    return fetch_get('/rest/acl/role/by-spcodes', params);
  },
  /**
     * 删除角色
     */
  yop_acl_role_delete (params) {
    return fetch_post_normal('/rest/acl/role/delete', params);
  },
  /**
     * 根据用户编码查询服务角色列表
     */
  yop_acl_role_by_optcode (params) {
    return fetch_get_params('/rest/acl/role/by-optcode', params);
  },
  /**
     * 根据角色编码查重
     */
  yop_acl_role_check (params) {
    return fetch_get_params('/rest/acl/role/check-duplicate', params);
  },
  /**
     * 根据角色类型查询资源列表
     */
  yop_acl_resource_by_role_type (params) {
    return fetch_get_params('/rest/acl/resource/by-role-type', params);
  },
  /**
     * 角色页面查询用户列表
     */
  yop_acl_role_users (params) {
    return fetch_get_params('/rest/acl/role/users', params);
  },

  /**
     * ------------------------------
     * 用户管理(/acl/user/list.vue)
     * ------------------------------
     */
  /**
     * 后端应用列表查询
     */
  yop_acl_user_list (params) {
    return fetch_get_params('/rest/acl/user/list', params);
  },
  /**
     * 根据岗位查询用户列表(角色管理-批量授权用户)
     */
  yop_acl_user_by_position (params) {
    return fetch_get_params('/rest/acl/user/by-position', params);
  },
  /**
     * 用户详情
     */
  yop_acl_user_detail (params) {
    return fetch_get_params('/rest/acl/user/detail', params);
  },
  /**
     * 新增用户并授权
     */
  yop_acl_user_create (params) {
    return fetch_post('/rest/acl/user/create', params);
  },
  /**
     * 编辑用户
     */
  yop_acl_user_edit (params) {
    return fetch_post_normal('/rest/acl/user/edit', params);
  },
  /**
     * 分配角色
     */
  yop_acl_user_auth (params) {
    return fetch_post_normal_array('/rest/acl/user/auth', params);
  },
  /**
     * 冻结用户
     */
  yop_acl_user_frozen (params) {
    return fetch_post_normal('/rest/acl/user/frozen', params);
  },
  /**
     * 解冻用户
     */
  yop_acl_user_unfreeze (params) {
    return fetch_post_normal('/rest/acl/user/unfreeze', params);
  },
  /**
     * 修改用户类型
     */
  yop_acl_user_edit_type (params) {
    return fetch_post_normal('/rest/acl/user/edit-type', params);
  },
  /**
     * 角色管理-根据用户名查询用户是否存在于我们系统中
     */
  yop_acl_user_check_native_user (params) {
    return fetch_get_params('/rest/acl/user/check-native-user', params);
  },
  /**
     * 用户管理-新增用户-检查用户在boss,且不在我们系统
     */
  yop_acl_user_check_boss_user (params) {
    return fetch_get_params('/rest/acl/user/check-boss-user', params);
  },
  /**
     * 查询没有分配角色给用户的且用户所属的服务提供方列表（用户管理-分配角色-查询服务提供方下拉菜单）
     */
  yop_acl_user_has_right (params) {
    return fetch_get_params(
      '/rest/acl/isp/has-right-but-unassigned-to-user',
      params
    );
  },
  /**
     * ------------------------------
     * 服务提供方管理(/isp/list.vue)
     * ------------------------------
     */
  /**
     * 提供方列表
     */
  yop_isp_list (params) {
    return fetch_get_params('/rest/isp/list', params);
  },
  /**
     * 服务提供方详情
     */
  yop_isp_detail (params) {
    return fetch_get_params('/rest/isp/detail', params);
  },
  /**
     * 新增服务提供方
     */
  yop_isp_create (params) {
    return fetch_post('/rest/isp/create', params);
  },
  /**
     * 编辑服务提供方
     */
  yop_isp_edit (params) {
    return fetch_post('/rest/isp/edit', params);
  },
  /**
     * 删除服务提供方
     */
  yop_isp_delete (params) {
    return fetch_post_normal('/rest/isp/delete', params);
  },
  /**
     * isp状态list
     */
  yop_isp_status () {
    return fetch_get('/rest/isp/status-enum');
  },
  /**
     * ------------------------------
     * 产品模块(/product-code/list.vue)
     * ------------------------------
     */
  // 产品分页查询
  yop_product_list (params) {
    return fetch_get_params('/rest/product/list', params);
  },
  // 全部产品查询
  yop_product_all (params) {
    return fetch_get_params('/rest/product/list-all-normal', params);
  },
  // 创建产品
  yop_product_create (params) {
    return fetch_post_multi('/rest/product/create', params);
  },
  // 产品详情
  yop_product_detail (params) {
    return fetch_get_params('/rest/product/detail', params);
  },
  // 产品编辑
  yop_product_edit (params) {
    return fetch_post_multi('/rest/product/edit', params);
  },

  // 产品下线
  yop_product_offline (params) {
    return fetch_post('/rest/product/offline', params);
  },
  // 产品恢复
  yop_product_normal (params) {
    return fetch_post('/rest/product/normal', params);
  },
  // 产品删除
  yop_product_delete (params) {
    return fetch_post_normal('/rest/product/delete', params);
  },

  // 产品变更记录
  yop_product_change (params) {
    return fetch_get_params('/rest/product/change/list', params);
  },
  // 批量关联API或分组
  yop_product_bath_create (params) {
    return fetch_post('/rest/product/api/batch-create', params);
  },
  // 批量取消关联API或分组
  yop_product_bath_delete (params) {
    return fetch_post('/rest/product/api/batch-delete', params);
  },
  // 产品-API列表
  yop_product_api_list (params) {
    return fetch_get_params('/rest/product/api/list', params);
  },

  // 场景查询
  yop_product_scene_list (params) {
    return fetch_get_params('/rest/product/scene/list', params);
  },
  // 创建场景
  yop_product_scene_create (params) {
    return fetch_post('/rest/product/scene/create', params);
  },

  // 删除场景
  yop_product_scene_delete (params) {
    return fetch_post('/rest/product/scene/delete', params);
  },
  // 排序场景
  yop_product_scene_order (params) {
    return fetch_post('/rest/product/scene/order', params);
  },

  // 授权分页查询

  yop_product_authz_list (params) {
    return fetch_get_params('/rest/product/authz/list', params);
  },
  // 授权
  yop_product_authz_batch_auth (params) {
    return fetch_post('/rest/product/authz/batch-auth', params);
  },

  // 确认授权
  yop_product_authz_confirm (params) {
    return fetch_post('/rest/product/authz/confirm', params);
  },

  // 取消授权
  yop_product_authz_unauth (params) {
    return fetch_post('/rest/product/authz/unauth', params);
  },
  // 恢复授权
  yop_product_authz_recover (params) {
    return fetch_post('/rest/product/authz/recover', params);
  },

  // 展期授权
  yop_product_authz_defer (params) {
    return fetch_post('/rest/product/authz/defer', params);
  },

  // 重新授权
  yop_product_authz_defer_reauth (params) {
    return fetch_post('/rest/product/authz/reauth', params);
  },
  // 产品授权变更记录
  yop_product_authz_change (params) {
    return fetch_get_params('/rest/product/authz/change/list', params);
  },

  /**
     * ------------------------------
     * 服务分组管理(/service-group/list.vue)
     * ------------------------------
     */
  /**
     * 服务分组树查询
     */
  yop_service_group_list (params) {
    return fetch_get_params('/rest/service-group/list', params);
  },

  // 产品列表
  yop_product_group_list (params) {
    return fetch_get_params('/rest/product/list/for-authz', params);
  },

  /**
     * 服务分组详情
     */
  yop_service_group_detail (params) {
    return fetch_get_params('/rest/service-group/detail', params);
  },
  /**
     * 服务分组授权方式
     */
  yop_service_group_auth_type () {
    return fetch_get_params('/rest/service-group/commons/authorize-types');
  },
  /**
     * 服务分组创建
     */
  yop_service_group_create (params) {
    return fetch_post('/rest/service-group/create', params);
  },
  /**
     * 服务分组验重
     */
  yop_service_group_check_exist (params) {
    return fetch_get_params('/rest/service-group/check-exists', params);
  },
  /**
     * 服务分组关联API查询
     */
  yop_service_group_api_list (params) {
    return fetch_get_params('/rest/service-group/api/list', params);
  },
  /**
     * 服务分组关联API分组查询
     */
  yop_service_group_api_group_list (params) {
    return fetch_get_params('/rest/service-group/api-group/list', params);
  },
  /**
     * 服务分组禁用
     */
  yop_service_group_disable (params) {
    return fetch_post('/rest/service-group/disable', params);
  },
  /**
     * 服务分组启用
     */
  yop_service_group_enable (params) {
    return fetch_post('/rest/service-group/enable', params);
  },
  /**
     * 服务分组删除
     */
  yop_service_group_delete (params) {
    return fetch_post('/rest/service-group/delete', params);
  },
  /**
     * 服务分组修改
     */
  yop_service_group_edit (params) {
    return fetch_post('/rest/service-group/edit', params);
  },
  /**
     * 服务分组拖动
     */
  yop_service_group_drag (params) {
    return fetch_post('/rest/service-group/change-parent', params);
  },
  /**
     * 服务分组关联API
     */
  yop_service_group_api_link (params) {
    return fetch_post('/rest/service-group/api/link', params);
  },
  /**
     * 服务分组关联API分组
     */
  yop_service_group_api_group_link (params) {
    return fetch_post('/rest/service-group/api-group/link', params);
  },
  /**
     * 服务分组API取消关联
     */
  yop_service_group_api_unlink (params) {
    return fetch_post('/rest/service-group/api/unlink', params);
  },
  /**
     * 服务分组API分组取消关联
     */
  yop_service_group_api_group_unlink (params) {
    return fetch_get_params('/rest/service-group/api-group/unlink', params);
  },
  /**
     * ------------------------------
     * 附加上传管理(/attachments/list.vue)
     * ------------------------------
     */
  /**
     * 附件列表分页查询
     */
  yop_attachment_list (params) {
    return fetch_get_params('/rest/attachments/list', params);
  },
  /**
     * 附件详情
     */
  yop_attachment_detail (params) {
    return fetch_get_params('/rest/attachments/detail', params);
  },
  /**
     * 上传附件
     */
  yop_attachment_upload (params) {
    return fetch_post_multi('/rest/attachments/upload', params);
  },
  /**
     * 附件编辑
     */
  yop_attachment_modify (params) {
    return fetch_post_multi('/rest/attachments/modify', params);
  },
  /**
     * 附件删除
     */
  yop_attachment_delete (params) {
    return fetch_post_normal_array('/rest/attachments/delete', params);
  },
  /**
     * 附件链接获取
     */
  yop_attachment_markdown_copy (params) {
    return fetch_get_params('/rest/attachments/markdown-format', params);
  },
  /**
     * ------------------------------
     * 服务授权管理(/service-authz/list.vue)
     * ------------------------------
     */
  /**
     * 服务分组授权列表
     */
  yop_service_authz_list (params) {
    return fetch_get_params('/rest/service-authz/list', params);
  },
  /**
     * 服务分组授权新增(重新授权)
     */
  yop_service_authz_create (params) {
    return fetch_post('/rest/service-authz/create', params);
  },
  /**
     * 服务分组授权展期
     */
  yop_service_authz_extension (params) {
    return fetch_post('/rest/service-authz/extension', params);
  },
  /**
     * 服务分组授权重新授权
     */
  yop_service_authz_reauthorize (params) {
    return fetch_post('/rest/service-authz/reauthorize', params);
  },
  /**
     * 服务分组取消授权
     */
  yop_service_authz_disable (params) {
    return fetch_post('/rest/service-authz/disable', params);
  },
  /**
     * 服务分组恢复授权
     */
  yop_service_authz_enable (params) {
    return fetch_post('/rest/service-authz/enable', params);
  },
  /**
     * ------------------------------
     * 应用管理(/app/list.vue)
     * ------------------------------
     */
  /**
     * 应用分页查询
     */
  yop_app_list (params) {
    return fetch_get_params('/rest/app/list', params);
  },
  /**
     * 应用详情
     */
  yop_app_detail (params) {
    return fetch_get_params('/rest/app/detail', params);
  },
  /**
     * 应用编辑
     */
  yop_app_edit (params) {
    return fetch_post('/rest/app/update', params);
  },
  /**
     * 应用启用
     */
  yop_app_active (params) {
    return fetch_post_normal('/rest/app/active', params);
  },
  /**
     * 应用禁用
     */
  yop_app_forbid (params) {
    return fetch_post_normal('/rest/app/forbid', params);
  },
  /**
     * 应用添加
     */
  yop_app_create (params) {
    return fetch_post('/rest/app/create', params);
  },
  /**
     * 应用删除
     */
  yop_app_delete (params) {
    return fetch_post_normal('/rest/app/delete', params);
  },
  /**
     * 商户通知功能列表详情
     */
  yop_notify_config_detail (params) {
    return fetch_get_params('/rest/notify-config/detail', params);
  },
  /**
     * 商户通知功能新增&修改
     */
  yop_notify_config_update (params) {
    return fetch_post('/rest/notify-config/update', params);
  },
  /**
     * 商户通知功能协议下拉框
     */
  yop_notify_config_protocol (params) {
    return fetch_get_params('/rest/notify-config/commons/protocol', params);
  },
  /**
     * 应用验重
     */
  yop_app_exists (params) {
    return fetch_get_params('/rest/app/exists', params);
  },
  /**
     * 商编验重
     * @param {params} 商编
     */
  yop_isv_exists (params) {
    return fetch_get_params('/rest/isv/exists', params);
  },
  /**
     * ------------------------------
     * 商户管理(/isv/list.vue)
     * ------------------------------
     */
  yop_isv_list (params) {
    return fetch_get_params('/rest/isv/list', params);
  },
  /**
     * ------------------------------
     * 商户新增(/isv/list.vue)
     * ------------------------------
     */
  yop_isv_create (params) {
    return fetch_post('/rest/isv/create', params);
  },
  /**
     * ------------------------------
     * 商户详情(/isv/list.vue)
     * ------------------------------
     */
  yop_isv_detail (params) {
    return fetch_get_params('/rest/isv/detail', params);
  },
  /**
     * ------------------------------
     * 商户编辑(/isv/list.vue)
     * ------------------------------
     */
  yop_isv_update (params) {
    return fetch_post('/rest/isv/update', params);
  },
  /**
     * ------------------------------
     * 商户冻结(/isv/list.vue)
     * ------------------------------
     */
  yop_isv_frozen (params) {
    return fetch_post('/rest/isv/frozen', params);
  },
  /**
     * ------------------------------
     * 商户解冻(/isv/list.vue)
     * ------------------------------
     */
  yop_isv_unfrozen (params) {
    return fetch_post('/rest/isv/unfrozen', params);
  },
  /**
     * ------------------------------
     * 获取编码(/isv/list.vue)
     * ------------------------------
     */
  yop_isv_generate_no (params) {
    return fetch_get_params('/rest/isv/generate-no', params);
  },

  /**
     * ------------------------------
     * 商户变更记录(/isv/list.vue)
     * ------------------------------
     */
  yop_isv_change (params) {
    return fetch_get_params('/rest/isv/change', params);
  },

  /**
     * ------------------------------
     * 操作员分页(/isv/oper/list.vue)
     * ------------------------------
     */
  yop_isv_oper_list (params) {
    return fetch_get_params('/rest/isv/oper/list', params);
  },
  /**
     * ------------------------------
     * 重置密码
     * ------------------------------
     */
  yop_isv_oper_resetPwd (params) {
    return fetch_get_params('/rest/isv/oper/reset/pwd', params);
  },
  /**
     * ------------------------------
     * 操作员新增(/isv/oper/list.vue)
     * ------------------------------
     */
  yop_isv_oper_create (params) {
    return fetch_post('/rest/isv/oper/create', params);
  },
  /**
     * ------------------------------
     * 操作员详情(/isv/oper/list.vue)
     * ------------------------------
     */
  yop_isv_oper_detail (params) {
    return fetch_get_params('/rest/isv/oper/detail', params);
  },
  /**
     * ------------------------------
     * 操作员编辑(/isv/oper/list.vue)
     * ------------------------------
     */
  yop_isv_oper_update (params) {
    return fetch_post('/rest/isv/oper/update', params);
  },
  /**
     * ------------------------------
     * 操作员冻结(/isv/oper/list.vue)
     * ------------------------------
     */
  yop_isv_oper_frozen (params) {
    return fetch_post('/rest/isv/oper/frozen', params);
  },
  /**
     * ------------------------------
     * 操作员解冻(/isv/oper/list.vue)
     * ------------------------------
     */
  yop_isv_oper_unfrozen (params) {
    return fetch_post('/rest/isv/oper/unfrozen', params);
  },
  /**
     * ------------------------------
     * 操作员解锁(/isv/oper/list.vue)
     * ------------------------------
     */
  yop_isv_oper_unlock (params) {
    return fetch_post('/rest/isv/oper/unlock', params);
  },
  /**
     * ------------------------------
     * 操作员删除(/isv/oper/list.vue)
     * ------------------------------
     */
  yop_isv_oper_delete (params) {
    return fetch_post('/rest/isv/oper/delete', params);
  },
  /**
     * ------------------------------
     * 商户密钥管理(/isv/cert/list.vue)
     * ------------------------------
     */
  /**
     * 密钥分页查询
     */
  yop_isv_cert_list (params) {
    return fetch_get_params('/rest/isv/cert/list', params);
  },
  /**
     * 新增密钥
     */
  yop_isv_cert_create (params) {
    return fetch_post_normal_array('/rest/isv/cert/create', params);
  },
  /**
     * 禁用密钥
     */
  yop_isv_cert_forbid (params) {
    return fetch_post('/rest/isv/cert/disable', params);
  },
  /**
     * 启用密钥
     */
  yop_isv_cert_active (params) {
    return fetch_post('/rest/isv/cert/enable', params);
  },
  /**
     * 吊销密钥
     */
  yop_isv_cert_revoke (params) {
    return fetch_post('/rest/isv/cert/revoke', params);
  },
  /**
     * 展期密钥
     */
  yop_isv_cert_defer (params) {
    return fetch_post('/rest/isv/cert/defer', params);
  },
  /**
     * 变更记录查询
     */
  yop_isv_cert_change_record (params) {
    return fetch_get_params('/rest/isv/cert/change-record', params);
  },
  /**
     * 易宝公钥查询
     */
  yop_isv_cert_public_key () {
    return fetch_get('/rest/isv/cert/yop-platform-public-key');
  },
  /**
     * 密钥用途查询
     */
  yop_isv_cert_usage () {
    return fetch_get('/rest/isv/cert/commons/usage');
  },
  /**
     * 密钥内容查询
     */
  yop_isv_cert_value (params) {
    return fetch_get_params('/rest/isv/cert/value', params);
  },
  /**
     * 校验密钥
     */
  yop_isv_cert_exists (params) {
    return fetch_get_params('/rest/isv/cert/exists', params);
  },
  /**
     * 查询CFCA证书
     */
  yop_isv_cfca_cert_list (params) {
    return fetch_get_params('/rest/isv/cfca-cert/list', params);
  },
  /**
     * 吊销CFCA证书
     */
  yop_isv_cfca_cert_revoke (params) {
    return fetch_post_normal_array('/rest/isv/cfca-cert/revoke', params);
  },
  /**
     * ------------------------------
     * 文档中心部分(/doc/edit-platform.vue)
     * ------------------------------
     */
  // 编辑平台文档 左侧树
  yop_doc_edit_tree (params) {
    return fetch_get_params('/rest/doc/tree', params);
  },
  // 编辑平台文档 左侧树新增
  yop_doc_edit_tree_add (params) {
    return fetch_post('/rest/doc/tree/add', params);
  },
  // 编辑平台文档 左侧树删除
  yop_doc_edit_tree_delete (params) {
    return fetch_post('/rest/doc/page/delete', params);
  },
  // 编辑平台文档 同步更新
  yop_doc_edit_tree_refresh (params) {
    return fetch_post_normal('/rest/doc/tree/refresh', params);
  },
  // 编辑平台文档 页面编辑
  yop_doc_edit_page_edit (params) {
    return fetch_post('/rest/doc/page/edit', params);
  },
  // 编辑平台文档 页面变更记录
  yop_doc_edit_change_history (params) {
    return fetch_get_params('/rest/doc/page/change-history', params);
  },
  // 编辑平台文档 编辑页面-请求当前页面信息
  yop_doc_edit_view (params) {
    return fetch_get_params('/rest/doc/page/view', params);
  },
  // 编辑平台文档 编辑页面-回滚版本
  yop_doc_edit_recover (params) {
    return fetch_post('/rest/doc/page/recover', params);
  },
  // 编辑平台文档 编辑页面-拖拽
  yop_doc_edit_drag (params) {
    return fetch_post('/rest/doc/tree/drag', params);
  },

  /**
     * ------------------------------
     * 应用 黑白名单(/limit-rules/*.vue)
     * ------------------------------
     */

  // 应用列表 白名单列表
  yop_app_limit_rule_list (params, type) {
    return fetch_get_params(`/rest/limit-rule/${type}/list`, params);
  },
  // 应用列表 白名单列表
  yop_app_limit_rule_blacklist (params, type) {
    return fetch_get_params(`/rest/limit-rule/${type}`, params);
  },
  // 应用列表 黑白名单详情
  yop_app_limit_rule_detail (params, type) {
    return fetch_get_params(`/rest/limit-rule/${type}/detail`, params);
  },
  // 应用列表 黑白名单详情
  yop_app_limit_rule_commons_status (params) {
    return fetch_get_params('/rest/limit-rule/commons/status', params);
  },
  // 应用列表 禁用
  yop_app_limit_rule_disable (params, type) {
    return fetch_post(`/rest/limit-rule/${type}/disable`, params);
  },
  // 应用列表 启用
  yop_app_limit_rule_enable (params, type) {
    return fetch_post(`/rest/limit-rule/${type}/enable`, params);
  },
  // 应用列表 删除
  yop_app_limit_rule_delete (params, type) {
    return fetch_post(`/rest/limit-rule/${type}/delete`, params);
  },
  // 应用列表 新增
  yop_app_limit_rule_create (params, type) {
    return fetch_post(`/rest/limit-rule/${type}/create`, params);
  },
  // 应用列表 更新
  yop_app_limit_rule_update (params, type) {
    return fetch_post(`/rest/limit-rule/${type}/update`, params);
  },
  // 应用列表 api 校验是否存在
  yop_api_exists (params) {
    return fetch_get_params('/rest/api/exists', params);
  },
  // 端点方法
  // getFacadeDesc (params) {
  //   return fetch_get_params('/rest/api/manage/method/load', params);
  // },
  // 端点类名
  getFacadeList (params) {
    // return fetch_get_params('/rest/api/manage/class/load', params);
    return fetch_get_params('https://cloudos.yeepay.com/self-desc/self_desc/getFacadeList', params);
  },
  // 端点类名 方法
  getFacadeDesc (params) {
    // return fetch_get_params('/rest/api/manage/class/load', params);
    // return fetch_get_params('rest/api/manage/method/analysis', params);
    return fetch_get_params('https://cloudos.yeepay.com/self-desc/self_desc/getFacadeDesc', params);
  },
  // 端点类名
  getManageEnvList (params) {
    return fetch_get_params('/rest/api/manage/env/list', params);
  },
  // 端点类名 方法 解析
  getFacadeDescData (params) {
    return fetch_post('/rest/api/manage/method/analysis', params);
  },

  /**
   * ------------------------------
   * 自定义解决方案
   * ------------------------------
   */
  // 自定义解决方案 列表
  yop_custom_solution_list (params) {
    return fetch_get_params('/rest/custom-solution/list', params);
  },
  // 自定义解决方案 创建
  yop_custom_solution_create (params) {
    return fetch_post('/rest/custom-solution/create', params);
  },
  // 自定义解决方案 编辑
  yop_custom_solution_edit (params) {
    return fetch_post('/rest/custom-solution/edit', params);
  },
  // 自定义解决方案 复制
  yop_custom_solution_copy (params) {
    return fetch_post('/rest/custom-solution/copy', params);
  },
  // 自定义解决方案 分享
  yop_custom_solution_share (params) {
    return fetch_get_params('/rest/custom-solution/share', params);
  },
  // 自定义解决方案 已关联api 是否必要
  yop_related_api_require (params) {
    return fetch_post('/rest/custom-solution/related-api/require', params);
  },
  // 自定义解决方案 已关联api 取消关联
  yop_related_api_remove (params) {
    return fetch_post('/rest/custom-solution/related-api/remove', params);
  },
  // 自定义解决方案 已关联api 添加关联
  yop_related_api_add (params) {
    return fetch_post('/rest/custom-solution/related-api/add', params);
  },
  // 自定义解决方案 已关联api 列表
  yop_related_api_list (params) {
    return fetch_get_params('/rest/custom-solution/related-api/list', params);
  },
  // 自定义解决方案 未关联api 列表
  yop_related_api_unrelated_list (params) {
    return fetch_get_params('/rest/custom-solution/related-api/unrelated-list', params);
  }
};
