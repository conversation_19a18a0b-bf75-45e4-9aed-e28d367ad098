// eslint-disable-next-line camelcase
import { fetch_post, fetch_get_params } from '../api';

export default {
  createDoc (params) {
    return fetch_post('/rest/doc/create', params);
  },
  editDoc (params) {
    return fetch_post('/rest/doc/settings/edit', params);
  },
  getDocDetail (params) {
    return fetch_get_params('/rest/doc/settings/detail', params);
  },
  getDocCategoryList (params) {
    return fetch_get_params('/rest/doc/commons/category/list', params);
  },
  getProductCodesList (params) {
    return fetch_get_params('/rest/product/list-for-select', params);
  },
  checkDocNo (params) {
    return fetch_get_params('/rest/doc/check-if-exists', params);
  },
  findCategory (params) {
    return fetch_get_params('/rest/product/category/find', params);
  }
};
