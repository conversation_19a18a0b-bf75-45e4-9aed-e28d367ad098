// eslint-disable-next-line camelcase
import { fetch_post, fetch_post_normal, fetch_get_params_normal } from '../api';

export default {
  createApiClass (params) {
    return fetch_post('/rest/doc/category/create', params);
  },
  updateApiClass (params) {
    return fetch_post('/rest/doc/category/update', params);
  },
  deleteApiClass (params) {
    return fetch_post_normal('/rest/doc/category/delete', params);
  },
  getPageOwners (params) {
    return fetch_get_params_normal('/rest/doc/page/owners', params);
  },
  updatePageOwners (params) {
    return fetch_post('/rest/doc/page/owners/update', params);
  },
  searchPageOwners (params) {
    return fetch_get_params_normal('/rest/user/search', params);
  },
  listForPublish (params) {
    return fetch_get_params_normal('/rest/doc/page/list-for-publish', params);
  },
  checkForPublish (params) {
    return fetch_post('/rest/doc/check-for-publish-with-request', params);
  },
  publish (params) {
    return fetch_post('/rest/doc/publish', params);
  },
  diffList (params) {
    return fetch_get_params_normal('/rest/doc/page/publish/list', params);
  },
  diff (params) {
    return fetch_post('/rest/doc/page/diff', params);
  }
};
