// eslint-disable-next-line camelcase
import { fetch_get_params, fetch_post } from '../api';
export default {
  getParamList (params) {
    return fetch_get_params('/rest/api/manage/request-param/list', params)
  },
  getResParamList (params) {
    return fetch_get_params('/rest/api/manage/response-param/list', params)
  },
  getErrorCodeList (params) {
    return fetch_get_params('/rest/api/manage/error-code/list', params)
  },
  getApiOption (params) {
    return fetch_get_params('/rest/api/manage/commons/api-option', params);
  },
  getDetail (params) {
    return fetch_get_params('/rest/api/manage/option/detail', params);
  },
  update (params) {
    return fetch_post('/rest/api/manage/option/update', params);
  },
  preview (params) {
    return fetch_post('/rest/api/manage/option-doc/preview', params);
  },
  getCallbacks (params) {
    return fetch_get_params('/rest/api/old/callbacks', params);
  },
}
