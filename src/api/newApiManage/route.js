// eslint-disable-next-line camelcase
import { fetch_post_normal, fetch_get_params, fetch_post, fetch_get } from '../api';

export default {
  // 发布路由
  deployRoute (params) {
    return fetch_post_normal('/rest/api/route/deploy', params);
  },
  // 回滚路由
  rollbackRoute (params) {
    return fetch_post_normal('/rest/api/route/rollback', params);
  },
  // 路由发布记录列表
  routeDeployList (params) {
    return fetch_get_params('/rest/api/route/deploy-record/list', params);
  },
  // api发布记录详情
  apiDeployDetail (params) {
    return fetch_get_params('/rest/api/manage/publish-record/detail', params);
  },
  // 路由发布记录详情
  routeDeployDetail (params) {
    return fetch_get_params('/rest/api/route/deploy-record/detail', params);
  },
  // 创建路由-dubbo
  create (params) {
    return fetch_post('/rest/api/route/create', params);
  },
  // 拖动路由
  arrange (params) {
    return fetch_post('/rest/api/route/arrange', params);
  },
  // 启用路由
  enable (params) {
    return fetch_post_normal('/rest/api/route/enable', params);
  },
  // 禁用路由
  disable (params) {
    return fetch_post_normal('/rest/api/route/disable', params);
  },
  // 查询路由详情
  routeDetail (params) {
    return fetch_get_params('/rest/api/route/detail', params);
  },
  // 编辑路由
  editRoute (params) {
    return fetch_post('/rest/api/route/update', params);
  },
  // 删除路由
  deleteRoute (params) {
    return fetch_post_normal('/rest/api/route/delete', params);
  },
  // 查询api下的route列表
  routeList (params) {
    return fetch_get_params('/rest/api/route/list', params);
  },
  // 查询支持的系统参数
  sysParams (params) {
    return fetch_get_params('/rest/api/route/commons/sys-params', params);
  },
  // 映射的隐式入参地址
  implicitParamIn (params) {
    return fetch_post_normal('/rest/api/route/commons/implicit-param-in', params);
  },
  // 查询后端
  getServiceNameList (params) {
    return fetch_get('/rest/backend-service/commons/simple-list', params);
  },
  // 查询后端类
  getEndclassList (params) {
    return fetch_get_params('/rest/api/route/end-class/list', params);
  },
  // 查询端点类对应的方法
  getListByEndclass (params) {
    return fetch_get_params('/rest/api/route/end-class/method/list', params);
  },
  getFacadeList (params) {
    // return fetch_get_params('/rest/api/manage/class/load', params);
    return fetch_get_params('https://cloudos.yeepay.com/self-desc/self_desc/getFacadeList', params);
  },
  // 端点类名 方法
  getFacadeDesc (params) {
    // return fetch_get_params('/rest/api/manage/class/load', params);
    // return fetch_get_params('rest/api/manage/method/analysis', params);
    return fetch_get_params('https://cloudos.yeepay.com/self-desc/self_desc/getFacadeDesc', params);
  },
  // 端点类名
  getManageEnvList (params) {
    return fetch_get_params('/rest/api/manage/env/list', params);
  },
  // 端点类名 方法 解析
  getFacadeDescData (params) {
    return fetch_post('/rest/api/manage/method/analysis', params);
  }
};
