// eslint-disable-next-line camelcase
import { fetch_post_normal, fetch_get_params, fetch_post } from '../api';
export default {
  // api发布到文档
  apiDeloyToDoc (params) {
    return fetch_post_normal('/rest/api/manage/publish', params);
  },
  // api从文档下线
  apiOfflineFromDoc (params) {
    return fetch_post_normal('/rest/api/manage/offline-from-doc', params);
  },
  // 查询api的安全需求
  queryApiSecurityReq (params) {
    return fetch_get_params('/rest/api/manage/security-req', params);
  },
  // 更新api的安全需求
  updateApiSecurityReq (params) {
    return fetch_post('/rest/api/manage/security-req/update', params);
  },
  // 查询api的安全需求byapigroup
  queryApiSecurityReqByGroup (params) {
    return fetch_get_params('/rest/api-group/security-req', params);
  },
  // 更新api的安全需求
  updateApiSecurityReqByGroup (params) {
    return fetch_post('/rest/api-group/security-req/update', params);
  },
  // api的文档记录回滚
  rollback (params) {
    return fetch_post_normal('/rest/api/manage/rollback', params);
  },
  // 查询入参需要映射的参数
  getParametersList (params) {
    return fetch_get_params('/rest/api/manage/parameters/list', params);
  },
  // 查询出参需要映射的参数
  getResponseList (params) {
    return fetch_get_params('/rest/api/manage/response/list', params);
  },
  // 查询api分类
  getApiClassic (params) {
    return fetch_get_params('/rest/api/manage/commons/api-classic', params);
  }
};
