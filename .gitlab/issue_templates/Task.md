### Description

(Include problem, use cases, benefits, and/or goals)

### Proposal

### Links / references

### Documentation blurb

#### Overview

What is it?
Why should someone use this feature?
What is the underlying (business) problem?
How do you use this feature?

#### Use cases

Who is this for? Provide one or more use cases.

### Feature checklist

Make sure these are completed before closing the issue,
with a link to the relevant commit.

- [ ] [Feature assurance](https://about.gitlab.com/handbook/product/#feature-assurance)
- [ ] Documentation
- [ ] Added to [features.yml](https://gitlab.com/gitlab-com/www-gitlab-com/blob/master/data/features.yml)

/label ~"feature proposal"